package com.ruoyi.tms.controller.basic;

import cn.hutool.core.net.URLEncodeUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.aspectj.DataScopeAspect;
import com.ruoyi.system.domain.SysConfig;
import com.ruoyi.system.domain.SysDictData;
import com.ruoyi.system.domain.SysUser;
import com.ruoyi.system.mapper.SysConfigMapper;
import com.ruoyi.util.ShiroUtils;
import com.ruoyi.util.UrlReplaceIUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.HashSet;
import java.util.Set;

/**
 * @description:
 * @author: baoy
 * @create: 20200309
 **/
@Controller
@RequestMapping("/report")
public class FRReportController extends BaseController {
    private String prefix = "tms/fineReport";

    @Value("${fineReportPort}")
    private String fineReportPort;
    @Value("${fineHttpHead}")
    private String fineHttpHead;
    @Autowired
    private ShiroUtils shiroUtils;
    @Autowired
    private SysConfigMapper configMapper;
    /**
     * 业务统计
     * @return
     */
    @RequiresPermissions("tms:report:invoiceInfo")
    @GetMapping("/invoiceInfo")
    public String invoiceInfo(ModelMap mmap) {
        //查询当前用户数据权限
        SysUser user = shiroUtils.getSysUser();
        String sqlString = DataScopeAspect.getDataScopeSqlString(user, "invoice.sales_dept", "invoice.psndoc", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=invoiceInfo.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        mmap.put("url",url);
        return prefix + "/invoiceInfo";
    }

    /**
     * 业务统计 (批处理抽取数据)
     * @return
     */
    @RequiresPermissions("tms:report:invoiceInfo")
    @GetMapping("/invoiceInfoMonth")
    public String invoiceInfoMonth(ModelMap mmap) {
        //查询当前用户数据权限
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"t.sales_dept","t.psndoc", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=invoiceInfoMonth.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        mmap.put("url",url);
        return prefix + "/invoiceInfo";
    }

    /**
     * 调度统计
     * @return
     */
    @RequiresPermissions("tms:report:segmentInfo")
    @GetMapping("/segmentInfo")
    public String segmentInfo(ModelMap mmap) {
        //查询当前用户数据权限
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"seg.trans_line_id","seg.dispatcher_id", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=segmentInfo.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        mmap.put("url",url);
        return prefix + "/segmentInfo";
    }

    /**
     * 调度统计(批处理)
     * @return
     */
    @RequiresPermissions("tms:report:segmentInfo")
    @GetMapping("/segmentInfoMonth")
    public String segmentInfoMonth(ModelMap mmap) {
        //查询当前用户数据权限
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"trans_line_id","dispatcher_id", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=segmentInfoMonth.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        mmap.put("url",url);
        return prefix + "/segmentInfo";
    }

    /**
     * 票据打印
     * @return
     */
    @RequiresPermissions("tms:invoice:print")
    @GetMapping("/invoicePrint/{id}")
    public String invoicePrint(@PathVariable String id, ModelMap mmap) {
        String[] split = id.split(",");
        //取前两位加入SET
        Set<String> stringSet = new HashSet<>();
        for(String s : split){
            stringSet.add(s.substring(0,2));
        }
        if(stringSet.size() > 1){
            throw new BusinessException("请选择同一公司发货单票据进行打印！");
        }
        id ="'"+id.replaceAll(",","','")+"'";
        String url;
        if(split[0].substring(0,2).equals("JH")){
            url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=invoicePrint_JH.cpt&vbillno="+id;
        }else{
            url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=invoicePrint.cpt&vbillno="+id;
        }
        mmap.put("url",url);
        return prefix + "/invoicePrint";
    }

    /**
     * 营运总览
     * @return
     */
    @RequiresPermissions("tms:report:overview")
    @GetMapping("/overview")
    public String overview(ModelMap mmap) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=operationOverview.cpt";
        mmap.put("url",url);
        return prefix + "/operationOverview";
    }

    /**
     * 业务分析（发货单）
     * @return
     */
    @RequiresPermissions("tms:report:operationAnalyse")
    @GetMapping("/operationAnalyse")
    public String operationAnalyse(ModelMap mmap) {
        //查询当前用户数据权限
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"t.sales_dept","t.psndoc", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=operationAnalyse.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        mmap.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 业务分析（发货单，批处理抽取数据）
     * @return
     */
    @RequiresPermissions("tms:report:operationAnalyse")
    @GetMapping("/operationAnalyseMonth")
    public String operationAnalyseMonth(ModelMap mmap) {
        //查询当前用户数据权限
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"t.sales_dept","t.psndoc", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=operationAnalyseMonth.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        mmap.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 业务分析（运单）
     * @return
     */
    @RequiresPermissions("tms:report:operationAnalyseLot")
    @GetMapping("/operationAnalyseLot")
    public String operationAnalyseLot(ModelMap mmap) {
        //查询当前用户数据权限
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"t.trans_line_id","t.dispatcher_id", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=operationAnalyseLot.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        mmap.put("url",url);
        return prefix + "/operation_analyse_lot";
    }

    /**
     * 业务分析（运单，批处理）
     * @return
     */
    @RequiresPermissions("tms:report:operationAnalyseLot")
    @GetMapping("/operationAnalyseLotMonth")
    public String operationAnalyseLotMonth(ModelMap mmap) {
        //查询当前用户数据权限
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"t.trans_line_id","t.dispatcher_id", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=operationAnalyseLotMonth.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        mmap.put("url",url);
        return prefix + "/operation_analyse_lot";
    }

    /**
     * 业务分析
     * @return
     */
    @RequiresPermissions("tms:report:invoiceAnalyse")
    @GetMapping("/invoiceAnalyse")
    public String invoiceAnalyse(ModelMap mmap) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=invoiceAnalyse.cpt";
        mmap.put("url",url);
        return prefix + "/invoiceAnalyse";
    }

    /**
     *  油卡绩效审核
     * @return
     */
    @RequiresPermissions("tms:report:oilcardCheck")
    @GetMapping("/oilcardCheck")
    public String oilcardCheck(ModelMap mmap) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=oilcardCheck.cpt";
        mmap.put("url",url);
        return prefix + "/oilcardCheck";
    }

    /**
     *  回单
     * @return
     */
    @RequiresPermissions("tms:report:receipt")
    @GetMapping("/receipt")
    public String receipt(ModelMap mmap) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=receipt.cpt";
        mmap.put("url",url);
        return prefix + "/receipt";
    }

    /**
     * 指导价导出
     * @return
     */
    @RequiresPermissions("tms:guidePrice:export")
    @GetMapping("/guidePriceExport/{id}")
    public String guidePriceExport(@PathVariable String id,ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=guidePrice.cpt&op=write&customerId="+id;
        map.put("url",url);
        return prefix + "/guidePriceExport";
    }

    /**
     * 调度指导价分析
     * @return
     */
    @RequiresPermissions("tms:report:guridPriceAnalyse")
    @GetMapping("/guridPriceAnalyse")
    public String guridPriceAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=guridPriceAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 客户调度指导价分析
     * @return
     */
    @RequiresPermissions("tms:report:guridPriceAnalyseCustTotal")
    @GetMapping("/guridPriceAnalyseCustTotal")
    public String guridPriceAnalyseCustTotal(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=guridPriceAnalyseCustTotal.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }


    /**
     * 调度人调度指导价分析
     * @return
     */
    @RequiresPermissions("tms:report:guridPriceAnalysePersonTotal")
    @GetMapping("/guridPriceAnalysePersonTotal")
    public String guridPriceAnalysePersonTotal(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=guridPriceAnalysePersonTotal.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 调度指导价分析合计
     * @return
     */
    @RequiresPermissions("tms:report:guridPriceAnalyseTotal")
    @GetMapping("/guridPriceAnalyseTotal")
    public String guridPriceAnalyseTotal(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=guridPriceAnalyseTotal.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }


    /**
     * 轨迹分析
     * @return
     */
    @RequiresPermissions("tms:report:locusReport")
    @GetMapping("/locusReport")
    public String locusReport(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=locusReport.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 客户应收对账分析
     * @return
     */
    @RequiresPermissions("tms:report:receiveAnalyse")
    @GetMapping("/receiveAnalyse")
    public String receiveAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=receiveAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 订单跟踪
     * @return
     */
    @RequiresPermissions("tms:report:traceAnalyse")
    @GetMapping("/traceAnalyse")
    public String traceAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=traceAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 每日简报
     * @return
     */
    @RequiresPermissions("tms:report:dailyReport")
    @GetMapping("/dailyReport")
    public String dailyReport(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?formlet=dailyReport.frm";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 每日业务分析
     * @return
     */
    @RequiresPermissions("tms:report:dailyCompleteReport")
    @GetMapping("/dailyCompleteReport")
    public String dailyCompleteReport(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"dept_id","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=dailyCompleteReport.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 客户集团报表
     * @return
     */
    @RequiresPermissions("tms:report:custGroup")
    @GetMapping("/custGroup")
    public String custGroup(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=custGroup.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 在途费用详情报表
     * @return
     */
    @RequiresPermissions("tms:report:lotCostTypeOnWay")
    @GetMapping("/lotCostTypeOnWay")
    public String lotCostTypeOnWay(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=lotCostTypeOnWay.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 账龄分析
     * @return
     */
    @RequiresPermissions("tms:report:accountAge")
    @GetMapping("/accountAge")
    public String accountAge(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=accountAge.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 账龄分析
     * @return
     */
    @RequiresPermissions("tms:report:companyOperationAnalyse")
    @GetMapping("/companyOperationAnalyse")
    public String companyOperationAnalyse(ModelMap map) {
        //零单20
        SysConfig lclDate = configMapper.checkConfigKeyUnique("lcl_req_deli_date");
        //整车10
        SysConfig wholeDate = configMapper.checkConfigKeyUnique("whole_car_req_deli_date");
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=companyOperationAnalyse.cpt&lclDate="+lclDate.getConfigValue()+"&wholeDate="+wholeDate.getConfigValue();
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 异常汇总
     * @return
     */
    @RequiresPermissions("tms:report:expTotal")
    @GetMapping("/expTotal")
    public String expTotal(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=expTotal.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 客户投诉分析
     * @return
     */
    @RequiresPermissions("tms:report:complaints")
    @GetMapping("/custComplaints")
    public String custCompaint(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=custComplaints.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 回单汇总
     * @return
     */
    @RequiresPermissions("tms:report:receiptSummary")
    @GetMapping("/receiptSummary")
    public String receiptSummary(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=receiptSummary.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 超期未回单
     * @return
     */
    @RequiresPermissions("tms:report:overdueNoReturns")
    @GetMapping("/overdueNoReturns")
    public String overdueNoReturns(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=overdueNoReturns.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

	/**
	 * 业务分析（发货单，批处理抽取数据）
	 * @return
	 */
	@RequiresPermissions("tms:report:transportAnalyse")
	@GetMapping("/transportAnalyse")
	public String transportAnalyse(ModelMap mmap) {
		//查询当前用户数据权限
		SysUser user = shiroUtils.getSysUser();
		String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","cust.psndoc", null);
		String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=transportAnalyse.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
		mmap.put("url",url);
		return prefix + "/operation_analyse";
	}

	/**
	 * 营运KPI考核
	 * @return
	 */
	@RequiresPermissions("tms:report:kPICompleteReport")
	@GetMapping("/kPICompleteReport")
	public String kPICompleteReport(ModelMap mmap) {
        //零单20
        SysConfig lclDate = configMapper.checkConfigKeyUnique("lcl_req_deli_date");
        //整车10
        SysConfig wholeDate = configMapper.checkConfigKeyUnique("whole_car_req_deli_date");
		String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=kPICompleteReport.cpt&lclDate="+lclDate.getConfigValue()+"&wholeDate="+wholeDate.getConfigValue();
		mmap.put("url",url);
		return prefix + "/operation_analyse";
	}

    /**
     * 对账分析（未对账）
     * @return
     */
    @RequiresPermissions("tms:report:receCheckAnalyse")
    @GetMapping("/receCheckAnalyse")
    public String receCheckAnays(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=receCheckAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * KPI新
     * @return
     */
    @RequiresPermissions("tms:report:KPITotalAnalyse")
    @GetMapping("/KPITotalAnalyse")
    public String KPITotalAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=KPITotalAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 每月应收款达成统计
     * @return
     */
    @RequiresPermissions("tms:report:monthlyReceiveAnalyse")
    @GetMapping("/monthlyReceiveAnalyse")
    public String monthlyReceiveAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=monthlyReceiveAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 客户收入分析
     * @return
     */
    @RequiresPermissions("tms:report:custReceiveAnalyse")
    @GetMapping("/custReceiveAnalyse")
    public String custReceiveAnalyse(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","", "cust.customer_id");
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=custReceiveAnalyse.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 车队外部成本表
     * @return
     */
    @RequiresPermissions("tms:report:carExternalCost")
    @GetMapping("/carExternalCost")
    public String carExternalCost(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=carExternalCost.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 车队内部成本表
     * @return
     */
    @RequiresPermissions("tms:report:carInternalCost")
    @GetMapping("/carInternalCost")
    public String carInternalCost(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=carInternalCost.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 车队内部应收表
     * @return
     */
    @RequiresPermissions("tms:report:carTeamReceiveInnerAnalyse")
    @GetMapping("/carTeamReceiveInnerAnalyse")
    public String carTeamReceiveInnerAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=carTeamReceiveInnerAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 车队外部应收表
     * @return
     */
    @RequiresPermissions("tms:report:carTeamReceiveAnalyse")
    @GetMapping("/carTeamReceiveAnalyse")
    public String carTeamReceiveAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=carTeamReceiveAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 业务清单收表
     * @return
     */
    @RequiresPermissions("tms:report:businessAnalyse")
    @GetMapping("/businessAnalyse")
    public String businessAnalyse(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","", "cust.customer_id");
        if(user.getDeptId() != null && user.getDeptId() == 1121l){
            sqlString = "";
        }
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=businessAnalyse.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 应收月度对账
     * @return
     */
    @RequiresPermissions("tms:report:receCheckSheetMonth")
    @GetMapping("/receCheckSheetMonth")
    public String receCheckSheetMonth(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=receCheckSheetMonth.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 应付月度对账
     * @return
     */
    @RequiresPermissions("tms:report:payMonthCheckSheet")
    @GetMapping("/payMonthCheckSheet")
    public String payMonthCheckSheet(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=payMonthCheckSheet.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 第三方月度对账
     * @return
     */
    @RequiresPermissions("tms:report:otherFeeMonth")
    @GetMapping("/otherFeeMonth")
    public String otherFeeMonth(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=otherFeeMonth.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 第三方月度对账
     * @return
     */
    @RequiresPermissions("tms:report:dispatchGRCAnalyse")
    @GetMapping("/dispatchGRCAnalyse")
    public String dispatchGRCAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=dispatchGRCAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 第三方调整
     * @return
     */
    @RequiresPermissions("tms:report:otherFeeMonthAdjustSelect")
    @GetMapping("/otherFeeMonthAdjustSelect")
    public String otherFeeMonthAdjustSelect(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=otherFeeMonthAdjustSelect.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 应付调整
     * @return
     */
    @RequiresPermissions("tms:report:payMonthCheckSheetAdjustSelect")
    @GetMapping("/payMonthCheckSheetAdjustSelect")
    public String payMonthCheckSheetAdjustSelect(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=payMonthCheckSheetAdjustSelect.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 应收调整
     * @return
     */
    @RequiresPermissions("tms:report:receCheckSheetMonthAdjustSelect")
    @GetMapping("/receCheckSheetMonthAdjustSelect")
    public String receCheckSheetMonthAdjustSelect(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=receCheckSheetMonthAdjustSelect.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 推送统计
     * @return
     */
    @RequiresPermissions("tms:report:pushCheckAnalyse")
    @GetMapping("/pushCheckAnalyse")
    public String pushCheckAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=pushCheckAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 推送统计g7
     * @return
     */
    @RequiresPermissions("tms:report:g7PushCheckAnalyse")
    @GetMapping("/g7PushCheckAnalyse")
    public String g7PushCheckAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=g7PushCheckAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 调度合规性分析
     * @return
     */
    @RequiresPermissions("tms:report:dispatchGRCAnalyseTotal")
    @GetMapping("/dispatchGRCAnalyseTotal")
    public String dispatchGRCAnalyseTotal(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=dispatchGRCAnalyseTotal.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }


    /**
     * G7运单合规性
     * @return
     */
    @RequiresPermissions("tms:report:g7PushAnalyse")
    @GetMapping("/g7PushAnalyse")
    public String g7PushAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=g7PushAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * G7合计
     * @return
     */
    @RequiresPermissions("tms:report:g7TotalAnalyse")
    @GetMapping("/g7TotalAnalyse")
    public String g7TotalAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=g7TotalAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 财务管理分析
     * @return
     */
    @RequiresPermissions("tms:report:financeManager")
    @GetMapping("/financeManager")
    public String financeManager(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=financeManager.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 财务管理分析
     * @return
     */
    @RequiresPermissions("tms:report:financeManager_cust")
    @GetMapping("/financeManager_cust")
    public String financeManager_cust(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=financeManager_cust.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 调度详情
     * @return
     */
    @RequiresPermissions("tms:report:operationOverviewDetail")
    @GetMapping("/operationOverviewDetail")
    public String operationOverviewDetail(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"invoice.sales_dept","invoice.psndoc", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=operationOverviewDetail.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 调度详情
     * @return
     */
    @RequiresPermissions("tms:report:operationOverviewDispatchDetail")
    @GetMapping("/operationOverviewDispatchDetail")
    public String operationOverviewDispatchDetail(ModelMap map) {
        //查询当前用户数据权限
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"entrustLot.trans_line_id","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=operationOverviewDispatchDetail.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 客户收入汇总详情
     * @return
     */
    @RequiresPermissions("tms:report:custReceiveAnalyseTotal")
    @GetMapping("/custReceiveAnalyseTotal")
    public String custReceiveAnalyseTotal(ModelMap map) {
        //查询当前用户数据权限
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"x.sales_dept","", "x.customer_id");
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=custReceiveAnalyseTotal.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 订单调度一览
     * @return
     */
    @RequiresPermissions("tms:report:operationOverviewDispatch2")
    @GetMapping("/operationOverviewDispatch2")
    public String operationOverviewDispatch2(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?formlet=operationOverviewDispatch2.frm";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 业务账款详情
     * @return
     */
    @RequiresPermissions("tms:report:ReceiveOverTimeOverview")
    @GetMapping("/ReceiveOverTimeOverview")
    public String ReceiveOverTimeOverview(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?formlet=ReceiveOverTimeOverview.frm";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 业务账款详情明细
     * @return
     */
    @GetMapping("/ReceiveOverTime")
    public String ReceiveOverTime(ModelMap map) {
        //查询当前用户数据权限
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","cust.psndoc", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=ReceiveOverTime.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 历史运价
     * @return
     */

    @RequiresPermissions("tms:report:guidePriceList")
    @GetMapping("/guidePriceList")
    public String guidePriceList(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=guidePriceList.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 超期未处理
     * @return
     */

    @RequiresPermissions("tms:report:receiveOverTimeNotReceiveTotal")
    @GetMapping("/receiveOverTimeNotReceiveTotal")
    public String receiveOverTimeNotReceiveTotal(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=receiveOverTimeNotReceiveTotal.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 委托单调度实例
     * @return
     */

    @RequiresPermissions("tms:report:entrustList")
    @GetMapping("/entrustList")
    public String entrustList(ModelMap map) {
        //查询当前用户数据权限
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","cust.psndoc", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=entrustList.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 调度延迟
     * @return
     */

    @RequiresPermissions("tms:report:EntrustOperateExceptionTotal")
    @GetMapping("/EntrustOperateExceptionTotal")
    public String EntrustOperateExceptionTotal(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?formlet=EntrustOperateExceptionTotal.frm";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 委托单调度实例
     * @return
     */

    @RequiresPermissions("tms:report:guidePriceCustList")
    @GetMapping("/guidePriceCustList")
    public String guidePriceCustList(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=guidePriceCustList.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 逾期支付
     * @return
     */
    @RequiresPermissions("tms:report:ReceiveOverTimeZJCB")
    @GetMapping("/ReceiveOverTimeZJCB")
    public String ReceiveOverTimeZJCB(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=ReceiveOverTimeZJCB.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 应付账期
     * @return
     */
    @RequiresPermissions("tms:report:payDetailLateTotal")
    @GetMapping("/payDetailLateTotal")
    public String payDetailLateTotal(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=payDetailLateTotal.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 未开票逾期
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:ReceiveOverTimeNCS")
    @GetMapping("/ReceiveOverTimeNCS")
    public String ReceiveOverTimeNCS(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"tt.sales_dept","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=ReceiveOverTimeNCS.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 已开票逾期
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:ReceiveOverTimeCS")
    @GetMapping("/ReceiveOverTimeCS")
    public String ReceiveOverTimeCS(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"c.sales_dept","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=ReceiveOverTimeCS.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 调度延迟
     * @return
     */

    @RequiresPermissions("tms:report:transportAnalyseFrm")
    @GetMapping("/transportAnalyseFrm")
    public String transportAnalyseFrm(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?formlet=transportAnalyse.frm";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }


    /**
     * 净利润分析
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:profitsAnalyse")
    @GetMapping("/profitsAnalyse")
    public String profitsAnalyse(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","", null);
        if(user.getDeptId() != null && user.getDeptId() == 1121l){
            sqlString = "";
        }
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=profitsAnalyse.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 净利润分析
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:profitsAnalyse_HS")
    @GetMapping("/profitsAnalyse_HS")
    public String profitsAnalyse_HS(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","", "cust.customer_id");
        if(user.getDeptId() != null && user.getDeptId() == 1121l){
            sqlString = "";
        }
        boolean permitted = ShiroUtils.getSubject().isPermitted("tms:report:profitsAnalyseGroup");
        Integer groupPermit = permitted ? 1:0;
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=profitsAnalyse_HS.cpt&dateScope="+URLEncodeUtil.encode(sqlString)+"&groupPermit="+groupPermit;
        map.put("url",url);
        return prefix + "/operation_analyse";
    }


    /**
     * 客户分析
     * @return
     */
    @RequiresPermissions("tms:report:CustKPIAnalyse")
    @GetMapping("/CustKPIAnalyse")
    public String CustKPIAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?formlet=CustKPIAnalyse.frm";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }



    /**
     * 未开票逾期 业务
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:ReceiveOverTimeNCS_SALES")
    @GetMapping("/ReceiveOverTimeNCS_SALES")
    public String ReceiveOverTimeNCS_SALES(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"tt.sales_dept","", "tt.customer_id");
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=ReceiveOverTimeNCS_SALES.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url", url);
        return prefix + "/operation_analyse";
    }


    /**
     * 回款跟进情况
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:ReceiveBack_SALES")
    @GetMapping("/ReceiveBack_SALES")
    public String ReceiveBack_SALES(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","", "cust.customer_id");
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=ReceiveBack_SALES.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 回款跟进情况
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:ReceiveBack")
    @GetMapping("/ReceiveBack")
    public String ReceiveBack(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"c.sales_dept","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=ReceiveBack.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 已开票逾期 运营组
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:ReceiveOverTimeCS_SALES")
    @GetMapping("/ReceiveOverTimeCS_SALES")
    public String ReceiveOverTimeCS_SALES(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","", "cust.customer_id");
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=ReceiveOverTimeCS_SALES.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 销售经营报表 运营组
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:invoiceInfo_SALES")
    @GetMapping("/invoiceInfo_SALES")
    public String invoiceInfo_SALES(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","", "cust.customer_id");
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=invoiceInfo_SALES.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 业务营运状况汇总表 运营组
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:invoiceInfoTotal_SALES")
    @GetMapping("/invoiceInfoTotal_SALES")
    public String invoiceInfoTotal_SALES(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","", "cust.customer_id");
        if(user.getDeptId() != null && user.getDeptId() == 1121l){
            sqlString = "";
        }
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=invoiceInfoTotal_SALES.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 岗位工作量汇总
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:workloadAnalyse")
    @GetMapping("/workloadAnalyse")
    public String workloadAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=workloadAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }


    /**
     * 业务营运状况汇总表 运营组
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:operationOverviewDetailDetailDailyTms")
    @GetMapping("/operationOverviewDetailDetailDailyTms")
    public String operationOverviewDetailDetailDailyTms(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"dept.dept_id","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=operationOverviewDetailDetailDailyTms.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 业务营运状况汇总表 运营组
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:operationOverviewDetailDetailDailyTmsFML")
    @GetMapping("/operationOverviewDetailDetailDailyTmsFML")
    public String operationOverviewDetailDetailDailyTmsFML(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"dept.dept_id","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=operationOverviewDetailDetailDailyTmsFML.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 业务营运状况汇总表 运营组
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:operationOverviewDetailDetailDailyTmsDML")
    @GetMapping("/operationOverviewDetailDetailDailyTmsDML")
    public String operationOverviewDetailDetailDailyTmsDML(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"dept.dept_id","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=operationOverviewDetailDetailDailyTmsDML.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 岗位工作量汇总
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:DP3_detail")
    @GetMapping("/DP3_detail")
    public String dP3_detail(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=DP3_detail.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 岗位工作量汇总
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:custImportantInfo")
    @GetMapping("/custImportantInfo")
    public String custImportantInfo(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=custImportantInfo.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 单笔承运商结算分析
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:payRecordAnalyse")
    @GetMapping("/payRecordAnalyse")
    public String payRecordAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=payRecordAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }


    /**
     * 专线未上传回单
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:carrierPicWillOvertimeReport")
    @GetMapping("/carrierPicWillOvertimeReport")
    public String carrierPicWillOvertimeReport(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=carrierPicWillOvertimeReport.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 专线未正本回单
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:carrierWillOvertimeReport")
    @GetMapping("/carrierWillOvertimeReport")
    public String carrierWillOvertimeReport(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=carrierWillOvertimeReport.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }


    /**
     * 非专线未上传回单
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:carrierOtherPicWillOvertimeReport")
    @GetMapping("/carrierOtherPicWillOvertimeReport")
    public String carrierOtherPicWillOvertimeReport(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=carrierOtherPicWillOvertimeReport.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 非专线未正本回单
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:carrierOtherWillOvertimeReport")
    @GetMapping("/carrierOtherWillOvertimeReport")
    public String carrierOtherWillOvertimeReport(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=carrierOtherWillOvertimeReport.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 净利润分析
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:netProfitsAnalyse")
    @GetMapping("/netProfitsAnalyse")
    public String netProfitsAnalyse(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=netProfitsAnalyse.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 客服工作统计
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:custServiceAnalyse")
    @GetMapping("/custServiceAnalyse")
    public String custServiceAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=custServiceAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 无指导价分析
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:noGuridPriceAnalyse")
    @GetMapping("/noGuridPriceAnalyse")
    public String noGuridPriceAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=noGuridPriceAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 特殊指导价分析
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:guridPriceSpecialAnalyse")
    @GetMapping("/guridPriceSpecialAnalyse")
    public String guridPriceSpecialAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=guridPriceSpecialAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 工作量统计
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:jobWrokloadStatistics")
    @GetMapping("/jobWrokloadStatistics")
    public String jobWrokloadStatistics(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString = DataScopeAspect.getDataScopeSqlString(user, "salesTarget.SALES_DEPT", "", null);
        String serviceName = "";
        if(StringUtils.isNotBlank(sqlString)){
            serviceName = user.getUserName();
        }
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=jobWrokloadStatistics.cpt&serviceName="+serviceName;
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 发货单分析
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:invoiceInfoAnalyse0707")
    @GetMapping("/invoiceInfoAnalyse0707")
    public String invoiceInfoAnalyse0707(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=invoiceInfoAnalyse0707.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 发货单分析
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:invoiceInfoAnalyseJD")
    @GetMapping("/invoiceInfoAnalyseJD")
    public String invoiceInfoAnalyseJD(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=invoiceInfoAnalyseJD.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 客户发货单分析
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:custInvoiceAnalyseTotal")
    @GetMapping("/custInvoiceAnalyseTotal")
    public String custInvoiceAnalyseTotal(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=custInvoiceAnalyseTotal.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 发货单分析
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:invoiceInfoAnalyseWeek")
    @GetMapping("/invoiceInfoAnalyseWeek")
    public String invoiceInfoAnalyseWeek(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=invoiceInfoAnalyseWeek.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    @RequiresPermissions("tms:report:actualInvoiceBlongAnalyse")
    @GetMapping("/actualInvoiceBlongAnalyse")
    public String actualInvoiceBlongAnalyse(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"salesTarget.SALES_DEPT","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=actualInvoiceBlongAnalyse.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    /**
     * 正本回单分析
     * @param map
     * @return
     */
    @RequiresPermissions("tms:report:receiptAnalyse")
    @GetMapping("/receiptAnalyse")
    public String receiptAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?formlet=receiptAnalyse.frm";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    @RequiresPermissions("tms:report:kfWorkAnalyse")
    @GetMapping("/kfWorkAnalyse")
    public String kfWorkAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=kfWorkAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    @RequiresPermissions("tms:report:rfqAnalyse")
    @GetMapping("/rfqAnalyse")
    public String rfqAnalyse(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"t.sales_dept","", "c.customer_id");
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=rfqAnalyse.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }


    @RequiresPermissions("tms:report:onWayFeeAnalyse")
    @GetMapping("/onWayFeeAnalyse")
    public String onWayFeeAnalyse(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=onWayFeeAnalyse.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    @RequiresPermissions("tms:report:lotRecommendAnalyse")
    @GetMapping("/lotRecommendAnalyse")
    public String lotRecommendAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=lotRecommendAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    @RequiresPermissions("tms:report:lotRecommendCarrierAnalyse")
    @GetMapping("/lotRecommendCarrierAnalyse")
    public String lotRecommendCarrierAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=lotRecommendCarrierAnalyse.cpt";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }


    //定金报表跳转
    @RequiresPermissions("tms:report:depositAnalyse")
    @GetMapping("/depositAnalyse")
    public String depositAnalyse(ModelMap map) {
        //查询当前用户数据权限
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"lot.trans_line_id","",null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=depositAnalyse.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url", url);
        return prefix + "/operation_analyse";
    }

    @RequiresPermissions("tms:report:waitSegmentInvoice")
    @GetMapping("/waitSegmentInvoice")
    public String waitSegmentInvoice(ModelMap map) {
        //查询当前用户数据权限
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"t.trans_line_id","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?formlet=waitSegmentInvoice.frm&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }


    @RequiresPermissions("tms:report:updateGuidePrice")
    @GetMapping("/updateGuidePrice")
    public String updateGuidePrice(ModelMap map) {
        //查询当前用户数据权限
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"t.trans_line_id","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=updateGuidePrice.cpt&op=write&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    @RequiresPermissions("tms:report:updateGuidePriceLot")
    @GetMapping("/updateGuidePriceLot")
    public String updateGuidePriceLot(ModelMap map) {
        //查询当前用户数据权限
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"t.trans_line_id","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=updateGuidePriceLot.cpt&op=write&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    @RequiresPermissions("tms:report:invoiceHTAnalyse")
    @GetMapping("/invoiceHTAnalyse")
    public String invoiceHTAnalyse(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=invoiceHTAnalyse.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    @RequiresPermissions("tms:report:profitsAnalyse_HS_WB")
    @GetMapping("/profitsAnalyse_HS_WB")
    public String profitsAnalyse_HS_WB(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=profitsAnalyse_HS_WB.cpt&dateScope=";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    @RequiresPermissions("tms:report:invoiceInfo_CBSJ")
    @GetMapping("/invoiceInfo_CBSJ")
    public String invoiceInfo_CBSJ(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=invoiceInfo_CBSJ.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    @RequiresPermissions("tms:report:invoiceInfo_CBSJ_dispatch")
    @GetMapping("/invoiceInfo_CBSJ_dispatch")
    public String invoiceInfo_CBSJ_dispatch(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=invoiceInfo_CBSJ_dispatch.cpt&dateScope=";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }


    @RequiresPermissions("tms:report:quotationAnalyse")
    @GetMapping("/quotationAnalyse")
    public String quotationAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=quotationAnalyse.cpt&dateScope=";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    @RequiresPermissions("tms:report:g7PushCheckAnalyseKF")
    @GetMapping("/g7PushCheckAnalyseKF")
    public String g7PushCheckAnalyseKF(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=g7PushCheckAnalyseKF.cpt&dateScope=";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }


    @RequiresPermissions("tms:report:profitsAnalyse_HS_group")
    @GetMapping("/profitsAnalyse_HS_group")
    public String profitsAnalyse_HS_group(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=profitsAnalyse_HS_group.cpt&dateScope=";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }


    @RequiresPermissions("tms:report:userOperateTotal")
    @GetMapping("/userOperateTotal")
    public String userOperateTotal(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=userOperateTotal.cpt&dateScope=";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    @RequiresPermissions("tms:report:carrierLineAnalyse")
    @GetMapping("/carrierLineAnalyse")
    public String carrierLineAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=carrierLineAnalyse.cpt&op=view";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    @RequiresPermissions("tms:report:taxRateCust")
    @GetMapping("/taxRateCust")
    public String taxRateCust(ModelMap map) {
        SysUser user = shiroUtils.getSysUser();
        String sqlString =  DataScopeAspect.getDataScopeSqlString(user,"cust.sales_dept","", null);
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=taxRateCust.cpt&dateScope="+URLEncodeUtil.encode(sqlString);
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    @RequiresPermissions("tms:report:taxRateDispatch")
    @GetMapping("/taxRateDispatch")
    public String taxRateDispatch(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?reportlet=taxRateDispatch.cpt&dateScope=";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

    @RequiresPermissions("tms:report:carrierCreateAnalyse")
    @GetMapping("/carrierCreateAnalyse")
    public String carrierCreateAnalyse(ModelMap map) {
        String url = fineHttpHead+fineReportPort+"/WebReport/ReportServer?formlet=carrierCreateAnalyse.frm";
        map.put("url",url);
        return prefix + "/operation_analyse";
    }

}
