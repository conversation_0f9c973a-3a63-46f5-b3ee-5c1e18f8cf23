package com.ruoyi.tms.controller.basic;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.text.MoneyUtil;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.interceptor.annotation.RepeatSubmit;
import com.ruoyi.framework.web.service.DictService;
import com.ruoyi.system.domain.SysDictData;
import com.ruoyi.system.domain.SysUploadFile;
import com.ruoyi.system.domain.SysUser;
import com.ruoyi.system.mapper.SysDictDataMapper;
import com.ruoyi.system.mapper.SysUploadFileMapper;
import com.ruoyi.system.service.ISysUploadFileService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.tms.constant.AppPicEnum;
import com.ruoyi.tms.constant.BillingMethod;
import com.ruoyi.tms.constant.basic.BalaTypeEnum;
import com.ruoyi.tms.constant.basic.CarrierTypeEnum;
import com.ruoyi.tms.constant.basic.PersionalDriverPicEnum;
import com.ruoyi.tms.constant.basic.driver.DriverType;
import com.ruoyi.tms.constant.carrier.EntrustLotStatusEnum;
import com.ruoyi.tms.domain.basic.*;
import com.ruoyi.tms.domain.carrier.CarrierBilling;
import com.ruoyi.tms.domain.carrier.CarrierBillingVO;
import com.ruoyi.tms.domain.carrier.EntrustLot;
import com.ruoyi.tms.domain.client.AutoDispatchConfig;
import com.ruoyi.tms.domain.client.AutoDispatchSection;
import com.ruoyi.tms.domain.client.CustPic;
import com.ruoyi.tms.domain.finance.*;
import com.ruoyi.tms.domain.segment.Segment;
import com.ruoyi.tms.dto.FixCarrierDTO;
import com.ruoyi.tms.dto.contract.ContractCarrierDTO;
import com.ruoyi.tms.mapper.basic.CarrierCheckMapper;
import com.ruoyi.tms.mapper.basic.CarrierMapper;
import com.ruoyi.tms.mapper.basic.CarrierPeriodInfoMapper;
import com.ruoyi.tms.mapper.carrier.TransCapacityMapper;
import com.ruoyi.tms.mapper.client.AutoDispatchConfigMapper;
import com.ruoyi.tms.mapper.client.AutoDispatchSectionMapper;
import com.ruoyi.tms.mapper.contract.MContractCarrierMapper;
import com.ruoyi.tms.mapper.finance.MCarrierMarginMapper;
import com.ruoyi.tms.mapper.finance.PayDetailMapper;
import com.ruoyi.tms.mapper.segment.SegmentMapper;
import com.ruoyi.tms.service.basic.IBankInfoService;
import com.ruoyi.tms.service.basic.ICarService;
import com.ruoyi.tms.service.basic.ICarrierService;
import com.ruoyi.tms.service.basic.IDriverService;
import com.ruoyi.tms.service.carrier.*;
import com.ruoyi.tms.service.mobile.MobileUserService;
import com.ruoyi.tms.service.segment.ISegmentService;
import com.ruoyi.tms.service.trace.IEntPackGoodsService;
import com.ruoyi.tms.util.HtmlToPdf;
import com.ruoyi.tms.vo.basic.CarrierDispatchVO;
import com.ruoyi.tms.vo.basic.CarrierVO;
import com.ruoyi.tms.vo.basic.ContractVO;
import com.ruoyi.tms.vo.client.AutoDispatchVO;
import com.ruoyi.tms.vo.client.ClientPopupVO;
import com.ruoyi.tms.vo.contract.ContractCarrierVO;
import com.ruoyi.util.ShiroUtils;
import io.swagger.models.auth.In;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基础数据-承运商管理 信息操作处理
 *
 * <AUTHOR>
 * @date 2019-09-10
 */
@Controller
@RequestMapping("/basic/carrier")
public class CarrierController extends BaseController {
    private static String PREFIX = "tms/basic/carrier";

    @Resource
    private ICarrierService carrierService;

    @Autowired
    IEntrustLotService entrustLotService;

    @Autowired
    IEntrustService entrustService;

    @Autowired
    private MobileUserService mobileUserService;

    @Autowired
    private IBankInfoService bankInfoService;

    @Autowired
    private ISegmentService segmentService;

    @Autowired
    private ILotOutService lotOutService;

    @Autowired
    private ILotOutDtlService lotOutDtlService;

    @Autowired
    private IDriverService driverService;

    @Autowired
    private IEntrustLotCarDriverService entrustLotCarDriverService;

    @Autowired
    private IEntPackGoodsService entPackGoodsService;

    @Autowired
    private ICarService carService;

    @Autowired
    private CarrierMapper carrierMapper;
    @Autowired
    private ISysUploadFileService sysUploadFileService;
    @Autowired
    private ShiroUtils shiroUtils;
    @Autowired
    private CarrierCheckMapper carrierCheckMapper;
    @Resource
    private SegmentMapper segmentMapper;
    @Autowired
    private DictService dictService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private PayDetailMapper payDetailMapper;
    @Autowired
    private SysDictDataMapper sysDictDataMapper;
    @Autowired
    private MContractCarrierMapper contractCarrierMapper;
    @Resource
    private AutoDispatchConfigMapper autoDispatchConfigMapper;
    @Autowired
    private MCarrierMarginMapper carrierMarginMapper;
    @Autowired
    private SysUploadFileMapper sysUploadFileMapper;
    @Autowired
    private TransCapacityMapper transCapacityMapper;
    @Autowired
    private CarrierPeriodInfoMapper carrierPeriodInfoMapper;
    @Resource
    private AutoDispatchSectionMapper autoDispatchSectionMapper;
    /**
     * 承运商页面
     *
     * @return
     */
    @RequiresPermissions("basic:carrier:view")
    @GetMapping()
    public String goods(@RequestParam(required = false) String checkStatus,
                        ModelMap mmap) {
        //获取承运商类别
        mmap.put("carrierType", CarrierTypeEnum.getCarrierTypeEnum());
        if (StringUtils.isNotEmpty(checkStatus)) {
            mmap.put("checkStatus", checkStatus);
        }

        return "tms/basic/carrier/carrier";
    }

    /**
     * 查询承运商列表
     *
     * @param carrier
     * @return
     */
    @RequiresPermissions("basic:carrier:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Carrier carrier) {
        startPage();
        List<Carrier> list = carrierService.selectCarrierMenuList(carrier);
        return getDataTable(list);
    }

    /**
     * 承运商合同列表搜索
     *
     * @param carrier
     * @return
     */
    @RequiresPermissions("tms:contract_carrier:carrierSearch")
    @RequestMapping(value = "/bsSuggestList", method = {RequestMethod.GET,RequestMethod.POST})
    @ResponseBody
    public TableDataInfo list(Carrier carrier, @RequestParam Map<String, Object> params) {
        String carrName = params.get("carrName") == null ? "" : String.valueOf(params.get("carrName"));
        if(StringUtils.isEmpty(carrName)) {
            startPage();
            List<Carrier> list = new ArrayList<>();
            return getDataTable(list);
        }
        startPage();
        List<Carrier> list = carrierService.selectCarrierMenuList(carrier);
        return getDataTable(list);
    }

    /**
     * 查询承运商列表(选择框)
     *
     * @param carrier
     * @return
     */
    //@RequiresPermissions(value = {"basic:driver:list","tms:segment:dispatch","tms:fleet:segment:dispatch"}, logical = Logical.OR)
    @PostMapping("/listCarrier")
    @ResponseBody
    public TableDataInfo listCarrier(Carrier carrier) {
        startPage();
        List<Carrier> list = carrierService.selectCarrier(carrier);
        return getDataTable(list);
    }

    /**
     * 查询承运商列表(选择框)--调度画面用
     * @param carrier
     * @return
     */
    @PostMapping("/listCarrierDispatch")
    @ResponseBody
    public TableDataInfo listCarrierDispatch(CarrierDispatchVO carrier) {

        Segment segment = segmentMapper.selectSegmentById(carrier.getSegmentId());
        carrier.setDeliArea(segment.getDeliAreaId());
        carrier.setArriArea(segment.getArriAreaId());
        carrier.setCarType(segment.getCarType());
        carrier.setCarLen(segment.getCarLen());

        startPage();
        List<CarrierDispatchVO> list = carrierService.selectCarrierDispatch(carrier);

        /*if(null != list){
            for(CarrierDispatchVO vo : list){
                Map<String,String> retMap = carrierMapper.selectCarrierCarAndDriverCnt(vo.getCarrierId());
                vo.setCarCnt(retMap.get("CAR_CNT"));
                vo.setDriverCnt(retMap.get("DRIVER_CNT"));
            }
        }*/
        return getDataTable(list);
    }

    /**
     * 新增承运商页面
     *
     * @return
     */
    @RequiresPermissions("basic:carrier:add")
    @GetMapping("/add")
    public String add(@RequestParam Map<String, Object> param, ModelMap map) {
        //App转为承运商
        map.putAll(param);
        if (StringUtils.isEmpty(String.valueOf(param.get("mobileUserId")))) {
            param.put("mobileUserId","");
        }
        Map<Integer, Object> dictMap = mobileUserService.selectAppPic(String.valueOf(param.get("mobileUserId")));
        map.put("dictMap", dictMap);
        List<CustPic> custPicList = mobileUserService.selectAppPicList(String.valueOf(param.get("mobileUserId")));
        map.put("carrierPicList", custPicList);
        String lineMaxNumber = carrierService.selectCountLineMax();
        //关注线路最大个数
        map.put("lineMaxNumber",lineMaxNumber);
        //获取上传图片类型
        map.put("carrPicList", AppPicEnum.getAppPicEnum());
        //获取承运商类别
        map.put("carrierType", CarrierTypeEnum.getCarrierTypeEnum());
        //获取结算方式
        map.put("balaType", BalaTypeEnum.getAllToMap());

        //Map<String, Object> dataBase = new HashMap<>();
        //货品分类代码
        //map.put("transportType", dictService.getDictValueLabelMap("transport_type"));

        return PREFIX + "/add";
    }

    /**
     * 新增保存承运商
     *
     * @param carrier 承运商信息
     * @return
     */
    @RepeatSubmit
    @RequiresPermissions("basic:carrier:add")
    @Log(title = "新增承运商", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestParam Map<String, String> param,@Validated Carrier carrier) {
        //手机号验证
        SysUser sysUser = new SysUser();
        sysUser.setUserType("2");
        sysUser.setPhonenumber(carrier.getPhone());
        if( UserConstants.USER_PHONE_NOT_UNIQUE.equals(sysUserService.checkPhoneUnique(sysUser))){
            return AjaxResult.error("手机号码已存在");
        }
        //得到页面关注线路个数
        int carrLineListSize = getLineListSize(carrier);
        //查询参数设置关注线路个数
        String lineMaxNumber =carrierService.selectCountLineMax();
        //不能超过最大关注线路数
        if (carrLineListSize > Integer.parseInt(lineMaxNumber)) {
            return AjaxResult.error("关注线路最多只能关注" + lineMaxNumber + "条");
        }
        //carrier.setSalt(shiroUtils.randomSalt());
        carrier.setCarrierId(IdUtil.simpleUUID());
        //carrier.setPassword(passwordService.encryptPassword(carrier.getCarrierId(), configService.selectConfigByKey("sys.user.initPassword"), carrier.getSalt()));
        carrier.setRegScrId("carrier_basic");
        carrier.setCorScrId("carrier_basic");
        return toAjax(carrierService.insertCarrier(param,carrier));
    }

    /**
     * 修改承运商页面
     *
     * @param carrierId 承运商id
     * @param mmap
     * @return
     */
    @RequiresPermissions("basic:carrier:edit")
    @GetMapping("/edit/{carrierId}")
    public String edit(@PathVariable("carrierId") String carrierId, ModelMap mmap) {
        Carrier carrier = carrierService.selectCarrierById(carrierId);
        mmap.put("carrier", carrier);
        //附件所对应的value
        Map<Integer, Object> dictMap = carrierService.selectCarrPicByCarrId(carrierId);
        mmap.put("dictMap", dictMap);
        //附件所对应的Tid
        List<CarrierPic> carrierPicList = carrierService.selectCarrPicListById(carrierId, getFilePathPrefix());
        mmap.put("carrierPicList", carrierPicList);
        //获取枚举所有类型，图片初始化
        mmap.put("carrPicList", AppPicEnum.getAppPicEnum());
        //关注线路最大个数
        mmap.put("lineMaxNumber",carrierService.selectCountLineMax());
        //获取承运商类别
        mmap.put("carrierType", CarrierTypeEnum.getCarrierTypeEnum());
        //获取结算方式
        mmap.put("balaType", BalaTypeEnum.getAllToMap());

        //判断是否存在已经支付的应付
        Integer cnt = carrierService.countHasPayCount(carrier.getCarrierId());
        if(cnt > 0){
            mmap.put("hasPayCnt",1);
        }else{
            mmap.put("hasPayCnt",0);
        }

        return PREFIX + "/edit";
    }

    /**
     * 修改固定承运商页面
     *
     * @param carrierId 承运商id
     * @param mmap
     * @return
     */
    @GetMapping("/fixEdit/{carrierId}")
    public String fixEdit(@PathVariable("carrierId") String carrierId, ModelMap mmap) {
        Carrier carrier = carrierService.selectCarrierById(carrierId);
        mmap.put("carrier", carrier);
        //附件所对应的value
        Map<Integer, Object> dictMap = carrierService.selectCarrPicByCarrId(carrierId);
        mmap.put("dictMap", dictMap);
        //附件所对应的Tid
        List<CarrierPic> carrierPicList = carrierService.selectCarrPicListById(carrierId, getFilePathPrefix());
        mmap.put("carrierPicList", carrierPicList);
        //获取枚举所有类型，图片初始化
        mmap.put("carrPicList", AppPicEnum.getAppPicEnum());
        //关注线路最大个数
        mmap.put("lineMaxNumber",carrierService.selectCountLineMax());
        //获取承运商类别
        mmap.put("carrierType", CarrierTypeEnum.getCarrierTypeEnum());
        //获取结算方式
        mmap.put("balaType", BalaTypeEnum.getAllToMap());
        return PREFIX + "/fixEdit";
    }

    /**
     * 修改保存承运商
     *
     * @param carrier
     * @return
     */
    @RepeatSubmit
    @RequiresPermissions("basic:carrier:edit")
    @Log(title = "修改承运商", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestParam Map<String, String> param,@Validated Carrier carrier) {
        //得到关注线路个数
        int carrLineListSize = getLineListSize(carrier);
        //查询参数设置关注线路个数
        String lineMaxNumber =carrierService.selectCountLineMax();
        //不能超过最大关注线路数
        if (carrLineListSize > Integer.parseInt(lineMaxNumber)) {
            return AjaxResult.error("关注线路最多只能关注" + lineMaxNumber + "条");
        }
        carrier.setCorScrId("carrier_edit");
        Carrier carrierInfo = carrierService.selectCarrierById(carrier.getCarrierId());
        if(!carrierInfo.getCorDate().equals(carrier.getCorDate())){
            return AjaxResult.warn("该数据已被其他人操作");
        }
        //判断修改姓名，身份证号，联系人，联系方式
        if(!carrierInfo.getCarrName().equals(carrier.getCarrName()) || !carrierInfo.getLegalCard().equals(carrier.getLegalCard())
                || !carrierInfo.getContact().equals(carrier.getContact()) || !carrierInfo.getPhone().equals(carrier.getPhone()) ){
            //判断是否存在已经支付的应付
            Integer cnt = carrierService.countHasPayCount(carrier.getCarrierId());
            if(cnt > 0){
                return AjaxResult.warn("存在已申请或已付款应付，无法修改承运商关键信息");
            }
        }
        return toAjax(carrierService.updateCarrier(param, carrier));
    }

    /**
     * 删除承运商基本信息-关联银行卡-关联关注线路
     *
     * @param ids 基本信息ids
     * @return
     */
    @RepeatSubmit
    @RequiresPermissions(value = {"basic:carrier:remove","tms:order:carrier:remove"}, logical = Logical.OR)
    @Log(title = "承运商银行卡", businessType = BusinessType.DELETE)
    @PostMapping("/removeCarr")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(carrierService.deleteCarrierByIds(ids));
    }

    /**
     * 承运人选择
     *
     * @param
     * @return
     */
    //@RequiresPermissions(value = {"basic:driver:list","tms:segment:dispatch","tms:fleet:segment:dispatch","tms:trustDeed:editCarrier"}, logical = Logical.OR)
    @RequestMapping("/selectCarrier")
    public String selectCarrier(@RequestParam(value = "carrType",required = false) String carrType,ModelMap mmap) {
        mmap.put("carrType",carrType);
        return PREFIX + "/selectCarrier";
    }

    /**
     * 承运人选择-调度画面用
     *
     * @param
     * @return
     */
    @RequiresPermissions(value = {"basic:driver:list","tms:segment:dispatch","tms:fleet:segment:dispatch","tms:trustDeed:editCarrier"}, logical = Logical.OR)
    @RequestMapping("/selectCarrierDispatch")
    public String selectCarrierDispatch(@RequestParam(value = "carrType",required = false) String carrType,@RequestParam(value = "segmentId",required = false) String segmentId,
                                        @RequestParam(value = "isFleet",required = false) boolean isFleet,ModelMap mmap) {
        mmap.put("carrType",carrType);
        if(StringUtils.isNotBlank(segmentId)) {
            segmentId = segmentId.split(",")[0];

        }

        Segment segment = segmentMapper.selectSegmentById(segmentId);
        mmap.put("deliAreaCode", segment.getDeliAreaId());
        mmap.put("arriAreaCode", segment.getArriAreaId());
        mmap.put("carLen", segment.getCarLen());
        mmap.put("carLenName",segment.getCarLenName());
        mmap.put("carType", segment.getCarType());
        mmap.put("carTypeName",segment.getCarTypeName());
        mmap.put("segmentId", segmentId);
        String deliFullAddr = segment.getDeliProName()+segment.getDeliCityName()+segment.getDeliAreaName();
        mmap.put("deliFullAddr",deliFullAddr);
        String arriFullAddr = segment.getArriProName()+segment.getArriCityName()+segment.getArriAreaName();
        mmap.put("arriFullAddr",arriFullAddr);
        mmap.put("isFleet",isFleet);

        return PREFIX + "/selectCarrier_dispatch";
    }

    /**
     * 承运人选择
     *
     * @param
     * @return
     */
    @RequiresPermissions(value = {"basic:driver:list","tms:segment:dispatch","tms:fleet:segment:dispatch","tms:myCarrier:carrierCar","tms:myCarrier:carrierDriver"}, logical = Logical.OR)
    @RequestMapping("/findCarrier")
    public String findCarrier(@RequestParam(value = "carrType",required = false) String carrType,
                              @RequestParam(value = "isRadio",required = false) Integer isRadio,
                              @RequestParam(value = "carrierId",required = false) String carrierId,
                              ModelMap mmap) {
        mmap.put("carrType",carrType);
        mmap.put("isRadio", isRadio != null && isRadio == 1);
        if(StringUtils.isBlank(carrierId)){
            mmap.put("carrierArr",new ArrayList<Map<String,Object>>());
        }else{
            List<Map<String,String>> carrierArr = new ArrayList<>();
            String[] split = carrierId.split(",");
            for(String id : split){
                Carrier carrier = carrierMapper.selectCarrierById(id);
                Map<String,String> map = new HashMap<>();
                if(carrier != null){
                    map.put("carrierId",carrier.getCarrierId());
                    map.put("carrCode",carrier.getCarrCode());
                    map.put("carrierName",carrier.getCarrName());
                    carrierArr.add(map);
                }

            }
            mmap.put("carrierArr",carrierArr);
        }
        return PREFIX + "/findCarrier";
    }

    /**
     * 承运人选择
     *
     * @param
     * @return
     */
    @RequiresPermissions(value = {"basic:driver:list","tms:segment:dispatch","tms:fleet:segment:dispatch","tms:myCarrier:carrierCar","tms:myCarrier:carrierDriver"}, logical = Logical.OR)
    @RequestMapping("/findCarrierContract")
    public String findCarrierContract(@RequestParam(value = "carrType",required = false) String carrType,
                              @RequestParam(value = "isRadio",required = false) Integer isRadio,
                              @RequestParam(value = "carrierId",required = false) String carrierId,
                              ModelMap mmap) {
        mmap.put("carrType",carrType);
        mmap.put("isRadio", isRadio != null && isRadio == 1);
        if(StringUtils.isBlank(carrierId)){
            mmap.put("carrierArr",new ArrayList<Map<String,Object>>());
        }else{
            List<Map<String,String>> carrierArr = new ArrayList<>();
            String[] split = carrierId.split(",");
            for(String id : split){
                Carrier carrier = carrierMapper.selectCarrierById(id);
                Map<String,String> map = new HashMap<>();
                if(carrier != null){
                    map.put("carrierId",carrier.getCarrierId());
                    map.put("carrCode",carrier.getCarrCode());
                    map.put("carrierName",carrier.getCarrName());
                    carrierArr.add(map);
                }

            }
            mmap.put("carrierArr",carrierArr);
        }
        return PREFIX + "/findCarrierContract";
    }

    /**
     * 承运商页面明细
     *
     * @param carrierId 承运商id
     * @param mmap
     * @param type detailSave 明细修改保存；默认明细查看
     * @return
     */
    @RequiresPermissions(value={"basic:carrier:detail", "basic:carrier:detailSave"}, logical=Logical.OR)
    @GetMapping("/detail")
    public String detail(String carrierId,@RequestParam(value = "type",required = false) String type,ModelMap mmap) {
        Carrier carrier = carrierService.selectCarrierById(carrierId);
        mmap.put("carrier", carrier);
        //附件所对应的value
        Map<Integer, Object> dictMap = carrierService.selectCarrPicByCarrId(carrierId);
        mmap.put("dictMap", dictMap);
        //附件所对应的Tid
        List<CarrierPic> carrierPicList = carrierService.selectCarrPicListById(carrierId, getFilePathPrefix());
        mmap.put("carrierPicList", carrierPicList);
        //获取枚举所有类型，图片初始化
        mmap.put("carrPicList", AppPicEnum.getAppPicEnum());
        //获取承运商类别
        mmap.put("carrierType", CarrierTypeEnum.getCarrierTypeEnum());
        //获取结算方式
        mmap.put("balaType", BalaTypeEnum.getAllToMap());

        //获取司机信息
        List<Driver> driverList = driverService.selectDriverListAndPicByCarrierId(carrierId,null);
        if(driverList.isEmpty()){
            driverList = new ArrayList<>();
            driverList.add(new Driver());
        }
        mmap.put("driverList", driverList);
        //司机类型
        mmap.put("driverType", DriverType.getDriverTypeEnum());

        //获取车辆信息
        List<Car> carList = carService.selectCarListAndPicPathByCarrId(carrierId,null);
        if(carList.isEmpty()){
            carList = new ArrayList<>();
            Car car = new Car();
            car.setCarrierinfo(new Carrierinfo());
            carList.add(car);
        }
        mmap.put("carList", carList);
        if("detailSave".equals(type)){
            return PREFIX + "/detailSave";
        }
        return PREFIX + "/detail";
    }

    /**
     * 获取页面关注线路个数
     *
     * @param carrier 承运商信息
     * @return
     */
    public int getLineListSize(Carrier carrier) {
        if(carrier.getCarrLineList() == null){
            return 0;
        }
        //过滤线路空对象
        List<CarrLine> carrLineList = carrier.getCarrLineList();
        carrLineList = carrLineList.stream().filter((CarrLine c)
                -> StringUtils.isNotEmpty(c.getStartProvinceId())).collect(Collectors.toList());
        return carrLineList.size();
    }

    /**
     * 导出承运商信息
     * @param carrier
     * @return
     */
    @RequiresPermissions("basic:carrier:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Carrier carrier) {
        List<Carrier> list = carrierService.selectCarrierMenuList(carrier);
        ExcelUtil<Carrier> util = new ExcelUtil<>(Carrier.class);

        Map<String, Object> dataBase = new HashMap<>();
        dataBase.put("transportType", dictService.getDictValueLabelMap("transport_type"));
        return util.exportExcel(list, "承运商信息", dataBase);
    }


    /**
     * 导出承运商信息
     * @param carrier
     * @return
     */
    @RequiresPermissions("basic:carrier:fixExport")
    @PostMapping("/fixExport")
    @ResponseBody
    public AjaxResult fixExport(Carrier carrier) {
        carrier.setSetTransportPool(2);
        List<Carrier> list = carrierService.selectFixCarrierList(carrier);
        List<FixCarrierDTO> fixList = new ArrayList<>();
        for(Carrier ca : list){
            //保证金余额计算
            BigDecimal receiveMargin = BigDecimal.ZERO;
            BigDecimal payMargin = BigDecimal.ZERO;
            List<MCarrierMargin> carrierMarginList = carrierMarginMapper.selectCarrierMarginDetailByCarrierId(ca.getCarrierId());
            for(MCarrierMargin carrierMargin : carrierMarginList){
                if(carrierMargin.getRecPayType() == 1){
                    receiveMargin = receiveMargin.add(carrierMargin.getMarginAmount());
                }else {
                    payMargin = payMargin.add(carrierMargin.getMarginAmount());
                }
            }
            ca.setMarginLess(receiveMargin.subtract(payMargin));

            //查询是否有承运合同
            ContractCarrierDTO contractCarrier = new ContractCarrierDTO();
            contractCarrier.setPartyBId(ca.getCarrierId());
            List<ContractCarrierVO> contractCarrierVOS = contractCarrierMapper.selectContractCarrierPage(contractCarrier);
            if(contractCarrierVOS != null && contractCarrierVOS.size() != 0){
                ca.setIfHasContract(1);
            }else{
                ca.setIfHasContract(0);
            }

            FixCarrierDTO fixCarrierDTO = new FixCarrierDTO();
            BeanUtils.copyBeanProp(fixCarrierDTO,ca);
            fixList.add(fixCarrierDTO);
        }
        ExcelUtil<FixCarrierDTO> util = new ExcelUtil<>(FixCarrierDTO.class);

        Map<String, Object> dataBase = new HashMap<>();
        dataBase.put("transportType", dictService.getDictValueLabelMap("transport_type"));
        return util.exportExcel(fixList, "承运商信息", dataBase);
    }

    /**
     * 导入承运商模板
     * @return
     */
    @RequiresPermissions("basic:carrier:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate() {
        ExcelUtil<CarrierVO> util = new ExcelUtil<>(CarrierVO.class);
        return util.importTemplateExcel("承运商模板");
    }

    /**
     * 导入数据
     * @param file
     * @return
     */
    @RequiresPermissions("basic:carrier:import")
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(@RequestParam MultipartFile file) throws Exception {
        ExcelUtil<CarrierVO> util = new ExcelUtil<>(CarrierVO.class);
        List<CarrierVO> carrierVOList = util.importExcel(file.getInputStream());
        String message = carrierService.importCarrier(carrierVOList);
        return AjaxResult.success(message);
    }

    /**
     * 跳转运单合同列表
     *
     * @param carrierId 承运商id
     * @param map
     * @return
     */
    @RequiresPermissions("basic:carrier:contract")
    @GetMapping("/contract/{carrierId}")
    public String contract(@PathVariable("carrierId") String carrierId, ModelMap map) {
        map.put("lotStatus", EntrustLotStatusEnum.getAllToMap());
        map.put("carrierId",carrierId);
        return PREFIX + "/contract/list";
    }

    /**
     * 查看承运商下运单列表
     * @return
     */
    @PostMapping("/contractList")
    @ResponseBody
    public TableDataInfo contractList(EntrustLot entrustLot) {
        startPage();
        List<EntrustLot> entrustLotList = entrustLotService.selectEntrustLotList(entrustLot);
        return getDataTable(entrustLotList);
    }

    /**
     * 合同预览下载页面
	 *
     * @param entrustLotId
     * @param map
     * @return
     */
    @GetMapping("/contractView/{entrustLotId}")
    public String contractView(@PathVariable("entrustLotId") String entrustLotId, ModelMap map) {
		ContractVO contract = carrierService.selectContractById(entrustLotId);
		map.put("contract",contract);
        return PREFIX + "/contract/contract";
    }

    /**
     * 合同下载
     * @param entrustLotId
     */
    @RequiresPermissions(value = {"carrier:contract:view","carrier:contract:view-month","fleet:carrier:contract:download"},logical = Logical.OR)
    @RequestMapping("/contractDownload/{entrustLotId}")
    @ResponseBody
    public AjaxResult exportWord(@PathVariable("entrustLotId") String entrustLotId){
        String dateStr = DateFormatUtils.format(new Date(),"yyyyMMddHHmmssSSS");
        String downloadPath = HtmlToPdf.convertContract(entrustLotId,"货物运输交易协议"+dateStr);
        if(StringUtils.isBlank(downloadPath)){
            throw new BusinessException("合同下载失败！");
        }
        return AjaxResult.success("下载成功！",downloadPath);
    }

    /**
     * 检验承运商名称
     * @param  carrier
     * @return
     */
    @PostMapping("/checkCarrNameUnique")
    @ResponseBody
    public String checkGoodsTypeNameUnique(Carrier carrier){
        return carrierService.checkCarrNameUnique(carrier);
    }

    /**
     * 银行行号名称页面(弹出框)
     * @return
     */
    //@RequiresPermissions("basic:bankInfo:view")
    @GetMapping("/selectBankInfo/{bankIndex}")
    public String bankInfo(@PathVariable("bankIndex") String bankIndex, ModelMap mmap) {
        mmap.put("bankIndex",bankIndex );
        return PREFIX + "/bankInfoList";
    }

    /**
     * 查询银行行号名称
     * @param bankInfo
     * @return
     */
    //@RequiresPermissions("basic:bankInfo:list")
    @PostMapping("/bankInfoList")
    @ResponseBody
    public TableDataInfo bankInfoList(BankInfo bankInfo) {
        startPage();
        List<BankInfo> list = bankInfoService.selectBankInfoList(bankInfo);
        return getDataTable(list);
    }

    /**
     * 查询银行行号名称
     * @param bankTotal
     * @return
     */
    //@RequiresPermissions("basic:bankInfo:list")
    @PostMapping("/bankTotalList")
    @ResponseBody
    public TableDataInfo bankTotalList(BankTotal bankTotal) {
        startPage();
        List<BankTotal> list = bankInfoService.selectBankTotalList(bankTotal);
        return getDataTable(list);
    }

    /**
     * 检验承运商 是否绑定司机 ->绑定：则不能修改承运商类别
     * @param carrierId
     * @return
     */
    @PostMapping("/checkCarrType")
    @ResponseBody
    public AjaxResult checkCarrType(@RequestParam("carrierId") String carrierId) {
        Driver driver = new Driver();
        driver.setCarrierId(carrierId);
        List<Driver> driverList =  driverService.selectDriverListByCarrierId(driver,false);
        if(driverList.size() == 0 ){
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }

    /**
     * 校验银行卡号唯一性
     * @param carrBank
     * @return
     */
    @PostMapping("/checkBankCardUnique")
    @ResponseBody
    public String checkBankCardUnique(CarrBank carrBank){
        return carrierService.checkBankCardUnique(carrBank);
    }

    /**
     * 校验身份证号唯一性
     * @param carrier
     * @return
     */
    @PostMapping("/checkLegalCardUnique")
    @ResponseBody
    public String checkLegalCardUnique(Carrier carrier){
        return carrierService.checkLegalCardUnique(carrier);
    }

    /**
     * 删除银行卡
     * @param carrBank
     * @return
     */
    @PostMapping("/deleteCarrBank")
    @ResponseBody
    public AjaxResult deleteCarrBank(CarrBank carrBank) {
       return toAjax(carrierService.deleteCarrBankByCarrBankId(carrBank));
    }

    /**
     * 审核承运商
     */
    @RepeatSubmit
    @RequiresPermissions("tms:carrier:check")
    @Log(title = "承运商审核", businessType = BusinessType.UPDATE)
    @PostMapping("/check")
    @ResponseBody
    public AjaxResult check(String ids,String flag) {
        return toAjax(carrierService.checkCarrierStatusByIds(ids, flag));
    }



    /**
     * 修改结算方式
     *
     * @param carrierId
     * @param map
     * @return
     */
    @RequiresPermissions("tms:carrier:editBalaType")
    @GetMapping("/editBalaType/{carrierId}")
    public String editBalaType(@PathVariable("carrierId") String carrierId, ModelMap map) {
        Carrier carrier = carrierService.selectCarrierById(carrierId);
        map.put("carrier",carrier);
        //获取结算方式
        map.put("balaType", BalaTypeEnum.getAllToMap());
        return PREFIX + "/edit_bala_type";
    }

    /**
     * 修改跳过验证
     *
     * @param carrierId
     * @param map
     * @return
     */
    @GetMapping("/shipExamine/{carrierId}")
    public String shipExamine(@PathVariable("carrierId") String carrierId, ModelMap map) {
        Carrier carrier = carrierService.selectCarrierById(carrierId);
        map.put("carrier",carrier);
        return PREFIX + "/edit_ship_examine";
    }

    /**
     * 修改跳过验证
     *
     * @param carrierId
     * @param map
     * @return
     */
    @GetMapping("/ifHasBill/{carrierId}")
    public String ifHasBill(@PathVariable("carrierId") String carrierId, ModelMap map) {
        Carrier carrier = carrierService.selectCarrierById(carrierId);
        map.put("carrier",carrier);
        if(carrier.getBillingType() == null){
            map.put("billingType1", "6");
        }else {
            map.put("billingType1", "" + carrier.getBillingType());
        }
        //获取结算方式
        map.put("balaType", BalaTypeEnum.getAllToMap());
        //查询承运商未付金额
        List<PayDetail> payDetailList = payDetailMapper.selectPayDetailListByCarrierId(carrierId);
        BigDecimal sumUngotAmount = payDetailList.stream().map(PayDetail::getUngotAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        map.put("sumUngotAmount",sumUngotAmount);

        List<SysUploadFile> sysUploadFiles = new ArrayList<>();
        if(StringUtils.isNotBlank(carrier.getTransportTid())){
            sysUploadFiles = sysUploadFileService.selectSysUploadFileByTid(carrier.getTransportTid());
        }
        map.put("files",sysUploadFiles);
        return PREFIX + "/edit_has_bill";
    }

    /**
     * 修改跳过验证
     *
     * @param carrierId
     * @param map
     * @return
     */
    @GetMapping("/blackAll/{carrierId}")
    public String blackAll(@PathVariable("carrierId") String carrierId, ModelMap map) {
        Carrier carrier = carrierService.selectCarrierById(carrierId);
        map.put("carrier",carrier);

        Car car = new Car();
        car.setCarrierId(carrierId);
        car.setIsblacklist("0");
        List<Car> carList = carService.selectCarList(car);
        map.put("carList",carList);

        Driver driver = new Driver();
        driver.setCarrierId(carrierId);
        driver.setIsblacklist("0");
        List<Driver> driverList = driverService.selectDriverList(driver);
        map.put("driverList",driverList);
        return PREFIX + "/black_all";
    }

    @GetMapping("/unblackAll/{carrierId}")
    public String unblackAll(@PathVariable("carrierId") String carrierId, ModelMap map) {
        Carrier carrier = carrierService.selectCarrierById(carrierId);
        map.put("carrier",carrier);

        Car car = new Car();
        car.setCarrierId(carrierId);
        car.setIsblacklist("1");
        List<Car> carList = carService.selectCarList(car);
        map.put("carList",carList);

        Driver driver = new Driver();
        driver.setCarrierId(carrierId);
        driver.setIsblacklist("1");
        List<Driver> driverList = driverService.selectDriverList(driver);
        map.put("driverList",driverList);
        return PREFIX + "/unblack_all";
    }


    /**
     * 修改验证跳过
     *
     * @param carrier
     * @return
     */
    @RepeatSubmit
    @Log(title = "修改承运商", businessType = BusinessType.UPDATE)
    @PostMapping("/editShipExamine")
    @ResponseBody
    public AjaxResult editShipExamine(Carrier carrier) {
        carrier.setCorScrId("carrier");
        Integer ifHasBill = carrier.getIfHasBill();
        if(0 == ifHasBill){
            carrier.setBillingType(6);
        }
        //boolean updateFlag = carrierService.updateCarrierByAdmin(carrier);
        if (carrier.getBalaType() == 2) {//月结时重置为非熟车
            carrier.setFamiliar(0);
        }
        return toAjax(carrierService.updateCarrierByAdmin(carrier));
    }

    /**
     * 修改验证跳过
     *
     * @param carrier
     * @return
     */
    @RepeatSubmit
    @Log(title = "黑名单承运商", businessType = BusinessType.UPDATE)
    @PostMapping("/blackAll")
    @ResponseBody
    public AjaxResult blackAll(CarrierBlack carrierBlack) {
        return carrierService.blackAll(carrierBlack);
    }

    @RepeatSubmit
    @Log(title = "黑名单承运商", businessType = BusinessType.UPDATE)
    @PostMapping("/unblackAll")
    @ResponseBody
    public AjaxResult unblackAll(CarrierBlack carrierBlack) {
        return carrierService.unblackAll(carrierBlack);
    }

    /**
     * 修改保存结算方式
     *
     * @param carrier
     * @return
     */
    @RepeatSubmit
    @RequiresPermissions("tms:carrier:editBalaType")
    @Log(title = "修改承运商", businessType = BusinessType.UPDATE)
    @PostMapping("/editSaveBalaType")
    @ResponseBody
    public AjaxResult editSaveBalaType(Carrier carrier) {
        carrier.setCorScrId("carrier");
        return toAjax(carrierMapper.updateCarrier(carrier));
    }


    /**
     * 承运商明细 修改保存
     *
     * @param carrier
     * @return
     */
    @RepeatSubmit
    @RequiresPermissions("basic:carrier:detailSave")
    @Log(title = "修改明细承运商", businessType = BusinessType.UPDATE)
    @PostMapping("/detailSave")
    @ResponseBody
    public AjaxResult detailSave(Carrier carrier) {
        return carrierService.detailSaveCarrier(carrier);
    }

    /**
     * 跳转调整额配置
     * @param map
     * @param carrierId 承运商id
     * @return
     */
    @RequiresPermissions("basic:carrier:adjustmentConfig")
    @GetMapping("/adjustmentConfig")
    public String adjustmentConfig(String carrierId,ModelMap map){
        Carrier carrier = carrierService.selectCarrierById(carrierId);
        map.put("carrier", carrier);
        return PREFIX + "/adjustment_config";
    }

    /**
     * 保存调整额配置
     * @param carrier
     * @return
     */
    @RepeatSubmit
    @RequiresPermissions("basic:carrier:adjustmentConfig")
    @Log(title = "保存调整额配置", businessType = BusinessType.UPDATE)
    @PostMapping("/saveAdjustmentConfig")
    @ResponseBody
    public AjaxResult saveAdjustmentConfig(Carrier carrier) {
        return toAjax(carrierMapper.editCarrier(carrier));
    }

    @PostMapping("/selectCarrierById")
    @ResponseBody
    public Carrier selectCarrierById(String carrierId) {
        return carrierMapper.selectCarrierById(carrierId);
    }
    /**
     * 承运商页面
     *
     * @return
     */
    @RequiresPermissions("basic:carrier:myCarrier")
    @GetMapping("/myCarrier")
    public String myCarrier(ModelMap mmap) {
        //获取承运商类别
        mmap.put("carrierType", CarrierTypeEnum.getCarrierTypeEnum());
        return PREFIX + "/my_carrier";
    }

    /**
     * 查询承运商列表
     *
     * @param carrier
     * @return
     */
    @PostMapping("/listMyCarrier")
    @ResponseBody
    public TableDataInfo listMyCarrier(Carrier carrier) {
        startPage();
        if(!"1".equals(shiroUtils.getUserId().toString())){
            carrier.setBlongUserId(shiroUtils.getUserId().toString());
        }
        List<Carrier> list = carrierService.selectMyCarrierList(carrier);
        return getDataTable(list);
    }

    /**
     * 个人司机快速注册页面
     *
     * @return
     */
    @GetMapping("addMyCarrier")
    public String add(ModelMap mmap) {
        //获取承运商类别
        mmap.put("carrierType", CarrierTypeEnum.getCarrierTypeEnum());
        //附件类型
        mmap.put("persionalDriverPicList", PersionalDriverPicEnum.getPersionalDriverPicEnum());
        //司机类型
        mmap.put("driverTypeList", DriverType.getDriverTypeEnum());
        //获取结算方式
        mmap.put("balaType", BalaTypeEnum.getAllToMap());
        return "tms/basic/personal_driver/add";
    }

    /**
     * 修改承运商页面
     *
     * @param carrierId 承运商id
     * @param mmap
     * @return
     */
    @GetMapping("/MyCarrier/{carrierId}")
    public String MyCarrier(@PathVariable("carrierId") String carrierId, ModelMap mmap) {
        Carrier carrier = carrierService.selectCarrierById(carrierId);
        mmap.put("carrier", carrier);
        //获取承运商类别
        mmap.put("carrierType", CarrierTypeEnum.getCarrierTypeEnum());
        //附件类型
        mmap.put("persionalDriverPicList", PersionalDriverPicEnum.getPersionalDriverPicEnum());
        //司机类型
        mmap.put("driverTypeList", DriverType.getDriverTypeEnum());
        //获取结算方式
        mmap.put("balaType", BalaTypeEnum.getAllToMap());

        //获取新建和审核失败司机信息
        List<Driver> driverList = driverService.selectDriverListAndPicByCarrierId(carrierId,1);
       /* if(driverList.isEmpty()){
            driverList = new ArrayList<>();
            driverList.add(new Driver());
        }*/
        mmap.put("driverList", driverList);

        //获取新建和审核失败车辆信息
        List<Car> carList = carService.selectCarListAndPicPathByCarrId(carrierId,1);
        /*if(carList.isEmpty()){
            carList = new ArrayList<>();
            Car car = new Car();
            car.setCarrierinfo(new Carrierinfo());
            carList.add(car);
        }*/
        mmap.put("carList", carList);
        return "tms/basic/personal_driver/edit";
    }

    /**
     * 承运商页面明细
     *
     * @param carrierId 承运商id
     * @param mmap
     * @param type detailSave 明细修改保存；默认明细查看
     * @return
     */
    @GetMapping("/detailMyCarrier")
    public String detailMyCarrier(String carrierId,@RequestParam(value = "type",required = false) String type,ModelMap mmap) {
        Carrier carrier = carrierService.selectCarrierById(carrierId);
        mmap.put("carrier", carrier);
        //附件所对应的value
        Map<Integer, Object> dictMap = carrierService.selectCarrPicByCarrId(carrierId);
        mmap.put("dictMap", dictMap);
        //附件所对应的Tid
        List<CarrierPic> carrierPicList = carrierService.selectCarrPicListById(carrierId, getFilePathPrefix());
        mmap.put("carrierPicList", carrierPicList);
        //获取枚举所有类型，图片初始化
        mmap.put("carrPicList", AppPicEnum.getAppPicEnum());
        //获取承运商类别
        mmap.put("carrierType", CarrierTypeEnum.getCarrierTypeEnum());
        //获取结算方式
        mmap.put("balaType", BalaTypeEnum.getAllToMap());

        //获取司机信息
        List<Driver> driverList = driverService.selectDriverListAndPicByCarrierId(carrierId,0);
        if(driverList.isEmpty()){
            driverList = new ArrayList<>();
           /* driverList.add(new Driver());*/
        }
        mmap.put("driverList", driverList);
        //司机类型
        mmap.put("driverType", DriverType.getDriverTypeEnum());

        //获取车辆信息
        List<Car> carList = carService.selectCarListAndPicPathByCarrId(carrierId,0);
        if(carList.isEmpty()){
            carList = new ArrayList<>();
            /*Car car = new Car();
            car.setCarrierinfo(new Carrierinfo());
            carList.add(car);*/
        }
        mmap.put("carList", carList);
        if("detailSave".equals(type)){
            return PREFIX + "/detail_my";
        }
        return PREFIX + "/detail1";
    }

    /**
     * 审核承运商
     */
    @RepeatSubmit
    @Log(title = "承运商审核", businessType = BusinessType.UPDATE)
    @PostMapping("/pushCheck")
    @ResponseBody
    public AjaxResult check(String id,Integer checkStatus) {
        //TODO 向审核主表插入申请信息
        //TODO 车辆司机必填项判断
        //TODO 车辆司机认证
        return toAjax(carrierService.updateCarrierCheckStatusById(id, checkStatus));
    }

    /**
     * 审核承运商
     */
    @RepeatSubmit
    @Log(title = "承运商审核", businessType = BusinessType.UPDATE)
    @PostMapping("/checkCarrier")
    @ResponseBody
    public AjaxResult checkCarrier(CarrierCheck carrierCheck) {
        return carrierService.checkCarrier(carrierCheck);
    }

    /**
     * 审核承运商信息
     */
    @RepeatSubmit
    @Log(title = "承运商审核信息", businessType = BusinessType.UPDATE)
    @PostMapping("/checkCarrierInfo")
    @ResponseBody
    public AjaxResult checkCarrierInfo(CarrierCheck carrierCheck) {
        return carrierService.checkCarrierInfo(carrierCheck);
    }



    /**
     * 审核历史
     * @param carrierId
     * @param mmap
     * @return
     */
    @GetMapping("/checkHistory")
    public String checkHistory(String carrierId,ModelMap mmap) {
        //承运商信息
        Carrier carrier = carrierService.selectCarrierById(carrierId);
        mmap.put("carrier", carrier);

        //查询承运商审核记录
        List<CarrierCheck> carrierChecks = carrierCheckMapper.selectCheckByCarrierIdNotNew(carrierId);
        for(CarrierCheck carrierCheck : carrierChecks){
            //根据审核记录查询车辆司机详情
            List<CarrierCarCheck> carrierCarChecks = carrierCheckMapper.selectCarCheckByCheckId(carrierCheck.getId());
            carrierCheck.setCarrierCarCheckList(carrierCarChecks);

            List<CarrierDriverCheck> carrierDriverChecks = carrierCheckMapper.selectDriverCheckByCheckId(carrierCheck.getId());
            carrierCheck.setCarrierDriverCheckList(carrierDriverChecks);
        }
        mmap.put("carrierChecks",carrierChecks);

        return PREFIX + "/detail1";
    }

    /**
     * 修改承运商页面
     *
     * @param carrierId 承运商id
     * @param mmap
     * @return
     */
    @GetMapping("/carrierCar/{carrierId}")
    public String carrierCar(@PathVariable("carrierId") String carrierId, ModelMap mmap) {
        mmap.put("carrierId",carrierId);
        return PREFIX + "/carrier_car";
    }

    /**
     * 修改承运商页面
     *
     * @param carrierId 承运商id
     * @param mmap
     * @return
     */
    @GetMapping("/carrierDriver/{carrierId}")
    public String carrierDriver(@PathVariable("carrierId") String carrierId, ModelMap mmap) {
        mmap.put("driverTypeList", DriverType.getDriverTypeEnum());
        mmap.put("carrierId",carrierId);
        return PREFIX + "/carrier_driver";
    }

    /**
     * 承运商页面
     *
     * @return
     */
    @GetMapping("/carrierLayer")
    public String carrierLayer(@RequestParam Map<String, Object> param,ModelMap map) {
        //App转为承运商
        map.putAll(param);
        if (StringUtils.isEmpty(String.valueOf(param.get("mobileUserId")))) {
            param.put("mobileUserId","");
        }
        Map<Integer, Object> dictMap = mobileUserService.selectAppPic(String.valueOf(param.get("mobileUserId")));
        map.put("dictMap", dictMap);
        List<CustPic> custPicList = mobileUserService.selectAppPicList(String.valueOf(param.get("mobileUserId")));
        map.put("carrierPicList", custPicList);
        String lineMaxNumber = carrierService.selectCountLineMax();
        //关注线路最大个数
        map.put("lineMaxNumber",lineMaxNumber);
        //获取上传图片类型
        map.put("carrPicList", AppPicEnum.getAppPicEnum());
        //获取承运商类别
        map.put("carrierType", CarrierTypeEnum.getCarrierTypeEnum());
        //获取结算方式
        map.put("balaType", BalaTypeEnum.getAllToMap());
        return PREFIX + "/addCarrier";
    }

    @GetMapping("/disDispatch/{entrustLotId}")
    public String toDisDispatch(@PathVariable(value = "entrustLotId") String entrustLotId,ModelMap map) {
        //获取结算方式
        map.put("entrustLotId", entrustLotId);
        //查询当前用户的承运商
        Long userId = shiroUtils.getUserId();
        List<Carrier> carrierList = carrierMapper.selectCarrIdByLoginId(userId);
        if(carrierList == null || 0 == carrierList.size()){
            return "";
        }
        Carrier carrier = carrierList.get(0);
        map.put("carrier",carrier);

        String segmentIds = entrustService.selectSegmentIdsByLotId(entrustLotId);
        map.put("segmentIds",segmentIds);

        EntrustLot entrustLot = entrustLotService.selectEntrustLotById(entrustLotId);
        map.put("entrustLot",entrustLot);


        return "tms/carrier/entrustLot/disDispatch";
    }


    @GetMapping("/to_carrier_exception/{carrierId}")
    public String toCarrierException(ModelMap map, @PathVariable("carrierId") String carrierId){
        map.put("carrierId", carrierId);
        return PREFIX + "/carrier_exception/exception_list";
    }

    /**
     * 申请入驻
     *
     * @param carrierId
     * @param map
     * @return
     */
    @GetMapping("/applyJoin/{carrierId}")
    public String applyJoin(@PathVariable("carrierId") String carrierId, ModelMap map) {
        Carrier carrier = carrierService.selectCarrierById(carrierId);
        map.put("carrier",carrier);
        String userName = shiroUtils.getSysUser().getUserName();
        map.put("userName",userName);
        //查询承运商下车辆数量
        return PREFIX + "/apply_join";
    }

    /**
     * 保存申请入驻
     */
    @RepeatSubmit
    @Log(title = "保存申请入驻", businessType = BusinessType.UPDATE)
    @PostMapping("/saveApplyJoin")
    @ResponseBody
    public AjaxResult checkCarrier(Carrier carrier) {
        carrier.setSetTransportPool(2);
        return toAjax(carrierService.updateCarrierByAdmin(carrier));
    }

    /**
     * 保存线路标签
     * @param tip
     * @return
     */
    @RequestMapping("/addRoadTip")
    @ResponseBody
    public AjaxResult addRoadTip(String tip){
        //判断标签是否存在
        SysDictData road_tip = sysDictDataMapper.selectDictDataByTypeAndLabel("road_tip", tip.trim());
        if(road_tip != null){
            return AjaxResult.error("该线路便签已存在");
        }
        //查询最大值
        int max = sysDictDataMapper.selectMaxDictValueByType("road_tip");
        SysDictData addTip = new SysDictData();
        addTip.setDictSort((long) max);
        addTip.setDictLabel(tip.trim());
        addTip.setDictType("road_tip");
        addTip.setDictValue(String.valueOf(max));
        addTip.setIsDefault("Y");
        addTip.setStatus("0");
        addTip.setCreateBy(shiroUtils.getUserId().toString());
        sysDictDataMapper.insertDictData(addTip);
        //插入标签到字典表
        return AjaxResult.success();
    }



    @GetMapping("/infoCheck")
    public String infoCheck(String carrierId, Integer type , ModelMap map) {
        Carrier carrier = carrierService.selectCarrierById(carrierId);
        map.put("carrier",carrier);
        map.put("type",type);

        List<SysUploadFile> files = new ArrayList<>();
        if(StringUtils.isNotBlank(carrier.getTransportTid())){
            files = sysUploadFileService.selectSysUploadFileByTid(carrier.getTransportTid());
        }
        map.put("files",files);

        //获取承运商类别
        map.put("carrierType", CarrierTypeEnum.getCarrierTypeEnum());
        //查询是否有承运合同
        ContractCarrierDTO contractCarrier = new ContractCarrierDTO();
        contractCarrier.setPartyBId(carrier.getCarrierId());
        List<ContractCarrierVO> contractCarrierVOS = contractCarrierMapper.selectContractCarrierPage(contractCarrier);
        if(contractCarrierVOS != null && contractCarrierVOS.size() != 0){
            map.put("ifHasContact", "是");
            if(contractCarrierVOS.get(0).getEffectiveDate() != null){
                map.put("effectiveDate",DateFormatUtils.format(contractCarrierVOS.get(0).getEffectiveDate(),"yyyy-MM-dd"));
            }
            if("1".equals(contractCarrierVOS.get(0).getLongTermEffective())){
                map.put("endDate","长期");
            }else if (contractCarrierVOS.get(0).getExtensionDate() != null || contractCarrierVOS.get(0).getEndDate() != null){
                map.put("endDate",contractCarrierVOS.get(0).getExtensionDate() == null ? DateFormatUtils.format(contractCarrierVOS.get(0).getEndDate(),"yyyy-MM-dd") : DateFormatUtils.format(contractCarrierVOS.get(0).getExtensionDate(),"yyyy-MM-dd"));
            }
            if(contractCarrierVOS.get(0).getSysUploadFileList() != null ){
                map.put("sysUploadFiles",contractCarrierVOS.get(0).getSysUploadFileList());
            }
        }else{
            map.put("ifHasContact", "否");
            map.put("effectiveDate","");
            map.put("endDate","");
        }
        AutoDispatchConfig configSele = new AutoDispatchConfig();
        configSele.setCarrierId(carrier.getCarrierId());
        List<AutoDispatchConfig> autoDispatchConfigs = autoDispatchConfigMapper.selectBySame(configSele);
        if(autoDispatchConfigs != null && autoDispatchConfigs.size() != 0){
            map.put("autoConfig", "是");
        }else{
            map.put("autoConfig", "否");
        }
        BigDecimal bzjye = BigDecimal.ZERO;
        List<MCarrierMargin> carrierMarginList = carrierMarginMapper.selectCarrierMarginDetailByCarrierId(carrierId);
        for(MCarrierMargin carrierMargin : carrierMarginList){
            if(carrierMargin.getRecPayType() == 1){
                bzjye = bzjye.add(carrierMargin.getMarginAmount());
            }else{
                bzjye = bzjye.subtract(carrierMargin.getMarginAmount());
            }
            if(StringUtils.isNotBlank(carrierMargin.getAppendixId())){
                List<SysUploadFile> sysUploadFiles = sysUploadFileMapper.selectSysUploadFileByTid(carrierMargin.getAppendixId());
                carrierMargin.setSysUploadFile(sysUploadFiles);
            }
        }
        map.put("carrierMarginList", carrierMarginList);
        map.put("bzjye", bzjye);


        //查询客户的业务部门
        String salesName = carrierMapper.selectCarrierCustomerSales(carrierId);
        map.put("salesName", salesName);
        String custAbbr = carrierMapper.selectCarrierCustomerAbbr(carrierId);
        map.put("custAbbr", custAbbr);
        //查询承运线路
        TransCapacity capacity = new TransCapacity();
        capacity.setCarrierId(carrierId);
        List<TransCapacity> transCapacities = transCapacityMapper.selectList(capacity);
        map.put("transCapacities", transCapacities);
        List<CarrierBilling> carrierBillings = carrierMapper.selectCarrierBillingByCarrierId(carrierId);
        map.put("carrierBillings",carrierBillings);
        return PREFIX + "/info_check";
    }

    @GetMapping("/fixDetail")
    public String fixDetail(String carrierId, ModelMap map) {
        Carrier carrier = carrierService.selectCarrierById(carrierId);
        map.put("carrier",carrier);

        List<SysUploadFile> files = new ArrayList<>();
        if(StringUtils.isNotBlank(carrier.getTransportTid())){
            files = sysUploadFileService.selectSysUploadFileByTid(carrier.getTransportTid());
        }
        map.put("files",files);

        //获取承运商类别
        map.put("carrierType", CarrierTypeEnum.getCarrierTypeEnum());
        //查询是否有承运合同
        ContractCarrierDTO contractCarrier = new ContractCarrierDTO();
        contractCarrier.setPartyBId(carrier.getCarrierId());
        List<ContractCarrierVO> contractCarrierVOS = contractCarrierMapper.selectContractCarrierPage(contractCarrier);
        if(contractCarrierVOS != null && contractCarrierVOS.size() != 0){
            map.put("ifHasContact", "是");
            map.put("effectiveDate",DateFormatUtils.format(contractCarrierVOS.get(0).getEffectiveDate(),"yyyy-MM-dd"));
            map.put("endDate",contractCarrierVOS.get(0).getExtensionDate() == null ? DateFormatUtils.format(contractCarrierVOS.get(0).getEndDate(),"yyyy-MM-dd") : DateFormatUtils.format(contractCarrierVOS.get(0).getExtensionDate(),"yyyy-MM-dd"));
            if(contractCarrierVOS.get(0).getSysUploadFileList() != null ){

                map.put("sysUploadFiles",contractCarrierVOS.get(0).getSysUploadFileList());
            }
        }else{
            map.put("ifHasContact", "否");
            map.put("effectiveDate","");
            map.put("endDate","");
        }
        AutoDispatchConfig configSele = new AutoDispatchConfig();
        configSele.setCarrierId(carrier.getCarrierId());
        List<AutoDispatchConfig> autoDispatchConfigs = autoDispatchConfigMapper.selectBySame(configSele);
        if(autoDispatchConfigs != null && autoDispatchConfigs.size() != 0){
            map.put("autoConfig", "是");
        }else{
            map.put("autoConfig", "否");
        }

        BigDecimal bzjye = BigDecimal.ZERO;
        List<MCarrierMargin> carrierMarginList = carrierMarginMapper.selectCarrierMarginDetailByCarrierId(carrierId);
        for(MCarrierMargin carrierMargin : carrierMarginList){
            if(carrierMargin.getRecPayType() == 1){
                bzjye = bzjye.add(carrierMargin.getMarginAmount());
            }else{
                bzjye = bzjye.subtract(carrierMargin.getMarginAmount());
            }
            if(StringUtils.isNotBlank(carrierMargin.getAppendixId())){
                List<SysUploadFile> sysUploadFiles = sysUploadFileMapper.selectSysUploadFileByTid(carrierMargin.getAppendixId());
                carrierMargin.setSysUploadFile(sysUploadFiles);
            }
        }
        map.put("carrierMarginList", carrierMarginList);
        map.put("bzjye", bzjye);

        //查询客户的业务部门
        String salesName = carrierMapper.selectCarrierCustomerSales(carrierId);
        map.put("salesName", salesName);
        String custAbbr = carrierMapper.selectCarrierCustomerAbbr(carrierId);
        map.put("custAbbr", custAbbr);

        return PREFIX + "/fix_detail";
    }


    /**
     * 固定承运商
     *
     * @return
     */
    @GetMapping("/fixCarrier")
    public String fixCarrier(ModelMap mmap) {
        //获取承运商类别
        mmap.put("carrierType", CarrierTypeEnum.getCarrierTypeEnum());
        return PREFIX+"/fixCarrier";
    }

    /**
     * 保存承运商发票
     * @param carrierBillingVO
     * @return
     */
    @PostMapping("/saveB")
    @ResponseBody
    public AjaxResult saveB(CarrierBillingVO carrierBillingVO) {
        return carrierService.saveCarrierBillingVO(carrierBillingVO);
    }



    /**
     * 查询承运商列表
     *
     * @param carrier
     * @return
     */
    @PostMapping("/fixList")
    @ResponseBody
    public TableDataInfo fixList(Carrier carrier) {
        startPage();
        carrier.setSetTransportPool(2);
        List<Carrier> list = carrierService.selectFixCarrierList(carrier);
        for(Carrier ca : list){
            //保证金余额计算
            BigDecimal receiveMargin = BigDecimal.ZERO;
            BigDecimal payMargin = BigDecimal.ZERO;
            List<MCarrierMargin> carrierMarginList = carrierMarginMapper.selectCarrierMarginDetailByCarrierId(ca.getCarrierId());
            for(MCarrierMargin carrierMargin : carrierMarginList){
                if(carrierMargin.getRecPayType() == 1){
                    receiveMargin = receiveMargin.add(carrierMargin.getMarginAmount());
                }else {
                    payMargin = payMargin.add(carrierMargin.getMarginAmount());
                }
            }
            ca.setMarginLess(receiveMargin.subtract(payMargin));

            //查询是否有承运合同
            ContractCarrierDTO contractCarrier = new ContractCarrierDTO();
            contractCarrier.setPartyBId(ca.getCarrierId());
            List<ContractCarrierVO> contractCarrierVOS = contractCarrierMapper.selectContractCarrierPage(contractCarrier);
            if(contractCarrierVOS != null && contractCarrierVOS.size() != 0){
                ca.setIfHasContract(1);
            }else{
                ca.setIfHasContract(0);
            }
        }
        return getDataTable(list);
    }

    @GetMapping("/billEdit")
    public String billEdit(String carrierId,ModelMap mmap){
        mmap.put("carrierId",carrierId);
        List<CarrierBilling> carrierBillings = carrierMapper.selectCarrierBillingByCarrierId(carrierId);
        mmap.put("carrierBillings",carrierBillings);
        return PREFIX+"/billEdit";
    }

    @RequestMapping("/checkCarrierAuto")
    @ResponseBody
    public AjaxResult checkCarrierAuto(String ids){
        String[] split = ids.split(",");
        for(String id : split){
            List<CarrierPeriodInfo> carrierPeriodInfos = carrierPeriodInfoMapper.selectByCarrierId(id);
            List<AutoDispatchConfig> autoDispatchConfigs = autoDispatchConfigMapper.selectAllByCarrierId(id);
            if(carrierPeriodInfos != null && carrierPeriodInfos.size() > 0){
                if(autoDispatchConfigs != null && autoDispatchConfigs.size() > 0){
                    return AjaxResult.warn("该承运商存在协议价和自动调度配置，");
                }else{
                    return AjaxResult.warn("该承运商存在协议价配置，");
                }
            }else if(autoDispatchConfigs != null && autoDispatchConfigs.size() > 0){
                return AjaxResult.warn("该承运商存在自动调度配置，");
            }
        }
        return AjaxResult.success();
    }

    /**
     * 打印
     * @return
     */
    @RequestMapping("/applyPrint")
    public String applyPrint(String id,ModelMap map){
        Carrier carrier = carrierMapper.selectCarrierById(id);
        map.put("carrier",carrier);
        //查询承运商协议价
        AutoDispatchConfig configSele = new AutoDispatchConfig();
        configSele.setDelFlag(0);
        configSele.setCarrierId(id);
        configSele.setAutoDispatchType(2);

        List<AutoDispatchConfig> configList = autoDispatchConfigMapper.selectBySame1(configSele);

        Set<String> custAbbrSet = new HashSet<>();
        for(AutoDispatchConfig config : configList){
            custAbbrSet.add(config.getCustAbbr());
        }
        StringBuffer custAbbrBuffer = new StringBuffer();
        map.put("custAbbrSet",custAbbrSet);
        for (String element : custAbbrSet) {
            custAbbrBuffer.append(element).append("、");
        }
        if(custAbbrBuffer.length() > 0){
            map.put("custAbbrBuffer",custAbbrBuffer.toString().substring(0,custAbbrBuffer.toString().length()-1));
        }else{
            map.put("custAbbrBuffer","");
        }
        List<AutoDispatchVO.AutoDispatchConfigVO> collect = configList.stream()
                .map(x -> {
                    AutoDispatchVO.AutoDispatchConfigVO configVO = new AutoDispatchVO.AutoDispatchConfigVO();
                    BeanUtils.copyProperties(x, configVO);

                    if (configVO.getDeductionType() == 1) {
                        configVO.setDeductionAmount(NumberUtil.mul(configVO.getDeductionAmount(), 100));
                    }

                    if (configVO.getOilRatio() != null && configVO.getOilType() == 0) {
                        configVO.setOilRatio(NumberUtil.mul(configVO.getOilRatio().doubleValue(), 100));
                    }

                    return configVO;
                })
                .collect(Collectors.toList());

        for (AutoDispatchVO.AutoDispatchConfigVO autoDispatchConfigVO : collect) {

            List<AutoDispatchSection> autoDispatchSections = autoDispatchSectionMapper
                    .selectBySameId(autoDispatchConfigVO.getSameId());

            if (autoDispatchSections.size() > 0) {
                List<AutoDispatchVO.AutoDispatchSectionVO> sectionVOList = autoDispatchSections.stream()
                        .map(x -> {
                            AutoDispatchVO.AutoDispatchSectionVO sectionVO = new AutoDispatchVO.AutoDispatchSectionVO();
                            BeanUtils.copyProperties(x, sectionVO);

                            return sectionVO;
                        }).collect(Collectors.toList());

                autoDispatchConfigVO.setAutoDispatchSectionList(sectionVOList);

            }
        }


        map.put("configList",collect);

        map.put("billingMethod", BillingMethod.getAllToMap());

        return PREFIX + "/apply_print";
    }
}