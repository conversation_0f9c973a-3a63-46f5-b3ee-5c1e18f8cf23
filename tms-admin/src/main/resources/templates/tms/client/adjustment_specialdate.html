<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('调整账期')"/>
</head>
<style>
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .ml10{
        margin-left: 10px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 80px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .bg-title{
        font-weight: bold;
        font-size: 14px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-client-add" class="form-horizontal" novalidate="novalidate">
        <!--客户id-->
        <input type="hidden" name="customerId" th:value="${client.customerId}">

        <div class="panel panel-default">
<!--            <div class="panel-heading">-->
<!--                <h4 class="panel-title">-->
<!--                    <a data-toggle="collapse" data-parent="#accordion"-->
<!--                       href="tabs_panels.html#collapseTwo">基本信息</a>-->
<!--                </h4>-->
<!--            </div>-->
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div>
                        <div class="bg-title">调整账期</div>
                        <div class="mt10">
                            <div class="">
                                <div class="flex">
                                    <label class="flex_left"><span style="">对账期：</span></label>
                                    <div class="flex_right">
                                        <input name="paymentDays" id="paymentDays" th:value="${client.paymentDays}" class="form-control"
                                               type="text" min="0" oninput="$.numberUtil.onlyNumber(this)" maxlength="5" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="">
                            <div class="">
                                <div class="flex">
                                    <label class="flex_left"><span style="">申请开票期：</span></label>
                                    <div class="flex_right">
                                        <input name="invoiceDays" id="invoiceDays" th:value="${client.invoiceDays}" class="form-control"
                                               type="text" min="0" oninput="$.numberUtil.onlyNumber(this)" maxlength="5" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="">
                            <div class="">
                                <div class="flex">
                                    <label class="flex_left"><span style="">合同账期：</span></label>
                                    <div class="flex_right">
                                        <input name="collectionDays" id="collectionDays" th:value="${client.collectionDays}" class="form-control"
                                               type="text" min="0" oninput="$.numberUtil.onlyNumber(this)" maxlength="5" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="">
                            <div class="">
                                <div class="flex">
                                    <label class="flex_left"><span style="">特殊日期：</span></label>
                                    <div class="flex_right">
                                        <div class="over">
                                            <div class="fl" style="width: 90%">
                                                <input name="specialDate" id="specialDate" th:value="${client.specialDate}" class="form-control"
                                                       type="number" min="1" max="31" oninput="$.numberUtil.onlyNumber(this)" maxlength="5" autocomplete="off">
                                            </div>
                                            <div class="fl ml10">
                                                <a href="#" data-toggle="tooltip" data-placement="left" title="涉及对账收款月份非自然月的时候需要填写，例如某客户25号（包含）之后单据算到下月对账，则特殊日期填25">
                                                    <i class="fa fa-question-circle" style="color: #7f7f7f;font-size: 18px"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="">
                            <div class="bg-title">调整配置额</div>
                            <div class="mt10">
                                <div class="">
                                    <div class="flex">
                                        <label class="flex_left"><span style="color: red">*</span>调整限额：</label>
                                        <div class="flex_right">
                                            <div class="over">
                                                <div class="fl" style="width: 90%">
                                                    <div class="input-group">
                                                        <input name="adjustment" id="adjustment" th:value="${client.adjustment}" class="form-control"
                                                               type="text" min="0" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" autocomplete="off">
                                                        <span class="input-group-addon">元</span>
                                                    </div>
                                                </div>
                                                <div class="fl ml10">
                                                    <a href="javascript:;" data-toggle="tooltip" data-placement="left" title="开票申请：金额降低超出该金额时，需要线下申请">
                                                        <i class="fa fa-question-circle" style="color: #7f7f7f;font-size: 18px"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt10">
                                <div class="">
                                    <div class="flex">
                                        <label class="flex_left"><span style="color: red">*</span>平台费率：</label>
                                        <div class="flex_right">
                                            <div class="over">
                                                <div class="fl" style="width: 90%">
                                                    <input name="platRate" id="platRate" th:value="${client.platRate}" class="form-control"
                                                           type="text" min="0" oninput="$.numberUtil.onlyNumberCustom(this,999,0,5,4)" maxlength="5" autocomplete="off">
                                                </div>
                                                <div class="fl ml10">
                                                    <a href="javascript:;" data-toggle="tooltip" data-placement="left" title="3个点时填0.03，以此类推">
                                                        <i class="fa fa-question-circle" style="color: #7f7f7f;font-size: 18px"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div class="mt10">
                                <div class="">
                                    <div class="flex">
                                        <label class="flex_left"><span style="color: red">*</span>平台税点：</label>
                                        <div class="flex_right">
                                            <div class="over">
                                                <div class="fl" style="width: 90%">
                                                    <input name="platTax" id="platTax" th:value="${client.platTax}" class="form-control"
                                                           type="text" min="0" oninput="$.numberUtil.onlyNumberCustom(this,99,0,5,2)" maxlength="5" autocomplete="off">
                                                </div>
                                                <div class="fl ml10">
                                                    <a href="javascript:;" data-toggle="tooltip" data-placement="left" title="对方平台能开出的票：普票类均填1；专票类：3%专票填1.03，以此类推">
                                                        <i class="fa fa-question-circle" style="color: #7f7f7f;font-size: 18px"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="">
                            <div class="bg-title">第三方费用配置</div>
                            <div class="mt10">
                                <div class="">
                                    <div class="flex">
                                        <label class="flex_left"><span style="color: red">*</span>是否锁定：</label>
                                        <div class="flex_right">
                                            <select id="isLockOtherFee" name="isLockOtherFee" class="form-control valid"
                                                    th:with="type=${@dict.getType('is_deposit')}" required>
                                                <option value=""></option>
                                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                        th:value="${dict.dictValue}" th:field = "${client.isLockOtherFee}"></option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> -->
                        <div class="">
                            <div class="bg-title">其他配置</div>
                            <!-- <div class="mt10">
                                <div class="flex">
                                    <label class="flex_left" style="width: 140px"><span style="">是否启用合同价计费：</span></label>
                                    <div class="radio-box">
                                        <input type="radio" th:value="2" th:checked="${client.enableContractPrice == 2}" name="enableContractPrice">
                                        <label>是</label>
                                    </div>
                                    <div class="radio-box">
                                        <input type="radio" th:value="1" th:checked="${client.enableContractPrice == 1}" name="enableContractPrice">
                                        <label>否</label>
                                    </div>
                                </div>
                            </div> -->
                        </div>
                        <div class="">
                            <div class="">
                                <div class="flex">
                                    <label class="flex_left" style="width: 140px"><span style="">是否设置一般指导价：</span></label>
                                    <div class="flex_right">
                                        <div class="radio-box">
                                            <input type="radio" th:value="1" th:checked="${client.crtGuidePrice == 1}" name="crtGuidePrice">
                                            <label>是</label>
                                        </div>
                                        <div class="radio-box">
                                            <input type="radio" th:value="0" th:checked="${client.crtGuidePrice == 0}" name="crtGuidePrice">
                                            <label>否</label>
                                        </div>
<!--                                        <select name="crtGuidePrice" class="form-control valid">-->
<!--                                            <option value="0" th:selected="${client.crtGuidePrice == 0}">否</option>-->
<!--                                            <option value="1" th:selected="${client.crtGuidePrice == 1}">是</option>-->
<!--                                        </select>-->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt10">
                            <div class="">
                                <div class="flex">
                                    <label class="flex_left" style="width: 140px">指导价浮动率：</label>
                                    <div class="flex_right">
                                        <div class="over">
                                            <div class="fl" style="width: 80%">
                                                <input name="referenceRate" id="referenceRate" th:value="${client.referenceRate == null ? '' : client.referenceRate * 100}" class="form-control"
                                                       type="text" min="0" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" maxlength="5" autocomplete="off">
                                            </div>
                                            <div class="fr ml10">
                                                <a href="javascript:;" data-toggle="tooltip" data-placement="left" title="指导价的浮动比率，在基础指导价的基础上乘以该比率，得到该客户的最终指导价">
                                                    <i class="fa fa-question-circle" style="color: #7f7f7f;font-size: 18px"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="">
                            <div class="">
                                <div class="flex">
                                    <label class="flex_left" style="width: 140px"><span style="">是否设置特殊指导价：</span></label>
                                    <div class="flex_right">
                                        <div class="radio-box">
                                            <input type="radio" th:value="1" th:checked="${client.isSpecialReferencePrice == 1}" name="isSpecialReferencePrice">
                                            <label>是</label>
                                        </div>
                                        <div class="radio-box">
                                            <input type="radio" th:value="0" th:checked="${client.isSpecialReferencePrice == 0}" name="isSpecialReferencePrice">
                                            <label>否</label>
                                        </div>
                                        <!--                                        <select name="crtGuidePrice" class="form-control valid">-->
                                        <!--                                            <option value="0" th:selected="${client.crtGuidePrice == 0}">否</option>-->
                                        <!--                                            <option value="1" th:selected="${client.crtGuidePrice == 1}">是</option>-->
                                        <!--                                        </select>-->
                                        <div class="fr ml10" style="display: flex; align-items: center;">
                                            <a href="javascript:;" data-toggle="tooltip" data-placement="left" title="如果设置特殊指导价，则一般指导价将无效">
                                                <i class="fa fa-question-circle" style="color: #7f7f7f;font-size: 18px"></i>
                                            </a>
                                        </div>

                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="">
                            <div class="">
                                <div class="flex">
                                    <label class="flex_left" style="width: 140px"><span style="">应收回单结算：</span></label>
                                    <div class="flex_right">
                                        <div class="radio-box">
                                            <input type="radio" th:value="1" th:checked="${client.settlementCheck == 1}" name="settlementCheck">
                                            <label>是</label>
                                        </div>
                                        <div class="radio-box">
                                            <input type="radio" th:value="0" th:checked="${client.settlementCheck == 0}" name="settlementCheck">
                                            <label>否</label>
                                        </div>
<!--                                        <div class="fr ml10" style="display: flex; align-items: center;">-->
<!--                                            <a href="javascript:;" data-toggle="tooltip" data-placement="left" title="如果设置特殊指导价，则一般指导价将无效">-->
<!--                                                <i class="fa fa-question-circle" style="color: #7f7f7f;font-size: 18px"></i>-->
<!--                                            </a>-->
<!--                                        </div>-->

                                    </div>

                                </div>
                            </div>
                        </div>

                        <div class="">
                            <div class="">
                                <div class="flex">
                                    <label class="flex_left" style="width: 140px"><span style="">是否需要回单：</span></label>
                                    <div class="flex_right">
                                        <div class="radio-box">
                                            <input type="radio" th:value="1" th:checked="${client.isNeedReceipt == 1}" name="isNeedReceipt">
                                            <label>是</label>
                                        </div>
                                        <div class="radio-box">
                                            <input type="radio" th:value="0" th:checked="${client.isNeedReceipt == 0}" name="isNeedReceipt">
                                            <label>否</label>
                                        </div>
<!--                                        <select name="isNeedReceipt" class="form-control valid">-->
<!--                                            <option value="1" th:selected="${client.isNeedReceipt == 1}">是</option>-->
<!--                                            <option value="0" th:selected="${client.isNeedReceipt == 0}">否</option>-->
<!--                                        </select>-->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt10">
                            <div class="">
                                <div class="flex">
                                    <label class="flex_left" style="width: 140px"><span style="">能否议价下单：</span></label>
                                    <div class="flex_right">
                                        <select class="form-control custom-select" id="contractPriceType" name="contractPriceType">
                                            <option value="0" th:selected="${client.contractPriceType == 0}">仅合同价</option>
                                            <option value="1" th:selected="${client.contractPriceType == 1}">可议价</option>
                                        </select>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt10">
                            <div class="">
                                <div class="flex">
                                    <label class="flex_left" style="width: 140px"><span style="">允许无合同下单：</span></label>
                                    <div class="flex_right">
                                        <select class="form-control custom-select" id="contractNeedType" name="contractNeedType">
                                            <option value="0" th:selected="${client.contractNeedType == 0}">否</option>
                                            <option value="1" th:selected="${client.contractNeedType == 1}">是</option>
                                        </select>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt10">
                            <div class="">
                                <div class="flex">
                                    <label class="flex_left" style="width: 140px"><span style="">是否议价：</span></label>
                                    <div class="flex_right">
                                        <div class="radio-box">
                                            <input type="radio" th:value="1" th:checked="${client.ifBargain == 1}" name="ifBargain">
                                            <label>是</label>
                                        </div>
                                        <div class="radio-box">
                                            <input type="radio" th:value="0" th:checked="${client.ifBargain == 0}" name="ifBargain">
                                            <label>否</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="">
                            <div class="">
                                <div class="flex">
                                    <label class="flex_left" style="width: 140px"><span style="">客户状态：</span></label>
                                    <div class="flex_right">
                                        <label class="toggle-switch switch-solid">
                                            <input type="checkbox" id="isEnabled" th:checked="${client.isEnabled == 0}">
                                            <span></span>
                                        </label>
                                        <label  th:if="${client.disabledTime != null and client.disabledTime != ''}">
                                            禁用时间：[[${#dates.format(client.disabledTime, 'yyyy-MM-dd HH:mm')}]]
                                        </label>

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="">
                            <div class="">
                                <div class="flex">
                                    <label class="flex_left" style="width: 140px"><span style="">是否特殊流转：</span></label>
                                    <div class="flex_right">
                                        <div class="radio-box">
                                            <input type="radio" th:value="1" th:checked="${client.isSpecialTransfer == 1}" name="isSpecialTransfer">
                                            <label>是</label>
                                        </div>
                                        <div class="radio-box">
                                            <input type="radio" th:value="0" th:checked="${client.isSpecialTransfer == 0}" name="isSpecialTransfer">
                                            <label>否</label>
                                        </div>
                                        <!--                                        <select name="crtGuidePrice" class="form-control valid">-->
                                        <!--                                            <option value="0" th:selected="${client.crtGuidePrice == 0}">否</option>-->
                                        <!--                                            <option value="1" th:selected="${client.crtGuidePrice == 1}">是</option>-->
                                        <!--                                        </select>-->
                                        <div class="fr ml10" style="display: flex; align-items: center;">
                                            <a href="javascript:;" data-toggle="tooltip" data-placement="left" title="“是”流转赵羽桐审核用费用">
                                                <i class="fa fa-question-circle" style="color: #7f7f7f;font-size: 18px"></i>
                                            </a>
                                        </div>

                                    </div>

                                </div>
                            </div>
                        </div>

                        <div class="">
                            <div class="">
                                <div class="flex">
                                    <label class="flex_left" style="width: 140px"><span style="">下单联系人校验：</span></label>
                                    <div class="flex_right">
                                        <div class="radio-box">
                                            <input type="radio" th:value="1" th:checked="${client.invContactCheck == 1}" name="invContactCheck">
                                            <label>是</label>
                                        </div>
                                        <div class="radio-box">
                                            <input type="radio" th:value="0" th:checked="${client.invContactCheck == 0}" name="invContactCheck">
                                            <label>否</label>
                                        </div>
                                        <div class="fr ml10" style="display: flex; align-items: center;">
                                            <a href="javascript:;" data-toggle="tooltip" data-placement="left" title="发货单确认时是否校验下单联系人及电话">
                                                <i class="fa fa-question-circle" style="color: #7f7f7f;font-size: 18px"></i>
                                            </a>
                                        </div>

                                    </div>

                                </div>
                            </div>
                        </div>

                    </div>


                </div>
            </div>
        </div>
    </form>
</div>



<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "client";


    //提交表单
    function submitHandler() {
        if (!$('#form-client-add').validate({
            errorPlacement: function(error,element){
                if (element.parent().hasClass("input-group")){
                    element.parent().after(error);
                } else if (element.parent().hasClass("bootstrap-select")) {
                    element.parent().after(error);
                } else {
                    element.after(error);
                }
            }
        }).form()) {
            return;
        }
        var specialDate = $("#specialDate").val();
        if(specialDate != '' && specialDate <= 0) {
            //小tips
            layer.tips('请输入大于0的数值', '#specialDate', {
                tips: [1, '#ed5565'],
                time: 4000
            });
            return
        }
        if(specialDate != '' && specialDate > 31) {
            //小tips
            layer.tips('请输入不大于31的数值', '#specialDate', {
                tips: [1, '#ed5565'],
                time: 4000
            });
            return
        }
        //if ($.validate.form()) {
            var data = $("#form-client-add").serializeArray();
            var isEnabled = $("input[id='isEnabled']").is(':checked') == true ? 0 : 1;
            data.push({"name": "isEnabled", "value": isEnabled});
            $.operate.save(prefix + "/saveAdjustSpecialDate", data);
        //}
    }
</script>
</body>
</html>