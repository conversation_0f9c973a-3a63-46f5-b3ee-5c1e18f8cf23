<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <th:block th:include="include :: header('新添保险')" />
    <th:block th:include="include :: bootstrap-select-css" />
    <style>
        .form-content {
            padding: 10px;
        }
        .form-row {
            margin-bottom: 8px;
        }
        .form-row {
            display: flex;
            margin-bottom: 15px;
            gap: 15px;
        }
        .form-col {
            flex: 1;
        }
        .form-item {
            margin-bottom: 0;
            position: relative;
        }
        .form-item .item-label {
            display: block;
            text-align: left;
            padding-right: 0;
            font-size: 14px;
            white-space: nowrap;
            color: #2d2d2d;
            font-weight: 500;
            margin-bottom: 5px;
        }
        .form-item .item-content {
            display: block;
            width: 100%;
            position: relative;
        }
        .form-control {
            height: 36px;
            padding: 4px 8px;
            font-size: 13px;
            width: 100%;
            box-sizing: border-box;
        }
        textarea.form-control {
            height: auto;
        }
        .card-header {
            font-size: 16px;
            font-weight: bold;
            color: #2d2d2d;
            margin-bottom: 15px;
            margin-top: 20px;
            border-bottom: 1px solid #0071ce;
            padding-bottom: 8px;
        }
        .required:before {
            content: "*";
            color: #f56c6c; /* 调整必填星号的颜色为更柔和的红色 */
            margin-right: 4px;
            font-weight: bold;
        }
        /* 自定义验证消息样式 */
        .error-tip {
            position: absolute;
            right: -115px; /* 消息显示在输入框右侧 */
            top: 0;
            color: red;
            font-size: 12px;
            white-space: nowrap;
            background-color: #fff;
            padding: 2px 5px;
            border-radius: 2px;
            z-index: 100;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: none;
        }
        /* 输入框错误状态 */
        .form-control.error {
            border-color: #f56c6c;  /* 使用较柔和的红色 */
            box-shadow: 0 0 0 1px rgba(245, 108, 108, 0.2);  /* 添加微弱的红色阴影效果 */
            transition: border-color 0.2s ease-in-out;
        }
        .Validform_error, input.error, select.error {
            background-color: #fff;
        }
                /* 为layui下拉框添加错误样式 */
        .error-border .layui-select-title .layui-input {
            border-color: #f56c6c;  /* 使用较柔和的红色 */
            box-shadow: 0 0 0 1px rgba(245, 108, 108, 0.2);  /* 添加微弱的红色阴影效果 */
            transition: border-color 0.2s ease-in-out;
        }

        /* 移动设备适配 */
        @media (max-width: 768px) {
            .error-tip {
                right: 0;
                top: -20px;
                box-shadow: none;
            }
        }
    </style>
</head>

<body>
<div class="form-content">
    <form id="form-insurance-add" class="layui-form" novalidate="novalidate">
        <!-- 基本信息 -->
        <input type="hidden" name="id" th:value="${insurancePolicy.id}">
        <div class="card-header">基本信息</div>
        <div class="form-row">
            <div class="form-col">
                <div class="form-item">
                    <label class="item-label required">保险所属类别</label>
                    <div class="item-content">
                        <select name="ownerType" id="ownerType" class="form-control" required>
                            <option value=""></option>
                            <option th:each="dict : ${@dict.getType('insurance_owner_type')}" th:text="${dict.dictLabel}"
                                    th:value="${dict.dictValue}" th:selected="${dict.dictValue==insurancePolicy.ownerType?.toString()}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="form-col">
                <div class="form-item">
                    <label class="item-label required">保单号</label>
                    <div class="item-content">
                        <input id="policyNo" name="policyNo" class="form-control"
                               placeholder="请输入保单号" type="text" required th:value="${insurancePolicy.policyNo}">
                    </div>
                </div>
            </div>
            <div class="form-col">
                <div class="form-item">
                    <label class="item-label required">保险主体</label>
                    <div class="item-content">
                        <select name="insuredParty" id="insuredParty" class="form-control" required>
                            <option value=""></option>
                            <option th:each="dict : ${@dict.getType('bala_corp')}" th:text="${dict.dictLabel}"
                                    th:value="${dict.dictValue}" th:selected="${dict.dictValue==insurancePolicy.insuredParty}"></option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-row">
            <div class="form-col">
                <div class="form-item">
                    <label class="item-label required">保险公司</label>
                    <div class="item-content">
                        <input id="insuranceCompany" name="insuranceCompany" class="form-control"
                               placeholder="请输入保险公司名称" type="text" required th:value="${insurancePolicy.insuranceCompany}">
                    </div>
                </div>
            </div>
            <div class="form-col">
                <!-- 占位 -->
            </div>
            <div class="form-col">
                <!-- 占位 -->
            </div>
        </div>

        <!-- 金额与期限 -->
        <div class="card-header">金额与期限</div>
        <div class="form-row">
            <div class="form-col">
                <div class="form-item">
                    <label class="item-label">保险金额</label>
                    <div class="item-content">
                        <input id="insuredAmount" name="insuredAmount" class="form-control"
                               placeholder="请输入保险金额" type="number" step="0.01" th:value="${insurancePolicy.insuredAmount}">
                    </div>
                </div>
            </div>
            <div class="form-col">
                <div class="form-item">
                    <label class="item-label ">保费金额</label>
                    <div class="item-content">
                        <input id="premiumAmount" name="premiumAmount" class="form-control"
                               placeholder="请输入保费金额" type="number" step="0.01" th:value="${insurancePolicy.premiumAmount}">
                    </div>
                </div>
            </div>
            <div class="form-col">
                <div class="form-item">
                    <label class="item-label ">开始日期</label>
                    <div class="item-content">
                        <input id="policyStartDate" name="policyStartDate" class="form-control"
                               placeholder="开始日期" type="text" th:value="${#dates.format(insurancePolicy.policyStartDate, 'yyyy-MM-dd')}">
                    </div>
                </div>
            </div>
        </div>
        <div class="form-row">
            <div class="form-col">
                <div class="form-item">
                    <label class="item-label ">结束日期</label>
                    <div class="item-content">
                        <input id="policyEndDate" name="policyEndDate" class="form-control"
                               placeholder="结束日期" type="text" th:value="${#dates.format(insurancePolicy.policyEndDate, 'yyyy-MM-dd')}">
                    </div>
                </div>
            </div>
            <div class="form-col">
                <!-- 占位 -->
            </div>
            <div class="form-col">
                <!-- 占位 -->
            </div>
        </div>
    </form>
</div>

<th:block th:include="include :: footer" />
<script th:inline="javascript">
    $(function () {
        // 使用layui日期选择器
        layui.use(['form', 'laydate'], function(){
            var form = layui.form;
            var laydate = layui.laydate;

            laydate.render({
                elem: '#policyStartDate',
                type: 'date',
                done: function(value, date) {
                    if (value !== '') {
                        $("#policyStartDate").removeClass('error');
                    }
                }
            });

            laydate.render({
                elem: '#policyEndDate',
                type: 'date',
                done: function(value, date) {
                    if (value !== '') {
                        $("#policyEndDate").removeClass('error');
                    }
                }
            });

            form.on('select', function(data){
                // 如果选择了"请选择"（空值），添加错误样式并重置显示文本
                if(!data.value) {
                    $("#" + data.elem.id).addClass('error');
                    $(data.elem).next('.layui-form-select').addClass('error-border');
                    // 重置显示文本为"请选择"
                    $(data.elem).next('.layui-form-select').find('.layui-select-title input').attr('placeholder', '请选择');
                } else {
                    // 如果有值，移除错误样式
                    $("#" + data.elem.id).removeClass('error');
                    $(data.elem).next('.layui-form-select').removeClass('error-border');
                }

                // 当ownerType等于0时，保单号、保险主体、保险公司、保险金额需要必填
                if (data.elem.id === 'ownerType') {
                    toggleRequiredFields(data.value);
                }
            });

            // 验证下拉框选择
            form.verify({
                selectRequired: function(value, item) {
                    if (!value) {
                        return '请选择' + $(item).prev('.item-label').text().replace('*', '');
                    }
                }
            });

            form.render();
        });

        // 添加输入事件监听器，在输入时隐藏错误提示
        $("input[required]").on('input', function() {
            if($(this).val().trim() !== '') {
                $(this).removeClass('error');
            }
        });


        // 初始化时根据ownerType的值设置必填字段
        var initialOwnerType = $('#ownerType').val();
        toggleRequiredFields(initialOwnerType);

    });

    /**
     * 表单验证 - 使用自定义提示
     */
    function validateForm() {
        var isValid = true;

        // 隐藏所有错误提示
        $(".form-control").removeClass('error');
        $(".layui-form-select").removeClass('error-border');

        // 验证必填字段
        $("input[required]").each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('error');
                if (isValid) { // 只滚动到第一个错误
                    // $(this).focus();
                }
                isValid = false;
            }
        });

        // 验证必填字段 - select元素
        $("select[required]").each(function() {
            if (!$(this).val()) {
                $(this).addClass('error');
                // 同时为layui生成的下拉框添加错误样式
                $(this).next('.layui-form-select').addClass('error-border');
                if (isValid) { // 只滚动到第一个错误
                    // $(this).focus();
                }
                isValid = false;
            }
        });

        if (!isValid) {
            // 使用layer的提示，不影响布局
            layer.msg('请填写必填项', {icon: 2});
        }

        return isValid;
    }

    /**
     * 根据ownerType的值切换字段的必填状态
     * @param {string} ownerTypeValue - ownerType的值
     */
    function toggleRequiredFields(ownerTypeValue) {
        // 定义需要根据ownerType切换必填状态的字段
        var conditionalRequiredFields = [
            {field: '#policyNo', label: '保单号'},
            {field: '#insuredParty', label: '保险主体'},
            {field: '#insuranceCompany', label: '保险公司'},
            {field: '#insuredAmount', label: '保险金额'}
        ];

        if (ownerTypeValue === '0') {
            // 当ownerType等于0时，设置这些字段为必填
            conditionalRequiredFields.forEach(function(item) {
                $(item.field).attr('required', true);
                // 添加视觉上的必填标识
                var labelElement = $(item.field).closest('.form-item').find('.item-label');
                if (!labelElement.hasClass('required')) {
                    labelElement.addClass('required');
                }
            });
        } else {
            // 当ownerType不等于0时，移除这些字段的必填属性
            conditionalRequiredFields.forEach(function(item) {
                $(item.field).removeAttr('required');
                // 移除视觉上的必填标识
                var labelElement = $(item.field).closest('.form-item').find('.item-label');
                if (labelElement.hasClass('required')) {
                    labelElement.removeClass('required');
                }
            });
        }
    }


    /**
     * 提交处理函数
     */
    function submitHandler(index, layero) {
        if (validateForm()) {
            var data = $("#form-insurance-add").serializeArray();
            $.operate.save(ctx + "insurancePolicy/edit", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    layer.close(index);
                    $.modal.msgSuccess(result.msg);
                    // 修改刷新方式，使用父窗口的表格刷新方法
                    if (window.parent.$.table !== undefined) {
                        window.parent.$.table.refresh();
                    }
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        }
    }
</script>
</body>
</html>
</body>
</html>