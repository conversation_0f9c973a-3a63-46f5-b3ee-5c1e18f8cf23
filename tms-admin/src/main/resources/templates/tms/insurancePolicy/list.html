<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('指导价')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/extensions/bootstrap3-editable/css/bootstrap-editable.css}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.css}" rel="stylesheet"/>
    <!--    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/extensions/sticky-header/bootstrap-table-sticky-header.min.css}" rel="stylesheet"/>-->

</head>
<style>
    .custom-cell-background-red {
        background-color: rgb(255 0 0 / 43%) !important; /* 自定义的背景颜色 */
    }
    .custom-cell-background-red a {
        color: #FFFFFF;
    }

    .custom-cell-background-green {
        background-color: #8DBF8B !important; /* 自定义的背景颜色 */
    }
    .custom-cell-background-green a {
        color: #FFFFFF;
    }

    /* 表格滚动条 */
    .bootstrap-table {
        overflow-x: auto;
    }
    .bootstrap-table .fixed-table-container,
    .bootstrap-table .fixed-table-body {
        overflow: visible !important; /* 强制覆盖内部滚动 */
    }
    /*.bootstrap-table .table {*/
    /*    min-width: 1600px; !* 设置表格最小宽度，可以根据实际需求调整 *!*/
    /*    width: 100%;*/
    /*}*/
    /* 彻底修复表头文字重叠问题 */
    .bootstrap-table .fixed-table-container .table thead th {
        position: relative;
        text-shadow: none !important;
        font-weight: 500;
        color: #333;
        background-color: #f5f7fa;
    }

    .bootstrap-table .fixed-table-container .table thead th .th-inner {
        padding: 8px;
        line-height: 1.5;
        vertical-align: middle;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        box-sizing: border-box;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
    }

    /* 防止表头内容重叠 */
    .bootstrap-table .fixed-table-container .table thead th .sortable {
        background-image: none;
        position: relative;
    }

    /* 确保表头只有一层文字 */
    .bootstrap-table .fixed-table-container .table thead th:before,
    .bootstrap-table .fixed-table-container .table thead th:after {
        content: none !important;
    }

    /* 添加工具栏与表格之间的间距 */
    #toolbar {
        margin-bottom: 10px;
    }

    /* 美化新添按钮 */
    #toolbar .btn-info {
        margin-right: 5px;
        padding: 5px 15px;
        transition: all 0.3s;
    }

    #toolbar .btn-info:hover {
        background-color: #17a2b8;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    .lf{
        margin-left: 10px;
        background-color: #ffffff;
        border-radius: 3px;
        cursor:pointer;
    }

    .label-success{
        color: #1ab394;
        background-color: transparent;
        border: 1px solid #1ab394;
    }
    .label-primary{
        color: #1c84c6;
        background-color: transparent;
        border: 1px solid #1c84c6;
    }
    .label-warning{
        color: #f8ac59;
        background-color: transparent;
        border: 1px solid #f8ac59;
    }

    .customer-stats {
        font-size: 16px;
    }

    .customer-stats span {
        margin-left: 10px;
        font-weight: 550;
    }

    .customer-stats span:nth-child(2) {
        margin-left: 0px;
        font-size: 16px;
        font-weight: 450;
        color: #FF6C00;
    }
    .customer-stats span:nth-child(3) {
        margin-left: 0px;
        font-size: 16px;
        font-weight: 450;
    }

    .customer-stats span:nth-child(4) {
        margin-left: 0px;
        font-size: 16px;
        font-weight: 450;
        color: #0d62bb;
    }

    .table-light {
        background-color: #f8f9fa; /* 设置背景色为浅灰色 */
    }

    .progress-wrapper {
        position: relative;
        width: 100%;
        height: 20px;
        background-color: #f5f5f5;
        border-radius: 4px;
        overflow: hidden;
    }

    .progress {
        position: relative;
        width: 100%;
        height: 100%;
    }

    .progress-bar {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        background-color: #4caf50;
        transition: width 0.3s ease;
    }

    .progress-text {
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        line-height: 20px;
        padding: 0 10px;
        font-size: 12px;
    }

    .tagged-div {
        position: relative; /* 设置为相对定位，使内嵌元素定位相对于此元素 */
    }

    .tag {
        position: absolute; /* 设置为绝对定位，使其相对于包含它的 .tagged-div 定位 */
        top: -14px; /* 距离顶部为0 */
        left: -5px; /* 距离左侧为0 */
        background-color: #d2e7f9; /* 标记的背景颜色 */
        padding: 1px 3px; /* 内边距 */
        border-radius: 6px; /* 边框圆角 */
        font-size: x-small; /* 字体大小 */
    }

    .sticky-header-container {
        pointer-events: none;
    }
    #buttons-toolbar-container {
        clear: both;
        display: block;
        width: 100%;
    }

    #toolbar {
        margin-bottom: 10px;
        display: inline-block;
    }

    .select-table {
        clear: both;
    }

    /* 确保表格在工具栏下方正确显示 */
    .bootstrap-table {
        clear: both;
        margin-top: 10px;
    }

        /* 表格页脚样式 */
    .bootstrap-table .table tfoot {
        font-weight: bold;
        background-color: #f5f7fa;
    }
    
    .bootstrap-table .table tfoot td {
        padding: 8px;
        border-top: 2px solid #ddd;
    }
    
    /* 确保表格在工具栏下方正确显示 */
    .bootstrap-table {
        clear: both;
        margin-top: 10px;
    }

</style>
<body class="gray-bg">
<div class="col-sm-12 search-collapse">
    <form id="role-form" class="form-horizontal" onkeydown="if(event.keyCode==13){searchPre();return false;}">
        <div class="row no-gutter">
            <div class="col-md-2 col-sm-2">
                <div class="form-group flex">
                    <div class="col-sm-12">
                        <input name="policyNo" id="policyNo" placeholder="保单号" class="form-control" type="text" maxlength="20" autocomplete="off">                    </div>
                </div>
            </div>
            <div class="col-md-2 col-sm-2">
                <div class="form-group flex">
                    <div class="col-sm-12">
                        <input name="insuranceCompany" id="insuranceCompany" placeholder="保险公司名称" class="form-control" type="text" maxlength="20" autocomplete="off">                    </div>
                </div>
            </div>
            <div class="col-md-2 col-sm-2">
                <div class="form-group flex">
                    <div class="col-sm-12">
                        <select name="insuredParty" id="insuredParty" class="form-control valid" th:with="type=${@dict.getType('bala_corp')}" >
                            <option value="">---请选择保险主体---</option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                    th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-md-2 col-sm-2">
                <div class="form-group flex">
                    <div class="col-sm-12">
                        <select name="ownerType" id="ownerType" class="form-control valid">
                            <option value="">---请选择所属类别---</option>
                            <option th:each="dict : ${@dict.getType('insurance_owner_type')}" th:text="${dict.dictLabel}"
                                    th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-3 col-sm-3">
                <div class="form-group" style="text-align: left;">
                    <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                </div>
            </div>

        </div>
    </form>
</div>

<div class="col-sm-12 select-table  ">
    <div class="btn-group-sm" id="toolbar" role="group">
        <a class="btn btn-primary" shiro:hasAnyPermissions="tms:insuranceRecord:list"
           onclick="add()">
            <i class="fa fa-plus"></i> 新添
        </a>

        <a class="btn btn-danger multiple disabled" shiro:hasAnyPermissions="tms:insuranceRecord:list"
           onclick="deleteRecord()">
            <i class="fa fa-remove"></i> 删除
        </a>

    </div>

    <div id="buttons-toolbar-container" style="margin-bottom: 10px;">
        <span id="buttons-toolbar"></span>
    </div>

    <table id="bootstrap-table"
           data-buttons-toolbar="#buttons-toolbar"
           class="table table-striped table-responsive table-bordered table-hover" >
    </table>
</div>


<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/export/tableExport.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/bootstrap3-editable/js/bootstrap-editable.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/editable/bootstrap-table-editable.min.js}"></script>
<!--<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/sticky-header/bootstrap-table-sticky-header.min.js}"></script>-->
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/export/bootstrap-table-export.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/locale/bootstrap-table-zh-CN.min.js}"></script>


<script th:inline="javascript">
    var bala_corp = [[${@dict.getType('bala_corp')}]];
    var insurance_owner_type = [[${@dict.getType('insurance_owner_type')}]];

    $(function () {
        let options = initOptions();
        $.table.init(options);
    });

    function initOptions() {
        return {
            url: ctx + "insurancePolicy/list",
            uniqueId: "id",
            showToggle:false,
            showColumns:false,
            showSearch:false,
            pagination:false,
            // showRefresh:false,
            modalName: "保险",
            height: 560,
            clickToSelect: true,
            showFooter: true,  // 启用表格页脚，用于显示汇总
            // stickyHeader: true,  // 启用固定表头功能
            // stickyHeaderOffsetY: 0,  // 可选，调整固定表头距离顶部的距离
            columns: [
                [
                    {
                        checkbox: true,
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        title: '操作',
                        align: 'center',
                        valign: 'middle',
                        switchable:false,
                        formatter: function(value, row, index) {
                            var actions = [];
                            if ([[${@permission.hasPermi('tms:insurancePolicy:list')}]] != "hidden") {
                                actions.push(`<a class="btn btn-xs" href="javascript:void(0)" onclick="edit('${row.id}')" title="修改">
                                                    <i class="fa fa-edit" style="font-size: 15px;"></i></a>`);
                            }
                            if ([[${@permission.hasPermi('tms:insuranceRecord:list')}]] != "hidden") {
                                actions.push(`<a class="btn btn-xs" href="javascript:void(0)" onclick="claims('${row.id}','${row.policyNo}')" title="保险理赔">
                                                    <i class="fa fa-envelope-open-o" style="font-size: 15px;"></i></a>`);
                            }
                            if ([[${@permission.hasPermi('tms:insuranceRecord:list')}]] != "hidden") {
                                actions.push(`<a class="btn btn-xs" href="javascript:void(0)" onclick="insuranceRecordExport('${row.id}')" title="出险记录导出">
                                                    <i class="fa fa-download" style="font-size: 15px;"></i></a>`);
                            }
                            return actions.join('');
                        }
                    },
                    {
                        title: '所属类别',
                        align: 'center',
                        valign: 'middle',
                        field: 'ownerType',
                        formatter: function status(value, row, index) {
                            return $.table.selectDictLabel(insurance_owner_type, value);
                        }
                    },
                    {
                        title: '保险主体',
                        align: 'center',
                        valign: 'middle',
                        field: 'insuredParty',
                        formatter: function status(value, row, index) {
                            return $.table.selectDictLabel(bala_corp, value);
                        }
                    },
                    {
                        title: '保单号',
                        valign: 'middle',
                        align: 'center',
                        field : 'policyNo',
                        formatter: function(value, row, index) {
                            return $.table.tooltip(value);
                        }
                    },
                    {
                        title: '保险公司名称',
                        align: 'center',
                        valign: 'middle',
                        field: 'insuranceCompany',
                    },
                    {
                        title: '保险开始日期',
                        align: 'center',
                        valign: 'middle',
                        field : 'policyStartDate',
                    },
                    {
                        title: '保险结束日期',
                        align: 'center',
                        valign: 'middle',
                        field : 'policyEndDate',
                    },

                    {
                        title: '保险金额',
                        align: 'right',
                        field : 'insuredAmount',
                        footerFormatter: function (data) {
                            return sumFormatter(data, 'insuredAmount');
                        }

                    },
                    {
                        title: '保费金额',
                        align: 'right',
                        field : 'premiumAmount',
                        footerFormatter: function (data) {
                            return sumFormatter(data, 'premiumAmount');
                        }

                    },
                    {
                        title: '货损',
                        align: 'right',
                        field : 'accidentAmount',
                        footerFormatter: function (data) {
                            return sumFormatter(data, 'accidentAmount');
                        }

                    },
                    {
                        title: '其他费',
                        align: 'right',
                        field : 'otherFees',
                        footerFormatter: function (data) {
                            return sumFormatter(data, 'otherFees');
                        }

                    },
                    {
                        title: '赔付及残值',
                        align: 'right',
                        field : 'insuranceCompensationAmount',
                        footerFormatter: function (data) {
                            return sumFormatter(data, 'insuranceCompensationAmount');
                        }

                    },
                    {
                        title: '公司损益',
                        align: 'right',
                        field : 'companyProfitLoss',
                        footerFormatter: function (data) {
                            return sumFormatter(data, 'companyProfitLoss');
                        }

                    },
                ]
            ],
        };
    }


        // 添加金额汇总计算函数
    function sumFormatter(data, field) {
        let total = 0;
        $.each(data, function (i, row) {
            // 确保值为数字
            let value = parseFloat(row[field]) || 0;
            total += value;
        });
        // 格式化为两位小数
        return total.toFixed(2);
    }

    function add() {
        layer.open({
            type: 2,
            area: ['80%', '70%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "新添保险",
            content: ctx + "insurancePolicy/add",
            btn: ['确认', '关闭'],
            // shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function (index) {
                return true;
            }
        });
    }

    function edit(id) {
        layer.open({
            type: 2,
            area: ['80%', '70%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "修改保险记录",
            content: ctx + `insurancePolicy/edit/${id}`,
            btn: ['确认', '关闭'],
            // shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function (index) {
                return true;
            }
        });
    }

    function claims(id,policyNo) {
        let displayPolicyNo = policyNo.length > 10 ? policyNo.substring(0, 10) + "..." : policyNo;

        $.modal.openTab(`[${displayPolicyNo}]保险理赔`, ctx + `insuranceRecord?insurancePolicyId=${id}`);

        // layer.open({
        //     type: 2,
        //     area: ['98%', '98%'],
        //     fix: false,
        //     maxmin: true,
        //     shade: 0.3,
        //     title: "保险理赔",
        //     content: ctx + `insuranceRecord`,
        //     btn: ['关闭'],
        //     // shadeClose: true,            // 弹层外区域关闭
        //     cancel: function (index) {
        //         return true;
        //     }
        // });
    }

    function insuranceRecordExport(id) {
        var data = [];
        data.push({name: "insurancePolicyId", value: id});

        $.modal.confirm("确定导出所有发货单数据吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(ctx + "insuranceRecord/export", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    function deleteRecord() {
        var idArr = $.table.selectColumns("id");
        if (idArr.length == 0) {
            $.modal.msgWarning("请至少选择一条数据");
            return;
        }

        $.modal.confirm("确认要删除选中的" + idArr.length + "条数据吗?", function() {
            var url = ctx + "insurancePolicy/remove";
            var data = { "ids": idArr.join(",") };
            $.operate.submit(url, "post", "json", data);
        });
    }

    function searchPre() {
        $.table.search('role-form');
    }
    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }
</script>

</body>
</html>