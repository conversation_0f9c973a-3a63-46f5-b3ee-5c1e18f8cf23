<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应收汇总列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">发货单号：</label>
                            <div class="col-sm-7">
                                <input name="invoiceVbillno" id="invoiceVbillno" class="form-control" type="text"
                                       placeholder="请输入发货单号"
                                       maxlength="25" required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">应收单号：</label>
                            <div class="col-sm-7">
                                <input name="vbillno" id="vbillno" placeholder="请输入应收单号" class="form-control valid"
                                       type="text"  maxlength="25">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">应收单状态：</label>
                            <div class="col-sm-8">
                                <select name="status" id="status" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="应收单状态" multiple>
                                    <option th:each="dict : ${receiveDetailStatusEnum}" th:text="${dict.context}"
                                            th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">结算客户：</label>
                            <div class="col-sm-8">
                                <input name="balaName" id="balaName" placeholder="请输入结算客户"
                                       class="form-control valid" type="text" maxlength="25">
                                <input name="customerId" id="customerId" type="hidden" th:value="${customerId}">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">结算方式：</label>
                            <div class="col-sm-7">
                                <select name="balatype" id="balatype" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="结算方式" multiple
                                        th:with="type=${@dict.getType('bala_type')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">要求提货日期：</label>
                            <div class="col-sm-7">
                                <input class="time-input form-control"
                                       id="reqDeliDate" name="reqDeliDate">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-2">创建时间：</label>
                            <div class="col-sm-7">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  name="startDate">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="endtDate"  name="endtDate">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">创建人：</label>
                            <div class="col-sm-7">
                                <input name="regUserName" id="regUserName" class="form-control"
                                       placeholder="请输入创建人" maxlength="25">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6"></div>
                    <div class="col-md-3 col-sm-6"></div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary single disabled" onclick="batchRece()" shiro:hasPermission="finance:receive:batchRece">
                <i class="fa fa-file-text-o"></i> 分批收款
            </a>
            <a class="btn btn-primary single disabled" onclick="receRecord()" shiro:hasPermission="finance:receive:receRecord">
                <i class="fa fa-calculator"></i> 收款记录
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var balaCorp = [[${@dict.getType('bala_corp')}]];
    var balatype = [[${@dict.getType('bala_type')}]];
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var prefix = ctx + "custReceiveView/receiveView";

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add?customerId="+$("#customerId").val(),
            showToggle: false,
            showColumns: true,
            modalName: "应收明细",
            fixedColumns: true,
            rememberSelected: false,
            fixedNumber: 2,
            columns: [{
                checkbox: true
                },
                {
                    title: '应收单号',
                    field: 'vbillno',
                    align: 'left'
                },
                {
                    title: '应收单据状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value) {
                            case 0:
                                return '<span>新建</label>';
                            case 1:
                                return '<span>已确认</label>';
                            case 2:
                                return '<span>已对账</label>';
                            case 3:
                                return '<span>部分核销 </label>';
                            case 4:
                                return '<span>已核销 </label>';
                            case 5:
                                return '<span>关闭 </label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '发货单号',
                    align: 'left',
                    field: 'invoiceVbillno'
                },
                {
                    title: '是否符合无车承运人',
                    align: 'left',
                    field: 'isNtocc',
                    formatter: function status(value, row, index) {
                        if (value === 0) {
                            return '否';
                        }
                        return '是';
                    }
                },
                {
                    title: '客户名称',
                    align: 'left',
                    field: 'custName'

                },
                {
                    title: '客户订单号',
                    align: 'left',
                    field: 'custOrderno'

                },

                {
                    title: '费用类型',
                    field: 'freeType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value - 0) {
                            case 0:
                                return '<span>运费</label>';
                            case 1:
                                return '<span>在途费用</label>';
                            case 2:
                                return '<span>调整费用</label>';
                            default:
                                break;
                        }
                    }

                },
                {
                    title: '收款类型',
                    field: 'costTypeOnWay',
                    align: 'left',
                    formatter: function status(value, row, index) {

                        if (row.freeType === '0'){
                            return $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                        }else if (row.freeType === '2')  {
                            return '调整费'
                        }
                        return $.table.selectDictLabel(costTypeOnWay, value);
                    }

                },
                {
                    title: '计费件数',
                    field: 'numCount'
                },
                {
                    title: '计费重量',
                    align: 'left',
                    field: 'feeWeightCount'
                },
                {
                    title: '计费体积',
                    align: 'left',
                    field: 'volumeCount'
                },
                {
                    title: '总金额',
                    align: 'right',
                    field: 'transFeeCount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '已收金额',
                    align: 'right',
                    field: 'gotAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未收金额',
                    align: 'right',
                    field: 'ungotAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '要求提货日期',
                    field: 'reqDeliDate',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(value == "" || value == null || value == 'undefined'){
                            return "";
                        }
                        return value.substring(0,10);
                    }

                },

                {
                    title: '要求到货日期',
                    field: 'reqArriDate',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(value == "" || value == null || value == 'undefined'){
                            return "";
                        }
                        return value.substring(0,10);
                    }

                },

                {
                    title: '结算客户',
                    align: 'left',
                    field: 'balaName'
                },
                {
                    title: '创建人',
                    align: 'left',
                    field: 'regUserName'
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field: 'regDate'
                },
                {
                    title: '是否为调整单',
                    align: 'left',
                    field: 'isAdjust',
                    formatter: function (value, row, index) {
                        if (value === 0) {
                            return '否';
                        }
                        return '是';
                    }
                },
                {
                    title: '调整原因',
                    align: 'left',
                    field: 'adjustMemo'
                },
                {
                    title: '备注',
                    align: 'left',
                    field: 'memo',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '结算方式',
                    align: 'left',
                    field: 'balatype',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(balatype, value);
                    }
                },
                {
                    title: '账期',
                    align: 'left',
                    field: 'paymentDays'
                },
                {
                    title: '结算公司',
                    field: 'balaCorp',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(balaCorp, value);
                    }

                }
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
    });

    /**
     * 客户的选择框
     */
    function selectClient() {
        $.modal.open("选择客户", ctx + "client/related",'','',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                parent.$.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //客户id
            $("#balaName").val(rows[0]["customerId"]);
            //客户名称
            $("#balaCustomerId").val(rows[0]["custName"]);

            layer.close(index);
        });
    }
    var height = document.documentElement.clientHeight - 50;
    var width = document.documentElement.clientWidth - 320;

    /**
     * 跳转应收修改页面
     * @param id
     */
    function edit(id,vbillstatus,isClose) {
        if (vbillstatus != 0) {
            $.modal.alertWarning("只能修改新建状态的应收单");
            return;
        }
        if (isClose === '1') {
            $.modal.alertWarning("该应收单已关账");
            return;
        }
        var url = prefix + "/edit?receiveDetailId="+id;
        $.modal.openTab("应收明细修改", url);
    }

    /**
     * 调整
     */
    function  adjust() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["isClose"] === 0) {
            $.modal.alertWarning("请选择已关账的应收单据");
            return;
        }
        $.modal.openTab("调整", prefix + "/adjust?receiveDetailId="+bootstrapTable[0]["receiveDetailId"]);
    }
    /**
     * 生成对账单的方法
     */
    function checking() {

        var rows =  $.table.selectColumns("receiveDetailId");
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length;i++ ) {
            if (bootstrapTable[i]["vbillstatus"] !== 1) {
                $.modal.alertWarning("生成对账单的应收单据只能为已确认状态");
                return;
            }
        }
        if (rows.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        $.modal.openTab("生成对账单", prefix + "/checking?receiveDetailIds="+rows.join());
    }

    /**
     * 跳转反确认页面
     */
    function oppositeAffirm() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length;i++ ) {
            if (bootstrapTable[i]["vbillstatus"] !== 1) {
                $.modal.alertWarning("应收单据只能为已确认状态下才能反确认");
                return;
            }
            if (bootstrapTable[i]["isClose"] === 1 ) {
                $.modal.alertWarning("请选择未在关账日期内的应收单据");
                return;
            }
        }

        var receiveDetailIds =  $.table.selectColumns("receiveDetailId");
        if (receiveDetailIds.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.open("反确认", prefix + "/back_confirm/" + receiveDetailIds,500,300);
    }

    /**
     * 确认应收明细
     */
    function affirm() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length;i++ ) {

            if (bootstrapTable[i]["vbillstatus"] !== 0) {
                $.modal.alertWarning("应收单据只能为新建状态下才能确认");
                return;
            }
            if (bootstrapTable[i]["isClose"] === 1 ) {
                $.modal.alertWarning("请选择未在关账日期内的应收单据");
                return;
            }
        }
        var receiveDetailIds =  $.table.selectColumns("receiveDetailId");
        if (receiveDetailIds.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        $.modal.confirm("是否确认？", function () {
            $.operate.post(prefix + "/affirm", {"receiveDetailIds": receiveDetailIds.join()});
        });
    }




    /**
     * 加入对账单的方法
     */
    function insertChecking() {

        var rows =  $.table.selectColumns("receiveDetailId");
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length;i++ ) {
            if (bootstrapTable[i]["vbillstatus"] !== 1) {
                $.modal.alertWarning("加入对账单的应收单据只能为已确认状态");
                return;
            }
        }
        if (rows.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var customerId =  bootstrapTable[0]["customerId"];
        $.modal.open("加入对账单", prefix + "/insertChecking?customerId="+customerId+"&receiveDetailIds="+rows);
    }

    /**
     * 批量删除
     */
    function remove() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 0 ) {
                $.modal.alertWarning("只能删除新建状态下的应收单据");
                return;
            }
            if (bootstrapTable[i]["isClose"] === 1 ) {
                $.modal.alertWarning("请选择未在关账日期内的应收单据");
                return;
            }
        }
        var rows = $.table.selectFirstColumns();
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认要删除选中的数据吗?", function() {
            var url = prefix + "/remove";
            var data = { "ids": rows.join() };
            $.operate.submit(url, "post", "json", data);
        });
    }

    /**
     * 分批收款
     */
    function batchRece(){
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        if (bootstrapTable[0]["vbillstatus"] !== 1 && bootstrapTable[0]["vbillstatus"] !== 3) {
            $.modal.alertWarning("请选择已确认/部分核销的应收单");
            return;
        }
        if (bootstrapTable[0]["transFeeCount"] === bootstrapTable[0]["gotAmount"]) {
            $.modal.alertWarning("该应收单已完成");
            return;
        }
        var url = prefix + "/batchRece?receiveDetailId="+bootstrapTable[0]["receiveDetailId"];
        $.modal.open('分批收款',url);
    }

    function receRecord(){
        var id = $.table.selectColumns('receiveDetailId');
        var url =  ctx + "receSheetRecord" + "/receRecord?receiveDetailId="+id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "收款记录",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }
    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        data.status = $.common.join($('#status').selectpicker('val'));
        data.balatype = $.common.join($('#balatype').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 复核
     */
    function reexamine() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length;i++ ) {

            if (bootstrapTable[i]["vbillstatus"] !== 0) {
                $.modal.alertWarning("应收单据只能为新建状态下才能复核确认");
                return;
            }
            if (bootstrapTable[i]["isClose"] === 1 ) {
                $.modal.alertWarning("请选择未在关账日期内的应收单据");
                return;
            }
        }
        var receiveDetailIds =  $.table.selectColumns("receiveDetailId");
        if (receiveDetailIds.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var url = prefix + "/reexamine?receiveDetailIds="+receiveDetailIds;
        $.modal.openTab("复核", url);
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }
</script>

</body>
</html>