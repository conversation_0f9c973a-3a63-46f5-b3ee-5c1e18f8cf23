<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付对账')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">运单号：</label>-->
                            <div class="col-sm-12">
                                <input name="lot" id="lot" class="form-control"
                                       placeholder="请输入运单号" maxlength="30" th:value="${lot}" >
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">运单状态：</label>-->
                            <div class="col-sm-12">
                                <select name="lotVbillstatus" id="lotVbillstatus" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="运单状态" multiple>
                                    <option th:each="dict : ${entrustLotStatus}" th:text="${dict.context}"
                                            th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>


                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">承运商名称：</label>-->
                            <div class="col-sm-12">
                                <input name="carrierName" id="carrierName" placeholder="请输入承运商名称" class="form-control valid">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">调整人：</label>-->
                            <div class="col-sm-12">
                                <input name="regUserName" placeholder="请输入调整人" class="form-control valid">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                        <label class="col-sm-3">调整时间：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="pickStartDate" placeholder="调整开始日期">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="pickEndDate" placeholder="调整结束日期">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-6"></label>-->
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
<!--        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary single disabled" onclick="contract()" shiro:hasPermission="carrier:contract:view">
                <i class="fa fa-newspaper-o"></i> 查看合同
            </a>
            <a class="btn btn-primary single disabled" onclick="downloadForWord()" shiro:hasPermission="carrier:contract:view">
                <i class="fa fa-download"></i> 下载合同
            </a>
            <a class="btn btn-primary multiple single" onclick="detailTab()" shiro:hasPermission="trace:payReconciliation:detailTab">
                <i class="fa fa-newspaper-o"></i> 费用明细
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="trace:payReconciliation:export">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-info"  onclick="$.table.importExcel()" shiro:hasPermission="trace:payReconciliation:adjustImport">
                <i class="fa fa-upload"></i> 调整单导入
            </a>
            <a class="btn btn-warning"  onclick="adjustExport()" shiro:hasPermission="trace:payReconciliation:adjustExport">
                <i class="fa fa-download"></i> 调整单导出
            </a>
        </div>-->

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:payDetailAdjust:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a href="file/PayDetailAdjustModel.xlsx" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    //运单状态
    var entrustLotStatus = [[${entrustLotStatus}]];
    var prefix = ctx + "payDetailAdjust";

    //合计
/*    var receiptAmountFreightTotal = 0;//运费总应收
    var receiptAmountOnWayTotal = 0;//在途总应收
    var oilCardAmountTotal = 0;//油卡
    var sumTransFeeCountTotal = 0;//总金额
    var sumUngotAmountTotal = 0;//未付金额
    var sumGotAmountTotal = 0;//已付金额*/

    //合计
    var receiptAmountTotal = 0;//总金额
    var receiptAmountAdjustTotal = 0;//总调整金额




    $(function () {
        var options = {
            url: prefix + "/list",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/adjustImport",
            showToggle: false,
            showColumns: true,
            modalName: "应付对账",
            fixedColumns: true,
            height: 580,
            rememberSelected: false,
            fixedNumber: 3,
            clickToSelect:true,
            showFooter:true,
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            onPostBody: function () {
                //合并页脚
                merge_footer();
                getAmountCount();
            },
            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "合计:&nbsp&nbsp"
                        + "总金额:<nobr id='receiptAmountTotal'>￥0</nobr>&nbsp&nbsp"
                        + "调整后总金额:<nobr id='receiptAmountAdjustTotal'>￥0</nobr>&nbsp&nbsp<br>" +
                        "<b>总合计：</b>  总金额：<nobr id='receiptAmountTotalAll'>￥0</nobr>&nbsp&nbsp" +
                        "调整后总金额：<nobr id='receiptAmountAdjustTotalAll'>￥0</nobr>&nbsp&nbsp" ;
                }
            },


                {
                    title: '运单号',
                    field: 'lot',
                    align: 'left',
                },
                {
                    title: '运单状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(value,row) {
                        switch (value) {
                            case 0:
                                return '<span class="label label-default">待运</span>'
                            case 1:
                                return '<span class="label label-info">部分提货</span>';
                            case 2:
                                return '<span class="label label-success">已提货</span>';
                            case 3:
                                return '<span class="label label-info">部分到货 </label>';
                            case 4:
                                return '<span class="label label-success">已到货</span>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '车牌号',
                    align: 'left',
                    field: 'carNo'
                },

                {
                    title: '承运商',
                    align: 'left',
                    field: 'carrierName'
                },
                {
                    title: '司机',
                    align: 'left',
                    field: 'driverName'
                },
                {
                    title: '车长车型',
                    align: 'left',
                    field: 'carLenName',
                    formatter: function status(row,value) {
                        if(value.carLenName == null || value.carLenName == ''){
                            value.carLenName = '';
                        }
                        if(value.carTypeName == null || value.carTypeName == ''){
                            value.carTypeName = '';
                        }
                        return value.carLenName+value.carTypeName;
                    }
                },
                // {
                //     title: '车型',
                //     align: 'left',
                //     field: 'carTypeName'
                // },
                {
                    title: '提货|到货省市区',
                    align: 'left',
                    field: 'deliDetailAddress',
                    switchable: false,
                    formatter: function status(value, row, index) {
                        return row.deliDetailAddress + '<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>' + row.arriDetailAddress;
                    },
                },


                {
                    title: '调整前|调整后运费(元)',
                    align: 'right',
                    field: 'receiptAmountFreight',
                    formatter: function (value, row, index) {
                        if(value < row.receiptAmountFreightAfter){
                            return value+'|<label style="color:#fc2727">'+row.receiptAmountFreightAfter+'</label>';
                        }else if(value > row.receiptAmountFreightAfter){
                            return value+'|<label style="color:#068e48">'+row.receiptAmountFreightAfter+'</label>';
                        }else{
                            return value+'|'+row.receiptAmountFreightAfter;
                        }
                    }

                },
                {
                    title: '调整前|调整后在途(元)',
                    align: 'right',
                    field: 'receiptAmountOnWay',
                    formatter: function (value, row, index) {
                        if(value < row.receiptAmountOnWayAfter){
                            return value+'|<label style="color:#fc2727">'+row.receiptAmountOnWayAfter+'</label>';
                        }else if(value > row.receiptAmountOnWayAfter){
                            return value+'|<label style="color:#068e48">'+row.receiptAmountOnWayAfter+'</label>';
                        }else{
                            return value+'|'+row.receiptAmountOnWayAfter;
                        }
                    }

                },
                {
                    title: '调整前|调整后油卡金额(元)',
                    align: 'right',
                    field: 'oilCardAmount',
                    formatter: function (value, row, index) {
                        if(value < row.oilCardAmountAfter){
                            return value+'|<label style="color:#fc2727">'+row.oilCardAmountAfter+'</label>';
                        }else if(value > row.oilCardAmountAfter){
                            return value+'|<label style="color:#068e48">'+row.oilCardAmountAfter+'</label>';
                        }else{
                            return value+'|'+row.oilCardAmountAfter;
                        }
                    }

                },
                {
                    title: '调整前|调整后总金额(元)',
                    align: 'right',
                    field: 'sumTransFeeCount',
                    formatter: function (value, row, index) {
                        if(value < row.sumTransFeeCountAfter){
                            return value+'|<label style="color:#fc2727">'+row.sumTransFeeCountAfter+'</label>';
                        }else if(value > row.sumTransFeeCountAfter){
                            return value+'|<label style="color:#068e48">'+row.sumTransFeeCountAfter+'</label>';
                        }else{
                            return value+'|'+row.sumTransFeeCountAfter;
                        }
                    }

                },
                /*{
                    title: '已付金额(元)',
                    align: 'right',
                    field: 'sumGotAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未付金额(元)',
                    align: 'right',
                    field: 'sumUngotAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },*/
                {
                    title:'调整前|调整后总件数',
                    align:'left',
                    field:'numCount',
                    formatter: function (value, row, index) {
                        if(value < row.numCountAdjust){
                            return value+'|<label style="color:#fc2727">'+row.numCountAdjust+'</label>';
                        }else if(value > row.numCountAdjust){
                            return value+'|<label style="color:#068e48">'+row.numCountAdjust+'</label>';
                        }else{
                            return value+'|'+row.numCountAdjust;
                        }
                    }
                },
                {
                    title:'调整前|调整后总重量(吨)',
                    align:'left',
                    field:'weightCount',
                    formatter: function (value, row, index) {
                        if(value < row.weightCountAdjust){
                            return value+'|<label style="color:#fc2727">'+row.weightCountAdjust+'</label>';
                        }else if(value > row.weightCountAdjust){
                            return value+'|<label style="color:#068e48">'+row.weightCountAdjust+'</label>';
                        }else{
                            return value+'|'+row.weightCountAdjust;
                        }
                    }
                },
                {
                    title:'调整前|调整后总体积(m³)',
                    align:'left',
                    field:'volumeCount',
                    formatter: function (value, row, index) {
                        if(value < row.volumeCountAdjust){
                            return value+'|<label style="color:#fc2727">'+row.volumeCountAdjust+'</label>';
                        }else if(value > row.volumeCountAdjust){
                            return value+'|<label style="color:#068e48">'+row.volumeCountAdjust+'</label>';
                        }else{
                            return value+'|'+row.volumeCountAdjust;
                        }
                    }
                },

               /* {
                    title: '调整后运费(元)',
                    align: 'right',
                    field: 'receiptAmountFreightAfter',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '调整后在途(元)',
                    align: 'right',
                    field: 'receiptAmountOnWayAfter',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '调整后油卡金额(元)',
                    align: 'right',
                    field: 'oilCardAmountAfter',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '调整后总金额(元)',
                    align: 'right',
                    field: 'sumTransFeeCountAfter',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },*/
               /* {
                    title: '调整后已付金额(元)',
                    align: 'right',
                    field: 'sumGotAmountAfter',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '调整后未付金额(元)',
                    align: 'right',
                    field: 'sumUngotAmountAfter',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title:'调整后总件数',
                    align:'left',
                    field:'numCountAdjust'
                },
                {
                    title:'调整后总重量(吨)',
                    align:'left',
                    field:'weightCountAdjust'
                },
                {
                    title:'调整后总体积(m³)',
                    align:'left',
                    field:'volumeCountAdjust'
                },*/
                {
                    title:'调整人',
                    align:'left',
                    field:'regUserName'
                },
                {
                    title:'调整时间',
                    align:'left',
                    field:'regDate'
                }

            ]
        };
        $.table.init(options);

        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
    });



    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        data.lotVbillstatus = $.common.join($('#lotVbillstatus').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        data.lotVbillstatus = $.common.join($('#lotVbillstatus').selectpicker('val'));
        $.ajax({
            url: prefix + "/getAdjustCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    // 总金额
                    $("#receiptAmountTotalAll").text(data.sumTransFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    // 运费类型金额
                    $("#receiptAmountAdjustTotalAll").text(data.sumTransFeeCountAfter.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }


    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 费用明细
     */
    function detailTab(){
        var entrustLotId = $.table.selectColumns('entrustLotId');//运单id
        var url = prefix + "/detailTab/"+entrustLotId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "费用明细",
            area: ['800px', '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }


    /**
     * 将总计金额清零
     */
    function clearTotal() {
        receiptAmountTotal = 0;//总金额
        receiptAmountAdjustTotal = 0;//总调整金额

    }

    /**
     * 累计总金额
     */
    function addTotal(row) {
        receiptAmountTotal = receiptAmountTotal + row.sumTransFeeCount;//总金额
        receiptAmountAdjustTotal = receiptAmountAdjustTotal + row.sumTransFeeCountAfter;//总调整金额

    }

    function subTotal(row) {
        receiptAmountTotal = receiptAmountTotal - row.sumTransFeeCount;//总金额
        receiptAmountAdjustTotal = receiptAmountAdjustTotal - row.sumTransFeeCountAfter;//总调整金额

    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#receiptAmountTotal").text(receiptAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#receiptAmountAdjustTotal").text(receiptAmountAdjustTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));

    }

    //查看合同
    function contract() {
        var entrustLotId = $.table.selectColumns('entrustLotId');//运单id
        var url = ctx + "basic/carrier/contractView/"+entrustLotId
        $.modal.openTab("查看合同" , url);
    }

    //下载合同
    function downloadForWord(){
        var entrustLotId = $.table.selectColumns('entrustLotId');//运单id
        var url = ctx + "basic/carrier/contractDownload/"+entrustLotId
        $.operate.saveModal(url, $('#role-form').serialize(),function (result) {
            if (result.code == web_status.SUCCESS){
                var hostport=document.location.host;
                var donwloadPath = "http://"+hostport + result.data;
                window.open(donwloadPath);
            }
        });
    }

    function adjustExport(){
        var data = $("#role-form").serializeArray();
        var lotVbillstatusList = {};
        lotVbillstatusList.name = "lotVbillstatus"
        lotVbillstatusList.value = $.common.join($('#lotVbillstatus').selectpicker('val'));
        data.push(lotVbillstatusList);
        $.modal.confirm("确定导出所有" +
            $.table._option.modalName + "吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(prefix + "/exportAdjust", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

</script>
</body>
</html>