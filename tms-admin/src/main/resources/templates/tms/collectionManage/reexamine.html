<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('复核')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>

</head>
<style type="text/css">
    .td td {
        position: relative
    }
</style>
<body>
<div class="form-content custom-content">

    <form id="form-user-add"  class="form-horizontal" novalidate="novalidate">

        <div class="panel panel-default">
            <div class="panel-heading">
                <h5 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseOne">客户信息</a>
                </h5>
            </div>
            <div id="collapseOne" class="panel-collapse collapse in">
                <div class="panel-body">
                    <!--基础信息 begin-->
                    <input type="hidden" id="custId" name="custId" th:value="${client.customerId}">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">客户编码：</label>
                                <div class="col-sm-7">
                                    [[${client.custCode}]]
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">客户名称：</label>
                                <div class="col-sm-7">
                                    [[${client.custName}]]
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">结算组：</label>
                                <div class="col-sm-7">
                                    [[${client.balaDeptName}]]
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">运营组：</label>
                                <div class="col-sm-7">
                                    [[${client.salesDeptName}]]
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">结算公司：</label>
                                <div class="col-sm-7">
                                    <span class="form-control-static" th:text="${@dict.getLabel('bala_corp',client.balaCorp)}"></span>
                                </div>
                            </div>
                        </div>


                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">业务员：</label>
                                <div class="col-sm-7">
                                    [[${client.psndocName}]]
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">业务员联系方式：</label>
                                <div class="col-sm-7">
                                    [[${client.psncontact}]]
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--基础信息 end-->
                </div>
            </div>
        </div>

        <div class="panel panel-default" id="isShow" style="display: none">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseCheck">开票信息</a>
                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseCheck">
                <div class="panel-body">
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTabThree" class="custom-tab table" >

                            <thead>
                            <tr>
                                <th style="width: 10%;" class="isCheck">发票抬头</th>
                                <th style="width: 8%;" class="isCheck">开票公司</th>
                                <th style="width: 8%;" class="isCheck">开票金额</th>
                                <th style="width: 10%;" class="isCheck">纳税识别号</th>
                                <th style="width: 12%;" class="isCheck">开户银行</th>
                                <th style="width: 12%;">开票类型</th>
                                <th style="width: 10%;" class="isCheck">开户账号</th>
                                <th style="width: 12%;" class="isCheck">地址及电话</th>
                                <th style="width: 15%;">开票备注</th>
                            </tr>

                            </thead>
                            <tbody>

                            <tr th:each="receBilling:${receBillingList}">

                                <td th:text="${receBilling.billingPayable}">
                                </td>
                                <td  th:each="dict : ${@dict.getType('bala_corp')}"
                                     th:if="${dict.dictValue} == ${receBilling.billingCorp}"
                                     th:text="${dict.dictLabel}">
                                </td>
                                <td th:text="${receBilling.billingAmount}">

                                </td>
                                <td th:text="${receBilling.taxIdentify}">
                                </td>
                                <td th:text="${receBilling.bank}">
                                    <input type="text" id="bank0" name="receBillingList[0].bank" maxlength="125" class="form-control isCheckVal" required disabled>
                                </td>
                                <td th:each="dict : ${@dict.getType('billing_type')}"
                                    th:if="${dict.dictValue} == ${receBilling.billingType}"
                                    th:text="${dict.dictLabel}">
                                </td>
                                <td th:text="${receBilling.bankAccount}">
                                </td>
                                <td th:text="${receBilling.addressPhone}">
                                </td>
                                <td th:text="${receBilling.memo}">

                                </td>


                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>




        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">合同信息</a>
                </h4>
            </div>

            <div class="panel-collapse collapse in" id="collapseTwo">
                <div class="panel-body">
                    <!-- begin-->
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTab" class="custom-tab table td">
                            <thead>
                            <tr>
                                <th style="width: 20%;">合同附件</th>
                                <th style="width: 15%;">合同名称</th>
                                <th style="width: 10%;">合同开始时间</th>
                                <th style="width: 10%;">合同到期时间</th>
                                <th style="width: 10%;">合同预警时间</th>
                            </tr>
                            </thead>
                            <tbody>


                            <tr th:each="mapS,status:${contracts}">

                                <th:block th:if='${mapS.tid!=null}'>
                                    <td>
                                        <a href="#" th:name="${mapS.filePath}" onclick="downloadFile(this.name)">[[${mapS.fileName}]]</a>
                                        <input type="hidden" th:name="|contractList[${status.index}].tid|"  th:value="${mapS.tid}">
                                        <input  th:name="|contractList[${status.index}].fileName|" type="hidden" th:value="${mapS.fileName}">
                                    </td>
                                </th:block>
                                <th:block th:if='${mapS.tid==null}'>
                                <td>
                                   未上传合同附件
                                </td>
                                </th:block>
                                <td><div class="input-group" th:text="${mapS.name}"></div></td>


                                 <input type="hidden" id="contractsSize" th:value="${status.size}">

                                <td><div class="input-group" th:text="${#dates.format(mapS.effectiveDate, 'yyyy-MM-dd')}"></div></td>
                                <td><div class="input-group" th:text="${#dates.format(mapS.invalidDate, 'yyyy-MM-dd')}"></div></td>
                                <td><div class="input-group" th:text="${#dates.format(mapS.warningDate, 'yyyy-MM-dd')}"></div></td>
                            </tr>

                            </tbody>
                        </table>
                    </div>
                    <!--end-->
                </div>
            </div>
        </div>

        <div class="panel panel-default" >
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion" href="tabs_panels.html#collapseFour">合同价</a>
                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseFour">
                <div class="panel-body">
                    <!-- begin-->
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTabFour" class="custom-tab table td">
                            <thead>
                            <tr>
                                <th>线路名称</th>
                                <th >公里</th>
                                <th>计费方式</th>
                                <th>货品特性</th>
                                <th>车长</th>
                                <th>车型</th>
                                <th>是否有区间</th>
                                <th rowspan="2" style="width: 10%;">&nbsp;价格</th>
                            </tr>

                            </thead>
                            <tbody>
                            <tr th:each="mapS,status:${contractpcs}" >
                                <input type="hidden" id="contractpcsSize" th:value="${status.size}">

                                <td><div class="input-group" th:text="${mapS.lineName}"></div></td>
                                <td><div class="input-group" th:text="${mapS.mileage}"></div></td>
                                <td><div class="input-group" th:text="${mapS.billingName}"></div></td>
                                <td><div class="input-group" th:text="${@dict.getLabel('goods_character', mapS.goodsCharacter)}"></div></td>
                                <th:block th:if='${mapS.carLen==null}'>
                                    <td></td>
                                </th:block>
                                <th:block th:if='${mapS.carLen!=null}'>
                                    <td><div class="input-group" th:text="${@dict.getLabel('car_len', mapS.carLen)}"></div></td>
                                </th:block>
                                <th:block th:if='${mapS.carType==null}'>
                                    <td></td>
                                </th:block>
                                <th:block th:if='${mapS.carType!=null}'>
                                    <td><div class="input-group" th:text="${@dict.getLabel('car_type', mapS.carType)}"></div></td>
                                </th:block>
                                <input type="hidden" th:id="ifSection_+${status.index}" th:value="${mapS.ifSection}">
                                <td><div class="input-group" th:text="${@dict.getLabel('if_section', mapS.ifSection)}"></div></td>
                                <td style="text-align: center;" class="cla">
                                    <div class="price" th:id="price_+${status.index}"
                                        th:text="￥+${#numbers.formatDecimal(mapS.guidingPrice,1,'COMMA',2,'POINT')}"></div>
                                    <a href="#" class="collapse-link add-alink show-layer-alink popup" th:id="popup_+${status.index}"
                                       style="font-size: 12px; margin:0px; font-weight:400; width:auto; padding:0px 6px;">价格区间</a>


                                    <!--弹出层div展示内容在这里-->
                                    <div class="show-div">


                                        <div class="row show-add" >
                                            <div class="col-sm-12"><div>
                                        </div>

                                        <div class="row show-add d0" th:each="sectionS,sectionSatus:${contractpcs[status.index].contractpcSectionList}">
                                            <a class="close-link  show-alink" style="color: black" ><span th:text="${sectionSatus.index}+1+'.'"></span></a>

                                            <div class="col-sm-12 sectionIndex" th:id="${sectionSatus.size}">
                                                <input th:name="range_+${status.index}"  hidden>
                                                <div class="form-group"  >
                                                    <div class="col-sm-2 ">
                                                        <div class="form-group">
                                                        <label class="col-sm-6">区间开始：</label>
                                                            <div class="col-sm-6">
                                                                [[${sectionS.startSection}]]
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-sm-1">
                                                        <th:block th:if='${sectionS.startOperator==0}'>
                                                            ＞
                                                        </th:block>
                                                        <th:block th:if='${sectionS.startOperator==1}'>
                                                            ≥
                                                        </th:block>
                                                    </div>
                                                    <div class="col-sm-1">
                                                        <th:block th:if='${sectionS.endOperator==2}'>
                                                            ＜
                                                        </th:block>
                                                        <th:block th:if='${sectionS.endOperator==3}'>
                                                            ≤
                                                        </th:block>
                                                    </div>
                                                    <div class="col-sm-2">
                                                        <div class="form-group">
                                                            <label class="col-sm-6">区间结束：</label>
                                                            <div class="col-sm-6">
                                                                [[${sectionS.endSection}]]
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-2">
                                                        <div class="form-group">
                                                            <label class="col-sm-6">价格：</label>
                                                            <div class="col-sm-6">
                                                                ￥[[${#numbers.formatDecimal(sectionS.guidingPrice,1,'COMMA',2,'POINT')}]]
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>

                                        </div>
                                    </div>
                            </tr>

                            </tbody>
                        </table>

                    </div>
                    <!--end-->
                </div>
            </div>

        </div>
        <div th:each="reexamine,status:${reexamineList}" style="display: none">
        <div class="inv-box">
            <div class="inv-tit">
                <p class="inv-name" data-toggle="collapse" data-parent="#accordion" th:href="tabs_panels.html+'#'+cbox+${status.index}+0" >发货单号：<span th:text="${reexamine.invoiceNo}"></span></p>
            </div>
        <div class="inv-con collapse in" th:id="cbox+${status.index}+0" aria-expanded="true">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       th:href="tabs_panels.html+'#'+collapse+${status.index}+1">回单信息</a>
                </h4>
            </div>
            <div th:id="collapse+${status.index}+1" class="panel-collapse collapse in">
                <div class="panel-body"  th:each="entrustDto:${reexamine.entrustList}">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">回单人：</label>
                                <div class="col-sm-7" th:text="${entrustDto.receiptMan}"></div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">回单时间：</label>
                                <div class="col-sm-7" th:text ="${#dates.format(entrustDto.receiptDate,'yyyy-MM-dd HH:mm:ss')}">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">客户订单号：</label>
                                <div class="col-sm-7" th:text="${entrustDto.custOrderno}">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">发货单号：</label>
                                <div class="col-sm-7" th:text="${entrustDto.invoiceVbillno}"></div>
                            </div>
                        </div>

                    </div>
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">回单数：</label>
                                <div class="col-sm-7" th:text="${entrustDto.receiptNum}">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">

                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">回单图片：</label>
                                <div class="col-sm-8" >
                                    <img style="height:100px" modal="zoomImg" th:src="${entrustDto.filePath}"/>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-2">回单备注：</label>
                                <div class="col-sm-10" th:text ="${entrustDto.receiptMemo}">
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--基础信息 end-->
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       th:href="tabs_panels.html+'#'+collapse+${status.index}+2">回单货品信息</a>
                </h4>
            </div>
            <div th:id="collapse+${status.index}+2" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0"  class="custom-tab table">
                            <thead>
                            <tr>
                                <th style="width: 15%;">货品编码</th>
                                <th style="width: 12%;">货品名称</th>
                                <th style="width: 10%;">件数</th>
                                <th style="width: 10%;">重量</th>
                                <th style="width: 10%;">体积</th>
                                <th style="width: 10%;">回单件数</th>
                                <th style="width: 10%;">回单重量</th>
                                <th style="width: 10%;">回单体积</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr name="regRows" th:each="goods:${reexamine.entPackGoodsList}">
                                <td><div class="input-group" th:text="${goods.goodsCode}"></div></td>
                                <td><div class="input-group" th:text="${goods.goodsName}"></div></td>
                                <td><div class="input-group" th:text="${goods.num}"></div></td>
                                <td><div class="input-group" th:text="${goods.weight}"></div></td>
                                <td><div class="input-group" th:text="${goods.volume}"></div></td>
                                <td><div class="input-group" th:text="${goods.receiptNum}"></div></td>
                                <td><div class="input-group" th:text="${goods.receiptWeight}"></div></td>
                                <td><div class="input-group" th:text="${goods.receiptVolume}"></div></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--订单货品费用明细 end-->
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       th:href="tabs_panels.html+'#'+collapse+${status.index}+3">委托单信息</a>
                </h4>
            </div>
            <div th:id="collapse+${status.index}+3" class="panel-collapse collapse in">
                <div class="panel-body">
                    <!--订单货品费用明细 begin-->
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0"  class="custom-tab table">
                            <thead>
                            <tr>
                                <th style="width: 10%;">委托单号</th>
                                <th style="width: 8%;">委托单状态</th>
                                <th style="width: 8%;">运输方式</th>
                                <th style="width: 6%;">车长</th>
                                <th style="width: 10%;">车型</th>
                                <th style="width: 15%;">要求提货时间</th>
                                <th style="width: 15%;">要求到货时间</th>

                            </tr>
                            </thead>
                            <tbody>
                            <tr  th:each="entrustDto:${reexamine.entrustList}">
                                <td><div class="input-group" th:text="${entrustDto.vbillno}"></div></td>
                                <th:block th:if='${entrustDto.vbillstatus=="0"}'>
                                    <td><div class="input-group">待确认</div></td>
                                </th:block>
                                <th:block th:if='${entrustDto.vbillstatus=="1"}'>
                                    <td><div class="input-group">已确认</div></td>
                                </th:block>
                                <th:block th:if='${entrustDto.vbillstatus=="2"}'>
                                    <td><div class="input-group">已提货</div></td>
                                </th:block>
                                <th:block th:if='${entrustDto.vbillstatus=="3"}'>
                                    <td><div class="input-group">已到货</div></td>
                                </th:block>
                                <th:block th:if='${entrustDto.vbillstatus=="5"}'>
                                    <td><div class="input-group">关闭</div></td>
                                </th:block>
                                <td><div class="input-group" th:text="${entrustDto.transName}"></div></td>
                                <th:block th:if='${entrustDto.carLenId!=null}'>
                                    <td><div class="input-group" th:text="${@dict.getLabel('car_len',entrustDto.carLenId)}"></div></td>
                                </th:block>
                                <th:block th:if='${entrustDto.carLenId==null}'>
                                    <td></td>
                                </th:block>
                                <td><div class="input-group" th:text="${entrustDto.carTypeName}"></div></td>
                                <td><div class="input-group" th:text="${#dates.format(entrustDto.reqDeliDate, 'yyyy-MM-dd HH:mm:ss')}"></div></td>
                                <td><div class="input-group" th:text="${#dates.format(entrustDto.reqArriDate, 'yyyy-MM-dd HH:mm:ss')}"></div></td>

                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--订单货品费用明细 end-->
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       th:href="tabs_panels.html+'#'+collapse+${status.index}+5">运单信息</a>
                </h4>
            </div>
            <div th:id="collapse+${status.index}+5" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0"  class="custom-tab table">
                            <thead>
                            <tr>
                                <th style="width: 15%;">运单号</th>
                                <th style="width: 20%;">发货地址</th>
                                <th style="width: 20%;">到货地址</th>
                                <th style="width: 15%;">司机</th>

                            </tr>
                            </thead>
                            <tbody>
                            <tr  th:each="entrustLot:${reexamine.entrustLotList}">
                                <td><div class="input-group" th:text="${entrustLot.lot}"></div></td>
                                <td><div class="input-group" th:text="${entrustLot.deliProvinceName}+${entrustLot.deliCityName}+${entrustLot.deliAreaName}"></div></td>
                                <td><div class="input-group" th:text="${entrustLot.arriProvinceName}+${entrustLot.arriCityName}+${entrustLot.arriAreaName}"></div></td>
                                <td><div class="input-group" th:text="${entrustLot.driverName}"></div></td>

                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       th:href="tabs_panels.html+'#'+collapse+${status.index}+6">成本分摊信息</a>
                </h4>
            </div>
            <div th:id="collapse+${status.index}+6" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0"  class="custom-tab table">
                            <thead>
                            <tr>
                                <th style="width: 15%;">发货单号</th>
                                <th style="width: 15%;">客户名称</th>
                                <th style="width: 15%;">运单号</th>
                                <th style="width: 15%;">委托单号</th>
                                <th style="width: 15%;">费用类型</th>
                                <th style="width: 15%;">金额</th>


                            </tr>
                            </thead>
                            <tbody>
                            <tr  th:each="allocation:${reexamine.allocationList}">
                                <td><div class="input-group" th:text="${allocation.invoiceNo}"></div></td>
                                <td><div class="input-group" th:text="${allocation.custAbbr}"></div></td>
                                <td><div class="input-group" th:text="${allocation.lot}"></div></td>
                                <td><div class="input-group" th:text="${allocation.entrustNo}"></div></td>
                                <th:block th:if='${allocation.freeType=="0"}'>
                                    <td><div class="input-group" th:text="运费"></div></td>
                                </th:block>
                                <th:block th:if='${allocation.freeType=="1"}'>
                                    <td><div class="input-group" th:text="在途费用"></div></td>
                                </th:block>
                                <th:block th:if='${allocation.freeType=="2"}'>
                                    <td><div class="input-group" th:text="调整费"></div></td>
                                </th:block>
                                <td  th:align="right" th:text="￥+${#numbers.formatDecimal(allocation.costShare,1,'COMMA',2,'POINT')}"></td>

                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       th:href="tabs_panels.html+'#'+collapse+${status.index}+7">应收费用明细</a>
                </h4>
            </div>
            <div th:id="collapse+${status.index}+7" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0"  class="custom-tab table">
                            <thead>
                            <tr>
                                <th style="width: 15%;">应付单号</th>
                                <th style="width: 15%;">应付单状态</th>
                                <th style="width: 15%;">发货单号</th>
                                <th style="width: 10%;">费用类型</th>
                                <th style="width: 10%;">总金额</th>
                                <th style="width: 10%;">已付金额</th>
                                <th style="width: 10%;">未付金额</th>


                            </tr>
                            </thead>
                            <tbody>
                            <tr  th:each="receiveDetail:${reexamine.receiveDetails}">
                                <td><div class="input-group" th:text="${receiveDetail.vbillno}"></div></td>
                                <th:block th:if='${receiveDetail.vbillstatus==0}'>
                                    <td><div class="input-group" th:text="新建"></div></td>
                                </th:block>
                                <th:block th:if='${receiveDetail.vbillstatus==1}'>
                                    <td><div class="input-group" th:text="已确认"></div></td>
                                </th:block>
                                <th:block th:if='${receiveDetail.vbillstatus==2}'>
                                    <td><div class="input-group" th:text="已对账"></div></td>
                                </th:block>
                                <th:block th:if='${receiveDetail.vbillstatus==3}'>
                                    <td><div class="input-group" th:text="部分核销"></div></td>
                                </th:block>
                                <th:block th:if='${receiveDetail.vbillstatus==4}'>
                                    <td><div class="input-group" th:text="已核销"></div></td>
                                </th:block>
                                <th:block th:if='${receiveDetail.vbillstatus==5}'>
                                    <td><div class="input-group" th:text="关闭"></div></td>
                                </th:block>
                                <td><div class="input-group" th:text="${receiveDetail.invoiceVbillno}"></div></td>
                                <th:block th:if='${receiveDetail.freeType=="0"}'>
                                    <td><div class="input-group" th:text="运费"></div></td>
                                </th:block>
                                <th:block th:if='${receiveDetail.freeType=="1"}'>
                                    <td><div class="input-group" th:text="在途费用"></div></td>
                                </th:block>
                                <th:block th:if='${receiveDetail.freeType=="2"}'>
                                    <td><div class="input-group" th:text="调整费用"></div></td>
                                </th:block>

                                <td  th:align="right" th:text="￥+${#numbers.formatDecimal(receiveDetail.transFeeCount,1,'COMMA',2,'POINT')}"></td>
                                <td  th:align="right" th:text="￥+${#numbers.formatDecimal(receiveDetail.gotAmount,1,'COMMA',2,'POINT')}"></td>
                                <td  th:align="right" th:text="￥+${#numbers.formatDecimal(receiveDetail.ungotAmount,1,'COMMA',2,'POINT')}"></td>

                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       th:href="tabs_panels.html+'#'+collapse+${status.index}+8">费用信息</a>
                </h4>
            </div>
            <div th:id="collapse+${status.index}+8" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <label class="col-sm-4">成本：</label>
                                <div class="col-sm-8">
                                    <div style="color:#ff8b1b;font-size:15px" th:text="￥+${#numbers.formatDecimal(reexamine.totalCostShare,1,'COMMA',2,'POINT')}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <label class="col-sm-4">应收：</label>
                                <div class="col-sm-8">
                                    <div style="color:#ff8b1b;font-size:15px" th:text="￥+${#numbers.formatDecimal(reexamine.totalTransFeeCount,1,'COMMA',2,'POINT')}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <label class="col-sm-4">第三方费用：</label>
                                <div class="col-sm-8">
                                    <div style="color:#ff8b1b;font-size:15px" th:text="￥+${#numbers.formatDecimal(reexamine.totalOtherFee,1,'COMMA',2,'POINT')}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <label class="col-sm-4">毛利：</label>
                                <div class="col-sm-8">
                                    <div style="color:#ff8b1b;font-size:15px" th:text="￥+${#numbers.formatDecimal(reexamine.margin,1,'COMMA',2,'POINT')}"></div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="fixed-table-body table-responsive" th:if="${reexamine.totalOtherFee != 0}">
                        <table class="table-hover table">
                            <thead>
                            <tr>
                                <th style="width: 15%;">费用类型</th>
                                <th style="width: 15%;">总金额</th>
                                <th style="width: 70%;">备注</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr  th:each="otherFee:${reexamine.otherFeeList}">
                                <td><div class="input-group" th:each="dict : ${@dict.getType('cost_type_on_way')}"
                                         th:if="${dict.dictValue} == ${otherFee.feeType}"
                                         th:text="${dict.dictLabel}"></div></td>
                                <td  th:align="right" th:text="￥+${#numbers.formatDecimal(otherFee.feeAmount,1,'COMMA',2,'POINT')}"></td>
                                <td th:text="${otherFee.memo}"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        </div>
        </div>

        </div>
    </form>

    </div>

</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" th:if="${receSheetRecord.vbillstatus == 0}" onclick="commit()" shiro:hasPermission="finance:receSheetRecord:check">
            <i class="fa fa-check"></i>审核通过
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" th:if="${receSheetRecord.vbillstatus == 0}" onclick="check()" shiro:hasPermission="finance:receSheetRecord:check">
            <i class="fa fa-remove"></i>不通过
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger"  onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">

    var prefix = ctx + "client";
    $(function () {

        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseCheck').collapse('show');


        var options = {};
        $.table.init(options);


        var contractpcsIndex = $("#contractpcsSize").val() - 0;

        for (var i = 0; i <contractpcsIndex ; i++) {
            // 是否有区间
            var price = "#price_"+i;
            var ifSection = "#ifSection_"+i;
            var popup = "#popup_"+i;
            if ($(ifSection).val() == '1') {
                $(popup).show();
                $(price).hide();
            }else {
                $(popup).hide();
                $(price).show();
            }

            // 计费方式
            var billingMethod = "#billingMethod_"+i;
            var mileage = "#mileage_"+i;
            if ($(billingMethod).val() == '6') {
                $(mileage).prop("disabled", false);
                $(ifSection).each(function(){
                    $(this).find("option").eq(1).prop("selected",true)
                });
                $(ifSection).prop("disabled", true);
                $(popup).hide();
                $(price).show();
            }else {
                $(mileage).prop("disabled", true);
                $(ifSection).prop("disabled", false);
            }
        }
    });


    // 审核通过
    function commit() {
        var vbillstatus = [[${vbillstatus}]];
        if (vbillstatus !== 0){
            $.modal.alertWarning("只能在待审核状态下才能进行审核");
            return false;
        }
        var receSheetRecordId = [[${receSheetRecordId}]];
        var url = ctx+"receSheetRecord/check/1/"+receSheetRecordId;
        $.operate.saveTab(url, '')
    }
    // 审核不通过
    function check() {
        var vbillstatus = [[${vbillstatus}]];
        if (vbillstatus !== 0){
            $.modal.alertWarning("只能在待审核状态下才能进行审核");
            return false;
        }
        var receSheetRecordId = [[${receSheetRecordId}]];
        var url = ctx+"receSheetRecord/check/2/"+receSheetRecordId;
        $.operate.saveTab(url, '')
    }


    var height = document.documentElement.clientHeight - 50;
    var width = document.documentElement.clientWidth - 100;
    $(function () {
        //弹出层事件
        $(".table").on('click', 'a.show-layer-alink', function () {
            layer.open({
                type: 1,
                title: '查看区间',
                content: $(this).next(".show-div"),
                area: [width + 'px', height + 'px'],
                btn: ['确定', '取消'],
                yes: function (index, layero) {
                    layer.close(index);
                }
            });
            return false;
        });

    });


    function downloadFile(value){
        window.location.href = ctx + "common/downloadFile?fileName=" + value+"&delete=false";
    }
</script>
</body>

</html>