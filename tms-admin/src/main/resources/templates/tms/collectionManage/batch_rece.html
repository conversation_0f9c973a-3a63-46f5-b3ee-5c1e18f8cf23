<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收款申请管理-分批收款')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-batchRece-add" class="form-horizontal" novalidate="novalidate">
        <!--收款申请ids-->
        <input type="hidden" name="receSheetRecordId" th:value="${receSheetRecordIds}">
        <!--收款额度-->
        <input type="hidden" id="quota" th:value="*{receSheetRecord.ungotAmount}">
        <!--总金额-->
        <input type="hidden" name="totalAmount" th:value="*{receSheetRecord.receivableAmount}">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <input name="deptId" type="hidden" id="treeId">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">总金额：</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" th:value="*{receSheetRecord.receivableAmount}" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">调整金额：</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" th:value="*{receSheetRecord.adjustAmount}" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">可申请额度：</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" th:value="*{receSheetRecord.ungotAmount}" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款类型：</label>
                                    <div class="col-sm-8">
                                        <input name="receivableType" class="form-control" value="0" type="hidden">
                                        <input class="form-control" value="对账收款" disabled >
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">收款金额：</label>
                                    <div class="col-sm-8">
                                        <input name="receivableAmount" id="receivableAmount"  required
                                               type="text" oninput="$.numberUtil.onlyNumberTwoDecimal(this)"  maxlength="15"
                                               th:value="*{receSheetRecord.ungotAmount}" class="form-control">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款方式：</label>
                                    <div class="col-sm-8">
                                        <select name="receivableMethod" class="form-control" th:with="type=${@dict.getType('receivable_method')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"  th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款日期：</label>
                                    <div class="col-sm-8">
                                        <input name="receivableDate" id="receivableDate" class="form-control">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">转入账户：</label>
                                    <div class="col-md-8 col-sm-8">
                                        <div class="input-group">
                                            <input name="accountName" id="accountName" required class="form-control valid"
                                                   type="text" aria-required="true" maxlength="25">
                                            <!--账户id-->
                                            <input name="inAccount" id="inAccount"  class="form-control valid"
                                                   type="hidden" aria-required="true" maxlength="25">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="memo" maxlength="250" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel-group">
            <div class="panel panel-default">
                <div class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div id="collapseThree" class="panel-collapse collapse in">
                            <div class="panel-body">
                                <!--订单货品费用明细 begin-->
                                <div class="fixed-table-body" style="margin: 0px -5px;">
                                    <table border="0" class="custom-tab table" id="table" style="min-width:1500px;">
                                        <thead>
                                        <tr>
                                            <th style="width: 3%;"><input type="checkbox" name="selectAll"></th>
                                            <th style="width: 3%;">详情</th>
                                            <th style="width: 7%;">发票抬头</th>
                                            <th style="width: 7%;">开票金额(元)</th>
                                            <th style="width: 8%;">开票公司</th>
                                            <th style="width: 7%;">纳税识别号</th>
                                            <th style="width: 10%;">开票类型</th>
                                            <th style="width: 7%;">是否开票</th>
                                            <th style="width: 8%;">开票人</th>
                                            <th style="width: 10%;">开票时间</th>
                                            <th style="width: 10%;">开户银行</th>
                                            <th style="width: 10%;">开户账号</th>
                                            <th style="width: 10%;">地址及电话</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="receBilling:${receBillingList}">
                                            <td><input type="checkbox" name="rmReq" th:value="${receBilling.receBillingId}"></td>
                                            <td nowrap="nowrap">
                                                <a class="btn  btn-xs" href="javascript:void(0)" title="应收明细"
                                                   th:data-id="${receBilling.receBillingId}"
                                                   onclick="detail(this.getAttribute('data-id'))">
                                                    <i class="fa fa fa-list" style="font-size: 15px;"></i>
                                                </a>
                                            </td>
                                            <td nowrap="nowrap" th:text="${receBilling.billingPayable}"></td>
                                            <td nowrap="nowrap" th:text="${receBilling.billingAmount}"></td>
                                            <td nowrap="nowrap">
                                                <div class="col-sm-8" th:each="dict : ${@dict.getType('bala_corp')}"
                                                     th:if="${dict.dictValue} == ${receBilling.billingCorp}" th:text="${dict.dictLabel}">
                                                </div>
                                            </td>
                                            <td nowrap="nowrap" th:text="${receBilling.taxIdentify}"></td>
                                            <td nowrap="nowrap">
                                                <div class="col-sm-8" th:each="dict : ${@dict.getType('billing_type')}"
                                                     th:if="${dict.dictValue} == ${receBilling.billingType}" th:text="${dict.dictLabel}">
                                                </div>
                                            </td>
                                            <td nowrap="nowrap" th:if="${receBilling.billingStatus == 0}">否</td>
                                            <td nowrap="nowrap" th:if="${receBilling.billingStatus == 1}">是</td>
                                            <td nowrap="nowrap" th:text="${receBilling.billingUser}"></td>
                                            <td nowrap="nowrap" th:text="${receBilling.billingDate}"></td>
                                            <td nowrap="nowrap" th:text="${receBilling.bank}"></td>
                                            <td nowrap="nowrap" th:text="${receBilling.bankAccount}"></td>
                                            <td nowrap="nowrap" th:text="${receBilling.addressPhone}"></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<script th:inline="javascript">
    var prefix = ctx + "receSheetRecord";

    /**
     * 默认收款日期为当前时间
     */
    var time = new Date();
    var day = ("0" + time.getDate()).slice(-2);
    var month = ("0" + (time.getMonth() + 1)).slice(-2);
    var today = time.getFullYear() + "-" + (month) + "-" + (day);
    var h=time.getHours();       //获取当前小时数(0-23)
    var m=time.getMinutes();     //获取当前分钟数(0-59)
    var s=time.getSeconds();     //获取当前秒数
    var today = time.getFullYear() + "-" + (month) + "-" + (day) + " "+h+":" +m+ ":"+s;
    $("#receivableDate").val(today);

    var quota = parseFloat($("#quota").val());//收款额度

    $(function () {
        $('#collapseOne').collapse('show');
        /** 校验 */
        $("#form-batchRece-add").validate({
            focusCleanup: true
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#receivableDate',
                type: 'datetime',
                trigger: 'click'
            });
        });

        $('input[name="selectAll"]').on("change",function(){
            if($(this).is(':checked')){
                $('input[name="rmReq"]').each(function(){
                    $(this).attr("tag","selected");
                    $(this).prop("checked",true);
                });
            }else{
                $('input[name="rmReq"]').each(function(){
                    $(this).attr("tag","");
                    $(this).prop("checked",false);
                });
            }
        });

        $('input[name="rmReq"]').on("change",function(){
            if($(this).is(':checked')){
                $(this).attr("tag","selected");
                if($("input[name='rmReq']:checked").length ==  $('input[name="rmReq"]').length){
                    $('input[name="selectAll"]').prop("checked",true);
                }
            }else{
                $(this).attr("tag","");
                $('input[name="selectAll"]').prop("checked",false);
            }
        });

        //除了表头（第一行）以外所有的行添加click事件.
        $("#table").find("tr").slice(1).click(function () {
            // // 切换样式
            // $(this).toggleClass("tr_active");
            // 找到checkbox对象
            var chks = $("input[type='checkbox']",this);
            var tag = chks.attr("tag");
            if(tag=="selected"){
                // 之前已选中，设置为未选中
                chks.attr("tag","");
                chks.prop("checked",false).change();
            }else{
                // 之前未选中，设置为选中
                chks.attr("tag","selected");
                chks.prop("checked",true).change();
            }
        });

    });
    //提交
    function submitHandler() {
        var receivableAmount = parseFloat($("#receivableAmount").val());//本次收款金额
        if(receivableAmount>quota){
            $.modal.alertError("本次收款金额不能大于可申请额度");
            return ;
        }
        if ($.validate.form()) {
            //遍历选中的委托单
            var receBillingIds = [];
            $("input[name='rmReq']").each(function(){
                if($(this).is(":checked")){
                    receBillingIds.push($(this).val());
                }
            });
            // if(receBillingIds.length == 0){
            //     $.modal.msgWarning("请勾选开票信息！");
            //     return false;
            // }
            var data = $('#form-batchRece-add').serializeArray();

            data.push({"name": "params[receBillingIds]", "value": receBillingIds.join()});
            $.modal.confirm("是否确定核销",function removeUser() {
                $.operate.saveTab(prefix + "/saveBatchRece", data);
            });
        }
    }

    /**
     * 展示开票对应的收款记录
     * @param receBillingId
     */
    function detail(receBillingId) {
        layer.open({
            type: 2,
            area: ['60%', '60%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "开票收款记录",
            content: ctx + "receSheetRecord/record_billing/" + receBillingId,
            btn: ['关闭'],
            shadeClose: true,            // 弹层外区域关闭
            btn3: function (index, layero) {
                return true;
            }
        });
    }

    /**
     * 关键字提示查询 转入账户
     */
    $("#accountName").bsSuggest('init', {
        url: ctx + "finance/account/findAccount?paymentType=0&keyword=",
        indexId: 0,
        showBtn: false,
        allowNoKeyword: false,
        getDataMethod: "url",
        keyField: "accountName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        effectiveFields: ["accountName"],
        delay: 300,
        searchingTip: '搜索中...',
        hideOnSelect: true,
        maxOptionCount: 10,
        inputWarnColor: '',
    }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        $("#accountName").val(data.accountName);
        $("#inAccount").val(data.accountId);
    });



</script>
</body>

</html>