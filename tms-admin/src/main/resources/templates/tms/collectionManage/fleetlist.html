<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收款申请管理')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" name="isFleetData" id="isFleetData" th:value="${isFleetData}">
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">单据号：</label>-->
                            <div class="col-sm-12">
                                <input name="vbillno" class="form-control" th:value="${vbillno}" type="text" placeholder="请输入单据号"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">客户名称：</label>-->
                            <div class="col-sm-12">
                                <input name="customerId" class="form-control" type="text" placeholder="请输入客户名称"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">结算客户：</label>-->
                            <div class="col-sm-12">
                                <input name="balaCustomer" class="form-control" type="text" placeholder="请输入结算客户"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">对账单号：</label>-->
                            <div class="col-sm-12">
                                <input name="reconNumber" class="form-control" type="text" placeholder="请输入对账单号"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">单据状态：</label>-->
                            <div class="col-sm-12">
                                <select name="status" id="status" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="单据状态" multiple >
                                    <option th:each="dict : ${vbillstatus}" th:text="${dict.context}" th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <!--<div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="billingPayable" class="form-control"
                                       type="text" placeholder="请输入发票抬头"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>-->
                </div>
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">是否开票：</label>-->
                            <div class="col-sm-12">
                                <select name="isCheck" class="form-control" th:with="type=${@dict.getType('if_billing')}">
                                    <option value="">--是否开票--</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"  th:value="${dict.dictValue}" ></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">申请人：</label>-->
                            <div class="col-sm-12">
                                <input name="applyUser" class="form-control" type="text" placeholder="请输入申请人"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">运营组：</label>-->
                            <div class="col-sm-12">
                                <select name="salesDept" id="salesDept" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="运营组" multiple>
                                    <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1 col-sm-1">
                    </div>
                    <div class="col-md-2 col-sm4">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary multiple disabled" onclick="check()" shiro:hasPermission="finance:receSheetRecord:check">
                <i class="fa fa-check"></i>审核
            </a>
            <a class="btn btn-primary single disabled" onclick="checkReply()" shiro:hasPermission="finance:receSheetRecord:checkReply">
                <i class="fa fa-reply"></i>撤销审核通过
            </a>
            <!--<a class="btn btn-primary multiple disabled" onclick="batchRece()" shiro:hasPermission="finance:receRecord:batchRece">
                <i class="fa fa-file-text-o"></i> 分批收款
            </a>-->
            <a class="btn btn-primary single disabled" onclick="receRecord()" shiro:hasPermission="finance:receRecord:batchRece">
                <i class="fa fa-calculator"></i> 收款记录
            </a>
<!--            <a class="btn btn-primary single  disabled" onclick="openSheet()" shiro:hasPermission="finance:receSheetRecord:check">-->
<!--                <i class="fa fa-check"></i>查看对账单-->
<!--            </a>-->
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "receSheetRecord";
    //收款方式
    var receivable_method = [[${@dict.getType('receivable_method')}]];
    //发票类型
    var billing_type = [[${@dict.getType('billing_type')}]];
    //公司名称
    var bala_corp = [[${@dict.getType('bala_corp')}]];
    //是否开票
    var if_billing = [[${@dict.getType('if_billing')}]];
    //收款单据状态
    var vbillstatus = [[${vbillstatus}]];

    var transFeeCount = 0;
    var gotAmountCount = 0;
    var ungotAmountCount = 0;
    var sumOilApplicationAmount = 0;
    var sumCashApplicationAmount = 0;

    //初始化查询条件传参
    queryParams = function(params) {
        var search = {};
        $.each($("#role-form").serializeArray(), function(i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            queryParams: queryParams,
            showToggle: false,
            showColumns: true,
            modalName: "应收汇总",
            fixedColumns: true,
            fixedNumber:4,
            clickToSelect:true,
            height: 620,
            showFooter:true,
            showExport: true,
            exportTypes:['excel','csv'],
            exportOptions:{
                ignoreColumn: [0, 1],
                fileName:"收款申请"
            },
            onPostBody:function () {
                //合并页脚
                merge_footer();
            },
            onRefresh:function(params){
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                sumOilApplicationAmount = 0;//开票金额
                sumCashApplicationAmount = 0;//现金金额
            },
            onCheck: function (row,$element) {
                var transFee = row.receivableAmount;
                var gotAmount = row.gotAmount;
                var ungotAmount = row.ungotAmount;
                var oilApplicationAmount = row.oilApplicationAmount;
                var cashApplicationAmount = row.cashApplicationAmount;
                //总数加上本行数值
                transFeeCount = transFeeCount + transFee;
                gotAmountCount = gotAmountCount + gotAmount;
                ungotAmountCount = ungotAmountCount + ungotAmount;
                sumOilApplicationAmount = sumOilApplicationAmount + oilApplicationAmount;//申请开票金额
                sumCashApplicationAmount = sumCashApplicationAmount + cashApplicationAmount;//开票金额
                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#sumOilApplicationAmount").text(sumOilApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#sumCashApplicationAmount").text(sumCashApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheck: function (row, $element) {
                var transFee = row.receivableAmount;
                var gotAmount = row.gotAmount;
                var ungotAmount = row.ungotAmount;
                //总数减去本行数值
                transFeeCount = transFeeCount - transFee;
                gotAmountCount = gotAmountCount - gotAmount;
                ungotAmountCount = ungotAmountCount - ungotAmount;
                sumOilApplicationAmount = sumOilApplicationAmount - row.oilApplicationAmount;//申请开票金额
                sumCashApplicationAmount = sumCashApplicationAmount - row.cashApplicationAmount;//开票金额

                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#sumOilApplicationAmount").text(sumOilApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#sumCashApplicationAmount").text(sumCashApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onCheckAll: function (rowsAfter) {
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                sumOilApplicationAmount = 0;//开票金额
                sumCashApplicationAmount = 0;//现金金额
                //循环累加
                for (var row of rowsAfter) {
                    transFeeCount = transFeeCount + row.receivableAmount;
                    gotAmountCount = gotAmountCount + row.gotAmount;
                    ungotAmountCount = ungotAmountCount + row.ungotAmount;
                    sumOilApplicationAmount = sumOilApplicationAmount + row.oilApplicationAmount;//申请开票金额
                    sumCashApplicationAmount = sumCashApplicationAmount + row.cashApplicationAmount;//开票金额
                }
                //赋值
                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#sumOilApplicationAmount").text(sumOilApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#sumCashApplicationAmount").text(sumCashApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheckAll: function () {
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                sumOilApplicationAmount = 0;//开票金额
                sumCashApplicationAmount = 0;//现金金额
                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#sumOilApplicationAmount").text(sumOilApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#sumCashApplicationAmount").text(sumCashApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onPostBody: function () {
                //合并页脚
                merge_footer();
                //计算总合计
                getAmountCount();
            },
            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "总金额：<nobr id='transFeeCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "已收金额：<nobr id='gotAmountCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "未收金额：<nobr id='ungotAmountCountTotal'>￥0</nobr>&nbsp&nbsp"+
                        "开票金额:<nobr id='sumOilApplicationAmount'>￥0</nobr>&nbsp&nbsp"+
                        "现金金额:<nobr id='sumCashApplicationAmount'>￥0</nobr><br>"+
                        "总合计:总金额：<nobr id='transFeeCountTotalAll'>￥0</nobr>&nbsp&nbsp" +
                        "已收金额：<nobr id='gotAmountCountTotalAll'>￥0</nobr>&nbsp&nbsp" +
                        "未收金额：<nobr id='ungotAmountCountTotalAll'>￥0</nobr>&nbsp&nbsp"+
                        "开票金额：<nobr id='sumOilApplicationAmountTotal'>￥0</nobr>&nbsp&nbsp"+
                        "现金金额：<nobr id='sumCashApplicationAmountTotal'>￥0</nobr>&nbsp&nbsp";
                }
            },{title: '操作',align: 'left',field: 'receSheetRecordId',
                formatter: function (value, row, index) {
                    var actions = [];
                    if ([[${@permission.hasPermi('tms:checkDetail:list')}]] != "hidden") {
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="开票明细" onclick="checkDetail(\'' + value + '\',\'' + row.vbillstatus + '\',\'' + row.treceCheckSheetId + '\')"><i  class="fa fa-dollar" style="font-size: 15px;" ></i></a>');
                    }
                    if ([[${@permission.hasPermi('finance:receSheetRecord:reexamine')}]] != "hidden") {
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="复核" onclick="reexamine(\'' + value + '\',\'' + row.vbillstatus + '\',\'' + row.treceCheckSheetId + '\',\'' + row.receiveDetailId + '\')"><i  class="fa fa-calculator" style="font-size: 15px;" ></i></a>');
                    }
                    /*if (row.treceCheckSheetId != null) {
                        if ([[${@permission.hasPermi('finance:receSheetRecord:receiveList')}]] != "hidden") {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="应收明细" onclick="receive(\'' + row.treceCheckSheetId + '\',\'' + 1 +'\')"><i  class="fa fa fa-list" style="font-size: 15px;" ></i></a>');
                        }
                    }*/
                    return actions.join('');
                }
            },
                {field: 'vbillno',title: '单据号',align: 'left'},
                {field: 'balaCustomer',title: '结算客户',align: 'left'},
                {field: 'reconNumber',title: '对账单号/应收明细号',align: 'left',
                    formatter: function status(value,row) {
                        return $.table.tooltip(value);
                    }},
                /*{field: 'checkSheetName',title: '对账名称',align: 'left',
                    formatter: function status(value,row) {
                        return $.table.tooltip(value);
                    }},*/
                {field: 'vbillstatus',title: '单据状态',align: 'left',
                    formatter: function status(value,row) {
                        switch (value) {
                            case 0:
                                return '<span class="label label-default">待审核</span>'
                            case 1:
                                return '<span class="label label-primary">审核通过</span>';
                            case 2:
                                return '<span class="label label-warning">审核未通过</span>';
                            case 3:
                                return '<span class="label label-info">部分收款 </label>';
                            case 4:
                                return '<span class="label label-success">已收款</span>';
                            default:
                                break;
                        }
                    }
                },
                {field: 'receivableAmount',halign: "center",title: '收款金额',align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {field: 'gotAmount',halign: "center",title: '已收款金额',align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {field: 'ungotAmount',halign: "center",title: '未收金额',align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {field: 'adjustAmount',halign: "center",title: '调整金额',align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '开票金额(元)',
                    field: 'oilApplicationAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '现金金额(元)',
                    field: 'cashApplicationAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                <!--{field: 'params.billingPayable',title: '发票抬头',align: 'left',},-->
                {field: 'receivableDate',title: '申请日期',align: 'left',},
                {field: 'applyUser',title: '申请人',align: 'left'},
                {field: 'receivableMethod',title: '收款方式',align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(receivable_method, value);
                    }

                },
                {field: 'isCheck',title: '是否选择开票',align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(if_billing, value);
                    }

                },
                {field: 'customerId',title: '客户名称',align: 'left'},
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });

    var date = new Date();
    var now = date .getFullYear() + "-" +("0" + (date.getMonth() + 1)).slice(-2);

    //审核
    function check() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        //获取状态
        var vbillstatus = $.btTable.bootstrapTable('getSelections');
        //循环判断是否能够进行审核
        for(var i=0;i<vbillstatus.length;i++){
            if(vbillstatus[i]['vbillstatus'] !== 0){
                $.modal.alertWarning("只能在待审核状态下才能进行审核");
                return false;
            }
        }

        var receSheetRecordId = $.table.selectColumns('receSheetRecordId');

        var flag = false;
        layer.confirm("请审核选中的" + rows.length + "条数据",{
            btn:["通过","不通过"]
        },function (index, layero) {
            if(!flag){
                flag = true;
                layer.close(index);
                var url = prefix+"/check/1/"+receSheetRecordId;
                $.operate.submit(url, "post", "json");
            }
        },function (index) {
            if(!flag){
                flag = true;
                layer.close(index);
                var url = prefix+"/check/2/"+receSheetRecordId;
                $.operate.submit(url, "post", "json");
            }
        });
    }

    /**
     * 分批收款 相同结算客户，状态：部分收款，审核通过
     */
    /*function batchRece(){
        //选中的行
        var rows = $.btTable.bootstrapTable('getSelections');
        //结算客户id
        var balaCustomerId=rows[0]["balaCustomerId"];
        for(var i = 0;i<rows.length;i++){
            if (rows[i]["vbillstatus"] !== 1 && rows[i]["vbillstatus"] !== 3) {
                $.modal.alertWarning("请选择审核通过/部分收款的申请单");
                return;
            }
            if(balaCustomerId !== rows[i]["balaCustomerId"]){
                $.modal.alertWarning("请选择相同结算客户的申请单");
                return;
            }
        }

        var ids = $.table.selectColumns('receSheetRecordId').join();
        var url = prefix + "/batchRece/"+ids;
        // $.modal.open('分批收款',url);
        $.modal.openTab("分批收款", url );

    }*/

    //收款记录
    function receRecord(){
        var id = $.table.selectColumns('receSheetRecordId');
        var url = prefix + "/receRecord/"+id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "收款记录",
            area: ['800px', '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 复核
     */
    function reexamine(receSheetRecordId,vbillstatus,tReceCheckSheetId,receiveDetailId) {
        tReceCheckSheetId = tReceCheckSheetId == 'null' ? "" : tReceCheckSheetId;
        receiveDetailId = receiveDetailId == 'null' ? "" : receiveDetailId;
        var url = prefix + "/reexamine?receSheetRecordId=" + receSheetRecordId+"&vbillstatus="
            +vbillstatus+"&tReceCheckSheetId="+tReceCheckSheetId+"&receiveDetailId="+receiveDetailId;
        $.modal.openTab("客户相关信息", url);
    }


    /**
     * 撤销审核通过 未付款、未开票
     */
    function checkReply(){
        var receSheetRecordId = $.table.selectColumns('receSheetRecordId').join();//id
        var vbillstatus = $.table.selectColumns('vbillstatus');//单据状态
        var data = {};
        data.receSheetRecordId = receSheetRecordId;
        var url = ctx+"finance/receBilling/countReceBilling";
        //判断开票金额
        var oilApplicationAmount = $.table.selectColumns('oilApplicationAmount');
        if(vbillstatus == 1){
            if(oilApplicationAmount !=0){
                $.ajax({
                    url: url,
                    data: data,
                    type:"post",
                    success:function(result){
                        if(result.code == 0){
                            $.modal.alertError("该申请单存在开票信息");
                            return ;
                        }else{
                            $.modal.open("撤销审核通过", prefix + "/checkReply?receSheetRecordId="+receSheetRecordId,500,300);
                        }
                    }
                });
            }else{
                $.modal.open("撤销审核通过", prefix + "/checkReply?receSheetRecordId="+receSheetRecordId,500,300);
            }
        }else{
            $.modal.alertError("只有审核通过状态才能撤销审核通过");
            return ;
        }

    }

    // 跳转对应的应收明细页面
    function receive(receCheckSheetId,vbillstatus) {
        var url = ctx + "receCheckSheet/receive?receCheckSheetId="+receCheckSheetId + "&vbillstatus=" + vbillstatus;
        $.modal.openTab('应收明细',url);
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 开票明细
     */
    function checkDetail(id){
        var url = ctx + "finance/receBilling/checkDetail/"+id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "开票明细",
            area: ['1200px', '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });

    }

    /**
     * 跳转客户对账单管理
     */
    function openSheet() {
        var receSheetRecordId = $.table.selectColumns("receSheetRecordId");
        $.modal.openTab("客户对账单管理", ctx + "receCheckSheet?receSheetRecordId=" + receSheetRecordId);
    }

    /**
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        data.params = new Map();
        data.status = $.common.join($('#status').selectpicker('val'));//单据状态
        data.isFleetData = $("#isFleetData").val()
        $.ajax({
            url: ctx + "receSheetRecord/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result.data;

                if (result.code == 0 && data != undefined) {
                    //总金额
                    $("#transFeeCountTotalAll").text(data.TRANSFEECOUNTTOTALALL.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //已收金额
                    $("#gotAmountCountTotalAll").text(data.GOTAMOUNTCOUNTTOTALALL.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //未收金额
                    $("#ungotAmountCountTotalAll").text(data.UNGOTAMOUNTCOUNTTOTALALL.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //开票金额
                    $("#sumOilApplicationAmountTotal").text(data.TOTALOILAPPLICATIONAMOUNTALL.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //现金金额
                    $("#sumCashApplicationAmountTotal").text(data.TOTALCASHAPPLICATIONAMOUNTALL.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        data.status = $.common.join($('#status').selectpicker('val'));//单据状态
        data.salesDept = $.common.join($('#salesDept').selectpicker('val'));
        //data.checkType = $.common.join($('#checkType').selectpicker('val'));//发票类型
        $.table.search('role-form', data);
    }

</script>


</body>
</html>