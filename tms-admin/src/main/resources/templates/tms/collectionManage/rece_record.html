<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收款申请-收款记录')"/>
    <style>
        .bootstrap-table .fixed-table-container {
            height: calc(100% - 70px) !important;
        }
        .table-striped {
            height: calc(100% - 25px);
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped" >
            <input name="receiveDetailId" id="receiveDetailId" type="hidden" th:value="${receiveDetailId}">

            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    var prefix = ctx + "receSheetRecord";
   //收款申请id
    var receSheetRecordId = [[${receSheetRecordId}]];
    var url = prefix + "/receRecordList/"+receSheetRecordId;
    // 应收明细ID
    var receiveDetailId = $("#receiveDetailId").val();
    if (receiveDetailId !== null && receiveDetailId !== ''){
        url = prefix + "/receRecordList?receiveDetailId="+receiveDetailId;
    }
    var receivable_method = [[${@dict.getType('receivable_method')}]];//收款方式
    var receivable_type = [[${@dict.getType('receivable_type')}]];//收款类型

    $(function () {
        var options = {
            url: url,
            showToggle:false,
            showColumns:false,
            showSearch: false,
            showRefresh:false,
            modalName: "收款记录",
            height: 560,
            clickToSelect:true,
            columns: [
                {
                    title: '操作',
                    field: 'receRecordId',
                    formatter: function (value, row, index) {
                        var actions = [];
                        /*[# shiro:hasPermission="tms:receBilling:cancelRece"]*/
                        if (row.red == 0 && !row.redReceRecordId) {
                            actions.push('<a class="btn btn-xs" href="javascript:;" title="撤销收款" onclick="cancelRece(\'', value, '\')"><i class="fa fa-remove" style="font-size: 15px;"></i></a>');
                        } else if (row.red == 1) {
                            actions.push('<span class=\'fa fa-question-circle\' style=\'color:red\' data-toggle=\'tooltip\' title=\'红冲数据\'>')
                        } else {
                            actions.push('<span class=\'fa fa-question-circle\' style=\'color:blue\' data-toggle=\'tooltip\' title=\'已被红冲\'>')
                        }
                        /*[/]*/
                        return actions.join('');
                    }
                },
                {
                    title: '单据号',
                    field: 'vbillno',
                    align: 'left'
                },
                {
                    title: '收款日期',
                    field: 'receivableDate',
                    align: 'left'
                },
                {
                    title: '收款类型',
                    field: 'receivableType',
                    align: 'left',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(receivable_type,value);
                    }
                },

                {
                    title: '收款方式',
                    field: 'receivableMethod',
                    align: 'left',
                    formatter: function(value, row, index) {
                        let s = $.table.selectDictLabel(receivable_method, value);
                        let ticketNumber = ''
                        if (row.ticketNumber) {
                            ticketNumber = `(${row.ticketNumber})`
                        }
                        return s + ticketNumber;
                    }
                },
                {
                    title: '收款金额(元)',
                    halign: "center",
                    field: 'receivableAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '备注',
                    field: 'memo',
                    align: 'left'
                },
            ]
        };

        $.table.init(options);
    });

    function cancelRece(receRecordId) {
        $.modal.confirm("确定撤销该收款吗？", function(){
            $.operate.post(prefix + "/cancelReceRecord", "receRecordId=" + receRecordId, function(){
                parent.$.table.refresh();
            });

            /*$.ajax({
                url: prefix + "/cancelReceRecord",
                data: "receRecordId=" + receRecordId,
                success: function(result) {
                    console.log(result)
                }
            })*/
        })
    }
</script>

</body>
</html>