<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('开票')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-receiptAppl-add" class="form-horizontal" novalidate="novalidate">

        <input  class="form-control" th:value="${receSheetRecord.receSheetRecordId}" name="receSheetRecordId" type="hidden">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <input name="deptId" type="hidden" id="treeId">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">申请金额：</label>
                                    <div class="col-sm-8">
                                        <!--申请金额-->
                                        <input  class="form-control" th:value="${receSheetRecord.receivableAmount}" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">申请日期：</label>
                                    <div class="col-sm-8">
                                        <input name="receivableDate" id="receivableDate" class="form-control"
                                               th:value="${#dates.format(receSheetRecord.receivableDate, 'yyyy-MM-dd HH:mm:ss')}" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款方式：</label>
                                    <div class="col-sm-8">
                                        <select name="receivableMethod" class="form-control" th:with="type=${@dict.getType('receivable_method')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:field="${receSheetRecord.receivableMethod}" th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">转入账户：</label>
                                    <div class="col-sm-8">
                                        <select name="inAccount" class="form-control">
                                            <option value=""></option>
                                            <option th:each="dict : ${account}" th:text="${dict.accountName}" th:field="${receSheetRecord.inAccount}" th:value="${dict.accountId}" ></option>
                                        </select>
                                        <!--<div class="input-group">
                                            <input onclick="selectAccount()" id="accountName" type="text" class="form-control" required>
                                            <input name="inAccount"  id="accountId" type="hidden" class="form-control">
                                            <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                        </div>-->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkAmount" style="color: red">开票金额：</label>
                                    <div class="col-sm-8">
                                        <input type="text" oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateTotals()"  th:field="${receSheetRecord.checkAmount}"
                                               id="checkAmount" name="checkAmount" maxlength="15" class="form-control" required>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">是否开票：</label>
                                    <div class="col-sm-8">
                                        <select id="isCheck" name="isCheck" class="form-control" required>
                                            <option value="0">是</option>
                                            <option value="1">否</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkDate" style="color: red">开票日期：</label>
                                    <div class="col-sm-8">
                                        <input type="text"  name="checkDate" id="checkDate" class="form-control"
                                               required readonly th:value="${#dates.format(receSheetRecord.checkDate, 'yyyy-MM-dd HH:mm:ss')}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkNo" style="color: red">发票号：</label>
                                    <div class="col-sm-8">
                                        <input type="text"  name="checkNo" id="checkNo" maxlength="25"
                                               th:field="${receSheetRecord.checkNo}" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkHead" style="color: red" >发票抬头：</label>
                                    <div class="col-sm-8">
                                        <input type="text"  name="checkHead" id="checkHead" th:field="${receSheetRecord.checkHead}"
                                               maxlength="250" class="form-control"  required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkCorp" style="color: red">开票公司：</label>
                                    <div class="col-sm-8">
                                        <select  name="checkCorp" id="checkCorp" class="form-control valid" required th:with="type=${@dict.getType('bala_corp')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:field="${receSheetRecord.checkCorp}"
                                                    th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkType" style="color: red">发票类型：</label>
                                    <div class="col-sm-8">
                                        <select name="checkType" id="checkType" class="form-control valid" th:with="type=${@dict.getType('billing_type')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:field="${receSheetRecord.checkType}" th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkType" style="color: red">开户行：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="bank" id="bank" maxlength="125" th:value="${receSheetRecord.bank}"
                                               class="form-control"required>
                                    </div>
                                </div>
                            </div>



                        </div>


                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 isCheck" style="color: red">账号：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="bankAccount" th:value="${receSheetRecord.bankAccount}" maxlength="25"
                                               class="form-control isCheckVal" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 isCheck" style="color: red">纳税人识别号：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="taxIdentify" id="taxIdentify" maxlength="25"
                                               th:value="${receSheetRecord.taxIdentify}" class="form-control isCheckVal"  required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" >待收：</label>
                                    <div class="col-sm-8">
                                        <input type="text" oninput="$.numberUtil.onlyNumber(this);calculateTotals()" name="income"
                                               maxlength="15" class="form-control" th:field="${receSheetRecord.income}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" >待支：</label>
                                    <div class="col-sm-8">
                                        <input type="text" oninput="$.numberUtil.onlyNumber(this);calculateTotals()" name="expend"
                                               maxlength="15" class="form-control" th:field="${receSheetRecord.expend}">
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-2" style="color: red">地址及电话：</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="address"
                                               maxlength="105" class="form-control" th:field="${receSheetRecord.address}">
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2 checkRemark" style="color: red">发票备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="checkRemark" maxlength="250" class="form-control valid"
                                                      rows="3" th:field="${receSheetRecord.checkRemark}" required></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="memo" maxlength="250" class="form-control valid"
                                                      rows="3" th:field="${receSheetRecord.memo}"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">发票：</label>
                                    <div class="col-sm-4">
                                        <input name="checkAppendix" id="checkAppendix" class="form-control" type="file" multiple>
                                        <input type="hidden" id="checkAppendixId" name="checkAppendixId" th:field="${receSheetRecord.checkAppendixId}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()" th:if="${receSheetRecord.isCheckSuccess == 1}">
            <i class="fa fa-check"></i>保 存
        </button>
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "receSheetRecord";
    //图片
    var sysUploadFiles = [[${sysUploadFiles}]];

    $(function () {
        $('#collapseOne').collapse('show');
        /**
         * 表单校验
         */
        $("#form-receiptAppl-add").validate({
            focusCleanup: true
        });

        /**
         * 初始化图片
         */
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: 'file',
        };
        $.file.loadEditFiles("checkAppendix", "checkAppendixId",sysUploadFiles,picParam);

        /**
         * 根据是否开票 否：开票日期，发票抬头，开票公司，发票类型,开票金额 不必填
         */
        $('#isCheck').change(function(){
            var isCheck  = $(this).find(":selected").text();
            if(isCheck === '否'){
                //开票日期
                $('.checkDate').css("color","inherit");
                $("#checkDate").removeAttr("required");
                $("#form-receiptAppl-add").validate().element($("#checkDate"));
                //发票抬头
                $('.checkHead').css("color","inherit");
                $("#checkHead").removeAttr("required");
                $("#form-receiptAppl-add").validate().element($("#checkHead"));
                //开票公司
                $('.checkCorp').css("color","inherit");
                $("#checkCorp").removeAttr("required");
                $("#form-receiptAppl-add").validate().element($("#checkCorp"));
                //发票类型
                $('.checkType').css("color","inherit");
                $("#checkType").removeAttr("required");
                $("#form-receiptAppl-add").validate().element($("#checkType"));
                //开票金额
                $('.checkAmount').css("color","inherit");
                $("#checkAmount").removeAttr("required");
                $("#form-receiptAppl-add").validate().element($("#checkAmount"));
                //发票备注
                $('.checkRemark').css("color","inherit");
                $("#checkRemark").removeAttr("required");
                $("#form-receiptAppl-add").validate().element($("#checkRemark"));
                //发票号
                $('.checkNo').css("color","inherit");
                $("#checkNo").removeAttr("required");
                $("#form-receiptAppl-add").validate().element($("#checkNo"));

            }
            if(isCheck === '是'){
                //开票日期
                $('.checkDate').css("color","red");
                $("#checkDate").attr("required","true");
                //发票抬头
                $('.checkHead').css("color","red");
                $("#checkHead").attr("required","true");
                //开票公司
                $('.checkCorp').css("color","red");
                $("#checkCorp").attr("required","true");
                //发票类型
                $('.checkType').css("color","red");
                $("#checkType").attr("required","true");
                //开票金额
                $('.checkAmount').css("color","red");
                $("#checkAmount").attr("required","true");
                //发票备注
                $('.checkRemark').css("color","red");
                $("#checkRemark").attr("required","true");
                //发票号
                $('.checkNo').css("color","red");
                $("#checkNo").attr("required","true");
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#checkDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#receivableDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
    });

    //提交
    function submitHandler() {
        if ($.validate.form()) {
            $('#checkAppendix').fileinput('upload');
            jQuery.subscribe("cmt", commit);
        }
    }

    function commit(){
        $.operate.saveTab(prefix + "/saveCheckAppl", $('#form-receiptAppl-add').serialize());
    }





</script>
</body>
</html>