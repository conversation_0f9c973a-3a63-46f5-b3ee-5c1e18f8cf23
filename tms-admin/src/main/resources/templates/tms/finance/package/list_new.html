<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('打包对账')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse,.f7f7{
        background-color: #F7F7F7;
    }
    .table-striped {
        height: calc(100% - 78px);
    }
    .taCl thead{
        background-color: #F7F8FA !important;
    }
    .table-responsive{
        width: 90vw;
        margin-left: 26px;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
        min-width: 16px;
    }
    .label-successT {
        color: #1c84c6;
        background-color: transparent;
        border: 1px solid #1c84c6;
    }
    .tooltip-inner{
        /* background: transparent !important; 
        text-align: left !important;
        color:#363636 !important;
        border:1px solid transparent; */
        max-width: 700px !important;
    }

    .ontooltip {
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        background-color: #FFFFFF;
        color: #000;
        text-align: left;
        margin: 4px 0;
    }
    .label-errorT{
        color: #1c84c6;
        background-color: yellow;
        border: 1px solid #1c84c6;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">运单号：</label>-->
                            <div class="col-sm-12">
                                <input name="lotno" id="lotno" class="form-control"
                                       placeholder="请输入运单号" maxlength="30">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">应付单状态：</label>-->
                            <div class="col-sm-12">
                                <select name="status" id="status" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="应付单状态" multiple>
                                    <option th:each="dict : ${payDetailStatusEnum}" th:text="${dict.context}"
                                            th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>


                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">承运商名称：</label>-->
                            <div class="col-sm-12">
                                <input name="carrName" id="carrName" placeholder="请输入承运商名称" class="form-control valid">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">司机名称：</label>-->
                            <div class="col-sm-6">
                                <input name="driverName" id="driverName" class="form-control" placeholder="请输入司机名称"
                                       maxlength="30">
                            </div>
                            <div class="col-sm-6">
                                <input name="carno" id="carno" class="form-control" placeholder="请输入车牌号"
                                       maxlength="30">
                            </div>
                        </div>
                    </div>

                     <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="transType" id="transType" class="form-control valid" th:with="type=${@dict.getType('trans_code')}">
                                    <option value="">-- 运输方式 --</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <!-- <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="regUserName" id="regUserName" class="form-control"
                                       placeholder="请输入调度人" maxlength="25">
                            </div>
                        </div>
                    </div> -->
                </div>

                <div class="row">

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">发货单号：</label>-->
                            <div class="col-sm-12">
                                <input name="invoiceVbillno"  class="form-control"
                                       placeholder="请输入发货单号" maxlength="25">
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">客户简称：</label>-->
                            <div class="col-sm-12">
                                <input name="custAbbr"  class="form-control"
                                       placeholder="请输入客户简称" maxlength="25">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">运营组：</label>-->
                            <div class="col-sm-12">
                                <!--<input name="salesDeptName" id="salesDeptName" placeholder="请输入运营组" class="form-control valid">-->
                                <select name="salesDeptName" id="salesDeptName" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="运营组" multiple>
                                    <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-3">要求提货日期：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 47%; float: left;" class="form-control"
                                       id="reqDeliDateStart"  name="reqDeliDateStart" placeholder="要求提货日期开始">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:6%;">-</span>
                                <input type="text" style="width: 47%; float: left;" class="form-control"
                                       id="reqDeliDateEnd"  name="reqDeliDateEnd" placeholder="要求提货日期结束">
                            </div>
                        </div>
                    </div>

                   

                </div>

                <div class="row">
                    <div class="col-md-5 col-sm-5">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <div class="col-sm-4">
                                    <select name="deliProvince" id="deliProvince" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="deliCity" id="deliCity" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="deliArea" id="deliArea" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-5 col-sm-5">
                        <div class="form-group">
                            <div class="col-sm-2" onclick="changeDiv()">
                                <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                            </div>
                            <div class="col-sm-10">
                                <div class="col-sm-4">
                                    <select name="arriProvince" id="arriProvince"
                                            class="form-control valid"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriCity" id="arriCity" class="form-control valid"></select>
                                </div>

                                <div class="col-sm-4">
                                    <select name="arriArea" id="arriArea" class="form-control valid"></select>
                                </div>
                            </div>

                        </div>
                    </div>
                   
                     <div class="col-md-2 col-sm-2">
                        <div class="form-group" style="text-align: center">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>




                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary multiple disabled" onclick="checking()" shiro:hasPermission="tms:package:checking">
                <i class="fa fa-file-text-o"></i> 生成对账单
            </a>
            <a class="btn btn-primary multiple disabled" onclick="insertChecking()" shiro:hasPermission="tms:package:join">
                <i class="fa fa-file-text-o"></i> 加入对账单
            </a>

            <a class="btn btn-primary single disabled" onclick="showChecking()" shiro:hasPermission="tms:package:showCheking">
                <i class="fa fa-file-text-o"></i> 查看对账单
            </a>
            <a class="btn btn-primary  single disabled" onclick="detailTab()" shiro:hasAnyPermissions="trace:payReconciliation:detailTab,fleet:trace:payReconciliation:detailTab">
                <i class="fa fa-newspaper-o"></i> 费用明细
            </a>
            <a class="btn btn-primary " onclick="ltlData()">
                <i class="fa fa-newspaper-o"></i> 零担调度明细导出
            </a>

        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />

<script id="ltlDataHtml" type="text/template">
    <div class="form-content">
        <div class="row" >
            <div class="col-md-12 col-sm-12">
                <div class="form-group" style="margin-bottom: 35px;">
                    <label class="col-sm-3">
                        时间范围：
                    </label>
                    <div class="col-sm-9">
                        <div id="ltlDate">
                            <input type="text" style="width: 47%; float: left;" class="form-control" id="dateStart" name="reqDeliDateStart" placeholder="要求提货日期开始" readonly>
                            <span style="display: block; line-height: 20px; float: left; text-align:center; width:6%;">-</span>
                            <input type="text" style="width: 47%; float: left;" class="form-control" id="dateEnd" name="reqDeliDateEnd" placeholder="要求提货日期结束" readonly>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row" >
            <div class="col-md-12 col-sm-12">
                <div class="form-group" style="margin-bottom: 35px;">
                    <label class="col-sm-3">
                        客户简称：
                    </label>
                    <div class="col-sm-9">
                        <input id="custAbbr" name="custAbbr" class="form-control" type="text"/>
                    </div>
                </div>
            </div>
        </div>

        <div class="row" >
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-sm-3">
                        承运商：
                    </label>
                    <div class="col-sm-9">
                        <input id="shCarrName" name="shCarrName" class="form-control" type="text"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>
<script th:src="@{'/js/wecom.js'}"></script>
<script th:inline="javascript">
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var balatype = [[${@dict.getType('bala_type')}]];
    var prefix = ctx + "payDetail";
    var transCode = [[${@dict.getType('trans_code')}]];

    var payDetailStatusEnum = [[${payDetailStatusEnum}]];

    $('#status').selectpicker('val', [0,1,2]);

    var queryParams = function(params) {
        console.log(params)
        var search = {};
        //var search = $("#role-form").serializeObject();
        $.each($("#role-form").serializeArray(), function(i, field) {
            search[field.name] = field.value;
        });
        search["status"] = $.common.join($('#status').selectpicker('val'));
        search["salesDeptName"] = $.common.join($('#salesDeptName').selectpicker('val'));
        //search["costTypeFreight"] = $.common.join($('#costTypeFreight').selectpicker('val'));
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
        //var comName =null;
    }

    var transFeeCount = 0;
    var gotAmountCount = 0;
    var ungotAmountCount = 0;

    $(function () {
        $.provinces.init("deliProvince","deliCity","deliArea");
        $.provinces.init("arriProvince","arriCity","arriArea");

        var options = {
            url: ctx + "packagePayDetail/listx",
            createUrl: prefix + "/add?carrierId="+$("#carrierId").val(),
            queryParams: queryParams,
            showToggle: false,
            showColumns: true,
            modalName: "应付明细",
            fixedColumns: false,
            rememberSelected: false,
            fixedNumber: 0,
            height: 560,
            clickToSelect:true,
            showFooter:true,
            detailView: true,
            uniqueId: "lotId",
            onExpandRow: function (index, row, $detail) {
                InitSubTable(index, row, $detail);
            },
            onPostBody:function () {
                //查询合计总金额
                getAmountCount();
            },
            onRefresh:function(params){
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
            },
            onCheck: function (row,$element) {
                var transFee = row.transFeeCount;
                var gotAmount = row.gotAmount;
                var ungotAmount = row.ungotAmount;
                //总数加上本行数值
                transFeeCount = transFeeCount + transFee;
                gotAmountCount = gotAmountCount + gotAmount;
                ungotAmountCount = ungotAmountCount + ungotAmount;

                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheck: function (row, $element) {
                var transFee = row.transFeeCount;
                var gotAmount = row.gotAmount;
                var ungotAmount = row.ungotAmount;
                //总数减去本行数值
                transFeeCount = transFeeCount - transFee;
                gotAmountCount = gotAmountCount - gotAmount;
                ungotAmountCount = ungotAmountCount - ungotAmount;

                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onCheckAll: function (rowsAfter) {
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                //循环累加
                for (var row of rowsAfter) {
                    transFeeCount = transFeeCount + row.transFeeCount;
                    gotAmountCount = gotAmountCount + row.gotAmount;
                    ungotAmountCount = ungotAmountCount + row.ungotAmount;
                }
                //赋值
                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheckAll: function () {
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            columns: [
                {
                    title: '',
                    align: 'left',
                    field: 'entrustCt',
                    formatter: function status(value, row, index) {
                        return '<span class="label label-info pa2" data-toggle="tooltip" data-placement="top" data-container="body" data-html="true" title="共'+value+'条车载货源信息">'+value+'</span>';
                    }
                },

                {
                    valign : 'middle',
                    checkbox: true,
                    formatter:function(value,row,index){
                        if(row.driverCollectionCt > 0 || row.singleLock == '1'){
                            return {
                                disabled : true,
                            }
                        }else if((row.ifAllConfirm == '2' || row.lotVbillstatus == '7' || row.ifAllReceipt == 2) && [0,2,4].indexOf(row.spStatus) >= 0){
                            return {
                                disabled : false,
                            }
                        }else{
                            return {
                                disabled : true,
                            }
                        }
                    }
                },
                {
                    footerFormatter: function (row) {
                        return "总金额合计：<nobr id='transFeeCountTotal'>￥0</nobr>&nbsp&nbsp" +
                            "已付金额合计：<nobr id='gotAmountCountTotal'>￥0</nobr>&nbsp&nbsp" +
                            "未付金额合计：<nobr id='ungotAmountCountTotal'>￥0</nobr><br>"+
                        "总合计:总金额合计：<nobr id='sumTransFeeCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "已付金额合计：<nobr id='sumGotAmountCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "未付金额合计：<nobr id='sumUngotAmountCountTotal'>￥0</nobr>";
                    }
                },
                // {
                //     title: '操作',
                //     align: 'left',
                //     field: 'lotId',
                //     formatter: function (value, row, index) {
                //         var actions = [];
                //         actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="结算公司" onclick="balaCorp(\'' + value + '\',\'' + row.payDetailId + '\')"><i  class="fa fa-calculator" style="font-size: 15px;" ></i></a>');

                //         return actions.join('');
                //     }
                // },
                {
                    title: '客户简称',
                    align: 'left',
                    field: 'custAbbr',
                    formatter:function status(value, row) {
                        let html = '<span>'+value+'</span>'
                        if(row.lockPayCarrier == 1 ) {
                            html += '<span class="label label-danger ml5"  data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="' + row.lockPayReasonUnion + '">锁</span>'
                        }
                        //异常锁
                        for(let i = 0 ; i < row.entrustExpList.length; i++){
                            let item = row.entrustExpList[i];
                            if(item.lockPay == 1){
                                html += `<span class="label label-danger ml5" style="padding:2px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="异常应付锁：${item.handleNote}">锁</span>`
                            }
                            if(item.lockPay == 2){
                                html += `<span class="label label-danger ml5" style="padding:2px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="异常承运商锁：${item.handleNote}">锁</span>`
                            }
                            if(item.lockOtherFee == 1){
                                html += `<span class="label label-danger ml5" style="padding:2px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="异常三方锁：${item.handleNote}">锁</span>`
                            }
                        }
                        if(row.singleLock == '1'){
                            html += '<span class="label label-warning ml5"  data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="' + row.lockMemo + '">锁</span>'
                        }
                        if(row.isCheck == 1){
                            html += '<span class="label label-warning ml5"  data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="该单据正在审核中">审</span>'
                        }


                        let invoiceMemo = '';
                        if (row.invoiceMemo) {
                            let formatTooltip1 = formatTooltip(row.invoiceMemo);
                            invoiceMemo = `<span class="label label-success ml5 pa2" data-toggle="tooltip"
                                 data-container="body" data-placement="top" data-html="true"
                                 title="${formatTooltip1}">发备</span>`;
                        }
                        let lotMemo = ''
                        if (row.lotMemo) {
                            lotMemo = `<span class="label label-success ml5 pa2" data-toggle="tooltip"
                                             data-container="body" data-placement="top" data-html="true"
                                             title="${row.lotMemo}">运备</span>`
                        }


                        return html + invoiceMemo + lotMemo;
                    }
                },
                {
                    title: '回单',
                    align: 'left',
                    field: 'ifAllConfirm',
                    formatter: function status(value, row, index) {
                        let ifAllConfirm="",ifAllReceiptUpload="",ifAllReceipt='';
                        if(row.ifAllConfirm == 0){
                            ifAllConfirm = '<span class="label label-success">未确认</span>';
                        }else if(row.ifAllConfirm == 1){
                            ifAllConfirm = '<span class="label label-warning">部分确认</span>';
                        }else if(row.ifAllConfirm == 2){
                            ifAllConfirm = '<span class="label label-primary">已确认</span>';
                        }

                        if(row.ifAllReceiptUpload == 0){
                            ifAllReceiptUpload = '<span class="label label-success">未上传</span>';
                        }else if(row.ifAllReceiptUpload == 1){
                            ifAllReceiptUpload = '<span class="label label-warning">部分上传</span>';
                        }else if(row.ifAllReceiptUpload == 2){
                            ifAllReceiptUpload = '<span class="label label-primary">已上传</span>';
                        }

                        if(row.ifAllReceipt == 0){
                            ifAllReceipt = '<span class="label label-success">未正本</span>';
                        }else if(row.ifAllReceipt == 1){
                            ifAllReceipt = '<span class="label label-warning">部分正本</span>';
                        }else if(row.ifAllReceipt == 2){
                            ifAllReceipt = '<span class="label label-primary">已正本</span>';
                        }


                        // ifAllConfirm = row.ifAllConfirm==1?"部分确认":row.ifAllConfirm==2?"已确认":"未确认";
                        // ifAllReceiptUpload = row.ifAllReceiptUpload==1?"部分上传":row.ifAllReceiptUpload==2?"已上传":"未上传";
                        return ifAllConfirm+" "+ifAllReceiptUpload+" "+ifAllReceipt
                    }
                },

                {
                    title: '要求提货日期',
                    align: 'left',
                    field: 'entrustReqDeliDate'
                },
                {
                    title: '提货|到货省市区',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(row.deliAddr == null || row.deliAddr == ""){
                            row.deliAddr = '';
                        }
                        if(row.arriAddr == null || row.arriAddr.arriAddr == ""){
                            row.arriAddr = ''
                        }
                        if(row.deliAddr == "" && row.arriAddr == ""){
                            return "";
                        }else{
                            return row.deliAddr+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriAddr;
                        }

                    }
                },
                {
                    title: '货量',
                    align: 'left',
                    field: 'goodsName',
                    formatter: function (value, row, index) {
                        let html=[]
                        if(row.numCount != null && row.numCount != ""&& row.numCount != 0){
                            html.push(row.numCount+"件")
                        }
                        if(row.weightCount != null && row.weightCount != ""&& row.weightCount != 0){
                            html.push(row.weightCount+"吨")
                        }
                        if(row.volumeCount != null && row.volumeCount != ""&& row.volumeCount != 0){
                            html.push(row.volumeCount+"方")
                        }
                        return row.goodsName+html.join("|");
                    }
                },
                {
                    title: '承运商',
                    align: 'left',
                    field: 'carrName'
                },
                {
                    title: '税点类别',
                    align:'left',
                    field:'billingTypeLabel'
                },
               
                {
                    title: '运费',
                    align: 'right',
                    field: 'transFeeCount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        let t = ''

                        if (row.transFeeCash) {
                            t = '运费：' + row.transFeeCash.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
                        }
                        if (row.transFeeOil) {
                            if (t !== '') {
                                t = t + '<br>'
                            }

                            t = t + '油卡：' +row.transFeeOil.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }
                        t = '<div style="display: inline-block;">' + t + '</div>'

                        let freightFeeRateTooltip="";
                        if(row.freightFeeRate){
                            freightFeeRateTooltip=`<i class="fa fa-question-circle ml5" data-toggle="tooltip" style="font-size: 15px;display: inline-block;" data-html="true" title="运费加票点：`+row.freightFeeRate+`%"></i>`
                        }  
                                        
                        return t+freightFeeRateTooltip;
                    }
                },
                {
                    title: '已付金额',
                    align: 'right',
                    field: 'gotAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未付金额',
                    align: 'right',
                    field: 'ungotAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        if(value < 0){
                            let str = value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                            return `<span style="color: red">`+str+`</span>`;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '油卡比例',
                    align: 'left',
                    field: 'oilRatio',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value + '%';
                    }
                },
                {
                    title: '运输方式',
                    align: 'left',
                    field : 'transType',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(transCode, value);
                    }
                },
                {
                    title: '运单号',
                    field: 'lotno',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var result = value;
                        let auditStatusTxt = ['待提审','审核中','审核通过','审核失败'];
                        if (row.lotG7End == 2) {
                            let et = '';
                            if (row.g7LotQst) {
                                et = row.g7LotQst + "；"
                            }
                            if (row.g7CarExt && row.g7Corp) {
                                let g7CarExt = JSON.parse(row.g7CarExt);
                                if (g7CarExt.ysz) {
                                    let auditStatus = g7CarExt.ysz[row.g7Corp.toLowerCase()];
                                    if (auditStatus != 2 && auditStatus != null) {
                                        let _st_t = auditStatusTxt[auditStatus]
                                        et = et + "车辆道路运输证【" + _st_t + "】；";
                                    }
                                }
                            }
                            if (row.g7DriverExt && row.g7Corp) {
                                let g7DriverExt = JSON.parse(row.g7DriverExt);
                                if (g7DriverExt.zgz) {
                                    let auditStatus = g7DriverExt.zgz[row.g7Corp.toLowerCase()];
                                    if (auditStatus != 2 && auditStatus != null) {
                                        let _st_t = auditStatusTxt[auditStatus]
                                        et = et + "司机从业资格证【" + _st_t + "】；";
                                    }
                                }
                            }
                            result += ' <span class="label '+ (et?'label-errorT':'label-success') +'" style="padding:1px;vertical-align: middle;"';
                            if (et) {
                                result += ' data-toggle="tooltip" data-container="body" data-placement="right" data-html="true" title="' + et + '"';
                            }
                            result += '>G7</span>'
                        } else if (row.lotG7Syn != null) {
                            result += ' <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true" title="'
                            if (row.g7LotQst) {
                                result = result + row.g7LotQst + "；"
                            }
                            if (row.g7CarExt && row.g7Corp) {
                                let g7CarExt = JSON.parse(row.g7CarExt);
                                if (g7CarExt.ysz) {
                                    let auditStatus = g7CarExt.ysz[row.g7Corp.toLowerCase()];
                                    if (auditStatus != 2 && auditStatus != null) {
                                        let _st_t = auditStatusTxt[auditStatus]
                                        result = result + "车辆道路运输证【" + _st_t + "】；";
                                    }
                                }
                            }
                            if (row.g7DriverExt && row.g7Corp) {
                                let g7DriverExt = JSON.parse(row.g7DriverExt);
                                if (g7DriverExt.zgz) {
                                    let auditStatus = g7DriverExt.zgz[row.g7Corp.toLowerCase()];
                                    if (auditStatus != 2 && auditStatus != null) {
                                        let _st_t = auditStatusTxt[auditStatus]
                                        result = result + "司机从业资格证【" + _st_t + "】；";
                                    }
                                }
                            }
                            if (row.lotG7Syn == 0) {
                                result += '等待G7审验'
                            } else if (row.lotG7Syn == 1) {
                                result += row.lotG7Msg
                            } else if (row.lotG7Syn == 2) {
                                if (row.lotG7Start == null || row.lotG7Start == 0) {
                                    result += '等待推送【发车】'
                                } else if (row.lotG7Start == 1) {
                                    result += '【发车】推送失败'
                                } else if (row.lotG7End == null || row.lotG7End == 0) {
                                    result += '等待推送【到达】'
                                } else if (row.lotG7End == 1) {
                                    result += '【到达】推送失败'
                                }
                            } else if (row.lotG7Syn == 7) {
                                result += '运单已作废'
                            }
                            result += '">G7</span>'
                        } else if (row.payWay == 'Y') {
                            result += ' <span class="label label-warning" style="padding:1px 5px;">Y</span>'
                        }
                        return result
                    }

                },
                {
                    title: '应付单状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(value == 0){
                            return '<span class="label label-primary">新建</span>';
                        }else if(value == 1){
                            return '<span class="label label-warning">已确认</span>';
                        }else if(value == 2){
                            return '<span class="label label-success">已对账</span>';
                        }else if(value == 3){
                            return '<span class="label label-info">部分核销</span>';
                        }else if(value == 4){
                            return '<span class="label label-info">已核销</span>';
                        }else if(value == 5){
                            return '<span class="label label-default">关闭</span>';
                        }else if(value == 6){
                            return '<span class="label label-success">已申请</span>';
                        }else if(value == 7){
                            return '<span class="label label-danger">核销中</span>';
                        } else if (value==8) {
                            return '<span class="label label-info">审核中</span>';
                        } else if (value==9) {
                            return '<span class="label label-success">复核通过</span>';
                        }
                    }
                },
               
                {
                    title: '发货单号',
                    align: 'left',
                    field: 'invoiceVbillno',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.invoiceVbillno);
                    }
                },
               
                {
                    title: '运营组',
                    align: 'left',
                    field: 'salesDeptName'
                },
                
                
                {
                    title: '车牌号',
                    align: 'left',
                    field: 'carno'
                },

                // {
                //     title: '费用类型',
                //     field: 'freeType',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         switch(value - 0) {
                //             case 0:
                //                 return '<span>运费</label>';
                //             case 1:
                //                 return '<span>在途费用</label>';
                //             case 2:
                //                 return '<span>调整费用</label>';
                //             default:
                //                 break;
                //         }
                //     }
                // },
                {
                    title: '司机',
                    align: 'left',
                    field: 'driverName'
                },
                {
                    title: '调度人',
                    align: 'left',
                    field: 'regUserId'
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field: 'regDate'
                },
                {
                    title: '结算客户',
                    align: 'left',
                    field: 'balaCorp'
                },
                {
                    title: '结算方式',
                    align: 'left',
                    field: 'balaMethod',
                    formatter: function(value, row, index) {
                        //if(value == 1){
                        //    return "单笔付款";
                        //}else if(value == 2){
                            return "月度付款"
                        //}else{
                        //    return "";
                        //}
                    }
                },
                {
                    title: '是否自动调度',
                    align: 'left',
                    field: 'isAutoDis',
                    formatter: function(value, row, index) {
                        return value == 1 ? "自动调度" : "非自动调度"
                    }
                },
                // {
                //     title: '对账单号',
                //     align: 'left',
                //     field: 'sheetVbillno',
                // },
                {
                    title: '审批单号',
                    field: 'spNo',
                    formatter: function (value, row, index) {
                        if (value) {
                            return '<a href="javascript:wecom_process(\''+value+'\')">' + value + '</a>'
                        }
                    }
                }
            ]
        };

        $.table.init(options);

        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });
        });

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#reqDeliDateStart',
                type: 'date',
                trigger: 'click'
            });
        });

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#reqDeliDateEnd',
                type: 'date',
                trigger: 'click'
            });
        });
    });
    function sortNum(a,b) {
        return a.addressType-b.addressType;
    }
    function changeDiv(){
        var deliProvinceId= $('#deliProvince').val()
        var arriProvinceId= $('#arriProvince').val()
        var deliCityId= $('#deliCity').val()
        var arriCityId= $('#arriCity').val()
        var deliAreaId= $('#deliArea').val()
        var arriAreaId= $('#arriArea').val()

        $.provinces.init("deliProvince","deliCity","deliArea",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvince","arriCity","arriArea",deliProvinceId,deliCityId,deliAreaId);

        searchPre();
    }

    function InitSubTable(index, row, $detail) {
        var childTable = $detail.html('<div class="table-responsive"><table id="child-table" class="table table-bordered table-striped taCl"></table></div>').find('table');
        $(childTable).bootstrapTable({
            url: ctx + "packagePayDetail/entrust/list",
            method: 'post',
            sidePagination: "server",
            contentType: "application/x-www-form-urlencoded",
            queryParams:{
                lotId:row.lotId
            },
            columns: [
                {
                    title: '发货单号',
                    field: 'invoiceVbillno',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        var result = '<a href="javascript:void(0)" onclick="detail(\'' + row.entrustId + '\',\''+ row.isFleetData +'\')">'+value+'</a>';

                        var val = ''
                        if(row.ltlType == 0) {
                            val = '提货段'
                        }else if(row.ltlType == 1) {
                            val = '干线段'
                        }else if(row.ltlType == 2) {
                            val = '送货段'
                        }
                        result += ' <br /><span class="label label-successT" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="零担运段类型">'+val+'</span>';

                        if(row.collectAmount != null && row.collectAmount != "") {
                            let title = ``;

                            if (row.invoiceBalaType === '2') {
                                title = `现金到付（全部司机代收）：${row.collectAmount}元`
                            }else if (row.invoiceBalaType === '5') {
                                title = `到付（现金给公司）：${row.collectAmount}元`
                            }else if (row.invoiceBalaType === '6') {
                                title = `到付+回单（部分司机代收）：${row.collectAmount}元`
                            }

                            result += ' <span class="label label-success" style="padding:1px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="' + title + '">到付</span>';
                        }
                        if(row.memo != null && row.memo != "") {
                            result += ' <i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px" data-html="true" title="'+row.memo+'"></i>';
                        }
                        return result;
                    }

                },
                {
                    title: '客户',
                    field: 'custAbbr',
                    align: 'left'
                },
                {
                    title: '提货|到货省市区',
                    field: 'deliName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return row.multipleShippingAddressList.sort(sortNum).map(item=>{
                            if(item.addressType == 0){
                                return '<span class="label label-warning pa2">装</span>'+$.table.tooltip(item.provinceName+item.cityName+item.areaName+item.detailAddr);
                            }
                            if(item.addressType == 1){
                                return '<span class="label label-success pa2">卸</span>'+$.table.tooltip(item.provinceName+item.cityName+item.areaName+item.detailAddr);
                            }
                        }).join("<br/>");
                    }
                },
                {
                    title: '货量',
                    field: 'goodsName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        let html=[]
                        if(row.numCount != null && row.numCount != ""&& row.numCount != 0){
                            html.push(row.numCount+"件")
                        }
                        if(row.weightCount != null && row.weightCount != ""&& row.weightCount != 0){
                            html.push(row.weightCount+"吨")
                        }
                        if(row.volumeCount != null && row.volumeCount != ""&& row.volumeCount != 0){
                            html.push(row.volumeCount+"方")
                        }
                        return row.goodsName+html.join("|");
                    }
                },
                {
                    title: '发货单公里',
                    field: 'mileage',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return row.mileage;
                    }
                    
                },
                {
                    title: '车型',
                    field: 'carTypeName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return row.carLenName+value;
                    }

                },
                {
                    title: '应收',
                    field: 'receivableAmount',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return `<span data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="`+getDailList(row.receiveDetailList)+`">`+value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                    
                    }
                },
                {
                    title: '应付',
                    field: 'payAmount',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return `<span data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="`+getDailList(row.payDetailList)+`">`+value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                    }
                },
                // {
                //     title: '运费',
                //     field: 'costAmount',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                {
                    title: '回单照片',
                    field: 'receiptFlies',
                    align: 'left',
                     formatter: function(value, row, index) {
                        var html = "<div class='picviewer'>"
                        if(value != null && value != '') {
                            value.forEach(function (element, index) {
                                html += `<img style="height:32px" src="`+element.filePath+`"/>`
                            });
                        }else {
                            html = '-';
                        }
                        html +="</div>"
                        return html;
                    }
                },
                {
                    title: '客服',
                    field: 'serviceName',
                    align: 'left'
                }
            ],
            onLoadSuccess: function(data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar:false,
                });

                $('[data-toggle="tooltip"]').tooltip()
            }
        });
    }


    /**
     * 跳转应付修改页面
     * @param id
     */
 /*   function edit(id,vbillstatus,isClose) {
        if (vbillstatus != 0) {
            $.modal.alertWarning("只能修改新建状态的应付单");
            return;
        }
        if (isClose === '1') {
            $.modal.alertWarning("该应付单已关账");
            return;
        }
        var url = prefix + "/edit?payDetailId=" + id;
        $.modal.openTab("应付明细修改", url);
    }*/
    var height = document.documentElement.clientHeight - 50;
    var width = document.documentElement.clientWidth - 320;
    /**
     * 结算公司
     */
    function balaCorp(id) {
        var url = prefix + "/balaCorp?payDetailId=" + id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "结算信息",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 生成对账单的方法
     */
    function checking() {
        //var rows = $.table.selectColumns("payDetailId");
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        var balaCorps = $.table.selectColumns("balaCorp");
        if (balaCorps.length > 1) {
            $.modal.alertWarning("请选择相同的结算公司");
            return;
        }


        //var isNtocc = bootstrapTable[0]["isNtocc"];

        var carrierId = bootstrapTable[0]["carrierId"];//承运商

        var lotG7End = bootstrapTable[0]["lotG7End"];
        //var lotG7Syn = bootstrapTable[0]["lotG7Syn"];
        if (lotG7End != 2) {
            lotG7End = null;
        }
        //console.log(lotG7Syn)
        console.log(lotG7End)
        var rows = []
        for (var i = 0; i < bootstrapTable.length; i++) {
            //if (bootstrapTable[i]["isNtocc"] !== isNtocc ) {
            //    $.modal.alertWarning("请选择同为符合或不符合无车承运人的应付单");
            //    return;
            //}
            if (bootstrapTable[i]["carrierId"] !== carrierId ) {
                $.modal.alertWarning("请选择相同承运商下的应付单");
                return;
            }
            //let g7Syn = bootstrapTable[i]["lotG7Syn"]
            let g7End = bootstrapTable[i]["lotG7End"]
            if (g7End != 2) {
                g7End = null;
            }
            if (g7End != lotG7End) {
                $.modal.alertWarning("G7合规单不能与其它单据一起打包");
                return;
            }
            if (bootstrapTable[i]["vbillstatus"] !== 1 && bootstrapTable[i]["vbillstatus"] !== 0 ) {
                $.modal.alertWarning("生成对账单的应付单据只能为新建或已确认状态");
                return;
            }
            if (bootstrapTable[i]["freeType"] === '0') {
                if (bootstrapTable[i]["costTypeFreight"] == '1' || bootstrapTable[i]["costTypeFreight"] == '3') {
                    $.modal.alertWarning("预/到付油卡类型无法生成对账单");
                    return;
                }
            }
            rows = rows.concat(bootstrapTable[i]['payDetailIdList'].split(','))

            let flag = false
            $.ajax({
                type: "POST",
                url: ctx + "payDetail/checkEntrustIfReceipt?lot="+bootstrapTable[i]["lotno"],
                async: false,
                success: function(r){
                    if(r.code != 0){
                        flag = true;
                    }
                }
            });

            if (flag) {
                $.modal.alertWarning("正本回单后才可以生成对账单。");
                return;
            }


        }
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        //验证是否存在异常记录
        $.ajax({
            type: "POST",
            url: prefix + "/checkEntrustExp?payDetailIds="+rows.join(),
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.openTab("生成对账单", prefix + "/checking?payDetailIds=" + rows.join()+"&lotG7End="+lotG7End);
                }
            }
        });
    }

    /**
     * 费用明细
     */
    function detailTab(){
        var lotId = $.table.selectColumns('lotId');//运单id
        if (lotId.length != 1) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }

        var url = ctx + "trace/payReconciliation/detailTab/"+lotId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "费用明细",
            area: ['800px', '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 加入对账单的方法
     */
    function insertChecking() {

        //var rows = $.table.selectColumns("payDetailId");
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        var balaCorps = $.table.selectColumns("balaCorp");
        if (balaCorps.length > 1) {
            $.modal.alertWarning("请选择相同的结算公司");
            return;
        }

        var  isNtocc = 0;//bootstrapTable[0]["isNtocc"];

        var carrierId = bootstrapTable[0]["carrierId"];//承运商
        var lotG7End = bootstrapTable[0]["lotG7End"];
        if (lotG7End != 2) {
            lotG7End = null;
        }
        var rows = []
        for (var i = 0; i < bootstrapTable.length; i++) {
            //if (bootstrapTable[i]["isNtocc"] !== isNtocc ) {
            //    $.modal.alertWarning("请选择同为符合或不符合无车承运人的应付单");
            //    return;
            //}
            if (bootstrapTable[i]["carrierId"] !== carrierId ) {
                $.modal.alertWarning("请选择相同承运商下的应付单");
                return;
            }
            if (bootstrapTable[i]["vbillstatus"] !== 1 && bootstrapTable[i]["vbillstatus"] !== 0 ) {
                $.modal.alertWarning("加入对账单的应付单据只能为新建或已确认状态");
                return;
            }
            let g7End = bootstrapTable[i]["lotG7End"]
            if (g7End != 2) {
                g7End = null;
            }
            if (g7End != lotG7End) {
                $.modal.alertWarning("G7合规单不能与其它单据一起打包");
                return;
            }
            if (bootstrapTable[i]["freeType"] === '0') {
                if (bootstrapTable[i]["costTypeFreight"] == '1' || bootstrapTable[i]["costTypeFreight"] == '3') {
                    $.modal.alertWarning("预/到付油卡类型无法加入对账单");
                    return;
                }
            }
            rows = rows.concat(bootstrapTable[i]['payDetailIdList'].split(','))

            let flag = false
            $.ajax({
                type: "POST",
                url: ctx + "payDetail/checkEntrustIfReceipt?lot="+bootstrapTable[i]["lotno"],
                async: false,
                success: function(r){
                    if(r.code != 0){
                        flag = true;
                    }
                }
            });

            if (flag) {
                $.modal.alertWarning("正本回单后才可以加入对账单。");
                return;
            }

        }

        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        //验证是否存在异常记录
        $.ajax({
            type: "POST",
            url: prefix + "/checkEntrustExp?payDetailIds="+rows.join(),
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    let url = prefix + "/insertChecking?carrierId=" + carrierId + "&payDetailIds=" + rows.join() + "&isNtocc=" + isNtocc;
                    if (lotG7End != null) {
                        url = url + "&g7End=" + lotG7End
                    }
                    // $.modal.open("加入对账单", url);

                    layer.open({
                        type: 2,
                        area: ['80%', '90%'],
                        fix: false,
                        maxmin: true,
                        shade: 0.3,
                        title: "加入对账单",
                        content: url,
                        btn: ['油卡加入', '现金加入', '关闭'],
                        shadeClose: true,            // 弹层外区域关闭
                        btn1: function (index, layero) {
                            var iframeWin = layero.find('iframe')[0];
                            iframeWin.contentWindow.submitHandler(index, layero, 0);
                        },
                        btn2: function (index, layero) {
                            var iframeWin = layero.find('iframe')[0];
                            iframeWin.contentWindow.submitHandler(index, layero, 1);
                            return false;
                        },
                        btn3: function (index, layero) {
                            return true;
                        }
                    });

                }
            }
        });

    }


    /**
     * 分批付款
     */
/*
    function batchPay(){
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["vbillstatus"] !== 1 && bootstrapTable[0]["vbillstatus"] !== 3) {
            $.modal.alertWarning("请选择已确认/部分核销的应付单");
            return;
        }

        if (bootstrapTable[0]["transFeeCount"] === bootstrapTable[0]["gotAmount"]) {
            $.modal.alertWarning("该应付单已完成");
            return;
        }
        var url = prefix + "/batchPay?payDetailId="+bootstrapTable[0]["payDetailId"];
        $.modal.open('分批付款',url);
    }
*/

    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        data.status = $.common.join($('#status').selectpicker('val'));
        data.salesDeptName = $.common.join($('#salesDeptName').selectpicker('val'));
        //data.costTypeFreight = $.common.join($('#costTypeFreight').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        data.status = $.common.join($('#status').selectpicker('val'));
        data.salesDeptName = $.common.join($('#salesDeptName').selectpicker('val'));
        //data.costTypeFreight = $.common.join($('#costTypeFreight').selectpicker('val'));
        $.ajax({
            url: ctx + "packagePayDetail/getCountX",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    $("#sumTransFeeCountTotal").text(data.TRANSFEECOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#sumGotAmountCountTotal").text(data.GOTAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#sumUngotAmountCountTotal").text(data.UNGOTAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

    function showChecking() {
        //运单号
        /*var lot = $.table.selectColumns("lot");
        var url = prefix + "/showChecking/"+lot.join();
        $.modal.openTab("费用确认", url);*/

        /*var id = $.table.selectColumns('payDetailId');
        $.ajax({
            url: prefix + "/selectChecking/"+id,
            type:'POST',
            dataType:'json',
            success:function(result){
                if(result.code == web_status.SUCCESS){
                    var vbillno = result.data.vbillno;
                    var url = prefix + "/showChecking/"+vbillno+"/1";
                    $.modal.openTab("承运商对账", url);
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });*/
        var sheetVbillno = $.table.selectColumns('sheetVbillno')[0];
        if (sheetVbillno) {
            var url = prefix + "/showChecking/" + sheetVbillno + "/1";
            $.modal.openTab("承运商对账", url);
        } else {
            $.modal.alertError('未查询到该应付单的对账单信息');
        }
    }

    function getDailList(list) {
        let html=`<div class='ontooltip'><div class='panel-body tooltipBody'><div class='padt5'>`;
            
        if(list){
            html+=`<table class='custom-tab tab table'> <thead style='background: #f4f6f7;'><tr>
                    <th>费用类型</th>
                    <th>金额</th>
                    <th>备注</th>
                    <th>状态</th>
                </tr></thead><tbody> `;
                    list.forEach(res=>{
                        let vbillstatus="";
                        if(res.vbillstatus==0){
                            vbillstatus= `<span class='carve carve-inverse'>新建</span>` ;
                        }else if(res.vbillstatus==1){
                            vbillstatus = `<span class='carve carve-primary'>已确认</span>` ;
                        }else if(res.vbillstatus==2){
                            vbillstatus = `<span class='carve carve-success'>已对账</span>` ;
                        }else if(res.vbillstatus==3){
                            vbillstatus = `<span class='carve carve-coral'>部分核销</span>` ;
                        }else if(res.vbillstatus==4){
                            vbillstatus = `<span class='carve carve-warning'>已核销</span>` ;
                        }else if(res.vbillstatus==5){
                            vbillstatus = `<span class='carve carve-danger'>关闭</span>` ;
                        }else if(res.vbillstatus==6){
                            vbillstatus = `<span class='carve carve-primary'>已申请</span>` ;
                        }else if(res.vbillstatus==7){
                            vbillstatus = `<span class='carve carve-warning'>核销中</span>` ;
                        }else if(res.vbillstatus==8){
                            vbillstatus = `<span class='carve carve-yellow'>审核中</span>` ;
                        }
                        
                        let costType="";
                        if(res.freeType=='1'){
                            costType = $.table.selectDictLabel(costTypeOnWay, res.costTypeOnWay);
                        }else  if(res.freeType=='0'){
                            costType = $.table.selectDictLabel(costTypeFreight, res.costTypeFreight);
                        }

                        let memo = res.memo ==null ? '' :res.memo;
                        
                        html+=`<tr>
                            <td>`+(costType?costType:'-')+`</td>
                            <td>`+res.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</td>
                            <td>`+ memo +`</td>
                            <td>`+vbillstatus+`</td>
                        </tr>`
                    })

                    
            html+=`</tbody></table>`;
            
        }else{
            html+=`暂无数据`; 
        }
        html+=`</div></div></div>`;
        return html
    }
    
    /**
     * 跳转委托单详情页
     */
     function detail(entrustId,isFleetData) {
        var url = ctx + "trustDeed/detail?entrustId="+entrustId + "&isFleetIn=" + isFleetData;
        $.modal.openTab($.table._option.modalName + "详细", url);
    }




    function ltlData() {
        layer.open({
            type: 1,
            title: '零担明细导出',
            area: ['50%', '25%'],//弹框大小  屏幕宽度的80%，高度的80%；
            content: $('#ltlDataHtml').html(),
            btn: ['导出','取消'],
            // 打开弹窗的回调函数，用于回显页面数据
            success: function () {
                // 获取当前日期
                var now = new Date();
                // 设置为当前月第一天
                var start = new Date(now.getFullYear(), now.getMonth(), 1);
                // 设置为当前月最后一天
                var end = new Date(now.getFullYear(), now.getMonth() + 1, 0);

                // 格式化开始和结束日期
                var startStr = formatDate(start);
                var endStr = formatDate(end);
                // 拼接时间范围字符串
                var lastMonth = startStr + " - " + endStr;

                layui.laydate.render({
                    elem: '#ltlDate',
                    range: ['#dateStart', '#dateEnd'],
                    rangeLinked: true,
                    value: lastMonth
                });
            },
            yes: function (index, layero) {
                layer.confirm('确定导出条数据吗?', function (index) {
                    var data = {};
                    data["dateStart"] = $("#dateStart").val()
                    data["dateEnd"] = $("#dateEnd").val()
                    data["custAbbr"] = $("#custAbbr").val()
                    data["carrName"] = $("#shCarrName").val()

                    if (data.dateStart == '' || data.dateEnd == '') {
                        $.modal.alertWarning("日期区间必填。")
                        return
                    }

                    $.ajax({
                        url: ctx + "tms/segment/ltl-data-all",
                        type: "post",
                        dataType: "json",
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify(data),
                        beforeSend: function () {
                            $.modal.loading("正在导出数据，请稍候...");
                            $.modal.disable();
                        },
                        success: function(result) {
                            if (result.code == web_status.SUCCESS) {
                                window.location.href = ctx + "common/download?fileName=" + encodeURI(result.data) + "&delete=" + true;
                            } else if (result.code == web_status.WARNING) {
                                $.modal.alertWarning(result.msg)
                            } else {
                                $.modal.alertError(result.msg);
                            }
                            $.modal.closeLoading();
                            $.modal.enable();

                            layer.closeAll()
                        }
                    });
                });



            }
        })

    }
    function formatDate(date) {
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var day = date.getDate();

        return year + '-' + month + '-' + day;
    }

    function formatTooltip(text) {
        if (!text) return '';
        return text.toString()
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;")
            .replace(/\\n/g, "<br>")   // 替换文本中的 \n（两个字符）
            .replace(/\n/g, "<br>");   // 替换真实换行符
    }

</script>
</body>
</html>