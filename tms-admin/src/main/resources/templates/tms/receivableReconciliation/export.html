<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('在途导出')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <th:block th:include="include :: bootstrap-editable-css" />
</head>
<style>
    .fcff{
        color: #ff1f1f;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 130px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .checkbox-inline{
        width: 14%;
    }
    .fw {
        font-weight: bold;
    }
    .f14{
        font-size: 14px;
    }
</style>
<body>
<div class="form-content">
    <form id="role-form" class="form-horizontal" novalidate="novalidate">
        <!--客户id-->
        <input type="hidden" name="customerId" th:value="${customerId}">
        <!--要求提货年月-->
        <input type="hidden" name="reqStartDate" th:value="${reqStartDate}">
        <input type="hidden" name="reqEndDate" th:value="${reqEndDate}">
        <div class="row">
            <div class="col-md-3 col-sm-3">
                <div class="form-group">
                    <div class="col-sm-12">
                        <input name="custAbbr" placeholder="请输入客户简称" class="form-control valid"
                               type="text" maxlength="25">
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-3">
                <div class="form-group">
                    <div class="col-sm-12">
                        <select name="transCode" id="transCode" class="form-control valid noselect2 selectpicker"
                                aria-invalid="false" data-none-selected-text="运输方式" multiple th:with="type=${@dict.getType('trans_code')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-sm-6">
                <div class="form-group">
                    <div class="col-sm-12">
                        <input type="text" style="width: 47%; float: left;" class="form-control"
                               id="reqDeliDateStart" name="reqDeliDateStart" readonly placeholder="要求提货开始日">
                        <span style="display: block; line-height: 20px; float: left; text-align:center; width:6%;">-</span>
                        <input type="text" style="width: 47%; float: left;" class="form-control"
                               id="reqDeliDateEnd" name="reqDeliDateEnd" readonly placeholder="要求提货结束日">
                    </div>
                </div>
            </div>
            
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-3">
                <div class="form-group">
                    <div class="col-sm-12">
                        <select id="invoice_vbillstatus" class="form-control selectpicker"
                                aria-invalid="false" data-none-selected-text="发货单状态" multiple th:with="type=${invoiceStatusList}">
                            <option th:each="dict : ${type}" th:text="${dict.context}" th:value="${dict.value}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-3">
                <div class="form-group">
                    <div class="col-sm-12" style="padding-right: 0">
                        <select name="carType" class="form-control" th:with="type=${@dict.getType('car_type')}">
                            <option value="">-- 车型 --</option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" ></option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-3 col-sm-3">
                <div class="form-group">
                    <div class="col-sm-12">
                        <select name="custSalesDeptId" id="custSalesDeptId" class="form-control selectpicker" data-none-selected-text="客户运营部">
                            <option value=""></option>
                            <option th:each="mapS,status:${salesNameList}" th:value="${mapS.deptId}"
                                th:text="${mapS.deptName}"></option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 col-sm-3">
                <div class="form-group">
                    <div class="col-sm-12">
                        <select name="salesDeptId" id="salesDeptId" class="form-control valid noselect2 selectpicker"
                                aria-invalid="false" data-none-selected-text="单据销售员" multiple>
                            <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                    th:text="${mapS.deptName}"></option>
                        </select>
                    </div>
                </div>
            </div>
            
<!--            <div class="col-md-5 col-sm-5">-->
<!--                <div class="form-group">-->
<!--                    <div class="col-sm-12">-->
<!--                        <input name="vbillno" id="vbillno" class="form-control" type="text"-->
<!--                               placeholder="请输入发货单号，多个发货单号用逗号(,)分隔（多个单号时只支持精确查找）"-->
<!--                               onblur="this.value=this.value.replace(/，/g, ',').replace(/ /g, '').replace(/	/g, '')"-->
<!--                               required="" aria-required="true">-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->

        </div>
        <div style="margin: 10px -5px 0;">
            <span class="fw f14">勾选需要导出的字段列</span>
        </div>
        <div style="margin: 0 -5px;">
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="custOrderno">  客户发货单号
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="reqDeliDate">  发货日期
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="vbillno">  发货单号
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="custAbbr">  客户简称
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="deliAddress">  发货地址
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="deliContact">  发货人
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="arriAddrName">  收货单位
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="arriAddress">  收货地址
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="arriContact">  收货人
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="goodsName">  货品名称
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="numCount">  件数
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="weightCount">  重量
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="volumeCount">  体积
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="billingMethod">  计费方式
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="receiptAmountOnWay">  其他费用
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="actualReceiptAmount">  合计费用
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="carLenName">  车长
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="carTypeName">  车型
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="transName">  运输方式
            </label>

            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="memo">  订单备注
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="driverName">  司机姓名
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="driverMobile">  司机手机
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="carno">  主车牌
            </label>
            <label class="checkbox-inline">
                <input type="checkbox" checked ='checked' name="expType" value="trailerNo">  挂车车牌
            </label>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<th:block th:include="include :: bootstrap-table-editable-js" />
<script th:inline="javascript">

    var prefix = ctx + "receivableReconciliation";
    let hideColumnNameList=['custOrderno','reqDeliDate','vbillno','custAbbr','deliAddress','deliContact',
                            'arriAddrName','arriAddress','arriContact','goodsName','numCount','weightCount',
                            'volumeCount','billingMethod','receiptAmountOnWay','actualReceiptAmount',
                            'carLenName','carTypeName','transName','memo','driverName','driverMobile','carno','trailerNo']
    var date = new Date();
    date.setTime(date.getTime());
    var year = date.getFullYear();
    var month = ("0" + (date.getMonth() + 1)).slice(-2);
    var day = ("0" + (date.getDate())).slice(-2);
    var today = year + "-" + month + "-" + day;
    //$("#reqDeliDateEnd").val(today);

    //获取上月日期
    var days = new Date(year, month, 0);
    //获取当前日期中月的天数
    days = days.getDate();
    var year2 = year;
    var month2 = parseInt(month) - 1;
    if (month2 == 0) {
        year2 = parseInt(year2) - 1;
        month2 = 12;
    }
    var day2 = day;
    var days2 = new Date(year2, month2, 0);
    days2 = days2.getDate();
    if (day2 > days2) {
        day2 = days2;
    }
    if (month2 < 10) {
        month2 = '0' + month2;
    }
    var t2 = year2 + '-' + month2 + '-' + day2;
    $("#reqDeliDateStart").val(t2);

    $(function() {
         /**
         * 初始化日期控件
         */
         layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#reqDeliDateStart',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#reqDeliDateEnd',
                type: 'date',
                trigger: 'click'
            });
        });
    })


    /**
     * 提交
     */
    function submitHandler() {
        if ($("#reqDeliDateStart").val().trim() == '') {
            $.modal.msg("请选择要求提货日期起始日期", modal_status.WARNING);
            return;
        }
        var tmpEndDate = [[${#dates.format(#dates.createToday(), 'yyyy-MM-dd')}]]
        if ($("#reqDeliDateEnd").val().trim()!= '') {
            tmpEndDate = $("#reqDeliDateEnd").val().trim();
        }
        var ymd1 = $("#reqDeliDateStart").val().trim().split("-");
        var ymd2 = tmpEndDate.split("-");
        if ((ymd2[0] - ymd1[0]) * 12 + (ymd2[1] - ymd1[1]) + (ymd2[2] - ymd1[2]) / 31 > 12) {
            $.modal.msg("要求提货日期跨度不能超过12个月", modal_status.WARNING);
            return;
        }

        let data = $("#role-form").serializeArray();

        var expTypes = $.form.selectCheckeds("expType");

        let a = new Set( expTypes.split(",") );
        let b = new Set( hideColumnNameList );
        let arr = Array.from(new Set([...b].filter(x => !a.has(x))));
        // data.hideColumnName=arr.join(',');
        data.push({"name": "hideColumnName", "value": arr.join(',')});

        $.modal.confirm("确定导出所有应收对账吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            // var search = $.common.formToJSON("role-form");
            // search.params = new Map();
            // search.params.invoice_vbillstatus = $.common.join($('#invoice_vbillstatus').selectpicker('val'));
            // //search.params.receiveVbillstatus = $.common.join($('#receiveVbillstatus').selectpicker('val'));
            // search.salesDeptId = $.common.join($('#salesDeptId').selectpicker('val'));
            //
            $.post(prefix + "/template/export", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }
</script>
</body>
</html>