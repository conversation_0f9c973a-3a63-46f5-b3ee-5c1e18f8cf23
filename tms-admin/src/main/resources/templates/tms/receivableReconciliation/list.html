<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应收对账列表')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <th:block th:include="include :: bootstrap-editable-css" />
    <style type="text/css">
        .container-div{
            padding: 0px 15px;
        }
        .search-collapse, .select-table{
            margin: 0;
            border-radius:0;
            padding: 5px;
        }
        .search-collapse,.f7f7{
            background-color: #F7F7F7;
        }
        .form-group{
            margin: 0;
        }
        .row + .row{
            margin-top: 5px;
        }
        .btn-group-sm>.btn, .btn-sm{
            padding: 3px 10px;
        }


    
        .fixed-table-footer .th-inner {
            width: auto !important;
            word-wrap: break-word !important;
            word-break: normal !important;
            white-space: normal;
        }
        .table-striped{
            height: calc(100% - 110px);
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <!--客户id-->
                <input type="hidden" name="customerId" th:value="${customerId}">
                <!--要求提货年月-->
                <input type="hidden" name="reqStartDate" th:value="${reqStartDate}">
                <input type="hidden" name="reqEndDate" th:value="${reqEndDate}">
                <div class="row">
                    <div class="col-md-2 col-sm-2">
                        <input name="custAbbr" id="custAbbr" placeholder="请输入客户简称" class="form-control valid"
                               type="text" maxlength="25">
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <select name="transCode" id="transCode" class="form-control valid noselect2 selectpicker"
                                aria-invalid="false" data-none-selected-text="运输方式" multiple th:with="type=${@dict.getType('trans_code')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
                    <div class="col-md-4 col-sm-4">
                        <input type="text" style="width: 45%; float: left;" class="form-control"
                               id="reqDeliDateStart" name="reqDeliDateStart" placeholder="要求提货开始日" autocomplete="off" aria-autocomplete="none">
                        <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                        <input type="text" style="width: 45%; float: left;" class="form-control"
                               id="reqDeliDateEnd" name="reqDeliDateEnd" placeholder="要求提货结束日" autocomplete="off" aria-autocomplete="none">
                    </div>
                    <div class="col-md-4 col-sm-4">
                        <div class="col-md-4 col-sm-4">
                            <select id="invoice_vbillstatus" class="form-control selectpicker"
                                    aria-invalid="false" data-none-selected-text="发货单状态" multiple th:with="type=${invoiceStatusList}">
                                <option th:each="dict : ${type}" th:text="${dict.context}" th:value="${dict.value}"></option>
                            </select>
                        </div>
                        <div class="col-md-4 col-sm-4">
                            <select name="carType" class="form-control" th:with="type=${@dict.getType('car_type')}">
                                <option value="">-- 车型 --</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" ></option>
                            </select>
                        </div>
                        <div class="col-md-4 col-sm-4">
                            <select class="form-control valid"  aria-invalid="false"
                                    name="operateCorp" th:with="type=${@dict.getType('bala_corp')}" required>
                                <option value="">--结算公司--</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-2 col-sm-2">
                        <select name="custSalesDeptId" id="custSalesDeptId" class="form-control selectpicker" data-none-selected-text="客户运营部">
                            <option value=""></option>
                            <option th:each="mapS,status:${salesNameList}" th:value="${mapS.deptId}"
                                th:text="${mapS.deptName}"></option>
                        </select>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <select name="salesDeptId" placeholder="单据销售员" id="salesDeptId" class="form-control valid noselect2 selectpicker"
                                aria-invalid="false" data-none-selected-text="单据销售员" multiple>
                            <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                    th:text="${mapS.deptName}"></option>
                        </select>
                    </div>
                    <div class="col-md-4 col-sm-6">
                        <div style="display: flex;algin-items:center;justify-content:space-between;">
                            <label class="flex_left" style="width: 70px;line-height: 25px;margin: 0">提货地区：</label>
                            <select  name="deliProvinceId" id="deliProvinceId" class="form-control valid" style="flex: 1" aria-invalid="false"></select>
                            <select name="deliCityId" id="deliCityId" class="form-control valid" style="flex: 1" aria-invalid="false"></select>
                            <select name="deliAreaId" id="deliAreaId" class="form-control valid" style="flex: 1" aria-invalid="false"></select>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-6">
                        <div style="display: flex;algin-items:center;justify-content:space-between;">
                            <label class="flex_left" style="width: 70px;line-height: 25px;margin: 0">到货地区：</label>
                            <select  name="arriProvinceId" id="arriProvinceId" class="form-control valid" style="flex: 1" aria-invalid="false"></select>
                            <select name="arriCityId" id="arriCityId" class="form-control valid" style="flex: 1" aria-invalid="false"></select>
                            <select name="arriAreaId" id="arriAreaId" class="form-control valid" style="flex: 1" aria-invalid="false"></select>
                        </div>
                    </div>



                </div>
                <!-- <div class="row">
                    <div class="col-md-7 col-sm-12">
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                
                                <div class="col-sm-12">
                                    <select name="ifBilling" id="ifBilling" class="form-control valid"
                                            aria-invalid="false">
                                        <option value="">--是否开票--</option>
                                        <option value="0">否</option>
                                        <option value="1">是</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="receivableWriteOffStatusList" id="receivableWriteOffStatusList"
                                            class="form-control valid noselect2 selectpicker"
                                            data-none-selected-text="应收是否核销" multiple>
                                        <option  value="0">未核销</option>
                                        <option  value="1">部分核销</option>
                                        <option  value="2">已核销</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->

                <div class="row">
                    <div class="col-md-5 col-sm-6">
                        <input name="vbillno" id="vbillno" class="form-control" type="text"
                               placeholder="输入发货单号，多个单号用逗号(,)分隔（多单号只支持精确查询）"
                               autocomplete="off" aria-autocomplete="none"
                               onblur="this.value=this.value.replace(/，/g, ',').replace(/ /g, '').replace(/	/g, '')"
                               required="" aria-required="true">
                    </div>
                    <div class="col-md-5 col-sm-6">
                        <input name="custOrderno" id="custOrderno" class="form-control" type="text"
                               placeholder="输入客户单号，多个单号用逗号(,)分隔（多单号只支持精确查询）"
                               autocomplete="off" aria-autocomplete="none"
                               onblur="this.value=this.value.replace(/，/g, ',').replace(/ /g, '').replace(/	/g, '')"
                               required="" aria-required="true">
                    </div>

                    <div class="col-md-2 col-sm-6">
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                class="fa fa-refresh"></i>&nbsp;重置</a>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="exportExcel()" shiro:hasPermission="tms:receivableReconciliation:export">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-warning" onclick="exportExcelTemplate()" shiro:hasPermission="tms:receivableReconciliation:template:export">
                <i class="fa fa-download"></i> 对账模板导出
            </a>
            <!--<a class="btn btn-info"  onclick="$.table.importExcel()" shiro:hasPermission="tms:receivableReconciliation:adjustImport">
                <i class="fa fa-upload"></i> 调整单导入
            </a>
            <a class="btn btn-warning"  onclick="adjustExport()" shiro:hasPermission="tms:receivableReconciliation:adjustExport">
                <i class="fa fa-download"></i> 调整单导出
            </a>-->
            <a class="btn btn-success" onclick="batchAdjust()" shiro:hasPermission="tms:receivableReconciliation:batchAdjust">
                <i class="fa fa-balance-scale"></i> 批量调账
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<th:block th:include="include :: bootstrap-table-editable-js" />
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:inline="javascript">
    var vbillstatus = [[${invoiceStatusList}]]; //发货单状态
    //发货单状态map
    var invoiceStatusMap = [[${invoiceStatusMap}]];

    var prefix = ctx + "receivableReconciliation";

    var date = new Date();
    date.setTime(date.getTime());
    var year = date.getFullYear();
    var month = ("0" + (date.getMonth() + 1)).slice(-2);
    var day = ("0" + (date.getDate())).slice(-2);
    var today = year + "-" + month + "-" + day;
    //$("#reqDeliDateEnd").val(today);



    //获取上月日期
    var days = new Date(year, month, 0);
    //获取当前日期中月的天数
    days = days.getDate();
    var year2 = year;
    var month2 = parseInt(month) - 1;
    if (month2 == 0) {
        year2 = parseInt(year2) - 1;
        month2 = 12;
    }
    var day2 = day;
    var days2 = new Date(year2, month2, 0);
    days2 = days2.getDate();
    if (day2 > days2) {
        day2 = days2;
    }
    if (month2 < 10) {
        month2 = '0' + month2;
    }
    var t2 = year2 + '-' + month2 + '-' + day2;
    $("#reqDeliDateStart").val(t2);




    //合计
    var receiptAmountTotal = 0;//总应收
    var actualReceiptAmountTotal = 0;//实际总应收
    var adjustAmountTotal = 0;//调整后应收
    var receiptAmountFreightTotal = 0;//运费总应收
    var receiptAmountOnWayTotal = 0;//在途总应收
    var costCountTotal = 0;//总成本
    var costCountFreightTotal = 0;//运费总成本
    var costCountOnWayTotal = 0;//在途总成本
    var otherFeeCountTotal = 0;//第三方应付
    var managerFeeCountTotal = 0;//管理费
    var grossProfitTotal = 0;//含管理费毛利
    var percentageTotal = 0;//含管理费毛利率
    //var noGrossProfitTotal = 0;//不含管理费毛利
    //var noPercentageTotal = 0;//不含管理费毛利率
    var netProfitsTotal = 0;
    var netTotalReceiveAmountTotal = 0;

    $(function () {
        var options = {
            url: prefix + "/list",
            exportUrl: prefix + "/export",
            importUrl: ctx + "receive/adjustImport",
            //detailUrl:  "invoice/detail/{id}",
            showToggle: false,
            showColumns: true,
            modalName: "应收对账",
            fixedColumns: true,
            rememberSelected: false,
            fixedNumber: 2,
            clickToSelect: true,
            showFooter: true,
            showExport: false,
            height: 590,
            exportTypes:['excel','csv'],
            exportOptions:{
                ignoreColumn: [0],
                fileName:"应收对账"
            },
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            onPostBody: function () {
                //合并页脚
                clearTotal()
                merge_footer();
                getAmountCount();
            },
            onEditableSave: function (field, row, oldValue, $el) {
                var data;
                if (field === 'receiptAmountFreight') {
                    data = {invoiceId: row.invoiceId, amount: row.receiptAmountFreight,status: row.receiveStatus, type: 0};
                    editReceive(data);
                } else if(field === 'receiptAmountOnWay'){
                    data = {invoiceId: row.invoiceId, amount: row.receiptAmountOnWay,status: row.receiveStatus, type: 1};
                    editReceive(data);
                }else if (field === 'custOrderno') {
                    data = {invoiceId: row.invoiceId,orderNo:row.custOrderno}
                    editCustOrderno(data);
                }
            },
            columns: [
                {
                    checkbox: true,
                    footerFormatter: function (row) {
                        //return "总应收:<nobr id='receiptAmountTotal'>￥0</nobr>&nbsp&nbsp"
                        return "实际总应收:<nobr id='actualReceiptAmountTotal'>￥0</nobr>&nbsp&nbsp"
                            //+ "调整后金额:<nobr id='adjustAmountTotal'>￥0</nobr>&nbsp&nbsp"
                            + "运费总应收:<nobr id='receiptAmountFreightTotal'>￥0</nobr>&nbsp&nbsp"
                            + "在途总应收:<nobr id='receiptAmountOnWayTotal'>￥0</nobr>&nbsp&nbsp<br>"
                            /*[# th:if="${@shiroUtils.getSubject().isPermitted('tms:revenue:view')}"]*/
                            + "总成本:<nobr id='costCountTotal'>￥0</nobr>&nbsp&nbsp"
                            + "运费总成本:<nobr id='costCountFreightTotal'>￥0</nobr>&nbsp&nbsp"
                            /*[/]*/
                            + "在途总成本:<nobr id='costCountOnWayTotal'>￥0</nobr>&nbsp&nbsp"
                            + "第三方应付:<nobr id='otherFeeCountTotal'>￥0</nobr>&nbsp&nbsp"
                         /*   + "管理费:<nobr id='managerFeeCountTotal'>￥0</nobr>&nbsp&nbsp"*/
                       /*     + "含管理费毛利:<nobr id='grossProfitTotal'>￥0</nobr>&nbsp&nbsp"
                            + "含管理费毛利率:<nobr id='percentageTotal'>￥0</nobr>&nbsp&nbsp"*/
                            //+ "毛利:<nobr id='noGrossProfitTotal'>￥0</nobr>&nbsp&nbsp"
                            //+ "毛利率:<nobr id='noPercentageTotal'>￥0</nobr>"
                            /*[# th:if="${@shiroUtils.getSubject().isPermitted('tms:revenue:view')}"]*/
                            + "利润：<nobr id='netProfitsTotal'>￥0</nobr>&nbsp;&nbsp;"
                            + "利润率：<nobr id='netProfitsRateTotal'>0%</nobr>&nbsp;&nbsp;"
                            /*[/]*/
                            + "<br>"
                            + "<b>总合计：</b>"
                            //+ "总金额：<nobr id='allTransFeeCount'>￥0</nobr>&nbsp&nbsp"
                            + "实际总应收：<nobr id='actualReceiptAmount'>￥0</nobr>&nbsp&nbsp"
                            //+ "调整后金额：<nobr id='allAdjustAmount'>￥0</nobr>&nbsp&nbsp"
                            + "运费类型金额：<nobr id='allFreightTotal'>￥0</nobr>&nbsp&nbsp"
                            + "在途类型金额：<nobr id='allOnWayTotal'>￥0</nobr>&nbsp&nbsp<br>"
                            /*[# th:if="${@shiroUtils.getSubject().isPermitted('tms:revenue:view')}"]*/
                            + "总成本：<nobr id='allCostCount'>￥0</nobr>&nbsp&nbsp"
                            + "运费总成本：<nobr id='allCostCountFreight'>￥0</nobr>&nbsp&nbsp"
                            /*[/]*/
                            + "在途总成本：<nobr id='allCostCountOnWay'>￥0</nobr>&nbsp&nbsp"
                            + "第三方应付：<nobr id='allOtherFeeCount'>￥0</nobr>&nbsp&nbsp"
                           /* "管理费：<nobr id='allManagerFeeCount'>￥0</nobr>&nbsp&nbsp<br>"+
                            "<b>毛利：</b>含管理费毛利：<nobr id='allGrossProfit'>￥0</nobr>&nbsp&nbsp"+
                            "含管理费毛利率：<nobr id='grossProfitMargin'>￥0</nobr>&nbsp&nbsp"+*/
                            //"毛利：<nobr id='noAllGrossProfit'>￥0</nobr>&nbsp&nbsp"+
                            //"毛利率：<nobr id='noGrossProfitMargin'>￥0</nobr>&nbsp&nbsp";
                            /*[# th:if="${@shiroUtils.getSubject().isPermitted('tms:revenue:view')}"]*/
                            + "利润：<nobr id='netProfitsAll'>￥0</nobr>&nbsp;&nbsp;"
                            + "利润率：<nobr id='netProfitsRateAll'>0%</nobr>"
                            /*[/]*/
                    }
                },
                {
                    title: '发货单号',
                    align: 'left',
                    field: 'vbillno',
                    // switchable:false,
                    formatter: function status(value, row,index) {
                        let html = '<a href="javascript:void(0)" style="text-decoration: underline;" onclick="invoiceDetail(\'' + row.invoiceId + '\')">'+value+'</a>'

                        if(row.memo){
                            html += ' <span style="padding:1px;vertical-align: middle;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+row.memo+'"> <i class="fa fa-exclamation-circle"/> </span>'
                        }

                        // if (row.memo) {
                        //     html = html + '<span class="label label-warning" style="padding:3px;vertical-align: middle;margin-left:5px;" ' +
                        //         'data-toggle="tooltip"  data-placement="right" data-container="body" data-html="true" title="'+ row.memo +'">注</span>'
                        //
                        // }

                        //
                        return html;
                    }
                },
                {
                    title: '客户发货单号',
                    align: 'left',
                    field: 'custOrderno',
                    cellStyle: formatTableUnit,
                    formatter: function(value, row, index){
                        let htmlText;
                        if(value){
                            htmlText=`<span class='label badge-white' data-toggle='tooltip' data-placement="top" data-container="body" data-html='true' title='`+value+`'>`+value+`</span>`
                        }else{
                            htmlText=value
                        }

                        return htmlText
                    }

                    // editable:{
                    //     type: 'text',
                    //     title: '修改客户发货单号',
                    //     validate:  function (v) {
                    //         //判断是否为空
                    //         if (v === "") {
                    //             return "不能为空！";
                    //         }
                    //         //判断长度
                    //         if (getStringLen(v) > 50) {
                    //             return "长度过长！";
                    //         }
                    //     }
                    // }
                },
                // {title: '是否开票', align: 'left', field: 'ifBilling',formatter: function (value, row, index) {
                //         if (value === '0') {
                //             return '否';
                //         } else if (value === '1') {
                //             return '是';
                //         }
                //         return ''
                //     }
                // },
                {
                    title: '发货单状态', field: 'invoiceStatus', align: 'left', width: 20, switchable: false,
                    formatter: function status(row, value) {
                        var context = '';

                        vbillstatus.forEach(function (v) {
                            if (v.value === value.invoiceStatus) {
                                if (value.invoiceStatus === invoiceStatusMap.NEW) {
                                    //新建
                                    context = '<span class="label label-primary">' + v.context  + '</span>';
                                } else if (value.invoiceStatus == invoiceStatusMap.AFFIRM) {
                                    //已确认
                                    context = '<span class="label label-warning">' + v.context  + '</span>';
                                } else if (value.invoiceStatus == invoiceStatusMap.PORTION_PICK_UP
                                    || value.invoiceStatus == invoiceStatusMap.PICK_UP) {
                                    //部分提货 与 已提货
                                    context = '<span class="label label-info">' + v.context  +'</span>';
                                } else if (value.invoiceStatus == invoiceStatusMap.PORTION_ARRIVALS
                                    || value.invoiceStatus == invoiceStatusMap.ARRIVALS) {
                                    //部分到货  已到货
                                    context = '<span class="label label-success">' + v.context  +'</span>';
                                } else if (value.invoiceStatus == invoiceStatusMap.PORTION_RETURNS
                                    || value.invoiceStatus == invoiceStatusMap.RETURNS) {
                                    //部分回单  已回单
                                    context = '<span class="label label-primary">' + v.context  +'</span>';
                                } else {
                                    //关闭
                                    context = '<span class="label label-inverse">' + v.context + '</span>';
                                }

                                return false;
                            }
                        });
                        return context;
                    }
                },
                
                // {
                //     title: '应收单据状态', field: 'receiveStatus', align: 'left',
                //     formatter: function status(value, row, index) {
                //         switch (value) {
                //             case 0:
                //                 return '<span class="label label-primary">新建</span>'
                //             case 1:
                //                 return '<span class="label label-warning">已确认</span>';
                //             case 2:
                //                 return '<span class="label label-coral">已对账</span>';
                //             case 3:
                //                 return '<span class="label label-info">部分核销 </label>';
                //             case 4:
                //                 return '<span class="label label-success">已核销</span>';
                //             case 5:
                //                 return '<span class="label label-inverse">关闭 </label>';
                //             default:
                //                 break;
                //         }
                //     }
                // },
                {title: '客户简称', align: 'left', field: 'custAbbr'},
                {title: '要求提货日', field: 'reqDeliDate', align: 'left'},
                {title: '提货到货省市区', align: 'left', field: 'address',switchable:false,
                    formatter: function status(value, row, index) {
                        return row.deliAddress
                            + '<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>' + row.arriAddress;
                    }},
                {title: '货品名称', align: 'left', field: 'goodsName'},
                {title: '总件', align: 'left', field: 'numCount'},
                {title: '总T', align: 'left', field: 'weightCount'},
                {title: '总m³', align: 'left', field: 'volumeCount'},

                // {title: '发货单备注', align: 'left', field: 'memo'},
                {title: '车长', align: 'left', field: 'carLenName'},
                {title: '车型', align: 'left', field: 'carTypeName'},
                {title: '车号', align: 'left', field: 'carno'},


                {title: '运费应收', align: 'right', field: 'receiptAmountFreight'},
                {title: '在途应收', align: 'right', field: 'receiptAmountOnWay'},
                // {title: '总应收', align: 'right', field: 'receiptAmount'},
                {
                    title: '实际总应收', align: 'right', field: 'actualReceiptAmount',
                    formatter: function status(value, row, index) {
                        if(row.adjustAmount){
                            return value+`<i class="fa fa-question-circle-o" data-toggle="tooltip" style="font-size: 15px" data-html="true" data-container="body" title="调整额：`+row.adjustAmount+`"></i> `;
                        }
                        return value;
                    }
                },
                // {title: '已收金额', align: 'right', field: 'gotAmount'},
                // {title: '未收金额', align: 'right', field: 'ungotAmount'},
                // {title: '调整后金额', align: 'right', field: 'adjustAmount'},
                /*[# th:if="${@shiroUtils.getSubject().isPermitted('tms:revenue:view')}"]*/
                {title: '运费成本', align: 'right', field: 'costCountFreight'},/*[/]*/
                {title: '在途成本', align: 'right', field: 'costCountOnWay'},
                /*[# th:if="${@shiroUtils.getSubject().isPermitted('tms:revenue:view')}"]*/
                {title: '总成本', align: 'right', field: 'costCount'},/*[/]*/
                {title: '第三方应付', align: 'right', field: 'otherFeeCount'},
               /* {title: '管理费', align: 'right', field: 'managerFee'},*/
                /*{title: '毛利', align: 'right', field: 'grossProfit'},
                {
                    title: '毛利率',
                    align: 'right',
                    field: 'grossProfit',
                    formatter: function status(value, row, index) {
                        var grossProfit = row.grossProfit;
                        var actualReceiptAmount = row.actualReceiptAmount;
                        var percentage = grossProfit/actualReceiptAmount;
                        return percentage.toLocaleString('zh', {
                            style: 'percent',
                            minimumFractionDigits: '2'
                        });
                    }
                },*/
                {title: '平台费',align:'right',field:'ptf',formatter:function(value,row,index){
                    if (value != null) {
                        return value.toFixed(2)
                    }
                }},
                /*[# th:if="${@shiroUtils.getSubject().isPermitted('tms:revenue:view')}"]*/
                {title: '应付税金', align: 'right', field: 'yfTax',formatter:function(value,row,index){
                        if (value != null) {
                            return value.toFixed(2)
                        }
                    }},/*[/]*/
                {title: '三方税金', align: 'right', field: 'sfTax',formatter:function(value,row,index){
                        if (value != null) {
                            return value.toFixed(2)
                        }
                    }},
                /*[# th:if="${@shiroUtils.getSubject().isPermitted('tms:revenue:view')}"]*/
                {title: '利润',align:'right',field:'netProfits',formatter:function(value,row,index){
                    if (value != null) {
                        return value.toFixed(2)
                    }
                    return value
                }},
                {title: '利润率',align:'right',field:'netTotalReceiveAmount',formatter:function(value,row,index){
                        var percentage = row.netProfits/value;
                        return percentage.toLocaleString('zh', {
                            style: 'percent',
                            minimumFractionDigits: '2'
                        });
                    }},
                {title: '成本价',align:'right',field:'costPrice',formatter:function(value,row,index){
                        if (value != null) {
                            return value.toFixed(2)
                        }
                        return value
                    }},
                {title: '指导价',align:'right',field:'guidingPrice',formatter:function(value,row,index){
                        if (value != null) {
                            return value.toFixed(2)
                        }
                        return value
                    }},


                /*[/]*/
                /*{title: '含税应付',align:'right',field:'directPay',formatter:function(value,row,index){
                        if (value != null) {
                            return value.toFixed(2)
                        }
                    }},
                {title: '含税第三方',align:'right',field:'directOtherFee',formatter:function(value,row,index){
                        if (value != null) {
                            return value.toFixed(2)
                        }
                    }},*/

                {title: '单据运营部', field: 'invoiceCustSalesDeptName', align: 'left'},
                {title: '销售员', field: 'salesDeptName', align: 'left'},
                {title: '运输方式', align: 'left', field: 'transName'},
                {title: '询价单号', align: 'left', field: 'enquiryNo'}

                
               
                // {title: '要求到货日', field: 'reqArriDate', align: 'left'},
                // {title: '创建人', field: 'regUserName', align: 'left'},
                // {title: '创建时间', field: 'regDate', align: 'left'},
                // {
                //     title: '应收是否核销',
                //     field: 'receivableWriteOffStatus',
                //     align: 'left',
                //     formatter: function status(value, row) {
                //         let status ='';
                //         switch (row.receivableWriteOffStatus){
                //             case '0':
                //                 status = '未核销';
                //                 break;
                //             case '1':
                //                 status = '部分核销';
                //                 break;
                //             case '2':
                //                 status = '已核销';
                //                 break;
                //         }
                //         return status;
                //     }
                // },
                // {title: '运营组', align: 'left', field: 'salesDeptName'}
            ]
        };

        $.table.init(options);
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#reqDeliDateStart',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#reqDeliDateEnd',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function () {
            var laydate = layui.laydate;

        });
        layui.use('laydate', function () {
            var reqDeliDateStart = layui.laydate;

        });
        layui.use('laydate', function () {
            var reqDeliDateEnd = layui.laydate;

        });

        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
    });

    function invoiceDetail(invoiceId) {

        var url = "invoice/detail/" + invoiceId;
        $.modal.openTab('发货单详情',url);
    }

    /**
     * 修改运费或在途费用
     */
    function editReceive(data) {
        $.ajax({
            url: prefix + "/edit_receive",
            type: "post",
            dataType: "json",
            data: data,
            success: function (result) {
                if (result.code === 0) {
                    var data = result.data;
                    //刷新
                    $.btTable.bootstrapTable('refresh', {
                        silent: true
                    });
                } else {
                    $.modal.msgError(result.msg);
                    //刷新
                    $.btTable.bootstrapTable('refresh', {
                        silent: true
                    });
                }
            }
        });
    }

    /**
     * 修改客户发货单号
     */
    function editCustOrderno(data) {
        $.ajax({
            url: prefix + "/edit_order_no",
            type: "post",
            dataType: "json",
            data: data,
            success: function (result) {
                if (result.code === 0) {
                    var data = result.data;
                    //刷新
                    $.btTable.bootstrapTable('refresh', {
                        silent: true
                    });
                } else {
                    $.modal.msgError(result.msg);
                    //刷新
                    $.btTable.bootstrapTable('refresh', {
                        silent: true
                    });
                }
            }
        });
    }

    //验证字符串是否是数字
    function checkNumber(val) {
        if(val === "" || val ==null){
            return false;
        }
        if(!isNaN(val)){
            return true;
        }
        else{
            return false;
        }
    }
    //获取字符长度
    function getStringLen(str) {
        var len = 0;
        for (var i = 0; i < str.length; i++) {
            str.charCodeAt(i) > 255 ? len += 2 : len += 1;
        }
        return len;
    }
    function plus(n1, n2) { // +运算
        return new BigNumber(n1).dp(10).plus(new BigNumber(n2).dp(10)).dp(10).toNumber()
    }
    function minus(n1, n2) { // -运算
        return new BigNumber(n1).dp(10).minus(new BigNumber(n2).dp(10)).dp(10).toNumber()
    }
    function times(n1, n2) {
        return new BigNumber(n1).dp(10).times(new BigNumber(n2).dp(16)).toNumber()
    }
    function div(n1, n2, dp) { // 除运算（被除数，除数，保留小数）
        if (dp == undefined) {
            dp = 2;
        }
        return new BigNumber(n1).dp(10).div(new BigNumber(n2).dp(10)).dp(dp).toNumber()
    }

    /**
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        data.params = new Map();
        data.params.invoice_vbillstatus = $.common.join($('#invoice_vbillstatus').selectpicker('val'));
        //data.params.receiveVbillstatus = $.common.join($('#receiveVbillstatus').selectpicker('val'));
        data.transCode = $.common.join($('#transCode').selectpicker('val'));
        data.salesDeptId = $.common.join($('#salesDeptId').selectpicker('val'));

        $.ajax({
            url: ctx + "receivableReconciliation/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result.data;

                if (result.code == 0 && data != undefined) {
                    //总金额
                    $("#allTransFeeCount").text(data.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //实际总应收
                    $("#actualReceiptAmount").text(data.actualReceiptAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //调整金额
                    $("#allAdjustAmount").text(data.adjustAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //运费类型金额
                    $("#allFreightTotal").text(data.freightTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //在途类型金额
                    $("#allOnWayTotal").text(data.onWayTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //总成本
                    $("#allCostCount").text(data.costCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //运费总成本
                    $("#allCostCountFreight").text(data.costCountFreight.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //在途总成本
                    $("#allCostCountOnWay").text(data.costCountOnWay.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //第三方应付
                    $("#allOtherFeeCount").text(data.otherFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //管理费
                    //$("#allManagerFeeCount").text(data.managerFee.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //毛利
                    //$("#allGrossProfit").text(data.grossProfit.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //$("#noAllGrossProfit").text(data.noGrossProfit.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //毛利率
                  /*  $("#grossProfitMargin").text(data.grossProfitMargin.toLocaleString('zh', {style: 'percent',
                        minimumFractionDigits: '2'}));*/
                    //不含管理费毛利率
                    //$("#noGrossProfitMargin").text(data.noGrossProfitMargin.toLocaleString('zh', {style: 'percent', minimumFractionDigits: '2'}));
                    $('#netProfitsRateAll').text(div(data.netProfits,data.netTotalReceiveAmount,4).toLocaleString('zh', {style: 'percent', minimumFractionDigits: '2'}))
                    $('#netProfitsAll').text(data.netProfits.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                }
            }
        });
    }

    /**
     * 将总计金额清零
     */
    function clearTotal() {
        receiptAmountTotal = 0;//总应收
        actualReceiptAmountTotal = 0;//实际总应收
        adjustAmountTotal = 0;//调整后应收
        receiptAmountFreightTotal = 0;//运费总应收
        receiptAmountOnWayTotal = 0;//在途总应收
        costCountTotal = 0;//总成本
        costCountFreightTotal = 0;//运费总成本
        costCountOnWayTotal = 0;//在途总成本
        otherFeeCountTotal = 0;//第三方应付
        managerFeeCountTotal = 0;//管理费
        grossProfitTotal = 0;//含管理费毛利
        percentageTotal = 0;//含管理费毛利率
        //noGrossProfitTotal = 0 //不含管理费毛利
        //noPercentageTotal = 0;//不含管理费毛利率
        netProfitsTotal = 0; // 未税利润
        netTotalReceiveAmountTotal = 0; // 未税总应收
        netProfitsRateTotal = 0; // 未税利润率
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     * 累计总金额
     */
    function addTotal(row) {
        receiptAmountTotal = receiptAmountTotal + row.receiptAmount;//总应收
        actualReceiptAmountTotal = actualReceiptAmountTotal + row.actualReceiptAmount;//实际总应收
        adjustAmountTotal = adjustAmountTotal + row.adjustAmount;//调整后应收
        receiptAmountFreightTotal = receiptAmountFreightTotal + row.receiptAmountFreight;//运费总应收
        receiptAmountOnWayTotal = receiptAmountOnWayTotal + row.receiptAmountOnWay;//在途总应收
        costCountTotal = costCountTotal + row.costCount;//总成本
        costCountFreightTotal = costCountFreightTotal + row.costCountFreight;//运费总成本
        costCountOnWayTotal = costCountOnWayTotal + row.costCountOnWay;//在途总成本
        otherFeeCountTotal = otherFeeCountTotal + row.otherFeeCount;//第三方应付
        managerFeeCountTotal = managerFeeCountTotal + row.managerFee;//管理费
        //grossProfitTotal = grossProfitTotal + row.grossProfit;//毛利
        //noGrossProfitTotal = noGrossProfitTotal + row.noGrossProfit;//不含管理费毛利
        //毛利率
        //if(actualReceiptAmountTotal === 0){
        //    percentageTotal = 0
        //    noPercentageTotal = 0
        //}else{
        //    percentageTotal = grossProfitTotal/actualReceiptAmountTotal;
        //    noPercentageTotal = noGrossProfitTotal/actualReceiptAmountTotal;
        //}

        netProfitsTotal = plus(netProfitsTotal, row.netProfits); // 未税利润
        netTotalReceiveAmountTotal = plus(netTotalReceiveAmountTotal, row.netTotalReceiveAmount); // 未税总应收
    }

    function subTotal(row) {
        receiptAmountTotal = receiptAmountTotal - row.receiptAmount;//总应收
        actualReceiptAmountTotal = actualReceiptAmountTotal - row.actualReceiptAmount;//实际总应收
        adjustAmountTotal = adjustAmountTotal - row.adjustAmount;//调整后应收
        receiptAmountFreightTotal = receiptAmountFreightTotal - row.receiptAmountFreight;//运费总应收
        receiptAmountOnWayTotal = receiptAmountOnWayTotal - row.receiptAmountOnWay;//在途总应收
        costCountTotal = costCountTotal - row.costCount;//总成本
        costCountFreightTotal = costCountFreightTotal - row.costCountFreight;//运费总成本
        costCountOnWayTotal = costCountOnWayTotal - row.costCountOnWay;//在途总成本
        otherFeeCountTotal = otherFeeCountTotal - row.otherFeeCount;//第三方应付
        managerFeeCountTotal = managerFeeCountTotal - row.managerFee;//管理费
        //grossProfitTotal = grossProfitTotal - row.grossProfit;//毛利
        //noGrossProfitTotal = noGrossProfitTotal - row.noGrossProfit;//不含管理费毛利
        //毛利率
        //if(actualReceiptAmountTotal === 0){
        //    percentageTotal = 0
        //    noPercentageTotal = 0
        //}else{
        //    percentageTotal = grossProfitTotal/actualReceiptAmountTotal;
        //    noPercentageTotal = noGrossProfitTotal/actualReceiptAmountTotal;
        //}
        netProfitsTotal = minus(netProfitsTotal, row.netProfits); // 未税利润
        netTotalReceiveAmountTotal = minus(netTotalReceiveAmountTotal, row.netTotalReceiveAmount); // 未税总应收
    }

    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#receiptAmountTotal").text(receiptAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#actualReceiptAmountTotal").text(actualReceiptAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#adjustAmountTotal").text(adjustAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#receiptAmountFreightTotal").text(receiptAmountFreightTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#receiptAmountOnWayTotal").text(receiptAmountOnWayTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#costCountTotal").text(costCountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#costCountFreightTotal").text(costCountFreightTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#costCountOnWayTotal").text(costCountOnWayTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#otherFeeCountTotal").text(otherFeeCountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#managerFeeCountTotal").text(managerFeeCountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        //含管理费
        //$("#grossProfitTotal").text(grossProfitTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        //$("#percentageTotal").text(percentageTotal.toLocaleString('zh', {style: 'percent', minimumFractionDigits: '2'}));
        //console.log(noPercentageTotal);
        //不含管理费
        //$("#noGrossProfitTotal").text(noGrossProfitTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        //$("#noPercentageTotal").text(noPercentageTotal.toLocaleString('zh', {style: 'percent', minimumFractionDigits: '2'}));

        $('#netProfitsTotal').text(netProfitsTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'})); // 未税利润
        let rate = 0; // 未税利润率
        if (netTotalReceiveAmountTotal != 0) {
            rate = netProfitsTotal/netTotalReceiveAmountTotal;
        }
        $("#netProfitsRateTotal").text(rate.toLocaleString('zh', {style: 'percent', minimumFractionDigits: '2'}));
    }
    /**
     * 搜索的方法
     */
    function searchPre() {
        if ($("#reqDeliDateStart").val().trim() == '') {
            $.modal.msg("请选择要求提货日期起始日期", modal_status.WARNING);
            return;
        }
        var tmpEndDate =[[${#dates.format(#dates.createToday(), 'yyyy-MM-dd')}]]
        if ($("#reqDeliDateEnd").val().trim() != '') {
            tmpEndDate = $("#reqDeliDateEnd").val().trim();
        }
        var ymd1 = $("#reqDeliDateStart").val().trim().split("-");
        var ymd2 = tmpEndDate.split("-");
        if ((ymd2[0] - ymd1[0]) * 12 + (ymd2[1] - ymd1[1]) + (ymd2[2] - ymd1[2]) / 31 > 6) {
            $.modal.msg("要求提货日期跨度不能超过6个月", modal_status.WARNING);
            return;
        }

        var data = {};
        data.params = new Map();
        data.params.invoice_vbillstatus = $.common.join($('#invoice_vbillstatus').selectpicker('val'));
        //data.params.receiveVbillstatus = $.common.join($('#receiveVbillstatus').selectpicker('val'));
        // data.params.receivableWriteOffStatusList = $.common.join($('#receivableWriteOffStatusList').selectpicker('val'));

        data.transCode = $.common.join($('#transCode').selectpicker('val'));

        data.salesDeptId = $.common.join($('#salesDeptId').selectpicker('val'));

        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }



    /**
     * 导出
     */
    function exportExcel() {
        $.modal.confirm("确定导出所有应收对账吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            var search = $.common.formToJSON("role-form");
            search.params = new Map();
            search.params.invoice_vbillstatus = $.common.join($('#invoice_vbillstatus').selectpicker('val'));
            //search.params.receiveVbillstatus = $.common.join($('#receiveVbillstatus').selectpicker('val'));
            search.salesDeptId = $.common.join($('#salesDeptId').selectpicker('val'));
            search.transCode = $.common.join($('#transCode').selectpicker('val'));

            $.post(prefix + "/export", search, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });

    }

    function exportExcelTemplate() {
        // $.modal.confirm("确定导出所有应收对账吗？", function() {
        //     $.modal.loading("正在导出数据，请稍后...");
        //     var search = $.common.formToJSON("role-form");
        //     search.params = new Map();
        //     search.params.invoice_vbillstatus = $.common.join($('#invoice_vbillstatus').selectpicker('val'));
        //     //search.params.receiveVbillstatus = $.common.join($('#receiveVbillstatus').selectpicker('val'));
        //     search.salesDeptId = $.common.join($('#salesDeptId').selectpicker('val'));

        //     $.post(prefix + "/export", search, function(result) {
        //         if (result.code == web_status.SUCCESS) {
        //             window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
        //         } else if (result.code == web_status.WARNING) {
        //             $.modal.alertWarning(result.msg)
        //         } else {
        //             $.modal.alertError(result.msg);
        //         }
        //         $.modal.closeLoading();
        //     });
        // });

        $.modal.open("对账模板导出", prefix + "/template/export",'800', '490');
        var ele = document.getElementsByClassName("layui-layer-btn0")[0];
        ele.innerText = "确定导出";
    }

    function adjustExport() {
        var data = $("#role-form").serializeArray();
        var invoiceStatusList = {};
        invoiceStatusList.name = "invoiceStatus"
        invoiceStatusList.value = $.common.join($('#invoice_vbillstatus').selectpicker('val'));
        data.push(invoiceStatusList);
        data.push({name:"params[reqDeliDateStart]", value:$("#reqDeliDateStart").val()})
        data.push({name:"params[reqDeliDateEnd]", value:$("#reqDeliDateEnd").val()})
        data.push({name:"salesDeptId", value:$.common.join($('#salesDeptId').selectpicker('val'))})
        $.modal.confirm("确定导出调整单吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(ctx + "receive/exportAdjust", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }
    function formatTableUnit(value, row, index) {
        return {
            css: {
                "white-space": "nowrap",
                "text-overflow": "ellipsis",
                "overflow": "hidden",
                "max-width": "100px"
            }
        }

    }

    /*function netProfits(invoiceId) {
        $.ajax({
            url: ctx + "receivableReconciliation/netProfits",
            data: 'invoiceId=' + invoiceId,
            type: 'post',
            success: function(result) {
                if (result.code == web_status.SUCCESS) {
                    layer.open({
                        type: 1,
                        title: '净利润明细',
                        area: ['500px', '400px'],
                        btn: ['关闭'],
                        content: `
                            <form class="form-horizontal m">
                            <div class="row">
                                <div class="col-sm-offset-1 col-sm-10">
                                    <div class="form-group">
                                        <label class="col-sm-3">总应收：</label>
                                        <div class="col-sm-6">
                                            <span class="form-control valid">${result.data.ys}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-offset-1 col-sm-10">
                                    <div class="form-group">
                                        <label class="col-sm-3">总运费：</label>
                                        <div class="col-sm-6">
                                            <span class="form-control valid">${result.data.yf}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-offset-1 col-sm-10" style="display: ${result.data.fareMsg ? 'block' : 'none'}">
                                    <div class="form-group">
                                        <label class="col-sm-offset-3 col-sm-6" style="color: #999">${result.data.fareMsg}</label>
                                    </div>
                                </div>
                                <div class="col-sm-offset-1 col-sm-10">
                                    <div class="form-group">
                                        <label class="col-sm-3">第三方费用：</label>
                                        <div class="col-sm-6">
                                            <span class="form-control valid">${result.data.dsf}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-offset-1 col-sm-10" style="display: ${result.data.otherMsg ? 'block' : 'none'}">
                                    <div class="form-group">
                                        <label class="col-sm-offset-3 col-sm-6" style="color: #999">${result.data.otherMsg}</label>
                                    </div>
                                </div>
                                <div class="col-sm-offset-1 col-sm-10">
                                    <div class="form-group">
                                        <label class="col-sm-3">平台费：</label>
                                        <div class="col-sm-6">
                                            <span class="form-control valid">${result.data.ptf}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-offset-1 col-sm-10" style="color: #006dcc">
                                净利润 = 总应收 - 总运费 - 第三方费用 - 平台费<br>
                                ※各费用按各自税率去除税费计算
                                </div>
                            </div>
                            </form>
                        `,
                        yes: function(index, layero){
                            layer.close(index);
                        }
                    });
                } else {
                    $.modal.msgError(result.msg);
                }
            }
        })
    }*/
    function batchAdjust() {
        var idList = $.table.selectColumns("invoiceId");
        var customerIds = $.table.selectColumns("customerId"); // 去重后的结果
        if (customerIds.length > 1) {
            $.modal.msgWarning("不同客户请分开调账")
            return
        }
        var url = ctx + "batch-adjust/add?ids=" + idList.join(',')
        $.modal.openTab("批量调账", url);
    }
</script>
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a href="file/ReceiveAdjustModel.xlsx" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</body>
</html>