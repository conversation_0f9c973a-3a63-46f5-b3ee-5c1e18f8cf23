<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运商列表')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<style>
    .container-div {
        padding: 0px 15px;
    }

    .search-collapse, .select-table {
        margin: 0;
        border-radius: 0;
        padding: 5px;
    }

    .search-collapse {
        background-color: #F7F7F7;
    }

    .form-group {
        margin: 0;
    }

    .row + .row {
        margin-top: 5px;
    }

    .btn-group-sm > .btn, .btn-sm {
        padding: 3px 10px;
    }

    .table-striped {
        height: calc(100% - 100px);
        padding-top: 0;
    }

    .left-fixed-body-columns {
        z-index: 10;
    }

    .dropdown-menu {
        min-width: 50px;
    }

    .flex {
        display: flex;
        align-items: center;
    }

    .label-inverse {
        background-color: rgba(38, 38, 38, .4);
    }

    .pa2 {
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
        min-width: 16px;
    }

</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row no-gutter">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">承运商名称：</label>-->
                            <div class="col-sm-12">
                                <input name="carrName" placeholder="承运商名称/承运商简称" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <select name="infoCheckStatus" class="form-control"
                                th:with="type=${@dict.getType('check_status')}">
                            <option value="">&#45;&#45;信息审核状态&#45;&#45;</option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                    th:value="${dict.dictValue}"
                                    th:selected="${checkStatus == dict.dictValue}"></option>
                        </select>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <select name="carrType" class="form-control" th:with="type=${@dict.getType('check_status')}">
                            <option value="">&#45;&#45;承运商类别&#45;&#45;</option>
                            <option value="0">自有车队</option>
                            <option value="2">外协车队</option>
                            <option value="3">物流专线</option>
                        </select>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <input name="recommendMan" placeholder="推荐人" class="form-control valid" type="text"
                               aria-required="true">
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <input name="phone" placeholder="请输入手机" class="form-control valid" type="text"
                               aria-required="true">
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <input name="legalCard" placeholder="请输入身份证号码" class="form-control valid" type="text"
                               aria-required="true">
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="lockPay" class="form-control">
                                    <option value="">-锁定-</option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <select name="isblaclklist" class="form-control">
                            <option value="">-黑名单-</option>
                            <option value="0">否</option>
                            <option value="1">是</option>
                        </select>
                    </div>
                    <div class="col-md-1 col-sm-2">
                        <select name="ifHasMargin" class="form-control">
                            <option value="-1">-保证金-</option>
                            <option value="0">无</option>
                            <option value="1">有</option>
                        </select>
                    </div>
                    <div class="col-md-1 col-sm-2">
                        <select name="ifOil" class="form-control">
                            <option value="-1">-油卡-</option>
                            <option value="0">否</option>
                            <option value="1">是</option>
                        </select>
                    </div>
                    <div class="col-md-1 col-sm-2">
                        <select name="ifHasContract" class="form-control">
                            <option value="-1">-合同-</option>
                            <option value="0">无</option>
                            <option value="1">有</option>
                        </select>
                    </div>
                    <div class="col-md-1 col-sm-2">
                        <select name="ifBillingType" class="form-control">
                            <option value="">-开票-</option>
                            <option value="1">否</option>
                            <option value="2">是</option>
                        </select>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <input name="billingPayable" placeholder="请输入发票抬头" class="form-control valid" type="text"
                               aria-required="true">
                    </div>

                    <div class="col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>


                </div>


            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <!-- <a class="btn btn-primary" onclick="$.operate.addTab()" shiro:hasPermission="basic:carrier:add">
                 <i class="fa fa-plus"></i> 新增
             </a>
             <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
                shiro:hasPermission="basic:carrier:remove">
                 <i class="fa fa-remove"></i> 删除
             </a>
 -->
            <a class="btn btn-primary single disabled" onclick="infoCheck()" shiro:hasPermission="tms:fixCarrier:check">
            <i class="fa fa-check"></i>信息审核
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="basic:carrier:fixExport">
                <i class="fa fa-download"></i> 导出
            </a>
            <!--<a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="basic:carrier:import">
                <i class="fa fa-upload"></i> 导入
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="basic:carrier:export">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-success single disabled" onclick="contract()"
               shiro:hasPermission="basic:carrier:contract">
                <i class="fa fa-clone"></i> 运单合同
            </a>-->

        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:inline="javascript">
    var prefix = ctx + "basic/carrier";
    //优先级
    var priority = [[${@dict.getType('priority')}]];
    //开票类型
    var billingType = [[${@dict.getType('billing_type')}]];
    //承运商类别
    var carrierType = [[${carrierType}]];
    //权限
    var editFlag = [[${@permission.hasPermi('basic:fixCarrier:edit')}]];
    var detailFlag = [[${@permission.hasPermi('basic:carrier:detail')}]];
    var detailSaveFlag = [[${@permission.hasPermi('basic:carrier:detailSave')}]];
    //运输类型
    var transportType = [[${@dict.getType('transport_type')}]];

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                $.table.search();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });
        });

        // 初始化省市区
        $.provinces.init("provinceId", "cityId", "areaId");

        var options = {
            url: prefix + "/fixList",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            removeUrl: prefix + "/removeCarr",
            exportUrl: prefix + "/fixExport",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            clickToSelect: true,
            showToggle: false,
            showColumns: true,
            modalName: "承运商",
            fixedColumns: true,
            height: 560,
            fixedNumber: 0,
            uniqueId: "carrierId",
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'center',
                    field: 'carrierId',
                    formatter: function (value, row, index) {
                        var actions = [];
                        var actionsT = [];
                        actions.push('<a class="btn btn-xs ' + editFlag + ' " href="javascript:void(0)" title="修改" onclick="edit(\'' + row.carrierId + '\',\'' + row.checkStatus + '\')"><i class="fa fa-edit" style="font-size: 15px;"></i></a> ');
                        if ([[${@permission.hasPermi('basic:fixCarrier:carrierUser')}]] != "hidden") {
                            actionsT.push('<a class="btn btn-xs  " href="javascript:void(0)" title="承运商账号"onclick="accountList(\'' + row.carrierId + '\')"><i class="fa fa-user" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:fixCarrier:adminConfig')}]] != "hidden") {
                            actionsT.push('<a class="btn btn-xs  " href="javascript:void(0)" title="管理员配置" onclick="ifHasBill(\'' + row.carrierId + '\')"><i class="fa fa-file-powerpoint-o" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:fixCarrier:billEdit')}]] != "hidden") {
                            actionsT.push('<a class="btn btn-xs  " href="javascript:void(0)" title="开票信息" onclick="billEdit(\'' + row.carrierId + '\')"><i class="fa fa-shopping-bag" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:fixCarrier:blackAll')}]] != "hidden") {
                            actionsT.push('<a class="btn btn-xs  " href="javascript:void(0)" title="批量黑名单" onclick="blackAll(\'' + row.carrierId + '\')"><i class="fa fa-bell-slash" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:fixCarrier:moneyPrint')}]] != "hidden") {
                            actionsT.push('<a class="btn btn-xs  " href="javascript:void(0)" title="打印" onclick="prints(\'' + row.carrierId + '\')"><i class="fa fa-print" style="font-size: 15px;"></i></a>');
                        }
                        actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="明细" onclick="detail(\'' + row.carrierId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        return actions.join('') + '<div class="btn-group btn-xs">' +
                            '<div class="dropdownpad" data-toggle="dropdown">' +
                            '<i class="fa fa-angle-down"></i>' +
                            '</div>' +
                            '<ul class="dropdown-menu">' + actionsT.join('') +
                            '</ul>' +
                            '</div>';
                    }

                },


                {
                    title: '承运商全称',
                    align: 'left',
                    field: 'carrName',
                    formatter:function status(value, row) {
                        let html='';
                        if(row.isblaclklist==1){
                            html='<span>'+value+'&nbsp;&nbsp;'+'</span>' + '<span  class="label label-danger"   data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+row.blackMemo+'">黑名单</span>'
                        }else {
                            html= '<span>'+value+'</span>'
                        }
                        html += '</br><span>'+row.carrCode+'</span>'
                        if(row.lockPay==1){
                            html+='<br/><span class="label label-danger"  data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+row.lockPayReason+'">锁定应付</span>'
                        }
                        return html;
                    }
                },
                {
                    title: '承运商简称',
                    align: 'left',
                    field: 'carrAbbr',
                    formatter:function status(value, row) {
                        let html = '';
                        html += `<span>`+$.table.tooltip(value)+`</span>`;
                        if(row.memo != '' && row.memo != null){
                            html += '<i class="fa fa-question-circle" data-toggle="tooltip" data-container="body" style="font-size: 15px;" data-html="true" title="'+row.memo+'"></i>'
                        }
                        html += `<br/><span>`+$.table.tooltip(row.legalCard)+`</span>`;
                        return html;
                    }
                },
                {
                    title: '引荐人',
                    align: 'left',
                    field: 'recommendMan'
                },
                {
                    title: '信息审核状态',
                    align: 'left',
                    field: 'infoCheckStatus',
                    formatter: function (value, item, index) {
                        if (item.infoCheckStatus == 0) {
                            return '<span class="carve carve-primary">待审核</span>';
                        } else if (item.infoCheckStatus == 1) {
                            return '<span class="carve carve-success">审核通过</span>';
                        } else if (item.infoCheckStatus == 2) {
                            return '<span class="carve carve-danger">审核未通过</span>';
                        } else if (item.infoCheckStatus == 3) {
                            return '<span class="carve carve-white">新建</span>';
                        }
                    }

                },
                {
                    title: '联系人/联系方式',
                    align: 'left',
                    field: 'contact',
                    formatter: function (value, item, index) {
                        return $.table.tooltip(value) + '<br/>' + $.table.tooltip(item.phone);
                    }
                },


                {
                    title: '承运商类型',
                    align: 'left',
                    field: 'carrType',
                    formatter: function status(row, value) {
                        var context = '';

                        if (value.balaType == 1) {
                            context += "<div class='flex'><span class='label label-success pa2 mr5'>单</span><div>";
                        } else if (value.balaType == 2) {
                            context += "<div class='flex'><span class='label label-warning pa2 mr5'>月</span><div>";
                        }

                        carrierType.forEach(function (v) {
                            if (v.value == value.carrType) {
                                context += v.context;
                                return false;
                            }
                        });
                        context += "<br/>" + $.table.selectDictLabel(transportType, value.transportType) + "</div></div>";


                        return context;
                    }
                },
                {
                    title: '银行卡',
                    align: 'center',
                    field: 'bankCount',
                    formatter: function (value, row) {
                        return '<a href="javascript:;" onclick="bankList(\'' + row.carrierId + '\')"> ' + value + '张</a>';
                    }
                },
                {
                    title: '运力',
                    align: 'left',
                    field: 'carCnt',
                    formatter: function (value, row) {
                        let htmlText = [];
                        if (value) {
                            htmlText.push('<div><img src="/img/cl.png" style="width: 20px;height: 20px;"><a href="javascript:;" onclick="openCarCarrier(\'' + row.carrierId + '\')"> ' + value + "辆</a></div>")
                        }
                        if (row.driverCnt) {
                            htmlText.push('<div class="mt5"><img src="/img/sj.png" style="width: 20px;height: 20px;"><a href="javascript:;" onclick="openDriverCarrier(\'' + row.carrierId + '\')"> ' + row.driverCnt + "位</a></div>")
                        }

                        return htmlText.join("")
                    }
                },
                {
                    title: '保证金余额',
                    align: 'right',
                    field: 'marginLess',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '结算账期',
                    align: 'left',
                    field: 'balaTime'
                },
                {
                    title: '付款方式',
                    align: 'left',
                    field: 'billingType',
                    formatter: function status(value, row, index) {
                        let html = ``;
                        if (row.oilCardRate && row.oilCardRate != null) {
                            html += `<span>油卡比例：` + row.oilCardRate + `%</span></br>`;
                        }

                        html += `<span>` + $.table.selectDictLabel(billingType, value) + `</span>`;
                        return html;
                    }
                },
                {
                    title: '是否有合同',
                    align: 'left',
                    field: 'ifHasContract',
                    formatter: function status(value, row, index) {
                        if (value == 0) {
                            return '否'
                        } else if (value == 1) {
                            return '是'
                        }
                    }
                },
                {
                    title: '预借/信用额度',
                    align: 'left',
                    field: 'advancePayMoney',
                    formatter: function (value, row) {
                        let htmlText = [];
                        if (value) {
                            htmlText.push(value);
                        } else {
                            htmlText.push('-');
                        }
                        if (row.advancePayMoneyQuota) {
                            htmlText.push(row.advancePayMoneyQuota);
                        } else {
                            htmlText.push('-');
                        }
                        return htmlText.join("/");
                    }
                },
                {
                    title: '异常数量',
                    align: 'center',
                    field: 'carrierExpCount',
                    formatter: function(value, row, index) {
                        let htmlText=[];
                        if(value){
                            htmlText.push('<div><a href="javascript:;" onclick="openCarrierException(\''+row.carrierId+'\')"> ' + value + "</a></div>")
                        }
                        return htmlText.join("")
                    }
                },
                {
                    title: '创建信息',
                    align: 'left',
                    field: 'regUserId',
                    formatter: function(value, item, index) {
                        return $.table.tooltip(value)+'<br/>'+ $.table.tooltip(item.regDate);
                    }
                },

                {
                    title: '修改信息',
                    align: 'left',
                    field: 'corUserId',
                    formatter: function (value, item, index) {
                        return $.table.tooltip(value) + '<br/>' + $.table.tooltip(item.corDate);
                    }
                },
                {
                    title: '审核信息',
                    align: 'left',
                    field: 'infoCheckMan',
                    formatter: function status(value, row) {
                        return $.table.tooltip(value) + '<br/>' + $.table.tooltip(row.infoCheckDate);
                    }
                }
            ]
        };
        $.table.init(options);
    });

    function getValue(val) {
        if (val == null) {
            val = "/";
        }
        return val
    }

    //审核
    function check() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var url = prefix + "/check";
        //获取状态
        var checkStatusArr = $.btTable.bootstrapTable('getSelections');
        //循环判断是否能够进行审核
        for (var i = 0; i < checkStatusArr.length; i++) {
            if (checkStatusArr[i]['checkStatus'] != 0 && checkStatusArr[i]['checkStatus'] != 3) {
                $.modal.alertWarning("请选择待审核或新建数据进行审核操作");
                return;
            }
            /* if(checkStatusArr[i]['checkStatus'] == 2){
                 var flag = false;
                 layer.confirm("请审核选中的" + rows.length + "条数据",{
                     btn:["待审核"]
                 },function (index, layero) {
                     if(!flag){
                         flag = true;
                         layer.close(index);
                         var data = { "ids": rows.join(),"flag":"0" };
                         $.operate.submit(url, "post", "json", data);
                     }

                 })
                 return ;
             }*/
        }

        /* var flag = false;
         layer.confirm("请审核选中的" + rows.length + "条数据",{
             btn:["通过","不通过"]
         },function (index, layero) {
             if(!flag){
                 flag = true;
                 layer.close(index);
                 var data = { "ids": rows.join(),"flag":"1" };
                 $.operate.submit(url, "post", "json", data);
             }
         },function (index) {
             if(!flag){
                 flag = true;
                 layer.close(index);
                 var data = { "ids": rows.join(),"flag":"2" };
                 $.operate.submit(url, "post", "json", data);
             }
         });*/

        var type = 'detailSave';
        var url = prefix + "/detailMyCarrier?carrierId=" + rows.join() + "&type=" + type;
        $.modal.openTab('承运商证件审核', url);
    }

    function infoCheck() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var url = prefix + "/infoCheck?carrierId=" + rows.join()+"&type=0";
        $.modal.openTab('承运商信息审核', url);
    }


    function detail(id) {
        var url = prefix + "/infoCheck?carrierId=" + id+"&type=1";
        $.modal.openTab('承运商明细', url);
    }

    /**
     * 明细修改
     * @param id
     */
    function detailSave(id) {
        var type = 'detailSave';
        var url = prefix + "/detail?carrierId=" + id + "&type=" + type;
        $.modal.openTab('承运商明细修改', url);
    }


    //承运商账号
    function accountList(id) {
        var url = ctx + "basic/carrierUser/list/" + id;
        $.modal.openTab('承运商账号', url);
    }

    //申请入驻
    function applyJoin(id) {
        var url = prefix + "/applyJoin/" + id;
        var title = '申请入驻';
        layer.open({
            type: 2,
            area: ['600px', '600px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: title,
            content: url,
            btn: ['打印', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function (index, layero) {
                $(layero).find("iframe")[0].contentWindow.submitHandler();
            },
            cancel: function (index) {
                return true;
            }
        });
    }

    //承运商银行卡
    function bankList(id) {
        var url = ctx + "g7/payee/"+id;
        $.modal.openTab('承运商银行卡',url);
    }

    function openCarrierException(carrierId) {
        var url = prefix + "/to_carrier_exception/" + carrierId;
        $.modal.openTab("异常", url);
    }

    //修改结算方式
    // function editBalaType(id) {
    //     var url = prefix + "/editBalaType/"+id;
    //     $.modal.open('修改结算方式',url,500,300);
    // }

    // function shipExamine(id){
    //     var url = prefix + "/shipExamine/"+id;
    //     $.modal.open('修改跳过验证',url,500,300);
    // }

    function ifHasBill(id) {
        var url = prefix + "/ifHasBill/" + id;
        $.modal.open('管理员配置', url, 700, $(window).height() - 50);
    }

    /**
     * 调整额配置
     */
    // function adjustmentConfig(id){
    //     $.modal.open("调整额配置", prefix + "/adjustmentConfig?carrierId="+id,400,300);
    // }


    function edit(id, checkStatus) {
        var url = prefix + "/fixEdit/" + id;
        $.modal.openTab('承运商修改', url);
    }

    //运单合同
    function contract() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        var url = prefix + "/contract/" + rows.join();
        $.modal.openTab("运单合同", url);
    }

    function addLayer() {
        var url = ctx + "basic/carrier/carrierLayer";
        $.modal.open("新增承运商", url);
    }

    function openCarCarrier(carrierId) {
        var url = prefix + "/carrierCar/" + carrierId;
        $.modal.openTab("承运商车辆", url);
    }

    function openDriverCarrier(carrierId) {
        var url = prefix + "/carrierDriver/" + carrierId;
        $.modal.openTab("承运商司机", url);
    }

    function billEdit(carrierId) {
        var url = ctx + "basic/carrier/billEdit?carrierId=" + carrierId;
        $.modal.openTab("开票信息", url);
    }

    function blackAll(id){
        var url = prefix + "/blackAll/"+id;
        $.modal.open('批量黑名单', url, 750, $(window).height() - 50);
    }
    function prints(id){
        let iframe = document.getElementById("print-frame");
        if (!iframe) {
            iframe = document.createElement('IFRAME');
            iframe.id = "print-frame"
            document.body.appendChild(iframe);
            iframe.setAttribute('style', 'display:none;');
        }
        iframe.src = prefix + "/applyPrint?id="+id;
        iframe.onload = function () { //解决图片显示不了的问题
            iframe.contentWindow.focus();
            iframe.contentWindow.print();
        };
    }

</script>

</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp; <a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i
                    class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>