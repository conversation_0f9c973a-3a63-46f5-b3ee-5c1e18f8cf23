<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运商列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 100px);
        padding-top: 0;
    }
    .left-fixed-body-columns{
        z-index: 10;
    }
    .dropdown-menu{
        min-width: 50px;
    }
    .flex{
        display: flex;
        align-items: center;
    }
    .label-inverse{
        background-color: rgba(38, 38, 38, .4);
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
        min-width: 16px;
    }

</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row no-gutter">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">运输类型:</label>-->
                            <div class="col-sm-12">
                                <select name="transportType" class="form-control" th:with="type=${@dict.getType('transport_type')}" required >
                                    <option value="">--请选择运输类型--</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">审核状态 ：</label>-->
                            <div class="col-sm-12">
                                <select name="checkStatus"  class="form-control"  th:with="type=${@dict.getType('check_status')}">
                                    <option value="">&#45;&#45;证件审核状态&#45;&#45;</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}" th:selected="${checkStatus == dict.dictValue}"></option>
                                </select>
                            </div>

                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">承运商名称：</label>-->
                            <div class="col-sm-12">
                                <input name="carrName" placeholder="承运商编码/承运商名称" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>


                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">承运商类别 ：</label>-->
                            <div class="col-sm-12">
                                <select name="carrType" class="form-control" required
                                        th:with="type=${carrierType}">
                                    <option value="">--承运商类别--</option>
                                    <option th:each="dict : ${type}" th:text="${dict.context}"
                                            th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select  name="balaType" class="form-control">
                                    <option value="">-- 结算方式 --</option>
                                    <option value="1">单笔承运商</option>
                                    <option value="2">月结承运商</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select  name="setTransportPool" class="form-control">
                                    <option value="">-- 是否入驻运力池 --</option>
                                    <option value="1">否</option>
                                    <option value="2">是</option>
                                </select>
                            </div>
                        </div>
                    </div>


                </div>
                <div class="row no-gutter">
<!--                    <div class="col-md-2 col-sm-4">-->
<!--                        <div class="form-group">-->
<!--                            &lt;!&ndash;                            <label class="col-sm-4">承运商编码：</label>&ndash;&gt;-->
<!--                            <div class="col-sm-12">-->
<!--                                <input name="carrCode" placeholder="请输入承运商编码" class="form-control valid" type="text"-->
<!--                                       aria-required="true">-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">手机：</label>-->
                            <div class="col-sm-12">
                                <input name="phone" placeholder="请输入手机" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">身份证号码：</label>-->
                            <div class="col-sm-12">
                                <input name="legalCard" placeholder="请输入身份证号码" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">创建人：</label>-->
                            <div class="col-sm-12">
                                <input name="corUserId" placeholder="请输入创建人" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-2">创建时间：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  name="regDate" placeholder="创建开始时间">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="endtDate"  name="corDate" placeholder="创建结束时间">
                            </div>
                        </div>
                    </div>


                     <div class="col-sm-3">
                        <div class="form-group">
                           <!--<label class="col-sm-4">跳过验证：</label>-->
                            <div class="col-sm-12">
                                    <select  name="ifBillingType" class="form-control">
                                        <option value="">-- 是否开票 --</option>
                                        <option value="1">否</option>
                                        <option value="2">是</option>
                                    </select>
                            </div>
                        </div>
                    </div>
                </div>
                 <div class="row">
                     <div class="col-sm-3">
                         <div class="form-group">
                             <div class="col-sm-12">
                                 <select  name="isblaclklist" class="form-control">
                                     <option value="">-- 是否黑名单 --</option>
                                     <option value="0">否</option>
                                     <option value="1">是</option>
                                 </select>
                             </div>
                         </div>
                     </div>
                     <div class="col-sm-3">
                         <div class="form-group">
                             <div class="col-sm-12">
                                 <select  name="lockPay" class="form-control">
                                     <option value="">-- 是否锁定 --</option>
                                     <option value="0">否</option>
                                     <option value="1">是</option>
                                 </select>
                             </div>
                         </div>
                     </div>
                     <div class="col-sm-2">
                         <div class="form-group">
                             <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                             <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                         </div>
                     </div>
                </div>



            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="$.operate.addTab()" shiro:hasPermission="basic:carrier:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger multiple disabled" onclick="removeAll()"
               shiro:hasPermission="basic:carrier:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-primary single disabled" onclick="check()" shiro:hasPermission="tms:carrier:check">
                <i class="fa fa-check"></i>证件审核
            </a>

            <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="basic:carrier:import">
                <i class="fa fa-upload"></i> 导入
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="basic:carrier:export">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-success single disabled" onclick="contract()"
               shiro:hasPermission="basic:carrier:contract">
                <i class="fa fa-clone"></i> 运单合同
            </a>
            <!--<a class="btn btn-primary" onclick="addLayer()">
                <i class="fa fa-check"></i>承运商弹窗
            </a>-->
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "basic/carrier";
    //优先级
    var priority = [[${@dict.getType('priority')}]];
    //开票类型
    var billingType = [[${@dict.getType('billing_type')}]];
    //承运商类别
    var carrierType = [[${carrierType}]];
    //权限
    var editFlag = [[${@permission.hasPermi('basic:carrier:edit')}]];
    var detailFlag = [[${@permission.hasPermi('basic:carrier:detail')}]];
    var detailSaveFlag = [[${@permission.hasPermi('basic:carrier:detailSave')}]];
    //运输类型
    var transportType = [[${@dict.getType('transport_type')}]];

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });
        });

        // 初始化省市区
        $.provinces.init("provinceId","cityId","areaId");

        var options = {
            url: prefix + "/list?setTransportPool=1",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            removeUrl: prefix + "/removeCarr",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            clickToSelect:true,
            showToggle:false,
            showColumns:true,
            modalName: "承运商",
            fixedColumns: true,
            height: 560,
            fixedNumber:0,
            uniqueId:"carrierId",
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'center',
                    field: 'carrierId',
                    formatter: function(value, row, index) {
                        var actions = [];
                        var actionsT = [];
                        actions.push('<a class="btn btn-xs ' + editFlag + ' " href="javascript:void(0)" title="修改" onclick="edit(\'' + row.carrierId + '\',\''+row.checkStatus+'\')"><i class="fa fa-edit" style="font-size: 15px;"></i></a> ');
                        //actions.push('<a class="btn btn-xs ' + detailFlag + ' " href="javascript:void(0)" title="明细"onclick="detail(\'' + row.carrierId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        //actionsT.push('<a class="btn btn-xs ' + detailSaveFlag + ' " href="javascript:void(0)" title="明细修改"onclick="detailSave(\'' + row.carrierId + '\')"><i class="fa fa-plus-square-o" style="font-size: 15px;"></i></a>');
                        if ([[${@permission.hasPermi('basic:carrierUser:view')}]] != "hidden") {
                            actionsT.push('<a class="btn btn-xs  " href="javascript:void(0)" title="承运商账号"onclick="accountList(\'' + row.carrierId + '\')"><i class="fa fa-user" style="font-size: 15px;"></i></a>');
                        }
                        actionsT.push('<a class="btn btn-xs  " href="javascript:void(0)" title="申请入驻" onclick="applyJoin(\'' + row.carrierId + '\')"><i class="fa fa-handshake-o" style="font-size: 15px;"></i></a>');
                        /*if ([[${@permission.hasPermi('basic:carrBank:view')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="银行卡"onclick="bankList(\'' + row.carrierId + '\')"><i class="fa fa-yen" style="font-size: 15px;"></i></a>');
                        }*/
                        // if ([[${@permission.hasPermi('tms:carrier:editBalaType')}]] != "hidden") {
                        //     actionsT.push('<a class="btn btn-xs  " href="javascript:void(0)" title="结算方式" onclick="editBalaType(\'' + row.carrierId + '\')"><i class="fa fa-cc-paypal" style="font-size: 15px;"></i></a>');
                        // }
                        // if ([[${@permission.hasPermi('basic:carrier:adjustmentConfig')}]] !== "hidden") {
                        //     actionsT.push('<a class="btn btn-xs  " href="javascript:void(0)" title="调整额配置" '
                        //         + 'onclick="adjustmentConfig(\'' + value +'\')">'
                        //         + '<i class="fa fa-key" style="font-size: 15px;"></i></a>');
                        // }
                        // if ([[${@permission.hasPermi('tms:carrier:shipExamine')}]] != "hidden") {
                        //     actionsT.push('<a class="btn btn-xs  " href="javascript:void(0)" title="跳过验证" onclick="shipExamine(\'' + row.carrierId + '\')"><i class="fa fa-shield" style="font-size: 15px;"></i></a>');
                        // }
                        if ([[${@permission.hasPermi('tms:carrier:adminConfig')}]] != "hidden") {
                            actionsT.push('<a class="btn btn-xs  " href="javascript:void(0)" title="管理员配置" onclick="ifHasBill(\'' + row.carrierId + '\')"><i class="fa fa-file-powerpoint-o" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:carrier:blackAll')}]] != "hidden") {
                            actionsT.push('<a class="btn btn-xs  " href="javascript:void(0)" title="批量黑名单" onclick="blackAll(\'' + row.carrierId + '\','+row.isblaclklist+')"><i class="fa fa-bell-slash" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:carrier:moneyPrint')}]] != "hidden") {
                            actionsT.push('<a class="btn btn-xs  " href="javascript:void(0)" title="打印" onclick="prints(\'' + row.carrierId + '\')"><i class="fa fa-print" style="font-size: 15px;"></i></a>');
                        }
                        return actions.join('')+'<div class="btn-group btn-xs">' +
                            '<div class="dropdownpad" data-toggle="dropdown">' +
                            '<i class="fa fa-angle-down"></i>' +
                            '</div>' +
                            '<ul class="dropdown-menu">' + actionsT.join('') +
                            '</ul>' +
                            '</div>';
                    }

                },
                {
                    title: '承运商编码',
                    align: 'left',
                    field: 'carrCode',
                    formatter:function status(value, row) {
                        let html= '<span>'+value+'</span><br/>'
                        if(row.lockPay==1){
                            html+='<span style="margin-left:2px"  class="label label-danger"  data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+row.lockPayReasonUnion+'">锁定应付</span>'
                        }
                        if(row.isblaclklist==1){
                            html+='<span style="margin-left:2px" class="label label-danger"  data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+row.blackMemo+'">黑名单</span>'
                        }
                        return html;
                    }

                },

                {
                    title: '承运商信息',
                    align: 'left',
                    field: 'carrName',
                    formatter:function status(value, row) {
                        return $.table.tooltip(value)+'<br/>'+ $.table.tooltip(row.legalCard);
                    }
                },
                {
                    title: '联系人/联系方式',
                    align: 'left',
                    field: 'contact',
                    formatter: function(value, item, index) {
                        return $.table.tooltip(value)+'<br/>'+ $.table.tooltip(item.phone);
                    }
                },
                {
                    title: '证件审核状态',
                    align: 'left',
                    field: 'checkStatus',
                    formatter: function(value, item, index) {
                        if (item.checkStatus == 0) {
                            return '<span class="carve carve-primary">待审核</span>';
                        }else if (item.checkStatus == 1) {
                            return '<span class="carve carve-success">审核通过</span>';
                        }else if (item.checkStatus == 2) {
                            return '<span class="carve carve-danger">审核未通过</span>';
                        }else if (item.checkStatus == 3) {
                            return '<span class="carve carve-white">新建</span>';
                        }
                    }

                },

                // {
                //     title: '运输类型',
                //     field: 'transportType',
                //     align: 'left',
                //     formatter: function(value, row, index) {
                //         return $.table.selectDictLabel(transportType, value);
                //     }
                // },

                {
                    title: '承运商类型',
                    align: 'left',
                    field: 'carrType',
                    formatter: function status(row,value) {
                        var context = '';

                        if(value.balaType == 1 ){
                            context+= "<div class='flex'><span class='label label-success pa2 mr5'>单</span><div>";
                        }else if(value.balaType == 2){
                            context+= "<div class='flex'><span class='label label-warning pa2 mr5'>月</span><div>";
                        }

                        carrierType.forEach(function (v) {
                            if (v.value == value.carrType) {
                                context += v.context;
                                return false;
                            }
                        });
                        context+= "<br/>"+$.table.selectDictLabel(transportType, value.transportType)+"</div></div>";
                        

                        return context;
                    }
                },
                {
                    title: '银行卡',
                    align: 'center',
                    field: 'bankCount',
                    formatter:function(value,row){
                        return '<a href="javascript:;" onclick="bankList(\''+row.carrierId+'\')"> ' + value + '张</a>';
                    }
                },
                {
                    title: '运力',
                    align: 'left',
                    field: 'carCnt',
                    formatter:function(value,row){
                        let htmlText=[];
                        if(value){
                            htmlText.push('<div><img src="/img/cl.png" style="width: 20px;height: 20px;"><a href="javascript:;" onclick="openCarCarrier(\''+row.carrierId+'\')"> ' + value + "辆</a></div>")
                        }
                        if(row.driverCnt){
                            htmlText.push('<div class="mt5"><img src="/img/sj.png" style="width: 20px;height: 20px;"><a href="javascript:;" onclick="openDriverCarrier(\''+row.carrierId+'\')"> ' + row.driverCnt + "位</a></div>")
                        }

                        return htmlText.join("")
                    }
                },
                {
                    title: '预借/信用额度',
                    align: 'left',
                    field: 'advancePayMoney',
                    formatter:function(value,row){
                        let htmlText=[];
                        if(value){
                            htmlText.push(value);
                        }else{
                            htmlText.push('-');
                        }
                        if(row.advancePayMoneyQuota){
                            htmlText.push(row.advancePayMoneyQuota);
                        }else{
                            htmlText.push('-');
                        }
                        return htmlText.join("/");
                    }
                },

                // {
                //     title: '车辆数量',
                //     align: 'center',
                //     field: 'carCnt',
                //     formatter:function(value,row){
                //         return '<a href="javascript:;" onclick="openCarCarrier(\''+row.carrierId+'\')"> ' + value + "</a>";
                //     }
                // },
                // {
                //     title: '司机数量',
                //     align: 'center',
                //     field: 'driverCnt',
                //     formatter:function(value,row){
                //         return '<a href="javascript:;" onclick="openDriverCarrier(\''+row.carrierId+'\')"> ' + value + "</a>";
                //     }
                // },

                {
                    title: '开票协议配置',
                    align: 'left',
                    field: 'ifHasBill',
                    formatter: function(value, row, index) {
                        let html="";
                        if(value==1){
                            html+=`是`;
                        }else{
                            html+=`否`;
                        }
                        if(row.billingType){
                            html += '/' + $.table.selectDictLabel(billingType, row.billingType)
                        }

                        if(row.freightFeeRate){
                            html+=`<div class="mt5"><span class="label label-inverse mr5">运费加票点</span>`+row.freightFeeRate+`%</div>`
                        }
                        if(row.oilCardRate){
                            html+=`<div class="mt5"><span class="label label-inverse mr5">油卡比例</span>`+row.oilCardRate+`%</div>`
                        }
                        return html;
                    }
                },
                {
                    title: '协议有效期',
                    align: 'left',
                    field: 'expiredDate'
                },
                {
                    title: '异常数量',
                    align: 'center',
                    field: 'carrierExpCount',
                    formatter: function(value, row, index) {
                        let htmlText=[];
                        if(value){
                            htmlText.push('<div><a href="javascript:;" onclick="openCarrierException(\''+row.carrierId+'\')"> ' + value + "</a></div>")
                        }
                        return htmlText.join("")
                    }
                },
                {
                    title: '入驻畅运通/运力池',
                    align: 'left',
                    field: 'isRegister',
                    formatter:function(value,row, index){
                        let htmlText=[];
                        if(value == 1){
                            htmlText.push(`是/`+getValue(row.registerDate.substring(0,10)));
                        }else{
                            htmlText.push(`否`);
                        }
                        if(row.setTransportPool== 2){
                            htmlText.push(`是`);
                        }else{
                            htmlText.push(`否`);
                        }

                        return htmlText.join('<br/>');

                    }
                },
                // {
                //     title: '跳过验证',
                //     align: 'left',
                //     field: 'shipExamine',
                //     formatter: function(value, item, index) {
                //         if (value == 0) {
                //             return '<span class="label label-warning">否</span>';
                //         }
                //         if (value == 1) {
                //             return '<span class="label label-primary">是</span>';
                //         }
                //     }
                // },  
                

                
                // {
                //     title: '入驻时间',
                //     align: 'center',
                //     field: 'registerDate',
                // },
                // {
                //     title: '是否入驻运力池',
                //     align: 'center',
                //     field: 'setTransportPool',
                //     formatter:function(value){
                //         if(value == 1){
                //             return '否'
                //         }
                //         return '是'
                //     }
                // },
               
                // {
                //     title: '认证状态',
                //     align: 'left',
                //     field: 'authStatus',
                //     formatter: function(value, item, index) {
                //         if (value == '0') {
                //             return '<span class="carve carve-warning">待认证</span>';
                //         }
                //         if (value == '1') {
                //             return '<span class="carve carve-primary">认证通过</span>';
                //         }
                //         if (value == '2') {
                //             var txt = '<span class="carve carve-danger">认证未通过</span>'
                //             if (item.reason) {
                //                 // onmouseover="alert(\''+item.reason.replace(/\n/g,'\\n')+'\')"
                //                 txt = txt + ' <i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px" data-html="true" title="'+item.reason.replace(/\n/g,'<br>')+'"></i>'
                //             }
                //             return txt;
                //         }
                //     }

                // },
                // {
                //     title: '优先级',
                //     align: 'left',
                //     field: 'priority',
                //     formatter: function status(row,value) {
                //         return $.table.selectDictLabel(priority, value.priority);
                //     }
                // },
                {
                    title: '详细地址',
                    align: 'left',
                    formatter: function status(row,value) {
                        if(value.provinceName == null){
                            value.provinceName = "";
                        }
                        if(value.cityName == null){
                            value.cityName = "";
                        }
                        if(value.areaName == null){
                            value.areaName = "";
                        }
                        if(value.address == null){
                            value.address = "";
                        }
                        var addr = value.provinceName + value.cityName + value.areaName + value.address;
                        return addr;
                    }
                },
                {
                    title: '是否外包导入',
                    align: 'left',
                    field: 'isOutsource',
                    formatter: function status(value, row, index) {
                        if(value==0){
                            return '否';
                        }else if(value==1){
                            return '是';
                        }
                    }
                },
                {
                    title: '创建信息',
                    align: 'left',
                    field: 'regUserId',
                    formatter: function(value, item, index) {
                        return $.table.tooltip(value)+'<br/>'+ $.table.tooltip(item.regDate);
                    }
                },
                {
                    title: '修改信息',
                    align: 'left',
                    field: 'corUserId',
                    formatter: function(value, item, index) {
                        return $.table.tooltip(value)+'<br/>'+ $.table.tooltip(item.corDate);
                    }
                },
                {
                    title: '审核信息',
                    align: 'left',
                    field: 'checkMan',
                    formatter: function status(value,row) {
                        return  $.table.tooltip(value)+'<br/>'+ $.table.tooltip(row.checkDate);;
                    }
                },
                {
                    title: '线路标签',
                    align: 'left',
                    field: 'roadTips'
                }
            ]
        };
        $.table.init(options);
    });

    function getValue(val){
        if(val == null){
            val = "/";
        }
        return val
    }

    //审核
    function check(){
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var url = prefix + "/check";
        //获取状态
        var checkStatusArr = $.btTable.bootstrapTable('getSelections');
        //循环判断是否能够进行审核
        for(var i=0;i<checkStatusArr.length;i++){
            if(checkStatusArr[i]['checkStatus'] != 0 && checkStatusArr[i]['checkStatus'] != 3){
                $.modal.alertWarning("请选择待审核或新建数据进行审核操作");
                return;
            }
           /* if(checkStatusArr[i]['checkStatus'] == 2){
                var flag = false;
                layer.confirm("请审核选中的" + rows.length + "条数据",{
                    btn:["待审核"]
                },function (index, layero) {
                    if(!flag){
                        flag = true;
                        layer.close(index);
                        var data = { "ids": rows.join(),"flag":"0" };
                        $.operate.submit(url, "post", "json", data);
                    }

                })
                return ;
            }*/
        }

       /* var flag = false;
        layer.confirm("请审核选中的" + rows.length + "条数据",{
            btn:["通过","不通过"]
        },function (index, layero) {
            if(!flag){
                flag = true;
                layer.close(index);
                var data = { "ids": rows.join(),"flag":"1" };
                $.operate.submit(url, "post", "json", data);
            }
        },function (index) {
            if(!flag){
                flag = true;
                layer.close(index);
                var data = { "ids": rows.join(),"flag":"2" };
                $.operate.submit(url, "post", "json", data);
            }
        });*/

        var type = 'detailSave';
        var url = prefix + "/detailMyCarrier?carrierId="+rows.join()+"&type="+type;
        $.modal.openTab('承运商证件审核',url);
    }

    function infoCheck(){
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var url = prefix + "/infoCheck?carrierId="+rows.join();
        $.modal.openTab('承运商信息审核',url);
    }

    function removeAll(){
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        let msg = "";
        $.ajax({
            type: "POST",
            url: ctx + "basic/carrier/checkCarrierAuto",
            data: { "ids": rows.join() },
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    msg += r.msg;
                }
            }
        });
        msg = msg + "确认要删除选中的" + rows.length + "条数据吗?"
        $.modal.confirm(msg, function() {
            var url = $.table._option.removeUrl;
            var data = { "ids": rows.join() };
            $.operate.submit(url, "post", "json", data);
        });
    }


    function detail(id) {
        var url = prefix + "/detail?carrierId="+id;
        $.modal.openTab('承运商明细',url);
    }

    /**
     * 明细修改
     * @param id
     */
    function detailSave(id) {
        var type = 'detailSave';
        var url = prefix + "/detail?carrierId="+id+"&type="+type;
        $.modal.openTab('承运商明细修改',url);
    }



    //承运商账号
    function accountList(id) {
        var url = ctx + "basic/carrierUser/list/"+id;
        $.modal.openTab('承运商账号',url);
    }

    //申请入驻
    function applyJoin(id){
        var url = prefix + "/applyJoin/"+id;
        var title = '申请入驻';
        layer.open({
            type: 2,
            area: ['600px', '600px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: title,
            content: url,
            btn: ['打印', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function (index, layero){
                $(layero).find("iframe")[0].contentWindow.submitHandler();
            },
            cancel: function(index) {
                return true;
            }
        });
    }

    //承运商银行卡
    function bankList(id) {
        //var url = ctx + "basic/carrierBank/list/"+id;
        var url = ctx + "g7/payee/"+id;
        $.modal.openTab('承运商银行卡',url);
    }

    function openCarrierException(carrierId){
        var url = prefix + "/to_carrier_exception/"+carrierId;
        $.modal.openTab("异常" , url);
    }
    //修改结算方式
    // function editBalaType(id) {
    //     var url = prefix + "/editBalaType/"+id;
    //     $.modal.open('修改结算方式',url,500,300);
    // }

    // function shipExamine(id){
    //     var url = prefix + "/shipExamine/"+id;
    //     $.modal.open('修改跳过验证',url,500,300);
    // }

    function ifHasBill(id){
        var url = prefix + "/ifHasBill/"+id;
        $.modal.open('管理员配置', url, 700, $(window).height() - 50);
    }

    function blackAll(id,isblaclklist){
        if(isblaclklist == 1){
            var url = prefix + "/unblackAll/"+id;
            $.modal.open('批量取消黑名单', url, 750, $(window).height() - 50);
        }else{
            var url = prefix + "/blackAll/"+id;
            $.modal.open('批量黑名单', url, 750, $(window).height() - 50);
        }
    }


    function prints(id){
        let iframe = document.getElementById("print-frame");
        if (!iframe) {
            iframe = document.createElement('IFRAME');
            iframe.id = "print-frame"
            document.body.appendChild(iframe);
            iframe.setAttribute('style', 'display:none;');
        }
        iframe.src = prefix + "/applyPrint?id="+id;
        iframe.onload = function () { //解决图片显示不了的问题
            iframe.contentWindow.focus();
            iframe.contentWindow.print();
        };
    }

    /**
     * 调整额配置
     */
    // function adjustmentConfig(id){
    //     $.modal.open("调整额配置", prefix + "/adjustmentConfig?carrierId="+id,400,300);
    // }



    function edit(id,checkStatus) {
        if(checkStatus == '0'){
            $.modal.alertWarning("承运商待审核状态无法修改!");
            return false;
        }
        var url = prefix + "/edit/"+id;
        $.modal.openTab('承运商修改',url);
    }

    //运单合同
    function contract() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        var url = prefix + "/contract/"+rows.join();
        $.modal.openTab("运单合同" , url);
    }

    function addLayer(){
        var url = ctx + "basic/carrier/carrierLayer";
        $.modal.open("新增承运商" , url);
    }

    function openCarCarrier(carrierId){
        var url = prefix + "/carrierCar/"+carrierId;
        $.modal.openTab("承运商车辆" , url);
    }

    function openDriverCarrier(carrierId){
        var url = prefix + "/carrierDriver/"+carrierId;
        $.modal.openTab("承运商司机" , url);
    }

</script>

</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>