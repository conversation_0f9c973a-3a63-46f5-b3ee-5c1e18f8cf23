<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <meta charset="UTF-8">
    <title></title>
    <style>
        @page {
            /*size: 210mm 297mm;*/
            /*margin: 1.54cm 1.17cm 1.54cm 1.17cm;*/
            margin: 6mm;
            /*mso-header-margin: 1.5cm;
            mso-footer-margin: 1.75cm;
            mso-paper-source: 0;*/
        }
        html {
            /*width: 210mm;
            height: 297mm;*/
            border: 0px #000 solid;
            margin: 0;
            padding: 0;
        }
        body {
            margin: 0;
            padding: 0;
        }
        * {
            font-family: SimHei;
            font-size: 14px;
        }
        .title {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
        }
        .right {
            text-align: right;
        }
        .center {
            text-align: center;
        }
        .dtl {
            width: 100%;
            border-left: 1px #000 solid;
            border-top: 1px #000 solid;
            border-collapse: collapse;
            margin-top: 0.5mm;
        }
        .dtl td {
            border-right: 1px #000 solid;
            border-bottom: 1px #000 solid;
            padding: 1px 3px;
            line-height: 30px;
        }
        .tdTitle {
            background-color: #dedede;
            width:14.28%;
            text-align: center;
            -webkit-print-color-adjust: exact; /*控制打印的时候有背景色*/
        }
        .bold {
            font-weight: bold;
        }
        .bg{
            padding: 0 20px 0 50px;
            background: #fff url("../../../img/yd.png") no-repeat left top;
            background-size: 40px 40px;
        }
    </style>
</head>
<body>
<div class="title">承运商引荐表</div>
<div style="display: flex; justify-content: space-between; align-items: center;">
    <span>
        <img style="height: 35px" th:src="@{/img/yd.png}" alt="">
    </span>
    <span>有效期：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
</div>

<table class="dtl">
    <tr>
        <td class="tdTitle" style="line-height: 20px;background-color: #999797" colspan="8">基本信息</td>
    </tr>
    <tr>
        <td class="tdTitle" colspan="2">承运商名称</td>
        <td style="width: auto" colspan="2">[[${carrier.carrName}]]</td>
        <td class="tdTitle">承运商性质</td>
        <td style="width: auto" colspan="3">个体<input type="checkbox"> 公司<input type="checkbox"></td>
    </tr>
    <tr>
        <td class="tdTitle" colspan="2">联系人</td>
        <td style="width: auto" colspan="2">[[${carrier.contact}]]</td>
        <td class="tdTitle">联系电话</td>
        <td style="width: auto" colspan="3">[[${carrier.phone}]]</td>
    </tr>
    <tr>
        <td class="tdTitle" colspan="2">身份证信用代码</td>
        <td style="width: auto" colspan="2">[[${carrier.legalCard}]]</td>
        <td class="tdTitle">来源</td>
        <td style="width: auto" colspan="3">自主研发<input type="checkbox"> 推荐<input type="checkbox"> 其他<input type="checkbox"></td>
    </tr>
    <tr>
        <td class="tdTitle" colspan="2">结算方式</td>
        <td style="width: auto" colspan="2">单笔<input type="checkbox"> 月结<input type="checkbox"></td>
        <td class="tdTitle">账期</td>
        <td style="width: auto" colspan="3">(&nbsp;&nbsp;&nbsp;)天</td>
    </tr>
    <tr>
        <td style="width: auto;text-align: center" colspan="4">开票税点:&nbsp;&nbsp;%&nbsp;&nbsp;油卡占比:&nbsp;&nbsp;%&nbsp;&nbsp;G7<input type="checkbox"></td>
        <td class="tdTitle">保证金</td>
        <td style="width: auto" colspan="3"></td>
    </tr>
    <tr>
        <td class="tdTitle" style="line-height: 20px;background-color: #999797" colspan="8">承运线路信息</td>
    </tr>
    <tr>
        <td class="tdTitle"  style="line-height: 1.8;"  colspan="2">关联客户</br>(系统客户简称)</td>
        <td style="width: auto;line-height:1.5" colspan="7">[[${custAbbrBuffer}]]</td>
    </tr>
    <tr>
        <td class="tdTitle" style=" width:7.14%;">序号</td>
        <td class="tdTitle" style=" width:7.14%;">特长</td>
        <td class="tdTitle" >提货地</td>
        <td class="tdTitle" >到货地</td>
        <td class="tdTitle" >车型</td>
        <td class="tdTitle" >计价方式</td>
        <td class="tdTitle" >结算价格</td>
        <td class="tdTitle" >备注</td>
    </tr>

    <tr>
        <td style="width: auto;text-align: center">1</td>
        <td style="width: auto;text-align: center"><input type="checkbox"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
    </tr>
    <tr>
        <td style="width: auto;text-align: center">2</td>
        <td style="width: auto;text-align: center"><input type="checkbox"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
    </tr>
    <tr>
        <td style="width: auto;text-align: center">3</td>
        <td style="width: auto;text-align: center"><input type="checkbox"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
    </tr>
    <tr>
        <td style="width: auto;text-align: center">4</td>
        <td style="width: auto;text-align: center"><input type="checkbox"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
    </tr>
    <tr>
        <td style="width: auto;text-align: center">5</td>
        <td style="width: auto;text-align: center"><input type="checkbox"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
    </tr>
    <tr>
        <td style="width: auto;text-align: center">6</td>
        <td style="width: auto;text-align: center"><input type="checkbox"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
    </tr>
    <tr>
        <td style="width: auto;text-align: center">7</td>
        <td style="width: auto;text-align: center"><input type="checkbox"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
    </tr>
    <tr>
        <td style="width: auto;text-align: center">8</td>
        <td style="width: auto;text-align: center"><input type="checkbox"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
        <td style="width: auto"></td>
    </tr>
    <tr>
        <td class="tdTitle"  style="line-height: 1.8;"  colspan="2">证件</br>复印件</td>
        <td style="width: auto" colspan="7">
            营业执照<input type="checkbox"> 道路资格许可证<input type="checkbox"> 身份证正反<input type="checkbox"> 对公收款账户<input type="checkbox"></br>
            驾驶证<input type="checkbox"> 行驶证<input type="checkbox"> 从业资格证<input type="checkbox"> 道路运输证<input type="checkbox">
        </td>
    </tr>
    <tr>
        <td class="tdTitle" style="line-height: 20px;background-color: #999797" colspan="8">更新履历</td>
    </tr>
    <tr>
        <td class="tdTitle" style=" width:7.14%;">序号</td>
        <td class="tdTitle" colspan="2">更新时间</td>
        <td class="tdTitle" colspan="6">更新内容</td>
    </tr>
    <tr th:each="item,status:${configList}">
        <td style="width: auto;text-align: center">[[${status.index+1}]]</td>
        <td style="width: auto;text-align: center" colspan="2">[[${item.corDateStr}]]</td>
        <td style="width: auto;line-height: 1.5" colspan="6" th:id="${item.id}"></td>
    </tr>
    <tr>
        <td class="tdTitle" style="line-height: 20px;background-color: #999797" colspan="8">审批意见</td>
    </tr>
    <tr>
        <td class="tdTitle" colspan="2">申请部门</td>
        <td style="width: auto" colspan="2"></td>
        <td class="tdTitle">申请人</td>
        <td style="width: auto" colspan="3"></td>
    </tr>
    <tr>
        <td class="tdTitle" colspan="2">部门审批</td>
        <td style="width: auto" colspan="6">同意<input type="checkbox"> 修改后通过<input type="checkbox"> 拒绝<input type="checkbox"></td>
    </tr>
    <tr>
        <td class="tdTitle" colspan="2">部门审批人</td>
        <td style="width: auto" colspan="2"></td>
        <td style="width: auto" colspan="4">202&nbsp;&nbsp;年&nbsp;&nbsp;月&nbsp;&nbsp;日</td>
    </tr>
    <tr>
        <td class="tdTitle" colspan="2">总办审批</td>
        <td style="width: auto" colspan="6">同意<input type="checkbox"> 修改后通过<input type="checkbox"> 拒绝<input type="checkbox"></td>
    </tr>
    <tr>
        <td class="tdTitle" colspan="2">总办审批人</td>
        <td style="width: auto" colspan="2"></td>
        <td style="width: auto" colspan="4">202&nbsp;&nbsp;年&nbsp;&nbsp;月&nbsp;&nbsp;日</td>
    </tr>
</table>

</body>
<script th:inline="javascript">
let configList = [[${configList}]];
let billingMethod = [[${billingMethod}]]


for(let i=0;i<configList.length;i++){
    let item = configList[i];
    // 通过 id 获取元素
    const element = document.getElementById(item.id);
    let html = "";
    if(item.deliCityName = '市辖区'){
        html += item.deliProName + item.deliAreaName + '-';
    }else{
        html += item.deliCityName + item.deliAreaName+ '-';
    }
    if (item.arriType == '0') {
        if(item.arriCityName = '市辖区'){
            html += item.arriProName + item.arriAreaName;
        }else{
            html += item.arriCityName + item.arriAreaName;
        }
    }else{
        html += item.arriAddrName;
    }

    if(item.carLenName){
        html += ' '+item.carLenName+'米';
    }

    if(item.carTypeName){
        html += ' '+item.carTypeName;
    }

    for ( var j = 0; j < billingMethod.length; j++) {
        if(billingMethod[j].value == item.billingMethod){
            html += ' '+billingMethod[j].context;
        }
    }


    if (item.deductionType != '4') {
        let deductionAmount = ''
        if (item.deductionAmount != null) {
            if (item.deductionType == 0) {
                deductionAmount = '应收扣减：' + item.deductionAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
            }else if (item.deductionType == 1) {
                let deductionFeeType = ''
                if (item.deductionFeeType == 0) {
                    deductionFeeType = '(仅运费)'
                }else {
                    deductionFeeType = '(运费+在途)'
                }

                deductionAmount = '应收扣减：' +item.deductionAmount + '%' + deductionFeeType;
            }else if (item.deductionType == 2) {
                deductionAmount = '固定应付总价：' +item.deductionAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
            }else if (item.deductionType == 3) {
                deductionAmount = '固定应付单价：' +item.deductionAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
            }
        }
        html += ` ${deductionAmount}`
    }

    let sectionHtml = ''

    if (item.autoDispatchSectionList && item.deductionType == '4') {
        for(let section of item.autoDispatchSectionList) {
            let startOperator = "";
            if (section.startOperator != null) {
                startOperator = section.startOperator == '0' ? '&lt;' : '≤'
            }

            let endOperator = "";
            if (section.endOperator) {
                endOperator = section.endOperator == '2' ? '&lt;' : '≤'
            }

            let price = "";
            if (section.price != null) {
                price = section.price.toLocaleString('zh', {
                    style: 'currency',
                    currency: 'CNY'
                });
            }

            let isFixedPrice = "";
            if (section.isFixedPrice != null) {
                isFixedPrice = section.isFixedPrice == '0' ? '' : '固';
            }

            if(section.startSection && section.endSection){
                sectionHtml += '【'+section.startSection+startOperator+'X'+endOperator+section.endSection+' '+price+'】';
            }else if(section.startSection){
                sectionHtml += '【'+section.startSection+startOperator+'X '+price+'】';
            }else if(section.endSection){
                sectionHtml += '【X'+endOperator+section.endSection+' '+price+'】';
            }

        }
        html += sectionHtml;

    }

    
    // 设置元素内的 HTML 内容
    element.innerHTML = html;
}
</script>
</html>