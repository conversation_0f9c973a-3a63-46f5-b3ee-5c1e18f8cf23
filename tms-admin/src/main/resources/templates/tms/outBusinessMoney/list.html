<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细申请审核')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 40px);
        padding-top: 0;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
        min-width: 16px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row ">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" name="fybxId" th:value="${fybxId}">
                <div class="row no-gutter">
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="vbillno" class="form-control" type="text" placeholder="请输入单号"
                                       maxlength="30"  aria-required="true" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="custAbbr" class="form-control" type="text" placeholder="请输入客户"
                                       maxlength="30"  aria-required="true" >
                            </div>
                        </div>
                    </div>


                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="payCheckOff()" shiro:hasPermission="tms:outBusinessMoney:checkOut">
                <i class="fa fa-dollar"></i> 核销
            </a>
            <a class="btn btn-danger" onclick="$.operate.removeAll()" shiro:hasPermission="tms:outBusinessMoney:delete">
                <i class="fa fa-remove"></i> 删除
            </a>

        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "outBusinessMoney";

    //开票金额合计
    var ysyf = 0;

    var yfzje = 0;

    var yfk = 0;
    var yfye = 0;

    $(function () {

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });

        });
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });
        var options = {
            url: ctx + "outBusinessMoney/list",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            showToggle: false,
            showColumns: true,
            clickToSelect:true,
            height: 560,
            showFooter:true,
            uniqueId: "id",
            modalName: "背靠背台账",
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            onPostBody: function () {
                clearTotal();
                //合并页脚
                merge_footer();
                //getAmountCount();
            },
            columns: [
                {
                    checkbox: true,
                    /*footerFormatter: function (row) {
                        return "应收总金额：<nobr id='ysyf'>¥0.00</nobr>" +

                            " 应收：<nobr id='zys'>¥0.00</nobr>" +
                            " 已收：<nobr id='zhs'>¥0.00</nobr>" +
                            " 未收：<nobr id='zws'>¥0.00</nobr>" +

                            " 应付总金额：<nobr id='yfzje'>¥0.00</nobr>" +

                            " 已付款金额：<nobr id='yfk'>¥0.00</nobr>" +
                            " 应付余额：<nobr id='yfye'>¥0.00</nobr>" +
                            "<br>" +
                            "总合计：应收总金额：<nobr id='ysyfTotal'></nobr>" +

                            " 总应收：<nobr id='zysTotal'></nobr>" +
                            " 总已收：<nobr id='hasReceiveTotal'></nobr>" +
                            " 总未收：<nobr id='noReceiveTotal'></nobr>" +

                            " 应付总金额：<nobr id='yfzjeTotal'></nobr>" +
                            " 已付款金额：<nobr id='yfkTotal'></nobr>" +
                            " 应付余额：<nobr id='yfyeTotal'>¥0.00</nobr>";
                    }*/
                },

                {
                    field: 'vbillno',
                    title: '收入单号',
                    align: 'left'
                },
                {
                    field: 'custAbbr',
                    title: '客户简称',
                    align: 'left'
                },
                {
                    field: 'status',
                    title: '状态',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if(value == 0){
                            return '<span class="label label-warning">已申请</label>'
                        }else if(value == 1){
                            return '<span class="label label-success">已核销</label>'
                        }else{
                            return '-'
                        }
                    }
                },
                {
                    field: 'regUserName',
                    title: '申请人',
                    align: 'left'
                },
                {
                    field: 'createTime',
                    title: '申请时间',
                    align: 'left'
                },
                {
                    field: 'memo',
                    title: '申请说明',
                    align: 'left'
                },

                {
                    title: '收入金额',
                    align: 'right',
                    field: 'money',
                    formatter: function (value, row, index) {

                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    field: 'sysUploadFile',
                    title: '申请附件',
                    formatter: function(value, row, index) {
                        var html = ""
                        if(value != null && value != '') {
                            value.forEach(function (element, index) {
                                if (index != 3) { //最多展示三张图片 页面美观
                                    html +=  $.table.imageView(element.filePath)
                                }
                            });
                        }
                        return html;
                    }
                },
                {
                    field: 'verificationMan',
                    title: '核销人',
                    align: 'left'
                },
                {
                    field: 'verificationTime',
                    title: '核销时间',
                    align: 'left'
                },
                {
                    field: 'verificationMemo',
                    title: '核销备注',
                    align: 'left'
                },


            ]
        };

        $.table.init(options);
    });

    //审核
    function payOff() {
        var status = $.table.selectColumns("status").join();
        if(status != 0){
            $.modal.alertWarning("请选择新建的申请进行核销");
            return;
        }
        var id = $.table.selectColumns("id").join();
        var url = prefix + "/payOff?id="+id;
        $.modal.open('承运商预付核销',url,500,700);
    }

    function payCheckOff(){
        var status = $.table.selectColumns("status").join();
        if(status != 0){
            $.modal.alertWarning("请选择新建的申请进行核销");
            return;
        }
        var id = $.table.selectColumns("id").join();
        $.modal.open("营业外收入核销", ctx + "outBusinessMoney/moneyPayOff?id="+id ,600,450);
    }

    /**
     * 重置
     */
    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        $.table.search('role-form', data);
    }

    function getValue(val){
        if(val == null){
            val = "/";
        }
        return val
    }

    function add(){
        let url = prefix + "/add";
        $.modal.open("新增台账" , url,500,480);
    }

    function addInvoice(id){

        var url = prefix + "/invoiceView?id=" + id;
        $.modal.openTab("应收记录", url);


       /* let url = prefix + "/addInvoice?id=" + id;
        $.modal.open("开票登记" , url,800,650);*/
    }

    function prepayment(id){
        var url = prefix + "/prepaymentView?id=" + id;
        $.modal.openTab("付款记录", url);
    }

    function edit(id){
        let url = prefix + "/edit?id=" + id;
        $.modal.open("修改台账" , url,500,480);
    }

    function carrierInvoice(id){
        var url = prefix + "/carrierInvoice?id=" + id;
        $.modal.openTab("承运商收票", url);
    }

    /**
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        $.ajax({
            url: prefix + "/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result.data;
                if (result.code == 0 && data != undefined) {
                    $("#ysyfTotal").text((data.RECEIVE_AMOUNT+data.RECEIVE_ZXF_AMOUNT).toLocaleString('zh', {style: 'currency', currency: 'CNY'}));

                    $("#zysTotal").text((data.RECEIVE_ZXF_AMOUNT+data.RECEIVE_AMOUNT+data.PAYBACKAMOUNT).toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#hasReceiveTotal").text(data.HASRECEIVEAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#noReceiveTotal").text((data.RECEIVE_ZXF_AMOUNT+data.RECEIVE_AMOUNT+data.PAYBACKAMOUNT-data.HASRECEIVEAMOUNT).toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#yfzjeTotal").text(data.PAY_AMOUNT_TOTAL.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));

                    $("#yfkTotal").text(data.HASPAYAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#yfyeTotal").text(data.PAY_LESS_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    /**
     * 将总计金额清零
     */
    function clearTotal() {
        ysyf = 0;

        zys = 0;
        zhs = 0;
        zws = 0;
        yfzje = 0;
        yfk = 0;
        yfye = 0;
    }

    function addTotal(row) {
        ysyf =ysyf + row.receiveAmount + row.receiveZxfAmount;
        zys = zys + row.receiveAmount + row.receiveZxfAmount + row.payBackAmount;
        zhs += row.hasReceiveAmount;
        zws = zys - zhs;
        yfzje = yfzje + row.payAmount +row.payZxfAmount;
        yfk += row.hasPayAmount;
        yfye += row.payLessAmount;
    }


    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#ysyf").text(ysyf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#zys").text(zys.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#zhs").text(zhs.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#zws").text(zws.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#yfzje").text(yfzje.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#yfk").text(yfk.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#yfye").text(yfye.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }

    function subTotal(row) {
        ysyf =ysyf - row.receiveAmount - row.receiveZxfAmount;

        zys = zys - row.receiveAmount - row.receiveZxfAmount -row.payBackAmount;
        zhs -= row.hasReceiveAmount;
        zws = zys - zhs;
        yfzje = yfzje - row.payAmount - row.payZxfAmount;
        yfk -= row.hasPayAmount;
        yfye -= row.payLessAmount;
    }

    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }
</script>


</body>
</html>