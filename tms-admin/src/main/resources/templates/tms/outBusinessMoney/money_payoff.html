<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('反确认')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
</head>
<style>
    .file-drop-zone{
        height: 100% !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-trustDeed-unconfirm" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <input id="id" name="id" type="hidden" th:value="${id}">
            <div class="panel-body">
                <!--基础信息 begin-->
                <div class="row">
                    <div class="col-xs-6">
                        <div class="form-group">
                            <label class="col-sm-5"><span style="color: red">*</span>核销时间：</label>
                            <div class="col-sm-7">
                                <input id="verificationTime" name="verificationTime"  type="text" class="form-control valid" required="" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-sm-5"><span style="color: red">*</span> 核销备注：</label>
                            <div class="col-sm-12">
                                <textarea name="verificationMemo" id="verificationMemo" class="form-control" type="text"
                                          maxlength="200" required="" rows="10" aria-required="true"></textarea>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">

    var prefix = ctx + "outBusinessMoney";

    $(function() {
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#verificationTime',
                type: 'datetime',
                trigger: 'click'
            });
        });
    })
    /**
     * 校验
     */
    $("#form-trustDeed-unconfirm").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            unconfirmType:{
                required:true,
            },
            unconfirmMemo:{
                required:true,
            },
         /*   unconfirmApplicationName: {
                required:true
            },*/
            // image:{
            //     required:true
            // }
        }
    });

    /**
     * 提交
     */
    function submitHandler() {
        $("#error-panel").css('display', 'none')
        if ($.validate.form()) {
                commit();
        }
    }

    function commit() {

        let query = $("#form-trustDeed-unconfirm").serializeArray();
        console.log(query)
        $.operate.save(prefix + "/saveMoneyPayOff", query);

    }


    function clearUploadFile() {
        $('input[name=iamge]').val("")
        $('input[name=appendixId]').val("")
    }

    function typeChange(){
        let unconfirmType = $("#unconfirmType").val();
        if(unconfirmType == 1){
            $("#responsibleUserNameDiv").css("display",'block');
        }else{
            $("#responsibleUserNameDiv").css("display",'none');
        }
    }

</script>
</body>
</html>