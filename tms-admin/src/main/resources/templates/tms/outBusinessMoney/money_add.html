<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('反确认')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
</head>
<style>
    .file-drop-zone{
        height: 100% !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-trustDeed-unconfirm" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <input id="fybxId" name="fybxId" type="hidden" th:value="${fybxId}">
            <div class="panel-body">
                <!--基础信息 begin-->
                <div class="row">
                    <div class="col-xs-6">
                        <div class="form-group">
                            <label class="col-sm-5"><span style="color: red">*</span>收入金额：</label>
                            <div class="col-sm-7">
                                <input id="money" name="money"  type="text" class="form-control valid"  th:oninput="|$.numberUtil.onlyNumber(this);|" maxlength="20" required="">
                            </div>
                        </div>
                    </div>

                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-sm-5"><span style="color: red">*</span> 申请说明：</label>
                            <div class="col-sm-12">
                                <textarea name="memo" id="memo" class="form-control" type="text"
                                          maxlength="200" required="" aria-required="true"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="col-xs-12">
                        <div class="">申请附件：</div>
                        <div class="">
                            <div class="">
                                <div class="">
                                    <input id="image" class="form-control" name="image" type="file" multiple>
                                    <input id="appendixId" name="appendixId" type="hidden">
                                </div>
                                <label id="error-panel" class="error" for="image" style="display: none;"></label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">

    var prefix = ctx + "outBusinessMoney";

    $(function() {
        var image = {
            maxFileCount: 5,
            publish: "imgDone",  //用于绑定下一步方法
            fileType: null//文件类型
        };
        $.file.initAddFiles("image", "appendixId", image);

        // 图片上传成功后
        $("#image").on('filebatchuploadsuccess', function (event, data) {
            var tid = data.response.tid;
            $("#appendixId").val(tid);
            commit();
        });
    })
    /**
     * 校验
     */
    $("#form-trustDeed-unconfirm").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            unconfirmType:{
                required:true,
            },
            unconfirmMemo:{
                required:true,
            },
         /*   unconfirmApplicationName: {
                required:true
            },*/
            // image:{
            //     required:true
            // }
        }
    });

    /**
     * 提交
     */
    function submitHandler() {
        $("#error-panel").css('display', 'none')
        //判断图片是否上传
        // var title = $(".file-caption-name").attr('title');
        // if(title == undefined) {
        //     $("#error-panel").text("这是必填字段")
        //     $("#error-panel").css('display', 'block')
        //     return
        // }
        if ($.validate.form()) {
    /*        var reg= /^(([a-zA-Z+\.?\·?a-zA-Z+]{2,30}$)|([\u4e00-\u9fa5+\·?\u4e00-\u9fa5+]{2,30}$))/;
            var unconfirmApplicationName = $("#unconfirmApplicationName").val()
            if(!reg.test(unconfirmApplicationName)){
                $.modal.alertWarning("申请人姓名输入有误，请重新输入")
                return false;
            }*/

      /*      var title = $(".explorer-caption").attr("title")
            if(title == null) {
                $.modal.alertWarning("请先选择文件路径")
                return  false;
            }*/

            // if($("#image")[0].files[0] == null) {
            //     $.modal.alertWarning("请先选择文件路径")
            //     return  false;
            // }
            var errorMsg = $(".kv-fileinput-error ul li").text()
            if(errorMsg != null  && errorMsg != '') {
                $.modal.alertWarning("请上传有效的图片格式")
                return  false;
            }
            // console.log($("#form-trustDeed-unconfirm").serializeArray());
            if ($("#image").val() != "") {
                $("#image").fileinput('upload');
            }else {
                commit();
            }
            //$.operate.save(prefix + "/backConfirmPick", $("#form-trustDeed-unconfirm").serializeArray());
        }
    }

    function commit() {

        let query = $("#form-trustDeed-unconfirm").serializeArray();
        console.log(query)
        $.operate.save(prefix + "/saveMoneyAdd", query);

    }


    function clearUploadFile() {
        $('input[name=iamge]').val("")
        $('input[name=appendixId]').val("")
    }

    function typeChange(){
        let unconfirmType = $("#unconfirmType").val();
        if(unconfirmType == 1){
            $("#responsibleUserNameDiv").css("display",'block');
        }else{
            $("#responsibleUserNameDiv").css("display",'none');
        }
    }

</script>
</body>
</html>