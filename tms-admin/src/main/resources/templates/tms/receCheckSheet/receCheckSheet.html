<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户对账')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <style>
        .sm{
            background: #ffefef;
            padding: 5px 10px;
            border: 1px #ff9999 solid;
            margin-top: 10px;
        }
        .sm_icon{
            width: 18px;
            height: 18px;
            background: #faad14;
            color: #fff;
            border-radius: 50%;
            display: inline-block;
            line-height: 18px;
            text-align: center;
            font-size: 12px;
        }
        .sm_text{
            color: #655959;
            display: inline-block;
            margin-left: 10px;
            line-height: 20px;
        }
        .table-striped {
            height: calc(100% - 150px);
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="sm">
            <span class="sm_icon">!</span>
            <span class="sm_text" id="warn_msg">
                加载中...
            </span>
        </div>
        <div class="col-sm-12 search-collapse" th:style="${receSheetRecordId == null ? '':'display:none'}">
            <form id="role-form" class="form-horizontal">
                <!--客户id 应收对账跳转-->
                <input type="hidden" name="customerId" th:value="${customerId}">
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">对账单状态：</label>-->
                            <div class="col-sm-12">
                                <!--<select name="vbillstatus" id="vbillstatus" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="对账单状态" multiple th:with="type=${checkSheetStatusList}">
                                    <option th:each="dict : ${type}" th:text="${dict.context}" th:value="${dict.value}"></option>
                                </select>-->
                                <select id="status" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="对账单状态" multiple>
                                    <option value="0">新建</option>
                                    <option value="1">已确认待申请</option>
                                    <option value="2">已申请</option>
                                    <option value="3">部分收款</option>
                                    <option value="4">已收款</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">年份：</label>-->
                            <div class="col-sm-12">

                                <input name="year" id="year"  placeholder="请输入年份" class="form-control valid" type="text"
                                       aria-required="true" autocomplete="off">

                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">月份：</label>-->
                            <div class="col-sm-12">

                                <input name="month" id="month" placeholder="请输入月份" class="form-control valid" type="text"
                                       aria-required="true"  autocomplete="off">

                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <input type="hidden" name="receSheetRecordId" th:value="${receSheetRecordId}">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">对账单号：</label>-->
                            <div class="col-sm-12">
                                <input name="vbillno" id="vbillno" class="form-control" type="text" th:value="${vbillno}"
                                       placeholder="请输入对账单号" maxlength="25" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">运营组：</label>-->
                            <div class="col-sm-12">
                                <select name="salesDept" id="salesDept" class="form-control valid selectpicker"
                                        aria-invalid="false" data-none-selected-text="运营组" required>
                                    <option value=""></option>
                                    <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">客户名称：</label>-->
                            <div class="col-sm-12">
                                <input name="custName" id="custName" placeholder="请输入客户名称" class="form-control valid" type="text">
                            </div>
                        </div>
                    </div>

                </div>
                <div class="row">

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">结算客户：</label>-->
                            <div class="col-sm-12">
                                <input name="balaName" id="balaName" class="form-control" type="text"
                                       placeholder="请输入结算客户" maxlength="25" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">开票公司：</label>-->
                            <div class="col-sm-12">
                                <input name="checkCorp" id="checkCorp" class="form-control" type="text"
                                       placeholder="请输入开票公司" maxlength="10" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">收款状态：</label>-->
                            <!--<div class="col-sm-12">
                                <select name="receStatus" id="receStatus" class="form-control" aria-invalid="false" >
                                    <option value="">--请选择收款状态--</option>
                                    <option value="0">已收</option>
                                    <option value="1">部分</option>
                                    <option value="2">未收</option>
                                </select>
                            </div>-->
                            <div class="col-sm-12">
                                <select name="params[checkStatus]" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="申请审核状态">
                                    <option value=""></option>
                                    <option value="0">未申请</option>
                                    <!--<option value="1">待业务审核</option>
                                    <option value="2">待总经办审核</option>
                                    <option value="3">待财务复核</option>-->
                                    <option value="1,2,3">审核中</option>
                                    <option value="4">审核通过</option>
                                    <option value="5,6,7">审核未通过</option>
                                    <!--<option value="5">业务打回</option>
                                    <option value="6">总经办打回</option>
                                    <option value="7">财务打回</option>-->
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">

                            <div class="col-sm-12">
<!--                                <select name="ifTicket" id="ifTicket" class="form-control" aria-invalid="false" >-->
<!--                                    <option value="">&#45;&#45;请选择是否开票&#45;&#45;</option>-->
<!--                                    <option value="0">否</option>-->
<!--                                    <option value="1">是</option>-->
<!--                                    &lt;!&ndash;<option value="">&#45;&#45;请选择申请开票状态&#45;&#45;</option>-->
<!--                                    <option value="0">未申请</option>-->
<!--                                    <option value="1">部分申请待开票</option>-->
<!--                                    <option value="2">已开票</option>-->
<!--                                    <option value="3">已申请部分开票</option>-->
<!--                                    <option value="4">部分申请部分开票</option>-->
<!--                                    <option value="5">已申请未开票</option>&ndash;&gt;-->
<!--                                </select>-->
                                <select id="invoiceStatus" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="申请开票状态" multiple>
                                    <option value="0">未申请</option>
                                    <!--
                                    <option value="1">部分申请待开票</option>
                                    <option value="4">部分申请部分开票</option>
                                    <option value="7">部分申请不开票</option>
                                    <option value="8">部分申请已开票</option>
                                    <option value="5">已申请未开票</option>
                                    <option value="3">已申请部分开票</option>
                                    <option value="6">已申请不开票</option>
                                    <option value="2">已申请已开票</option>
                                    -->
                                    <option value="1,4">部分申请开票中</option>
                                    <option value="8">部分申请开票完成</option>
                                    <option value="7">部分申请不开票</option>
                                    <option value="3,5">已申请开票中</option>
                                    <option value="2">已申请开票完成</option>
                                    <option value="6">已申请不开票</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="params[spStatus]" class="form-control">
                                    <option value="">-- 应收应申请状态 --</option>
                                    <option value="1">应收款</option>
                                    <option value="2">逾期未收款</option>
                                    <option value="3">应申请开票</option>
                                    <option value="4">逾期未申请开票</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div style="text-align: center">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetx()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary multiple disabled" onclick="receiptAppl()" shiro:hasAnyPermissions="finance:receCheckSheet:receiptAppl,finance:fleet:receCheckSheet:receiptAppl">
                <i class="fa fa-dollar"></i> 收款/开票申请
            </a>
            <!--<a class="btn btn-primary single disabled" onclick="checkAppl()" shiro:hasPermission="finance:receCheckSheet:checkAppl">
                <i class="fa fa-dollar"></i> 开票申请
            </a>-->
            <!--<a class="btn btn-primary single disabled" onclick="managerAppl()" shiro:hasPermission="finance:receCheckSheet:managerAppl">
                <i class="fa fa-dollar"></i> 管理费
            </a>-->

            <a class="btn btn-primary single disabled" onclick="receRecord()" shiro:hasPermission="finance:receCheckSheet:receRecord">
                <i class="fa fa-calculator"></i> 收款记录
            </a>
            <a class="btn btn-warning multiple disabled" onclick="affirm()" shiro:hasAnyPermissions="finance:receCheckSheet:affirm,finance:fleet:receCheckSheet:affirm">
                <i class="fa fa-check-circle-o"></i> 确认
            </a>
            <a class="btn btn-danger multiple disabled" onclick="reverse()" shiro:hasAnyPermissions="finance:receCheckSheet:reverse,finance:fleet:receCheckSheet:reverse">
                <i class="fa fa-mail-reply"></i> 反确认
            </a>
            <a class="btn btn-danger multiple disabled" onclick="removeAll()" shiro:hasAnyPermissions="finance:receCheckSheet:remove,finance:fleet:receCheckSheet:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-primary single disabled" onclick="businessCheckRecord()" shiro:hasAnyPermissions="finance:receCheckSheet:businessCheckRecord,finance:fleet:receCheckSheet:businessCheckRecord">
                <i class="fa fa-calculator"></i> 审核记录
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasAnyPermissions="finance:receCheckSheet:export,finance:fleet:receCheckSheet:export">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-warning single disabled" onclick="checkout()" shiro:hasPermission="finance:receCheckSheet:checkout">
                <i class="fa fa-check-circle-o"></i> 对账包核销
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped" >
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />

<script th:inline="javascript">
    //是否是车队数据
    var isFleet = [[${isFleet}]];

    var prefix = isFleet ? ctx + "fleet/receCheckSheet" : ctx + "receCheckSheet";

    var isFleetData = isFleet ? 1 : 0;

    var billingType = [[${@dict.getType('billing_type')}]];
    //是否开票
    var ifBilling = [[${@dict.getType('if_billing')}]];

    var CloseAccountList = [[${CloseAccountList}]];//关账记录
    var receCheckSheetApplicationStatus = [[${receCheckSheetApplicationStatus}]];//申请状态

    //合计
    var sumTotalAmount = 0;//总应收
    var sumAdjustAmount = 0;//调整额
    var sumGotAmount = 0;//已收金额
    var sumUngotAmount = 0;//未收金额
    var sumApplicationAmount = 0;//已申请金额
    var sumOilApplicationAmount = 0;//开票金额
    var sumCashApplicationAmount = 0;//现金金额
    var totalInvoicedAmount = 0; // 财务开票金额
    //var totalUnInvoicedAmount = 0; // 财务未开票金额

    var date = new Date();
    var year = date.getFullYear();
    var queryReceSheetRecordId = [[${receSheetRecordId}]];
    if (!queryReceSheetRecordId) {
        $("#year").val(year);
    }

    $(function () {
        //隐藏 对账单状态 部分核销、已核销
        $("#vbillstatus").find("option[value='2']").hide();
        $("#vbillstatus").find("option[value='3']").hide();

        var options = {
            url: prefix + "/list",
            exportUrl: ctx + "receCheckSheet/export?isFleetData=" + isFleetData,
            showToggle:false,
            //showColumns:false,
            modalName: "客户对账",
            fixedColumns: true,
            fixedNumber:7,
            clickToSelect: true,
            height: 630,
            showFooter: true,
            showExport: true,
            exportTypes:['excel','csv'],
            exportOptions:{
                ignoreColumn: [0, 1],
                fileName:"客户对账"
            },
            onPostBody:function () {
                //合并页脚
                merge_footer();
                getAmountCount();
                $('[flag="checkNo"]').on('show.bs.tooltip', function () {
                    if ($(this).attr('data-original-title') == "加载中...") {
                        var that = this;
                        $.ajax({
                            url: ctx + 'receCheckSheet/checkNoOf?receSheetRecordId=' + $(this).attr('rece-sheet-record-id'),
                            cache: false,
                            success: function (res) {
                                if (res.code == 0) {
                                    $(that).attr('title', res.data.join(', ')).tooltip('fixTitle').tooltip('show');
                                } else {
                                    $(that).attr('title', res.msg).tooltip('fixTitle').tooltip('show');
                                }
                            },
                            error: function(res) {
                                $(that).attr('title', res.responseText).tooltip('fixTitle').tooltip('show');
                            }
                        })
                    }
                })
            },
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "总应收:<nobr id='sumTotalAmount'>￥0</nobr>&nbsp&nbsp"
                        + "调整金额:<nobr id='sumAdjustAmount'>￥0</nobr>&nbsp&nbsp"
                        + "已收金额:<nobr id='sumGotAmount'>￥0</nobr>&nbsp&nbsp"
                        + "未收金额:<nobr id='sumUngotAmount'>￥0</nobr>&nbsp&nbsp"
                        + "申请金额:<nobr id='sumApplicationAmount'>￥0</nobr>&nbsp&nbsp"
                        //+ "财务开票金额:<nobr id='totalInvoicedAmount'>￥0</nobr>" // 暂不显示开票合计，若要显示，需要把同一申请单的只计算一次
                        + "<br>"
                        + "总合计：总应收：<nobr id='sumTotalAmountTotal'>￥0</nobr>&nbsp&nbsp"
                        + "调整金额：<nobr id='sumAdjustTotalAmountTotal'>￥0</nobr>&nbsp&nbsp"
                        + "已收金额：<nobr id='sumGotAmountTotal'>￥0</nobr>&nbsp&nbsp"
                        + "未收金额：<nobr id='sumUngotAmountTotal'>￥0</nobr>&nbsp&nbsp"
                        + "申请金额：<nobr id='sumApplCheckAmountTotal'>￥0</nobr>&nbsp&nbsp"
                        + "财务开票金额：<nobr id='totalInvoicedAmountTotal'>￥0</nobr>"
                        + "&nbsp;&nbsp;未开票金额：<nobr id='totalUnInvoicedAmountTotal'>￥0</nobr>"
                        ;
                }
            },
                {
                    title: '操作',
                    align: 'left',
                    field: 'receCheckSheetId',
                    formatter: function(value, row, index) {
                        var actions = [];
                        // actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="开票申请" onclick="receiptAppl(\''+row.applicationAmount+'\')"><i  class="fa fa-dollar" style="font-size: 15px;" ></i></a>');
                        // actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="往来明细" onclick="receExchangeDetails()"><i  class="fa fa-exchange" style="font-size: 15px;" ></i></a>');
                        if ([[${@permission.hasAnyPermi('finance:receCheckSheet:receiveList,finance:fleet:receCheckSheet:receiveList')}]] != "hidden") {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="应收明细" onclick="receive(\'' + row.receCheckSheetId + '\',\'' + row.vbillstatus + '\')"><i  class="fa fa fa-list" style="font-size: 15px;" ></i></a>');
                        }
                        if(row.vbillstatus == '0' || (row.vbillstatus == '1' && (row.applicationStatus == '0' || row.applicationStatus == '1'))){
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="调整申请打印" onclick="adjustPrint(\'' + row.receCheckSheetId + '\')"><i  class="fa fa fa-print" style="font-size: 15px;" ></i></a>');
                        }
                        // if ([[${@permission.hasPermi('finance:receCheckSheet:invoiceList')}]] != "hidden" && !isFleet) {
                        //     actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="发货单明细" onclick="reexamine(\'' + value + '\')"><i  class="fa fa-calculator" style="font-size: 15px;" ></i></a>');
                        // }
                        // if ([[${@permission.hasPermi('finance:receCheckSheet:export')}]] != "hidden") {
                        //     actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="导出" onclick="exportExcel(\'' + row.receCheckSheetId + '\')"><i  class="fa fa-download" style="font-size: 15px;" ></i></a>');
                        // }
                        return actions.join('');
                    }
                },
                {
                    title: '申请收款状态',
                    field: 'vbillstatus',
                    formatter:function (value, row, index) {
                        var merageFlag = "";
                        if(row.isMerge == 1){
                            merageFlag = '<span class="label label-warning" style="margin-left: 2px;">合</span>';
                        }
                        var applicationStatus = row.applicationStatus;
                        switch(value) {
                            case 0:
                                return '<span class="label label-default">新建</span>'+merageFlag;;
                            case 1: {
                                if (applicationStatus == 0) {
                                    return '<span class="label label-warning">已确认待申请</span>' + merageFlag;
                                } else if (applicationStatus == 1) {
                                    return '<span class="label label-warning">已确认已部分申请</span>' + merageFlag;
                                } else if (applicationStatus == 2) {
                                    if (row.gotAmount == 0) {
                                        if (row.status == 4) {
                                            return "<span class='label label-info'>已收款</span>" + merageFlag;
                                        } else {
                                            return '<span class="label label-warning">待核销</span>' + merageFlag;
                                        }
                                    } else if (row.ungotAmount === 0) {
                                        return "<span class='label label-info'>已收款</span>" + merageFlag;
                                    } else if (row.adjustAmount != null) {
                                        if (row.adjustAmount != row.ungotAmount) {
                                            return "<span class='label label-warning'>部分核销</span>" + merageFlag;
                                        } else {
                                            return "<span class='label label-danger'>未收</span>" + merageFlag;
                                        }
                                    }
                                    //return '<span class="label label-warning">待核销</span>' + merageFlag;
                                } else if (applicationStatus == 3) {
                                    return '<span class="label label-warning">已确认待审核</span>' + merageFlag;
                                }
                                return '<span class="label label-warning">已确认</span>' + merageFlag;
                            }
                            case 2:
                                return '<span class="label label-info">部分核销</span>'+merageFlag;
                            case 3:
                                return '<span class="label label-success">已核销</span>'+merageFlag;
                            case 4:
                                return '<span class="label label-info">调整待审核</span>'+merageFlag;
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '审核状态',
                    //field: 'adjustCheckStatus',
                    field: 'checkStatus',
                    align: 'left',
                    formatter: function status(value,row) {
                        switch (value) {
                            /*case 0:
                                return '<span class="label label-primary">待业务审</span>';
                            case 1:
                                return '<span class="label label-warning">待总经办审</span>';
                            case 2:
                                return '<span class="label label-info">待财务确认</span>';
                            case 3:
                                return '<span class="label label-success">审核通过</span>';
                            case 4:
                                var txt = '<span class="label label-inverse">审核不通过</span>'
                                if (row.checkMemo) {
                                    txt = txt + ' <i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px" data-html="true" data-container="body" title="' + row.checkMemo.replace(/\n/g, '<br>') + '"></i>'
                                }
                                return txt;
                            default:
                                break;*/
                            case 0:
                                return '<span class="label">未申请</span>';
                            case 1:
                                return '<span class="label label-primary ">待业务审</span>';
                            case 2:
                                return '<span class="label label-warning">待总经办审</span>';
                            case 3:
                                return '<span class="label label-info">待财务复核</span>';
                            case 4:
                                return '<span class="label label-success">审核通过</span>';
                            case 5:
                                var txt = '<span class="label label-inverse">业务未通过</span>';
                                if (row.checkMemo) {
                                    txt = txt + ' <i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px" data-html="true" data-container="body" title="' + row.checkMemo.replace(/\n/g, '<br>') + '"></i>'
                                }
                                return txt;
                            case 6:
                                var txt = '<span class="label label-inverse">总经办未不通过</span>';
                                if (row.checkMemo) {
                                    txt = txt + ' <i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px" data-html="true" data-container="body" title="' + row.checkMemo.replace(/\n/g, '<br>') + '"></i>'
                                }
                                return txt;
                            case 7:
                                var txt = '<span class="label label-inverse">财务未通过</span>'
                                if (row.checkMemo) {
                                    txt = txt + ' <i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px" data-html="true" data-container="body" title="' + row.checkMemo.replace(/\n/g, '<br>') + '"></i>'
                                }
                                return txt;
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '申请开票状态',
                    //field: 'ifInvoice',
                    field: 'invoiceStatus',
                    align: 'left',
                    formatter: function status(value,row) {
                        let txt = '';
                        switch (value) {
                            case 0:
                                txt = '<span class="label label-danger">未申请</span>';
                                break;
                            case 1:
                                txt = '<span class="label label-warning">部分申请待开票</span>';
                                break;
                            case 2:
                                txt = '<span class="label label-info">已申请已开票</span>';
                                break;
                            case 3:
                                txt = '<span class="label label-info">已申请部分开票</span>';
                                break;
                            case 4:
                                txt = '<span class="label label-info">部分申请部分开票</span>';
                                break;
                            case 5:
                                txt = '<span class="label label-info">已申请未开票</span>';
                                break;
                            case 6:
                                txt = '<span class="label label-info">已申请不开票</span>';
                                break;
                            case 7:
                                txt = '<span class="label label-info">部分申请不开票</span>';
                                break;
                            case 8:
                                txt = '<span class="label label-info">部分申请已开票</span>';
                                break;
                            default:
                                break;
                        }
                        if (row.invoiceStatus == 2 || row.invoiceStatus == 3  || row.invoiceStatus == 4 || row.invoiceStatus == 8) {
                            txt = txt + ' <i class="fa fa-question-circle" flag="checkNo" data-toggle="tooltip" style="font-size: 15px" data-container="body" rece-sheet-record-id="' + row.receSheetRecordId + '" data-html="true" title="加载中..."></i>'
                        }
                        return txt;
                    }
                },
                {title: '应收对账单号', field: 'vbillno', align: 'left'},
                {title: '对账名称', field: 'receCheckSheetName', align: 'left'},
                /*{
                    title: '对账状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(value,row,index) {

                        switch(value) {
                            case 0:
                                return '<span class="label label-default">新建</span>';
                            case 1:
                                return '<span class="label label-warning">已确认</span>';
                            case 2:
                                return '<span class="label label-info">部分核销</span>';
                            case 3:
                                return '<span class="label label-success">已核销</span>';
                            case 4:
                                return '<span class="label label-info">调整待审核</span>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '申请状态',
                    field: 'applicationStatus',
                    align: 'left',
                    formatter: function status(value,row,index) {
                        var merageFlag = "";
                        if(row.isMerge == 1){
                            merageFlag = '<span class="label label-warning" style="margin-left: 2px;">合</span>';
                        }
                        switch(value) {
                            case 0:
                                return '<span class="label label-default">待申请</label>'+merageFlag;
                            case 1:
                                return '<span>部分申请</label>'+merageFlag;
                            case 2:
                                return '<span class="label label-primary">已申请</span>'+merageFlag;
                            case 3:
                                return '<span class="label label-success">待审核</span>'+merageFlag;
                            default:
                                break;
                        }
                    }

                },*/


                /*{
                    title: '收款核销状态',
                    field: 'ungotAmount',
                    align: 'left',
                    formatter: function (value, row) {
                        if (value != null) {
                            if (value === 0) {
                                return "<span class='label label-info'>已收</span>";
                            } else if (row.adjustAmount != null) {
                                if (row.adjustAmount != value) {
                                    return "<span class='label label-warning'>部分</span>";
                                } else {
                                    return "<span class='label label-danger'>未收</span>";
                                }
                            }
                        }
                    }
                },*/
                {title: '客户简称', field: 'custName', align: 'left'},
                {
                    title: '总金额(元)',
                    field: 'totalAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '调整金额(元)',
                    field: 'adjustAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '已收金额(元)',
                    field: 'gotAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未收金额(元)',
                    field: 'ungotAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                }/*,
                {
                    title: '申请金额(元)',
                    field: 'applicationAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                }*/,
                {
                    title: '开票金额(元)',
                    field: 'oilApplicationAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '现金金额(元)',
                    field: 'cashApplicationAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '财务开票金额(元)',
                    field: 'invoicedAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    field: 'ifCheck',
                    title: '是否申请开票',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(ifBilling, value);
                    }
                },
                {
                    title: '运营组',
                    align: 'left',
                    field: 'salesDeptName'
                },
                {title: '申请单号', field: 'receSheetRecordNo', align: 'left'},

                /*{
                    title: '开票抬头',
                    field: 'invoiceHead',
                    align: 'left',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.invoiceHead);
                    }
                },*/
                {title: '结算客户', field: 'balaName', align: 'left'},


                {
                    title: '创建人',
                    align: 'left',
                    field: 'regUserId'
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field: 'regDate'
                },
                {
                    title: '申请开票期',
                    field: 'invoiceDays',
                    align: 'right'
                },
                {
                    title: '收款期',
                    field: 'collectionDays',
                    align: 'right'
                },
                {
                    title: '备注',
                    align: 'left',
                    field: 'memo'
                },
                {
                    title: '撤销说明',
                    align: 'left',
                    field: 'replyMemo'
                },

            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });

        loadWarn()
    });

    layui.use('laydate', function(){
        var laydate = layui.laydate;
        laydate.render({
            elem: '#month',
            type: 'month',
            format:"MM"
        });
        laydate.render({
            elem: '#year',
            type: 'year'
        });
    });

    function resetx() {
        $('#status').val('').change();
        $('[name="params[invoiceStatus]"]').val('').change();
        $('[name="params[checkStatus]"]').val('').change();
        $.form.reset();
    }
    /**
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        data.params = new Map();
        //var vbillstatus = $('#vbillstatus').selectpicker('val');
        //if (vbillstatus != null) {
        //    data.params.vbillstatus = $.common.join(vbillstatus);
        //}
        var status = $('#status').selectpicker('val');
        if (status != null) {
            data.params.status = $.common.join(status);
        }
        var invoiceStatus = $('#invoiceStatus').selectpicker('val');
        if (invoiceStatus != null) {
            data.params.invoiceStatus = $.common.join(invoiceStatus);
        }

        $.ajax({
            url: prefix + "/list/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    //总应收
                    $("#sumTotalAmountTotal").text(data.TOTALAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //调整金额
                    $("#sumAdjustTotalAmountTotal").text(data.ADJUSTAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //已收金额
                    $("#sumGotAmountTotal").text(data.GOTAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //未收金额
                    $("#sumUngotAmountTotal").text(data.UNGOTAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //申请金额
                    $("#sumApplCheckAmountTotal").text(data.APPLICATIONAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //开票金额
                    //$("#sumOilApplicationAmountTotal").text(data.OILAPPLICATIONAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //现金金额
                    //$("#sumCashApplicationAmountTotal").text(data.CASHAPPLICATIONAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#totalInvoicedAmountTotal").text(data.TOTALINVOICEDAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#totalUnInvoicedAmountTotal").text((data.ADJUSTAMOUNT - data.TOTALINVOICEDAMOUNT).toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    function detail() {
        var url = prefix + "/detail";
        $.modal.openTab($.table._option.modalName + "详细", url);
    }
    //获取当前年月
    var date = new Date();
    var now = date .getFullYear() + "-" +("0" + (date.getMonth() + 1)).slice(-2);

    /**
     * 收款申请
     */
    function receiptAppl(){
        var vbillstatusArr = $.table.selectColumns('vbillstatus');//对账单状态
        var applicationStatus = $.table.selectColumns('applicationStatus');//申请状态
        var id = $.table.selectColumns('receCheckSheetId');//对账单id
        for(var i = 0 ; i < vbillstatusArr.length ; i++){
            if(vbillstatusArr[i] == 0){
                $.modal.alertError("该账单在新建状态下，不能收款申请");
                return ;
            }
        }
        for(var i = 0 ; i < applicationStatus.length ; i++){
            if(applicationStatus[i] != 0 && id.length > 1){
                $.modal.alertError("请勾选单条查看收款/开票申请");
                return ;
            }
            if(applicationStatus == 3){
                $.modal.alertError("该应收对账正在审核中，无法进行该操作");
                return ;
            }
        }
        //客户一直才可以一起开票申请
        //var customerIdArr = $.table.selectColumns('customerId');
        var customerIdArr = $.table.selectColumns('balaCustomer');<!--2021/1/6为了相同结算客户可以一起收款申请-->
        if(customerIdArr.length > 1){
            $.modal.alertError("不同客户不可以同时进行开票申请");
            return ;
        }
        var vbillstatusArr = $.table.selectColumns('vbillstatus');
        var url = prefix + "/receiptAppl/"+id.join();
        $.modal.openTab('收款/开票申请',url);
    }

    function managerAppl(){
        //对账单id
        var id = $.table.selectColumns('receCheckSheetId');
        $.modal.openTab("管理费申请", prefix + "/manager_apply/" + id);
    }

    /**
     * 开票申请
     */
    /*function checkAppl(){
        var applicationStatus = $.table.selectColumns('applicationStatus').join();//申请状态
        var ifCheck = $.table.selectColumns('ifCheck').join();//是否开票
        if(applicationStatus != 2){
            $.modal.alertError("该账单未进行收款申请，不能进行开票申请");
            return ;
        }
        if(ifCheck != 0){
            $.modal.alertError("该账单未选择开票");
            return ;
        }

        var id = $.table.selectColumns('receCheckSheetId');//对账单id
        var url = prefix + "/checkAppl/"+id;
        $.modal.openTab('开票申请',url);
    }*/
    //收款记录
    function receRecord() {
        var id = $.table.selectColumns('receCheckSheetId');
        var url = ctx + "receCheckSheet/receRecord/"+id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "收款记录",
            area: ['800px', '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 收款申请记录
     */
    function businessCheckRecord() {
        var id = $.table.selectColumns('receCheckSheetId');
        var url = ctx + "receCheckSheet/business_check_record/"+id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "收款申请记录",
            area: ['50%', '65%'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    // 跳转对应的应收明细页面
    function receive(receCheckSheetId,vbillstatus) {
        var url = ctx + "receCheckSheet/receive?receCheckSheetId="+receCheckSheetId + "&vbillstatus=" + vbillstatus;
        $.modal.openTab('应收明细',url);
    }

    function adjustPrint(receCheckSheetId){
        var url = ctx + "receCheckSheet/adjustPrint?receCheckSheetId="+receCheckSheetId;
        layer.open({
            type: 2,
            area: ['550px','300px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '调账申请',
            content: url,
            btn: ['打印', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(index, layero) {
                $(layero).find("iframe")[0].contentWindow.submitHandler();
                console.log(1);
                return;
            },
            //取消按钮
            btn3: function(index, layero){
                layer.close(index);
            }
        });
    }

    // 核销
    function verification() {
        var receCheckSheetIds = $.table.selectColumns('receCheckSheetId');
        var gotAmountList = $.table.selectColumns('gotAmount');
        var totalAmountList = $.table.selectColumns('totalAmount');
        var handVerificationList = $.table.selectColumns('handVerification');
        for (var i = 0; i < gotAmountList.length;i++ ) {
            if (gotAmountList[i] !== totalAmountList[i]) {
                $.modal.alertWarning("请选择已付清的对账单");
                return;
            }
            if (handVerificationList[i] === '0') {
                $.modal.alertWarning("请选择状态为手动核销的对账单");
                return;
            }
        }
        $.operate.post(prefix + "/verification", { "receCheckSheetIds": receCheckSheetIds.join()},location.reload());
    }

    /**
     * 确认应付明细
     */
    function affirm() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 0) {
                $.modal.alertWarning("应收单据只能为新建状态下才能确认");
                return;
            }
        }
        var receCheckSheetIds = $.table.selectColumns("receCheckSheetId");
        if (receCheckSheetIds.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("是否确认？", function () {
            $.operate.post(ctx + "receCheckSheet/affirm", {"receCheckSheetIds": receCheckSheetIds.join()});
        });
    }

    function checkout(){
        var receCheckSheetId = $.table.selectColumns("receCheckSheetId");
        if (receCheckSheetId.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        let vbillstatus = $.table.selectColumns('vbillstatus');
        if(Number($.table.selectColumns('ungotAmount')) != 0 || vbillstatus == '3'){
            $.modal.alertWarning("请选择未收款金额0且未核销的对账包");
        }else{
            $.modal.confirm("是否核销对账包？", function () {
                $.operate.post(prefix + "/checkout", {"receCheckSheetId": receCheckSheetId.join()});
            });
        }

    }

    function exportExcel(receCheckSheetId){
        $.modal.confirm("确定导出该条对账数据吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            var url = prefix + "/importData";
            var data = {receCheckSheetId:receCheckSheetId};
            $.operate.post(url,data,function(result){
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
            // $.post($.table._option.exportUrl, receCheckSheetId, function(result) {
            //     if (result.code == web_status.SUCCESS) {
            //         window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
            //     } else if (result.code == web_status.WARNING) {
            //         $.modal.alertWarning(result.msg)
            //     } else {
            //         $.modal.alertError(result.msg);
            //     }
            //     $.modal.closeLoading();
            // });
        });

    }

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        data.params = new Map();
        //data.params.vbillstatus = $.common.join($('#vbillstatus').selectpicker('val'));
        data.params.status = $.common.join($('#status').selectpicker('val'));
        data.params.invoiceStatus = $.common.join($('#invoiceStatus').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 删除
     */
    function removeAll(){
        var isClose = $.table.selectColumns("isClose");
        //关账判断
       /* for(var i=0 ; i< isClose.length ; i++ ){
            if(isClose[i] == 1){
                $.modal.alertWarning("该对账单已关账，无法进行操作");
                return;
            }
        }*/
        // 选中的行
        var rows = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < rows.length; i++) {
            if (rows[i]["vbillstatus"] !== 0) {
                $.modal.alertWarning("应收单据只能为新建状态下才能删除");
                return;
            }
            if(rows[i]["totalAmount"] != 0){
                $.modal.alertWarning("应收单据总金额为0才能删除");
                return;
            }
            if(rows[i]["gotAmount"] != 0){
                $.modal.alertWarning("应收单据已收金额为0才能删除");
                return;
            }
            if(rows[i]["ungotAmount"] != 0){
                $.modal.alertWarning("应收单据未收金额为0才能删除");
                return;
            }
            if(rows[i]["applicationAmount"] != 0){
                $.modal.alertWarning("应收单据申请金额为0才能删除");
                return;
            }
        }
        var receCheckSheetIds = $.table.selectColumns('receCheckSheetId').join();
        $.modal.confirm("确认要删除选中的" + rows.length + "条数据吗?", function() {
            var url = ctx + "receCheckSheet/removeAll"
            var data = { "ids": receCheckSheetIds};
            $.operate.submit(url, "post", "json", data);
        });
    }

    /**
     * 反确认
     */
    function reverse() {
        var isClose = $.table.selectColumns("isClose");
        //关账判断
       /* for(var i=0 ; i< isClose.length ; i++ ){
            if(isClose[i] == 1){
                $.modal.alertWarning("该对账单已关账，无法进行操作");
                return;
            }
        }*/
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 1) {
                $.modal.alertWarning("应收对账单状态为已确认才能进行反确认");
                return;
            }
        }
        var receCheckSheetIds = $.table.selectColumns("receCheckSheetId").join();
        $.modal.open("反确认", ctx + "receCheckSheet/back_confirm/" + receCheckSheetIds,500,300);
    }


    // 跳转发货单明细页面
    function reexamine(receCheckSheetId) {
        var url = prefix + "/reexamine?receCheckSheetId=" + receCheckSheetId;
        $.modal.openTab("明细", url);
    }

    /**
     * 将总计金额清零
     */
    function clearTotal() {
        sumTotalAmount = 0;//总应收
        sumAdjustAmount = 0;//总应收
        sumGotAmount = 0;//已收金额
        sumUngotAmount = 0;//未收金额
        sumApplicationAmount = 0;//已申请金额
        sumOilApplicationAmount = 0;//开票金额
        sumCashApplicationAmount = 0;//现金金额
        totalInvoicedAmount = 0; // 财务开票金额
    }

    /**
     * 累计总金额
     */
    function addTotal(row) {
        sumTotalAmount = sumTotalAmount + row.totalAmount;//总应收
        sumAdjustAmount = sumAdjustAmount + row.adjustAmount;//总应收
        sumGotAmount = sumGotAmount + row.gotAmount;//已收金额
        sumUngotAmount = sumUngotAmount + row.ungotAmount;//未收金额
        sumApplicationAmount = sumApplicationAmount + row.applicationAmount;//已申请金额
        sumOilApplicationAmount = sumOilApplicationAmount + row.oilApplicationAmount;//申请开票金额
        sumCashApplicationAmount = sumCashApplicationAmount + row.cashApplicationAmount;//开票金额
        totalInvoicedAmount = totalInvoicedAmount + row.invoicedAmount;
    }

    function subTotal(row) {
        sumTotalAmount = sumTotalAmount - row.totalAmount;//总应收
        sumAdjustAmount = sumAdjustAmount - row.adjustAmount;//总应收
        sumGotAmount = sumGotAmount - row.gotAmount;//已收金额
        sumUngotAmount = sumUngotAmount - row.ungotAmount;//未收金额
        sumApplicationAmount = sumApplicationAmount - row.applicationAmount;//已申请金额
        sumOilApplicationAmount = sumOilApplicationAmount - row.oilApplicationAmount;//申请开票金额
        sumCashApplicationAmount = sumCashApplicationAmount - row.cashApplicationAmount;//开票金额
        totalInvoicedAmount = totalInvoicedAmount - row.invoicedAmount
    }

    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#sumTotalAmount").text(sumTotalAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#sumAdjustAmount").text(sumAdjustAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#sumGotAmount").text(sumGotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#sumUngotAmount").text(sumUngotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#sumApplicationAmount").text(sumApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#sumOilApplicationAmount").text(sumOilApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#sumCashApplicationAmount").text(sumCashApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#totalInvoicedAmount").text(totalInvoicedAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }

    function loadWarn() {
        $.ajax({
            url: prefix + '/loadWarn',
            dataType: 'json',
            success: function(aj) {
                if (aj.code == 0) {
                    var d = aj.data;
                    //"YSK_COUNT":377,"YQWSK_COUNT":310,"YSQ_COUNT":266,"YQWSQ_COUNT":192,"YSQ_AMOUNT":55855709.15,"YQWSK_AMOUNT":97145256.17,"YQWSQ_AMOUNT":33443278.34,"YSK_AMOUNT":200035605.61}}
                    var html = []
                    html.push("应收款：", d.YSK_COUNT, '家(', d.YSK_AMOUNT, "元)；");
                    html.push("逾期未收款：", d.YQWSK_COUNT, '家(', d.YQWSK_AMOUNT, "元)；");
                    html.push("应申请开票：", d.YSQ_COUNT, '家(', d.YSQ_AMOUNT, "元)；");
                    html.push("逾期未申请开票：", d.YQWSQ_COUNT, '家(', d.YQWSQ_AMOUNT, "元)");
                    $("#warn_msg").html(html.join(''))
                } else {
                    $("#warn_msg").html(aj.msg)
                }
            }
        })
    }

</script>

</body>
</html>
