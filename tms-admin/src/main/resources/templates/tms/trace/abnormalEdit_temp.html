<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('修改异常跟踪')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .checkbox{
        padding-top: 0 !important;
    }
    .checkbox input[type="radio"] {
        position: absolute;
        clip: rect(0, 0, 0, 0);
    }

    .checkbox input[type='radio'] + label {
        display: block;
        height: 26px;
        padding: 6px 12px;
        font-size: 13px;
        font-weight: 500;
        line-height: 1;
        border: 1px solid #ccc;
        text-align: center;
        float: left;
        margin-right: 10px;
        cursor: pointer;
        border-radius: 2px;
    }

    .checkbox input[type='radio']:checked + label {
        border: 1px solid #009aff;
        color: #009aff;
        border-radius: 2px;
        font-weight: 500;
    }


    .fcff{
        color: #ff1f1f;
    }
    .switch{
        width:40px;
        height:24px;
        border-radius:16px;
        overflow: hidden;
        vertical-align:middle;
        position:relative;
        display: inline-block;
        background:#ccc;
        box-shadow: 0 0 1px #1ab394;
    }
    .switch input{
        visibility: hidden;
    }
    .switch span{
        position:absolute;
        top:0;
        left:0;
        border-radius: 50%;
        background:#fff;
        width:50%;
        height:100%;
        transition:all linear 0.2s;
    }
    .switch span::before{
        position: absolute;
        top:0;
        left:-100%;
        content:'';
        width:200%;
        height:100%;
        border-radius: 30px;
        background:#1ab394;
    }
    .switch span::after{
        content:'';
        position:absolute;
        left:0;
        top:0;
        width:100%;
        height:100%;
        border-radius: 50%;
        background:#fff;
    }
    .switch input:checked +span{
        transform:translateX(100%);
    }
    .f16 {
        font-size: 16px;
    }

    .flex {
        display: flex;
        algin-items: center;
        just-content: space-between;
    }

    .flex_left {
        /* width: 80px; */
        line-height: 24px;
        text-align: right;
    }

    .flex_right {
        min-width: 7em;
        flex: 1;
        line-height: 24px;
    }

    .over {
        overflow: hidden;
    }

    .fl {
        float: left;
    }

    .fr {
        float: right;
    }

    .mt10 {
        margin-top: 10px;
    }

    .mt20 {
        margin-top: 20px;
    }

    .ml20 {
        margin-left: 20px;
    }

    .fw {
        font-weight: bold;
    }

    .ydbox {
        padding: 0 10px;
        border: 1px #eee solid;
        height: 80px;
    }

    .ydcontent {
        background: #7f7f7f;
        border-radius: 10px;
        margin-bottom: 10px;
    }

    .ydcontent_title {
        padding: 5px 10px;
        color: #fff;
    }

    .ydcontent_box {
        padding: 10px 10px;
        border-radius: 10px;
        min-height: 60px;
        box-sizing: border-box;
        background: #fff;
        /*border: 1px #7f7f7f solid;*/
        box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.08), 0px 4px 10px 4px rgba(0, 0, 0, 0.08)
    }

    .fc80 {
        color: #808080;
    }

    .addbtn {
        background: #1ab394;
        color: #fff;
        cursor: pointer;
        width: 120px;
        text-align: center;
        line-height: 30px;
    }

    /*以dhlist开头的class*/
    [class^=dhlist] {
        border: 1px #eee solid;
        width: 100%;
        padding: 3px 0;
    }

    [class^=dhlist] span {
        display: inline-block;
        background: #f3f3f3;
        line-height: 20px;
        padding: 0 5px;
        margin-left: 5px;
        margin-bottom: 5px;
    }

    .table-title {
        text-align: center;
        font-weight: bold;
        line-height: 24px;
        background: #f7f8fa
    }

    .tablebox {
        border: 1px #eee solid;
    }

    .cys {
        padding: 20px 0;
        border-bottom: 1px #9f9f9f dashed;
    }

    .fc33 {
        color: #f33131;
    }

    .fc1a {
        color: #1ab394;
    }

    .bor33 {
        border: 1px #f33131 solid;
    }

    .bor1a {
        border: 1px #1ab394 solid;
    }

    .insurance {
        background: #f8f9fc;
        width: 100%;
    }

    .insurancebox {
        padding: 20px 20px;
    }

    .table_text {
        width: 100%;
        text-align: center;
    }

    .boree {
        border: 1px #eee solid;
        box-sizing: border-box;
        line-height: 20px;
    }

    .tabel_total {
        background: #fffcd3;
        padding: 10px 20px;

    }

    .mt10 {
        margin-top: 10px;
    }

    .mt20 {
        margin-top: 20px;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .btn.btn-file {
        padding: 0px 8px !important;
    }
    .input-group{
        width: 100%;
    }
    .theme-explorer{
        width: 100%;
    }
    .file-input-ajax-new .file-drop-zone-title{
        /*height: 80px;*/
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
    .uploads .table-bordered td{
        border: 0 !important;;
    }

    .boxs{
        box-shadow: 0px 2px 5px 0px rgb(0 0 0 / 8%), 0px 4px 10px 4px rgb(0 0 0 / 8%);
        border-radius: 10px;
    }
    .boxw{
        padding: 5px 0;
    }
    .boxw .flex_left{
        line-height: 26px;
    }
    .fc1ab{
        color: #1ab394;
    }
    .mar10{
        margin-right: 10px;
        margin-top: 10px;
    }
    .del_btn{
        background: #ed5565;
        color: #fff;
        width: 100px;
        line-height: 30px;
        text-align: center;
        height: 30px;
        border-radius: 3px;
    }
    .table>thead>tr>th {
        font-weight: normal;
    }
    .selloutBj{
        position: relative;
        overflow: hidden;
    }
    .sellout {
        background-color: #1c84c6;
        color: #fff !important;
        width: 30%;
        height: 30px;
        line-height: 30px;
        text-align: center;
        position: absolute;
        right: -8%;
        top: 16%;
        transform: rotate(38deg);
        font-size: 14px;
    }
    .selloutT {
        border: 1px solid #333;
        color: #333 !important;
        width: 50%;
        height: 30px;
        line-height: 30px;
        text-align: center;
        position: absolute;
        right: -20%;
        top: 16%;
        transform: rotate(38deg);
        font-size: 14px;
    }
    body{
        background-color: #e7eaec;
    }
    .form-content{
        background-color: transparent;
        padding-bottom: 60px;
    }
    .white-bg {
        padding: 10px 15px;
        border-radius: 5px;
    }
    .toBut{
        vertical-align: middle;
    }
    .f18{
        font-size: 18px;
    }
    .btnT{
        border-radius: 50px;
        padding: 3px 10px;
    }
    .flex{
        align-items: center;
    }
    .taTX{
        min-width: 5em;
    }
    .taTX>div:first-child{
        height: 24px;
        line-height: 24px;
    }
    .naTx{
        margin-top: 5px;
        padding: 5px 0;
        border-top: 1px solid #e7eaec;
        border-bottom: 1px solid #e7eaec;
        text-align: center;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .form-horizontal .radio-inline{
        padding-top: 0;
    }
    .f14{
        font-size: 15px;
    }
    .bTit{
        background-color: #FFE0E0;
        padding: 0 5px;
        border: 1px dashed #ED2929;
        color: #530F0F;
        font-size: 13px;
    }
    .tabr{
        border: 1px dashed;
        padding: 5px 15px;
        border-radius: 5px;
        display: inline-block;
    }
    .label-dangerT{
        color: #ed5565;
        border: 1px solid #ed5565;
        background-color: #fff;
    }
    .tooltip-inner{
        /* background: transparent !important;
        text-align: left !important;
        color:#363636 !important;
        border:1px solid transparent; */
        max-width: 700px !important;
    }

    .ontooltip {
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        background-color: #FFFFFF;
        color: #000;
        text-align: left;
        /* margin: 4px 0; */
        margin: -4px -8px;
    }
</style>
<body>
<div class="form-content">
    <input id="entrustExpId" name="entrustExpId" type="hidden" th:value="${entrustExp.entrustExpId}"/>
    <form id="form-adnormal-add" class="form-horizontal" novalidate="novalidate">
        <input id="isCustomerException" name="isCustomerException" type="hidden" th:value="${entrustExp.isCustomerException}"/>
        <div class="row" style="padding: 0 5px;">
            <div class="col-md-12 col-sm-12">
                <div class="flex boxw bTit">
                    <div class="flex_left">标题-</div>
                    <div class="flex_right" id="exceptionTitle" th:text="${entrustExp.exceptionTitle}"></div>
                </div>
            </div>
        </div>

        <div class="white-bg mt10">
            <div class=" f14 fw text-danger"> 异常类型基本信息 </div>
            <div>
                <label class="checkbox-inline" th:each="dict : ${@dict.getType('exception_type')}">
                    <input type="checkbox" name="expType" th:value="${dict.dictCode}" th:text="${dict.dictLabel}" onclick="onExceptionTitle()"
                    th:checked="${entrustExp.expType != null?#arrays.contains(#strings.arraySplit(entrustExp.expType,','),#strings.toString(dict.dictCode)):'false'}"
                    required="true" aria-required="true">
                    <i class="fa fa-question-circle-o" data-toggle="tooltip" style="font-size: 15px" data-html="true" data-container="body"
                    th:title="${dict.remark}" th:if="${dict.remark != null}"></i>
                </label>
            </div>
            <label id="expType-error" class="error" for="expType"></label>
            <div class="row mt10">

                <div class="col-md-2 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left"><span class="fcff">*</span>异常时间：</div>
                        <div class="flex_right">
                            <input type="text" class="form-control"
                            name="accidentTime" id="accidentTime" placeholder="请选择时间" readonly onblur="onExceptionTitle()"
                            required="true" th:value="${#dates.format(entrustExp.accidentTime, 'yyyy-MM-dd')}"
                            aria-required="true">
                        </div>
                    </div>
                </div>

                <div class="col-md-10 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left">初步损失情况：</div>
                        <div class="flex_right">
                            <input type="text" th:value="${entrustExp.preliminaryLoss}" id="preliminaryLoss" class="form-control" >
                        </div>
                    </div>
                </div>

                <div class="col-md-12 col-sm-12">
                    <div class="flex boxw" style="align-items: flex-start;">
                        <div class="flex_left"><span class="fcff">*</span>简要说明：</div>
                        <div class="flex_right">
                            <textarea id="handleNote" name="handleNote"  th:text="${entrustExp.handleNote}" maxlength="200" class="form-control" rows="3" required="true" aria-required="true"></textarea>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left">是否报保险：</div>
                        <div class="flex_right">
                            <label class="radio-inline">
                                <input type="radio" name="isInsurance" value="1" th:checked="${entrustExp.isInsurance == 1}"> 是
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="isInsurance" value="0" th:checked="${entrustExp.isInsurance == 0}"> 否
                            </label>
                        </div>
                    </div>
                </div>


          <!--      <div class="col-md-4 col-sm-6">
                    <div class="flex boxw">
                        <label class="radio-inline">
                            <input type="radio" name="lockPay" value="1" th:checked="${entrustExp.lockPay == 1}"> 锁定应付
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="lockPay" value="2" th:checked="${entrustExp.lockPay == 2}"> 锁定承运商
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="lockPay" value="0" th:checked="${entrustExp.lockPay == 0}"> 都不锁定
                        </label>
                    </div>
                </div>-->

                <div class="col-md-4 col-sm-6">
                    <div class="flex boxw">
                        <label class="checkbox">
                            <input type="checkbox" name="lockPay" th:checked="${entrustExp.lockPay == 1}"> 锁定应付
                        </label>

                    </div>
                </div>

                <div class="col-md-4 col-sm-6">
                    <div class="flex boxw">
                        <label class="checkbox">
                            <input type="checkbox" name="lockOtherFee" th:checked="${entrustExp.lockOtherFee == 1}"> 锁定三方
                        </label>
                    </div>
                </div>

                <div class="col-md-4 col-sm-6" id="insurancePolicyDiv" style="display: none;">
                    <div class="flex boxw">
                        <div class="flex_left">保险：</div>
                        <div class="flex_right">
                            <input type="text" id="insurancePolicy" class="form-control" readonly
                                   onclick="showInsurancePolicy()" placeholder="点击选择保险">
                            <input type="hidden" id="insurancePolicyId" name="insurancePolicyId" th:value="${entrustExp.insurancePolicyId}">
                        </div>
                    </div>
                </div>

                <div class="col-md-4 col-sm-6" id="reportNoDiv">
                    <div class="flex boxw">
                        <div class="flex_left">报案号：</div>
                        <div class="flex_right">
                            <input type="text" th:value="${entrustExp.reportNo}" id="reportNo" class="form-control" required="false"
                                   aria-required="false">
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <div class="white-bg mt10">
            <div class="f14 fw"> 客户/承运商信息 </div>
            <div class="row mt10">
                <div class="col-md-6 col-sm-6">
                    <div style="text-align: right;">
                        <span class="label label-warning pa2">总金额</span>
                        <span id="thdzje">0</span>
                        <span class="label label-danger pa2">增减合计(不纳入利润计算)</span>
                        <span id="thdzpk">0</span>
                    </div>
                    <a data-toggle="collapse" href="#collapse" role="button" aria-expanded="false" aria-controls="collapse" style="width: 100%;">
                        <div class="flex" style="align-items: center;">
                            <div class="ydbox flex selloutBj" style="border-radius: 5px;width: 100%;justify-content: space-between;">
                                <div class="flex">
                                    <img th:src="@{/img/hu.png}" style="width: 60px;height: 60px;"/>
                                    <div class="ml5">
                                        <div>
                                            <span class="toBut fw " th:text="${invoiceReceiveDetail.custAbbr}"></span>
                                        </div>
                                        <div class="mt10 flex">
                                            <div class="flex">
                                                <div class="flex_left fc80">运营部：</div>
                                                <div class="flex_right" th:text="${invoiceReceiveDetail.salesName}"></div>
                                            </div>
                                            <div class="flex">
                                                <div class="flex_left fc80">业务员：</div>
                                                <div class="flex_right" th:text="${invoiceReceiveDetail.deptName}"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <span class="btnT btn-white ml5 toBut">客户</span>
                            </div>
                        </div>
                    </a>
                    <div class="collapse in" id="collapse">
                        <div class="over">
                            <div class="hidden" id="fixedLotT" th:text="${invoiceReceiveDetail.vbillno}" ></div>
                            <div class="dhlistR hidden">
                                <span th:text="${invoiceReceiveDetail.vbillno}"></span>
                                <span th:each="more:${invoiceReceiveDetailList}" th:text="${more.vbillno}"></span>
                            </div>
                            <table class="table table-bordered">
                                <thead style="background:#f7f8fa">
                                <tr>
                                    <th style="text-align: center;" th:onclick="invoiceReceive([[${invoiceReceiveDetail.customerId}]])">
                                        <i class="fa fa-plus text-navy" style="font-size: 12px;"></i>
                                    </th>
                                    <th >发货单信息</th>
                                    <th >货品信息</th>
                                    <th >收款信息</th>
                                    <th >
                                        应收增减
                                        <i class="fa fa-question-circle-o" data-toggle="tooltip" style="font-size: 15px" data-html="true" data-container="body"
                                           title="不纳入利润计算"></i>
                                    </th>
                                    <th style="text-align: center;width: 8%;">操作</th>
                                </tr>
                                </thead>
                                <tbody class="yingfuR">
                                    <th:block th:each="item:${invoiceReceiveDetail}">
                                        <tr th:each="receiveDetail,status:${item.receiveDetailList}">
                                            <td th:rowspan="${status.size}" th:if="${status.index==0}"></td>
                                            <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                <div> 发货单-<span th:text="${item.vbillno}"></span> </div>
                                                <div>
                                                    <span class="label label-warning pa2">装</span>
                                                    <span th:text="${item.shippingAddress}"></span>
                                                </div>
                                                <div>
                                                    <span class="label label-success pa2">卸</span>
                                                    <span th:text="${item.receivingAddress}"></span>
                                                </div>
                                            </td>
                                            <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                <div class="flex" style="background-color: #F4F5F7;padding: 8px;">
                                                    <img th:src="@{/img/wp.png}" style="width: 20px;height: 20px;"/>
                                                    <span th:text="${item.goodsName}"></span>
                                                    <span class="ml5" th:text="${item.numCount+'件'}"></span>
                                                    <span class="ml5" th:text="${item.weightCount+'吨'}"></span>
                                                    <span class="ml5" th:text="${item.volumeCount+'m³'}"></span>
                                                </div>
                                                <div>
                                                    <span th:text="${@dict.getLabel('car_len',item.carLen)}"></span>
                                                    <span th:text="${item.carTypeName}"></span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="flex">
                                                    <div th:each="dict : ${receiveDetailStatusEnum}" th:text="${dict.context}" th:if="${dict.value}==${receiveDetail.vbillstatus}"></div>
                                                    <div class="ml5">
                                                        <div><span th:text="${receiveDetail.transFeeCount}"></span> -
                                                            <span th:each=" dict : ${@dict.getType('bala_type')}" th:text="${dict.dictLabel}" th:if="${dict.dictValue}==${receiveDetail.balatype}"></span> </div>
                                                        <div th:text="${receiveDetail.vbillno}"></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                <!--总金额-->
                                                <input type="hidden"
                                                       name="feeCnt"
                                                       th:id="|${item.invoiceId}_all_receiveFeeCnt|"
                                                       th:value="${#aggregates.sum(item.receiveDetailList.![transFeeCount==null || payoutMark ==1? 0:transFeeCount])}">

                                                <!-- 应收增减基础值 -->
                                                <input type="hidden" th:id="|${item.invoiceId}_base_feeCnt|" th:value="${#aggregates.sum(item.receiveDetailList.![payoutMark!=1&&transFeeCount!=null? 0:transFeeCount])}">

                                                <th:block th:if="${#aggregates.sum(item.receiveDetailList.![payoutMark!=1&&transFeeCount!=null? 0:transFeeCount]) == 0}">
                                                    <span class="label label-dangerT"></span>
                                                    <input type="hidden" name="invoiceIds" th:value="${item.invoiceId+'-0-deduct'}">
                                                </th:block>
                                                <th:block th:unless="${#aggregates.sum(item.receiveDetailList.![payoutMark!=1&&transFeeCount!=null? 0:transFeeCount]) == 0}">
                                                    <span class="label label-dangerT">
                                                        <th:block th:if="${#aggregates.sum(item.receiveDetailList.![payoutMark!=1&&transFeeCount!=null? 0:transFeeCount]) > 0}">
                                                            增<span th:text="${#aggregates.sum(item.receiveDetailList.![payoutMark!=1&&transFeeCount!=null? 0:transFeeCount])}"></span>
                                                        </th:block>
                                                        <th:block th:unless="${#aggregates.sum(item.receiveDetailList.![payoutMark!=1&&transFeeCount!=null? 0:transFeeCount]) > 0}">
                                                            减<span th:text="${#strings.substringAfter( #aggregates.sum(item.receiveDetailList.![payoutMark!=1&&transFeeCount!=null? 0:transFeeCount]) ,'-')}"></span>
                                                        </th:block>
                                                    </span>
<!--                                                    <input type="hidden" name="invoiceIds"-->
<!--                                                           th:value="${item.invoiceId + '-'-->
<!--                                                                        + (#aggregates.sum(item.receiveDetailList.![payoutMark!=1 && transFeeCount!=null ? 0 : transFeeCount]).abs())-->
<!--                                                                        + '-'-->
<!--                                                                        + (#aggregates.sum(item.receiveDetailList.![payoutMark!=1 && transFeeCount!=null ? 0 : transFeeCount]) > 0 ? 'add' : 'deduct')}">-->
                                                    <input type="hidden" name="invoiceIds" th:value="${item.invoiceId+'-0-deduct'}">

                                                </th:block>
                                            </td>
                                            <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                <div shiro:hasAnyPermissions="tms:trace:abnormal:showReceBtn">
                                                    <input type="hidden" name="transFeeCount1" th:value="${#aggregates.sum(item.receiveDetailList.![transFeeCount==null? 0:transFeeCount])}">
                                                    <span class="text-success" style="cursor:pointer"
                                                    onclick="compensation(this,this.getAttribute('data-invoiceId'),this.getAttribute('data-transFeeCounts'))"
                                                    th:data-invoiceId="${item.invoiceId}" th:data-transFeeCounts="${#aggregates.sum(item.receiveDetailList.![vbillstatus==3||vbillstatus==4||transFeeCount==null? 0:transFeeCount])}">应收增减</span>
                                                </div>

                                            </td>
                                        </tr>
                                    </th:block>

                                    <th:block th:each="item:${invoiceReceiveDetailList}">
                                        <tr th:each="receiveDetail,status:${item.receiveDetailList}">
                                            <td th:rowspan="${status.size}" th:if="${status.index==0}"></td>
                                            <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                <div> 发货单-<span th:text="${item.vbillno}"></span> </div>
                                                <div>
                                                    <span class="label label-warning pa2">装</span>
                                                    <span th:text="${item.shippingAddress}"></span>
                                                </div>
                                                <div>
                                                    <span class="label label-success pa2">卸</span>
                                                    <span th:text="${item.receivingAddress}"></span>
                                                </div>
                                            </td>
                                            <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                <div class="flex" style="background-color: #F4F5F7;padding: 8px;">
                                                    <img th:src="@{/img/wp.png}" style="width: 20px;height: 20px;"/>
                                                    <span th:text="${item.goodsName}"></span><span class="ml5" th:text="${item.weightCount+'吨'}"></span>
                                                </div>
                                                <div>
                                                    <span th:text="${@dict.getLabel('car_len',item.carLen)}"></span>
                                                    <span th:text="${item.carTypeName}"></span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="flex">
                                                    <div th:each="dict : ${receiveDetailStatusEnum}" th:text="${dict.context}" th:if="${dict.value}==${receiveDetail.vbillstatus}"></div>
                                                    <div class="ml5">
                                                        <div>
                                                            <span th:text="${receiveDetail.transFeeCount}"></span> -
                                                            <span th:each=" dict : ${@dict.getType('bala_type')}" th:text="${dict.dictLabel}" th:if="${dict.dictValue}==${receiveDetail.balatype}"></span>
                                                        </div>
                                                        <div th:text="${receiveDetail.vbillno}"></div>

                                                    </div>
                                                </div>
                                            </td>
                                            <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                <!--总金额-->
                                                <input type="hidden"
                                                       name="feeCnt"
                                                       th:id="|${item.invoiceId}_all_receiveFeeCnt|"
                                                       th:value="${#aggregates.sum(item.receiveDetailList.![transFeeCount==null || payoutMark ==1? 0:transFeeCount])}">

                                                <!-- 应收增减基础值 -->
                                                <input type="hidden" th:id="|${item.invoiceId}_base_feeCnt|" th:value="${#aggregates.sum(item.receiveDetailList.![payoutMark!=1&&transFeeCount!=null? 0:transFeeCount])}">

                                                <th:block th:if="${#aggregates.sum(item.receiveDetailList.![payoutMark!=1&&transFeeCount!=null? 0:transFeeCount]) == 0}">
                                                    <span class="label label-dangerT"></span>
                                                    <input type="hidden" name="invoiceIds" th:value="${item.invoiceId+'-0-deduct'}">
                                                </th:block>
                                                <th:block th:unless="${#aggregates.sum(item.receiveDetailList.![payoutMark!=1&&transFeeCount!=null? 0:transFeeCount]) == 0}">
                                                    <span class="label label-dangerT">
                                                        <th:block th:if="${#aggregates.sum(item.receiveDetailList.![payoutMark!=1&&transFeeCount!=null? 0:transFeeCount]) > 0}">
                                                            增<span th:text="${#aggregates.sum(item.receiveDetailList.![payoutMark!=1&&transFeeCount!=null? 0:transFeeCount])}"></span>
                                                        </th:block>
                                                        <th:block th:unless="${#aggregates.sum(item.receiveDetailList.![payoutMark!=1&&transFeeCount!=null? 0:transFeeCount]) > 0}">
                                                            减<span th:text="${#strings.substringAfter( #aggregates.sum(item.receiveDetailList.![payoutMark!=1&&transFeeCount!=null? 0:transFeeCount]) ,'-')}"></span>
                                                        </th:block>
                                                    </span>
<!--                                                    <input type="hidden" name="invoiceIds"-->
<!--                                                           th:value="${item.invoiceId + '-'-->
<!--                                                                        + (#aggregates.sum(item.receiveDetailList.![payoutMark!=1 && transFeeCount!=null ? 0 : transFeeCount]).abs())-->
<!--                                                                        + '-'-->
<!--                                                                        + (#aggregates.sum(item.receiveDetailList.![payoutMark!=1 && transFeeCount!=null ? 0 : transFeeCount]) > 0 ? 'add' : 'deduct')}">-->
                                                    <input type="hidden" name="invoiceIds" th:value="${item.invoiceId+'-0-deduct'}">

                                                </th:block>
                                            </td>
                                            <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                <div shiro:hasAnyPermissions="tms:trace:abnormal:showReceBtn">
                                                    <input type="hidden" name="transFeeCount1" th:value="${#aggregates.sum(item.receiveDetailList.![transFeeCount==null? 0:transFeeCount])}">
                                                    <span class="text-success" style="cursor:pointer"
                                                          onclick="compensation(this,this.getAttribute('data-invoiceId'),this.getAttribute('data-transFeeCounts'))"
                                                          th:data-invoiceId="${item.invoiceId}" th:data-transFeeCounts="${#aggregates.sum(item.receiveDetailList.![vbillstatus==3||vbillstatus==4||transFeeCount==null? 0:transFeeCount])}">应收增减</span>
                                                </div>

                                            </td>
                                        </tr>
                                    </th:block>
                                </tbody>
                            </table>
                        </div>
                    </div>


                </div>

                <div class="col-md-6 col-sm-6">
                    <div style="text-align: right;">
                        <span class="label label-warning pa2">总金额</span>
                        <span id="ydzje">0</span>
                        <span class="label label-primary pa2">应付扣减合计</span>
                        <span id="ydzpk">0</span>
                        <!--<span class="label label-primary pa2">总赔款</span>
                        <span id="ydzpk_pk">0</span>-->

                    </div>
                    <div id="addCarrier">
                        <div class="flex" style="align-items: center;">
                            <div style="width: 30px;">
                                <div onclick="showCarrier()">
                                    <i class="fa fa-plus text-navy" aria-hidden="true"  style="font-size: 20px;"></i>
                                </div>
                            </div>
                            <div style="width: 100%;">
                                <div class="ydbox flex selloutBj" style="border-radius: 5px;width: 100%;justify-content: space-between;">
                                    <div class="flex">
                                        <img th:src="@{/img/kh.png}" style="width: 60px;height: 60px;"/>
                                        <div class="ml5">
                                            <div class="flex">
                                                <div class="flex">
                                                    <input type="hidden" name="carrierId" id="carrierId" class="form-control" th:value="${lotPayDetailPayRecord.carrier.carrierId}">
                                                    <div class="fw">
                                                        <span>[[${lotPayDetailPayRecord.carrier.carrName}]]</span>
                                                        <span class="label label-primary pa2">[[${lotPayDetailPayRecord.carrier.balaType==1?'单笔':'月结'}]]</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex">
                                                <div class="flex">
                                                    <div class="flex_left fc80">联系人：</div>
                                                    <div class="flex_right" th:text="${lotPayDetailPayRecord.carrier.contact+'/'+lotPayDetailPayRecord.carrier.phone}"></div>
                                                </div>
                                            </div>

                                            <div class="flex">
                                                <div class="flex">
                                                    <div class="flex_left fc80">锁定付款：</div>
                                                    <div class="flex_right">
                                                        <label class="radio-inline">
                                                            <input type="radio" name="carrierLockStatus_0" value="1"
                                                                   th:checked="${lotPayDetailPayRecord.carrier.lockPay == 1}"
                                                                   onclick="toggleLockRemark(true,0)"> 锁定
                                                        </label>
                                                        <label class="radio-inline">
                                                            <input type="radio" name="carrierLockStatus_0" value="0"
                                                                   th:checked="${lotPayDetailPayRecord.carrier.lockPay == 0}"
                                                                   onclick="toggleLockRemark(false,0)"> 不锁定
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <textarea name="lockPayReason_0" id="lockPayReason_0" class="form-control"
                                              th:text="${lotPayDetailPayRecord.carrier.lockPayReason}"
                                              placeholder="锁定备注" th:style="${lotPayDetailPayRecord.carrier.lockPay == 0 ? 'margin: 0 10px; flex: 1; height: 55px; resize: none;display: none;':'margin: 0 10px; flex: 1; height: 55px; resize: none;'}"></textarea>
                                    <input id="carrierId_0" type="hidden" th:value="${lotPayDetailPayRecord.carrier.carrierId}">

<!--                                    <span class="btnT btn-white ml5 toBut">承运商</span>-->
                                </div>
                            </div>
                        </div>
                        <div class="collapse in" id="collapseExample">
                            <div class="over" style="margin-left: 30px;">
                                <div>
                                    <div class="hidden" id="fixedLot" th:text="${lotPayDetailPayRecord.lot}" ></div>
                                    <div class="dhlistT hidden">
                                        <span th:text="${lotPayDetailPayRecord.lot}"></span>
                                    </div>

                                    <table class="table table-bordered">
                                        <thead style="background:#f7f8fa">
                                        <tr>
                                            <th style="text-align: center;width: 2%;" th:onclick="showEntrusts('0',[[${lotPayDetailPayRecord.carrier.carrierId}]],1)">
                                                <i class="fa fa-plus text-navy" style="font-size: 12px;"></i>
                                            </th>
                                            <th style="width: 40%;">运单信息</th>
                                            <th style="width: 30%;">付款信息</th>
                                            <th style="width: 20%;">应付扣减</th>
                                            <th style="text-align: center;width: 8%;">操作</th>
                                        </tr>
                                        </thead>
                                        <tbody class="yingfuT">
                                            <th:block th:each="item:${lotPayDetailPayRecord}">
                                                <input type="hidden" name="lotIds" th:value="${item.entrustLotId}">
                                                <tr th:each="payDetail,status:${item.payDetailList}">
                                                    <td th:rowspan="${status.size}" th:if="${status.index==0}"></td>
                                                    <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                        <div> 运单-<span th:text="${item.lot}"></span> </div>
                                                        <div class="flex mt5">
                                                            <img th:src="@{/img/sj.png}" style="width: 20px;height: 20px;"/><span th:if="${item.driverName != null}" th:text="${item.driverName+'/'+item.driverPhone}"></span>
                                                        </div>
                                                        <div class="flex mt5">
                                                            <img th:src="@{/img/cl.png}" style="width: 20px;height: 20px;"/><span th:if="${item.carNo != null}" th:text="${item.carNo}"></span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div  class="flex">
                                                            <div th:each="dict : ${payDetailStatusEnum}" th:text="${dict.context}" th:if="${dict.value}==${payDetail.vbillstatus}"></div>
                                                            <div class="ml0">
                                                                <div>
                                                                    <span th:text="${payDetail.transFeeCount}"></span> -
                                                                    <th:block th:if="${payDetail.freeType} == '0'">
                                                                        <span th:each=" dict : ${@dict.getType('cost_type_freight')}" th:text="${dict.dictLabel}" th:if="${dict.dictValue}==${payDetail.costTypeFreight}"></span>
                                                                    </th:block>
                                                                    <th:block th:unless="${payDetail.freeType} == '0'">
                                                                        <span th:each=" dict : ${@dict.getType('cost_type_on_way')}" th:text="${dict.dictLabel}" th:if="${dict.dictValue}==${payDetail.costTypeOnWay}"></span>
                                                                    </th:block>
                                                                </div>
                                                                <div th:text="${payDetail.vbillno}"></div>
                                                                <div th:each="payRecord:${payDetail.payRecordList}">
                                                                    <span th:each="dict : ${@dict.getType('pay_method')}" th:if="${ dict.dictValue == payRecord.payMethod+'' }" th:text="${dict.dictLabel}"></span> - <span th:text="${payRecord.recCardNo}"></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                        <div>
                                                            扣：<span th:id="|${item.entrustLotId}_kk_sapn|"
                                                                     th:text="${#aggregates.sum(item.payDetailList.![transFeeCount==null or incomeRemark!=1 ? 0:transFeeCount.abs()])}"
                                                                    class="label label-dangerT" ></span>
                                                            <input th:id="|${item.entrustLotId}_kk_input|" type="hidden" name="lotIdTransFeeList" th:value="${item.entrustLotId+'-0'}">
                                                        </div>
                                                        <!--<div class="mt20">
                                                            赔：<span th:id="|${item.entrustLotId}_pk_sapn|"
                                                                    th:text="${#aggregates.sum(item.payDetailList.![transFeeCount==null or incomeRemark!=2 ? 0:transFeeCount.abs()])}"                                                                    class="label label-dangerT"></span>
                                                        </div>-->
                                                    </td>
                                                    <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                        <!-- 可扣金额-->
                                                        <input type="hidden"
                                                               name="transFeeCount2"
                                                               th:id="|${item.entrustLotId}_transFeeCount2|"
                                                               th:value="${#aggregates.sum(item.payDetailList.![transFeeCount==null or (vbillstatus!=0 and vbillstatus!=1) or incomeRemark!=0? 0:transFeeCount])}">
                                                        <!--总金额-->
                                                        <input type="hidden"
                                                               th:id="|${item.entrustLotId}_all_feeCnt|"
                                                               th:value="${#aggregates.sum(item.payDetailList.![transFeeCount==null or incomeRemark!=0? 0:transFeeCount])}">

                                                        <div>
                                                            <input type="hidden"
                                                                   th:id="|${item.entrustLotId}_kk_feeCnt|"
                                                                   th:value="${#aggregates.sum(item.payDetailList.![transFeeCount==null or incomeRemark!=1 ? 0:transFeeCount.abs()])}">
                                                            <span shiro:hasPermission="tms:trace:abnormal:deduct" class="text-success" style="cursor:pointer" onclick="deduct(this)"
                                                                  th:data-lotno="${item.entrustLotId}">应付扣减</span>
                                                        </div>
                                                        <div class="mt20" style="cursor: pointer;">
                                                            <input type="hidden"
                                                                   th:id="|${item.entrustLotId}_pk_feeCnt|"
                                                                   th:value="${#aggregates.sum(item.payDetailList.![transFeeCount==null or incomeRemark!=2 ? 0:transFeeCount.abs()])}">
                                                            <!--<span shiro:hasPermission="tms:trace:abnormal:deduct" class="text-success" onclick="deductDed(this)"
                                                                  th:data-lotno="${item.entrustLotId}">赔款</span>-->
                                                        </div>
                                                    </td>
                                                </tr>
                                            </th:block>

                                            <th:block th:each="mapS,status:${lotPayDetailPayRecordList}" th:if="${mapS.carrierId == lotPayDetailPayRecord.carrier.carrierId}">
                                                <th:block th:each="item:${mapS.entrustLotPayDetailPayRecordVOList}">
                                                    <input type="hidden" name="lotIds" th:value="${item.entrustLotId}">
                                                    <tr th:each="payDetail,status:${item.payDetailList}" th:id="|${item.entrustLotId}_${status.index}|">
                                                        <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                            <i class="fa fa-close" th:data-lot-id="${item.entrustLotId}" onclick="removeLotById(this)"></i>
                                                        </td>
                                                        <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                            <div> 运单-<span th:text="${item.lot}"></span> </div>
                                                            <div class="flex mt5">
                                                                <img th:src="@{/img/sj.png}" style="width: 20px;height: 20px;"/><span th:if="${item.driverName != null}" th:text="${item.driverName+'/'+item.driverPhone}"></span>
                                                            </div>
                                                            <div class="flex mt5">
                                                                <img th:src="@{/img/cl.png}" style="width: 20px;height: 20px;"/><span th:if="${item.carNo != null}" th:text="${item.carNo}"></span>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div  class="flex">
                                                                <div th:each="dict : ${payDetailStatusEnum}" th:text="${dict.context}" th:if="${dict.value}==${payDetail.vbillstatus}"></div>
                                                                <div class="ml0">
                                                                    <div>
                                                                        <span th:text="${payDetail.transFeeCount}"></span> -
                                                                        <th:block th:if="${payDetail.freeType} == '0'">
                                                                            <span th:each=" dict : ${@dict.getType('cost_type_freight')}" th:text="${dict.dictLabel}" th:if="${dict.dictValue}==${payDetail.costTypeFreight}"></span>
                                                                        </th:block>
                                                                        <th:block th:unless="${payDetail.freeType} == '0'">
                                                                            <span th:each=" dict : ${@dict.getType('cost_type_on_way')}" th:text="${dict.dictLabel}" th:if="${dict.dictValue}==${payDetail.costTypeOnWay}"></span>
                                                                        </th:block>
                                                                    </div>
                                                                    <div th:text="${payDetail.vbillno}"></div>
                                                                    <div th:each="payRecord:${payDetail.payRecordList}">
                                                                        <span th:each="dict : ${@dict.getType('pay_method')}" th:if="${ dict.dictValue == payRecord.payMethod+'' }" th:text="${dict.dictLabel}"></span> - <span th:text="${payRecord.recCardNo}"></span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                            <div>
                                                                扣：<span th:id="|${item.entrustLotId}_kk_sapn|"
                                                                        th:text="${#aggregates.sum(item.payDetailList.![transFeeCount==null or incomeRemark!=1 ? 0:transFeeCount.abs()])}"
                                                                        class="label label-dangerT"></span>
                                                                <input th:id="|${item.entrustLotId}_kk_input|" type="hidden" name="lotIdTransFeeList" th:value="${item.entrustLotId+'-0'}">
                                                            </div>
<!--                                                            <div class="mt20">-->
<!--                                                                赔：<span th:id="|${item.entrustLotId}_pk_sapn|"-->
<!--                                                                        th:text="${#aggregates.sum(item.payDetailList.![transFeeCount==null or incomeRemark!=2 ? 0:transFeeCount.abs()])}"                                                                        class="label label-dangerT"></span>-->
<!--                                                            </div>-->
                                                        </td>
                                                        <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                            <!-- 可扣金额-->
                                                            <input type="hidden"
                                                                   name="transFeeCount2"
                                                                   th:id="|${item.entrustLotId}_transFeeCount2|"
                                                                   th:value="${#aggregates.sum(item.payDetailList.![transFeeCount==null or (vbillstatus!=0 and vbillstatus!=1) or incomeRemark!=0? 0:transFeeCount])}">
                                                            <!--总金额-->
                                                            <input type="hidden"
                                                                   th:id="|${item.entrustLotId}_all_feeCnt|"
                                                                   th:value="${#aggregates.sum(item.payDetailList.![transFeeCount==null or incomeRemark!=0? 0:transFeeCount])}">
                                                            <div>
                                                                <input type="hidden"
                                                                       th:id="|${item.entrustLotId}_kk_feeCnt|"
                                                                       th:value="${#aggregates.sum(item.payDetailList.![transFeeCount==null or incomeRemark!=1 ? 0:transFeeCount.abs()])}">
                                                                <span shiro:hasPermission="tms:trace:abnormal:deduct" class="text-success" style="cursor:pointer" onclick="deduct(this)"
                                                                      th:data-lotno="${item.entrustLotId}">应付扣减</span>
                                                            </div>
                                                            <div class="mt20" style="cursor: pointer;">
                                                                <input type="hidden"
                                                                       th:id="|${item.entrustLotId}_pk_feeCnt|"
                                                                       th:value="${#aggregates.sum(item.payDetailList.![transFeeCount==null or incomeRemark!=2 ? 0:transFeeCount.abs()])}">
<!--                                                                <span shiro:hasPermission="tms:trace:abnormal:deduct" class="text-success" onclick="deductDed(this)"-->
<!--                                                                      th:data-lotno="${item.entrustLotId}">赔款</span>-->
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </th:block>
                                            </th:block>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="cys" th:each="mapS,status:${lotPayDetailPayRecordList}" th:if="${mapS.carrierId != lotPayDetailPayRecord.carrier.carrierId}">
                        <div class="flex" style="align-items: center;">
                            <div style="width: 30px;"></div>
                            <div style="width: 100%;">
                                <div class="ydbox flex selloutBj" style="border-radius: 5px;width: 100%;justify-content: space-between;">
                                    <div class="flex">
                                        <img th:src="@{/img/kh.png}" style="width: 60px;height: 60px;"/>
                                        <div class="ml5">
                                            <div class="flex">
                                                <div class="flex">
                                                    <input type="hidden" name="carrierId" id="carrierId" class="form-control" th:value="${mapS.carrierId}">
                                                    <div class="fw">
                                                        <span>[[${mapS.carrName}]]</span>
                                                        <span class="label label-primary pa2">[[${mapS.balaType==1?'单笔':'月结'}]]</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex">
                                                <div class="flex">
                                                    <div class="flex_left fc80">联系人：</div>
                                                    <div class="flex_right" th:text="${mapS.contact+'/'+mapS.phone}"></div>
                                                </div>
                                            </div>
                                            <div class="flex">
                                                <div class="flex">
                                                    <div class="flex_left fc80">锁定付款：</div>
                                                    <div class="flex_right">
                                                        <label class="radio-inline">
                                                            <input type="radio" th:name="|carrierLockStatus_${status.index+1}|" value="1"
                                                                   th:checked="${mapS.lockPay == 1}"
                                                                   th:onclick="|toggleLockRemark(true,${status.index+1})|"> 锁定
                                                        </label>
                                                        <label class="radio-inline">
                                                            <input type="radio" th:name="|carrierLockStatus_${status.index+1}|" value="0"
                                                                   th:checked="${mapS.lockPay == 0}"
                                                                   th:onclick="|toggleLockRemark(false,${status.index+1})|"> 不锁定
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <textarea th:name="|lockPayReason_${status.index+1}|" th:id="|lockPayReason_${status.index+1}|" class="form-control"
                                              th:text="${mapS.lockPayReason}"
                                              placeholder="锁定备注" th:style="${mapS.lockPay == 0 ? 'margin: 0 10px; flex: 1; height: 55px; resize: none;display: none;':'margin: 0 10px; flex: 1; height: 55px; resize: none;'}"></textarea>
                                    <input th:id="|carrierId_${status.index+1}|" type="hidden" th:value="${mapS.carrierId}">


<!--                                    <span class="btnT btn-white ml5 toBut">承运商</span>-->
                                </div>
                            </div>
                        </div>

                       <div class="collapse in" th:id="|collapseExample${status.index}|">
                            <div class="over" style="margin-left: 30px;">
                                <div>
                                    <div th:class="dhlist + ${status.index}+' '+hidden">
                                        <span th:each="more,sta:${mapS.entrustLotPayDetailPayRecordVOList}" th:text="${more.lot}"> </span>
                                    </div>

                                    <table class="table table-bordered">
                                        <thead style="background:#f7f8fa">
                                        <tr>
                                            <th style="text-align: center;width: 2%;" th:onclick="showEntrusts('[[${status.index}]]',[[${mapS.carrierId}]])">
                                                <i class="fa fa-plus text-navy" style="font-size: 12px;"></i>
                                            </th>
                                            <th style="width: 40%;">运单信息</th>
                                            <th style="width: 30%;">付款信息</th>
                                            <th style="width: 20%;">应付扣减</th>
                                            <th style="text-align: center;width: 8%;">操作</th>
                                        </tr>
                                        </thead>
                                        <tbody th:class="yingfu + ${status.index} + ' ' + over">
                                            <th:block th:each="item:${mapS.entrustLotPayDetailPayRecordVOList}">
                                                <input type="hidden" name="lotIds" th:value="${item.entrustLotId}">
                                                <tr th:each="payDetail,status:${item.payDetailList}" th:id="|${item.entrustLotId}_${status.index}|">
                                                    <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                        <i class="fa fa-close" th:data-lot-id="${item.entrustLotId}" onclick="removeLotById(this)"></i>
                                                    </td>
                                                    <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                        <div> 运单-<span th:text="${item.lot}"></span> </div>
                                                        <div class="flex mt5">
                                                            <img th:src="@{/img/sj.png}" style="width: 20px;height: 20px;"/><span th:if="${item.driverName != null}" th:text="${item.driverName+'/'+item.driverPhone}"></span>
                                                        </div>
                                                        <div class="flex mt5">
                                                            <img th:src="@{/img/cl.png}" style="width: 20px;height: 20px;"/><span th:if="${item.carNo != null}" th:text="${item.carNo}"></span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div  class="flex">
                                                            <div th:each="dict : ${payDetailStatusEnum}" th:text="${dict.context}" th:if="${dict.value}==${payDetail.vbillstatus}"></div>
                                                            <div class="ml0">
                                                                <div>
                                                                    <span th:text="${payDetail.transFeeCount}"></span> -
                                                                    <th:block th:if="${payDetail.freeType} == '0'">
                                                                        <span th:each=" dict : ${@dict.getType('cost_type_freight')}" th:text="${dict.dictLabel}" th:if="${dict.dictValue}==${payDetail.costTypeFreight}"></span>
                                                                    </th:block>
                                                                    <th:block th:unless="${payDetail.freeType} == '0'">
                                                                        <span th:each=" dict : ${@dict.getType('cost_type_on_way')}" th:text="${dict.dictLabel}" th:if="${dict.dictValue}==${payDetail.costTypeOnWay}"></span>
                                                                    </th:block>
                                                                </div>
                                                                <div th:text="${payDetail.vbillno}"></div>
                                                                <div th:each="payRecord:${payDetail.payRecordList}">
                                                                    <span th:each="dict : ${@dict.getType('pay_method')}" th:if="${ dict.dictValue == payRecord.payMethod+'' }" th:text="${dict.dictLabel}"></span> - <span th:text="${payRecord.recCardNo}"></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                        <div><!--incomeRemark-->
                                                            扣：<span th:id="|${item.entrustLotId}_kk_sapn|"
                                                                     th:text="${#aggregates.sum(item.payDetailList.![transFeeCount==null or incomeRemark!=1 ? 0:transFeeCount.abs()])}"
                                                                     class="label label-dangerT"></span>
                                                            <input th:id="|${item.entrustLotId}_kk_input|" type="hidden" name="lotIdTransFeeList" th:value="${item.entrustLotId+'-0'}">
                                                        </div>
<!--                                                        <div class="mt20">-->
<!--                                                            赔：<span th:id="|${item.entrustLotId}_pk_sapn|"-->
<!--                                                                    th:text="${#aggregates.sum(item.payDetailList.![transFeeCount==null or incomeRemark!=2 ? 0:transFeeCount.abs()])}"-->
<!--                                                                    class="label label-dangerT"></span>-->
<!--                                                        </div>-->

                                                    </td>
                                                    <td th:rowspan="${status.size}" th:if="${status.index==0}">
                                                        <!-- 可扣金额-->
                                                        <input type="hidden"
                                                               name="transFeeCount2"
                                                               th:id="|${item.entrustLotId}_transFeeCount2|"
                                                               th:value="${#aggregates.sum(item.payDetailList.![transFeeCount==null or (vbillstatus!=0 and vbillstatus!=1) or incomeRemark!=0? 0:transFeeCount])}">
                                                        <!--总金额-->
                                                        <input type="hidden"
                                                               th:id="|${item.entrustLotId}_all_feeCnt|"
                                                               th:value="${#aggregates.sum(item.payDetailList.![transFeeCount==null or incomeRemark!=0? 0:transFeeCount])}">

                                                        <div>
                                                            <input type="hidden"
                                                                   th:id="|${item.entrustLotId}_kk_feeCnt|"
                                                                   th:value="${#aggregates.sum(item.payDetailList.![transFeeCount==null or incomeRemark!=1 ? 0:transFeeCount.abs()])}">
                                                            <span shiro:hasPermission="tms:trace:abnormal:deduct" class="text-success" style="cursor:pointer" onclick="deduct(this)"
                                                                  th:data-lotno="${item.entrustLotId}">应付扣减</span>
                                                        </div>
                                                        <div class="mt20" style="cursor: pointer;">
                                                            <input type="hidden"
                                                                   th:id="|${item.entrustLotId}_pk_feeCnt|"
                                                                   th:value="${#aggregates.sum(item.payDetailList.![transFeeCount==null or incomeRemark!=2 ? 0:transFeeCount.abs()])}">
<!--                                                            <span shiro:hasPermission="tms:trace:abnormal:deduct" class="text-success" onclick="deductDed(this)"-->
<!--                                                                  th:data-lotno="${item.entrustLotId}">赔款</span>-->
                                                        </div>
                                                    </td>
                                                </tr>
                                            </th:block>
                                        </tbody>
                                    </table>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="white-bg mt10">
            <div class="f14 fw">
                <span>三方费用</span>
                <span style="float: right;">三方合计：<span id="otherFeeAll">¥0.00</span></span>
            </div>
            <div class="row mt10" style="margin-left: 0px;margin-right:0px">
                <table class="table table-bordered">
                    <thead style="background:#f7f8fa">
                    <tr>
                        <th >第三方费用单据号</th>
                        <th >状态</th>
                        <th >金额</th>
                        <th >费用类型</th>
                        <th >付款类型</th>
                        <th >备注</th>
                    </tr>
                    </thead>
                    <tbody id="othenFreeTbody">
                    </tbody>
                </table>
            </div>
        </div>

        <div class="white-bg mt10" style="display: flow-root">
            <div class=" f14 fw"> 异常收入/损失费用 </div>
            <div class="btn-group-sm" id="toolbar" role="group" >
                <a class="btn btn-danger" onclick="register()" shiro:hasAnyPermissions="trace:exceptionPay:new">
                    <i class="fa fa-jpy"></i> 损失费用登记
                </a>


            </div>
            <div class="col-sm-12 select-table table-striped" style="margin-top:0px">
                <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
            </div>
            <div class="btn-group-sm" id="toolbar1" role="group" >

                <a class="btn btn-primary" onclick="registerOther()" shiro:hasAnyPermissions="trace:exceptionPay:new">
                    <i class="fa fa-jpy"></i> 收入赔款登记
                </a>
                <!--        <a class="btn btn-warning" onclick="apply()" shiro:hasAnyPermissions="tms:trace:register">
                            <i class="fa fa-jpy"></i> 异常费用付款申请
                        </a>-->

            </div>
            <div class="col-sm-12 select-table table-striped" style="margin-top:0px">
                <table class="text-nowrap" id="bootstrap-table1" data-advanced-search="true"></table>
            </div>
        </div>


        <div class="white-bg mt10">
            <div class=" f14 fw"> 损失信息 </div>
            <div class="row mt10">
                <div class="col-md-3 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left">是否赔偿损失：</div>
                        <div class="flex_right">
                            <label class="radio-inline">
                                <input type="radio" name="isCompensationRequired" value="1" th:checked="${entrustExp.isCompensationRequired == 1}"> 是
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="isCompensationRequired" value="0" th:checked="${entrustExp.isCompensationRequired == 0}"> 否
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="isCompensationRequired" value="2" th:checked="${entrustExp.isCompensationRequired == 2}"> 待确认
                            </label>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left">是否有残值：</div>
                        <div class="flex_right">
                            <label class="radio-in/line">
                                <input type="radio" name="abnormalLink" value="1" th:checked="${entrustExp.abnormalLink == 1}"> 是
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="abnormalLink" value="0" th:checked="${entrustExp.abnormalLink == 0}"> 否
                            </label>
                            <div class="tabr" th:style="${entrustExp.abnormalLink == 1?'display: inline-block;':'display: none;'}" onclick="remain()">
                                <i class="fa fa-plus text-navy" style="font-size: 12px;"></i>
                                <span class="text-success">残值跟踪(<span th:text="${residualValueTrackingCount==null ? 0 : residualValueTrackingCount}"></span>)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="isCompensationRequiredDiv">
                <div class="mt10">
                    <span class="income_total">
                        <span class="label label-primary pa2">收入</span>
                        <span class="fw">0</span>
                    </span>
                    <span class="loss_total ml5">
                        <span class="label label-danger pa2">损失</span>
                        <span class="fw">0</span>
                    </span>
                    <span class="ml5 real_total">
                        <span>实际损失：</span>
                        <span class="fw fc33">0</span>
                    </span>
                    <span class="ml5">
                        <span>绩效损失：</span>
                        <input type="text" name="lossAmount" id="lossAmount" th:value="${entrustExp.lossAmount}" style="width: 100px;display: inline-block;"
                            class="form-control">
                    </span>
                    <span class="ml5">
                        <span>损失备注：</span>
                        <input type="text" name="lossMemo" id="lossMemo"  th:value="${entrustExp.lossMemo}" style="width: 200px;display: inline-block;"
                               class="form-control">
                    </span>
                    <span class="ml5">
                        <span class="label label-warning pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" data-original-title="123" id="lsjl">历史记录</span>
                    </span>
                </div>
                <div class="row mt10">
                    <div class="col-md-6 col-sm-6">
                        <div class="bor1a">
                            <table class="table table-bordered" id="incometab">
                                <thead style="background:#f7f8fa">
                                <tr>
                                    <th style="width: 8%;"><a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertIncomeRow()" title="新增行">+</a></th>
                                    <th style="width: 15%;text-align: center">收入明目</th>
                                    <!-- <th style="width: 20%;text-align: center">单据号</th> -->
                                    <th style="width: 15%;text-align: center">收入金额</th>
                                    <th style="width: 42%;text-align: center">备注</th>
                                </tr>
                                </thead>
                                <tbody style="background: #ffffff">
                                <tr  name="rowIncome" th:each="mapS,status:${incomeList}">
                                    <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeIncomeRow(this)" title="删除选择行"></a></td>
                                    <td>
            <!--                                                        <a class="close-link del-alink" onclick="removeIncomeRow(this)" title="删除行">-</a>-->
                                        <select name="lossItem" class="form-control" th:value="${mapS.lossItem}">
                                            <option value=""></option>
                                            <option value="保险赔付" th:selected="${mapS.lossItem == '保险赔付'}">保险赔付</option>
                                            <option value="承运商赔付" th:selected="${mapS.lossItem == '承运商赔付'}">承运商赔付</option>
                                            <option value="其他赔付" th:selected="${mapS.lossItem == '其他赔付'}">其他赔付</option>
                                            <option value="残值" th:selected="${mapS.lossItem == '残值'}">残值</option>
                                        </select>

                                    </td>
                                    <td style="display: none;">
                                        <input type="text" name="vbillno" th:value="${mapS.vbillno}" class="form-control"/>
                                    </td>
                                    <td>
                                        <input type="text" name="lossAmount" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" th:value="${mapS.lossAmount}" class="form-control" onkeyup="incomeheji()"/>
                                    </td>
                                    <td>
                                        <input type="text" name="remark" th:value="${mapS.remark}" class="form-control"/>
                                    </td>
                                    <td style="display: none">
                                        <input type="hidden" name="insuranceType" value="2" class="form-control"/>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-6">
                        <div class="bor33">
                            <table class="table table-bordered" id="losstab">
                                <thead style="background:#f7f8fa">
                                <tr>
                                    <th style="width: 8%;"><a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRow()" title="新增行">+</a></th>
                                    <th style="width: 15%;text-align: center">损失明目</th>
                                    <!-- <th style="width: 20%;text-align: center">单据号</th> -->
                                    <th style="width: 15%;text-align: center">损失金额</th>
                                    <th style="width: 42%;text-align: center">备注</th>
                                </tr>
                                </thead>
                                <tbody style="background: #ffffff">
            <!--                                                <td><a class="close-link del-alink" style="background: #fd8481;border-radius: 50%" onclick="removeRow(this)" title="删除选择行">x</a></td>-->
                                <tr name="regRows" th:each="mapS,status:${lossList}">
                                    <td>
                                        <a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRow(this)" title="删除选择行"></a>
                                    </td>
                                    <td>

                                        <select name="lossItem" class="form-control" th:value="${mapS.lossItem}">
                                            <option value=""></option>
                                            <option value="货损" th:selected="${mapS.lossItem == '货损'}">货损</option>
                                            <option value="二次运输费" th:selected="${mapS.lossItem == '二次运输费'}">二次运输费</option>
                                            <option value="驳货费" th:selected="${mapS.lossItem == '驳货费'}">驳货费</option>
                                            <option value="律师费" th:selected="${mapS.lossItem == '律师费'}">律师费</option>
                                            <option value="律师差旅费" th:selected="${mapS.lossItem == '律师差旅费'}">律师差旅费</option>
                                            <option value="诉讼费" th:selected="${mapS.lossItem == '诉讼费'}">诉讼费</option>
                                            <option value="其他费" th:selected="${mapS.lossItem == '其他费'}">其他费</option>
                                        </select>

                                    </td>
                                    <td style="display: none;">
                                        <input type="text" name="vbillno" th:value="${mapS.vbillno}" class="form-control"/>
                                    </td>
                                    <td>
                                        <input type="text" name="lossAmount" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" th:value="${mapS.lossAmount}" class="form-control" onkeyup="lossheji()"/>
                                    </td>
                                    <td>
                                        <input type="text" name="remark" th:value="${mapS.remark}" class="form-control"/>
                                    </td>
                                    <td style="display: none">
                                        <input type="hidden" name="insuranceType" value="1" class="form-control"/>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="white-bg mt10">
            <div class=" f14 fw"> 照片附件及跟踪记录 </div>
            <div class="row mt10">
                <div class="col-md-4 col-sm-12">
                    <div class="boxw" style="align-items: flex-start;">
                        <div>现场照片：</div>
                        <div class="mt5">
                            <input id="image" class="form-control" name="image" type="file" multiple>
                            <input id="appendixId" name="appendixId" type="hidden" th:value="${entrustExp.appendixId}">
                        </div>
                    </div>
                </div>

                <div class="col-md-4 col-sm-12">
                    <div class="boxw" style="align-items: flex-start;">
                        <div>保险附件：</div>
                        <div class="mt5">
                            <input id="imageInsurance" class="form-control" name="imageInsurance" type="file" multiple>
                            <input id="appendixIdInsurance" name="appendixIdInsurance" type="hidden" th:value="${entrustExp.appendixIdInsurance}">
                        </div>
                    </div>
                </div>

                <div class="col-md-4 col-sm-12">
                    <div class="boxw" style="align-items: flex-start;">
                        <div>客户支付回单附件：</div>
                        <div class="mt5">
                            <input id="receiptImage" class="form-control" name="receiptImage" type="file" multiple>
                            <input id="appendixIdReceiptImage" name="appendixIdReceiptImage" type="hidden" th:value="${entrustExp.appendixIdReceiptImage}">
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt10">
                <table class="table table-bordered" id="tracetab">
                    <thead style="background:#f7f8fa">
                    <tr>
                        <th style="width: 8%;"><a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRowTrace()" title="新增行">+</a></th>
                        <th style="width: 35%">跟踪日期</th>
                        <th style="width: 40%">内容</th>
                        <th>需协助内容</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr name="rowTrace" th:each="mapS,status:${exceptionTrackList}">
                        <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeTraceRow(this)" title="删除选择行"></a></td>
                        <td>
                            <input placeholder="请选择时间" type="text" class="form-control trackingdate"
                                   name="trackingDate" id="trackingDate0" readonly th:value="${#dates.format(mapS.trackingDate, 'yyyy-MM-dd HH:mm:ss')}"
                                   aria-required="false">
                        </td>
                        <td>
                            <input type="text" name="trackingContent" class="form-control" th:value="${mapS.trackingContent}"/>
                        </td>
                        <td>
                            <input type="text" name="assistanceContent" class="form-control" th:value="${mapS.assistanceContent}"/>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10 white-bg">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
</form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script type="text/template" id="rowLoss">
    <tr name="regRows">
        <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRow(this)" title="删除选择行"></a></td>
        <td>
<!--            <a class="close-link del-alink form-inline" onclick="removeRow(this)" title="删除行">-</a>-->
            <select name="lossItem" class="form-control">
                <option value=""></option>
                <option value="货损">货损</option>
                <option value="二次运输费">二次运输费</option>
                <option value="驳货费">驳货费</option>
                <option value="律师费">律师费</option>
                <option value="律师差旅费">律师差旅费</option>
                <option value="诉讼费">诉讼费</option>
                <option value="其他费">其他费</option>
            </select>

        </td>
        <td style="display: none;">
            <input name="vbillno" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off">
        </td>
        <td>
            <input name="lossAmount" placeholder="" class="form-control valid"
                   oninput="$.numberUtil.onlyNumberTwoDecimal(this)" type="text" required="true" onkeyup="lossheji()">
        </td>
        <td>
            <input name="remark" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off">
        </td>
        <td style="display: none">
            <input name="insuranceType" value="1" placeholder="" class="form-control" type="hidden" maxlength="100" autocomplete="off">
        </td>
    </tr>
</script>
<script type="text/template" id="rowIncome">
    <tr name="rowIncome">
        <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeIncomeRow(this)" title="删除选择行"></a></td>
        <td>
<!--            <a class="close-link del-alink" onclick="removeIncomeRow(this)" title="删除行">-</a>-->
            <select name="lossItem" class="form-control">
                <option value=""></option>
                <option value="保险赔付">保险赔付</option>
                <option value="承运商赔付">承运商赔付</option>
                <option value="其他赔付">其他赔付</option>
                <option value="残值">残值</option>
            </select>

        </td>
        <td style="display: none;">
            <input name="vbillno" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off">
        </td>
        <td>
            <input name="lossAmount" placeholder="" class="form-control valid"
                   oninput="$.numberUtil.onlyNumberTwoDecimal(this)" type="text" required="true" onkeyup="incomeheji()">
        </td>
        <td>
            <input name="remark" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off">
        </td>
        <td style="display: none">
            <input name="insuranceType" value="2" placeholder="" class="form-control" type="hidden" maxlength="100" autocomplete="off">
        </td>
    </tr>
</script>
<script type="text/template" id="rowTrace">
    <tr name="rowTrace">
        <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeTraceRow(this)" title="删除选择行"></a></td>
        <td>
            <input placeholder="请选择时间" type="text" class="form-control trackingdate"
                   id="trackingDate" name="trackingDate" readonly required="false" aria-required="false">
        </td>
        <td>
            <input name="trackingContent" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off" required="true">
        </td>
        <td>
            <input name="assistanceContent" placeholder="" class="form-control valid" type="text">
        </td>
    </tr>
</script>
<script type="text/template" id="carrier">
    <div class="cys">
        <div class="fw">承运信息：</div>
        <div class="row mt10">
            <!-- 承运商信息 -->
            <div class="col-md-3 col-sm-6">
                <div class="flex" style="display: none">
                    <div class="flex_left fc80">承运商id：</div>
                    <input type="text" name="carrierId" id="carrierId" class="form-control">
                </div>
            </div>
            <div class="flex">
                <div class="flex_left fc80">承运商姓名：</div>
                <div class="flex_right"> + rows[0].carrName + </div>
            </div>
            <div class="flex">
                <div class="flex_left fc80">联系方式：</div>
                <div class="flex_right">rows[0].phone</div>
            </div>
        </div>
        <!-- 添加关联单号 -->
        <div class="col-md-9 col-sm-6">
            <div class="flex">
                <div class="flex_left" style="width: 140px">
                    <div class="addbtn" onclick="showEntrusts()">
                        <span style="font-size: 18px;">⊕</span> 添加关联单号
                    </div>
                </div>
                <div class="flex_right">
                    <div class="col-md-12">
                        <div class="form-group">
                            <div class="col-sm-9 dhlist">
                                <input type="text" name="lotno" class="form-control"placeholder="请输入运单号">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>
<script type="text/template" id="scanCodeHtml">
    <div class="row" style="margin: 10px 0;">
        <div class="col-md-12 col-sm-12">
            <div class="form-group">
                <label class="col-xs-3" style="line-height: 26px;">操作类型：</label>
                <div class="col-xs-9">
                    <select id="adjustType" class="form-control" onchange="toggleDeductMode()">
                        <option value="deduct">扣减</option>
                        <option value="add">新增</option>
                    </select>
                </div>
<!--                <div class="col-xs-5">-->
<!--                    <select id="isProfitIncluded" class="form-control">-->
<!--                        <option value="0">不纳入利润计算</option>-->
<!--                        <option value="1">纳入利润计算</option>-->
<!--                    </select>-->
<!--                </div>-->
            </div>
        </div>
    </div>
    <div class="row" style="margin: 10px 0;">
        <div class="col-md-12 col-sm-12">
            <div class="form-group">
                <label class="col-xs-3" style="line-height: 26px;"><span class="fcff3">*</span>金额（元）：</label>
                <div id="amountInputContainer" class="col-xs-6">
                    <input name="adjustAmount" id="adjustAmount"  class="form-control" type="text" autocomplete="off" oninput="value=value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/,'$1')">
                </div>
                <div id="fullDeductContainer" class="col-xs-3" style="display:none;">
                    <button type="button" id="fullDeductBtn" class="btn btn-primary btn-sm"
                            style="padding: 3px 14px; font-size: 12px; ">全扣</button>
                </div>
            </div>
        </div>
    </div>
</script>

<script type="text/template" id="scanCodeHtml_yf">
    <div class="row" style="margin: 10px 0;">
        <div class="col-md-12 col-sm-12">
            <div class="form-group">
                <label class="col-xs-4" style="line-height: 26px;"><span class="fcff3">*</span>金额（元）：</label>
                <div class="col-xs-8">
                    <input id="adjustAmount_yf"  class="form-control" type="text" autocomplete="off" oninput="value=value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/,'$1')">
                </div>
            </div>
        </div>
        <div class="col-md-12 col-sm-12">
            <div class="form-group">
                <label class="col-sm-4">是否纳入利润计算：</label>
                <div class="col-sm-8">
                    <select id="isProfitIncluded" class="form-control valid" data-none-selected-text="请选择异常状态"
                            aria-invalid="false" aria-required="true">
                        <!--  0 不纳入  1纳入   -->
                        <option value="0">不纳入</option>
                        <option value="1">纳入</option>
                    </select>
                </div>
            </div>
        </div>

    </div>
</script>

<script type="text/template" id="dedHtml">
    <div class="row" style="margin: 10px 0;">
        <div class="col-md-12 col-sm-12">
            <div class="form-group">
                <label class="col-xs-3" style="line-height: 26px;"><span class="fcff3">*</span>金额（元）：</label>
                <div class="col-xs-9">
                    <input name="dedAmount" id="dedAmount"  class="form-control" type="text" autocomplete="off" oninput="value=value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/,'$1')">
                </div>
            </div>
        </div>
    </div>
    <div class="row" style="margin: 10px 0;">
        <div class="col-md-12 col-sm-12">
            <div class="form-group">
                <label class="col-xs-3" style="line-height: 26px;">备注：</label>
                <div class="col-xs-9">
                        <textarea id="memo" maxlength="200" class="form-control valid"
                                  rows="2"></textarea>
                </div>
            </div>
        </div>
    </div>
</script>
<script th:inline="javascript">
    var prefix = ctx + "trace";
    var payprefix = ctx + "payDetail";
    var entrustlot = ctx + "carrier/entrustLot";
    var moreprefix = ctx + "entrustexceptionmore";
    var entrust = [[${entrust}]];
    var entrustExpAfter = [[${entrustExp}]];
    var entrustExpLossRecords = [[${entrustExpLossRecords}]];

    var balaType=[[${@dict.getType('bala_type')}]]
    var receiveDetailStatusEnum=[[${receiveDetailStatusEnum}]]

    var carLen=[[${@dict.getType('car_len')}]];

    var payMethod = [[${@dict.getType('pay_method')}]];//付款方式
    var invoiceReceiveDetail=[[${invoiceReceiveDetail}]];
    var invoiceReceiveDetailList=[[${invoiceReceiveDetailList}]];

    var exceptionType=[[${@dict.getType('exception_type')}]];

    //费用类型
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];

    var invoiceIdList = invoiceReceiveDetailList.map(x => x.invoiceId)
    invoiceIdList.push(invoiceReceiveDetail.invoiceId)

    var bala_corp = [[${@dict.getType('bala_corp')}]];
    var insurance_owner_type = [[${@dict.getType('insurance_owner_type')}]];

    // 权限变量，供JS判断是否显示"应收扣减"按钮
    var hasShowReceBtnPerm = [[${@permission.hasPermi('tms:trace:abnormal:showReceBtn')}]] == 'hidden' ? false : true;

    function onExceptionTitle() {
        setTimeout(function () {
            let accidentTime=$("#accidentTime").val();
            let expTypes=$.form.selectCheckeds("expType");
            let html="";
            html+=' '+invoiceReceiveDetail.custAbbr;

            if(invoiceReceiveDetail.deliCityName=='市辖区'){
                html+= ' '+invoiceReceiveDetail.deliProName;
            }else{
                html+= ' '+invoiceReceiveDetail.deliCityName;
            }

            if(invoiceReceiveDetail.arriCityName=='市辖区'){
                html+= '-'+invoiceReceiveDetail.arriProName;
            }else{
                html+= '-'+invoiceReceiveDetail.arriCityName;
            }


            let expTypeName=[];
            expTypes.split(",").forEach(item=>{
                exceptionType.forEach(res=>{
                    if(item==res.dictCode){
                        expTypeName.push(res.dictLabel)
                    }
                })
            })

            html= accidentTime+html+" "+expTypeName.join(',');
            $("#exceptionTitle").html(html);
        }, 1000)
    }


    //新增校验逻辑
    function submitHandler() {
        if ($.validate.form()) {
            var entrustIds = [];
            $("input[name='rmReq']").each(function () {
                if ($(this).is(":checked")) {
                    entrustIds.push($(this).val());
                }
            });

            // if ($("#image").val() != "") {

            //     $("#image").fileinput('upload');
            // } else if ($("#imageInsurance").val() != "") {

            //     $("#imageInsurance").fileinput('upload');
            // } else if ($("#receiptImage").val() != "") {

            //     $("#receiptImage").fileinput('upload');
            // } else {
            //     commit();
            // }
            $("#image").fileinput('upload');
            jQuery.subscribe("imgDone_1",cmtFile_2);
            jQuery.subscribe("imgDone_2",cmtFile_3);
            jQuery.subscribe("imgDone_3",commit);
        }
    }
    function cmtFile_2(){
        $("#imageInsurance").fileinput('upload');
    }
    function cmtFile_3(){
        $("#receiptImage").fileinput('upload');
    }

    //新增提交
    function commit() {
        //验证是否存在在途费用
        //遍历选中的委托单
        var entrustIds = [];
        $("input[name='rmReq']").each(function () {
            if ($(this).is(":checked")) {
                entrustIds.push($(this).val());
            }
        });
        var ajaxParamsVO = {}; //参数类

        var entrustExp = new Object();
        //data.entrustId = $("#entrustId").val();
        //基础信息
        entrustExp.isCustomerException = $("#isCustomerException").val();
        entrustExp.entrustExpId = entrustExpAfter.entrustExpId;
        entrustExp.entrustId = entrust.entrustId;
        entrustExp.exceptionTitle = $("#exceptionTitle").html();
        entrustExp.handleNote = $("#handleNote").val();

        entrustExp.reportNo = $("#reportNo").val();
        entrustExp.insurancePolicyId = $("#insurancePolicyId").val();
        entrustExp.caseStatus = $("#caseStatus").val();
        //是否锁定应付
        // entrustExp.lockPay = ($('input:radio[name="lockPay"]:checked').val()!='' && $('input:radio[name="lockPay"]:checked').val()!=null)? $('input:radio[name="lockPay"]:checked').val() : 0;
        var lockPay = $('input[name="lockPay"]:checkbox').prop("checked");
        entrustExp.lockPay = lockPay ? 1 : 0;

        var lockOtherFee = $('input[name="lockOtherFee"]:checkbox').prop("checked");
        entrustExp.lockOtherFee = lockOtherFee ? 1 : 0;

        //是否有残值
        entrustExp.abnormalLink = $('input:radio[name="abnormalLink"]:checked').val();
        //是否报保险
        entrustExp.isInsurance = $('input:radio[name="isInsurance"]:checked').val();
        //是否赔偿损失
        entrustExp.isCompensationRequired = $('input:radio[name="isCompensationRequired"]:checked').val();
        //异常类型
        var expTypes = $.form.selectCheckeds("expType");
        entrustExp.expType =expTypes;
        //预估损失
        entrustExp.lossAmount = $("#lossAmount").val();
        entrustExp.lossMemo = $("#lossMemo").val();
        //出险信息
        entrustExp.accidentTime = $("#accidentTime").val();
        entrustExp.accidentReason = $("#accidentReason").val();
        entrustExp.preliminaryLoss = $("#preliminaryLoss").val();
        entrustExp.accidentPlace = $("#accidentPlace").val();
        //附件信息
        entrustExp.appendixId = $("#appendixId").val();
        entrustExp.appendixIdInsurance = $("#appendixIdInsurance").val();
        entrustExp.appendixIdReceiptImage = $("#appendixIdReceiptImage").val();

        var lotnolist = [];
        //运单号
        // $("input[name^='lotno']").each(function () {
        //     lotnolist.push($(this).val())
        // });
        //获取div class=dhlist下的第二个span开始的所有数据
        //去重
        $('div[class^="dhlist"]').children("span").each(function () {
            if($(this).text() != null && $(this).text() != '') {
                lotnolist.push($(this).text().replace(/[\r\n]/g, "").replace(/\s+/g, ""))
            }
        })
        var a = new Map();
        var carrierId = $("input[name='carrierId']").val()
        a.set(carrierId,lotnolist)
        //损失
        var insuranceList = [];
        $("tr[name='regRows']").each(function () {
            var row = {};
            var lossItem = $(this).find("select[name^='lossItem']").val();
            var vbillno = $(this).find("input[name^='vbillno']").val();
            var lossAmount = $(this).find("input[name^='lossAmount']").val();
            var remark = $(this).find("input[name^='remark']").val();
            var insuranceType = $(this).find("input[name^='insuranceType']").val();
            row.entrustId = entrust.entrustId;
            row.entrustNo = entrust.vbillno;
            row.lot = entrust.lot;
            row.lossItem = lossItem;
            row.vbillno = vbillno;
            row.lossAmount = lossAmount;
            row.remark = remark;
            row.insuranceType = insuranceType;
            insuranceList.push(row);
        });
        //收入
        $("tr[name='rowIncome']").each(function () {
            var row = {};
            var lossItem = $(this).find("select[name^='lossItem']").val();
            var vbillno = $(this).find("input[name^='vbillno']").val();
            var lossAmount = $(this).find("input[name^='lossAmount']").val();
            var remark = $(this).find("input[name^='remark']").val();
            var insuranceType = $(this).find("input[name^='insuranceType']").val();
            row.entrustId = entrust.entrustId;
            //row.entrustNo = entrust.vbillno;
            //row.lot = entrust.lot;
            row.lossItem = lossItem;
            row.vbillno = vbillno;
            row.lossAmount = lossAmount;
            row.remark = remark;
            row.insuranceType = insuranceType;
            insuranceList.push(row);
        });
        //异常跟踪
        var exceptionTrackList = [];
        $("tr[name='rowTrace']").each(function () {
            var row = {};
            var trackingDate = $(this).find("input[name^='trackingDate']").val();
            var trackingContent = $(this).find("input[name^='trackingContent']").val();
            var assistanceContent = $(this).find("input[name^='assistanceContent']").val();
            row.entrustId = entrust.entrustId;
            row.entrustNo = entrust.vbillno;
            row.lot = entrust.lot;
            row.trackingDate = trackingDate;
            row.trackingContent = trackingContent;
            row.assistanceContent = assistanceContent;
            exceptionTrackList.push(row);
        });
        ajaxParamsVO.entrustExp = entrustExp //主异常
        ajaxParamsVO.insuranceList = insuranceList //损失&收入
        ajaxParamsVO.lotnolist = lotnolist//运单
        ajaxParamsVO.exceptionTrackList = exceptionTrackList;//异常跟踪
        ajaxParamsVO.carrierEntrustLotMap = a;//承运商 - 运单map集合


        var lockCarrierList = [];
        // 遍历所有承运商ID
        $("input[id^='carrierId_']").each(function() {
            var index = $(this).attr("id").split("_")[1];
            var carrierId = $(this).val();
            var lockPayReason = $("#lockPayReason_" + index).val();

            // 获取锁定状态值
            var lockStatus = $("input[name='carrierLockStatus_" + index + "']:checked").val() || "0";

            // 添加到锁定列表，即使是不锁定状态也添加
            var lockCarrier = {
                carrierId: carrierId,
                lockPay: parseInt(lockStatus),
                lockPayReason: lockStatus == "0" ? "" : lockPayReason
            };
            lockCarrierList.push(lockCarrier);
        });
        ajaxParamsVO.lockCarrierList = lockCarrierList;

        //发货单信息赔款信息
        let invoiceIds=[];
        $("input[name='invoiceIds']").each(function () {
            if($(this).val())
            invoiceIds.push($(this).val());
        });
        ajaxParamsVO.invoiceIds = invoiceIds;
        //运单信息扣款信息
        let lotIdTransFeeList=[];
        $("input[name='lotIdTransFeeList']").each(function () {
            if($(this).val())
            lotIdTransFeeList.push($(this).val());
        });
        ajaxParamsVO.lotIdTransFeeList = lotIdTransFeeList;

        //运单信息扣款信息
        ajaxParamsVO.lotIdDedTransFeeList = lotIdDedTransFeeList;

        $.operate.saveTabJson(moreprefix + "/updateEntrustException", ajaxParamsVO);
    }

    $(function () {
        $('[data-toggle="tooltip"]').tooltip();
        //循环图片路径信息，初始化图片上传区域
        var files = [[${files}]];
        var filesInsurance = [[${filesInsurance}]];
        var filesReceipt = [[${filesReceipt}]]

        var image1 = {
            maxFileCount: 5,
            publish: "imgDone_1",  //用于绑定下一步方法
            fileType: null//文件类型
        };
        var image2 = {
            maxFileCount: 5,
            publish: "imgDone_2",  //用于绑定下一步方法
            fileType: null//文件类型
        };
        var image3 = {
            maxFileCount: 5,
            publish: "imgDone_3",  //用于绑定下一步方法
            fileType: null//文件类型
        };

        if(files == null){
            $.file.initAddFiles("image", "appendixId", image1);
        }else{
            $.file.loadEditFiles("image", "appendixId", files, image1);
        }
        if(filesInsurance == null){
            $.file.initAddFiles("imageInsurance", "appendixIdInsurance", image2);
        }else{
            $.file.loadEditFiles("imageInsurance", "appendixIdInsurance", filesInsurance, image2);
        }
        if(filesReceipt == null){
            $.file.initAddFiles("receiptImage", "appendixIdReceiptImage", image3);
        }else{
            $.file.loadEditFiles("receiptImage", "appendixIdReceiptImage", filesReceipt, image3);
        }

        getOtherFee()

        // 初始化保险信息
        var insurancePolicy = [[${insurancePolicy}]];
        if (insurancePolicy) {
            var insuredParty = '';
            // 查找对应的字典标签
            for (var i = 0; i < bala_corp.length; i++) {
                if (bala_corp[i].dictValue == ('' + insurancePolicy.insuredParty)) {
                    insuredParty = bala_corp[i].dictLabel || '';
                    break;
                }
            }
            var insuranceCompany = insurancePolicy.insuranceCompany || '';
            var policyNo = insurancePolicy.policyNo || '';
            $("#insurancePolicy").val(insuredParty + '-' + insuranceCompany + '-' + policyNo);
        }
        // var image = {
        //     maxFileCount: 0,
        //     publish: "publishFlag",  //用于绑定下一步方法
        //     fileType: null//文件类型
        // };
        // if(files == null){
        //     $.file.initAddFiles("image", "appendixId", image);
        // }else{
        //     $.file.loadEditFiles("image", "appendixId", files, image);
        // }
        // if(filesInsurance == null){
        //     $.file.initAddFiles("imageInsurance", "appendixIdInsurance", image);
        // }else{
        //     $.file.loadEditFiles("imageInsurance", "appendixIdInsurance", filesInsurance, image);
        // }
        // if(filesReceipt == null){
        //     $.file.initAddFiles("receiptImage", "appendixIdReceiptImage", image);
        // }else{
        //     $.file.loadEditFiles("receiptImage", "appendixIdReceiptImage", filesReceipt, image);
        // }

        // var image = {
        //     maxFileCount: 5,
        //     publish: "imgDone",  //用于绑定下一步方法
        //     fileType: null//文件类型
        // };
        // $.file.initAddFiles("image", "appendixId", image);
        // $.file.initAddFiles("imageInsurance", "appendixIdInsurance", image);
        // $.file.initAddFiles("receiptImage", "appendixIdReceiptImage", image);


        // // 图片上传成功后
        // $("#image").on('filebatchuploadsuccess', function (event, data) {
        //     var tid = data.response.tid;
        //     $("#appendixId").val(tid);
        //     if ($("#imageInsurance").val() != "") {
        //         $("#imageInsurance").fileinput('upload');
        //     }else if($("#receiptImage").val() != "") {
        //         $("#receiptImage").fileinput('upload');
        //     }else {
        //         commit();
        //     }
        // });

        // // 图片上传成功后
        // $("#imageInsurance").on('filebatchuploadsuccess', function (event, data) {
        //     var tid = data.response.tid;
        //     $("#appendixIdInsurance").val(tid);
        //     if($("#receiptImage").val() != "") {
        //         $("#receiptImage").fileinput('upload');
        //     }else if($("#image").val() != "") {
        //         $("#image").fileinput('upload');
        //     }else {
        //         commit();
        //     }
        // });

        // // 图片上传成功后
        // $("#receiptImage").on('filebatchuploadsuccess', function (event, data) {
        //     var tid = data.response.tid;
        //     $("#appendixIdReceiptImage").val(tid);
        //     if($("#imageInsurance").val() != "") {
        //         $("#imageInsurance").fileinput('upload');
        //     }else if($("#image").val() != "") {
        //         $("#image").fileinput('upload');
        //     }else {
        //         commit();
        //     }
        // });

        //时间初始化
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#accidentTime', //指定元素
                type: 'date',
                trigger: 'click',
                // ready: function (date) {
                //     var now = new Date();
                //     this.dateTime.hours = now.getHours();
                //     this.dateTime.minutes = now.getMinutes();
                //     this.dateTime.seconds = now.getSeconds();
                // },
                done: function (value, date, endDate) {
                    $("#accidentTime").val(value);
                    //单独校验日期
                    $("#form-adnormal-add").validate().element($("#accidentTime"))
                }
            });

            //日期循环初始化
            laydate.render({
                elem: '#trackingDate0', //指定元素
                type: 'datetime',
                trigger: 'click',
                ready: function (date) {
                    var now = new Date();
                    this.dateTime.hours = now.getHours();
                    this.dateTime.minutes = now.getMinutes();
                    this.dateTime.seconds = now.getSeconds();
                },
                done: function (value, date, endDate) {
                    $("#trackingDate0").val(value);
                    //单独校验日期
                    $("#form-adnormal-add").validate().element($("#trackingDate0"))
                }
            });

            $("#lsjl").attr("data-original-title", getOtherFeeList());
            $("[data-toggle='tooltip']").tooltip();
        });

        //损失合计展示
        var  losssum = 0;
        $("#losstab").find("input[name^='lossAmountapply']").each(function () {
            if($(this).val() != null && $(this).val() != '') {
                losssum = accAdd(losssum,parseFloat($(this).val()))
            }
        })
        $(".loss_total").find("span").eq(1).text(losssum)
        //收入合计展示
        var  incomesum = 0;
        $("#incometab").find("input[name^='lossAmount']").each(function () {
            if($(this).val() != null && $(this).val() != '') {
                incomesum = accAdd(incomesum,parseFloat($(this).val()))
            }
        })
        $(".real_total").find("span").eq(1).text(accSub(losssum,incomesum))
        $(".income_total").find("span").eq(1).text(incomesum)

        let isInsuranceNum=[[${entrustExp.isInsurance}]];
        if(isInsuranceNum==0){
            $("#reportNoDiv").css('display', 'none');
            $("#insurancePolicyDiv").css('display', 'none');

        }else{
            $("#reportNoDiv").css('display', 'block');
            $("#insurancePolicyDiv").css('display', 'block');

        }

        /*let isCompensationRequiredNum=[[${entrustExp.isCompensationRequired}]];
        if(isCompensationRequiredNum==0){
            $("#isCompensationRequiredDiv").css('display', 'none');
        }else{
            $("#isCompensationRequiredDiv").css('display', 'block');
        }*/

        $('input[type=radio][name=isInsurance]').change(function() {
            let isInsurance=$('input:radio[name="isInsurance"]:checked').val();
            if(isInsurance==0){
                $("#reportNoDiv").css('display', 'none');
                $("#insurancePolicyDiv").css('display', 'none');

            }else{
                $("#reportNoDiv").css('display', 'block');
                $("#insurancePolicyDiv").css('display', 'block');

            }
        });

        /*$('input[type=radio][name=isCompensationRequired]').change(function() {
            let isCompensationRequired=$('input:radio[name="isCompensationRequired"]:checked').val();
            if(isCompensationRequired==0){
                $("#isCompensationRequiredDiv").css('display', 'none');
            }else{
                $("#isCompensationRequiredDiv").css('display', 'block');
            }
        });*/

        $("input[name=abnormalLink]").change(function () {
            var val = $(this).val();
            if(val==1){
                $(".tabr").css("display", "inline-block")
            }else{
                $(".tabr").css("display", "none")
            }
        });



        // invoiceIds("invoiceIds","#thdzpk",1);
        calculate_thdzpk()
        // invoiceIds("transFeeCount1","#thdzje",2);
        calculate_thdzje()
        calculate_ydzpk_ydzpk_pk()
        calculate_ydzje()

        lossheji()

        var options = {
            id:'bootstrap-table',
            url: ctx + "trace/exceptionTotal/applyPayList?entrustExpId="+$("#entrustExpId").val()+"&type=0",
            showToggle: false,
            showColumns: false,
            modalName: "应付明细",
            fixedColumns: true,
            rememberSelected: false,
            fixedNumber: 0,
            clickToSelect:true,
            showFooter:false,
            showSearch:false,
            showRefresh:false,
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        if(row.status == 1 && row.type == 0 &&  [[${@permission.hasPermi('trace:exceptionPay:print')}]] != "hidden"){
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="打印" onclick="print(\'' + row.id + '\')"><i  class="fa fa-print" style="font-size: 15px;" ></i></a>');
                        }
                        if(row.status == 0 && [[${@permission.hasPermi('trace:exceptionPay:apply')}]] != "hidden"){
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="付款申请" onclick="apply(\'' + row.id + '\','+row.type+')"><i  class="fa fa-hourglass-start" style="font-size: 15px;" ></i></a>');
                        }
                        if(row.status == 0 && [[${@permission.hasPermi('trace:exceptionPay:remove')}]] != "hidden"){
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="删除" onclick="remove(\'' + row.id + '\')"><i  class="fa fa-remove" style="font-size: 15px;" ></i></a>');
                        }

                        return actions.join('');
                    }
                },

                {
                    title: '异常单号',
                    field: 'vbillno',
                    align: 'left',
                    formatter:function status(value, row) {
                        let html= '<span>'+value+'</span>'
                        if(row.backMemo != null){
                            html+='<span style="margin-left:2px;padding-left: 5px;padding-right: 5px;"  class="label label-danger"  data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+row.backMemo+'">退</span>'
                        }

                        return html;
                    }
                },
                {
                    title: '费用类型',
                    field: 'type',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if(value == 0){
                            return '损失费用'
                        }else if(value == 1){
                            return '收入赔款'
                        }else{
                            return '-'
                        }
                    }
                },
                {
                    title: '单据状态',
                    field: 'status',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if(value == 0){
                            return '新建'
                        }else if(value == 1){
                            return '已申请'
                        }else if(value == 2){
                            if(row.type == 0){
                                return '已付款'
                            }else if(row.type == 1){
                                return '已收款'
                            }
                        }else{
                            return '-'
                        }
                    }
                },
                {
                    title: '申请金额',
                    align: 'right',
                    field: 'payAmount',
                    formatter: function (value, row, index) {
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});

                    }
                },
                {
                    title: '收款人/收款账户',
                    align: 'left',
                    field: 'receiveMan',
                    formatter: function (value, row, index) {
                        let a = '';
                        if(value !== undefined && value !== null){
                            a += value;
                        }
                        if(row.receiveCardId !== undefined && row.receiveCardId !== null){
                            a += '<br />' + row.receiveCardId;
                        }
                        return a;
                    }
                },
                {
                    title: '收款银行',
                    align: 'left',
                    field: 'receiveBank'
                },
                {
                    title: '申请备注',
                    align: 'left',
                    field: 'applyMemo',
                    formatter: function status(value,row) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '申请附件',
                    field: 'sysUploadFiles',
                    align: 'left',
                    formatter: function(value, row, index) {
                        var html = "<div class='picviewer'>"
                        if(value != null && value != '') {
                            value.forEach(function (element, index) {
                                html += `<img src="`+element.filePath+`"/>`
                            });
                        }else {
                            html = '-';
                        }
                        html +="</div>"
                        return html;
                    }
                },
                {
                    title: '创建人/创建时间',
                    align: 'left',
                    field: 'regUserName',
                    formatter: function (value, row, index) {
                        let a = '';
                        if(value !== undefined && value !== null){
                            a += value;
                        }
                        if(row.regDate !== undefined && row.regDate !== null){
                            a += '<br />' + row.regDate;
                        }
                        return a;
                    }
                },

                {
                    title: '申请人/申请时间',
                    align: 'left',
                    field: 'applyUserName',
                    formatter: function (value, row, index){
                        let a = '';
                        if(value !== undefined && value !== null){
                            a += value;
                        }
                        if(row.applyDate !== undefined && row.applyDate !== null){
                            a += '<br />' + row.applyDate;
                        }
                        return a;

                    }
                },
                {
                    title: '付款人/付款时间',
                    align: 'left',
                    field: 'payUserName',
                    formatter: function (value, row, index){

                        let a = '';
                        if(value !== undefined && value !== null){
                            a += value;
                        }
                        if(row.payDate !== undefined && row.payDate !== null){
                            a += '<br />' + row.payDate;
                        }
                        return a;
                    }
                }




            ],
            onLoadSuccess: function(data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar:false,
                });
            }
        };

        $.table.init(options);


        var options1 = {
            id:'bootstrap-table1',
            toolbar:'toolbar1',
            url: ctx + "trace/exceptionTotal/applyPayList?entrustExpId="+$("#entrustExpId").val()+"&type=1",
            showToggle: false,
            showColumns: false,
            modalName: "应付明细",
            fixedColumns: true,
            rememberSelected: false,
            fixedNumber: 0,
            clickToSelect:true,
            showFooter:false,
            showSearch:false,
            showRefresh:false,
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        if(row.status == 1 && row.type == 0 &&  [[${@permission.hasPermi('trace:exceptionPay:print')}]] != "hidden"){
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="打印" onclick="print(\'' + row.id + '\')"><i  class="fa fa-print" style="font-size: 15px;" ></i></a>');
                        }
                        if(row.status == 0 && [[${@permission.hasPermi('trace:exceptionPay:apply')}]] != "hidden"){
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="收款申请" onclick="apply(\'' + row.id + '\','+row.type+')"><i  class="fa fa-hourglass-start" style="font-size: 15px;" ></i></a>');
                        }
                        if(row.status == 0 && [[${@permission.hasPermi('trace:exceptionPay:remove')}]] != "hidden"){
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="删除" onclick="remove(\'' + row.id + '\')"><i  class="fa fa-remove" style="font-size: 15px;" ></i></a>');
                        }

                        return actions.join('');
                    }
                },

                {
                    title: '异常单号',
                    field: 'vbillno',
                    align: 'left',
                    formatter:function status(value, row) {
                        let html= '<span>'+value+'</span>'
                        if(row.backMemo != null){
                            html+='<span style="margin-left:2px;padding-left: 5px;padding-right: 5px;"  class="label label-danger"  data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+row.backMemo+'">退</span>'
                        }

                        return html;
                    }
                },
                {
                    title: '费用类型',
                    field: 'type',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if(value == 0){
                            return '损失费用'
                        }else if(value == 1){
                            return '收入赔款'
                        }else{
                            return '-'
                        }
                    }
                },
                {
                    title: '单据状态',
                    field: 'status',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if(value == 0){
                            return '新建'
                        }else if(value == 1){
                            return '已申请'
                        }else if(value == 2){
                            if(row.type == 0){
                                return '已付款'
                            }else if(row.type == 1){
                                return '已收款'
                            }
                        }else{
                            return '-'
                        }
                    }
                },
                {
                    title: '申请金额',
                    align: 'right',
                    field: 'payAmount',
                    formatter: function (value, row, index) {
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});

                    }
                },
                {
                    title: '收款人/收款账户',
                    align: 'left',
                    field: 'receiveMan',
                    formatter: function (value, row, index) {
                        let a = '';
                        if(value !== undefined && value !== null){
                            a += value;
                        }
                        if(row.receiveCardId !== undefined && row.receiveCardId !== null){
                            a += '<br />' + row.receiveCardId;
                        }
                        return a;
                    }
                },
                {
                    title: '收款银行',
                    align: 'left',
                    field: 'receiveBank'
                },
                {
                    title: '申请备注',
                    align: 'left',
                    field: 'applyMemo',
                    formatter: function status(value,row) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '申请附件',
                    field: 'sysUploadFiles',
                    align: 'left',
                    formatter: function(value, row, index) {
                        var html = "<div class='picviewer'>"
                        if(value != null && value != '') {
                            value.forEach(function (element, index) {
                                html += `<img src="`+element.filePath+`"/>`
                            });
                        }else {
                            html = '-';
                        }
                        html +="</div>"
                        return html;
                    }
                },
                {
                    title: '创建人/创建时间',
                    align: 'left',
                    field: 'regUserName',
                    formatter: function (value, row, index) {
                        let a = '';
                        if(value !== undefined && value !== null){
                            a += value;
                        }
                        if(row.regDate !== undefined && row.regDate !== null){
                            a += '<br />' + row.regDate;
                        }
                        return a;
                    }
                },

                {
                    title: '申请人/申请时间',
                    align: 'left',
                    field: 'applyUserName',
                    formatter: function (value, row, index){
                        let a = '';
                        if(value !== undefined && value !== null){
                            a += value;
                        }
                        if(row.applyDate !== undefined && row.applyDate !== null){
                            a += '<br />' + row.applyDate;
                        }
                        return a;

                    }
                },
                {
                    title: '收款人/收款时间',
                    align: 'left',
                    field: 'payUserName',
                    formatter: function (value, row, index){

                        let a = '';
                        if(value !== undefined && value !== null){
                            a += value;
                        }
                        if(row.payDate !== undefined && row.payDate !== null){
                            a += '<br />' + row.payDate;
                        }
                        return a;
                    }
                }




            ],
            onLoadSuccess: function(data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar:false,
                });
            }
        };

        $.table.init(options1);
    });

    // 控制锁定备注输入框的显示和隐藏
    function toggleLockRemark(show,indx) {
        if (show) {
            $(`#lockPayReason_${indx}`).show();
        } else {
            $(`#lockPayReason_${indx}`).hide();
            // $("#lockRemark").val("");
        }
    }


    function showInsurancePolicy() {
        layer.open({
            type: 2,
            area: ['90%', '90%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "异常统计",
            content: ctx + "insurancePolicy/popUp",
            btn: ['确认', '关闭'],
            // shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                let checked = iframeWin.contentWindow.getChecked();

                console.log(checked);
                if (checked.length > 0) {
                    $("#insurancePolicyId").val(checked[0].id)

                    let insuredParty = (bala_corp.find(d => d.dictValue == ('' + checked[0].insuredParty)) || {}).dictLabel || '暂无';
                    let insuranceCompany = checked[0].insuranceCompany == null ? "暂无" : checked[0].insuranceCompany
                    let policyNo = checked[0].policyNo == null ? "暂无" : checked[0].policyNo
                    let ownerType = (insurance_owner_type.find(d => d.dictValue == ('' + checked[0].ownerType)) || {}).dictLabel || '暂无';

                    $("#insurancePolicy").val(ownerType + '-' + insuredParty + '-' + insuranceCompany + '-' + policyNo);

                }
                layer.close(index);
            },
            cancel: function (index) {
                return true;
            }
        });

    }


    //弹出承运商
    var carrierIndex = 0;
    function showCarrier() {
        $.modal.open("选择承运商", ctx + "basic/carrier/selectCarrier", '', '', function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //承运商去重
            var carrierflag = true
            $('input[name^="carrierId"]').each(function () {
                if(rows[0].carrierId == $(this).val()) {
                    $.modal.alertWarning("请勿选择重复的承运商!");
                    carrierflag  = false;
                }
            })
            if(carrierflag) {
                //获取最后一个承运商的index
                carrierIndex = $('div[class^="dhlist"]').length
                var html =` <div class=\"cys\">
                <div class="flex" style="align-items: center;">
                    <div style="width: 30px;">
                        <div onclick=\"deleteCarrier(this)\">
                            <i class="fa fa-times text-danger" aria-hidden="true"  style="font-size: 20px;"></i>
                        </div>
                    </div>
                    <div style="width: 100%;">
                        <div class="ydbox flex selloutBj" style="border-radius: 5px;width: 100%;">
                            <img src="/img/kh.png" style="width: 60px;height: 60px;"/>
                            <div class="ml5">
                                <div class="flex">
                                    <div class="flex">
                                        <input type="hidden" name="carrierId" id="carrierId" class="form-control" value="`+  rows[0].carrierId  +`">
                                        <div class="fw">
                                            <span>${rows[0].carrName}</span>
                                            <span class="label label-primary pa2">${rows[0].balaType==1?'单笔':'月结'}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex">
                                    <div class="flex">
                                        <div class="flex_left fc80">联系人：</div>
                                        <div class="flex_right">`+  rows[0].contact +`/`+ rows[0].phone +`</div>
                                    </div>
                                </div>
                                   <div class="flex">
                                    <div class="flex">
                                        <div class="flex_left fc80">锁定付款：</div>
                                        <div class="flex_right">
                                            <label class="radio-inline">
                                                <input type="radio" name="carrierLockStatus_${carrierIndex}" value="1"
                                                       ${rows[0].lockPay==1 ? 'checked' : ''}
                                                       onclick="toggleLockRemark(true,${carrierIndex})"> 锁定
                                            </label>
                                            <label class="radio-inline">
                                                <input type="radio" name="carrierLockStatus_${carrierIndex}" value="0"
                                                        ${rows[0].lockPay==0 ? 'checked' : ''}
                                                        onclick="toggleLockRemark(false,${carrierIndex})"> 不锁定
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <textarea name="lockPayReason_${carrierIndex}" id="lockPayReason_${carrierIndex}"
                                      class="form-control" placeholder="锁定备注" style="margin: 0 10px; flex: 1; height: 55px; resize: none;${rows[0].lockPay==0 ? 'display: none;' : ''}">${rows[0].lockPayReason}</textarea>
                            <input id="carrierId_${carrierIndex}" type="hidden"
                                   value="${rows[0].carrierId}">

<!--                            <div class='sellout'>承运商</div>-->
                        </div>
                    </div>
                </div>
                <div class="collapse in" id="collapseExample`+ carrierIndex + `">
                    <div class="over" style="margin-left: 30px;">
                        <div>
                            <div class="dhlist`+ carrierIndex + ` hidden">
                                <span></span>
                            </div>

                            <table class="table table-bordered">
                                <thead style="background:#f7f8fa">
                                <tr>
                                    <th style="text-align: center;width: 2%;" onclick=\"showEntrusts('`+ carrierIndex + `\',\'` +  rows[0].carrierId+`')\">
                                        <i class="fa fa-plus text-navy" style="font-size: 12px;"></i>
                                    </th>
                                    <th style="width: 40%;">运单信息</th>
                                    <th style="width: 30%;">付款信息</th>
                                    <th style="width: 20%;">应付扣减</th>
                                    <th style="text-align: center;width: 8%;">操作</th>
                                </tr>
                                </thead>
                                <tbody class="yingfu` + carrierIndex +` ">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>`

                //添加承运商按钮之后拼接
                $("#addCarrier").after(html)
                layer.close(index);
            }
        });
    }

    /**
     * 删除承运商
     */
    function deleteCarrier(obj) {
        $(obj).parent().parent().parent().remove();
        calculate_ydzpk_ydzpk_pk()
        calculate_ydzje()
    }

    /* 弹出委托单列表 */
    var lotNoIndex = 0;
    /*function showEntrusts(lotNoIndex,carrierId) {
        var url = prefix + "/entrustlist?carrierId=" + carrierId;
        $.modal.open("运单列表", url, '1000', '600', function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            //定义结果数组
            var arr = [];
            for (var i = 0; i < rows.length; i++) {
                arr.push(rows[i]);
            }
            //获取上一次选择的运单号
            var choseIndex = ".dhlist"+lotNoIndex;

            var lotnolist = [];
            $(choseIndex).children("span").each(function () {
                //str = str.replace(/\s*!/g,"");
                if($(this).text() != null && $(this).text() != '') {
                    lotnolist.push($(this).text().replace(/\s*!/g,""))
                }
            })
            //将新选择的运单号和原来选中的对比
            var newCodeArray = [];
            $.map(arr, function (row) {
                if(lotnolist.indexOf(row["lot"]) == -1) {
                    newCodeArray.push(row["lot"])
                }
            })
            //拼接发货单号(运单号)
            var lot = $.map(newCodeArray, function (row) {
                return row;
            }).join(",");
            //拼接运单对应的应付明细 页面
            $.ajax({
                url: payprefix + "/paydetaillist",
                type: "post",
                dataType: "json",
                data: {"lotno": lot},
                success: function (data) {
                    var html = "";
                    var lotcode = "";
                    //应付页面拼接
                    $.map(data, function (array, index) {
                        array.forEach( response=> {
                            let transFeeCounts=0;
                            response.payDetails.map(res=>{ transFeeCounts+=(Number(res.transFeeCount)+Number(res.taxAmount)); })
                            response.payDetails.forEach((item,i)=>{
                                if(i==0){
                                    lotcode = lotcode + "<span>"+ item.lotno  + "</span>\n";
                                }
                                let vbillstatusHtml=""
                                if(item.vbillstatus == 0) {
                                    vbillstatusHtml =  "<span class=\"label label-default\">新建</span>"
                                }else if(item.vbillstatus == 1) {
                                    vbillstatusHtml =  "<span class=\"label label-warning\">已确认</span>"
                                }else if(item.vbillstatus == 2) {
                                    vbillstatusHtml =  "<span class=\"label label-coral\">已对账</span>"
                                }else if(item.vbillstatus == 3) {
                                    vbillstatusHtml =  "<span class=\"label label-info\">部分核销</span>"
                                }else if(item.vbillstatus == 4) {
                                    vbillstatusHtml =  "<span class=\"label label-success\">已核销</span>"
                                }else if(item.vbillstatus == 5) {
                                    vbillstatusHtml =  "<span class=\"label label-inverse\">关闭</span>"
                                }else if(item.vbillstatus == 6) {
                                    vbillstatusHtml =  "<span class=\"label label-success\">已申请</span>"
                                }else if(item.vbillstatus == 7) {
                                    vbillstatusHtml =  "<span class=\"label label-info\">核销中</span>"
                                }else {
                                    vbillstatusHtml =  "<span class=\"label label-default\">审核中</span>"
                                }
                                let costType="";
                                if(item.freeType == '0'){
                                    costType=item.costTypeFreight;
                                }else{
                                    costType=item.costTypeOnWay;
                                }

                                let payMan=[],payMethodT=[];
                                item.payRecordList.forEach(a=>{
                                    if(a.payMan){
                                        payMan.push(a.payMan);
                                    }
                                    if(a.payMethod){
                                        payMethod.forEach(b=>{
                                            if(a.payMethod==b.dictValue){
                                                payMethodT.push( b.dictLabel );
                                            }
                                        })
                                    }
                                })

                                if(i==0){
                                    html+=`<tr class="no_`+item.lotno+`">
                                        <td style="text-align: center;" rowspan="`+response.payDetails.length+`" ><i class="fa fa-close" onclick="removeLot('` + lotNoIndex + `', '` + item.lotno + `')"></i></td>
                                        <td rowspan="`+response.payDetails.length+`">
                                            <div> 运单-`+item.lotno+`</div>
                                            <div class="flex mt5">
                                                <img src="/img/sj.png" style="width: 20px;height: 20px;"/><span>`+response.entrustLot.driverName+`/`+response.entrustLot.driverMobile+`</span>
                                            </div>
                                            <div class="flex mt5">
                                                <img src="/img/cl.png" style="width: 20px;height: 20px;"/> <span>`+response.entrustLot.carNo+`</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="flex">
                                                <span>`+vbillstatusHtml+ `</span>
                                                <div class="ml0">
                                                    <div>
                                                        <span>`+(item.transFeeCount + item.taxAmount)+`</span> -
                                                        <span >` + costType +`</span>
                                                    </div>
                                                    <div> `+ item.vbillno+`</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td rowspan="`+response.payDetails.length+`">
                                            <span class="label label-dangerT"></span>
                                            <input type="hidden" name="lotIdTransFeeList">
                                        </td>
                                        <td rowspan="`+response.payDetails.length+`">
                                            <input type="hidden" name="transFeeCount2" value="`+transFeeCounts+`">
                                            <span class="text-success" onclick="deduct(this,this.getAttribute('data-lotno'),this.getAttribute('data-transFeeCounts'))"
                                            data-lotno="`+item.lotId+`"  data-transFeeCounts="`+transFeeCounts+`">扣款</span>
                                        </td>
                                    </tr>`
                                }else{
                                    html+=`<tr class="no_`+item.lotno+`">
                                        <td>
                                            <div  class="flex">
                                                <span>`+vbillstatusHtml+ `</span>
                                                <div class="ml0">
                                                    <div>
                                                        <span>`+(item.transFeeCount + item.taxAmount)+`</span> -
                                                        <span >` + costType +`</span>
                                                    </div>
                                                    <div> `+ item.vbillno+`</div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>`
                                }

                            })
                        })

                        //html赋值
                        $('div[class^=\"dhlist'+ lotNoIndex +'\"]').append(lotcode)
                        $('tbody[class^=\"yingfu'+ lotNoIndex +'\"]').append(html)
                        //清空第一个html
                        html = "";
                        lotcode = "";
                        invoiceIds("transFeeCount2","#ydzje",2)
                    })
                }
            });
            //回填数据
            $("input[name='lotno']").val(lot);
            layer.close(index);
        })
    }*/

    function showEntrusts(lotNoIndex,carrierId,num) {
        var fixedLot = $("#fixedLot").text();

        const lotIds = $('[name="lotIds"]').map(function(){
            return $(this).val();
        }).get().join(',');

        var url = ctx + "carrier/entrustLot/selectEntrustLotListForException/" + carrierId + "/" + lotIds;
        layer.open({
            type: 2,
            area: ['95%', '95%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "运单列表",
            content: url,
            btn: ['确定','关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function (index, layero) {
                //获取选择的委托单整行
                let deductionValue = layero.find('iframe')[0].contentWindow.$("#deductionValue").val();
                var rows = layero.find('iframe')[0].contentWindow.getChecked();
                //定义结果数组
                var arr = [];
                for (var i = 0; i < rows.length; i++) {
                    //判断多个委托单之间是否有重复的运单号,排除掉一开始主页面上的一个运单号;  运单对应的应付
                    if(fixedLot != rows[i].lot) {
                        arr.push(rows[i]);
                    }

                }
                var lot = $.map(rows, function (row) {
                    return row.lot;
                }).join(",");

                var lotIds = $.map(rows, function (row) {
                    return row.entrustLotId;
                });

                //拼接运单对应的应付明细 页面
                $.ajax({
                    url: payprefix + "/paydetaillist",
                    type: "post",
                    dataType: "json",
                    data: {"lotno": lot},
                    success: function (data) {
                        var html = "";
                        var lotcode = "";
                        //应付页面拼接
                        $.map(data, function (array, index) {
                            array.forEach(response => {
                                //可扣款金额
                                let transFeeCounts=0;
                                response.payDetails.map(res => {
                                    if ((res.vbillstatus==0 || res.vbillstatus==1) && res.incomeRemark == 0) {
                                        transFeeCounts += (Number(res.transFeeCount) + Number(res.taxAmount));
                                    }
                                });
                                //总金额
                                let feeCnt=0;
                                response.payDetails.map(res => {
                                    if (res.incomeRemark == 0) {
                                        feeCnt += (Number(res.transFeeCount) + Number(res.taxAmount));
                                    }
                                });
                                //已扣款金额
                                let kk_feeCnt=0;
                                response.payDetails.map(res => {
                                    if (res.incomeRemark == 1) {
                                        kk_feeCnt += (Number(res.transFeeCount) + Number(res.taxAmount));
                                    }
                                });
                                //已赔款金额
                                let pk_feeCnt=0;
                                response.payDetails.map(res => {
                                    if (res.incomeRemark == 2) {
                                        pk_feeCnt += (Number(res.transFeeCount) + Number(res.taxAmount));
                                    }
                                });


                                response.payDetails.forEach((item,i)=>{
                                    if(i==0){
                                        lotcode = lotcode + "<span>"+ item.lotno  + "</span>\n";
                                    }
                                    let vbillstatusHtml=""
                                    if(item.vbillstatus == 0) {
                                        vbillstatusHtml =  "<span class=\"label label-default\">新建</span>"
                                    }else if(item.vbillstatus == 1) {
                                        vbillstatusHtml =  "<span class=\"label label-warning\">已确认</span>"
                                    }else if(item.vbillstatus == 2) {
                                        vbillstatusHtml =  "<span class=\"label label-coral\">已对账</span>"
                                    }else if(item.vbillstatus == 3) {
                                        vbillstatusHtml =  "<span class=\"label label-info\">部分核销</span>"
                                    }else if(item.vbillstatus == 4) {
                                        vbillstatusHtml =  "<span class=\"label label-success\">已核销</span>"
                                    }else if(item.vbillstatus == 5) {
                                        vbillstatusHtml =  "<span class=\"label label-inverse\">关闭</span>"
                                    }else if(item.vbillstatus == 6) {
                                        vbillstatusHtml =  "<span class=\"label label-success\">已申请</span>"
                                    }else if(item.vbillstatus == 7) {
                                        vbillstatusHtml =  "<span class=\"label label-info\">核销中</span>"
                                    }else {
                                        vbillstatusHtml =  "<span class=\"label label-default\">审核中</span>"
                                    }
                                    let costType="";
                                    if(item.freeType == '0'){
                                        costType=item.costTypeFreight;
                                    }else{
                                        costType=item.costTypeOnWay;
                                    }
                                    let payRecordList=""
                                    item.payRecordList.forEach(payRecord=>{
                                        payRecordList += `<div>`;
                                        payMethod.forEach(b=>{
                                            if(payRecord.payMethod==b.dictValue){
                                                payRecordList+=`<span>`+ b.dictLabel+`</span> -`

                                            }
                                        })
                                        payRecordList+=` <span>`+payRecord.recAccount+`</span></div>`
                                    })

                                    let  hasDeduct = [[${@permission.hasPermi('tms:trace:abnormal:deduct')}]] != "hidden"

                                    if(i==0){
                                        html+=`
                                    <tr class="no_`+item.lotno+`">
                                    <input type="hidden" name="lotIds" value="${response.entrustLot.entrustLotId}">
                                        <td style="text-align: center;" rowspan="`+response.payDetails.length+`" ><i class="fa fa-close" onclick="removeLot('` + lotNoIndex + `', '` + item.lotno + `','`+num+`')"></i></td>
                                        <td rowspan="`+response.payDetails.length+`">
                                            <div> 运单-`+item.lotno+`</div>
                                            <div class="flex mt5">
                                                <img src="/img/sj.png" style="width: 20px;height: 20px;"/><span>`+response.entrustLot.driverName+`/`+response.entrustLot.driverMobile+`</span>
                                            </div>
                                            <div class="flex mt5">
                                                <img src="/img/cl.png" style="width: 20px;height: 20px;"/> <span>`+response.entrustLot.carNo+`</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="flex">
                                                <span>`+vbillstatusHtml+ `</span>
                                                <div class="ml0">
                                                    <div>
                                                        <span>`+(item.transFeeCount + item.taxAmount)+`</span> -
                                                        <span >` + costType +`</span>
                                                    </div>
                                                    <div> `+ item.vbillno+`</div>
                                                    `+payRecordList+`
                                                </div>
                                            </div>
                                        </td>
                                        <td rowspan="`+response.payDetails.length+`">
                                            <div>
                                                扣：<span id="${item.lotId}_kk_sapn" class="label label-dangerT">${kk_feeCnt}</span>
                                                <input  id="${item.lotId}_kk_input" type="hidden" name="lotIdTransFeeList" value="${response.entrustLot.entrustLotId}-0">
                                            </div>
                                            <!--<div class="mt20">
                                                赔：<span id="${item.lotId}_pk_sapn" class="label label-dangerT">${pk_feeCnt}</span>
                                            </div>-->
                                        </td>
                                        <td rowspan="`+response.payDetails.length+`">
                                            <input type="hidden"
                                                   id="${item.lotId}_transFeeCount2"
                                                   name="transFeeCount2" value="`+transFeeCounts+`">
                                            <input type="hidden"
                                                   id="${item.lotId}_all_feeCnt" value="${feeCnt}">

                                        `
                                        if(hasDeduct){
                                            html+=`
                                            <div>
                                                <input type="hidden" id="${item.lotId}_kk_feeCnt" value="${kk_feeCnt}">
                                                <span  class="text-success" onclick="deduct(this)" style="cursor:pointer"
                                                    data-lotno="${item.lotId}">应付扣减</span>
                                            </div>
                                            <div class="mt20" style="cursor: pointer;">
                                                <input type="hidden" id="${item.lotId}_pk_feeCnt" value="${pk_feeCnt}">
                                                <!--<span  class="text-success" onclick="deductDed(this)"
                                                    data-lotno="${item.lotId}">赔款</span>-->
                                            </div>
                                            `
                                        }

                                        html+=`</td></tr>`
                                    }else{
                                        html+=`<tr class="no_`+item.lotno+`">
                                        <td>
                                            <div  class="flex">
                                                <span>`+vbillstatusHtml+ `</span>
                                                <div class="ml0">
                                                    <div>
                                                        <span>`+(item.transFeeCount + item.taxAmount)+`</span> -
                                                        <span >` + costType +`</span>
                                                    </div>
                                                    <div> `+ item.vbillno+`</div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>`
                                    }


                                })
                            })

                            if (num==1) {
                                //html赋值
                                $('div[class^="dhlistT"]').append(lotcode)

                                $('tbody[class^="yingfuT"]').append(html)
                            }else{
                                //html赋值
                                $('div[class^=\"dhlist'+ lotNoIndex +'\"]').append(lotcode)

                                $('tbody[class^=\"yingfu'+ lotNoIndex +'\"]').append(html)
                            }


                            //清空第一个html
                            html = "";
                            lotcode = "";
                            calculate_ydzje()
                        })//map循环结束

                        if (deductionValue != '' && deductionValue != undefined) {
                            deductAll(lotIds, deductionValue);
                        }

                    }
                });

                $("input[name='lotno']").val(lot);


                //拼接该单号对应的车辆和司机信息
                layer.close(index);
            },
            // btn2: function(index, layero){
            //
            //
            //
            //
            //     layer.close(index);
            // },
            cancel: function(index) {
                return true;
            }
        });
    }

    //删除运单
    function removeLot(lotNoIndex,lotno,num) {
        var choseIndex = ".dhlist"+lotNoIndex;
        //获取上一次选择的运单号
        var lotnolist = [];
        $(choseIndex).children("span").each(function () {
            if($(this).text() != null && $(this).text() != '') {
                lotnolist.push($(this).text().replace(/\s*/g,""))
            }
        })
        if(num==1){
            $(".yingfuT").find('tr[class="no_'+lotno+'"]').remove();
        }else{
            $("." + "yingfu" + lotNoIndex).find('tr[class="no_'+lotno+'"]').remove();
        }

        calculate_ydzpk_ydzpk_pk()
        calculate_ydzje()
        //找到对应的运单号
        for (var i = 0; i < lotnolist.length; i++) {
            if(lotnolist[i] == lotno) {
                if(num==1){
                    $(".dhlistT").find("span").eq(i).remove();
                }else if(lotNoIndex == 0) {
                    //删除div dhlist下的第i+1个span中内容
                    $("." + "dhlist" + lotNoIndex).find("span").eq(i).remove();
                    //删除对应的应付明细
                    // $("." + "yingfu" + lotNoIndex).find(".fl").eq(i).remove();
                }else {
                    //删除div dhlist下的第i+1个span中内容
                    $("." + "dhlist" + lotNoIndex).find("span").eq(i).remove();
                    //删除对应的应付明细
                    // $("." + "yingfu" + lotNoIndex).find(".fl").eq(i).remove();
                }
            }
        }
    }


    function removeLotById(element) {
        var lotId = element.getAttribute('data-lot-id');


        layer.confirm('确定解除该条数据吗?', function (index) {
            $.ajax({
                url: ctx + "entrustexceptionmore" + "/removeLot",
                type: "post",
                dataType: "json",
                data: {"lotId": lotId, "entrustExpId": entrustExpAfter.entrustExpId},
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function (data) {
                    if (data.code == 0) {
                        $(`tr[id^="${lotId}_"]`).each(function() {
                            // 移除每个匹配的元素
                            $(this).remove();
                        });

                        $.modal.msgSuccess(data.msg);
                    }else {
                        $.modal.msgError(data.msg);
                    }

                    $.modal.closeLoading();
                    $.modal.enable();
                }
            })
            layer.close(index);
        });
    }

    //添加客户单号
    function invoiceReceive(customerId) {
        var fixedLot = $("#fixedLotT").text();
        var url = ctx + "invoice/toInvoiceReceiveDetailList/" + customerId;
        $.modal.open("选择发货单 ", url, '1000', '600', function (index, layero) {
            var rows = layero.find('iframe')[0].contentWindow.getChecked();

            //定义结果数组
            var arr = [];
            for (var i = 0; i < rows.length; i++) {
                //判断多个委托单之间是否有重复的运单号,排除掉一开始主页面上的一个运单号;  运单对应的应付
                if(fixedLot != rows[i].vbillno) {
                    arr.push(rows[i]);
                }

            }
            //获取上一次选择的运单号
            var lotnolist = [];
            $('div[class^="dhlistR"]').children("span").each(function () {
                if($(this).text() != null && $(this).text() != '') {
                    lotnolist.push($(this).text())
                }
            })
            //将新选择的运单号和原来选中的对比
            var newCodeArray = [];
            $.map(arr, function (row) {
                if(lotnolist.indexOf(row["vbillno"]) == -1) {
                    newCodeArray.push(row)
                }
            })


            var html = "";
            var lotcode = "";
            newCodeArray.forEach(response=>{
                invoiceIdList.push(response.invoiceId)

                lotcode += "<span>"+ response.vbillno  + "</span>\n";
                let transFeeCounts=0;
                response.receiveDetailList.map(res=>{
                    // if (res.vbillstatus!=3&&res.vbillstatus!=4) {
                        transFeeCounts+=Number(res.transFeeCount);
                    // }
                })

                let all_receiveFeeCnt =0
                response.receiveDetailList.map(res => {
                    if (res.payoutMark == 0) {
                        all_receiveFeeCnt += Number(res.transFeeCount)
                    }
                });

                response.receiveDetailList.forEach((item,i)=>{

                    let receiveDetailStatusEnumHtml="";
                    receiveDetailStatusEnum.forEach(resT=>{
                        if(item.vbillstatus==resT.value){
                            receiveDetailStatusEnumHtml=resT.context;
                        }
                    })

                    // let carLenHtml=""
                    // carLen.forEach(b=>{
                    //     if(response.carLen==b.dictValue){
                    //         carLenHtml= b.dictLabel;
                    //     }
                    // })
                    if(i==0){
                        html+=`<tr class="no_`+response.vbillno+`">
                                <td rowspan="`+response.receiveDetailList.length+`"><i class="fa fa-close" onclick="removeLoNo('` + response.vbillno + `','`+ response.invoiceId +`')"></i></td>
                                <td rowspan="`+response.receiveDetailList.length+`">
                                    <div> 发货单-<span>`+response.vbillno+`</span> </div>
                                    <div>
                                        <span class="label label-warning pa2">装</span>
                                        <span>`+response.shippingAddress+`</span>
                                    </div>
                                    <div>
                                        <span class="label label-success pa2">卸</span>
                                        <span>`+response.receivingAddress+`</span>
                                    </div>
                                </td>
                                <td rowspan="`+response.receiveDetailList.length+`">
                                    <div class="flex" style="background-color: #F4F5F7;padding: 8px;">
                                        <img src="/img/wp.png" style="width: 20px;height: 20px;"/><span>`+response.goodsName+`</span>
                                        <span class="ml5">`+response.numCount+`件</span>
                                        <span class="ml5">`+response.weightCount+`吨</span>
                                        <span class="ml5">`+response.volumeCount+`m³</span>
                                    </div>
                                    <div>
                                        <span>`+$.table.selectDictLabel(carLen, response.carLen)+`</span>
                                        <span>`+response.carTypeName+`</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="flex">
                                        <div>`+receiveDetailStatusEnumHtml+`</div>
                                        <div class="ml5">
                                            <div>`+item.transFeeCount+` - `+$.table.selectDictLabel(balaType, item.balatype)+`</div>
                                            <div>`+item.vbillno+`</div>
                                        </div>
                                    </div>
                                </td>
                                <td rowspan="`+response.receiveDetailList.length+`">
                                    <input type="hidden"
                                           id="${response.invoiceId}_all_receiveFeeCnt"
                                           value="${all_receiveFeeCnt}">

                                    <!-- 应收增减基础值 -->
                                    <input type="hidden" id="${response.invoiceId}_base_feeCnt" value="0">

                                    <span class="label label-dangerT"></span>
                                    <input type="hidden" name="invoiceIds" value="`+response.invoiceId+`-0-deduct">
                                </td>
                                <td rowspan="`+response.receiveDetailList.length+`">
                                    <div style="display:`+ (hasShowReceBtnPerm ? 'block' : 'none') + `">
                                        <input type="hidden" name="transFeeCount1" value="`+transFeeCounts+`">
                                        <span class="text-success" style="cursor:pointer" onclick="compensation(this,this.getAttribute('data-invoiceId'),this.getAttribute('data-transFeeCounts'))" data-transFeeCounts="`+transFeeCounts+`" data-invoiceId="`+response.invoiceId+`">应收增减</span>
                                    </div>

                                </td>
                               </tr>`
                    }else{
                        html+=`<tr class="no_`+response.vbillno+`">
                                <td>
                                    <div class="flex">
                                        <div>`+receiveDetailStatusEnumHtml+`</div>
                                        <div class="ml5">
                                            <div>`+item.transFeeCount+`-`+$.table.selectDictLabel(balaType, item.balatype)+`</div>
                                            <div>`+item.vbillno+`</div>
                                        </div>
                                    </div>
                                </td>
                            </tr>`
                    }
                })
            })
            $('div[class^="dhlistR"]').append(lotcode)
            $('tbody[class^="yingfuR"]').append(html)
            //清空第一个html
            html = "";
            lotcode = "";
            // invoiceIds("transFeeCount1","#thdzje",2)
            calculate_thdzje()

            layer.close(index);

            getOtherFee()
        })
    }

    //删除客户单号
    function removeLoNo(vbillno, invoiceId){
        invoiceIdList = invoiceIdList.filter(function(value) {
            return value !== invoiceId;
        });

        $(".yingfuR").find('tr[class="no_'+vbillno+'"]').remove();
        // invoiceIds("invoiceIds","#thdzpk",1);
        calculate_thdzpk()
        // invoiceIds("transFeeCount1","#thdzje",2);
        calculate_thdzje()

        $(".dhlistR").children("span").each(function () {
            if($(this).text() != null && $(this).text() != '') {
                if($(this).text()==vbillno){
                    $(this).remove();
                }
            }
        })

        getOtherFee()
    }

    //三方异常费用
    let abnormalFee = 0;
    function getOtherFee() {
        let ids = invoiceIdList.join(',');

        $.ajax({
            url: ctx + "trace/getOtherFee",
            type: "post",
            dataType: "json",
            data: {"invoiceIds": ids},
            success: function (data) {

                if (data.code === 0) {
                    let idList = []

                    let otherFeeAll = 0;
                    abnormalFee = 0

                    let html = ''
                    data.data.forEach(function(ele, index) {
                        //状态
                        let vbillstatus = ''
                        if (ele.vbillstatus == 0) {
                            //新建
                            vbillstatus = '<span class="label label-default">新建</span>';
                        }else if (ele.vbillstatus == 1) {
                            //已付款
                            vbillstatus = '<span class="label label-success">已付款</span>';
                        }else if(ele.vbillstatus == 2){
                            //已申请
                            vbillstatus = '<span class="label label-primary">已申请</span>';
                        }else if(ele.vbillstatus == 3){
                            //已对账
                            vbillstatus = '<span class="label label-coral">已对账</span>';
                        }
                        //金额
                        let feeAmount = ''
                        if (ele.feeAmount) {
                            feeAmount = ele.feeAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }
                        if (!isNaN(ele.feeAmount)) {
                            otherFeeAll += ele.feeAmount;
                        }

                        if (ele.feeType == '27' && !isNaN(ele.feeAmount)) {
                            abnormalFee += ele.feeAmount;
                        }

                        //费用类型
                        let feeType = '';
                        if (ele.feeType) {
                            feeType = $.table.selectDictLabel(costTypeOnWay, ele.feeType);
                        }

                        //付款类型/付款方式
                        let payType = ''
                        if(ele.payType == 0){
                            payType = '现金' + '/' + $.table.selectDictLabel(payMethod, ele.payMethod);
                        }
                        if(ele.payType == 1){
                            payType = '油卡'+ '/'+ $.table.selectDictLabel(payMethod, ele.payMethod)
                        }
                        //备注
                        let memo = ''
                        if (ele.memo) {
                            memo = ele.memo
                        }

                        //操作
                        let length = data.data.filter(item => item.lotId === ele.lotId).length;

                        let operate = ''
                        if (!idList.includes(ele.lotId)) {
                            operate = `<td rowspan="${length}" style="text-align: center;">
                                            <span class="text-success" style="cursor: pointer;" onclick="feeEntry('${ele.lotId}')">
                                                新添赔款
                                            </span>
                                       </td>`
                            idList.push(ele.lotId);
                        }

                        html = html +
                            `<tr>
                                <td>${ele.vbillno} / ${ele.invoiceVbillno}</td>
                                <td>${vbillstatus}</td>
                                <td style="text-align: right;">${feeAmount}</td>
                                <td>${feeType}</td>
                                <td>${payType}</td>
                                <td>${memo}</td>
                            </tr>`;
                    });

                    calculate_thdzpk();

                    $("#othenFreeTbody").empty();
                    $("#othenFreeTbody").append(html)

                    $("#otherFeeAll").html(otherFeeAll.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))

                    if ($("#othenFreeTbody").children().length < 1) {
                        $("#othenFreeTbody").append("<tr><td colspan='7' style='text-align: center'>暂无第三方数据</td></tr>")
                    }

                }
            }
        })
    }

    /**
     * 第三方费用录入
     */
    function feeEntry(invoiceId) {
        //关账校验
        // if (checkCloseAccount()) {
        //     $.modal.alertWarning("该月份已关账，无法进行操作！");
        //     return false;
        // }

        layer.open({
            type: 2,
            area: ['60%', '60%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "第三方费用录入",
            content: ctx + "invoice/feeEntry/" + invoiceId,
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero,function (){
                    getOtherFee()
                });
            },
            cancel: function(index) {
                return true;
            }
        });

    }


    var index = 0;
    /* 损失新增表格行 */
    function insertRow() {
        index++;
        var rowTmpl = $("#rowLoss").html();
        rowTmpl = rowTmpl.replace("lossItem", "lossItem" + index);
        rowTmpl = rowTmpl.replace("vbillno", "vbillno" + index);
        rowTmpl = rowTmpl.replace("lossAmount", "lossAmount" + index);
        rowTmpl = rowTmpl.replace("remark", "remark" + index);
        $("#losstab tbody").append(rowTmpl);
    }

    /* 删除指定表格行 */
    function removeRow(obj) {
        $("#losstab tbody").find(obj).closest("tr").remove();
        index--;
    }

    var indexincome = 0;
    /* 收入新增表格行 */
    function insertIncomeRow() {
        var isClose = entrust.isClose;
        indexincome++;
        var rowTmpl = $("#rowIncome").html();
        rowTmpl = rowTmpl.replace("lossItem", "lossItem" + indexincome);
        rowTmpl = rowTmpl.replace("vbillno", "vbillno" + indexincome);
        rowTmpl = rowTmpl.replace("lossAmount", "lossAmount" + indexincome);
        rowTmpl = rowTmpl.replace("remark", "remark" + indexincome);
        $("#incometab tbody").append(rowTmpl);
    }

    /* 删除指定表格行 */
    function removeIncomeRow(obj) {
        $("#incometab tbody").find(obj).closest("tr").remove();
        indexincome--;
    }

    var trackingIndex = 0;
    /** 添加跟踪记录 */
    function insertRowTrace() {
        trackingIndex++;
        var rowTmpl = $("#rowTrace").html();

        rowTmpl = rowTmpl.replace("trackingDate", "trackingDate" + trackingIndex);
        rowTmpl = rowTmpl.replace("trackingContent", "trackingContent" + trackingIndex);
        rowTmpl = rowTmpl.replace("assistanceContent", "assistanceContent" + trackingIndex);
        $("#tracetab tbody").append(rowTmpl);

        layui.use('laydate', function(){
            var id = "#trackingDate" + trackingIndex;
            var laydate = layui.laydate;

            laydate.render({
                elem: '#trackingDate' + trackingIndex, //指定元素
                // format: 'yyyy-MM-dd',
                trigger: 'click',
                type: 'datetime',
                ready: function (date) {
                    var now = new Date();
                    this.dateTime.hours=now.getHours();
                    this.dateTime.minutes=now.getMinutes();
                    this.dateTime.seconds=now.getSeconds();
                },
                done: function (value, date, endDate) {
                    $(id).val(value);
                    $("#form-adnormal-add").validate().element($(id));
                }
            });
        });
    }

    /* 删除追踪记录指定表格行 */

    function removeTraceRow(obj) {
        $("#tracetab tbody").find(obj).closest("tr").remove();
        trackingIndex--;
    }

    /**
     * 损失金额填入
     */
    function lossheji() {
        //损失合计展示
        var  losssum = 0;
        $("#losstab").find("input[name^='lossAmount']").each(function () {
            if($(this).val() != null && $(this).val() != '') {
                losssum = accAdd(losssum,parseFloat($(this).val()))
            }
        })
        var  incomesum = 0;
        $("#incometab").find("input[name^='lossAmount']").each(function () {
            if($(this).val() != null && $(this).val() != '') {
                incomesum = accAdd(incomesum,parseFloat($(this).val()))
            }
        })
        $(".loss_total").find("span").eq(1).text(losssum)
        //公司实际损失
        $(".real_total").find("span").eq(1).text(accSub(losssum,incomesum))
    }
    /**
     * 收入金额填入
     */
    function incomeheji() {
        //损失合计展示
        var  losssum = 0;
        $("#losstab").find("input[name^='lossAmount']").each(function () {
            if($(this).val() != null && $(this).val() != '') {
                losssum = accAdd(losssum,parseFloat($(this).val()))
            }
        })
        //收入合计展示
        var  incomesum = 0;
        $("#incometab").find("input[name^='lossAmount']").each(function () {
            if($(this).val() != null && $(this).val() != '') {
                incomesum = accAdd(incomesum,parseFloat($(this).val()))
            }
        })
        $(".income_total").find("span").eq(1).text(incomesum)
        //公司实际损失
        $(".real_total").find("span").eq(1).text(accSub(losssum,incomesum))
    }

    /**
     * 加法函数，用来得到精确的加法结果
     * 说明：javascript的加法结果会有误差，在两个浮点数相加的时候会比较明显。这个函数返回较为精确的加法结果。
     * 调用：accAdd(arg1,arg2)
     * 返回值：arg1加上arg2的精确结果
     *
     * @param arg1
     * @param arg2
     * @returns {number}
     */
    function accAdd(arg1, arg2) {
        var r1, r2, m, c;
        try {
            r1 = arg1.toString().split(".")[1].length;
        }
        catch (e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        }
        catch (e) {
            r2 = 0;
        }
        c = Math.abs(r1 - r2);
        m = Math.pow(10, Math.max(r1, r2));
        if (c > 0) {
            var cm = Math.pow(10, c);
            if (r1 > r2) {
                arg1 = Number(arg1.toString().replace(".", ""));
                arg2 = Number(arg2.toString().replace(".", "")) * cm;
            } else {
                arg1 = Number(arg1.toString().replace(".", "")) * cm;
                arg2 = Number(arg2.toString().replace(".", ""));
            }
        } else {
            arg1 = Number(arg1.toString().replace(".", ""));
            arg2 = Number(arg2.toString().replace(".", ""));
        }
        return (arg1 + arg2) / m;
    }

    /**
     * 减法函数，用来得到精确的减法结果
     * 说明：javascript的减法结果会有误差，在两个浮点数相减的时候会比较明显。这个函数返回较为精确的减法结果。
     * 调用：accSub(arg1,arg2)
     * 返回值：arg1加上arg2的精确结果
     * @param arg1
     * @param arg2
     * @returns {string}
     */
    function accSub(arg1, arg2) {
        var r1, r2, m, n;
        try {
            r1 = arg1.toString().split(".")[1].length;
        }
        catch (e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        }
        catch (e) {
            r2 = 0;
        }
        m = Math.pow(10, Math.max(r1, r2)); //last modify by deeka //动态控制精度长度
        n = (r1 >= r2) ? r1 : r2;
        return ((arg1 * m - arg2 * m) / m).toFixed(n);
    }

    function deductAll(lotIds, deductionValue) {
        deductionValue = Number(deductionValue)
        lotIds.forEach((item, i) => {
            //总的金额
            let transFeeCounts = Number($("#" + item + "_transFeeCount2").val());
            //已扣款的金额
            let oldFee = $("#" + item + "_kk_feeCnt").val();

            if (deductionValue > transFeeCounts) {

                $("#" + item + "_kk_sapn").text(parseFloat(oldFee) + parseFloat(transFeeCounts));

                $("#" + item + "_kk_input").val(item+"-"+transFeeCounts);

                calculate_ydzpk_ydzpk_pk()
                deductionValue = deductionValue - transFeeCounts;
            }else {
                $("#" + item + "_kk_sapn").text(parseFloat(oldFee) + parseFloat(deductionValue));
                $("#" + item + "_kk_input").val(item+"-"+deductionValue);

                calculate_ydzpk_ydzpk_pk()
                return;
            }
        })
    }

    function deduct(obj) {
        var no = $(obj).data("lotno");
        //可扣总的金额
        let transFeeCounts = $("#" + no + "_transFeeCount2").val();
        //已扣款的金额
        let oldFee = $("#" + no + "_kk_feeCnt").val();

        layer.open({
            type: 1,
            area: ['40%', '25'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "应付扣减",
            content: $("#scanCodeHtml_yf").html(),
            btn: ['全扣', '确定','取消'],
            shadeClose: true,            // 弹层外区域关闭
            btn1: function (index, layero) {
                let isProfitIncluded=$("#isProfitIncluded").val();

                $("#" + no + "_kk_sapn").text(parseFloat(oldFee) + parseFloat(transFeeCounts));
                $("#" + no + "_kk_input").val(no + "-" + transFeeCounts + "-" + isProfitIncluded);

                calculate_ydzpk_ydzpk_pk()
                layer.close(index);
            },
            btn2: function (index, layero) {
                let adjustAmount=$("#adjustAmount_yf").val();
                let isProfitIncluded=$("#isProfitIncluded").val();

                if (adjustAmount==""||adjustAmount==0||adjustAmount==null||adjustAmount==undefined) {
                    $.modal.msgError("金额不能为空为零！");
                    return false;
                }
                if( Number(transFeeCounts) < Number(adjustAmount)){
                    $.modal.msgError("扣款金额不能超过付款金额！");
                    return false;
                }
                $("#" + no + "_kk_sapn").text(parseFloat(oldFee) + parseFloat(adjustAmount));
                $("#" + no + "_kk_input").val(no + "-" + adjustAmount + "-" + isProfitIncluded);

                calculate_ydzpk_ydzpk_pk()
                layer.close(index);
            }
        });
    }
    //用于封装传入后台的参数
    var lotIdDedTransFeeList = []

    function deductDed(obj) {
        var no = $(obj).data("lotno");
        let oldFee = $("#" + no + "_pk_feeCnt").val();

        layer.open({
            type: 1,
            area: ['40%', '30%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "赔款",
            content: $("#dedHtml").html(),
            btn: ['确定'],
            shadeClose: true,            // 弹层外区域关闭
            btn1: function (index, layero) {
                let dedAmount=$("#dedAmount").val();
                if (dedAmount==""||dedAmount==0||dedAmount==null||dedAmount==undefined) {
                    $.modal.msgError("请输入大于0的金额。");
                    return false;
                }
                let memo=$("#memo").val();

                let lotIdDedTransFee = {
                    lotId: no,
                    fee: dedAmount,
                    memo: memo
                }
                var existingItem = lotIdDedTransFeeList.find(item => item.lotId == no);

                if (existingItem) {
                    // 如果存在相同的 lotId，则更新 fee 属性
                    existingItem.fee = dedAmount;
                } else {
                    // 如果不存在相同的 lotId，则插入新对象
                    lotIdDedTransFeeList.push(lotIdDedTransFee);
                }

                console.log(lotIdDedTransFeeList)

                dedAmount = Number(dedAmount) + Number(oldFee)
                //展示
                $("#" + no + "_pk_sapn").text(dedAmount);

                // $("#" + no + "_pk_feeCnt").val(dedAmount);
                calculate_ydzpk_ydzpk_pk()
                layer.close(index);
            },
        });
    }


    // 切换扣减模式的界面
    function toggleDeductMode() {
        let adjustType = $("#adjustType").val();
        if (adjustType === 'deduct') {
            // 扣减模式：缩短输入框，显示全扣按钮
            $("#amountInputContainer").removeClass("col-xs-9").addClass("col-xs-6");
            $("#fullDeductContainer").show();
        } else {
            // 新增模式：恢复输入框长度，隐藏全扣按钮
            $("#amountInputContainer").removeClass("col-xs-6").addClass("col-xs-9");
            $("#fullDeductContainer").hide();
        }
    }

    function compensation(obj,invoiceId,transFeeCounts){
        // 获取原始基础值
        let baseFee = parseFloat($("#" + invoiceId + "_base_feeCnt").val()) || 0;

        let layerIndex = layer.open({
            type: 1,
            area: ['40%', '25%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "应收增减",
            content: $("#scanCodeHtml").html(),
            btn: ['确定','取消'],
            shadeClose: true,            // 弹层外区域关闭
            success: function(layero, index) {
                // 初始化界面状态
                toggleDeductMode();

                // 绑定全扣按钮事件
                $("#fullDeductBtn").click(function() {
                    // let adjustType = $("#adjustType").val();
                    // let isProfitIncluded = $("#isProfitIncluded").val() || 0;
                    //
                    // // 计算新值（在原始基础值上增减）
                    // let newValue = adjustType === 'add' ? baseFee + parseFloat(transFeeCounts) : baseFee - parseFloat(transFeeCounts);
                    // let displayText = newValue >= 0 ? '增' + Math.abs(newValue).toFixed(2) : '减' + Math.abs(newValue).toFixed(2);
                    //
                    // $(obj).parent().parent().prev().find("span").text(displayText);
                    // $(obj).parent().parent().prev().find("input").val(invoiceId+"-"+transFeeCounts+"-"+adjustType+"-"+isProfitIncluded);
                    // calculate_thdzpk()
                    // layer.close(index);
                    $("#adjustAmount").val(transFeeCounts);

                });
            },
            btn1: function (index, layero) {
                let adjustAmount=$("#adjustAmount").val();
                if (adjustAmount==""||adjustAmount==0||adjustAmount==null||adjustAmount==undefined) {
                    $.modal.msgError("金额不能为空为零！");
                    return false;
                }
                let adjustType = $("#adjustType").val();
                let isProfitIncluded = $("#isProfitIncluded").val() || 0;

                // 计算新值（在原始基础值上增减）
                let newValue = adjustType === 'add' ? baseFee + parseFloat(adjustAmount) : baseFee - parseFloat(adjustAmount);
                let displayText = newValue >= 0 ? '增' + Math.abs(newValue).toFixed(2) : '减' + Math.abs(newValue).toFixed(2);

                $(obj).parent().parent().prev().find("span").text(displayText);
                $(obj).parent().parent().prev().find('input[name="invoiceIds"]').val(invoiceId+"-"+adjustAmount+"-"+adjustType+"-"+isProfitIncluded);
                calculate_thdzpk()
                layer.close(index);
            }
        });

    }

    function invoiceIds(obj,tobj,status) {
        let invoiceIds=0;
        $("input[name='"+obj+"']").each(function() {
            if(status==1){
                let num= Math.abs($(this).prev().html());
                invoiceIds+= Number( num )
            }else{
                let num= Math.abs($(this).val());
                invoiceIds+= Number( num )
            }
        })
        $(tobj).html( invoiceIds.toFixed(2) )
    }


    function register(){
        var entrustExpId = $("#entrustExpId").val();
        var url = prefix + "/exceptionTotal/applyPay?entrustExpId=" + entrustExpId+"&type=0";
        $.modal.open("新增异常费用", url,600,480);
    }

    function registerOther(){
        var entrustExpId = $("#entrustExpId").val();
        var url = prefix + "/exceptionTotal/applyPay?entrustExpId=" + entrustExpId+"&type=1";
        $.modal.open("新增赔款", url,600,480);
    }


    function apply(id,type){
        if(type == 0){
            var url = prefix + "/exceptionTotal/applyPayToFinance?id=" + id;
            $.modal.open("申请付款", url,650,520);
        }else if(type == 1){
            var url = prefix + "/exceptionTotal/applyPayToFinanceOther?id=" + id;
            $.modal.open("申请收款", url,650,520);
        }
    }

    function remove(id){
        layer.confirm("确认删除？",{
            btn:["确认","取消"]
        },function (index, layero) {
            $.operate.post(prefix + "/exceptionTotal/removePayApply?id="+id,null,function (){
                $('#bootstrap-table1').bootstrapTable('refresh');
                $('#bootstrap-table').bootstrapTable('refresh');
            });
        },function (index) {

        });
    }

    /**
     * 计算应收增减合计
     */
    function calculate_thdzpk() {
        let totalAmount = 0;

        // 遍历所有应收增减的数据
        $("input[name='invoiceIds']").each(function() {
            let value = $(this).val();
            if (value && value !== '0') {
                let parts = value.split('-');
                if (parts.length >= 3) {
                    let invoiceId = parts[0];
                    let amount = parseFloat(parts[1]) || 0;
                    let type = parts[2]; // 'add' or 'deduct'

                    // 获取原始基础值
                    let baseFee = parseFloat($("#" + invoiceId + "_base_feeCnt").val()) || 0;

                    // 计算实际值：原始基础值 + 操作金额
                    let actualValue = type === 'add' ? baseFee + amount : baseFee - amount;
                    totalAmount += actualValue;
                } else {
                    // 兼容旧格式
                    let amount = parseFloat(parts[1]) || 0;
                    totalAmount -= amount;
                }
            }
        })

        totalAmount += Number(abnormalFee || 0);

        // 显示时加上增减符号
        let displayText = totalAmount >= 0 ? '增' + Math.abs(totalAmount).toFixed(2) : '减' + Math.abs(totalAmount).toFixed(2);
        $("#thdzpk").html(displayText);

    }
    /**
     * 计算应收总金额
     */
    function calculate_thdzje() {
        let totalSum = 0;

        // 使用选择器筛选后缀为 "_all_feeCnt" 的 input 元素
        $("input[id$='_all_receiveFeeCnt']").each(function() {
            // 直接获取值并累加到总和
            totalSum += parseFloat($(this).val());
        });

        // 更新总和的显示
        $("#thdzje").text(totalSum);
    }

    /**
     * 计算应付总金额
     */
    function calculate_ydzje() {
        let totalSum = 0;

        // 使用选择器筛选后缀为 "_all_feeCnt" 的 input 元素
        $("input[id$='_all_feeCnt']").each(function() {
            // 直接获取值并累加到总和
            totalSum += parseFloat($(this).val());
        });

        // 更新总和的显示
        $("#ydzje").text(totalSum);
    }

    function calculate_ydzpk_ydzpk_pk() {
        let ydzpk = 0;
        $("span[id$='_kk_sapn']").each(function() {
            let value = parseFloat($(this).html());
            if (!isNaN(value)) {
                ydzpk += value;
            }
        });

        let ydzpk_pk = 0;
        $("span[id$='_pk_sapn']").each(function() {
            let value = parseFloat($(this).html());
            if (!isNaN(value)) {
                ydzpk_pk += value;
            }
        });

        // 使用 isFinite() 检查是否为有限数
        if (isFinite(ydzpk)) {
            $('#ydzpk').html(ydzpk.toFixed(2));
        } else {
            $('#ydzpk').html('0'); // 设置为 0 或其他默认值
        }

        if (isFinite(ydzpk_pk)) {
            $('#ydzpk_pk').html(ydzpk_pk.toFixed(2));
        } else {
            $('#ydzpk_pk').html('0'); // 设置为 0 或其他默认值
        }
    }

    function remain() {
        let expId=[[${entrustExpId}]];
        var url = prefix + "/residualValueTracking/"+expId;
        $.modal.open("残值跟踪", url,'1000');
    }

    function getOtherFeeList() {
        let list=entrustExpLossRecords;
        let html=`<div class='ontooltip'><div class='panel-body tooltipBody'><div class='padt5'>`;

            if(list){
                html+=`<table class='custom-tab tab table'> <thead style='background: #f4f6f7;'><tr>
                        <th>操作人</th>
                        <th>修改差额</th>
                        <th>备注</th>
                        <th>时间</th>
                    </tr></thead><tbody> `;
                        list.forEach(res=>{

                            html+=`<tr>
                                <td>`+res.regUserName+`</td>
                                <td>`+res.lossAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</td>
                                 <td>`+res.lossMemo+`</td>
                                <td>`+res.regDate.substring(0,10)+`</td>
                            </tr>`
                        })


                html+=`</tbody></table>`;

            }else{
                html+=`暂无数据`;
            }
            html+=`</div></div></div>`;

            return html;

    }

    function print(id){
        let iframe = document.getElementById("print-frame");
        if (!iframe) {
            iframe = document.createElement('IFRAME');
            iframe.id = "print-frame"
            document.body.appendChild(iframe);
            iframe.setAttribute('style', 'display:none;');
        }
        let entrustExpId = $("#entrustExpId").val();
        iframe.src = ctx + "trace/exceptionTotal/exceptionPrint?id="+id;
        iframe.onload = function () { //解决图片显示不了的问题
            iframe.contentWindow.focus();
            iframe.contentWindow.print();
        };
    }
</script>
</body>

</html>