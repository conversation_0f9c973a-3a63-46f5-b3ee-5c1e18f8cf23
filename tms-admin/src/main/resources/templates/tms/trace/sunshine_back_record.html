<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细申请审核')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 40px);
        padding-top: 0;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
        min-width: 16px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row ">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row no-gutter">
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="invoiceVbillno" class="form-control" type="text" placeholder="发货单号"
                                       maxlength="30"  aria-required="true" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="custOrderNo" class="form-control" type="text" placeholder="客户单号"
                                       maxlength="30"  aria-required="true" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1 col-sm-1">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="type" class="form-control">
                                    <option value="">--类型--</option>
                                    <option value="7">确认</option>
                                    <option value="2">提货</option>
                                    <option value="0">到货</option>
                                    <option value="1">回单</option>
                                    <option value="3">附加费</option>
                                    <option value="5">派车</option>
                                    <option value="6">车辆行驶证</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1 col-sm-1">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="successFlag" class="form-control">
                                    <option value="">--成功标记--</option>
                                    <option value="0">成功</option>
                                    <option value="1">失败</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-6">
                        <div class="form-group">
                            <!--                            <label class="col-sm-2">创建时间：</label>-->
                            <div class="col-sm-12">
                                <input type="text" placeholder="推送开始时间" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  name="params[startDate]">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" placeholder="推送结束时间" style="width: 45%; float: left;" class="form-control"
                                       id="endtDate"  name="params[endtDate]">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="backMemo" class="form-control" type="text" placeholder="推送结果"
                                     aria-required="true" >
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            <a class="btn btn-info btn-rounded btn-sm" onclick="$.table.exportExcel()"><i class="fa fa-download"></i>&nbsp;导出</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="carDriver()">
                <i class="fa fa-plus"></i> 派车
            </a>
            <a class="btn btn-primary" onclick="confirm()">
                <i class="fa fa-plus">确认</i>
            </a>
            <a class="btn btn-primary" onclick="pick()">
                <i class="fa fa-plus"></i> 推送提货
            </a>
           <!-- <a class="btn btn-primary" onclick="pickAll()">
                <i class="fa fa-plus"></i> 推送接收确认车辆司机提货
            </a>-->
            <a class="btn btn-primary" onclick="arrival()">
                <i class="fa fa-plus"></i> 推送到货
            </a>
            <a class="btn btn-primary" onclick="receipt()">
                <i class="fa fa-plus"></i> 推送回单
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "trace";

    //开票金额合计
    var ysyf = 0;
    var yszxf = 0;
    var yfyf = 0;
    var yfzxf = 0;
    var yfk = 0;
    var yfye = 0;

    $(function () {

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });

        });
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });
        var options = {
            url: prefix + "/sunshineBackRecordList",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/sunshineBackRecordExport",
            showToggle: false,
            showColumns: true,
            clickToSelect:true,
            height: 560,
            showFooter:false,
            uniqueId: "id",
            modalName: "阳光操作记录",
            columns: [
                {
                    checkbox: true,
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        if(!row.invoiceVbillno.startsWith('SP')){
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="指定时间到货"  onclick="arrivalTime(\''+row.entrustVbillno+'\')"><i class="fa fa-truck" style="font-size: 15px;"></i></a>');
                        }

                        return actions.join('');
                    }
                },

                {
                    title: '发货单号',
                    align: 'left',
                    field: 'invoiceVbillno',

                },
                {
                    title: '客户单号',
                    align: 'left',
                    field: 'custOrderNo',
                },
                {
                    field: 'type',
                    title: '类型',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch (value) {
                            case 0:
                                return '<span class="label label-info">到货</label>';
                            case 1:
                                return '<span class="label label-success">回单</label>';
                            case 2:
                                return '<span class="label label-primary">提货</label>';
                            case 3:
                                return '<span class="label label-warning">附加费</label>';
                            case 5:
                                return '<span class="label label-warning">派车</label>';
                            case 6:
                                return '<span class="label label-default">车辆行驶证</label>';
                            case 7:
                                return '<span class="label label-default">确认</label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    field: 'successFlag',
                    title: '成功标记',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch (value) {
                            case 0:
                                return '<span class="label label-primary">成功</label>';
                            case 1:
                                return '<span class="label label-danger">失败</label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '推送结果',
                    align: 'left',
                    field: 'backMemo',
                },

                {
                    title: '推送时间',
                    align: 'left',
                    field: 'regDate'
                }

            ]
        };

        $.table.init(options);
    });

    //审核
    function payOff() {
        var status = $.table.selectColumns("status").join();
        if(status != 0){
            $.modal.alertWarning("请选择新建的申请进行核销");
            return;
        }
        var id = $.table.selectColumns("id").join();
        var url = prefix + "/payOff?id="+id;
        $.modal.open('承运商预付核销',url,500,700);
    }

    function arrival(){
        var entrustVbillno = $.table.selectColumns("entrustVbillno");
        for(var i=0;i<entrustVbillno.length;i++){
            if(entrustVbillno[i].startsWith('SP')){
                $.modal.alertWarning("请选择一条TMS记录");
                return;
            }
        }
        if(entrustVbillno.length == 0){
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        let flag = true;
        var successFlag = $.table.selectColumns("successFlag");
        successFlag.forEach(function (item){
            if(item == 0){
                flag = false;
            }
        });
        if(!flag){
            $.modal.alertWarning("请选择失败记录");
            return;
        }
        let data = {"entrustVbillnos":entrustVbillno.join(",")};
        $.operate.post(prefix + "/sunshineArrival", data);

    }

    function arrivalTime(entrustVbillno){
        var url = prefix + "/sunshineArrivalTime?entrustVbillno="+entrustVbillno;
        $.modal.open('推送到货',url,500,500);
    }


    function pick(){
        var entrustVbillno = $.table.selectColumns("entrustVbillno");
        for(var i=0;i<entrustVbillno.length;i++){
            if(entrustVbillno[i].startsWith('SP')){
                $.modal.alertWarning("请选择一条TMS记录");
                return;
            }
        }
        if(entrustVbillno.length == 0){
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        let flag = true;
        var successFlag = $.table.selectColumns("successFlag");
        successFlag.forEach(function (item){
            if(item == 0){
                flag = false;
            }
        });
        if(!flag){
            $.modal.alertWarning("请选择失败记录");
            return;
        }
        let data = {"entrustVbillnos":entrustVbillno.join(",")};
        $.operate.post(prefix + "/sunshinePick", data);

    }

    function carDriver(){
        var entrustVbillno = $.table.selectColumns("entrustVbillno");
        for(var i=0;i<entrustVbillno.length;i++){
            if(entrustVbillno[i].startsWith('SP')){
                $.modal.alertWarning("请选择一条TMS记录");
                return;
            }
        }
        if(entrustVbillno.length == 0){
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        let flag = true;
        var successFlag = $.table.selectColumns("successFlag");
        successFlag.forEach(function (item){
            if(item == 0){
                flag = false;
            }
        });
        if(!flag){
            $.modal.alertWarning("请选择失败记录");
            return;
        }
        let data = {"entrustVbillnos":entrustVbillno.join(",")};
        $.operate.post(prefix + "/sunshineCarDriver", data);

    }

    function confirm(){
        var entrustVbillno = $.table.selectColumns("entrustVbillno");
        if(entrustVbillno.startsWith('SP')){
            $.modal.alertWarning("请选择一条TMS记录");
            return;
        }
        if(entrustVbillno.length == 0){
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        let flag = true;
        var successFlag = $.table.selectColumns("successFlag");
        successFlag.forEach(function (item){
            if(item == 0){
                flag = false;
            }
        });
        if(!flag){
            $.modal.alertWarning("请选择失败记录");
            return;
        }
        let data = {"entrustVbillnos":entrustVbillno.join(",")};
        $.operate.post(prefix + "/sunshineConfirm", data);

    }





    function pickAll(){
        var entrustVbillno = $.table.selectColumns("entrustVbillno");
        for(var i=0;i<entrustVbillno.length;i++){
            if(entrustVbillno[i].startsWith('SP')){
                $.modal.alertWarning("请选择一条TMS记录");
                return;
            }
        }
        if(entrustVbillno.length == 0){
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        let flag = true;
        var successFlag = $.table.selectColumns("successFlag");
        successFlag.forEach(function (item){
            if(item == 0){
                flag = false;
            }
        });
        if(!flag){
            $.modal.alertWarning("请选择失败记录");
            return;
        }
        let data = {"entrustVbillnos":entrustVbillno.join(",")};
        $.operate.post(prefix + "/sunshinePickAll", data);

    }

    function receipt(){
        var entrustVbillno = $.table.selectColumns("entrustVbillno");
        for(var i=0;i<entrustVbillno.length;i++){
            if(entrustVbillno[i].startsWith('SP')){
                $.modal.alertWarning("请选择一条TMS记录");
                return;
            }
        }
        if(entrustVbillno.length == 0){
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        let flag = true;
        var successFlag = $.table.selectColumns("successFlag");
        successFlag.forEach(function (item){
            if(item == 0){
                flag = false;
            }
        });
        if(!flag){
            $.modal.alertWarning("请选择失败记录");
            return;
        }
        let data = {"entrustVbillnos":entrustVbillno.join(",")};
        $.operate.post(prefix + "/sunshineReceipt", data);

    }

    /**
     * 重置
     */
    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        $.table.search('role-form', data);
    }




</script>


</body>
</html>