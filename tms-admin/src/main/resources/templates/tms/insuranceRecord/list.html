<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('指导价')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/extensions/bootstrap3-editable/css/bootstrap-editable.css}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.css}" rel="stylesheet"/>
<!--    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/extensions/sticky-header/bootstrap-table-sticky-header.min.css}" rel="stylesheet"/>-->

</head>
<style>
    .custom-cell-background-red {
        background-color: rgb(255 0 0 / 43%) !important; /* 自定义的背景颜色 */
    }
    .custom-cell-background-red a {
        color: #FFFFFF;
    }

    .custom-cell-background-green {
        background-color: #8DBF8B !important; /* 自定义的背景颜色 */
    }
    .custom-cell-background-green a {
        color: #FFFFFF;
    }

     /* 表格滚动条 */
    .bootstrap-table {
        overflow-x: auto;
    }
    .bootstrap-table .fixed-table-container,
    .bootstrap-table .fixed-table-body {
        overflow: visible !important; /* 强制覆盖内部滚动 */
    }
    .bootstrap-table .table {
        min-width: 1600px; /* 设置表格最小宽度，可以根据实际需求调整 */
        width: 100%;
    }
    /* 彻底修复表头文字重叠问题 */
    .bootstrap-table .fixed-table-container .table thead th {
        position: relative;
        text-shadow: none !important;
        font-weight: 500;
        color: #333;
        background-color: #f5f7fa;
    }

    .bootstrap-table .fixed-table-container .table thead th .th-inner {
        padding: 8px;
        line-height: 1.5;
        vertical-align: middle;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        box-sizing: border-box;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
    }

    /* 防止表头内容重叠 */
    .bootstrap-table .fixed-table-container .table thead th .sortable {
        background-image: none;
        position: relative;
    }

    /* 确保表头只有一层文字 */
    .bootstrap-table .fixed-table-container .table thead th:before,
    .bootstrap-table .fixed-table-container .table thead th:after {
        content: none !important;
    }

       /* 添加工具栏与表格之间的间距 */
    #toolbar {
        margin-bottom: 10px;
    }

    /* 美化新添按钮 */
    #toolbar .btn-info {
        margin-right: 5px;
        padding: 5px 15px;
        transition: all 0.3s;
    }

    #toolbar .btn-info:hover {
        background-color: #17a2b8;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    .lf{
        margin-left: 10px;
        background-color: #ffffff;
        border-radius: 3px;
        cursor:pointer;
    }

    .label-success{
        color: #1ab394;
        background-color: transparent;
        border: 1px solid #1ab394;
    }
    .label-primary{
        color: #1c84c6;
        background-color: transparent;
        border: 1px solid #1c84c6;
    }
    .label-warning{
        color: #f8ac59;
        background-color: transparent;
        border: 1px solid #f8ac59;
    }

    .customer-stats {
        font-size: 16px;
    }

    .customer-stats span {
        margin-left: 10px;
        font-weight: 550;
    }

    .customer-stats span:nth-child(2) {
        margin-left: 0px;
        font-size: 16px;
        font-weight: 450;
        color: #FF6C00;
    }
    .customer-stats span:nth-child(3) {
        margin-left: 0px;
        font-size: 16px;
        font-weight: 450;
    }

    .customer-stats span:nth-child(4) {
        margin-left: 0px;
        font-size: 16px;
        font-weight: 450;
        color: #0d62bb;
    }

    .table-light {
        background-color: #f8f9fa; /* 设置背景色为浅灰色 */
    }

    .progress-wrapper {
        position: relative;
        width: 100%;
        height: 20px;
        background-color: #f5f5f5;
        border-radius: 4px;
        overflow: hidden;
    }

    .progress {
        position: relative;
        width: 100%;
        height: 100%;
    }

    .progress-bar {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        background-color: #4caf50;
        transition: width 0.3s ease;
    }

    .progress-text {
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        line-height: 20px;
        padding: 0 10px;
        font-size: 12px;
    }

    .tagged-div {
        position: relative; /* 设置为相对定位，使内嵌元素定位相对于此元素 */
    }

    .tag {
        position: absolute; /* 设置为绝对定位，使其相对于包含它的 .tagged-div 定位 */
        top: -14px; /* 距离顶部为0 */
        left: -5px; /* 距离左侧为0 */
        background-color: #d2e7f9; /* 标记的背景颜色 */
        padding: 1px 3px; /* 内边距 */
        border-radius: 6px; /* 边框圆角 */
        font-size: x-small; /* 字体大小 */
    }

    .sticky-header-container {
        pointer-events: none;
    }
    #buttons-toolbar-container {
        clear: both;
        display: block;
        width: 100%;
    }
    
    #toolbar {
        margin-bottom: 10px;
        display: inline-block;
    }
    
    .select-table {
        clear: both;
    }
    
    /* 确保表格在工具栏下方正确显示 */
    .bootstrap-table {
        clear: both;
        margin-top: 10px;
    }

      /* 防止表格内容换行并添加原生tooltip */
      .bootstrap-table .table td {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
    }
    
    /* 使用CSS原生tooltip */
    .bootstrap-table .table td[title]:hover::after {
        content: attr(title);
        position: absolute;
        left: 0;
        top: 100%;
        z-index: 1000;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 5px 10px;
        white-space: normal;
        max-width: 300px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        word-break: break-all;
    }
</style>
<body class="gray-bg">
<div class="col-sm-12 search-collapse">
    <form id="role-form" class="form-horizontal" onkeydown="if(event.keyCode==13){searchPre();return false;}">
        <div class="row no-gutter">
            <div class="col-md-2 col-sm-2">
                <div class="form-group flex">
                    <div class="col-sm-12">
                        <input name="invoiceNo" id="invoiceNo" placeholder="发货单号" class="form-control" type="text" maxlength="20" autocomplete="off">                    </div>
                </div>
            </div>
            <div class="col-md-2 col-sm-2">
                <div class="form-group flex">
                    <div class="col-sm-12">
                        <input name="claimNumber" id="claimNumber" placeholder="保险报案号" class="form-control" type="text" maxlength="20" autocomplete="off">                    </div>
                </div>
            </div>



            <div class="col-md-3 col-sm-3">
                <div class="form-group" style="text-align: left;">
                    <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                </div>
            </div>

        </div>

    </form>
</div>

<div class="col-sm-12 select-table  ">
    <div class="btn-group-sm" id="toolbar" role="group">
     <!--   <a class="btn btn-primary" shiro:hasAnyPermissions="tms:insuranceRecord:list"
           onclick="add()">
            <i class="fa fa-plus"></i> 新添
        </a>-->

  <!--      <a class="btn btn-danger multiple disabled" shiro:hasAnyPermissions="tms:insuranceRecord:list"
           onclick="deleteRecord()">
            <i class="fa fa-remove"></i> 删除
        </a>-->
    </div>
   <!-- <div id="buttons-toolbar-container" style="margin-bottom: 10px;">
        <span id="buttons-toolbar"></span>
    </div>
-->
    <table id="bootstrap-table"
           data-buttons-toolbar="#buttons-toolbar"
           class="table table-striped table-responsive table-bordered table-hover" >
    </table>
</div>


<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/export/tableExport.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/bootstrap3-editable/js/bootstrap-editable.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/editable/bootstrap-table-editable.min.js}"></script>
<!--<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/sticky-header/bootstrap-table-sticky-header.min.js}"></script>-->
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/export/bootstrap-table-export.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/locale/bootstrap-table-zh-CN.min.js}"></script>


<script th:inline="javascript">
    var insurancePolicyId = [[${insurancePolicyId}]]

    $(function () {
        let options = initOptions();
        $.table.init(options);

        // 在表格初始化时一次性添加title属性
        // $('#bootstrap-table').on('post-body.bs.table', function() {
        //     // 使用requestAnimationFrame延迟执行，减少性能影响
        //     requestAnimationFrame(function() {
        //         $('#bootstrap-table tbody tr td').each(function() {
        //             var $cell = $(this);
        //             var cellText = $cell.text().trim();
        //             if (cellText) {
        //                 $cell.attr('title', cellText);
        //             }
        //         });
        //     });
        // });

    });

    function initOptions() {
        return {
            url: ctx + `insuranceRecord/list?insurancePolicyId=${insurancePolicyId}`,
            uniqueId: "id",
            showToggle:false,
            showColumns:false,
            // showSearch:true,
            pagination:false,
            showRefresh:true,
            modalName: "保险记账",
            height: 560,
            clickToSelect: true,
            // stickyHeader: true,  // 启用固定表头功能
            // stickyHeaderOffsetY: 0,  // 可选，调整固定表头距离顶部的距离
            columns: [
                [
        /*            {
                        checkbox: true,
                        align: 'center',
                        valign: 'middle',
                        rowspan:2,
                    },
                    {
                        title: '操作',
                        align: 'center',
                        valign: 'middle',
                        width: 20,
                        switchable:false,
                        rowspan:2,
                        formatter: function(value, row, index) {
                            var actions = [];
                            if ([[${@permission.hasPermi('tms:insuranceRecord:list')}]] != "hidden") {
                                actions.push(`<a class="btn btn-xs" href="javascript:void(0)" onclick="edit('${row.id}')" title="修改">
                                                    <i class="fa fa-edit" style="font-size: 15px;"></i></a>`);
                            }
                            return actions.join('');
                        }
                    },*/
                    {
                        title: '日期',
                        valign: 'middle',
                        align: 'center',
                        field : 'insuranceDate',
                        rowspan:2
                    },
                    {
                        title: '客户名称',
                        align: 'center',
                        valign: 'middle',
                        field: 'custAbbr',
                        rowspan:2,
                        cellStyle: function(value, row, index) {
                            return {
                                classes: 'text-nowrap',
                                css: {'max-width': '150px', 'overflow': 'hidden', 'text-overflow': 'ellipsis'}
                            };
                        },
                        formatter: function(value, row, index) {
                            if (value) {
                                return '<span title="' + value + '">' + value + '</span>';
                            }
                            return '';
                        }
                    },
                    {
                        title: '运营组',
                        align: 'center',
                        valign: 'middle',
                        field: 'salesDept',
                        rowspan:2,
                        cellStyle: function(value, row, index) {
                            return {
                                classes: 'text-nowrap',
                                css: {'max-width': '150px', 'overflow': 'hidden', 'text-overflow': 'ellipsis'}
                            };
                        },
                        formatter: function status(value, row) {
                            if (value) {
                                return '<span title="' + value + '">' + value + '</span>';
                            }
                            return '';
                        }
                    },
                    {
                        title: '运营部',
                        align: 'center',
                        valign: 'middle',
                        field: 'custSalesName',
                        rowspan:2,
                        cellStyle: function(value, row, index) {
                            return {
                                classes: 'text-nowrap',
                                css: {'max-width': '150px', 'overflow': 'hidden', 'text-overflow': 'ellipsis'}
                            };
                        },
                        formatter: function(value, row, index) {
                            if (value) {
                                return '<span title="' + value + '">' + value + '</span>';
                            }
                            return '';
                        }
                    },
                    {
                        title: '管理部',
                        align: 'center',
                        valign: 'middle',
                        field: 'mgmtDeptName',
                        rowspan:2,
                        cellStyle: function(value, row, index) {
                            return {
                                classes: 'text-nowrap',
                                css: {'max-width': '150px', 'overflow': 'hidden', 'text-overflow': 'ellipsis'}
                            };
                        },
                        formatter: function(value, row, index) {
                            if (value) {
                                return '<span title="' + value + '">' + value + '</span>';
                            }
                            return '';
                        }
                    },
                    {
                        title: '发货单号',
                        align: 'center',
                        valign: 'middle',
                        field : 'invoiceNo',
                        rowspan:2,
                        cellStyle: function(value, row, index) {
                            return {
                                classes: 'text-nowrap',
                                css: {'max-width': '150px', 'overflow': 'hidden', 'text-overflow': 'ellipsis'}
                            };
                        },
                        formatter: function(value, row, index) {
                            if (value) {
                                return '<span title="' + value + '">' + value + '</span>';
                            }
                            return '';
                        }
                    },
                    {
                        title: '保险报案号',
                        align: 'center',
                        valign: 'middle',
                        field : 'claimNumber',
                        rowspan:2
                    },
                    {
                        title: '货损',
                        align: 'right',
                        valign: 'middle',
                        field : 'accidentAmount',
                        rowspan:2
                    },
                    {
                        title: '其他损失',
                        align: 'center',
                        colspan:6
                    },
                    {
                        title: '保险赔付',
                        align: 'right',
                        valign: 'middle',
                        field : 'insuranceCompensationAmount',
                        rowspan:2
                    },
                    {
                        title: '承运商赔付',
                        align: 'right',
                        valign: 'middle',
                        field : 'carrierCompensationAmount',
                        rowspan:2
                    },
                    {
                        title: '其他赔付',
                        align: 'right',
                        valign: 'middle',
                        field : 'otherPayout',
                        rowspan:2
                    },

                    {
                        title: '残值',
                        align: 'right',
                        valign: 'middle',
                        field : 'residualValue',
                        rowspan:2
                    },
                    {
                        title: '公司损益',
                        align: 'right',
                        valign: 'middle',
                        field : 'companyProfitLoss',
                        rowspan:2
                    },
                    // {
                    //     title: '备注',
                    //     align: 'center',
                    //     valign: 'middle',
                    //     field : 'remarks',
                    //     rowspan:2
                    // },

                ],
                [
                    {
                        title: '二次运输费',
                        align: 'right',
                        field: 'secondaryTransportationFee',
                        // formatter: function (value, row, index) {
                        //     return `<div>${sum} / ${targetAmount}</div><div>${rateHtml}</div>`
                        // }
                    },
                    {
                        title: '驳货费',
                        align: 'right',
                        field: 'transshipmentFee',
                    },
                    {
                        title: '律师费',
                        align: 'right',
                        field: 'lawyerFee',
                    },
                    {
                        title: '律师差旅费',
                        align: 'right',
                        field: 'lawyerTravelFee',
                    },
                    {
                        title: '诉讼费',
                        align: 'right',
                        field: 'litigationFee',
                    },
                    {
                        title: '其他',
                        align: 'right',
                        field: 'otherFees',
                    },
                ]
            ],
        };
    }


    function add() {
        layer.open({
            type: 2,
            area: ['80%', '60%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "新添保险记录",
            content: ctx + "insuranceRecord/add?insurancePolicyId=" + insurancePolicyId,
            btn: ['确认', '关闭'],
            // shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function (index) {
                return true;
            }
        });
    }

    function edit(id) {
        layer.open({
            type: 2,
            area: ['80%', '60%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "修改保险记录",
            content: ctx + `insuranceRecord/edit/${id}`,
            btn: ['确认', '关闭'],
            // shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function (index) {
                return true;
            }
        });
    }

    function deleteRecord() {
        var idArr = $.table.selectColumns("id");
        if (idArr.length == 0) {
            $.modal.msgWarning("请至少选择一条数据");
            return;
        }

        $.modal.confirm("确认要删除选中的" + idArr.length + "条数据吗?", function() {
            var url = ctx + "insuranceRecord/remove";
            var data = { "ids": idArr.join(",") };
            $.operate.submit(url, "post", "json", data);
        });
    }

    function searchPre() {
        $.table.search('role-form');
    }
    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }
</script>

</body>
</html>