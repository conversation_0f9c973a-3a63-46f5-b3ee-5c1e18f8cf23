<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <th:block th:include="include :: header('新添保险记录')"/>
    <style>
        .form-content {
            padding: 10px;
        }

        .form-row {
            margin-bottom: 8px;
        }

        .form-item {
            display: flex;
            align-items: center;
            margin-bottom: 0;
            position: relative; /* 为验证消息定位 */
        }

        .form-item .item-label {
            width: 90px;
            text-align: right;
            padding-right: 8px;
            font-size: 13px;
            white-space: nowrap;
            flex-shrink: 0;
            color: #2d2d2d; /* 加深文字颜色 */
            font-weight: 500; /* 增加字重 */
        }

        .form-item .item-content {
            flex: 1;
            position: relative; /* 为验证消息定位 */
        }

        /* 添加鼠标悬停效果 */
        .form-item:hover .item-label {
            color: #1890ff;
        }

        .form-control {
            height: 30px;
            padding: 4px 8px;
            font-size: 13px;
            width: 100%; /* 确保宽度一致 */
            box-sizing: border-box; /* 确保padding不影响宽度 */
        }

        textarea.form-control {
            height: auto;
        }

        .card-header {
            padding: 8px 10px;
            background-color: #f5f7fa;
            margin-bottom: 8px;
            font-size: 13px;
            font-weight: bold;
            border-left: 3px solid #1890ff;
            border-radius: 0;
        }

        .other-fees {
            border: 1px solid #ebeef5;
            border-radius: 4px;
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .other-fees-body {
            padding: 5px 10px;
        }

        .required:before {
            content: "*";
            color: #f56c6c; /* 调整必填星号的颜色为更柔和的红色 */
            margin-right: 4px;
            font-weight: bold;
        }

        .btn-container {
            text-align: center;
            margin-top: 15px;
        }

        .btn-submit {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 6px 15px;
            border-radius: 4px;
            cursor: pointer;
        }

        .btn-submit:hover {
            background-color: #40a9ff;
        }

        .btn-cancel {
            background-color: #f5f5f5;
            color: #606266;
            border: 1px solid #dcdfe6;
            padding: 6px 15px;
            border-radius: 4px;
            margin-left: 10px;
            cursor: pointer;
        }

        .btn-cancel:hover {
            background-color: #e9e9e9;
        }

        /* 自定义验证消息样式 */
        .error-tip {
            position: absolute;
            right: -115px; /* 消息显示在输入框右侧 */
            top: 0;
            color: red;
            font-size: 12px;
            white-space: nowrap;
            background-color: #fff;
            padding: 2px 5px;
            border-radius: 2px;
            z-index: 100;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: none;
        }

        /* 输入框错误状态 */
        .form-control.error {
            border-color: #f56c6c; /* 使用较柔和的红色 */
            box-shadow: 0 0 0 1px rgba(245, 108, 108, 0.2); /* 添加微弱的红色阴影效果 */
            transition: border-color 0.2s ease-in-out;
        }

        .Validform_error, input.error, select.error {
            background-color: #fff;
        }

        /* 移动设备适配 */
        @media (max-width: 768px) {
            .error-tip {
                right: 0;
                top: -20px;
                box-shadow: none;
            }
        }
    </style>
</head>

<body>
<div class="form-content">
    <form id="form" class="layui-form" novalidate="novalidate">
        <input type="hidden" name="insurancePolicyId" th:value="${insurancePolicyId}">
        <!-- 主要信息 -->
        <div class="row form-row">
            <div class="col-sm-6">
                <div class="form-item">
                    <label class="item-label required">异常记录</label>
                    <div class="item-content">
                        <input id="exceptionTitle" name="exceptionTitle" class="form-control" onclick="showEntrustExp()"
                               placeholder="异常记录" type="text" required readonly>
                        <input id="entrustExpId" name="entrustExpId" type="hidden">
                    </div>
                </div>
            </div>
            <div class="col-sm-3">
                <div class="form-item">
                    <label class="item-label required">保险日期</label>
                    <div class="item-content">
                        <input id="insuranceDate" name="insuranceDate" class="form-control"
                               placeholder="保险日期" autocomplete="off" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-sm-3">
                <div class="form-item">
                    <label class="item-label required">保险报案号</label>
                    <div class="item-content">
                        <input id="claimNumber" name="claimNumber" class="form-control"
                               placeholder="保险报案号" autocomplete="off" type="text" required>
                    </div>
                </div>
            </div>
        </div>

<!--        <div class="row form-row">-->

<!--            <div class="col-sm-3">-->
<!--                <div class="form-item">-->
<!--                    <label class="item-label">公司损益</label>-->
<!--                    <div class="item-content">-->
<!--                        <input id="companyProfitLoss" name="companyProfitLoss" class="form-control"-->
<!--                               type="number" placeholder="公司损益" autocomplete="off" step="0.01">-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->

        <div class="other-fees">
            <div class="card-header">赔付及残值 <span style="float: right; font-weight: normal;">合计: <span
                    id="totalFeesDisplayPF" style="color: #1890ff; font-weight: bold;">0.00</span> 元</span></div>
            <div class="other-fees-body">
                <div class="row form-row">
                    <div class="col-sm-3">
                        <div class="form-item">
                            <label class="item-label required">保险赔付</label>
                            <div class="item-content">
                                <input id="insuranceCompensationAmount" name="insuranceCompensationAmount" class="form-control"
                                       type="number" placeholder="保险赔付" autocomplete="off" step="0.01" required>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-item">
                            <label class="item-label">承运商赔付</label>
                            <div class="item-content">
                                <input id="carrierCompensationAmount" name="carrierCompensationAmount" class="form-control"
                                       type="number" placeholder="承运商赔付" autocomplete="off" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-item">
                            <label class="item-label">其他赔付</label>
                            <div class="item-content">
                                <input id="otherPayout" name="otherPayout" class="form-control"
                                       placeholder="其他赔付" autocomplete="off" type="number" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-item">
                            <label class="item-label">残值</label>
                            <div class="item-content">
                                <input id="residualValue" name="residualValue" class="form-control"
                                       type="number" placeholder="残值" autocomplete="off" step="0.01">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 其他损失 -->
        <div class="other-fees">
            <div class="card-header">货损及其他损失 <span style="float: right; font-weight: normal;">合计: <span
                    id="totalFeesDisplaySH" style="color: #1890ff; font-weight: bold;">0.00</span> 元</span></div>
            <div class="other-fees-body">
                <div class="row form-row">
                    <div class="col-sm-3">
                        <div class="form-item">
                            <label class="item-label required">货损</label>
                            <div class="item-content">
                                <input id="accidentAmount" name="accidentAmount" class="form-control"
                                       type="number" placeholder="货损" autocomplete="off" step="0.01" required>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-3">
                        <div class="form-item">
                            <label class="item-label">二次运输费</label>
                            <div class="item-content">
                                <input id="secondaryTransportationFee" name="secondaryTransportationFee"
                                       class="form-control"
                                       type="number" placeholder="二次运输费" autocomplete="off" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-item">
                            <label class="item-label">驳货费</label>
                            <div class="item-content">
                                <input id="transshipmentFee" name="transshipmentFee" class="form-control"
                                       type="number" placeholder="驳货费" autocomplete="off" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-item">
                            <label class="item-label">律师费</label>
                            <div class="item-content">
                                <input id="lawyerFee" name="lawyerFee" class="form-control"
                                       type="number" placeholder="律师费" autocomplete="off" step="0.01">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row form-row">
                    <div class="col-sm-3">
                        <div class="form-item">
                            <label class="item-label">律师差旅费</label>
                            <div class="item-content">
                                <input id="lawyerTravelFee" name="lawyerTravelFee" class="form-control"
                                       type="number" placeholder="律师差旅费" autocomplete="off" step="0.01">
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-3">
                        <div class="form-item">
                            <label class="item-label">诉讼费</label>
                            <div class="item-content">
                                <input id="litigationFee" name="litigationFee" class="form-control"
                                       type="number" placeholder="诉讼费" autocomplete="off" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-item">
                            <label class="item-label">其他费</label>
                            <div class="item-content">
                                <input id="otherFees" name="otherFees" class="form-control"
                                       type="number" placeholder="其他费" autocomplete="off" step="0.01">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- 备注和公司损益 -->
        <div class="row form-row" style="margin-top: 15px;">
            <div class="col-sm-8">
                <div class="form-item">
                    <div class="item-content" style="margin-left: 5px;">
                        <textarea id="remarks" name="remarks" class="form-control"
                                  rows="3" placeholder="请输入备注信息..." style="resize: none;"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="form-item">
                    <div class="item-content" style="display: flex; align-items: center; height: 55px;justify-content: center;">
                        <span style="font-weight: bold; margin-right: 10px;">公司损益：</span>
                        <span id="companyProfitLossDisplay" style="color: #1890ff; font-weight: bold; font-size: 20px;">0.00</span>
                        <span style="margin-left: 5px;"> 元</span>

                        <input id="companyProfitLoss" name="companyProfitLoss" class="form-control"
                               type="hidden" placeholder="公司损益" autocomplete="off">

                    </div>
                </div>
            </div>
        </div>

        <!-- 备注 -->
    <!--    <div class="row form-row">
            <div class="col-sm-12">
                <div class="form-item">
                    <label class="item-label">备注</label>
                    <div class="item-content">
                        <textarea id="remarks" name="remarks" class="form-control"
                                  rows="2" placeholder="备注" style="resize: none;"></textarea>
                    </div>
                </div>
            </div>
        </div>-->


        <!-- 公司损益展示 -->
      <!--  <div class="other-fees" style="margin-top: 15px;">
            <div class="card-header">公司损益</div>
            <div class="other-fees-body">
                <div class="row form-row">
                    <div class="col-sm-12">
                        <div style="text-align: center; font-size: 18px; padding: 10px;">
                            <span style="font-weight: bold;">公司损益: </span>
                            <span id="companyProfitLossDisplay" style="color: #1890ff; font-weight: bold; font-size: 20px;">0.00</span>
                            <span> 元</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>-->


    </form>
</div>

<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    $(function () {
        // 使用layui日期选择器
        layui.use(['form', 'laydate'], function () {
            var form = layui.form;
            var laydate = layui.laydate;

            laydate.render({
                elem: '#insuranceDate',
                type: 'date',
                done: function (value, date) {
                    // 结束时间大于开始时间
                    if (value !== '') {
                        $("#insuranceDate").removeClass('error');
                        $("#insuranceDate").next('.error-tip').hide();
                    }
                }
            });

            form.render();
        });

        // 添加输入事件监听器，在输入时隐藏错误提示
        $("input[required]").on('input', function () {
            if ($(this).val().trim() !== '') {
                $(this).removeClass('error');
                $(this).next('.error-tip').hide();
            }
        });

        $("#insuranceCompensationAmount, #carrierCompensationAmount, #otherPayout, #residualValue, #accidentAmount, " +
            "#secondaryTransportationFee, #transshipmentFee, #lawyerFee, #lawyerTravelFee, #litigationFee, #otherFees")
            .on('input', function () {
                calculateCompanyProfitLoss();
            });

        // 初始计算一次
        calculateCompanyProfitLoss();

    });


    /**
     * 表单验证 - 使用自定义提示
     */
    function validateForm() {
        var isValid = true;

        // 隐藏所有错误提示
        $(".error-tip").hide();
        $(".form-control").removeClass('error');

        // 验证必填字段
        $("input[required]").each(function () {
            if (!$(this).val().trim()) {
                $(this).addClass('error');
                $(this).next('.error-tip').show();
                if (isValid) { // 只滚动到第一个错误
                    // $(this).focus();
                }
                isValid = false;
            }
        });

        if (!isValid) {
            // 使用layer的提示，不影响布局
            layer.msg('请填写必填项', {icon: 2});
        }

        return isValid;
    }

    function showEntrustExp() {
        parent.layer.open({
            type: 2,
            area: ['90%', '90%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "异常统计",
            content: ctx + "trace/exceptionTotal/popUp",
            btn: ['确认', '关闭'],
            // shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                let checked = iframeWin.contentWindow.getChecked();

                if (checked.length > 0) {
                    $("#exceptionTitle").val(checked[0].exceptionTitle);
                    $("#entrustExpId").val(checked[0].entrustExpId);

                    $("#exceptionTitle").removeClass('error');
                    $("#exceptionTitle").next('.error-tip').hide();


                    // 根据选中的entrustExpId获取保险记录和付款信息
                    $.ajax({
                        url: ctx + "entrustexceptionmore/insuranceDetail",
                        type: "get",
                        data: {
                            "entrustExpId": checked[0].entrustExpId
                        },
                        success: function(res) {
                            if (res.code == 0) {
                                var data = res.data;

                                // 填充残值信息（EntrustExpApplyPay的payAmount总和）
                                // if (data.totalPayAmount) {
                                //     $("#residualValue").val(data.totalPayAmount);
                                // }

                                // 填充保险记录信息
                                if (data.tInsurances && data.tInsurances.length > 0) {
                                    // 清空所有相关字段，避免数据混淆
                                    $("#accidentAmount").val("");
                                    $("#secondaryTransportationFee").val("");
                                    $("#transshipmentFee").val("");
                                    $("#lawyerFee").val("");
                                    $("#litigationFee").val("");
                                    $("#otherFees").val("");
                                    $("#insuranceCompensationAmount").val("");
                                    $("#carrierCompensationAmount").val("");
                                    $("#otherPayout").val("");

                                    // 创建一个对象来存储每个类别的合计金额
                                    var categoryTotals = {
                                        "货损": 0,
                                        "二次运输费": 0,
                                        "驳货费": 0,
                                        "律师费": 0,
                                        "律师差旅费": 0,
                                        "诉讼费": 0,
                                        "其他费": 0,
                                        "保险赔付": 0,
                                        "承运商赔付": 0,
                                        "其他赔付": 0,
                                        "残值": 0
                                    };

                                    // 循环处理所有保险记录，累加相同类别的金额
                                    for (var i = 0; i < data.tInsurances.length; i++) {
                                        var insuranceData = data.tInsurances[i];
                                        if (insuranceData.lossItem && insuranceData.lossAmount) {
                                            // 将字符串金额转为数字进行累加
                                            var amount = parseFloat(insuranceData.lossAmount) || 0;
                                            if (categoryTotals.hasOwnProperty(insuranceData.lossItem)) {
                                                categoryTotals[insuranceData.lossItem] += amount;
                                            }
                                        }

                                        // // 如果是第一条记录，填充备注
                                        // if (i === 0 && insuranceData.remark) {
                                        //     $("#remarks").val(insuranceData.remark);
                                        // }
                                    }

                                    // 将合计金额填充到对应的表单字段
                                    if (categoryTotals["货损"] > 0) {
                                        $("#accidentAmount").val(categoryTotals["货损"]);
                                    }
                                    if (categoryTotals["二次运输费"] > 0) {
                                        $("#secondaryTransportationFee").val(categoryTotals["二次运输费"]);
                                    }
                                    if (categoryTotals["驳货费"] > 0) {
                                        $("#transshipmentFee").val(categoryTotals["驳货费"]);
                                    }
                                    if (categoryTotals["律师费"] > 0) {
                                        $("#lawyerFee").val(categoryTotals["律师费"]);
                                    }
                                    if (categoryTotals["律师差旅费"] > 0) {
                                        $("#lawyerTravelFee").val(categoryTotals["律师差旅费"]);
                                    }
                                    if (categoryTotals["诉讼费"] > 0) {
                                        $("#litigationFee").val(categoryTotals["诉讼费"]);
                                    }
                                    if (categoryTotals["其他费"] > 0) {
                                        $("#otherFees").val(categoryTotals["其他费"]);
                                    }
                                    if (categoryTotals["保险赔付"] > 0) {
                                        $("#insuranceCompensationAmount").val(categoryTotals["保险赔付"]);
                                    }
                                    if (categoryTotals["承运商赔付"] > 0) {
                                        $("#carrierCompensationAmount").val(categoryTotals["承运商赔付"]);
                                    }
                                    if (categoryTotals["其他赔付"] > 0) {
                                        $("#otherPayout").val(categoryTotals["其他赔付"]);
                                    }
                                    if (categoryTotals["残值"] > 0) {
                                        $("#residualValue").val(categoryTotals["残值"]);
                                    }
                                }



                                // 重新计算公司损益
                                calculateCompanyProfitLoss();
                            } else {
                                $.modal.alertError(res.msg);
                            }
                        },
                        error: function() {
                            $.modal.alertError("获取异常详情失败");
                        }
                    });
                }
                parent.layer.close(index);
            },
            cancel: function (index) {
                return true;
            }
        });

    }

    /**
     * 计算公司损益和其他损失合计
     */
    function calculateCompanyProfitLoss() {
        // 获取各项金额，如果为空则默认为0
        var insuranceCompensation = parseFloat($("#insuranceCompensationAmount").val()) || 0;       //保险赔付
        var carrierCompensation = parseFloat($("#carrierCompensationAmount").val()) || 0;   //承运商赔付
        var otherPayout = parseFloat($("#otherPayout").val()) || 0;     //其他赔付
        var residualValue = parseFloat($("#residualValue").val()) || 0;     //残值

        //赔付展示
        var totalFeesDisplayPF = insuranceCompensation + carrierCompensation + otherPayout + residualValue;
        $("#totalFeesDisplayPF").text(totalFeesDisplayPF.toFixed(2));

        // 计算其他损失合计 - 使用ID选择器
        var secondaryTransportationFee = parseFloat($("#secondaryTransportationFee").val()) || 0;
        var transshipmentFee = parseFloat($("#transshipmentFee").val()) || 0;
        var lawyerFee = parseFloat($("#lawyerFee").val()) || 0;
        var lawyerTravelFee = parseFloat($("#lawyerTravelFee").val()) || 0;
        var litigationFee = parseFloat($("#litigationFee").val()) || 0;
        var otherFees = parseFloat($("#otherFees").val()) || 0;

        var accidentAmount = parseFloat($("#accidentAmount").val()) || 0;

        var totalFeesDisplaySH = secondaryTransportationFee + transshipmentFee + lawyerFee +
            lawyerTravelFee + litigationFee + otherFees + accidentAmount;

        // 更新其他损失合计显示
        $("#totalFeesDisplaySH").text(totalFeesDisplaySH.toFixed(2));

        // 计算公司损益
        var profitLoss = totalFeesDisplayPF - totalFeesDisplaySH;

        // 更新公司损益显示，保留两位小数
        $("#companyProfitLoss").val(profitLoss.toFixed(2));

        // 更新公司损益显示
        var displayElement = $("#companyProfitLossDisplay");
        displayElement.text(profitLoss.toFixed(2));
        
        // 根据正负值设置不同颜色
        if (profitLoss > 0) {
            displayElement.css("color", "#52c41a"); // 正值显示绿色
        } else if (profitLoss < 0) {
            displayElement.css("color", "#f5222d"); // 负值显示红色
        } else {
            displayElement.css("color", "#1890ff"); // 零值显示蓝色
        }

    }

    /**
     * 提交处理函数
     */
    function submitHandler(index, layero) {
        if (validateForm()) {
            var data = $("#form").serializeArray();
            $.operate.save(ctx + "insuranceRecord/add", data, function (result) {
                if (result.code == web_status.SUCCESS) {
                    layer.close(index);
                    $.modal.msgSuccess(result.msg);
                    // 修改刷新方式，使用父窗口的表格刷新方法
                    if (window.parent.$.table !== undefined) {
                        window.parent.$.table.refresh();
                    }
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        }
    }
</script>
</body>
</html>