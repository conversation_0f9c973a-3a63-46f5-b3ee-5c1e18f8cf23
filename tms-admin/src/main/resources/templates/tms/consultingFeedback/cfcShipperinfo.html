<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('托运人列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <!--同步至省平台-->
                <input name="isexception"  th:value="0" class="form-control" type="hidden"/>
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5" >托运人名称：</label>
                            <div class="col-sm-7">
                                <input name="shippername" id="shippername" class="form-control" type="text"
                                       maxlength="128"  autocomplete="off"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5" >托运人类别：</label>
                            <div class="col-sm-7">
                                <select id="shippertype" name="shippertype" class="form-control valid"
                                        th:with="type=${@dict.getType('shippertype')}" >
                                    <option value=""></option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group"></div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group"></div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
           <!-- <a class="btn btn-primary" onclick="$.operate.addTab()" shiro:hasPermission="tms:cfcShipperinfo:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="tms:cfcShipperinfo:remove">
                <i class="fa fa-remove"></i> 删除
            </a>-->

        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('tms:cfcShipperinfo:edit')}]];
    var prefix = ctx + "tms/consultingFeedback";
    var shippertype = [[${@dict.getType('shippertype')}]];
    var balaCorp = [[${@dict.getType('bala_corp')}]];
    $(function () {
        var options = {
            url: prefix + "/shipperinfoList",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "托运人",
            showToggle:false,
            showColumns:true,
            clickToSelect:true,
            fixedColumns: true,
            rememberSelected: true,
            fixedNumber:0,
            uniqueId: "shipperid",
            columns: [{
                radio: true
            },
                {
                    field: 'shipperid',
                    align: 'left',
                    title: '托运人ID'
                },
                {
                    field: 'platformcode',
                    align: 'left',
                    title: '无车承运平台编码 ',
                    visible: false
                },
                {
                    field: 'shippertype',
                    align: 'left',
                    title: '托运人类别',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(shippertype, value);
                    }
                },
                {
                    field: 'shippername',
                    align: 'left',
                    title: '托运人名称 '
                },
                {
                    field: 'isexception',
                    align: 'left',
                    title: '是否同步至省平台',
                    formatter: function status(value, row, index) {
                        if (value === '0 ') {
                            return '是';
                        }
                        return '否';
                    }
                },

                {
                    field: 'contactname',
                    align: 'left',
                    title: '联系人名称 '
                },
                {
                    field: 'contactmobiletelephonenumber',
                    align: 'left',
                    title: ' 联系人手机号码 '
                },
                {
                    field: 'registeredcapital',
                    align: 'left',
                    title: '注册资金',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    field: 'unifiedsocialcreditldentifier',
                    align: 'left',
                    title: '统一社会信用代码 '
                },
                {
                    field: 'unifiedsocialdatetime',
                    align: 'left',
                    title: '统一社会信用代码注册时间 '
                },
                {
                    field: 'registrationdatetime',
                    align: 'left',
                    title: '注册时间 '
                },
                {
                    field: 'updatetimedatetime',
                    align: 'left',
                    title: '更新时间 '
                },
                {
                    field: 'isblaclklist',
                    align: 'left',
                    title: '是否黑名单',
                    formatter: function (value, row, index) {
                        if (value === '0') {
                            return '否';
                        }
                        return '是';
                    }
                },

                {
                    field: 'contactidcard',
                    align: 'left',
                    title: '联系人身份证号码 '
                },
                {
                    field: 'balaCorp',
                    align: 'left',
                    title: '对应公司',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(balaCorp, value);
                    }
                }]
        };
        $.table.init(options);
    });



    /** 获取选中行*/
    function getChecked() {
        return $.btTable.bootstrapTable('getSelections');
    }


</script>
</body>
</html>