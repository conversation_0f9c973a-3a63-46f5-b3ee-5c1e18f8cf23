<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('平台信息列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">

        <div class="btn-group-sm" id="toolbar" role="group">
        </div>
        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    var prefix = ctx + "tms/consultingFeedback";
    //权限
    var editFlag = [[${@permission.hasPermi('tms:nocarcarrierplatforminfo:edit')}]];
    var detailFlag = [[${@permission.hasPermi('tms:nocarcarrierplatforminfo:detail')}]];

    $(function () {
        var options = {
            url: prefix + "/nocarcarrierplatforminfoList",
            createUrl: prefix + "/add",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:true,
            modalName: "平台信息",
            fixedColumns: true,
            fixedNumber: 0,
            columns: [{
                checkbox: true
            },
                {
                    title: '无车承运平台编码',
                    align: 'left',
                    field: 'platformcode'
                },
                {
                    title: '企业名称',
                    align: 'left',
                    field: 'enterprisename'

                },
                {
                    title: '企业注册地址',
                    align: 'left',
                    field: 'enterpriseregistrationaddress'

                },
                {
                    title: '注册资金(万元)',
                    align: 'right',
                    halign: "center",
                    field: 'registeredcapital'
                },
                {
                    title: '统一社会信用代码',
                    align: 'left',
                    field: 'unifiedsocialcreditldentifier'
                },
                {
                    title: '统一社会信用代码(注册时间)',
                    align: 'left',
                    field: 'unifiedsocialdatetime'
                },
                {
                    title: '经营范围',
                    align: 'left',
                    field: 'businessscope'
                },
                {
                    title: '营业执照期限开始',
                    align: 'left',
                    field: 'businesslicensestartdatetime'
                },
                {
                    title: '营业执照期限结束',
                    align: 'left',
                    field: 'businesslicenseenddatetime'
                },
                {
                    title: '所属辖区',
                    align: 'left',
                    field: 'countrysubdivisioncode'
                },
                {
                    title: '道路运输经营许可证',
                    align: 'left',
                    field: 'permitnumber'
                },
                {
                    title: '法人代表',
                    align: 'left',
                    field: 'legalpersonname'
                },
                {
                    title: '法人电话号码',
                    align: 'left',
                    field: 'legalpersontelephonenumber'
                },
                {
                    title: '联系人名称',
                    align: 'left',
                    field: 'contactname'
                },
                {
                    title: '联系人手机号码',
                    align: 'left',
                    field: 'contactmobiletelephonenumber'
                },
                {
                    title: '传真号码',
                    align: 'left',
                    field: 'faxnumber'
                },
                {
                    title: '互联网+属性',
                    align: 'left',
                    field: 'internetplusproperty'
                },
                {
                    title: '成立时间',
                    align: 'left',
                    field: 'enterprisecreatetime'
                },
                {
                    title: '营业执照注册日期',
                    align: 'left',
                    field: 'registerdate'
                },
                {
                    title: '电信业务经营许可证编号',
                    align: 'left',
                    field: 'ipcnum'
                },
                {
                    title: '三级等保备案编号',
                    align: 'left',
                    field: 'psnrnum'
                },
                {
                    title: '是否为分公司',
                    align: 'left',
                    field: 'psnrnum',
                    formatter: function(value, row, index) {
                       return value == 0 ? '否':'是'
                    }
                },
                {
                    title: '总公司名称',
                    align: 'left',
                    field: 'controllingname'
                },
                {
                    title: '总公司所属辖区代码',
                    align: 'left',
                    field: 'controllingareacode'
                },

                {
                    title: '同步时间',
                    align: 'left',
                    field: 'synchronizationtime'
                },
                {
                    title: '是否同步',
                    align: 'left',
                    field: 'isexception',
                    formatter: function(value, row, index) {
                        return value == 0 ? '成功':'失败'
                    }
                }
            ]
        };

        $.table.init(options);
    });


    /** 获取选中行*/
    function getChecked() {
        return $.btTable.bootstrapTable('getSelections');
    }
</script>
</body>
</html>