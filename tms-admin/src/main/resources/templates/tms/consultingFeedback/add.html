<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('咨询反馈-add')"/>
</head>

<body>
<div class="form-content">
    <form id="form-consultingFeedback-add" class="form-horizontal" novalidate="novalidate">


        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <input name="deptId" type="hidden" id="treeId">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2" style="color: red">标题：</label>
                                    <div class="col-md-11 col-sm-10">
                                        <input name="title" type="text" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2" style="color: red">内容：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="note" maxlength="250" required class="form-control valid" rows="3">
                                            </textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkAmount" style="color: red">咨询对象类别：</label>
                                    <div class="col-sm-8">
                                        <select name="complaintobjectcategories" id="complaintobjectcategories" class="form-control" th:with="type=${@dict.getType('object_level')}"
                                                required th:onclick="unlockComplaintobjectname()">
                                            <option value="">-- 请选择 --</option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">咨询人员：</label>
                                    <div class="col-sm-8">
                                        <input type="text"  required name="complaintobjectname" id="complaintobjectname" disabled="" onclick="selectComplaintobjectname()" class="form-control">
                                        <input type="hidden" name="complaintobjectid" id="complaintobjectid"  class="form-control">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkAmount" style="color: red">姓名：</label>
                                    <div class="col-sm-8">
                                        <input type="text"  required name="userName" maxlength="25"  class="form-control">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">电话：</label>
                                    <div class="col-sm-8">
                                        <input type="text"  required name="phone" id="phone" maxlength="11" class="form-control">
                                    </div>
                                </div>
                            </div>
                        </div>



                    </div>
                </div>
            </div>
        </div>

    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "tms/consultingFeedback";


    $(function () {
        $('#collapseOne').collapse('show');
        /** 校验 */
        $("#form-consultingFeedback-add").validate({
            focusCleanup: true
        });
    });


    //提交
    function submitHandler() {
        if ($.validate.form()) {
            $.operate.saveTab(prefix + "/add", $('#form-consultingFeedback-add').serialize());
        }
    }

    /**
     * 选择对象后，解锁咨询人员
     */
    function unlockComplaintobjectname() {
        var complaintobjectcategories = $("#complaintobjectcategories").val();
        if(complaintobjectcategories != '' && complaintobjectcategories != null){
            $("#complaintobjectname").removeAttr("disabled");
            $("#complaintobjectname").val("");
            $("#complaintobjectid").val("");
        }else{
            $("#complaintobjectname").attr("disabled","true");
        }

    }

    /**
     * 选择咨询人员
     */
    function selectComplaintobjectname() {
        var complaintobjectcategories = $("#complaintobjectcategories").val();
        //已同步至省平台
        var isexception = '0';
        var type = '3';
        var title = "";
        var url = "";
        if(complaintobjectcategories == 1){
            title = "选择托运人";
            url = prefix + "/shipperinfo"
        }else if(complaintobjectcategories == 2){
            title = "选择承运人";
            url = ctx + "basic/driver/selectActualCarrier?isexception="+isexception;
        }else if(complaintobjectcategories == 3) {
            title = "选择车辆";
            url = ctx +"basic/car/selectCarLic?isexception="+isexception;
        }else if(complaintobjectcategories == 4){
            title = "选择驾驶员";
            url = ctx +"basic/driver/selectDriver?isexception="+isexception;
        }else{
            title = "网络货运平台";
            url = prefix + "/nocarcarrierplatforminfo"
        }
        $.modal.open(title, url,"","",function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            var complaintobjectname = "";
            var complaintobjectid = "";
            if(complaintobjectcategories == 1){
                complaintobjectid = rows[0]["shipperid"];
                complaintobjectname = rows[0]["shippername"];
            }else if(complaintobjectcategories == 2){
                complaintobjectname = rows[0]["carriername"];
                complaintobjectid = rows[0]["carrierid"];
            }else if(complaintobjectcategories == 3) {
                complaintobjectname = rows[0]["carno"];
                complaintobjectid = rows[0]["carId"];
            }else if(complaintobjectcategories == 4){
                complaintobjectname = rows[0]["driverName"];
                complaintobjectid = rows[0]["driverId"];
            }else{
                complaintobjectname = rows[0]["enterprisename"];
                complaintobjectid = rows[0]["platformcode"];
            }

            $("#complaintobjectname").val(complaintobjectname);
            $("#complaintobjectid").val(complaintobjectid);

            layer.close(index);
            $("#form-consultingFeedback-add").validate().element($("#complaintobjectname"))
        });
    }






</script>
</body>
</html>