<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('咨询反馈列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
		 <div class="row">
			 <div class="col-sm-12 search-collapse">
				 <form id="role-form" class="form-horizontal">
					 <div class="row">
						 <div class="col-md-3 col-sm-6">
							 <div class="form-group">
								 <label class="col-sm-4">标题：</label>
								 <div class="col-sm-8">
									 <input name="title" placeholder="请输入标题" class="form-control valid" type="text"
											aria-required="true">
								 </div>
							 </div>
						 </div>
						 <div class="col-md-3 col-sm-6">
							 <div class="form-group">
								 <label class="col-sm-4">姓名：</label>
								 <div class="col-sm-8">
									 <input name="userName" placeholder="请输入姓名" class="form-control valid" type="text"
											aria-required="true">
								 </div>
							 </div>
						 </div>
						 <div class="col-md-3 col-sm-6">
							 <div class="form-group">
								 <label class="col-sm-4">电话 ：</label>
								 <div class="col-sm-8">
									 <input name="phone" placeholder="请输入电话" class="form-control valid" type="text"
											aria-required="true">
								 </div>
							 </div>
						 </div>
						 <div class="col-md-3 col-sm-6">
							 <div class="form-group">
								 <label class="col-sm-6"></label>
								 <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								 <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							 </div>
						 </div>
					 </div>
				 </form>
			 </div>
			
	        <div class="btn-group-sm" id="toolbar" role="group">
				<a class="btn btn-success" onclick="$.operate.addTab()" shiro:hasPermission="tms:consultingFeedback:add">
					<i class="fa fa-plus"></i> 添加
				</a>
				<a class="btn btn-success multiple disabled" onclick="handle()" shiro:hasPermission="tms:consultingFeedback:handle">
					<i class="fa fa-check-circle-o"></i> 处理
				</a>
			</div>
			<div class="col-sm-12 select-table table-striped">
				<table id="bootstrap-table" data-mobile-responsive="true"></table>
			</div>
		</div>
	</div>
    <div th:include="include :: footer"></div>
    <script th:inline="javascript">

        var prefix = ctx + "tms/consultingFeedback";

        $(function() {
            //监听回车事件 回车搜索
            $(document).keyup(function(e){
                var key = e.which;
                if(key==13){
                    $.table.search();
                }
            });

            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
				exportUrl: prefix + "/export",
                modalName: "咨询反馈",
                clickToSelect:true,
                showToggle:false,
                showColumns:true,
				width: 560,
		        showExport: false,
                columns: [{
		            checkbox: true
		        },
				{
					field : 'consultingFeedbackId', 
					title : '咨询反馈id',
					visible: false
				},
				{
					field : 'title', 
					title : '标题',
				},
				{
					field : 'note', 
					title : '内容',
				},
				{
					field : 'userName', 
					title : '姓名',
				},
				{
					field : 'phone', 
					title : '电话',
				},
				{
					field : 'handler',
					title : '处理人',
				},
				{
					field : 'handlDate',
					title : '处理时间',
				},
				{
					field : 'handlerStatus',
					title : '处理状态',
                    formatter: function status(value, row, index) {
                        return row.handlerStatus == 0? '待处理':'已处理';
                    }
				}
				]
            };
            $.table.init(options);
        });

        /**
		 * 处理
         */
        function handle(){
            var consultingFeedbackId =  $.table.selectColumns("consultingFeedbackId").join();
            var data = {consultingFeedbackId:consultingFeedbackId};
            var url = prefix + "/handle";
            $.modal.confirm("是否确认处理", function deposit() {
                $.operate.post(url,data);
            });
        }

    </script>




</body>
</html>