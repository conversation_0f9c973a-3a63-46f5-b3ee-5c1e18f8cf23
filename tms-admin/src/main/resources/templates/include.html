<!-- 通用CSS -->
<head th:fragment=header(title)>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="keywords" content="">
	<meta name="description" content="">
	<title th:text="${title}"></title>

	<link th:href="@{/ajax/libs/layui/css/layui.css}" rel="stylesheet"/>
	<link rel="shortcut icon" href="favicon.ico">
	<link th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
	<link th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
	<link th:href="@{/css/jump.css}" rel="stylesheet"/>
	<!-- bootstrap-table 表格插件样式 -->
	<link th:href="@{/ajax/libs/bootstrap-table/bootstrap-table.min.css}" rel="stylesheet"/>
	<link th:href="@{/css/animate.css}" rel="stylesheet"/>
	<link th:href="@{/css/style.css}" rel="stylesheet"/>
	<link th:href="@{/ruoyi/css/ry-ui.css}" rel="stylesheet"/>
	<!-- 新增form表单样式 -->
	<link th:href="@{/ruoyi/css/hg-form.css}" rel="stylesheet"/>
	<!-- 新增图片旋转样式 说明提示 -->
	<link th:href="@{/css/boxImg.css}" rel="stylesheet"/>
	<link th:href="@{/css/viewer.min.css}" rel="stylesheet"/>
<!--	<link th:href="@{/ajax/libs/layer/theme/moon/style.css}" rel="stylesheet"/>-->
</head>

<head th:fragment="deposit">
	<link th:href="@{/deposit/main.css}" rel="stylesheet"/>
	<link th:href="@{/deposit/swiper.min.css}" rel="stylesheet"/>
	<link th:href="@{/deposit/iconfont.css}" rel="stylesheet"/>
</head>

<!-- 通用JS -->
<div th:fragment="footer">
	<script th:src="@{/js/jquery.min.js}"></script>
	<script th:src="@{/js/jquery.qrcode.min.js}"></script>
	<script th:src="@{/js/bootstrap.min.js}"></script>
	<script th:src="@{/js/jquery.rating-stars.min.js}"></script>
	<!-- 新增图片旋转样式 说明提示 -->
	<script th:src="@{/js/boxImg.js}"></script>
	<script th:src="@{/js/viewer-jquery.min.js}"></script>
	<script th:src="@{/js/pictureViewer.js}"></script>
	<!-- bootstrap-table 表格插件 -->
	<script th:src="@{/ajax/libs/bootstrap-table/bootstrap-table.min.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/locale/bootstrap-table-zh-CN.min.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/mobile/bootstrap-table-mobile.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/toolbar/bootstrap-table-toolbar.min.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/columns/bootstrap-table-fixed-columns.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/filter-control/bootstrap-table-filter-control.js}"></script>
	<!-- jquery-validate 表单验证插件 -->
	<script th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
	<script th:src="@{/ajax/libs/validate/messages_zh.min.js}"></script>
	<script th:src="@{/ajax/libs/validate/jquery.validate.extend.js}"></script>
	<!-- jquery-validate 表单树插件 -->
	<script th:src="@{/ajax/libs/bootstrap-treetable/bootstrap-treetable.js}"></script>
	<!-- jquery-export 表格导出插件 -->
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/export/bootstrap-table-export.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/export/tableExport.js}"></script>
	<!-- 遮罩层 -->
	<script th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
	<script th:src="@{/ajax/libs/blockUI/jqprint.js}"></script>

    <script th:src="@{/ajax/libs/iCheck/icheck.min.js}"></script>
<!--	<script th:src="@{/ajax/libs/layer/layer.min.js}"></script>-->
	<script th:src="@{/ajax/libs/layui/layui.js}"></script>
	<script th:src="@{/ruoyi/js/common.js?v=3.4.0}"></script>
	<script th:src="@{/ruoyi/js/ry-ui.js?v=3.4.0}"></script>

	<script th:inline="javascript"> var ctx = [[@{/}]]; </script>

</div>

<!-- ztree树插件 -->
<div th:fragment="ztree-css">
    <link th:href="@{/ajax/libs/jquery-ztree/3.5/css/metro/zTreeStyle.css}" rel="stylesheet"/>
</div>
<div th:fragment="ztree-js">
    <script th:src="@{/ajax/libs/jquery-ztree/3.5/js/jquery.ztree.all-3.5.js}"></script>
</div>

<!-- select2下拉框插件 -->
<div th:fragment="select2-css">
    <link th:href="@{/ajax/libs/select2/select2.min.css}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/select2/select2-bootstrap.css}" rel="stylesheet"/>
</div>
<div th:fragment="select2-js">
    <script th:src="@{/ajax/libs/select2/select2.min.js}"></script>
</div>

<!-- bootstrap-select下拉框插件 -->
<div th:fragment="bootstrap-select-css">
    <link th:href="@{/ajax/libs/bootstrap-select/bootstrap-select.css}" rel="stylesheet"/>
</div>
<div th:fragment="bootstrap-select-js">
    <script th:src="@{/ajax/libs/bootstrap-select/bootstrap-select.js}"></script>
</div>

<!-- datetimepicker日期和时间插件 -->
<div th:fragment="datetimepicker-css">
    <link th:href="@{/ajax/libs/datapicker/bootstrap-datetimepicker.min.css}" rel="stylesheet"/>
</div>
<div th:fragment="datetimepicker-js">
    <script th:src="@{/ajax/libs//datapicker/bootstrap-datetimepicker.min.js}"></script>
</div>

<!-- ui布局插件 -->
<div th:fragment="layout-latest-css">
    <link th:href="@{/ajax/libs/jquery-layout/jquery.layout-latest.css}" rel="stylesheet"/>
</div>
<div th:fragment="layout-latest-js">
    <script th:src="@{/ajax/libs/jquery-layout/jquery.layout-latest.js}"></script>
</div>

<!-- summernote富文本编辑器插件 -->
<div th:fragment="summernote-css">
    <link th:href="@{/ajax/libs/summernote/summernote.css}" rel="stylesheet"/>
	<link th:href="@{/ajax/libs/summernote/summernote-bs3.css}" rel="stylesheet"/>
</div>
<div th:fragment="summernote-js">
    <script th:src="@{/ajax/libs/summernote/summernote.min.js}"></script>
	<script th:src="@{/ajax/libs/summernote/summernote-zh-CN.js}"></script>
</div>

<!-- cropbox图像裁剪插件 -->
<div th:fragment="cropbox-css">
    <link th:href="@{/ajax/libs/cropbox/cropbox.css}" rel="stylesheet"/>
</div>
<div th:fragment="cropbox-js">
    <script th:src="@{/ajax/libs/cropbox/cropbox.js}"></script>
</div>

<!-- jasny功能扩展插件 -->
<div th:fragment="jasny-bootstrap-css">
    <link th:href="@{/ajax/libs/jasny/jasny-bootstrap.min.css}" rel="stylesheet"/>
</div>
<div th:fragment="jasny-bootstrap-js">
    <script th:src="@{/ajax/libs/jasny/jasny-bootstrap.min.js}"></script>
</div>

<!-- fileinput文件上传插件 -->
<div th:fragment="bootstrap-fileinput-css">
    <link th:href="@{/ajax/libs/bootstrap-fileinput/fileinput.min.css}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/bootstrap-fileinput/themes/explorer/theme.css}" rel="stylesheet"/>
</div>
<div th:fragment="bootstrap-fileinput-js">
    <script th:src="@{/ajax/libs/bootstrap-fileinput/fileinput.min.js}"></script>
    <script th:src="@{/ajax/libs/bootstrap-fileinput/themes/explorer/theme.js}"></script>
</div>

<div th:fragment="bootstrap-fileinput553-css">
	<link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/bootstrap-icons.min.css}" crossorigin="anonymous">
	<link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/fileinput.css}" media="all" type="text/css"/>
	<link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/all.css}" crossorigin="anonymous">
	<link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/themes/explorer-fa5/theme.css}" media="all" type="text/css"/>
	<style type="text/css">
		.file-drop-zone {
			height: auto !important;
			min-height: auto !important;
		}
	</style>
</div>
<div th:fragment="bootstrap-fileinput553-js">
	<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/fileinput.min.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/themes/explorer-fa5/theme.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/locales/zh.js}"></script>
</div>

<div th:fragment="citypicker">
	<link th:href="@{'/ajax/libs/citypicker/css/city-picker.css'}" rel="stylesheet"/>
	<link th:href="@{'/ajax/libs/citypicker/css/city-picker-custom.css'}" rel="stylesheet"/>
	<script th:src="@{'/ajax/libs/citypicker/js/city-picker.data.js'}"></script>
	<script th:src="@{'/ajax/libs/citypicker/js/city-picker.js'}"></script>
</div>

<!-- duallistbox双列表框插件 -->
<div th:fragment="bootstrap-duallistbox-css">
    <link th:href="@{/ajax/libs/duallistbox/bootstrap-duallistbox.min.css}" rel="stylesheet"/>
</div>
<div th:fragment="bootstrap-duallistbox-js">
    <script th:src="@{/ajax/libs/duallistbox/bootstrap-duallistbox.min.js}"></script>
</div>

<!-- suggest搜索自动补全 -->
<div th:fragment="bootstrap-suggest-js">
    <script th:src="@{/ajax/libs/suggest/bootstrap-suggest.min.js}"></script>
</div>

<!-- typeahead搜索自动补全 -->
<div th:fragment="bootstrap-typeahead-js">
    <script th:src="@{/ajax/libs/typeahead/bootstrap3-typeahead.min.js}"></script>
</div>

<!-- jsonview格式化和语法高亮JSON格式数据查看插件 -->
<div th:fragment="jsonview-css">
    <link th:href="@{/ajax/libs/jsonview/jquery.jsonview.css}" rel="stylesheet"/>
</div>
<div th:fragment="jsonview-js">
    <script th:src="@{/ajax/libs/jsonview/jquery.jsonview.js}"></script>
</div>

<!-- jquery.steps表单向导插件 -->
<div th:fragment="jquery-steps-css">
    <link th:href="@{/ajax/libs/staps/jquery.steps.css}" rel="stylesheet"/>
</div>
<div th:fragment="jquery-steps-js">
    <script th:src="@{/ajax/libs/staps/jquery.steps.min.js}"></script>
</div>

<!-- ECharts百度统计图表插件 -->
<div th:fragment="echarts-js">
    <script th:src="@{/ajax/libs/report/echarts/echarts-all.js}"></script>
<!--    <script th:src="@{/ajax/libs/report/echarts/echarts-macarons.js}"></script>-->
</div>

<!-- peity图表组合插件 -->
<div th:fragment="peity-js">
    <script th:src="@{/ajax/libs/report/peity/jquery.peity.min.js}"></script>
</div>

<!-- sparkline线状图插件 -->
<div th:fragment="sparkline-js">
    <script th:src="@{/ajax/libs/report/sparkline/jquery.sparkline.min.js}"></script>
</div>

<!-- 表格拖拽插件 -->
<div th:fragment="bootstrap-table-reorder-js">
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder/bootstrap-table-reorder.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder/jquery.tablednd.js}"></script>
</div>

<!-- 表格列拖拽插件 table中添加 data-reorderable-columns="true" 即可-->
<div th:fragment="bootstrap-table-reorder-columns">
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder-columns/jquery-ui.min.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder-columns/jquery.dragtable.js}"></script>
	<link th:href="@{/ajax/libs/bootstrap-table/extensions/reorder-columns/dragtable.css}" rel="stylesheet"/>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder-columns/bootstrap-table-reorder-columns.js}"></script>
</div>

<!-- 表格列宽调整 table中添加 data-resizable="true" 即可-->
<div th:fragment="bootstrap-table-resizable">
	<link th:href="@{/ajax/libs/bootstrap-table/extensions/resizable/jquery.resizableColumns.css}" rel="stylesheet"/>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/resizable/jquery.resizableColumns.min.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/resizable/bootstrap-table-resizable.js}"></script>
</div>

<!-- 省市级联 -->
<div th:fragment="distpicker">
	<script th:src="@{/ajax/libs/distpicker/distpicker.min.js}"></script>
</div>

<!-- sockjs -->
<div th:fragment="sockjs">
	<script th:src="@{/ajax/libs/sockjs/sockjs.min.js}"></script>
	<script th:src="@{/ajax/libs/sockjs/stomp.min.js}"></script>
</div>

<!-- sockjs -->
<div th:fragment="toastr-css">
	<link th:href="@{/ajax/libs/toastr/toastr.min.css}" rel="stylesheet"/>

</div>
<div th:fragment="toastr-js">
	<script th:src="@{/ajax/libs/toastr/toastr.min.js}"></script>
</div>

<div th:fragment="galpop-css">
	<link th:href="@{/ajax/libs/galpop/css/jquery.galpop.css}" rel="stylesheet"/>
</div>
<div th:fragment="galpop-js">
	<script th:src="@{/ajax/libs/galpop/js/jquery.galpop.min.js}"></script>
</div>

<div th:fragment="template-js">
	<script type="text/javascript" th:src="@{/js/template.js}"></script>
</div>
<div th:fragment="bootstrap-editable-css">
	<link th:href="@{/ajax/libs/bootstrap-table/extensions/editable/bootstrap-editable.css}" rel="stylesheet"/>
</div>
<div th:fragment="bootstrap-table-editable-js">
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/editable/bootstrap-editable.min.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/editable/bootstrap-datepicker.zh-CN.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/editable/bootstrap-table-editable.min.js}"></script>
</div>

<!-- pdf-css  -->
<!--<div th:fragment="pdf-css">
	<link th:href="@{/ajax/libs/pdfjs-3.4.120-dist/build/viewer.css}" rel="stylesheet"/>
</div>-->
<!-- pdf-js -->
<div th:fragment="pdf-js">
	<script th:src="@{/ajax/libs/pdfjs/build/pdf.js}"></script>
</div>

<div th:fragment="xm-select">
	<script th:src="@{/js/xm-select.js}"></script>
</div>

<div th:fragment="animate-css">
	<link th:href="@{/css/animate.css}" rel="stylesheet"/>
</div>
<div th:fragment="iconfont-css">
	<link th:href="@{/css/iconfont/iconfont.css}" rel="stylesheet"/>
</div>
