<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">

<head>
    <th:block th:include="include :: header('重新登录')"/>
</head>

<body>

<!--banner begin-->
<div class="form-content">
    <form id="form-out-quote" class="form-horizontal" novalidate="novalidate">
        <div class="panel-body">
            <div class="col-md-6 col-sm-12">
                <div class="form-group">
                    <label class="col-sm-2">登录账号：</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="username" name="username"
                               placeholder="用户名" autocomplete="off">
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-sm-12">
                <div class="form-group">
                    <label class="col-sm-2">登录密码：</label>
                    <div class="col-sm-10">
                        <input type="password" class="form-control" id="password" name="password"
                               placeholder="密码" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<!--banner end-->

<script th:inline="javascript"> var ctx = [[@{
    /}]]; var captchaType = [[${captchaType}]]; </script>
<!-- 全局js -->
<script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
<script src="../static/js/bootstrap.min.js" th:src="@{/js/bootstrap.min.js}"></script>
<!-- 验证插件 -->
<script src="../static/ajax/libs/validate/jquery.validate.min.js"
        th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
<script src="../static/ajax/libs/validate/messages_zh.min.js"
        th:src="@{/ajax/libs/validate/messages_zh.min.js}"></script>
<script src="../static/ajax/libs/layer/layer.min.js" th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script src="../static/ajax/libs/blockUI/jquery.blockUI.js" th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
<script src="../static/ruoyi/js/ry-ui.js" th:src="@{/ruoyi/js/ry-ui.js?v=3.4.0}"></script>


<script th:inline="javascript">
    function submitHandler() {
        var Sys = {};
        var ua = navigator.userAgent.toLowerCase();
        var s;
        (s = ua.match(/msie ([\d.]+)/)) ? Sys.ie = s[1] :
            (s = ua.match(/chrome\/([\d.]+)/)) ? Sys.chrome = s[1] : 0;
        if (Sys.ie) {
            alert("您的浏览器版本过低，无法登陆！推荐使用最新的Chrome浏览器")
            return false;
        }

        var username = $.common.trim($("input[name='username']").val());
        var password = $.common.trim($("input[name='password']").val());
        var rememberMe = $("input[name='rememberme']").is(':checked');
        var config = {
            type: "post",
            url: ctx + "login",
            data: {
                "username": username,
                "password": password,
                "rememberMe": rememberMe,
                "userType": "0"
            },
            success: function (r) {
                if (r.code == 0) {
                    //$.modal.msgReload("登录成功", modal_status.SUCCESS);
                    $.modal.confirm("登录成功（添加的图片需要重新上传）！", function() {
                        $.modal.close();
                    });
                } else {
                    $.modal.closeLoading();
                    $(".code").val("");
                    $.modal.msg(r.msg);
                }
            }
        };
        $.ajax(config)
    }
</script>
</body>

</html>