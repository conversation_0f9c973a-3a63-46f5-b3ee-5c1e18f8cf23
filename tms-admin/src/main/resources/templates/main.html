<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<!-- Mirrored from www.zi-han.net/theme/hplus/teams_board.html by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 20 Jan 2016 14:19:44 GMT -->
<head>
    <th:block th:include="include :: header('首页')"/>
    <th:block th:include="include :: toastr-css"/>
    <link th:href="@{/css/jquery.contextMenu.min.css}" rel="stylesheet"/>
</head>
<style>
    body {
        background:#f5f5f5;
        font-size: 14px;
    }
    .form-content{
        background:#f5f5f5;
    }
    .notice{
        padding: 0 20px 0 50px;
        background: #fff url("../../img/gg.png") no-repeat left top;
        background-size: 40px 40px;
    }
    .cur{
        cursor: pointer;
    }
    .f12{
        font-size: 12px;
    }
    .f16{
        font-size: 16px;
    }
    .f18{
        font-size: 18px;
    }
    .f20{
        font-size: 20px;
    }
    .fc80{
        color: #808080;
    }
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .mt30{
        margin-top: 30px;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .tc{
        text-align: center;
    }
    .fw{
        font-weight: bold;
    }
    .pada20{
        padding: 20px 20px;
    }
    .notice_content{
        padding: 10px 0;
        border-bottom: 1px #eee solid;
    }
    .btn_new{
        background: #ff9a03;
        color: #fff;
        line-height: 20px;
        width: 60px;
        border-radius: 10px;
        text-align: center;
    }
    .line24{
        line-height: 24px;
    }
    .notice_img{
        width: 20px;
        height: 20px;
    }
    .notice_img img{
        width: 100%;
        height: 100%;
    }
    .maintitle{
        font-weight: bold;
        font-size: 16px;
        padding: 20px 0;
    }
    .boxs{
        background: #fff;
        padding: 20px 20px;
    }
    .bor{
        border-right: 1px #E0E0E0 solid;
    }
    .flexs{
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;
    }
    .dispatch{
        display: flex;
        justify-content: space-between;
    }
    .dispatch_content{
        /*padding: 15px 0px;*/
        /*width: 33%;*/
        color: #fff;
    }
    .pad15{
        padding: 10px 15px 0;
    }
    .dis_box{
        background: rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        padding: 0px 5px;
        line-height: 20px;
    }
    .dis_box span{
        display: inline-block;
    }
    .dis_err{
        /*width: 20px;*/
        /*height: 20px;*/
        /*line-height: 20px;*/
        /*border-radius: 50%;*/
        /*text-align: center;*/
        /*border: 1px solid #FFFFFF;*/
        /*font-size: 12px;*/
        border-radius: 10px;
        color: #fff;
        display: inline-block;
        font-size: 12px;
        height: 18px;
        line-height: 18px;
        padding: 0 6px;
        text-align: center;
        white-space: nowrap;
        border: 1px solid #fff;
    }
    .bg1ab{
        background: #1AB394;
    }
    .bg370{
        background: #3792E8;
    }
    .bgf18{
        background: #F18E3F;
    }
    .bgf31{
        background: #F31515;
    }
    .bgffa{
        background: #FFAA00;
    }
    .fc1ab{
        color: #1AB394;
    }
    .fcf31{
        color: #F31515;
    }
    .fcf8a{
        color: #ff9a03;
    }
    .wid25{
        /*width: 25%;*/
        padding: 0px 10px;
        line-height: 24px;
        position: relative;
    }
    .line{
        width: 1px;
        height: 40px;
        border-left: 1px #eee solid;
        position: absolute;
        right: 0;
        top: 10px;
    }
    .week{
        border: 1px solid #D9D9D9;
        width: 60px;
        line-height: 24px;
        text-align: center;
        color: #808080;
        cursor: pointer;
    }
    .act{
        border: 1px solid #1AB394;
        color: #1AB394;
    }
    .pm{
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        border-radius: 50%;
        background: #F0F2F5;
        margin: 0 auto;
    }
    .pmact{
        background: #314659;
        color: #fff;
    }
    .sm{
        background: #f6f2e5;
        padding: 5px 10px;

    }
    .sm_icon{
        width: 18px;
        height: 18px;
        background: #faad14;
        color: #fff;
        border-radius: 50%;
        display: inline-block;
        line-height: 18px;
        text-align: center;
        font-size: 12px;
    }
    .sm_text{
        color: #A36D00;
        display: inline-block;
        margin-left: 10px;
        line-height: 20px;
    }
    .cw{
        text-align: center;
        background: #F5F7FA;
        padding: 10px 0;
        cursor: pointer;
    }
    .cw:hover{
        background: #e2e5ea;

    }
    .rel{
        position: relative;
    }
    .tool{
        position: absolute;
        top: -5px;
        right: 0px;
        background: #F31515;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        line-height: 20px;
        text-align: center;
        color: #fff;
        font-size: 12px;
    }
    .tool_r{
        position: absolute;
        top: -5px;
        right: -5px;
        background: #F31515;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        line-height: 20px;
        text-align: center;
        color: #fff;
        font-size: 12px;
    }
    .djerr{
        background: #F5F7FA;
        padding: 5px 0;
        cursor: pointer;
    }
    .th{
        background: #F5F7FA;
        padding: 40px 0;
        cursor: pointer;
    }
    .wb{
        width: 20%;

    }
    .wb_img{
        width: 50px;
        height: 50px;
        margin: 0 auto 10px auto;
        position: relative;

    }
    .wb_img img{
        width: 100%;
        height: 100%;
    }
    .dis_img{
        width: 60px;
        height: 60px;
        margin: 10px 0 0;
    }
    .dis_img img{
        width: 100%;
        height: 100%;
    }
    .progress {
        height: 10px;
        background-color: rgba(255, 255, 255, 0.4);
        margin-bottom: 10px;
    }
    .progress-bar {
        background-color: #fff;
    }
    .dis_bor{
        border-top: 1px #1FCCA9 solid;
        line-height: 30px;
        padding: 0 15px;
    }
    .hov{
        padding: 5px 15px;
    }
    .hov:hover{
        background: #f7f7f7;

    }
    .hov:hover .text{
        color: #1AB394;
    }
    .tr{
        text-align: right;
    }
    .table>tbody>tr>td{
        padding: 10px 0px;
    }
    .disnone{
        display: none;
    }
    .f13{
        font-size: 13px;
    }
    .xsr{
        background: #f7f7f7;
        padding: 10px 5px;
    }
    .form-control, .single-line {
        border: 0;
    }
    .calendar{
        padding: 0px 10px;
        border-radius: 2px;
        border: 1px solid #e5e6e7;
    }
    .inputdiv{
        display:flex;
        border: 1px solid #D2D2D2!important;
        background-color: #fff;
        height: 30px;
        line-height: 30px;
        padding: 0px 10px;
        border-radius: 3px;
    }
    .layui-input {
        border-style: none;
    }
    .yye{
        position: relative;
        width: 100px;
        height: 200px;
    }
    .yyemb{
        border: 1px #718296 solid;
        width: 100px;
        position: absolute;
        bottom: 0;
        left: 0;
    }
    .yyezj{
        background: #f4cd64;
        width: 80px;
        bottom: 0;
        left: 10px;
        position: absolute;
    }
    .yyenum{
        width: 100px;
        text-align: center;
        left: 0;
        top: 10px;
        position: absolute;
    }
    .hovT{
        position: relative;
        text-decoration: none;
    }
    .hovT:before{
        content: "";
        position: absolute;
        left: 50%;
        bottom: -4px;
        width: 0;
        height: 2px;
        background: #4285f4;
        transition: all .3s;
    }
    .hovT:hover:before{
        width: 100%;
        left: 0;
        right: 0;
    }
    .iframe-main{
        width: 100%;
        height: calc(100vh - 102px);
    }
    .iframe-mainT{
        width: 100%;
        height: calc((100vh - 102px)/2);
    }
    .flex{
        display: flex;
        align-items: center;
        justify-content: flex-start;
    }
    .box-left{
        flex: 2;margin: 10px 5px 0 10px;box-shadow: 0 2px 10px 2px rgba(0, 0, 0, 0.05);
        background: #fff;border-top: 1px solid #e5e6e7;
    }
    .box-right{
        flex: 3;margin: 10px 10px 0 5px;box-shadow: 0 2px 10px 2px rgba(0, 0, 0, 0.05);
        background: #fff;border-top: 1px solid #e5e6e7;
    }
    .selectOpen{
        transition:  .3s ease-in;
        transform-origin: 50% 0;
        position: relative;
        right:0;
    }
    .leftIcon{
        position: absolute;
        z-index: 2;
        top: calc(50% - 12px);
        background-color: #ffffff;
        padding: 6px 0 6px 6px;
        cursor:pointer;
    }

    .container {
        text-align: center; /* Center align the content */
    }

    .notice_header {
        margin-bottom: 10px; /* Add some space below the header */
        padding-top: 7px;
    }
</style>
<body>
<div class="form-content" th:if="${!isShipper}">
    <div th:if="${1==1}">
        <div class="notice container" th:if="${not #lists.isEmpty(sysNoticesList) and #lists.size(sysNoticesList) > 0}">
            <div class="over notice_content cur" th:each="sysNotices,sysNoticesStat : ${sysNoticesList}"
                 th:onclick="|noticesTab(${sysNotices.noticeId})|" th:if="${sysNoticesStat.index} == 0">
                <div class="fl notice_img" >
                    <img th:src="@{/img/notice.png}" />
                </div>
                <div class="fl line24 ml10"  th:text="${sysNotices.noticeTitle}"></div>
                <div class="fl ml20 btn_new">最新</div>
                <div class="fr fc80" th:text="${#dates.format(sysNotices.updateTime, 'yyyy-MM-dd')}"></div>
            </div>

            <div id="collapseOne" class="panel-collapse collapse">
                <div class="over notice_content cur" th:each="sysNotices,sysNoticesStat : ${sysNoticesList}"
                     th:onclick="|noticesTab(${sysNotices.noticeId})|"  th:if="${sysNoticesStat.index} != 0">
                    <div class="fl notice_img" >
                        <img th:src="@{/img/notice.png}" />
                    </div>
                    <div class="fl line24 ml10"  th:text="${sysNotices.noticeTitle}"></div>
                    <div class="fl ml20 btn_new">最新</div>
                    <div class="fr fc80" th:text="${#dates.format(sysNotices.updateTime, 'yyyy-MM-dd')}"></div>
                </div>

            </div>
            <div class="notice_header" th:if="${not #lists.isEmpty(sysNoticesList) and #lists.size(sysNoticesList) > 1}">
                <a data-toggle="collapse" data-parent="#accordion"
                   href="tabs_panels.html#collapseOne" th:onclick="|toggleText(this,${#lists.size(sysNoticesList)-1})|">展开（[[${#lists.size(sysNoticesList)-1}]]条信息）</a>
            </div>

        </div>
        <!-- 业务中心-->
        <div shiro:hasAnyRoles="sales,admin">
            <div class="maintitle" shiro:hasAnyRoles="sales,admin">业务中心</div>
            <div class="boxs">
                <div class="row">
                    <div class="col-sm-3 wid25" >
                        <div class="over ">
                            <div class="fl hov cur" style="width: 45%;" onclick="invoiceTab()">
                                <div class="">待确认发货单</div>
                                <div class="fw f16 text" id="invoiceNewCount"></div>
                                <div> &nbsp; </div>

                            </div>
                            <div class="fr tr">
<!--                                <div class="tr fc1ab" id="orderTimeRate"></div>-->
                                <i class="fa fa-question-circle" data-toggle="tooltip"
                                   style="font-size: 15px" data-html="true" data-container="body"
                                   title="要求提货日期为当日的数据。"></i>
                                <nobr class="fcf8a hovT cur" id="theTimeoutInvoiceNewCount" onclick="invoiceToBeConfirmedTab()"></nobr>
                                <div class="fc1ab" id="orderTimeRate"></div>
                            </div>
                        </div>
                        <div class="line"></div>
                    </div>
                    <div class="col-sm-3 wid25 " >
                        <div class="over">
                            <div class="fl hov cur" style="width: 50%;" onclick="traceTab(null,1,null,null,null,1)">
                                <div class="">待提货</div>
                                <div class="fw f16 text" id="affirmEntrustCount" ></div>
                                <div> &nbsp; </div>
                            </div>
                            <div class="fr tr">
                                <i class="fa fa-question-circle" data-toggle="tooltip"
                                   style="font-size: 15px" data-html="true" data-container="body"
                                   title="超过提货日期并且未提货的数量"></i>
                                <nobr class="fcf31 hovT cur" id="pickUpMonthCount" onclick="pickUpExpireTab()"></nobr>
                                <div class="" >
                                    <i class="fa fa-question-circle" data-toggle="tooltip"
                                       style="font-size: 15px" data-html="true" data-container="body"
                                       title="要求提货日期为当日并且未提货的数据。"></i>
                                    <nobr class="fcf8a hovT cur" id="theTimeoutAffirmEntrustCount" onclick="pickUpExpiringSoonTab()"></nobr>
                                    <div class="fc1ab" id="pickUpMonthRate"></div>
                                </div>
                            </div>
                        </div>
                        <div class="line"></div>
                    </div>
                    <div class="col-sm-3 wid25 " >
                        <div class="over ">
                            <div class="fl hov cur" style="width: 50%;" onclick="traceTab(null,null,0,null,null,1)">
                                <div class="">待回单上传</div>
                                <div class="fw f16 text" id="receiptStatusCount"></div>
                                <div> &nbsp; </div>
                            </div>
                            <div class="fr tr">
                                <i class="fa fa-question-circle" data-toggle="tooltip"
                                   style="font-size: 15px" data-html="true" data-container="body"
                                   title="超过截止日期并且需要回单却没有回单上传的数量。&#10;截至日期：1、公路整车、危化整车、冷链整车为实际提货日期后第10天。&#10;                 2、其余为实际提货日期后第20天。"></i>
                                <nobr class="fcf31 hovT cur" id="receiptStatusFinishCount" onclick="traceTab(null,null,null,null,0,1)"></nobr>
                                <div>
                                    <i class="fa fa-question-circle" data-toggle="tooltip"
                                       style="font-size: 15px" data-html="true" data-container="body"
                                       title="截止日期前三天内并且需要回单上传的数量。&#10;截至日期：1、公路整车、危化整车、冷链整车为实际提货日期后第10天。&#10;                 2、其余为实际提货日期后第20天。"></i>
                                        <nobr class="fcf8a hovT cur" id="theTimeoutReceiptStatusCount" onclick="traceTab(null,null,null,null,1,1)"></nobr>
                                    <div class="fc1ab" id="receiptStatusFinishRate" ></div>
                                </div>
                            </div>
                        </div>
                        <div class="line"></div>
                    </div>
                    <div class="col-sm-3 wid25 ">
                        <div class="over">
                            <div class="fl cur hov" style="width: 50%;" onclick="custReceiveTab()">
                                <div class="">待对账客户</div>
                                <div class="fw f16 text" id="unreconciledCustomerCount"></div>
                                <div> &nbsp; </div>
                            </div>
                            <div class="fr tr">
                                <i class="fa fa-question-circle" data-toggle="tooltip"
                                   style="font-size: 15px" data-html="true" data-container="body"
                                   title="单据发生次月超过客户设定对账期天数即为逾期"></i>
                                <nobr class="fcf31" id="timedOutUnCustomerCount"></nobr>
                                <div>
                                    <i class="fa fa-question-circle" data-toggle="tooltip"
                                       style="font-size: 15px" data-html="true" data-container="body"
                                       title="单据发生次月即进入应对账期"></i>
                                    <nobr class="fcf8a" id="aboutToTimeOutUnCustomerCount"></nobr>
                                    <!--                                <div class="fc1ab" id=""></div>-->
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="row mt20">
                    <div class="col-sm-3 wid25" >
                        <div class="over">
                            <div class="fl cur hov" style="width: 45%;" onclick="traceTab(1,null,null,null,null,1)">
                                <div class="">当天待跟踪</div>
                                <div class="fw f16 text" id="needTraceTodayCount"></div>
                                <div> &nbsp; </div>
                            </div>
                            <div class="fr">
                                <div class="tr fc1ab" id="traceRate"></div>
                            </div>
                        </div>
                        <div class="line"></div>
                    </div>
                    <div class="col-sm-3 wid25" >
                        <div class="over">
                            <div class="fl cur hov" style="width: 50%;" onclick="traceTab(null,2,null,null,null,1)">
                                <div class="">待到货</div>
                                <div class="fw f16 text" id="pickUpEntrustCount" ></div>
                                <div> &nbsp; </div>
                            </div>
                            <div class="fr tr">
                                <i class="fa fa-question-circle" data-toggle="tooltip"
                                   style="font-size: 15px" data-html="true" data-container="body"
                                   title="超过要求到货日期并且未到货的数量"></i>
                                <nobr class="fcf31 hovT cur" id="arrivalMonthCount" onclick="arrivalExpireTab()"></nobr>
                                <div>
                                    <i class="fa fa-question-circle" data-toggle="tooltip"
                                       style="font-size: 15px" data-html="true" data-container="body"
                                       title="要求要求提货日期为当日并且未到货的数据"></i>
                                    <nobr class="fcf8a hovT cur" id="theTimeoutPickUpEntrustCount" onclick="arrivalExpiringSoonTab()"></nobr>
                                    <div class=" fc1ab" id="arrivalMonthRate"></div>
                                </div>
                            </div>
                        </div>
                        <div class="line"></div>
                    </div>
                    <div class="col-sm-3 wid25" >
                        <div class="over">
                            <div class="fl hov cur" style="width: 50%;" onclick="traceTab(null,null,null,0,null,1)">
                                <div class="">待正本回单</div>
                                <div class="fw f16 text" id="ifReceiptCount"></div>
                                <div> &nbsp; </div>
                            </div>
                            <div class="fr tr">
                                <i class="fa fa-question-circle" data-toggle="tooltip"
                                   style="font-size: 15px" data-html="true" data-container="body"
                                   title="超过截止日期并且需要正本回单却没有正本回单的数量。&#10;截至日期：1、公路整车、危化整车、冷链整车为实际提货日期后第10天。&#10;                 2、其余为实际提货日期后第20天。"></i>
                                <nobr class="fcf31 hovT cur" id="ifReceiptFinishCount" onclick="traceTab(null,null,null,null,2,1)"></nobr>
                                <div>
                                    <i class="fa fa-question-circle" data-toggle="tooltip"
                                       style="font-size: 15px" data-html="true" data-container="body"
                                       title="截止日期前五天内并且需要正本回单的数量。&#10;截至日期：1、公路整车、危化整车、冷链整车为实际提货日期后第10天。&#10;                 2、其余为实际提货日期后第20天。"></i>
                                    <nobr class="fcf8a hovT cur" id="theTimeoutIfReceiptCount" onclick="traceTab(null,null,null,null,3)"></nobr>
                                    <div class="fc1ab" id="ifReceiptFinishRate"></div>
                                </div>
                            </div>
                        </div>
                        <div class="line"></div>
                    </div>
                    <div class="col-sm-3 wid25 " >
                       <div class="over">
                           <div class="fl cur hov" style="width: 50%;" onclick="receCheckSheetTab()">
                               <div class="">待申请开票</div>
                               <div class="fw f16 text" id="pendingApplicationCount"></div>
                               <div> &nbsp; </div>
                           </div>
                           <div class="fr tr">
                               <i class="fa fa-question-circle" data-toggle="tooltip"
                                  style="font-size: 15px" data-html="true" data-container="body"
                                  title="已对账并且过了申请开票期的数量。"></i>
                               <nobr class="fcf31" id="timedOutUnBillingCount"></nobr>
                               <div>
                                   <div class="fcf8a" id="aboutToTimeOutBillingCount"></div>
                               </div>
                           </div>
                       </div>
                    </div>
                </div>
            </div>

             <div class="boxs mt20">
                <div class="fw">项目部营收利润完成统计图(月)
<!--                    <div class="fr" style="color: #1BB393;">查看详情 ></div>-->
                </div>
                <!-- <div class="over mt20">
                    <div class="fl week act" onclick="getYslrwcChatsByType(0)">按年</div>
                    <div class="fl week" onclick="getYslrwcChatsByType(1)">按月</div>
                    <div class="fl week" onclick="getYslrwcChatsByType(2)">按日</div>
                </div> -->


                <div class="row">
                    <div class="col-sm-2">

                        <div class="mt5" th:each="e:${salesComplete}">
                            [[${e.salesName}]]&nbsp;&nbsp;完成率：[[${e.rate}]]%
                            <div class="mt5">
                                <div class="inline mr20">
                                    <div class="mr5 inline" style="background-color: #3792E8;width: 20px;height: 10px;"></div>[[${e.target}]]万元
                                </div>
                                <div class="inline">
                                    <div class="mr5 inline" style="background-color: #FFB500;width: 20px;height: 10px;"></div>[[${e.complete}]]万元
                                </div> 
                            </div>
                        </div>


                    </div>
                    <div class="col-sm-10">
                        <div id="yslrwcChats" style="width: 100%;height: 300px"></div>
                    </div>
                </div>
            </div>

            <!-- <div class="boxs mt20">
                <div class="fw">项目运营情况汇总</div>
                <div class="over mt20">
                    <div class="fl act week" onclick="getDdblChatsAndDdzbChats(0)">按日</div>
                    <div class="fl week" onclick="getDdblChatsAndDdzbChats(1)">按月</div>
                </div>
                <div class="row">
                    <div class="col-sm-3">
                        <div id="ddblChats" style="width: 100%;height: 300px"></div>
                        <div class="mt10 fc80" style="margin-left: 10%" id="dispatchRate"></div>
                    </div>
                    <div class="col-sm-9">
                        <div id="ddzbChats" style="width: 100%;height: 300px"></div>
                        <div class="mt10 fc80 tc" style="">各调度组完成占比</div>
                    </div>
                </div>
            </div> -->
<!--            客户营业额-->
            <!-- <div class="row mt20">
                <div class="col-sm-8" >
                    <div class="boxs" style="min-height: 350px" id="table">
                        <div class="row">
                            <div class="col-sm-6" style="padding-right: 20px">
                                <div class="over">
                                    <div class="fl fw">客户营业额增加前十统计</div>
                                    <div class="fr fc1ab cur" onclick="custRankTable(0)">
                                        <span>查看详情</span>
                                        <i class="fa fa-angle-right" style=""></i>
                                    </div>
                                </div>
                                <div class="over mt20">
                                    <div class="fl over">
                                        <div class="fl act week" onclick="getCustRank(0,0,'custRank_desc')">按周</div>
                                        <div class="fl week" onclick="getCustRank(1,0,'custRank_desc')">按月</div>
                                        <div class="fl week" onclick="getCustRank(2,0,'custRank_desc')">按年</div>
                                    </div>

                                </div>
                                <div class="mt20">
                                    <div class="fixed-table-body table-responsive">
                                        <table class="custom-tab tab table">
                                            <thead style="background: #f4f6f7;">
                                            <tr>
                                                <th style="width: 12%">排名</th>
                                                <th style="width: 38%">客户简称</th>
                                                <th>营业额</th>
                                                <th style="width: 27%">涨幅</th>
                                            </tr>
                                            </thead>
                                            <tbody id="custRank_desc" >
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6" style="padding-left: 20px">
                                <div class="over">
                                    <div class="fl fw">客户营业额减少前十统计</div>
                                    <div class="fr fc1ab cur" onclick="custRankTable(1)">
                                        <span>查看详情</span>
                                        <i class="fa fa-angle-right" style=""></i>
                                    </div>
                                </div>
                                <div class="over mt20">
                                    <div class="fl over">
                                        <div class="fl act week" onclick="getCustRank(0,1,'custRank_asc')">按周</div>
                                        <div class="fl week" onclick="getCustRank(1,1,'custRank_asc')">按月</div>
                                        <div class="fl week" onclick="getCustRank(2,1,'custRank_asc')">按年</div>
                                    </div>
                                </div>
                                <div class="mt20">
                                    <div class="fixed-table-body table-responsive">
                                        <table class="custom-tab tab table">
                                            <thead style="background: #f4f6f7;">
                                            <tr>
                                                <th style="width: 12%">排名</th>
                                                <th style="width: 38%">客户简称</th>
                                                <th>营业额</th>
                                                <th style="width: 27%">涨幅</th>
                                            </tr>
                                            </thead>
                                            <tbody id="custRank_asc">
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
<!--                客户营业额涨幅纵览统计-->
                <!-- <div class="col-sm-4" >
                    <div class="boxs" id="table1">
                        <div class="over">
                            <div class="fl fw">客户营业额涨幅纵览统计</div>
                        </div>
                        <div class="mt20 over">
                            <div class="over fl">
                                <div class="fl act week" onclick="getKhyyezfChats(0)">按周</div>
                                <div class="fl week" onclick="getKhyyezfChats(1)">按月</div>
                                <div class="fl week" onclick="getKhyyezfChats(2)">按年</div>
                            </div>
                            <div class="fr line24 ml10">
                                <span class="fc80">营业额月环比</span>
                                <span id="chainRatio"></span>
                                <i class="fa  " style=""></i>
                            </div>
                            <div class="fr fcf31 line24">
                                <span class="fc80">营业额月同比</span>
                                <span id="yearOnYearRatio"></span>
                                <i class="fa" style=""></i>
                            </div>
                        </div>
                        <div class="mt20">
                            <div class="" id="khyyezfChats" style="width: 100%;height: 200px"></div>
                        </div>
                    </div>
                </div> 
            </div>-->
<!--            账款信息统计表-->
            <!-- <div class="boxs mt20">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="over">
                            <div class="fl fw">账款信息统计表</div>
                            <div class="fr fc1ab cur" onclick="zkxxTable()">
                                <span>查看详情</span>
                                <i class="fa fa-angle-right" style=""></i>
                            </div>
                        </div>
                        <div class="mt20 over">
                            <div class="fl" style="width: calc(100% - 50px)">
                                <div id="zkxxChats" style="width: 100%;height: 400px"></div>
                            </div>
                            <div class="fl" style="margin-top: 80px">
                                <div class="fc1ab" style="margin-top: 40px">未逾期</div>
                                <div class="fcf31" style="margin-top: 100px">已逾期</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="over">
                            <div class="fl fw">超指导价、亏损、低毛利统计表（近一月）</div>
                            <div class="fr fc1ab cur" onclick="czdjTable()">
                                <span>查看详情</span>
                                <i class="fa fa-angle-right" style=""></i>
                            </div>
                        </div>
                        <div class="mt20">
                            <div id="czdjChats" style="width: 100%;height: 380px"></div>
                        </div>
                    </div>
                </div>
            </div> -->
        </div>
        <!--    调度-->
        <div shiro:hasAnyRoles="dispatcher,admin">
            <div class="maintitle">调度</div>
            <div class="">
                <div class="dispatch">
                    <div style="width: 30%" class="dispatch_content bg1ab cur" onclick="toDispatchTab()">
                        <div class="over pad15">
                            <div class="fl">
                                <div class="over f12">
                                    <div class="fl">待调度单数</div>
                                    <div class="fl dis_box ml10">
                                        <span>即将过期</span>
                                        <span class="dis_err bgffa" id="willExpireSoonDispatchCount">0</span>
                                    </div>
                                    <div class="fl dis_box ml10">
                                        <span>已经过期</span>
                                        <span class="dis_err bgf31" id="alreadyExpiredDispatchCount">0</span>
                                    </div>

                                </div>
                                <div class="fw f20 mt10" id="toDispatch">0</div>
                                <div class="progress mt20">
                                    <div class="progress-bar" role="progressbar" aria-valuenow="60" id="toDispatch_progressbar"
                                         aria-valuemin="0" aria-valuemax="100" style="width:0%;">
                                        <!--                                        <span class="sr-only">40% 完成</span>-->
                                    </div>
                                </div>
                            </div>
                            <div class="fr ">
                                <div class="dis_img" >
                                    <img th:src="@{/img/bg1.png}" />
                                </div>
                            </div>
                        </div>
                        <div class="dis_bor" id="dispatchOnTimeRate">0%</div>
                    </div>
                    <div style="width: 37%" class="dispatch_content bg370 cur" onclick="myCarrierTab()">
                        <div class="over pad15">
                            <div class="fl">
                                <div class="over f12">
                                    <div class="fl">待完善司机信息数</div>
                                    <div class="fl dis_box ml10">
                                        <span>即将过期</span>
                                        <span class="dis_err bgffa" id="willExpireSoonDriverCount">0</span>
                                    </div>
                                    <div class="fl dis_box ml10">
                                        <span>已经过期</span>
                                        <span class="dis_err bgf31" id="alreadyExpiredDriverCount">0</span>
                                    </div>
                                </div>
                                <div class="fw f20 mt10" id="toBePerfectedDriverCount">0</div>
                                <div class="progress mt20">
                                    <div class="progress-bar" role="progressbar" aria-valuenow="60"
                                         aria-valuemin="0" aria-valuemax="100" id="completedDriverRate">
                                        <span class="sr-only"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="fr ">
                                <div class="dis_img" >
                                    <img th:src="@{/img/driver.png}" />
                                </div>
                            </div>
                        </div>
                        <div class="dis_bor" style="border-top: 1px #49A7FF solid" id="driverCount"></div>
                    </div>
                    <div style="width: 31%" class="dispatch_content bgf18 cur" onclick="myCarrierTab()">
                        <div class="pad15">
                            <div class="">
                                <div class="over f12">
                                    <div class="fl">待完善车辆信息数</div>
                                    <div class="fl dis_box ml10">
                                        <span>即将过期</span>
                                        <span class="dis_err bgffa" id="willExpireSoonCarCount">0</span>
                                    </div>
                                    <div class="fl dis_box ml10">
                                        <span>已经过期</span>
                                        <span class="dis_err bgf31" id="alreadyExpiredCarCount">12</span>
                                    </div>
                                </div>
                                <div class="fw f20 mt10" id="toBePerfectedCarCount">0</div>
                                <div class="progress mt20">
                                    <div class="progress-bar" role="progressbar" aria-valuenow="60"
                                         aria-valuemin="0" aria-valuemax="100" id="completedCarRate">
                                        <!--                                        <span class="sr-only">40% 完成</span>-->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dis_bor" style="border-top: 1px #FFA155 solid" id="carCount"></div>
                    </div>
                </div>
            </div>
            <div class="boxs mt10">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="over">
                            <div class="fl fw">订单调度统计表</div>
                            <div class="fr fc1ab cur" onclick="ddtjTable()">
                                <span>查看详情</span>
                                <i class="fa fa-angle-right" style=""></i>
                            </div>
                        </div>
                        <div class="over mt20">
                            <div class="fl act week" onclick="getDdtjChats(0)">按月</div>
                            <div class="fl week" onclick="getDdtjChats(1)">按日</div>
                        </div>


                        <div class="mt20">
                            <div id="ddtjChats" style="width: 100%;height: 350px"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--    运营管理-->
        <div shiro:hasAnyRoles="yyjl,admin">
            <div class="maintitle">运营管理</div>
            <div class="sm">
                <span class="sm_icon">!</span>
                <span class="sm_text">提醒：带红色角标的菜单按钮表示有待处理即将过期或已过期，请及时审核</span>
            </div>
            <div class="mt10 boxs">
                <div class="djerr" onclick="exceptionTotalTab()">
                    <div class="over" style="width: 150px;margin: 0 auto" >
                        <div class="fl">
                            <img th:src="@{/img/err.png}" style="width: 30px;height: 30px" />
                        </div>
                        <div class="fl" style="line-height: 30px">
                            <span>登记异常订单</span>
                            <span class="fw fcf31" id="exceptionTotal">0</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt10 boxs">
                <div class="fw">付款审核</div>
                <div class="row mt10">
                    <div class="col-sm-3">
<!--                        <div class="tc cw" onclick="payDetailCheckTab()">-->
                        <div class="tc cw" >
                            <span>单笔付款审核&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="payDetailCheckCt">0</span>
                        </div>
                    </div>
                    <div class="col-sm-3">
<!--                        <div class="tc cw" onclick="paySheetRecordTab()">-->
                        <div class="tc cw" >
                            <span>对账付款审核&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="paySheetRecordCt">0</span>
                        </div>
                    </div>
                    <div class="col-sm-3">
<!--                        <div class="tc cw" onclick="otherFeeCheckTab()">-->
                        <div class="tc cw" >
                            <span>三方单笔审核&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="otherFeeCheckCt">0</span>
                        </div>

                    </div>
                    <div class="col-sm-3">
<!--                        <div class="tc cw" onclick="otherFeeSheetRecordTab()">-->
                        <div class="tc cw" >
                            <span>三方对账审核&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="otherFeeSheetRecordCt">0</span>
                        </div>
                    </div>
                </div>
                <div class="fw mt10">调账审核</div>
                <div class="row mt10">
                    <div class="col-sm-3">
                        <div class="tc cw" onclick="payDetailAdjustCheckTab('0')">
                            <span>应付单笔调账审核&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="operationPayAdjustCheck">0</span>
                        </div>
                        <div class="tool" id="operationPayAdjustCheck_warn">0</div>
                    </div>
                    <div class="col-sm-3">
                        <div class="tc cw" onclick="payCheckSheetAdjustCheckTab('0')">
                            <span>应付对账调账审核&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="operationPayAdjustSheetCheck">0</span>
                        </div>
                        <div class="tool" id="operationPayAdjustSheetCheck_warn">0</div>
                    </div>
                    <div class="col-sm-3 rel">
                        <div class="tc cw" onclick="otherFeeAdjustCheckTab('0')">
                            <span>三方单笔调账审核&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="operationOtherFeelAdjustCheck">0</span>
                        </div>
                        <div class="tool" id="operationOtherFeelAdjustCheck_warn">0</div>
                    </div>
                </div>
                <div class="row mt10">
                    <div class="col-sm-3">
                        <div class="tc cw" onclick="receiveDetailAdjustCheckTab('0')">
                            <span>应收单笔调账审核&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="operationReceAdjustCheck">0</span>
                        </div>
                        <div class="tool" id="operationReceAdjustCheck_warn">0</div>

                    </div>
                    <div class="col-sm-3">
                        <div class="tc cw" onclick="receiveCheckSheetCheckTab('0')">
                            <span>应收月度调账审核&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="operationReceAdjustSheetCheck">0</span>
                        </div>
                        <div class="tool" id="operationReceAdjustSheetCheck_warn">0</div>

                    </div>
                </div>
                <div class="fw mt10">其他</div>
                <div class="row mt10">
                    <div class="col-sm-3">
                        <div class="tc cw" onclick="checkExpTab()">
                            <span>异常审核&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="expCheckCt">0</span>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="tc cw" onclick="checkMarginTab()">
                            <span>保证金审核&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="marginCheckCt">0</span>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="tc cw" onclick="checkDepositTab()">
                            <span>定金审核&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="depositCheckCt">0</span>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="tc cw" onclick="checkPayPassTab()">
                            <span>代付审核&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="payPassCheckCt">0</span>
                        </div>
                    </div>

                </div>
                <div class="row mt10">
                </div>
               <!-- <div class="fw mt10">成本控制</div>
                <div class="row mt10">
                    <div class="col-sm-3">
                        <div class="tc cw" onclick="checkGuidePriceTab()">
                            <span>指导价审核&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="operationGuidePriceCheckCt">0</span>
                        </div>
                    </div>

                    <div class="col-sm-3">
                        <div class="tc cw" onclick="segmentCheckTab(1)">
                            <span>调度一级审核&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="segmentCheckFirstCt">0</span>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="tc cw" onclick="segmentCheckTab(2)">
                            <span>调度二级审核&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="segmentCheckSecondCt">0</span>
                        </div>
                    </div>
                </div>-->
            </div>
            <!-- todo-->
<!--            <div class="mt10 boxs">-->
<!--                <div class="row">-->
<!--                    <div class="col-sm-5">-->
<!--                        <div class="over">-->
<!--                            <div class="fl fw">多种运输方式金额统计</div>-->
<!--                            <div class="fr fc1ab cur">-->
<!--                                <span>查看详情</span>-->
<!--                                <i class="fa fa-angle-right" style=""></i>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="mt20">-->
<!--                            <div class="over">-->
<!--                                <div class="fl act week" onclick="getYsfsChats(0)">按日</div>-->
<!--                                <div class="fl week" onclick="getYsfsChats(1)">按月</div>-->
<!--                                <div class="fl week" onclick="getYsfsChats(2)">按年</div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="mt20">-->
<!--                            <div class="" id="ysfsChats" style="width: 100%;height: 250px"></div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="col-sm-1"></div>-->
<!--                    <div class="col-sm-6">-->
<!--                        <div class="over">-->
<!--                            <div class="fl fw">项目部应收账款总额统计表</div>-->
<!--                            <div class="fr fc1ab cur">-->
<!--                                <span>查看详情</span>-->
<!--                                <i class="fa fa-angle-right" style=""></i>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="mt20">-->
<!--                            <div id="yszkChats" style="width: 100%;height: 300px"></div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
            <!--            <div class="mt10 boxs">-->
            <!--                <div class="row">-->
            <!--                    <div class="col-sm-5">-->
            <!--                        <div class="over">-->
            <!--                            <div class="fl fw">总订单数、营业额统计表</div>-->
            <!--                            <div class="fr fc1ab cur">-->
            <!--                                <span>查看详情</span>-->
            <!--                                <i class="fa fa-angle-right" style=""></i>-->
            <!--                            </div>-->
            <!--                        </div>-->
            <!--                        <div class="mt20">-->
            <!--                            <div id="zddChats" style="width: 100%;height: 300px"></div>-->
            <!--                        </div>-->

            <!--                    </div>-->
            <!--                    <div class="col-sm-1"></div>-->
            <!--                    <div class="col-sm-6">-->
            <!--                        <div class="over">-->
            <!--                            <div class="fl fw">项目部月末累积逾期账款统计表</div>-->
            <!--                            <div class="fr fc1ab cur">-->
            <!--                                <span>查看详情</span>-->
            <!--                                <i class="fa fa-angle-right" style=""></i>-->
            <!--                            </div>-->
            <!--                        </div>-->
            <!--                        <div class="mt20">-->
            <!--                            <div id="yqzkChats" style="width: 100%;height: 300px"></div>-->
            <!--                        </div>-->
            <!--                    </div>-->
            <!--                </div>-->
            <!--            </div>-->
        </div>
        <!--    财务中心-->
        <div shiro:hasAnyRoles="cn_cw,xmkj_cw,admin">
            <div class="maintitle">财务中心</div>
            <div class="sm">
                <span class="sm_icon">!</span>
                <span class="sm_text">提醒：带红色角标的菜单按钮表示有待处理即将过期，请及时审核</span>
            </div>
            <div class="boxs">
                <div shiro:hasAnyRoles="cn_cw,admin">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="tc cw" onclick="financePayDetailTab(0)">
                                <span>单笔现金&nbsp;&nbsp;</span>
                                <span class="fc1ab" id="cashPay">0</span>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="tc cw" onclick="financePayDetailTab(1)">
                                <span>单笔油卡&nbsp;&nbsp;</span>
                                <span class="fc1ab" id="oilPay">0</span>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="tc cw" onclick="paySheetTab(0)">
                                <span>月结现金&nbsp;&nbsp;</span>
                                <span class="fc1ab" id="cashPaySheet">0</span>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="tc cw" onclick="paySheetTab(1)">
                                <span>月结油卡&nbsp;&nbsp;</span>
                                <span class="fc1ab" id="oilPaySheet">0</span>
                            </div>
                        </div>
                    </div>
                    <div class="row mt10">
                        <div class="col-sm-3 rel">
                            <div class="tc cw" onclick="paySheetTab()">
                                <span>三方单笔&nbsp;&nbsp;</span>
                                <span class="fc1ab" id="OtherFee">0</span>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="tc cw" onclick="otherFeeSheetTab()">
                                <span>三方对账&nbsp;&nbsp;</span>
                                <span class="fc1ab" id="OtherFeeSheet">0</span>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="tc cw" onclick="driverCollectionTab()">
                                <span>司机代收&nbsp;&nbsp;</span>
                                <span class="fc1ab" id="driverCollection">0</span>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="tc cw" onclick="receBillingTab('6','0')">
                                <span>现金收款核销&nbsp;&nbsp;</span>
                                <span class="fc1ab" id="receBilling">0</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div shiro:hasAnyRoles="xmkj_cw,admin">
                    <div class="row mt10">
                        <div class="col-sm-3">
                            <div class="tc cw" onclick="receSheetRecordTab('0')">
                                <span>收款申请审核&nbsp;&nbsp;</span>
                                <span class="fc1ab" id="receSheetRecordWaitCheck">0</span>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="tc cw" onclick="receBillingTab('2,3,4,5,7',null,'0')">
                                <span>已申请待开票&nbsp;&nbsp;</span>
                                <span class="fc1ab" id="applyInvoicing">0</span>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="tc cw" onclick="receBillingTab(null,'0,3')">
                                <span>已申请待收款&nbsp;&nbsp;</span>
                                <span class="fc1ab" id="appliedPayment">0</span>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="tc cw" onclick="draftCtTab()">
                                <span>待处理汇票&nbsp;&nbsp;</span>
                                <span class="fc1ab" id="draftCt">0</span>
                            </div>
                            <div class="tool" id="draftCt_Warn">0</div>
                        </div>
                    </div>
                    <div class="row mt10">
                        <div class="col-sm-3">
                            <div class="tc cw" onclick="payDetailAdjustCheckTab('2')">
                                <span>单笔付款调账审核&nbsp;&nbsp;</span>
                                <span class="fc1ab" id="singleAdjustCheck">0</span>
                            </div>
                            <div class="tool" id="singleAdjustCheck_warn">0</div>
                        </div>

                        <div class="col-sm-3">
                            <div class="tc cw" onclick="payCheckSheetAdjustCheckTab('2')">
                                <span>月度付款调账审核&nbsp;&nbsp;</span>
                                <span class="fc1ab" id="packageAdjustCheck">0</span>
                            </div>
                            <div class="tool" id="packageAdjustCheck_warn">0</div>

                        </div>
                        <div class="col-sm-3">
                            <div class="tc cw" onclick="otherFeeAdjustCheckTab('2')">
                                <span>三方单笔调账审核&nbsp;&nbsp;</span>
                                <span class="fc1ab" id="otherFeeAdjustCheck">0</span>
                            </div>
                            <div class="tool" id="otherFeeAdjustCheck_warn">0</div>
                        </div>
                    </div>
                    <div class="row mt10">
                        <div class="col-sm-3">
                            <div class="tc cw" onclick="receiveDetailAdjustCheckTab('2')">
                                <span>应收单笔调账审核&nbsp;&nbsp;</span>
                                <span class="fc1ab" id="operationReceAdjustCWCheck">0</span>
                            </div>
                            <div class="tool" id="operationReceAdjustCheckCW_warn">0</div>

                        </div>
                        <div class="col-sm-3">
                            <div class="tc cw" onclick="receiveCheckSheetCheckTab('2')">
                                <span>应收月度调账审核&nbsp;&nbsp;</span>
                                <span class="fc1ab" id="operationReceAdjustSheetCWCheck">0</span>
                            </div>
                            <div class="tool" id="operationReceAdjustSheetCheckCW_warn">0</div>

                        </div>
                    </div>

                </div>
<!--                财务图表-->
                <div class=" row mt20">
                    <div class="col-sm-12">
                        <div class="over">
                            <div class="fl fw">项目部开票收款数据</div>
                            <div class="fr fc1ab cur" style="padding-right: 15px">
                                <span>查看详情</span>
                                <i class="fa fa-angle-right" style=""></i>
                            </div>
                        </div>
                        <div class="mt20">
                            <div id="kpskChats" style="width: 100%;height: 400px"></div>
                        </div>
                    </div>

                </div>
                <div style="width: 100%" class="mt20">
                    <div class="over">
                        <div class="fl fw">销售额类别占比</div>
                        <div class="fr fc1ab cur">
                            <span>查看详情</span>
                            <i class="fa fa-angle-right" style=""></i>
                        </div>
                    </div>
                    <div class="row mt10">
                        <div class="col-sm-6">
                            <div class="over">
                                <div class="over fl">
                                    <div class="fl act week" onclick="getXslbChats(0)">铭源</div>
                                    <div class="fl week" onclick="getXslbChats(1)">吉华</div>
                                    <div class="fl week" onclick="getXslbChats(2)">其他</div>
                                </div>
                                <div class="fr" >
                                    <div class="inputdiv">

                                        <input type="text" placeholder="" class="form-control"style="width: 6em"
                                               id="payDate"  autocomplete="off">
                                        <i class="fa fa-calendar" style="color: #bdbdbd;line-height: 24px"></i>
                                    </div>
                                    <!--                                <div class="fl" style="width: 180px">-->
                                    <!--                                    <input type="text" placeholder="" class="form-control"-->
                                    <!--                                           id="payDate"  autocomplete="off">-->
                                    <!--                                </div>-->
                                    <!--                                <div class="fl" style="line-height: 24px;">-->
                                    <!--                                    <i class="fa fa-calendar" style="color: #bdbdbd"></i>-->
                                    <!--                                </div>-->
                                </div>

                            </div>
                            <div class="xsr mt10" id="xslbAll">
                                <div class="fw line24">合计总额：￥0</div>
                                <div class="over">
                                    <div class="line24 f13 fl" style="width: 50%">0%&nbsp;&nbsp;￥0</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="" id="xslbChats" style="width: 100%;height: 300px"></div>
                        </div>
                    </div>

                </div>

            </div>
        </div>
        <!--    外包业务-->
        <div shiro:hasAnyRoles="logisticsPark,logisticsPark2">
            <div class="maintitle">外包业务</div>
            <div class="boxs over">
                <div class="wb fl" onclick="waybillTab()">
                    <div class="wb_img">
                        <img th:src="@{/img/bz4.png}" />
                    </div>
                    <div class="tc">台账录入</div>
                </div>
                <div class="wb fl" onclick="parkDataInvoiceTab(null)">
                    <div class="wb_img">
                        <img th:src="@{/img/bz3.png}" />
                    </div>
                    <div class="tc">快速导入运单</div>
                </div>
                <div class="wb fl" onclick="parkDataInvoiceTab(0)">
                    <div class="wb_img">
                        <img th:src="@{/img/bz2.png}" />
                        <div class="tool_r" id="receiptConfirmCount">0</div>
                    </div>
                    <div class="tc">待上传回单</div>

                </div>
                <div class="wb fl" onclick="parkDataInvoiceTab(null,0)">
                    <div class="wb_img">
                        <img th:src="@{/img/bz1.png}" />
                        <div class="tool_r" id="isAddReceCheckCount">0</div>

                    </div>
                    <div class="tc">待收款申请</div>
                </div>
                <div class="wb fl" onclick="parkDataInvoiceTab(null,null,0)">
                    <div class="wb_img">
                        <img th:src="@{/img/bz.png}" />
                        <div class="tool_r" id="isAddPayCheckCount">0</div>

                    </div>
                    <div class="tc">待付款申请</div>
                </div>
            </div>
        </div>
        <!--审核-->
        <div shiro:hasAnyRoles="xxb,admin">
            <div class="maintitle">审核</div>
            <div class="boxs">
                <div class="row">
                    <div class="col-sm-3">
                        <div class="tc cw" onclick="carrierCheckTab()">
                            <span>待审核承运商&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="carrierCheckCt">0</span>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="tc cw" onclick="clientCheckTab()">
                            <span>待审核客户&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="clientCheckCt">0</span>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="tc cw" onclick="goodsCheckTab()()">
                            <span>待审核货品&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="goodsCheckCt">0</span>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <!--    车队-->
        <div shiro:hasAnyRoles="fleet_finance,admin">
            <div class="maintitle">车队</div>
            <div class="boxs">
                <div class="row">
                    <div class="col-sm-3">
                        <div class="tc cw" onclick="fleetInvoiceTab()">
                            <span>待确认运单&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="fleetInvoiceNewCount">0</span>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="tc cw" onclick="fleetToDispatchTab()">
                            <span>待调度运单&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="fleetToDispatchCount">0</span>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="tc cw" onclick="fleetInvoiceWriteOffTab()">
                            <span>待收款运单&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="fleetInvoiceWriteOffCount">0</span>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="tc cw" onclick="fleetReceCheckSheetTab()">
                            <span>待开票运单&nbsp;&nbsp;</span>
                            <span class="fc1ab" id="fleetPendingApplicationCount">0</span>
                        </div>
                    </div>
                </div>

            </div>
        </div>


        <div th:if="${1==2}">
            <!--    质控-->
            <div>
                <div class="maintitle">质控</div>
                <div class="mt10 boxs">
                    <div class="row">
                        <div class="col-sm-5">
                            <div class="over">
                                <div class="fl fw">项目部运单待调度统计表</div>
                                <div class="fr fc1ab cur">
                                    <span>查看详情</span>
                                    <i class="fa fa-angle-right" style=""></i>
                                </div>
                            </div>
                            <div class="mt20">
                                <div id="ddChats" style="width: 100%;height: 400px"></div>
                            </div>
                        </div>
                        <div class="col-sm-1"></div>
                        <div class="col-sm-6">
                            <div class="over">
                                <div class="fl fw">项目部运营汇总统计表</div>
                                <div class="fr fc1ab cur">
                                    <span>查看详情</span>
                                    <i class="fa fa-angle-right" style=""></i>
                                </div>
                            </div>
                            <div class="mt20">
                                <div id="yyhzChats" style="width: 100%;height: 450px"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt10 boxs">
                    <div class="row">
                        <div class="col-sm-5">
                            <div class="over">
                                <div class="fl fw">运单调度不合规统计表</div>
                                <div class="fr fc1ab cur">
                                    <span>查看详情</span>
                                    <i class="fa fa-angle-right" style=""></i>
                                </div>
                            </div>
                            <div class="mt20">
                                <div class="over">
                                    <div class="fl act week">按日</div>
                                    <div class="fl week">按月</div>
                                    <div class="fl week">按年</div>
                                </div>
                            </div>
                            <div class="mt20">
                                <div id="bhgChats" style="width: 100%;height: 350px"></div>
                            </div>
                        </div>
                        <div class="col-sm-1"></div>
                        <div class="col-sm-6">
                            <div class="over">
                                <div class="fl fw">项目部运单不合规统计表</div>
                                <div class="fr fc1ab cur">
                                    <span>查看详情</span>
                                    <i class="fa fa-angle-right" style=""></i>
                                </div>
                            </div>
                            <div class="mt20">
                                <div class="over">
                                    <div class="fl act week">按日</div>
                                    <div class="fl week">按月</div>
                                    <div class="fl week">按年</div>
                                </div>
                            </div>
                            <div class="mt20">
                                <div id="ydbhgChats" style="width: 100%;height: 350px"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt10 boxs">
                    <div class="row">
                        <div class="col-md-8 col-sm-12">
                            <div class="fw">项目部不合规运单原因汇总表</div>
                            <div class="mt20">
                                <div id="ydyyChats" style="width: 100%;height: 350px"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt10 boxs">
                    <div class="row">
                        <div class="col-sm-5">
                            <div class="over">
                                <div class="fl fw">调度组整车订单指导价偏离统计表</div>
                                <div class="fr fc1ab cur">
                                    <span>查看详情</span>
                                    <i class="fa fa-angle-right" style=""></i>
                                </div>
                            </div>
                            <div class="mt20">
                                <div id="zdjChats" style="width: 100%;height: 300px"></div>
                            </div>
                        </div>
                        <div class="col-sm-1"></div>
                        <div class="col-sm-6">
                            <div class="th">
                                <div class="over" style="width: 180px;margin: 0 auto">
                                    <div class="fl">
                                        <img th:src="@{/img/err.png}" style="width: 24px;height: 24px" />
                                    </div>
                                    <div class="fl line24">提货预付油卡未充值</div>
                                </div>
                                <div class="mt10 f16 fw fcf31 tc">17</div>
                            </div>
                            <div class="th mt10">
                                <div class="over" style="width: 150px;margin: 0 auto">
                                    <div class="fl">
                                        <img th:src="@{/img/err.png}" style="width: 24px;height: 24px" />
                                    </div>
                                    <div class="fl line24">异常、投诉汇总</div>
                                </div>
                                <div class="mt10 f16 fw fcf31 tc">17</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--    G7运营-->
            <div>
                <div class="maintitle">G7运营</div>
                <div class="boxs">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="th">
                                <div class="tc">不合规运单数</div>
                                <div class="mt10 fw fc1ab tc" style="font-size: 18px">17</div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="th">
                                <div class="tc">提货不合规</div>
                                <div class="mt10 fw fc1ab tc" style="font-size: 18px">17</div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="th">
                                <div class="tc">到货不合规</div>
                                <div class="mt10 fw fc1ab tc" style="font-size: 18px">17</div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt10">
                        <div class="col-sm-6">
                            <div class="cur" style="padding: 10px 0;background: #F5F7FA">
                                <div class="tc">
                                    <span>不合格车辆</span>
                                    <span class="fw fc1ab">17</span>
                                </div>

                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="cur" style="padding: 10px 0;background: #F5F7FA">
                                <div class="tc">
                                    <span>不合格司机</span>
                                    <span class="fw fc1ab">17</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="boxs mt10">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="over">
                                <div class="fl fw">运单合规率统计表</div>
                                <div class="fr fc1ab cur">
                                    <span>查看详情</span>
                                    <i class="fa fa-angle-right" style=""></i>
                                </div>
                            </div>
                            <div class="mt20">
                                <div class="over">
                                    <div class="fl act week">按日</div>
                                    <div class="fl week">按月</div>
                                    <div class="fl week">按年</div>
                                </div>
                            </div>
                            <div class="mt20">
                                <div id="ydhgChats" style="width: 100%;height: 300px"></div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="over">
                                <div class="fl fw">合规运单同比、环比统计表</div>
                                <div class="fr fc1ab cur">
                                    <span>查看详情</span>
                                    <i class="fa fa-angle-right" style=""></i>
                                </div>
                            </div>
                            <div class="" style="margin-top: 60px">
                                <div class="" id="tbChats" style="width: 100%;height: 300px"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
<!--    搜索发货单-->
<div th:if="${isShipper}">
    

    <div class="flex">
        <div class="box-left" shiro:hasPermission="owner:bill:list">
            <div class="form-content" style="background: #fff;padding:16px;">
                <div class="mt10">
                    <div style="font-size: 24px;">运单BI分析</div>
                </div> 
            </div>

            <div class="iframe-mainT mt5" style="display: flex;padding-bottom: 5px;border-bottom:1px solid #cdcdcd;">
                
                <div style="flex: 1;">
                    <div class="over">
                        <div class="fl ml20">运单状态分析</div>
                    </div>
                    <div class="mt5 over">
                        <div class="over fl" style="margin-left: 12%;">
                            <div class="fl act week" onclick="getbicChats(0)">今日</div>
                            <div class="fl week" onclick="getbicChats(1)">本周</div>
                            <div class="fl week" onclick="getbicChats(2)">本月</div>
                        </div>
                    </div>
                    <div id="bicChats" style="width: 100%;height: calc(100% - 45px)"></div>
                </div>
                
            </div>

            <div class="iframe-mainT mt5" style="display: flex;">
                
                <div style="flex: 1;">
                    <div class="over">
                        <div class="fl ml20">对账状态分析</div>
                    </div>
                    <div class="mt5 over">
                        <div class="over fl" style="margin-left: 12%;">
                            <!-- <div class="fl act week" onclick="getbiChats(1)">本周</div>
                            <div class="fl week" onclick="getbiChats(2)">本月</div> -->
                            <div class="fl act week" onclick="getbiChats(3)">本季度</div>
                            <div class="fl week" onclick="getbiChats(4)">本年度</div>
                        </div>
                    </div>
                    <div id="biChats" style="width: 100%;height: calc(100% - 45px)"></div>
                </div>
               
            </div>

            
        </div>
        
        
        <div class="box-right">
            <div class="form-content" style="background: #fff;padding:16px;">
                <div class="mt10">
                    <div class="row">
                        <div class="col-md-3"></div>
                        <div class="col-md-5" style="padding-right: 0">
                            <div class="form-group">
                                <input name="vbillno" placeholder="请输入发货单查询" id="vbillno" class="form-control" type="text" 
                                    style="border: 1px solid #e5e6e7;line-height: 30px;height: 30px;border-radius: 4px;padding: 2px 16px ;" aria-required="true">
                            </div>
                        </div>
                        <div class="col-md-1" style="padding-left: 5px">  
                            <div class="form-group">
                                <a class="btn btn-primary btn-sm" id="search" onclick="searchPre();" style="padding:5px 16px;">搜索</a>
                            </div>
                        </div>
                    </div>
                </div> 
            </div>

            <div class="iframe-main mt10">
                <iframe id="sunPage" width="100%" height="100%" frameborder="no" border="0" marginwidth="0" marginheight="0" scrolling="no" allowtransparency="yes"></iframe>
            </div>
        </div>
    </div>
</div>



<!--客户营业额-->
<div th:fragment="custRank">
    <tr th:each="cust,stat : ${custTransFeeCountRank}">
        <td>
            <div class="pm pmact"
                 th:if="${stat.count == 1 || stat.count==2 || stat.count ==3}"
                 th:text="${stat.count}"></div>
            <div class="pm"
                 th:if="${stat.count != 1 && stat.count!=2 && stat.count !=3}"
                 th:text="${stat.count}"></div>
        </td>
        <td style="text-align: left">
            <div class="" th:text="${cust.custAbbr}"></div>
        </td>
        <td style="text-align: right">
            <div class="" th:text="|¥ ${cust.transFeeCount}|">¥ 27,091</div>
        </td>
        <td>
            <div class="" th:if="${cust.ratio >= 0}">
                <span class="fcf31" th:text="|${#numbers.formatDecimal(cust.ratio * 100,0,2)}%|">0%</span>
                <i class="fa fa-caret-up fcf31" style=""></i>
            </div>
            <div class="" th:if="${cust.ratio < 0}">
                <span class="fc1ab" th:text="|${#numbers.formatDecimal(cust.ratio * 100,0,2)}%|">0%</span>
                <i class="fa fa-caret-down fc1ab" style=""></i>
            </div>

        </td>
    </tr>
</div>

<th:block th:include="include :: footer"/>

<script th:src="@{/js/plugins/metisMenu/jquery.metisMenu.js}"></script>
<script th:src="@{/js/plugins/slimscroll/jquery.slimscroll.min.js}"></script>
<script th:src="@{/js/jquery.contextMenu.min.js}"></script>
<script th:src="@{/ruoyi/index.js}"></script>
<th:block th:include="include :: echarts-js" />
<!--<script type="text/javascript" th:src="@{/js/echarts.min.js}"></script>-->
<script th:inline="javascript">
    var height1 = $('#table').height()
    $('#table1').height(height1)
    /**
     * 搜索发货单编号
     */
    function searchPre() {
        var vbillno=$("#vbillno").val()
        $.ajax({
            url: ctx + "invoice/getInvoiceByVbillno",
            method: 'post',
            dataType: "json",
            data: {vbillno},
            success: function (result) { 
                if(result.code==0){
                    let url = ctx + "owner/order/selectPath?orderId=" + result.data.invoiceId;
                    $('#sunPage').attr('src', url);

                }else{
                    $.modal.alertError(result.msg)
                    $('#sunPage').attr('src', ctx + "owner/order/selectPath?orderId=1");
                }
            }
        });
    }

    function toggleText(link,size) {
        $(link).text(function(i, text){
            return text === `展开（${size}条信息）` ? "隐藏" : `展开（${size}条信息）`;
        });
    }


    // function fadeOutLeft(t) {
    //         let flex=$(t).parent().parent().css("flex");
    //         if(flex=="6 1 0%"){
    //             $(t).parent().parent().css( 'flex','.1')
    //             $(t).removeClass("glyphicon-indent-left")
    //             $(t).addClass("glyphicon-indent-right")
    //         }else{
    //             $(t).parent().parent().css( 'flex','6')
    //             $(t).removeClass("glyphicon-indent-right")
    //             $(t).addClass("glyphicon-indent-left")
    //         }   
    //     }


    /**
     * 客户营业额涨幅纵览统计    图表
     */
    var khyyezfChats;
    var khyyezfChatsOption;
    function khyyezfChatsInit(){
        khyyezfChats = echarts.init(document.getElementById("khyyezfChats"))
        khyyezfChatsOption = {
            tooltip: {
                trigger: 'item'
            },
            legend: {
                orient: 'vertical',
                right: 0,
                top:'center',
                data: ['营业额增加客户','营业额减少客户','涨幅稳定客户'],
                formatter: function(name) {
                    // 获取legend显示内容
                    let data = khyyezfChatsOption.series[0].data;
                    let total = 0;
                    let tarValue = 0;
                    for (let i = 0, l = data.length; i < l; i++) {
                        total += data[i].value;
                        if (data[i].name == name) {
                            tarValue = data[i].value;
                        }
                    }
                    let p = total == 0? 0 : (tarValue / total * 100).toFixed(2);
                    return name + ' ' + ' |' + ' '  + p + '%' + '\xa0\xa0\xa0\xa0' + tarValue;
                },
            },
            series: [
                {
                    type: 'pie',
                    radius: ['40%', '60%'],
                    center: ['20%', '50%'],
                    avoidLabelOverlap: false,
                    label: {
                        normal: {
                            show: true,
                            position: 'center',
                            formatter:  function (){
                                let data = khyyezfChatsOption.series[0].data;
                                let total = 0
                                for (let i = 0, l = data.length; i < l; i++) {
                                    total += data[i].value;
                                }
                                return  '{active|客户总量}'+ '\n\r' + '{total|' + total +'}'
                            },
                            rich: {
                                total:{
                                    fontSize: 20,
                                    fontWeight: 'bold',
                                    color:'#333'
                                },
                                active: {
                                    fontSize: 13,
                                    color:'#808080',
                                    lineHeight:24,
                                },
                            }
                        },
                    },
                    labelLine: {
                        show: false
                    },
                    data: [
                        { value: 0, name: '营业额增加客户' },
                        { value: 0, name: '营业额减少客户' },
                        { value: 0, name: '涨幅稳定客户' },
                    ]
                }
            ],
            color: ["#F2637B", "#4DCB73", "#3AA0FF"],
        };
        khyyezfChats.setOption(khyyezfChatsOption);
    }

    /**
     * 账款统计
     */
    var zkxxChats;
    var zkxxChatsOption;
    function zkxxChatsInit(){
        zkxxChats = echarts.init(document.getElementById("zkxxChats"))
        zkxxChatsOption = {
            legend: {
                data: ['应对账','应申请开票','应收款','逾期未对账','逾期未申请开票','逾期未收款'],
                right: '10%',

            },
            tooltip: {
                trigger: 'axis',
                formatter: function(params){
                    console.log(params)
                    var showHtm="";
                    showHtm = params[0].name
                    for(var i=0;i<params.length;i++){
                        if(i<3){
                            var name = params[i].name;

                            // var text = params[i][3];
                            var value = params[i].value
                            var value1 = params[i+3].value
                            if(value1<0){
                                value1 = -value1
                            }else if(value1>0){
                                value1 = value1
                            }else{
                                value1=0
                            }
                            var seriesName = params[i].seriesName
                            showHtm+= '</br>' + seriesName+'：' + value +'(逾期' + value1 + ')'
                        }
                    }
                    return showHtm;
                },
            },
            xAxis: {
                data: [],
                name: '',
                axisLine: { onZero: true },
                splitLine: { show: false },
                splitArea: { show: false },
                axisLabel: {
                    interval:0,
                    rotate:50//角度顺时针计算的
                }
            },
            yAxis: {
                type : 'value',
                axisLabel:{
                    formatter:function(value){
                        if (value<0) {
                            return -value;

                        }else{
                            return value;
                        }
                    }
                }
            },
            grid: {
                bottom: 100
            },
            series: [

            ],
            color: ["#0083FF", "#f3b73f", "#b7c846","#0083FF", "#f3b73f", "#b7c846"],
        };
        zkxxChats.setOption(zkxxChatsOption);
    }
    /**
     * 超指导价、亏损统计表
     */
    var czdjCharts;
    var czdjChatsOption;
    function czdjChartsInit(){
        czdjCharts = echarts.init(document.getElementById("czdjChats"))
        czdjChatsOption = {
            tooltip: {
                trigger: 'axis'
            },
            grid:{

                bottom:100
            },
            legend: {
                data: ['超指导价', '亏损','低毛利'],
                right: '10%',
            },
            toolbox: {
                show: true,
            },
            xAxis:
                {
                    type: 'category',
                    data: [],
                    axisLine: { onZero: true },
                    splitLine: { show: false },
                    splitArea: { show: false },
                    axisLabel: {
                        interval:0,
                        rotate:50//角度顺时针计算的
                    }
                }
            ,
            yAxis: [
                {
                    type: 'value'
                }
            ],
            series: [
            ],
            color: ["#3792E8", "#FFB500","#F2637B"],
        };
        czdjCharts.setOption(czdjChatsOption);
    }

    /**
     * 项目部开票收款数据
     */
    var kpskChats;
    var kpskChatsOption;
    function kpskChatsInit() {
        kpskChats = echarts.init(document.getElementById("kpskChats"))
        kpskChatsOption = {
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['未开票', '逾期未收款'],
                right: '10%',
            },
            toolbox: {
                show: true,
            },
            xAxis: {
                type: 'category',
                data: [],
                axisLine: { onZero: true },
                splitLine: { show: false },
                splitArea: { show: false },
                axisLabel: {
                    interval:0,
                    rotate:20//角度顺时针计算的
                }
            },
            yAxis: [
                {
                    type: 'value',
                    axisLabel:{
                        formatter: function (value) {
                            return bigNumberTransform(value);

                        }
                    }

                }
            ],
            dataZoom:[{
                realtime:true, //拖动滚动条时是否动态的更新图表数据
                height:25,//滚动条高度
                start:0,//滚动条开始位置（共100等份）
                end:50//结束位置（共100等份）
            },{
                type: "inside", //内置滑动，随鼠标滚轮展示
                realtime: true,
                start: 0,
                end: 20,
            },
            ],
            series: [],
            color: ["#3792E8", "#FFB500"],
        };
        kpskChats.setOption(kpskChatsOption);
    }

    /**
     * 销售额类别占比
     */
    var xslbChats;
    var xslbChatsOption;
    function xslbChatsInit(){
        xslbChats = echarts.init(document.getElementById("xslbChats"))
        xslbChatsOption = {
            tooltip: {
                trigger: 'item',
                // formatter: '{b}<br />{c}'
                formatter: function (params){
                    //console.log(params)
                    return params.name + '\xa0\xa0\xa0\xa0' + bigNumberTransform(params.value)
                }
            },
            legend: {
                orient: 'vertical',
                right: '0',
                top:'center',
                data: [],
                formatter: function(name) {
                    // 获取legend显示内容
                    let data = xslbChatsOption.series[0].data;
                    let total = 0;
                    let tarValue = 0;
                    for (let i = 0, l = data.length; i < l; i++) {
                        total += data[i].value;
                        if (data[i].name == name) {
                            tarValue = data[i].value;
                        }
                    }
                    let p = total == 0? 0 :(tarValue / total * 100).toFixed(2);
                    return name + '\n' + p + '%' + ' ' + '￥' + bigNumberTransform(tarValue);
                    // return name + ' ' + ' |' + ' '  + p + '%' + '\xa0\xa0\xa0\xa0' + '￥' + bigNumberTransform(tarValue) + '\xa0\xa0\xa0\xa0';
                },
            },
            series: [
                {
                    name: '',
                    type: 'pie',
                    radius: ['45%', '60%'],
                    center: ['30%', '50%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 7,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        normal: {
                            show: true,
                            position: 'center',
                            formatter: function (){
                                let data = xslbChatsOption.series[0].data;
                                let total = 0
                                for (let i = 0, l = data.length; i < l; i++) {
                                    total += data[i].value;
                                }
                                return  '{active|总额}'+ '\n\r' + '{total|' + bigNumberTransform(total) +'}'
                            },
                            rich: {
                                total:{
                                    fontSize: 16,
                                    fontWeight: 'bold',
                                    color:'#333'
                                },
                                active: {
                                    fontSize: 13,
                                    color:'#808080',
                                    lineHeight:24,
                                },
                            }
                        },
                    },
                    labelLine: {
                        show: false
                    },
                    data: [
                        // { value: 1048, name: 'G7金额' },
                        // { value: 735, name: '琦欣金额' },
                        // { value: 580, name: '银联金额' },
                        // { value: 580, name: '油卡' },
                    ]
                }
            ],
            color: ["#3BA0FF", "#4DCB73", "#F2637B","#FFB500"],
        };
        xslbChats.setOption(xslbChatsOption);
    }

    /**
     * BI分析类别占比
     */
    var biChats;
    var biChatsOption;
    function biChatsInit(){
        biChats = echarts.init(document.getElementById("biChats"))
        biChatsOption = {
            title: {
                text: '',
                right: '4%',
                bottom:'6%',
                textStyle:{
                    fontWeight:'500',
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: function (params){
                    return params.name + ':\xa0\xa0\xa0\xa0' + params.value+'单'
                }
            },
            legend:{
                data: [],
                show:false
            },
            series: [
                {
                    name: '',
                    type: 'pie',
                    radius: '60%',
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 7,
                        borderColor: '#fff',
                        borderWidth: 2,
                    }, 
                    label: {
                        alignTo: 'edge',
                        formatter: function(obj) {
                            let data = biChatsOption.series[0].data;
                            let total = 0;
                            let tarValue = 0;
                            for (let i = 0, l = data.length; i < l; i++) {
                                total += data[i].value;
                                if (data[i].name == obj.name) {
                                    tarValue = data[i].value;
                                }
                            }
                            let p = total == 0? 0 :(tarValue / total * 100).toFixed(2);
                            return obj.name +':  '+ tarValue +'单  '+ p + '%' ;  
                        },
                        minMargin: 5,
                        lineHeight: 15,
                    },
                    data: [
                       
                    ],
                    emphasis: {
                        itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }
            ],
            color: ["#3BA0FF", "#4DCB73", "#F2637B","#FFB500"],
        };
        biChats.setOption(biChatsOption);
    }

    var bicChats;
    var bicChatsOption;
    function bicChatsInit(){
        bicChats = echarts.init(document.getElementById("bicChats"))
        bicChatsOption = {
            title: {
                text: '',
                right: '4%',
                bottom:'6%',
                textStyle:{
                    fontWeight:'500',
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: function (params){
                    return params.name + ':\xa0\xa0\xa0\xa0' + params.value+'单'
                }
            },
            legend:{
                data: [],
                show:false
            },
            series: [
                {
                    name: '',
                    type: 'pie',
                    radius: '60%',
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 7,
                        borderColor: '#fff',
                        borderWidth: 2,
                    }, 
                    label: {
                        alignTo: 'edge',
                        formatter: function(obj) {
                            let data = bicChatsOption.series[0].data;
                            let total = 0;
                            let tarValue = 0;
                            for (let i = 0, l = data.length; i < l; i++) {
                                total += data[i].value;
                                if (data[i].name == obj.name) {
                                    tarValue = data[i].value;
                                }
                            }
                            let p = total == 0? 0 :(tarValue / total * 100).toFixed(2);
                            return obj.name +':  '+ tarValue +'单  '+ p + '%' ;  
                        },
                        minMargin: 5,
                        lineHeight: 15,
                    },
                    data: [
                       
                    ],
                    emphasis: {
                        itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }
            ],
            color: ["#3BA0FF", "#4DCB73", "#F2637B","#FFB500"],
        };
        bicChats.setOption(bicChatsOption);
    }


    /**
     * 订单调度统计表
     */
    var ddtjMyChart;
    var ddtjChatsOption;
    function ddtjChartInit(){
        ddtjMyChart = echarts.init(document.getElementById("ddtjChats"));
        ddtjChatsOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    crossStyle: {
                        color: '#999'
                    }
                }
            },
            toolbox: {
                feature: {

                }
            },
            legend: {
                data: ['整车调度', '零担调度','超指导价','油卡比例','月结比例']
            },
            xAxis:
                {
                    type: 'category',
                    data: [],
                    axisPointer: {
                        type: 'shadow'
                    },
                    axisLabel: {
                        interval:0,
                        rotate:30//角度顺时针计算的
                    }
                }
            ,
            yAxis: [
                {
                    type: 'value',
                    name: '',
                },
                {
                    type: 'value',
                    name: '',
                    // min: 0,1
                    // max: 50,
                    // interval: 10,
                    axisLabel: {
                        formatter: '{value} %'
                    }
                }
            ],
            // dataZoom:[{
            //     realtime:true, //拖动滚动条时是否动态的更新图表数据
            //     height:25,//滚动条高度
            //     start:0,//滚动条开始位置（共100等份）
            //     end:50//结束位置（共100等份）
            // },{
            //     type: "inside", //内置滑动，随鼠标滚轮展示
            //     realtime: true,
            //     start: 0,
            //     end: 20,
            // },
            // ],
            series: [],
            color: ["#3792E8", "#FFB500","#FA7877","#1AB394"],
        };
        ddtjMyChart.setOption(ddtjChatsOption);
    }

    /**
     * 多种运输方式金额统计
     */
    var ysfsChats;
    var ysfsChatsOption;
    function ysfsChatsInit() {
        ysfsChats = echarts.init(document.getElementById("ysfsChats"))
        ysfsChatsOption = {
            tooltip: {
                trigger: 'item',
                formatter: function (params){
                    //console.log(params)
                    return params.name + '\xa0\xa0\xa0\xa0' + bigNumberTransform(params.value)
                }
            },
            legend: {
                orient: 'vertical',
                right: 0,
                top:'center',
                data: ['整车','零担' ,'其他'],
                formatter: function(name) {
                    // 获取legend显示内容
                    let data = ysfsChatsOption.series[0].data;
                    let total = 0;
                    let tarValue = 0;
                    for (let i = 0, l = data.length; i < l; i++) {
                        total += data[i].value;
                        if (data[i].name == name) {
                            tarValue = data[i].value;
                        }
                    }
                    let p = (tarValue / total * 100).toFixed(2);
                    return name + ' ' + ' |' + ' '  + p + '%' + '\xa0\xa0\xa0\xa0' + '￥' + bigNumberTransform(tarValue);
                },
            },
            series: [
                {
                    name: '',
                    type: 'pie',
                    radius: ['45%', '60%'],
                    center: ['20%', '50%'],
                    avoidLabelOverlap: false,
                    label: {
                        normal: {
                            show: true,
                            position: 'center',
                            formatter: function (){
                                let data = ysfsChatsOption.series[0].data;
                                let total = 0
                                for (let i = 0, l = data.length; i < l; i++) {
                                    total += data[i].value;
                                }
                                return  '{active|总额}'+ '\n\r' + '{total|' + bigNumberTransform(total) +'}'
                            },
                            rich: {
                                total:{
                                    fontSize: 16,
                                    fontWeight: 'bold',
                                    color:'#333'
                                },
                                active: {
                                    fontSize: 13,
                                    color:'#808080',
                                    lineHeight:24,
                                },
                            }
                        },
                    },
                    labelLine: {
                        show: false
                    },
                    data: [
                        { value: 0, name: '整车' },
                        { value: 0, name: '零担' },
                        { value: 0, name: '其他' },
                    ]
                }
            ],
            color: ["#3BA0FF", "#4DCB73", "#F2637B"],
        };
        ysfsChats.setOption(ysfsChatsOption);
    }

    /**
     * 项目部应收账款总额统计表
     */
    var yszkMyChart;
    var yszkChatsOption;
    function yszkChatsInit(){
        yszkMyChart = echarts.init(document.getElementById("yszkChats"))
        yszkChatsOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    crossStyle: {
                        color: '#999'
                    }
                }
            },
            toolbox: {
                feature: {

                }
            },
            legend: {
                data: []
            },
            xAxis: [
                {
                    type: 'category',
                    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月','8月', '9月', '10月', '11月','12月'],
                    axisPointer: {
                        type: 'shadow'
                    }
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    name: '',
                    axisLabel:{
                        formatter: function (value) {
                            return bigNumberTransform(value)+'元';

                        }
                    }
                },
                {
                    type: 'value',
                    name: '',
                    // min: 0,
                    // max: 50,
                    // interval: 10,
                    axisLabel: {
                        formatter: '{value} %'
                    }
                }
            ],
            series: [
                // {
                //     name: '2020年',
                //     type: 'bar',
                //     data: [
                //         20, 49
                //     ]
                // },
                // {
                //     name: '2021年',
                //     type: 'bar',
                //     data: [
                //         40, 60, 30, 52, 40, 40, 50, 60, 30, 20, 50, 33
                //     ]
                // },
                // {
                //     name: '同比增长率',
                //     type: 'line',
                //     yAxisIndex: 1,
                //     data: [20, -30, 33]
                // },
                // {
                //     name: '环比增长率',
                //     type: 'line',
                //     yAxisIndex: 1,
                //     data: [10, 40, 23, 45, 40, 30, 30, 40, 10, 46, 30, 30]
                // }
            ],
            color: ["#3792E8", "#FFB500","#1AB394","#FF8400"],
        };
        yszkMyChart.setOption(yszkChatsOption);
    }

    /**
     * 调度完成比率
     */
    var ddblChats;
    var ddblChatsOption;
    function ddblChatsInit(){
        ddblChats = echarts.init(document.getElementById("ddblChats"))

        ddblChatsOption = {
            tooltip: {
                trigger: 'item'
            },
            legend: {
                orient: 'vertical',
                right: 0,
                top:'center',
                data: ['已调度','部分调度','未调度'],
                formatter: function(name) {
                    // 获取legend显示内容
                    let data = ddblChatsOption.series[0].data;
                    let total = 0;
                    let tarValue = 0;
                    for (let i = 0, l = data.length; i < l; i++) {
                        total += data[i].value;
                        if (data[i].name == name) {
                            tarValue = data[i].value;
                        }
                    }
                    let p = total == 0? 0 : (tarValue / total * 100).toFixed(2);
                    return name + ' ' + tarValue + "单";
                },
            },
            series: [
                {
                    type: 'pie',
                    radius: ['50%', '60%'],
                    center: ['30%', '50%'],
                    avoidLabelOverlap: false,
                    label: {
                        normal: {
                            show: true,
                            position: 'center',
                            formatter:  function (){
                                let data = ddblChatsOption.series[0].data;
                                let total = 0
                                for (let i = 0, l = data.length; i < l; i++) {
                                    total += data[i].value;
                                }
                                return  '{active|合计}'+ '\n\r' + '{total|' + total +'单}'
                            },
                            rich: {
                                total:{
                                    fontSize: 20,
                                    fontWeight: 'bold',
                                    color:'#333'
                                },
                                active: {
                                    fontSize: 13,
                                    color:'#808080',
                                    lineHeight:24,
                                },
                            }
                        },
                    },
                    labelLine: {
                        show: false
                    },
                    data: [
                        // { value: 70, name: '已调度' },
                        // { value: 70, name: '部分调度' },
                        // { value: 30, name: '未调度' },
                    ]
                }
            ],
            // color: ["#76c87e", "#e0e5df"],
        };
        ddblChats.setOption(ddblChatsOption);
    }


    /**
     * 项目部营收利润完成
     */
    var yslrwcMyChart;
    var yslrwcChatsOption;
    function yslrwcChatsInit() {
        yslrwcMyChart = echarts.init(document.getElementById("yslrwcChats"));
        yslrwcChatsOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    crossStyle: {
                        color: '#999'
                    }
                }
            },
            toolbox: {
                feature: {

                }
            },
            legend: {
                data: ['营收目标', '营收完成金额','完成率']
            },
            xAxis:
                [{
                    type: 'category',
                    data: [],
                    axisPointer: {
                        type: 'shadow'
                    },
                    axisLabel: {
                        interval:0,
                        rotate:0//角度顺时针计算的
                    }
                },{
                    position: 'bottom',// 将分组x轴位置定至底部，不然默认在顶部
                    offset: 30,// 向下偏移，使分组文字显示位置不与原x轴重叠
                    axisLine: {
                        show: true // 隐藏分组x轴的轴线
                    },
                    axisTick: {
                        length: 30, // 延长刻度线做分组线
                        inside: false, // 使刻度线相对轴线在上面与原x轴相接，默认在轴线下方
                        lineStyle: {color: '#6e7079'},// 非必须，仅为了演示，明显标示出分组刻度线
                        interval: function (index, value) {
                            return value != ''  // 在0、5、6处各画一条刻度线
                        }
                    },
                    axisLabel:{
                        interval:0,
                        align:'center'
                    },
                    data:[]
                }
                ]
            ,
            yAxis: [
                {
                    type: 'value',
                    name: '',
                },
                {
                    type: 'value',
                    name: '',
                    // min: 0,1
                    // max: 50,
                    // interval: 10,
                    axisLabel: {
                        formatter: '{value} %'
                    }
                }
            ],
            series: [],
            color: ["#3792E8", "#FFB500","#FA7877"],
        };
        yslrwcMyChart.setOption(yslrwcChatsOption);
    }

    /**
     * 各调度组完成占比
     */
    var ddzbChats;
    var ddzbChatsOption;
    function ddzbChatsInit() {
        ddzbChats = echarts.init(document.getElementById("ddzbChats"))
        ddzbChatsOption = {
            tooltip: {
                trigger: 'item',
                // formatter: '{b}<br />{c}'
                formatter: function (params){
                    //console.log(params)
                    return params.name + '\xa0\xa0\xa0\xa0' + params.value
                }
            },
            // legend: {
            //   orient: 'vertical',
            //   left: 'left'
            // },
            legend: {
                orient: 'vertical',
                right: 0,
                top:'center',
                data: [],
                formatter: function(name) {
                    // 获取legend显示内容
                    let data = ddzbChatsOption.series[0].data;
                    let total = 0;
                    let tarValue = 0;
                    for (let i = 0, l = data.length; i < l; i++) {
                        total += data[i].value;
                        if (data[i].name == name) {
                            tarValue = data[i].value;
                        }
                    }
                    let p = total == 0? 0 : (tarValue / total * 100).toFixed(2);
                    return name + ' ' + ' |' + ' '  + p + '%' + '\xa0\xa0\xa0\xa0' + tarValue + "单";
                },
            },
            series: [
                {
                    name: 'Access From',
                    type: 'pie',
                    radius: '50%',
                    center: ['20%', '50%'],
                    avoidLabelOverlap: false,
                    label: {
                        show: false,
                        position: 'center'
                    },
                    data: [
                        // { value: 1048, name: '调度1组' },
                        // { value: 735, name: '调度2组' },
                        // { value: 580, name: '调度3组' },
                        // { value: 484, name: '调度4组' },
                        // { value: 300, name: '调度5组' },
                        // { value: 300, name: '调度6组' },
                        // { value: 300, name: '调度7组' },
                        // { value: 300, name: '调度8组' },
                    ],
                }
            ]
        };
        ddzbChats.setOption(ddzbChatsOption);
    }


    $(function (){
        /*
         * 业务中心
         */
        let hasSales = [[${@permission.hasAnyRoles('sales,admin')}]]
        if (hasSales !== 'hidden') {
            //获取待确认发货单数量
            getInvoiceNewCount();
            //获取待提货委托单数量\近一月已完成 提货数量\计算当月提货及时率
            getAffirmEntrustCount()
            //获取待到货的委托单数量\近一月已完成 到货数量\计算当月到货及时率
            getPickUpEntrustCount()
            //查询当天待跟踪的数量
            getNeedTraceTodayCount()
            //查询未回单上传数量\近一月已完成 回单上传数量\近一月及时率 回单上传
            getReceiptStatusCount()
            //查询未正本回单数量\近一月已完成 正本回单数量\近一月及时率 正本回单
            getIfReceiptCount()
            //待对账客户
            getUnreconciledCustomerCount()
            //待申请开票
            getPendingApplicationCount()
            //获取 客户营业额 排名
            // getCustRank(0, 0, "custRank_desc");
            // getCustRank(0, 1, "custRank_asc");
            //客户营业额统计 所有 月环比与月同比
            // getCustRankTotal()

            //客户营业额统计
            // khyyezfChatsInit()
            // getKhyyezfChats(0)
            //账款统计
            // zkxxChatsInit()
            // getZkxxChats()
            //超指导价、亏损统计表
            // czdjChartsInit()
            // getCzdjChats()

            //调度完成比率
            // ddblChatsInit()
            // getDdblChats(0)

            //各调度组完成占比
            // ddzbChatsInit()
            // getDdzbChats(0)

            //项目部营收利润完成
            yslrwcChatsInit()
            getYslrwcChats()
        }
        /*
         * 财务
         */
        let cnCwSales = [[${@permission.hasAnyRoles('cn_cw,admin')}]]
        if (cnCwSales !== 'hidden') {
            //现金
            getPayDetailCt(0, "cashPay")
            //油卡
            getPayDetailCt(1, "oilPay")
            //月结现金
            getPaySheetCt(0,"cashPaySheet")
            //月结油卡
            getPaySheetCt(1,"oilPaySheet")

            //三方单笔
            getOtherFeeCt()
            //三方对账
            getOtherFeeSheetCt()
            //司机代收
            getDriverCollectionCt()
            //现金收款核销
            getReceBillingCt()
        }

        let xmkjCwSales = [[${@permission.hasAnyRoles('xmkj_cw,admin')}]]
        if (xmkjCwSales !== 'hidden') {
            //收款申请审核
            getReceSheetRecordCt(0, "receSheetRecordWaitCheck")
            //已申请开票
            getApplyInvoicingCt()
            //已申请待收款
            getAppliedPaymentCount()
            //待处理汇票
            getDraftCount()
            //单笔付款调账审核
            getSingleAdjustCheckCount()
            //月度付款调账审核
            getPackageAdjustCheckCount()
            //三方单笔调账审核
            getOtherFeeAdjustCheckCount()
            //应收单笔调账审核
            getOperationReceAdjustCheckCWCount()

        }

        if (cnCwSales !== 'hidden' || xmkjCwSales !== 'hidden') {
            var date = new Date();
            date.setTime(date.getTime());
            var year = date.getFullYear();
            var month = ("0" + (date.getMonth() + 1)).slice(-2);
            var curMonth = year + "-" + month;
            $("#payDate").val(curMonth);

            //项目部开票收款数据
            kpskChatsInit()
            getKpskChats();

            //销售额类别占比
            xslbChatsInit()
            getXslbChats(0);

           
        }

        /*
         * 调度
         */
        let dispatcherSales = [[${@permission.hasAnyRoles('dispatcher,admin')}]]
        if (dispatcherSales !== 'hidden') {
            //调度数量
            toDispatchCt()
            //
            driverCt()
            //
            carCt()

            //订单调度统计表
            ddtjChartInit()
            getDdtjChats(0);
        }

        /*
         * 车队 fleet_finance
         */
        let fleetSales = [[${@permission.hasAnyRoles('fleet_finance,admin')}]]
        if (fleetSales !== 'hidden') {
            fleetInvoiceCt()
            fleetSegmentCt()
            fleetInvoiceWriteOffCt()
            fleetReceCheckSheetPendingCt()
        }

        /*
         * 运营管理
         */
        let yyjlSales = [[${@permission.hasAnyRoles('yyjl,admin')}]]
        if (yyjlSales !== 'hidden') {
            getPayDetailCheckCt()
            getPaySheetRecordCt()
            getOtherFeeCheckCt()
            getOtherFeeSheetRecordCt()
            getOperationPayAdjustCheckCount()
            getOperationReceAdjustCheckCount()
            getOperationOtherFeelAdjustCheckCount()
            getOperationGuidePriceCheckCount()
            getCheckExpCount()
            getCheckMarginCount()
            getCheckDepositCount()
            getCheckPayPassCount()
            getSegmentCheckCount(1,"segmentCheckFirstCt")
            getSegmentCheckCount(2,"segmentCheckSecondCt")
            getExceptionTotalCount()

            //多种运输方式金额统计
            // ysfsChatsInit()
            // getYsfsChats(0)

            //项目部应收账款总额统计表
            // yszkChatsInit()
            // getYszkChats()
        }

        /*
         * 审核
         */
        let xxbSales = [[${@permission.hasAnyRoles('xxb,admin')}]]
        if (xxbSales !== 'hidden') {
            getCarrierCheckCount()
            getClientCheckCount()
            getGoodsCheckCount()
        }

        /*
         * 三方业务
         */
        let hasLogisticsPark = [[${@permission.hasAnyRoles('logisticsPark,logisticsPark2')}]]
        if (hasLogisticsPark !== 'hidden') {
            //获取 待上传回单、代收款申请、待付款申请 数量
            getParkDataInvoiceCount();
        }


        if([[${@permission.hasAnyPermi('owner:bill:list')}]] != "hidden"){
            biChatsInit()
            getbiChats(3)

            bicChatsInit()
            getbicChats(0)

            biChats.off('click'); // 防止重复绑定事件
            biChats.on('click', function (obj){
                let dateType=obj.data.dateType;
                if(obj.name=='未对账'){
                    $.modal.openTab("账单信息",ctx + "owner/bill?receiveStatus=0&dateType="+dateType);
                }else  if(obj.name=='已对账'){
                    $.modal.openTab("账单信息",ctx + "owner/bill?receiveStatus=1&dateType="+dateType);
                }else  if(obj.name=='已核销'){
                    $.modal.openTab("账单信息",ctx + "owner/bill?receiveStatus=2&dateType="+dateType);
                }   
            });


        }

        

        $('#sunPage').attr('src', ctx + "owner/order/selectPath?orderId=1");
    })

    // var salesRankList = [[${salesRankList}]];
    // var dispatchRankList = [[${dispatchRankList}]];
    // //调账状态
    // var adjustCheckStatusArr = [[${adjustCheckStatusArr}]];
    //
    // /*
    //  * 运营部累计票数
    //  */
    // var salesRankChart = echarts.init(document.getElementById('salesRank'));
    // var salesDeptNames = salesRankList.map(function(x){return x.DEPT_NAME});
    // var salesCount = salesRankList.map(function(x){return x.COUNT});
    // salesRankOption = {
    //     title: {
    //         text: '运营部累计票数',
    //         subtext: '当日数据'
    //     },
    //     tooltip: {
    //         trigger: 'axis',
    //         axisPointer: {
    //             type: 'shadow'
    //         }
    //     },
    //     grid: {
    //         left: '3%',
    //         right: '4%',
    //         bottom: '3%',
    //         containLabel: true
    //     },
    //     xAxis: {
    //         type: 'value',
    //         boundaryGap: [0, 0.01]
    //     },
    //     yAxis: {
    //         type: 'category',
    //         inverse: true,
    //         data: salesDeptNames,
    //         axisLabel:{
    //             interval: 0
    //         }
    //     },
    //     series: [
    //         {
    //             type: 'bar',
    //             itemStyle: {
    //                 color: '#1ab394'
    //             },
    //             // barWidth: 30,
    //             data: salesCount
    //         }
    //     ]
    // };
    // salesRankChart.setOption(salesRankOption);
    //
    // /*
    //  * 调度组累计票数
    //  */
    // var dispatcherRank = echarts.init(document.getElementById('dispatcherRank'));
    // var dispatchDeptNames = dispatchRankList.map(function(x){return x.DEPT_NAME});
    // var dispatchCount = dispatchRankList.map(function(x){return x.COUNT});
    // dispatcherRankOption = {
    //     title: {
    //         text: '调度组累计票数',
    //         subtext: '当日数据'
    //     },
    //     tooltip: {
    //         trigger: 'axis',
    //         axisPointer: {
    //             type: 'shadow'
    //         }
    //     },
    //     legend: {
    //         data: ['2011年']
    //     },
    //     grid: {
    //         left: '3%',
    //         right: '4%',
    //         bottom: '3%',
    //         containLabel: true
    //     },
    //     xAxis: {
    //         type: 'value',
    //         boundaryGap: [0, 0.01]
    //     },
    //     yAxis: {
    //         type: 'category',
    //         inverse: true,
    //         data: dispatchDeptNames,
    //         axisLabel:{
    //             interval: 0
    //         }
    //     },
    //     series: [
    //         {
    //             type: 'bar',
    //             itemStyle: {
    //                 color: '#1ab394'
    //             },
    //             // barWidth: 30,
    //             data: dispatchCount
    //         }
    //     ]
    // };
    // dispatcherRank.setOption(dispatcherRankOption);

    if (false) {

    var zddChats = document.getElementById("zddChats");
    var zddMyChart = echarts.init(zddChats);
    var zddChatsOption;
    zddChatsOption = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        toolbox: {
            feature: {

            }
        },
        legend: {
            data: ['订单数', '营业额']
        },
        xAxis: [
            {
                type: 'category',
                data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月','8月', '9月', '10月', '11月','12月'],
                axisPointer: {
                    type: 'shadow'
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '',
            },
            {
                type: 'value',
                name: '',
                // min: 0,
                // max: 50,
                // interval: 10,
                axisLabel: {
                    formatter: '{value} 亿'
                }
            }
        ],
        series: [
            {
                name: '订单数',
                type: 'bar',
                data: [
                    20, 49, 50, 32, 25, 20, 35, 20, 40, 20, 50, 33
                ]
            },
            {
                name: '营业额',
                type: 'line',
                yAxisIndex: 1,
                data: [20, 30, 33, 25, 30, 10, 20, 23, 23, 16, 12, 20]
            }
        ],
        color: ["#3792E8", "#FF8400"],
    };
    zddMyChart.setOption(zddChatsOption);


    var yqzkChats = document.getElementById("yqzkChats");
    var yqzkMyChart = echarts.init(yqzkChats);
    var yqzkChatsOption;
    yqzkChatsOption = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        toolbox: {
            feature: {

            }
        },
        legend: {
            data: ['2020年', '2021年','同比增长率','环比增长率']
        },
        xAxis: [
            {
                type: 'category',
                data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月','8月', '9月', '10月', '11月','12月'],
                axisPointer: {
                    type: 'shadow'
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '',
            },
            {
                type: 'value',
                name: '',
                // min: 0,
                // max: 50,
                // interval: 10,
                axisLabel: {
                    formatter: '{value} %'
                }
            }
        ],
        series: [
            {
                name: '2020年',
                type: 'bar',
                data: [
                    20, 49, 50, 32, 25, 20, 35, 20, 40, 20, 50, 33
                ]
            },
            {
                name: '2021年',
                type: 'bar',
                data: [
                    40, 60, 30, 52, 40, 40, 50, 60, 30, 20, 50, 33
                ]
            },
            {
                name: '同比增长率',
                type: 'line',
                yAxisIndex: 1,
                data: [20, 30, 33, 25, 30, 10, 20, 23, 23, 16, 12, 20]
            },
            {
                name: '环比增长率',
                type: 'line',
                yAxisIndex: 1,
                data: [10, 40, 23, 45, 40, 30, 30, 40, 10, 46, 30, 30]
            }
        ],
        color: ["#3792E8", "#FFB500","#1AB394","#FF8400"],
    };
    yqzkMyChart.setOption(yqzkChatsOption);

    var ddChats = document.getElementById("ddChats");
    var ddMyChart = echarts.init(ddChats);
    var ddChatsOption;
    ddChatsOption = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: ['超期待调度']
        },
        xAxis: {
            type: 'category',
            data: ['调度1组', '调度2组', '调度3组', '调度4组', '调度5组', '调度6组', '调度7组','调度8组', '调度9组', '调度10组'],
            axisLine: { onZero: true },
            splitLine: { show: false },
            splitArea: { show: false },
            axisLabel: {
                interval:0,
                rotate:50//角度顺时针计算的
            }
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '超期待调度',
                type: 'bar',
                data: [
                    20, 49, 50, 32, 25, 20, 35, 20, 40, 20
                ]
            },
        ],
        color: ["#3792E8"],
    };
    ddMyChart.setOption(ddChatsOption);

    var yyhzChats = document.getElementById("yyhzChats");
    var yyhzMyChart = echarts.init(yyhzChats);
    var yyhzTotal = 2000
    var yyhzData = [
        {
            name: '项目1部【张三】',
            value: 70
        }, {
            name: '项目2部【张三】',
            value: 68
        }, {
            name: '项目3部【张三】',
            value: 48
        }, {
            name: '项目4部【张三】',
            value: 40
        }, {
            name: '项目5部【张三】',
            value: 32
        }, {
            name: '项目6部【张三】',
            value: 27
        }, {
            name: '项目7部【张三】',
            value: 18
        },
        {
            name: '项目8部【张三】',
            value: 18
        },
        {
            name: '项目9部【张三】',
            value: 18
        },
        {
            name: '项目10部【张三】',
            value: 18
        }
    ];
    var yyhzChatsOption;
    yyhzChatsOption = {
        tooltip: {
            trigger: 'item'
        },
        series: [{
            type: 'pie',
            radius: ['55%', '70%'],
            //center: ['50%', '50%'],
            data: [{
                name: '项目1部【张三】',
                value: 60
            }, {
                name: '项目2部【张三】',
                value: 50
            }, {
                name: '项目3部【张三】',
                value: 48
            }, {
                name: '项目4部【张三】',
                value: 40
            }, {
                name: '项目5部【张三】',
                value: 32
            }, {
                name: '项目6部【张三】',
                value: 27
            }, {
                name: '项目7部【张三】',
                value: 18
            },
                {
                    name: '项目8部【张三】',
                    value: 18
                },
                {
                    name: '项目9部【张三】',
                    value: 18
                },
                {
                    name: '项目10部【张三】',
                    value: 18
                }],
            label: {
                normal: {
                    show: true,
                    position: 'center',
                    formatter:  '{active|提货不准时}'+ '\n\r' + '{total|' + yyhzTotal +'}',
                    rich: {
                        total:{
                            fontSize: 20,
                            fontWeight: 'bold',
                            color:'#333'
                        },
                        active: {
                            fontSize: 13,
                            color:'#808080',
                            lineHeight:20,
                            fontWeight: 'normal',
                        },
                    }
                },
            },
            left: 0,
            right: '75%',
            top: 0,
            bottom: '50%'
        }, {
            type: 'pie',
            radius: ['55%', '70%'],
            //center: ['50%', '50%'],
            data: [{
                name: '项目1部【张三】',
                value: 30
            }, {
                name: '项目2部【张三】',
                value: 48
            }, {
                name: '项目3部【张三】',
                value: 60
            }, {
                name: '项目4部【张三】',
                value: 70
            }, {
                name: '项目5部【张三】',
                value: 32
            }, {
                name: '项目6部【张三】',
                value: 27
            }, {
                name: '项目7部【张三】',
                value: 18
            },
                {
                    name: '项目8部【张三】',
                    value: 18
                },
                {
                    name: '项目9部【张三】',
                    value: 18
                },
                {
                    name: '项目10部【张三】',
                    value: 18
                }],
            label: {
                normal: {
                    show: true,
                    position: 'center',
                    formatter:  '{active|到货不准时}'+ '\n\r' + '{total|' + yyhzTotal +'}',
                    rich: {
                        total:{
                            fontSize: 20,
                            fontWeight: 'bold',
                            color:'#333'
                        },
                        active: {
                            fontSize: 13,
                            color:'#808080',
                            lineHeight:24,
                        },
                    }
                },
            },
            left: '25%',
            right: '50%',
            top: 0,
            bottom: '50%'
        }, {
            type: 'pie',
            radius: ['55%', '70%'],
            //center: ['50%', '50%'],
            data: [{
                name: '项目1部【张三】',
                value: 50
            }, {
                name: '项目2部【张三】',
                value: 78
            }, {
                name: '项目3部【张三】',
                value: 38
            }, {
                name: '项目4部【张三】',
                value: 60
            }, {
                name: '项目5部【张三】',
                value: 32
            }, {
                name: '项目6部【张三】',
                value: 27
            }, {
                name: '项目7部【张三】',
                value: 18
            },
                {
                    name: '项目8部【张三】',
                    value: 18
                },
                {
                    name: '项目9部【张三】',
                    value: 18
                },
                {
                    name: '项目10部【张三】',
                    value: 18
                }],
            label: {
                normal: {
                    show: true,
                    position: 'center',
                    formatter:  '{active|回单正本}'+ '\n\r' + '{active|不及时}' + '\n\r' + '{total|' + yyhzTotal +'}',
                    rich: {
                        total:{
                            fontSize: 20,
                            fontWeight: 'bold',
                            color:'#333',
                            lineHeight:24,
                        },
                        active: {
                            fontSize: 13,
                            color:'#808080',
                            lineHeight:20,
                        },
                    }
                },
            },
            left: '50%',
            right: '25%',
            top: 0,
            bottom: '50%'
        },
            {
                type: 'pie',
                radius: ['55%', '70%'],
                //center: ['50%', '50%'],
                data: [{
                    name: '项目1部【张三】',
                    value: 20
                }, {
                    name: '项目2部【张三】',
                    value: 38
                }, {
                    name: '项目3部【张三】',
                    value: 68
                }, {
                    name: '项目4部【张三】',
                    value: 30
                }, {
                    name: '项目5部【张三】',
                    value: 32
                }, {
                    name: '项目6部【张三】',
                    value: 27
                }, {
                    name: '项目7部【张三】',
                    value: 18
                },
                    {
                        name: '项目8部【张三】',
                        value: 18
                    },
                    {
                        name: '项目9部【张三】',
                        value: 18
                    },
                    {
                        name: '项目10部【张三】',
                        value: 18
                    }],
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        formatter:  '{active|回单影像上}'+ '\n\r' + '{active|传不及时}' + '\n\r' + '{total|' + yyhzTotal +'}',
                        rich: {
                            total:{
                                fontSize: 20,
                                fontWeight: 'bold',
                                color:'#333',
                                lineHeight:24,
                            },
                            active: {
                                fontSize: 13,
                                color:'#808080',
                                lineHeight:20,
                            },
                        }
                    },
                },
                left: '75%',
                right: 0,
                top: 0,
                bottom: '50%'
            },
            {
                type: 'pie',
                radius: ['40%', '55%'],
                center: ['50%', '50%'],
                data: yyhzData,
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        formatter:  '{active|下单不及时}'+ '\n\r' + '{total|' + yyhzTotal +'}',
                        rich: {
                            total:{
                                fontSize: 20,
                                fontWeight: 'bold',
                                color:'#333'
                            },
                            active: {
                                fontSize: 13,
                                color:'#808080',
                                lineHeight:24,
                            },
                        }
                    },
                },
                left: 0,
                right: '66.6667%',
                top: '20%',
                bottom: 0
            }, {
                type: 'pie',
                radius: ['40%', '55%'],
                center: ['50%', '50%'],
                data: yyhzData,
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        formatter:  '{active|对账不及时}'+ '\n\r' + '{total|' + yyhzTotal +'}',
                        rich: {
                            total:{
                                fontSize: 20,
                                fontWeight: 'bold',
                                color:'#333'
                            },
                            active: {
                                fontSize: 13,
                                color:'#808080',
                                lineHeight:24,
                            },
                        }
                    },
                },
                left: '33.3333%',
                right: '33.3333%',
                top: '20%',
                bottom: 0
            },
            {
                type: 'pie',
                radius: ['40%', '55%'],
                center: ['50%', '50%'],
                data: yyhzData,
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        formatter:  '{active|跟踪不规范}'+ '\n\r' + '{total|' + yyhzTotal +'}',
                        rich: {
                            total:{
                                fontSize: 20,
                                fontWeight: 'bold',
                                color:'#333'
                            },
                            active: {
                                fontSize: 13,
                                color:'#808080',
                                lineHeight:24,
                            },
                        }
                    },
                },
                left: '66.6667%',
                right: 0,
                top: '20%',
                bottom: 0
            }
        ],
        color: ["#F2637B", "#3AA0FF", "#4DCB73","#FFAE3A", "#FFBABA", "#FFE600","#1ABB95","#3A62FF", "#B43AFF","#3AA0FF"],
    };
    yyhzMyChart.setOption(yyhzChatsOption);

    var bhgChats = document.getElementById("bhgChats");
    var bhgMyChart = echarts.init(bhgChats);
    var bhgChatsOption;
    bhgChatsOption = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        legend: {
            data: ['总运单', '调度不合规', '不合规率']
        },
        xAxis: [
            {
                type: 'category',
                data: ['张三1', '张三2', '张三3', '张三4', '张三5', '张三6', '张三7','张三8', '张三9', '张三10', '张三11', '张三12', '张三13', '张三14'],
                axisPointer: {
                    type: 'shadow'
                },
                axisLine: { onZero: true },
                splitLine: { show: false },
                splitArea: { show: false },
                axisLabel: {
                    interval:0,
                    rotate:50//角度顺时针计算的
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
            },
            {
                type: 'value',
                name: '',

                axisLabel: {
                    formatter: '{value} %'
                }
            }
        ],
        series: [
            {
                name: '总运单',
                type: 'bar',
                stack: 'Ad',
                data: [120, 132, 101, 134, 90, 230, 210,120, 132, 101, 134, 90, 230, 210]
            },
            {
                name: '调度不合规',
                type: 'bar',
                stack: 'Ad',
                data: [220, 182, 191, 234, 290, 330, 310,220, 182, 191, 234, 290, 330, 310]
            },
            {
                name: '不合规率',
                type: 'line',
                yAxisIndex: 1,
                data: [30, 40, 20, 13, 39, 43, 40,30, 40, 20, 13, 39, 43, 40]
            }
        ],
        color: ["#3792E8", "#FFB500", "#1AB394"],
    };
    bhgMyChart.setOption(bhgChatsOption);

    var ydbhgChats = document.getElementById("ydbhgChats");
    var ydbhgMyChart = echarts.init(ydbhgChats);
    var ydbhgChatsOption;
    ydbhgChatsOption = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        legend: {
            data: ['总运单', '不合规', '不合规率']
        },
        xAxis: [
            {
                type: 'category',
                data: ['项目一部【顾小波】', '项目二部【顾小波】', '项目3部【顾小波】','项目4部【顾小波】', '项目5部【顾小波】', '项目6部【顾小波】','项目7部【顾小波】', '项目8部【顾小波】', '项目9部【顾小波】','项目10部【顾小波】'],
                axisPointer: {
                    type: 'shadow'
                },
                axisLine: { onZero: true },
                splitLine: { show: false },
                splitArea: { show: false },
                axisLabel: {
                    interval:0,
                    rotate:30//角度顺时针计算的
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
            },
            {
                type: 'value',
                name: '',

                axisLabel: {
                    formatter: '{value} %'
                }
            }
        ],
        series: [
            {
                name: '总运单',
                type: 'bar',
                stack: 'Ad',
                data: [120, 132, 101, 134, 90, 230, 210,120, 132, 101]
            },
            {
                name: '不合规',
                type: 'bar',
                stack: 'Ad',
                data: [220, 182, 191, 234, 290, 330, 310,220, 182, 191]
            },
            {
                name: '不合规率',
                type: 'line',
                yAxisIndex: 1,
                data: [30, 40, 20, 13, 39, 43, 40,30, 40, 20]
            }
        ],
        color: ["#3792E8", "#FFB500", "#1AB394"],
    };
    ydbhgMyChart.setOption(ydbhgChatsOption);


    var ydyyChats = document.getElementById("ydyyChats");
    var ydyyMyChart = echarts.init(ydyyChats);
    var ydyyChatsOption;
    ydyyChatsOption = {
        tooltip: {
            trigger: 'item'
        },
        legend: {
            orient: 'vertical',
            right: 0,
            top:'center',
            data: ['司机未签订合同','请上传身份证正面照片','车辆信息还未创建，请补充','当前运单被运单抽检，请上传单据申诉' ,
                '请根据提示上传车辆行驶证等证照图片' ,'请上传身份证反面照片' ,'请选择能源类型' ,'照片模糊或不是行驶证图片'],
            formatter: function(name) {
                // 获取legend显示内容
                let data = ydyyChatsOption.series[0].data;
                let total = 0;
                let tarValue = 0;
                for (let i = 0, l = data.length; i < l; i++) {
                    total += data[i].value;
                    if (data[i].name == name) {
                        tarValue = data[i].value;
                    }
                }
                let p = (tarValue / total * 100).toFixed(2);
                return name + ' ' + ' ' + p + '%' + '\xa0\xa0\xa0\xa0' + tarValue;
            },
        },
        series: [
            {
                type: 'pie',
                radius: '50%',
                center: ['25%', '50%'],
                data: [
                    { value: 1048, name: '司机未签订合同' },
                    { value: 735, name: '请上传身份证正面照片' },
                    { value: 580, name: '车辆信息还未创建，请补充' },
                    { value: 484, name: '当前运单被运单抽检，请上传单据申诉' },
                    { value: 300, name: '请根据提示上传车辆行驶证等证照图片' },
                    { value: 300, name: '请上传身份证反面照片' },
                    { value: 300, name: '请选择能源类型' },
                    { value: 300, name: '照片模糊或不是行驶证图片' }
                ],
                itemStyle: {
                    emphasis: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 1)'
                    },
                    normal:{
                        label:{
                            show: true,
                            formatter: '{d}%'
                        },
                        labelLine :{show:true}
                    }
                }
            }
        ],
        color: ['#3BA1FF','#37CCCC','#4ECC74','#FBD438','#F3647C','#9860E5','#5354D0','#435188'],
    };
    ydyyMyChart.setOption(ydyyChatsOption);

    var zdjChats = document.getElementById("zdjChats");
    var zdjMyChart = echarts.init(zdjChats);
    var zdjChatsOption;
    zdjChatsOption = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        toolbox: {
            feature: {

            }
        },
        legend: {
            data: ['已调度票数', '待调度','指导价偏差']
        },
        xAxis: [
            {
                type: 'category',
                data: ['调度1组', '调度2组', '调度3组', '调度4组', '调度5组', '调度6组', '调度7组','调度8组', '调度9组', '调度10组', '调度11组','调度12组'],
                axisPointer: {
                    type: 'shadow'
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '',
            },
            {
                type: 'value',
                name: '',
                // min: 0,
                // max: 50,
                // interval: 10,
                axisLabel: {
                    formatter: '{value} %'
                }
            }
        ],
        visualMap: {
            show:false,
            seriesIndex:2,
            pieces: [{
                gt: 0,
                lte: 30,
                color: '#1AB394'
            }],
            outOfRange: {
                color: 'red'
            }
        },

        series: [
            {
                name: '已调度票数',
                type: 'bar',
                data: [
                    20, 49, 50, 32, 25, 20, 35, 20, 40, 20, 50, 33
                ]
            },
            {
                name: '待调度',
                type: 'bar',
                data: [
                    40, 60, 30, 52, 40, 40, 50, 60, 30, 20, 50, 33
                ]
            },
            {
                name: '指导价偏差',
                type: 'line',
                yAxisIndex: 1,
                data: [20, 30, 33, 25, 30, 10, 20, 23, 23, 16, 42, 20]
            },

        ],
        color: ["#3792E8", "#FFB500","#1AB394","#FF8400"],
    };
    zdjMyChart.setOption(zdjChatsOption);

    //
    var ydhgChats = document.getElementById("ydhgChats");
    var ydhgMyChart = echarts.init(ydhgChats);
    var ydhgChatsOption;
    ydhgChatsOption = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        legend: {
            data: ['总运单', '合规运单', '合规率']
        },
        xAxis: [
            {
                type: 'category',
                data: ['张三1', '张三2', '张三3', '张三4', '张三5', '张三6', '张三7','张三8', '张三9', '张三10', '张三11', '张三12', '张三13', '张三14'],
                axisPointer: {
                    type: 'shadow'
                },
                axisLine: { onZero: true },
                splitLine: { show: false },
                splitArea: { show: false },
                axisLabel: {
                    interval:0,
                    rotate:50//角度顺时针计算的
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
            },
            {
                type: 'value',
                name: '',

                axisLabel: {
                    formatter: '{value} %'
                }
            }
        ],
        series: [
            {
                name: '总运单',
                type: 'bar',
                stack: 'Ad',
                data: [120, 132, 101, 134, 90, 230, 210,120, 132, 101, 134, 90, 230, 210]
            },
            {
                name: '合规运单',
                type: 'bar',
                stack: 'Ad',
                data: [220, 182, 191, 234, 290, 330, 310,220, 182, 191, 234, 290, 330, 310]
            },
            {
                name: '合规率',
                type: 'line',
                yAxisIndex: 1,
                data: [30, 40, 20, 13, 39, 43, 40,30, 40, 20, 13, 39, 43, 40]
            }
        ],
        color: ["#3792E8", "#FFB500", "#1AB394"],
    };
    ydhgMyChart.setOption(ydhgChatsOption);

    var tbChats = document.getElementById("tbChats");
    var tbMyChart = echarts.init(tbChats);
    var tbChatsOption;
    tbChatsOption = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        toolbox: {
            feature: {

            }
        },
        legend: {
            data: ['2020年', '2021年','同比增长率','环比增长率']
        },
        xAxis: [
            {
                type: 'category',
                data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月','8月', '9月', '10月', '11月','12月'],
                axisPointer: {
                    type: 'shadow'
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '',
            },
            {
                type: 'value',
                name: '',
                // min: 0,
                // max: 50,
                // interval: 10,
                axisLabel: {
                    formatter: '{value} %'
                }
            }
        ],
        series: [
            {
                name: '2020年',
                type: 'bar',
                data: [
                    20, 49, 50, 32, 25, 20, 35, 20, 40, 20, 50, 33
                ]
            },
            {
                name: '2021年',
                type: 'bar',
                data: [
                    40, 60, 30, 52, 40, 40, 50, 60, 30, 20, 50, 33
                ]
            },
            {
                name: '同比增长率',
                type: 'line',
                yAxisIndex: 1,
                data: [20, 30, 33, 25, 30, 10, 20, 23, 23, 16, 12, 20]
            },
            {
                name: '环比增长率',
                type: 'line',
                yAxisIndex: 1,
                data: [10, 40, 23, 45, 40, 30, 30, 40, 10, 46, 30, 30]
            }
        ],
        color: ["#3792E8", "#FFB500","#1AB394","#FF8400"],
    };
    tbMyChart.setOption(tbChatsOption);

    }

    $(".week").click(function () {
        $(this).addClass("act").siblings().removeClass("act"); //当前点击的添加样式，兄弟节点删除样式
    });

    layui.use('laydate', function(){
        var laydate = layui.laydate;
        var payDate = laydate.render({
            elem: '#payDate', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'month',
            done:function(value,data){
                $("#payDate").val(value)
                getXslbChats(0)
            }
        });
    });

    function bigNumberTransform (value) {
        const newValue = ['', '', '']
        let fr = 1000
        let num = 3
        let text1 = ''
        let fm = 1
        while (value / fr >= 1) {
            fr *= 10
            num += 1
            // console.log('数字', value / fr, 'num:', num)
        }
        if (num >= 5) { // 万
            //text1 = parseInt(num - 4) / 3 > 1 ? '千万' : '万'
            text1 = '万'
            // tslint:disable-next-line:no-shadowed-variable
            fm = 10000
            if (value % fm === 0) {
                newValue[0] = parseInt(value / fm) + ''
            } else {
                newValue[0] = parseFloat(value / fm).toFixed(2) + ''
            }
            newValue[1] = text1
        }
        if (value < 10000) {
            newValue[0] = value + ''
            newValue[1] = ''
        }
        return newValue.join('')
    }
    /*====================业务中心============================*/

    /**
     * 获取待确认发货单数量
     */
    function getInvoiceNewCount() {
        $.ajax({
            url: ctx + "system/main/sales/new_invoice_ct",
            method: 'GET',
            success: function (result) {
                $('#invoiceNewCount').html(result.data.invoiceNewCount);
                $('#orderTimeRate').html("近一月及时率" + result.data.orderTimeRate +"%");

                $('#theTimeoutInvoiceNewCount').html("即将超时 "+result.data.theTimeoutInvoiceNewCount+"&nbsp;&nbsp;");
            }
        });
    }

    /**
     * 查询当天待跟踪的数量
     */
    function getNeedTraceTodayCount() {
        $.ajax({
            url: ctx + "system/main/sales/need_trace_today_ct",
            method: 'GET',
            success: function (result) {
                $('#needTraceTodayCount').html(result.data.needTraceTodayCount);
                $('#traceRate').html("近一月及时率" + result.data.traceRate +"%");

            }
        });
    }

    /**
     * 获取待提货委托单数量\近一月已完成 提货数量\计算当月提货及时率
     */
    function getAffirmEntrustCount() {
        $.ajax({
            url: ctx + "system/main/sales/affirm_entrust_ct",
            method: 'GET',
            success: function (result) {
                $('#affirmEntrustCount').html(result.data.affirmEntrustCount);
                $('#pickUpMonthCount').html("超时未完成 "+result.data.pickUpMonthCount);
                $('#theTimeoutAffirmEntrustCount').html("即将超时 "+result.data.theTimeoutAffirmEntrustCount+"&nbsp;&nbsp;");
                $('#pickUpMonthRate').html("及时率" + result.data.pickUpMonthRate +"%");
            }
        });
    }

    /**
     * 获取待到货的委托单数量\近一月已完成 到货数量\计算当月到货及时率
     */
    function getPickUpEntrustCount() {
        $.ajax({
            url: ctx + "system/main/sales/pick_up_entrust_ct",
            method: 'GET',
            success: function (result) {
                $('#pickUpEntrustCount').html(result.data.pickUpEntrustCount);
                $('#arrivalMonthCount').html("超时未完成 "+result.data.arrivalMonthCount);
                $('#theTimeoutPickUpEntrustCount').html("即将超时 "+result.data.theTimeoutPickUpEntrustCount+"&nbsp;&nbsp;");

                $('#arrivalMonthRate').html("及时率" + result.data.arrivalMonthRate +"%");
            }
        });
    }

    /**
     * 查询未回单上传数量\近一月已完成 回单上传数量\近一月及时率 回单上传
     */
    function getReceiptStatusCount() {
        $.ajax({
            url: ctx + "system/main/sales/receipt_status_ct",
            method: 'GET',
            success: function (result) {
                if(result.data.receiptStatusCount){
                    $('#receiptStatusCount').html(result.data.receiptStatusCount);
                }
                $('#receiptStatusFinishCount').html("超时未完成 "+result.data.receiptStatusFinishCount);
                $('#theTimeoutReceiptStatusCount').html("即将超时 "+result.data.theTimeoutReceiptStatusCount+"&nbsp;&nbsp;");

                $('#receiptStatusFinishRate').html("及时率" + result.data.receiptStatusFinishRate +"%");
            }
        });
    }

    /**
     * 查询未正本回单数量\近一月已完成 正本回单数量\近一月及时率 正本回单
     */
    function getIfReceiptCount() {
        $.ajax({
            url: ctx + "system/main/sales/if_receipt_ct",
            method: 'GET',
            success: function (result) {
                $('#ifReceiptCount').html(result.data.ifReceiptCount);
                $('#ifReceiptFinishCount').html("超时未完成 "+result.data.ifReceiptFinishCount);
                $('#theTimeoutIfReceiptCount').html("即将超时 "+result.data.theTimeoutIfReceiptCount+"&nbsp;&nbsp;");

                $('#ifReceiptFinishRate').html("及时率" + result.data.ifReceiptFinishRate +"%");
            }
        });
    }

    /**
     * 待对账客户
     */
    function getUnreconciledCustomerCount() {
        $.ajax({
            url: ctx + "system/main/sales/un_customer_ct",
            method: 'GET',
            success: function (result) {
                $('#unreconciledCustomerCount').html(result.data.unreconciledCustomerCount);
                $('#timedOutUnCustomerCount').html("逾期未对账 "+result.data.timedOutUnCustomerCount);
                $('#aboutToTimeOutUnCustomerCount').html("应对账 "+result.data.aboutToTimeOutUnCustomerCount+"&nbsp;&nbsp;");
            }
        });
    }

    /**
     *待申请开票
     */
    function getPendingApplicationCount() {
        $.ajax({
            url: ctx + "system/main/sales/pending_application_ct",
            method: 'GET',
            success: function (result) {
                $('#pendingApplicationCount').html(result.data.pendingApplicationCount);
                $('#timedOutUnBillingCount').html("逾期未申请 "+result.data.timedOutUnBillingCount);
                $('#aboutToTimeOutBillingCount').html("应申请 "+result.data.aboutToTimeOutBillingCount+"&nbsp;&nbsp;");
            }
        });
    }


    /**
     * 获取 客户营业额 排名
     * @param type      0按周  1按月  2按年
     * @param upOrDown  0 上涨 1 下跌
     * @param id        html需要更新的id
     */
    function getCustRank(type, upOrDown,id) {
        if (upOrDown == 0) {
            custRankUpType = type
        }else {
            custRankDownType = type
        }
        custRankUpOrDown = upOrDown;

        $.ajax({
            url: ctx + "system/main/sales/cust_rank",
            method: 'POST',
            data: {type:type, upOrDown: upOrDown},
            success: function (result) {
                $('#'+id).html(result);
                if(type==0){
                    var height1 = $('#table').height()
                    $('#table1').height(height1)
                }
            }
        });
    }

    var custRankUpType;
    var custRankDownType;

    /**
     * 客户营业额统计  跳转
     * @param upOrDown  0 上涨 1 下跌
     */
    function custRankTable(upOrDown){
        let type = upOrDown === 0 ? custRankUpType : custRankDownType

        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "客户营业额统计",
            area: ['55%', '90%'],
            content: ctx + "system/main/sales/cust_rank_page" +
                "?type="+ type + "&upOrDown=" + upOrDown,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });

        // $.modal.open("qqq",ctx + "system/main/sales/cust_rank_page" +
        //     "?type="+ type + "&upOrDown=" + upOrDown,1050,'');
    }


    /**
     * 客户营业额统计 所有 月环比与月同比
     */
    function getCustRankTotal() {
        $.ajax({
            url: ctx + "system/main/sales/cust_rank_total",
            method: 'GET',
            success: function (result) {
                $('#chainRatio').html(result.data.chainRatio +"%");
                if (result.data.chainRatio >= 0) {
                    $('#chainRatio').addClass("fcf31")
                    $('#chainRatio').next().addClass("fa-caret-up fcf31")
                }else {
                    $('#chainRatio').addClass("fc1ab")
                    $('#chainRatio').next().addClass("fa-caret-down fc1ab")
                }

                if (result.data.yearOnYearRatio >= 0) {
                    $('#yearOnYearRatio').addClass("fcf31")
                    $('#yearOnYearRatio').next().addClass("fa-caret-up fcf31")

                }else {
                    $('#yearOnYearRatio').addClass("fc1ab")
                    $('#yearOnYearRatio').next().addClass("fa-caret-down fc1ab")

                }
                $('#yearOnYearRatio').html(result.data.yearOnYearRatio +"%");
            }
        });
    }

    /**
     * 客户营业额统计 所有 月环比与月同比
     */
    function getKhyyezfChats(type) {
        $.ajax({
            url: ctx + "system/main/sales/cust_rank_ct",
            method: 'POST',
            data: {type:type},
            success: function (result) {
                let res = result.data
                let data = [
                    { value: res.upCount, name: '营业额增加客户'},
                    { value: res.downCount, name: '营业额减少客户' },
                    { value: res.flatCount, name: '涨幅稳定客户' },
                ]

                khyyezfChatsOption.series[0].data = data;

                khyyezfChats.setOption(khyyezfChatsOption);
            }
        });
    }

    /**
     * 账款信息统计表
     */
    function getZkxxChats() {
        $.ajax({
            url: ctx + "system/main/sales/zkxxchats",
            method: 'GET',
            success: function (result) {
                let res = result.data

                zkxxChatsOption.xAxis.data = res.deptNameList

                zkxxChatsOption.series = [{
                    name: '应对账',
                    type: 'bar',
                    stack: 'one',
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowColor: 'rgba(0,0,0,0.3)'
                        }
                    },
                    data: res.packageAmountList
                },
                    {
                        name: '应申请开票',
                        type: 'bar',
                        stack: 'two',
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowColor: 'rgba(0,0,0,0.3)'
                            }
                        },
                        data: res.checkAmountList
                    },
                    {
                        name: '应收款',
                        type: 'bar',
                        stack: 'three',
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowColor: 'rgba(0,0,0,0.3)'
                            }
                        },
                        data: res.gotAmountList
                    },
                    {
                        name: '逾期未对账',
                        type: 'bar',
                        stack: 'one',
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowColor: 'rgba(0,0,0,0.3)'
                            }
                        },
                        data: res.notPackageAmountList,
                    },
                    {
                        name: '逾期未申请开票',
                        type: 'bar',
                        stack: 'two',
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowColor: 'rgba(0,0,0,0.3)'
                            }
                        },
                        data: res.notCheckAmountList
                    },
                    {
                        name: '逾期未收款',
                        type: 'bar',
                        stack: 'three',
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowColor: 'rgba(0,0,0,0.3)'
                            }
                        },
                        data: res.notGotAmountList
                    }]


                zkxxChats.setOption(zkxxChatsOption);
            }
        });
    }

    /**
     * 账款信息统计表  跳转
     */
    function zkxxTable(){
        $.modal.openTab("业务账款详情",ctx + "report/ReceiveOverTime" );
    }

    /**
     * 超指导价、亏损统计表  跳转
     */
    function czdjTable(){
        $.modal.openTab("营运汇总分析",ctx + "report/operationOverviewDetail" );
    }

    /**
     * 超指导价、亏损统计表
     */
    function getCzdjChats() {
        $.ajax({
            url: ctx + "system/main/sales/czdjchats",
            method: 'GET',
            success: function (result) {
                let res = result.data

                czdjChatsOption.xAxis.data = res.deptNameList

                czdjChatsOption.series = [{
                        name: '超指导价',
                        type: 'bar',
                        data: res.moreMoneyTotalList,
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + '单';
                            }
                        },
                     },
                    {
                        name: '亏损',
                        type: 'bar',
                        data: res.noMoneyTotalList,
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + '单';
                            }
                        },
                    },
                    {
                        name: '低毛利',
                        type: 'bar',
                        data: res.lowMoneyTotalList,
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + '单';
                            }
                        },
                    }
                ]
                czdjCharts.setOption(czdjChatsOption);
            }
        });
    }

    /**
     * 调度完成比率
     */
    function getDdblChats(dateType){
        $.ajax({
            url: ctx + "system/main/sales/ddblChats",
            method: 'POST',
            data: {dateType:dateType},
            success: function (result) {
                let res = result.data

                ddblChatsOption.series[0].data = [
                    { value: res.fullyDispatch, name: '已调度' ,tooltip:{valueFormatter: function (value) {return value + '单' }}},
                    { value: res.partDispatch, name: '部分调度' ,tooltip:{valueFormatter: function (value) {return value + '单' }}},
                    { value: res.notDispatch, name: '未调度' ,tooltip:{valueFormatter: function (value) {return value + '单' }}},
                ]
                ddblChats.setOption(ddblChatsOption);

                //完成率
                $('#dispatchRate').html("调度完成率：" + result.data.dispatchRate + "%")
            }
        })

    }

    /**
     * 各调度组完成占比
     */
    function getDdzbChats(dateType){
        $.ajax({
            url: ctx + "system/main/sales/ddzbChats",
            method: 'POST',
            data: {dateType:dateType},
            success: function (result) {
                let res = result.data

                ddzbChatsOption.legend.data = res.transLineName

                let data = []
                for (const re of res.list) {
                    data.push({ value: re.count, name: re.transLineName,tooltip:{formatter:'{b}: {c} 单'}})
                }

                ddzbChatsOption.series[0].data = data
                ddzbChats.setOption(ddzbChatsOption);

                //完成率
                // $('#dispatchRate').html("调度完成率：" + result.data.dispatchRate + "%")
            }
        })
    }


    /**
     * 项目部营收利润完成
     * @param dateType
     */
    var yslrwcYearSeries = [];
    var yslrwcMonthSeries = [];
    var yslrwcDaySeries = [];

    function getYslrwcChats(dateType) {
        $.ajax({
            url: ctx + "system/main/segment/yslrwcchats",
            method: 'POST',
            data: {"dateType": dateType},
            success: function (result) {
                let res = result.data
                yslrwcChatsOption.xAxis[0].data = res.deptName
                yslrwcChatsOption.xAxis[1].data = res.salesName
                yslrwcYearSeries = [
                    {
                        name: '营收目标',
                        type: 'bar',
                        data: res.yearTarget,
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + '万';
                            }
                        },
                    },
                    {
                        name: '营收完成金额',
                        type: 'bar',
                        data: res.yearAmount,
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + '万';
                            }
                        }
                    }, {
                        name: '完成率',
                        type: 'line',
                        yAxisIndex: 1,
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + '%';
                            }
                        },
                        data: res.yearRate
                    }
                ]

                yslrwcMonthSeries = [
                    {
                        name: '营收目标',
                        type: 'bar',
                        data: res.monthTarget,
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + '万';
                            }
                        },
                    },
                    {
                        name: '营收完成金额',
                        type: 'bar',
                        data: res.monthAmount,
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + '万';
                            }
                        }
                    },{
                        name: '完成率',
                        type: 'line',
                        yAxisIndex: 1,
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + '%';
                            }
                        },
                        data: res.monthRate
                    }
                ]

                yslrwcDaySeries = [
                    {
                        name: '营收目标',
                        type: 'bar',
                        data: res.dayTarget,
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + '万';
                            }
                        },
                    },
                    {
                        name: '营收完成金额',
                        type: 'bar',
                        data: res.dayAmount,
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + '万';
                            }
                        }
                    },{
                        name: '完成率',
                        type: 'line',
                        yAxisIndex: 1,
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + '%';
                            }
                        },
                        data: res.dayRate
                    }
                ]

                yslrwcChatsOption.series=yslrwcMonthSeries
                yslrwcMyChart.setOption(yslrwcChatsOption);
            }
        });
    }
    function getYslrwcChatsByType(dateType) {
        if (dateType == 0) {
            yslrwcChatsOption.series = yslrwcYearSeries
            yslrwcMyChart.setOption(yslrwcChatsOption);
        } else if (dateType == 1) {
            yslrwcChatsOption.series = yslrwcMonthSeries
            yslrwcMyChart.setOption(yslrwcChatsOption);
        } else if (dateType == 2) {
            yslrwcChatsOption.series = yslrwcDaySeries
            yslrwcMyChart.setOption(yslrwcChatsOption);
        }
    }

    /**
     * 各调度组完成占比
     */
    function getDdblChatsAndDdzbChats(dateType){
        // getDdblChats(dateType)
        // getDdzbChats(dateType)
    }

    /**
     * 发货单
     */
    function invoiceTab() {
        $.modal.openTab("发货单",ctx + "invoice?vbillstatus=0" );
    }

    /**
     * 即将超时待确认发货单
     */
    function invoiceToBeConfirmedTab() {
        var now =  dateFormat(new Date(),"yyyy-MM-dd")
        $.modal.openTab("发货单", ctx + "invoice?vbillstatus=0&reqDeliDateStart=" + now + "&reqDeliDateEnd=" + now);
    }

    /**
     * 在途跟踪页面
     * @param needTraceToday    当天跟踪 1是
     * @param vbillstatus       委托单状态
     * @param receiptStatus     回单上传
     * @param ifReceipt
     * @param combinedState    0：回单上传已超时
     *                         1:回单上传即将超时
     *                         2:正本回单已超时
     *                         3:正本回单即将超时
     * @param isTimeLimit      时间限制  1：近三个月
     */
    function traceTab(needTraceToday, vbillstatus, receiptStatus, ifReceipt, combinedState, isTimeLimit) {
        let param = ''
        if (needTraceToday != null) {
            param = '?needTraceToday=' + needTraceToday
        }
        if (vbillstatus != null) {
            param = param !== '' ? param + '&vbillstatus=' + vbillstatus : '?vbillstatus=' + vbillstatus
        }
        if (receiptStatus != null) {
            param = param !== '' ? param + '&receiptStatus=' + receiptStatus : '?receiptStatus=' + receiptStatus
        }
        if (ifReceipt != null) {
            param = param !== '' ? param + '&ifReceipt=' + ifReceipt : '?ifReceipt=' + ifReceipt
        }
        if (combinedState != null) {
            param = param !== '' ? param + '&combinedState=' + combinedState : '?combinedState=' + combinedState
        }
        if (isTimeLimit != null) {
            param = param !== '' ? param + '&isTimeLimit=' + isTimeLimit : '?isTimeLimit=' + isTimeLimit
        }

        $.modal.openTab("在途跟踪", ctx + "trace" + param);
    }

    /**
     * 提货超时页面
     */
    function pickUpExpireTab() {
        var now = new Date();
        now.setDate(now.getDate() - 1);
        var yesterday =  dateFormat(now,"yyyy-MM-dd")

        let param = '?vbillstatus=1&pickStartDate=&pickEndDate='+yesterday+'&isTimeLimit=2'
        $.modal.openTab("在途跟踪", ctx + "trace" + param);
    }

    /**
     * 提货即将超时
     */
    function pickUpExpiringSoonTab() {
        var now =  dateFormat(new Date(),"yyyy-MM-dd")
        let param = '?vbillstatus=1&pickStartDate='+ now +'&pickEndDate='+now
        $.modal.openTab("在途跟踪", ctx + "trace" + param);

    }

    /**
     * 到货超时页面
     */
    function arrivalExpireTab() {
        var now = new Date();
        now.setDate(now.getDate() - 1);
        var yesterday =  dateFormat(now,"yyyy-MM-dd")
        let param = '?vbillstatus=2&pickStartDate=&reqArriDateEnd='+yesterday
        $.modal.openTab("在途跟踪", ctx + "trace" + param);
    }

    /**
     * 提货即将超时
     */
    function arrivalExpiringSoonTab() {
        var now =  dateFormat(new Date(),"yyyy-MM-dd")
        let param = '?vbillstatus=2&pickStartDate='+ now +'&pickEndDate='+now
        $.modal.openTab("在途跟踪", ctx + "trace" + param);

    }

    function dateFormat (date, pattern) {
        var o = {
            "M+" : date.getMonth()+1,     //月份
            "d+" : date.getDate(),     //日
            "h+" : date.getHours(),     //小时
            "m+" : date.getMinutes(),     //分
            "s+" : date.getSeconds(),     //秒
            "q+" : Math.floor((date.getMonth()+3)/3), //季度
            "S" : date.getMilliseconds()    //毫秒
        };
        if(/(y+)/.test(pattern))
            pattern=pattern.replace(RegExp.$1, (date.getFullYear()+"").substr(4 - RegExp.$1.length));
        for(var k in o)
            if(new RegExp("("+ k +")").test(pattern))
                pattern = pattern.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));
        return pattern;
    }
    /**
     * 待确认应收
     */
    function custReceiveTab() {
        $.modal.openTab("客户应收管理",ctx + "custReceive" );
    }

    /**
     * 客户对账管理
     */
    function receCheckSheetTab() {
        $.modal.openTab("客户对账管理",ctx + "receCheckSheet" );
    }

    /*==========================财务中心======*/

    /**
     * 单笔现金 或 单笔油卡
     * @param type  0现金 1油卡
     * @param id    回填html id
     */
    function getPayDetailCt(type, id) {
        $.ajax({
            url: ctx + "system/main/finance/pay_ct",
            method: 'POST',
            data: {costType:type},
            success: function (result) {
                $('#'+id).html(result.data.ct)
            }
        });
    }

    /**
     * 跳转单笔付款
     * @param type      0现金 1油卡
     * @constructor
     */
    function financePayDetailTab(type) {
        let param = ''
        if (type === 0) {
            param = param !== '' ? param + '&costTypeFreight=0,2,4' : '?costTypeFreight=0,2,4'
        }else if (type === 1) {
            param = param !== '' ? param + '&costTypeFreight=1,3,5' : '?costTypeFreight=1,3,5'
        }

        $.modal.openTab("单笔付款",ctx + "payDetail" + param );
    }


    /**
     * 单笔现金 或 单笔油卡
     * @param type  0现金 1油卡
     * @param id    回填html id
     */
    function getPaySheetCt(type, id) {
        $.ajax({
            url: ctx + "system/main/finance/pay_sheet_ct",
            method: 'POST',
            data: {type:type},
            success: function (result) {
                $('#'+id).html(result.data.ct)
            }
        });
    }

    /**
     * 跳转单笔付款
     * @param type      0现金 1油卡
     * @constructor
     */
    function paySheetTab(type) {
        let param = ''
        if (type === 0) {
            param = param !== '' ? param + '&type=1&status=1' : '?type=1&status=1'
        }else if (type === 1) {
            param = param !== '' ? param + '&type=0&status=1' : '?type=0&status=1'
        }

        $.modal.openTab("月度付款",ctx + "payManage" + param );
    }


    /**
     * 三方单笔
     */
    function getOtherFeeCt() {
        $.ajax({
            url: ctx + "system/main/finance/other_fee_ct",
            method: 'GET',
            // data: {type:type},
            success: function (result) {
                $('#OtherFee').html(result.data.ct)
            }
        });
    }

    /**
     * 跳转单笔付款
     * @constructor
     */
    function paySheetTab() {
        $.modal.openTab("第三方单笔",ctx + "finance/otherFee" );
    }

    /**
     * 三方对账
     */
    function getOtherFeeSheetCt() {
        $.ajax({
            url: ctx + "system/main/finance/other_fee_sheet_ct",
            method: 'GET',
            // data: {type:type},
            success: function (result) {
                $('#OtherFeeSheet').html(result.data.ct)
            }
        });
    }

    /**
     * 三方对账
     * @constructor
     */
    function otherFeeSheetTab() {
        $.modal.openTab("第三方对账",ctx + "tms/otherFeeSheetRecord" );
    }


    /**
     * 司机代收
     */
    function getDriverCollectionCt() {
        $.ajax({
            url: ctx + "system/main/finance/driver_collection_ct",
            method: 'GET',
            // data: {type:type},
            success: function (result) {
                $('#driverCollection').html(result.data.ct)
            }
        });
    }

    /**
     * 司机代收
     * @constructor
     */
    function driverCollectionTab() {
        $.modal.openTab("第三方对账",ctx + "driverReceipt" );
    }


    /**
     * 现金收款核销
     */
    function getReceBillingCt() {
        $.ajax({
            url: ctx + "system/main/finance/rece_billing_ct",
            method: 'GET',
            // data: {type:type},
            success: function (result) {
                $('#receBilling').html(result.data.ct)
            }
        });
    }

    /**
     * 开票管理页面
     * @constructor
     */
    function receBillingTab(billingType,receStatus,billingStatus) {
        let param = ''
        if (billingType != null) {
            param = '?billingType=' + billingType
        }
        if (receStatus != null) {
            param = param !== '' ? param + '&receStatus=' + receStatus : '?receStatus=' + receStatus
        }
        if (billingStatus != null) {
            param = param !== '' ? param + '&billingStatus=' + billingStatus + '&vbillstatus=1' : '?billingStatus=' + billingStatus + '&vbillstatus=1'
        }

        $.modal.openTab("开票管理",ctx + "finance/receBilling" + param );
    }


    /**
     * 收款申请审核
     */
    function getReceSheetRecordCt(type,id) {
        $.ajax({
            url: ctx + "system/main/finance/rece_sheet_record_ct",
            method: 'POST',
            data: {type:type},
            success: function (result) {
                $('#'+id).html(result.data.ct)
            }
        });
    }

    /**
     * 收款申请管理页面
     * @constructor
     */
    function receSheetRecordTab(status) {
        let param = ''
        if (status != null) {
            param = '?status=' + status
        }
        $.modal.openTab("开票管理",ctx + "receSheetRecord/0" + param );
    }


    /**
     * 已申请开票
     */
    function getApplyInvoicingCt() {
        $.ajax({
            url: ctx + "system/main/finance/apply_invoicing_ct",
            method: 'GET',
            success: function (result) {
                $('#applyInvoicing').html(result.data.ct)
            }
        });
    }

    /**
     * 已申请待收款
     */
    function getAppliedPaymentCount() {
        $.ajax({
            url: ctx + "system/main/finance/applied_payment_ct",
            method: 'GET',
            success: function (result) {
                $('#appliedPayment').html(result.data.ct)
            }
        });
    }

    /**
     * 待处理汇票
     */
    function getDraftCount() {
        $.ajax({
            url: ctx + "system/main/finance/draft_ct",
            method: 'GET',
            success: function (result) {
                $('#draftCt').html(result.data.ct)
                //$('#draftCt_Warn').html(result.data.ctWarn)
                if(result.data.ctWarn==0){
                    $('#draftCt_Warn').addClass('disnone')

                }else{
                    $('#draftCt_Warn').html(result.data.ctWarn)
                }
            }
        });
    }
    /**
     * 待处理汇票页面
     *
     */
    function draftCtTab() {
        $.modal.openTab("开票管理",ctx + "finance/draft?ticketStatus=1" );
    }


    /**
     * 单笔付款调账审核
     */
    function getSingleAdjustCheckCount() {
        $.ajax({
            url: ctx + "system/main/finance/single_adjust_check_ct",
            method: 'GET',
            success: function (result) {
                $('#singleAdjustCheck').html(result.data.ct)
                //$('#singleAdjustCheck_warn').html(result.data.ctWarn)
                if(result.data.ctWarn==0){
                    $('#singleAdjustCheck_warn').addClass('disnone')

                }else{
                    $('#singleAdjustCheck_warn').html(result.data.ctWarn)
                }
            }
        });
    }

    /**
     * 应付调整单审核 页面
     * @constructor
     */
    function payDetailAdjustCheckTab(adjustCheckStatusArr) {
        let param = ''
        if (adjustCheckStatusArr != null) {
            param = '?adjustCheckStatusArr=' + adjustCheckStatusArr
        }
        $.modal.openTab("应付调整单审核",ctx + "payDetailAdjustCheck" + param );
    }


    /**
     * 月度付款调账审核
     */
    function getPackageAdjustCheckCount() {
        $.ajax({
            url: ctx + "system/main/finance/package_adjust_check_ct",
            method: 'GET',
            success: function (result) {
                $('#packageAdjustCheck').html(result.data.ct)
                //$('#packageAdjustCheck_warn').html(result.data.ctWarn)
                if(result.data.ctWarn==0){
                    $('#packageAdjustCheck_warn').addClass('disnone')

                }else{
                    $('#packageAdjustCheck_warn').html(result.data.ctWarn)
                }
            }
        });
    }
    /**
     * 应付对账单调整审核 页面
     * @constructor
     */
    function payCheckSheetAdjustCheckTab(adjustCheckStatusArr) {
        let param = ''
        if (adjustCheckStatusArr != null) {
            param = '?adjustCheckStatusArr=' + adjustCheckStatusArr
        }
        $.modal.openTab("应付对账单调整审核",ctx + "payCheckSheetAdjustCheck" + param );
    }


    /**
     * 三方单笔调账审核
     */
    function getOtherFeeAdjustCheckCount() {
        $.ajax({
            url: ctx + "system/main/finance/other_fee_adjust_check_ct",
            method: 'GET',
            success: function (result) {
                $('#otherFeeAdjustCheck').html(result.data.ct)
                //$('#otherFeeAdjustCheck_warn').html(result.data.ctWarn)
                if(result.data.ctWarn==0){
                    $('#otherFeeAdjustCheck_warn').addClass('disnone')

                }else{
                    $('#otherFeeAdjustCheck_warn').html(result.data.ctWarn)
                }

            }
        });
    }
    /**
     * 第三方调整单审核
     * 页面
     * @constructor
     */
    function otherFeeAdjustCheckTab(adjustCheckStatusArr) {
        let param = ''
        if (adjustCheckStatusArr != null) {
            param = '?adjustCheckStatusArr=' + adjustCheckStatusArr
        }
        $.modal.openTab("第三方调整单审核",ctx + "otherFeeAdjustCheck" + param );
    }

    /**
     * 项目部开票收款数据
     */
    function getKpskChats() {
        $.ajax({
            url: ctx + "system/main/finance/kpskchats",
            method: 'GET',
            success: function (result) {
                let res = result.data

                kpskChatsOption.xAxis.data = res.deptName

                kpskChatsOption.series = [ {
                        name: '未开票',
                        type: 'bar',
                        data: res.unbilledAmount,
                        tooltip: {
                            valueFormatter: function (value) {
                                if(value<10000){
                                    return value + '元';
                                }else{
                                    return bigNumberTransform(value);
                                }

                            }
                        },
                    },
                    {
                        name: '逾期未收款',
                        type: 'bar',
                        data: res.overdueAmount,
                        tooltip: {
                            valueFormatter: function (value) {
                                if(value<10000){
                                    return value + '元';
                                }else{
                                    return bigNumberTransform(value);
                                }

                            }
                        },
                    }
                ]
                kpskChats.setOption(kpskChatsOption);
            }
        });
    }

    /**
     * 销售额类别占比
     */
    function getXslbChats(balaCorpType) {
        let payDateStr = $("#payDate").val()
        // let payDate = ;
        $.ajax({
            url: ctx + "system/main/finance/xslbChats",
            method: 'POST',
            data: {balaCorpType:balaCorpType,payDateStr:payDateStr},
            success: function (result) {
                let res = result.data

                let data = []
                for (const re of res.list) {
                    data.push({ value: re.payAmount, name: re.payMethodName })
                }
                xslbChatsOption.series[0].data = data
                xslbChatsOption.legend.data = res.payMethodName

                xslbChats.setOption(xslbChatsOption);

                /*
                 * 合计数据
                 */
                let html = `<div class="fw line24">铭源/吉华合计总额：￥`+ bigNumberTransform(res.payAmountSumAll) +`</div>`

                for (let i = 0; i < res.listAll.length; i++) {
                    html = html + `<div class="line24 f13" style="width: 50%;display: inline-block">`+ res.listAll[i].payMethodName +` | `
                        + res.rateAll[i] +`%&nbsp;￥`+ bigNumberTransform(res.listAll[i].payAmount) +`</div>`
                }

                $("#xslbAll").html(html)
            }
        });
    }

    function getbiChats(dateType) {
        $.ajax({
            url: ctx + "owner/bill/count",
            method: 'POST',
            data: {dateType},
            success: function (result) {
                let list = result.data;

                let data = [];
                let payMethodName = [];
                let payAmountSumAll=0;
                
                if(Object.keys(list)!=0){
                    if(list['未对账']){
                        data.push({ value: list['未对账'], name: '未对账',dateType });
                        payMethodName.push('未对账');
                        payAmountSumAll+=list['未对账'];
                    }else {
                        data.push({ value: 0, name: '未对账',dateType });
                        payMethodName.push('未对账');
                        payAmountSumAll+=0;
                    }

                    if(list['已对账']){
                        data.push({ value: list['已对账'], name: '已对账',dateType });
                        payMethodName.push('已对账');
                        payAmountSumAll+=list['已对账'];
                    }else {
                        data.push({ value: 0, name: '已对账',dateType });
                        payMethodName.push('已对账');
                        payAmountSumAll+=0;
                    }
                    
                    if(list['已核销']){
                        data.push({ value: list['已核销'], name: '已核销',dateType });
                        payMethodName.push('已核销');
                        payAmountSumAll+=list['已核销'];
                    }else{
                        data.push({ value: 0, name: '已核销',dateType });
                        payMethodName.push('已核销');
                        payAmountSumAll+=0;
                    }
                }else{
                    data = [
                        { value: 0, name: '未对账',dateType },
                        { value: 0, name: '已对账',dateType },
                        { value: 0, name: '已核销',dateType },
                    ];
                    payMethodName = ['未对账','已对账','已核销'];
                }
                

                biChatsOption.title.text='共'+payAmountSumAll+'单';
           
                biChatsOption.series[0].data = data
                biChatsOption.legend.data = payMethodName

                biChats.setOption(biChatsOption);
            }
        });
    }
    
    function getbicChats(dateType) {
        $.ajax({
            url: ctx + "owner/order/count",
            method: 'POST',
            data: {dateType},
            success: function (result) {
                let list = result.data;
                
                let data = [];
                let payMethodName = [];
                let payAmountSumAll=0;
 
                if(Object.keys(list)!=0){
                    if(list['已派车']){
                        data.push({ value: list['已派车'], name: '已派车',dateType });
                        payMethodName.push('已派车');
                        payAmountSumAll+=list['已派车'];
                    }else {
                        data.push({ value: 0, name: '已派车',dateType });
                        payMethodName.push('已派车');
                        payAmountSumAll+=0;
                    }

                    if(list['已提货']){
                        data.push({ value: list['已提货'], name: '已提货',dateType });
                        payMethodName.push('已提货');
                        payAmountSumAll+=list['已提货'];
                    }else {
                        data.push({ value: 0, name: '已提货',dateType });
                        payMethodName.push('已提货');
                        payAmountSumAll+=0;
                    }
                    
                    if(list['已到货']){
                        data.push({ value: list['已到货'], name: '已到货',dateType });
                        payMethodName.push('已到货');
                        payAmountSumAll+=list['已到货'];
                    }else{
                        data.push({ value: 0, name: '已到货',dateType });
                        payMethodName.push('已到货');
                        payAmountSumAll+=0;
                    }

                    if(list['已回单']){
                        data.push({ value: list['已回单'], name: '已回单',dateType });
                        payMethodName.push('已回单');
                        payAmountSumAll+=list['已回单'];
                    }else{
                        data.push({ value: 0, name: '已回单',dateType });
                        payMethodName.push('已回单');
                        payAmountSumAll+=0;
                    }
                }else{
                    data = [
                        { value: 0, name: '已派车',dateType },
                        { value: 0, name: '已提货',dateType },
                        { value: 0, name: '已到货',dateType },
                        { value: 0, name: '已回单',dateType },
                    ];
                    payMethodName = ['已派车','已提货','已到货','已回单'];
                }

                bicChatsOption.title.text='共'+payAmountSumAll+'单';
           
                bicChatsOption.series[0].data = data
                bicChatsOption.legend.data = payMethodName

                bicChats.setOption(bicChatsOption);
            }
        });
    }
    /*=======================================调度=============================*/

    /**
     * 调度数量
     */
    function toDispatchCt() {
        $.ajax({
            url: ctx + "system/main/segment/to_dispatch_ct",
            method: 'GET',
            success: function (result) {
                $('#toDispatch').html(result.data.toDispatchCount)

                $('#toDispatch_progressbar').css("width",result.data.dispatchOnTimeRate + "%")


                $('#willExpireSoonDispatchCount').html(result.data.willExpireSoonDispatchCount)
                $('#alreadyExpiredDispatchCount').html(result.data.alreadyExpiredDispatchCount)
                $('#dispatchOnTimeRate').html("近一月调度及时率 " + result.data.dispatchOnTimeRate + "%")
            }
        });
    }

    /**
     * 调度配载 页面
     * @constructor
     */
    function toDispatchTab() {
        $.modal.openTab("调度配置",ctx + "tms/segment" );
    }

    /**
     * 司机统计
     */
    function driverCt() {
        $.ajax({
            url: ctx + "system/main/segment/driver_ct",
            method: 'GET',
            success: function (result) {
                $('#toBePerfectedDriverCount').html(result.data.toBePerfectedDriverCount)
                $('#willExpireSoonDriverCount').html(result.data.willExpireSoonDriverCount)
                $('#alreadyExpiredDriverCount').html(result.data.alreadyExpiredDriverCount)

                $('#driverCount').html('司机总数 '+result.data.driverCount + '&nbsp;&nbsp;已完善数 '
                    + result.data.completedDriverCount + '&nbsp;&nbsp;总完善率 ' + result.data.completedDriverRate + '%')

                $('#completedDriverRate').css("width",result.data.completedDriverRate + "%")
            }
        });
    }

    /**
     * 调度配载 页面
     * @constructor
     */
    function myCarrierTab() {
        $.modal.openTab("调度配置",ctx + "basic/carrier/myCarrier" );
    }


    /**
     * 司机统计
     */
    function carCt() {
        $.ajax({
            url: ctx + "system/main/segment/car_ct",
            method: 'GET',
            success: function (result) {
                $('#toBePerfectedCarCount').html(result.data.toBePerfectedCarCount)
                $('#willExpireSoonCarCount').html(result.data.willExpireSoonCarCount)
                $('#alreadyExpiredCarCount').html(result.data.alreadyExpiredCarCount)

                $('#carCount').html('车辆总数 '+result.data.carCount + '&nbsp;&nbsp;已完善数 '
                    + result.data.completedCarCount + '&nbsp;&nbsp;总完善率 ' + result.data.completedCarRate + '%')

                $('#completedCarRate').css("width",result.data.completedCarRate + "%")
            }
        });
    }


    /**
     * 订单调度统计表
     */
    function getDdtjChats(dateType) {
        $.ajax({
            url: ctx + "system/main/segment/ddtjchats",
            method: 'POST',
            data: {"dateType": dateType},
            success: function (result) {
                let res = result.data
                ddtjChatsOption.xAxis.data = res.transLineName

                ddtjChatsOption.series=[
                        {
                            name: '整车调度',
                            type: 'bar',
                            data: res.totalAllCnt,
                            tooltip: {
                                valueFormatter: function (value) {
                                    return value + '单';
                                }
                            },
                        },
                        {
                            name: '零担调度',
                            type: 'bar',
                            data: res.totalZeroCnt,
                            tooltip: {
                                valueFormatter: function (value) {
                                    return value + '单';
                                }
                            }
                        },
                        {
                            name: '超指导价',
                            type: 'bar',
                            data: res.moreMoneyCnt,
                            tooltip: {
                                valueFormatter: function (value) {
                                    return value + '单';
                                }
                            }
                        },
                        {
                            name: '油卡比例',
                            type: 'line',
                            yAxisIndex: 1,
                            tooltip: {
                                valueFormatter: function (value) {
                                    return value + '%';
                                }
                            },
                            data: res.oilRate
                        },{
                            name: '月结比例',
                            type: 'line',
                            yAxisIndex: 1,
                            tooltip: {
                                valueFormatter: function (value) {
                                    return value + '%';
                                }
                            },
                            data: res.monthCarrierRate
                        }
                    ]
                ddtjMyChart.setOption(ddtjChatsOption);
            }
        });
    }

    /**
     * 订单调度统计表
     */
    function ddtjTable() {
        $.modal.openTab("调度详情",ctx + "report/operationOverviewDispatchDetail" );
    }


    /*==========================外包业务======*/
    /**
     * 台账
     */
    function waybillTab() {
        $.modal.openTab("我的台账",ctx + "park_data/waybill" );
    }
    /**
     * 运单管理
     */
    function parkDataInvoiceTab(receiptConfirmFlag,isAddReceCheck,isAddPayCheck) {
        let param = ''
        if (receiptConfirmFlag != null) {
            param = '?receiptConfirmFlag=' + receiptConfirmFlag
        }
        if (isAddReceCheck != null) {
            param = param !== '' ? param + '&isAddReceCheck=' + isAddReceCheck : '?isAddReceCheck=' + isAddReceCheck
        }
        if (isAddPayCheck != null) {
            param = param !== '' ? param + '&isAddPayCheck=' + isAddPayCheck : '?isAddPayCheck=' + isAddPayCheck
        }


        $.modal.openTab("运单管理",ctx + "park_data/invoice" +param);
    }

    /**
     * 获取 待上传回单、代收款申请、待付款申请 数量
     */
    function getParkDataInvoiceCount() {
        $.ajax({
            url: ctx + "system/main/park_data/count",
            method: 'POST',
            success: function (result) {
                $('#receiptConfirmCount').html(result.data.receiptConfirmCount);
                $('#isAddReceCheckCount').html(result.data.isAddReceCheckCount);
                $('#isAddPayCheckCount').html(result.data.isAddPayCheckCount);
            }
        });

    }

    /*=======================车队==========================================*/
    /**
     * 待确认运单
     */
    function fleetInvoiceCt() {
        $.ajax({
            url: ctx + "system/main/fleet/invoice_ct",
            method: 'GET',
            success: function (result) {
                $('#fleetInvoiceNewCount').html(result.data.fleetInvoiceNewCount)
            }
        });
    }

    /**
     * 车队发货单
     */
    function fleetInvoiceTab() {
        $.modal.openTab("发货单",ctx + "fleet/invoice?vbillstatus=0" );
    }


    /**
     * 车队待调度运单
     */
    function fleetSegmentCt() {
        $.ajax({
            url: ctx + "system/main/fleet/segment_ct",
            method: 'GET',
            success: function (result) {
                $('#fleetToDispatchCount').html(result.data.fleetToDispatchCount)
            }
        });
    }

    /**
     * 车队调度配载 页面
     * @constructor
     */
    function fleetToDispatchTab() {
        $.modal.openTab("车队调度配置",ctx + "fleet/segment" );
    }



    /**
     * 待收款运单 receivableWriteOffStatusList -> 0,1
     */
    function fleetInvoiceWriteOffCt() {
        $.ajax({
            url: ctx + "system/main/fleet/invoice_write_off_ct",
            method: 'GET',
            success: function (result) {
                $('#fleetInvoiceWriteOffCount').html(result.data.invoiceWriteOffCount)
            }
        });
    }

    /**
     * 车队发货单
     */
    function fleetInvoiceWriteOffTab() {
        $.modal.openTab("发货单",ctx + "fleet/invoice?receivableWriteOffStatusList=0,1" );
    }


    /**
     * 待开票运单
     */
    function fleetReceCheckSheetPendingCt() {
        $.ajax({
            url: ctx + "system/main/fleet/rece_sheet_pending_ct",
            method: 'GET',
            success: function (result) {
                $('#fleetPendingApplicationCount').html(result.data.fleetPendingApplicationCount)
            }
        });
    }

    /**
     * 客户对账管理
     */
    function fleetReceCheckSheetTab() {
        $.modal.openTab("车队应收对账",ctx + "fleet/receCheckSheet" );
    }


    /*=======================运营管理==========================================*/
    /**
     * 单笔付款审核
     */
    function getPayDetailCheckCt() {
        $.ajax({
            url: ctx + "system/main/operation/pay_detail_check_ct",
            method: 'GET',
            success: function (result) {
                $('#payDetailCheckCt').html(result.data.payDetailCheckCt)
            }
        });
    }

    /**
     * 单笔付款审核
     */
    function payDetailCheckTab() {
        // $.modal.openTab("单笔付款审核",ctx + "payDetailCheck" );
    }

    /**
     * 对账付款审核
     */
    function getPaySheetRecordCt() {
        $.ajax({
            url: ctx + "system/main/operation/pay_sheet_record_ct",
            method: 'GET',
            success: function (result) {
                $('#paySheetRecordCt').html(result.data.paySheetRecordCt)
            }
        });
    }

    /**
     * 单笔付款审核
     */
    function paySheetRecordTab() {
        // $.modal.openTab("对账付款审核",ctx + "paySheetRecordCheck" );
    }

    /**
     * 三方单笔审核
     */
    function getOtherFeeCheckCt() {
        $.ajax({
            url: ctx + "system/main/operation/other_fee_check_ct",
            method: 'GET',
            success: function (result) {
                $('#otherFeeCheckCt').html(result.data.otherFeeCheckCt)
            }
        });
    }
    /**
     * 第三方费用审核
     */
    function otherFeeCheckTab() {
        // $.modal.openTab("第三方费用审核",ctx + "check/other_fee_check" );
    }

    /**
     * 三方对账审核
     */
    function getOtherFeeSheetRecordCt() {
        $.ajax({
            url: ctx + "system/main/operation/other_fee_sheet_record_ct",
            method: 'GET',
            success: function (result) {
                $('#otherFeeSheetRecordCt').html(result.data.otherFeeSheetRecordCt)
            }
        });
    }
    /**
     * 第三方对账审核
     */
    function otherFeeSheetRecordTab() {
        // $.modal.openTab("第三方费用审核",ctx + "check/otherFeeSheetRecord" );
    }



    /**
     * 应付单笔调账审核
     */
    function getOperationPayAdjustCheckCount() {
        $.ajax({
            url: ctx + "system/main/operation/pay_adjust_check_ct",
            method: 'GET',
            success: function (result) {
                $('#operationPayAdjustCheck').html(result.data.ct)
                //$('#operationPayAdjustCheck_warn').html(result.data.ctWarn)

                if(result.data.ctWarn==0){
                    $('#operationPayAdjustCheck_warn').addClass('disnone')

                }else{
                    $('#operationPayAdjustCheck_warn').html(result.data.ctWarn)
                }

                $('#operationPayAdjustSheetCheck').html(result.data.sheetCt)
               // $('#operationPayAdjustSheetCheck_warn').html(result.data.sheetCtWarn)
                if(result.data.sheetCt==0){
                    $('#operationPayAdjustSheetCheck_warn').addClass('disnone')

                }else{
                    $('#operationPayAdjustSheetCheck_warn').html(result.data.sheetCt)
                }

            }
        });
    }

    /**
     * 应付对账单调整审核 页面
     * @constructor
     */
    function payCheckSheetAdjustCheckTab() {
        $.modal.openTab("应付对账单调整审核",ctx + "payCheckSheetAdjustCheck" );
    }


    /**
     * 应收对账调账审核
     */
    function getOperationReceAdjustCheckCount() {
        $.ajax({
            url: ctx + "system/main/operation/rece_adjust_check_ct",
            method: 'GET',
            success: function (result) {
                $('#operationReceAdjustCheck').html(result.data.ct)
                //$('#operationReceAdjustCheck_warn').html(result.data.ctWarn)
                if(result.data.ctWarn==0){
                    $('#operationReceAdjustCheck_warn').addClass('disnone')

                }else{
                    $('#operationReceAdjustCheck_warn').html(result.data.ctWarn)
                }

                $('#operationReceAdjustSheetCheck').html(result.data.sheetCt)
                //$('#operationReceAdjustSheetCheck_warn').html(result.data.sheetCt)
                if(result.data.sheetCt==0){
                    $('#operationReceAdjustSheetCheck_warn').addClass('disnone')

                }else{
                    $('#operationReceAdjustSheetCheck_warn').html(result.data.sheetCt)
                }

            }
        });
    }

    function getOperationReceAdjustCheckCWCount() {
        $.ajax({
            url: ctx + "system/main/finance/rece_adjust_check_ct",
            method: 'GET',
            success: function (result) {
                $('#operationReceAdjustCWCheck').html(result.data.ct)
                //$('#operationReceAdjustCheck_warn').html(result.data.ctWarn)
                if(result.data.ctWarn==0){
                    $('#operationReceAdjustCheckCW_warn').addClass('disnone')

                }else{
                    $('#operationReceAdjustCheckCW_warn').html(result.data.ctWarn)
                }

                $('#operationReceAdjustSheetCheckCW').html(result.data.sheetCt)
                //$('#operationReceAdjustSheetCheck_warn').html(result.data.sheetCt)
                if(result.data.sheetCt==0){
                    $('#operationReceAdjustSheetCheckCW_warn').addClass('disnone')

                }else{
                    $('#operationReceAdjustSheetCheckCW_warn').html(result.data.sheetCt)
                }

            }
        });

    }

    /**
     * 应收调整单审核 页面
     * @constructor
     */
    function receiveDetailAdjustCheckTab(adjustCheckStatusArr) {
        let param = ''
        if (adjustCheckStatusArr != null) {
            param = '?adjustCheckStatusArr=' + adjustCheckStatusArr
        }

        $.modal.openTab("应收调整单审核",ctx + "receiveDetailAdjustCheck" + param );
    }
    /**
     * 应收调整单审核 页面
     * @constructor
     */
    function receiveCheckSheetCheckTab(adjustCheckStatusArr) {
        let param = ''
        if (adjustCheckStatusArr != null) {
            param = '?adjustCheckStatusArr=' + adjustCheckStatusArr
        }

        $.modal.openTab("应收对账调整单审核",ctx + "receiveCheckSheetCheck" + param);
    }

    /**
     * 三方单笔调账审核
     */
    function getOperationOtherFeelAdjustCheckCount() {
        $.ajax({
            url: ctx + "system/main/operation/other_feel_adjust_check_ct",
            method: 'GET',
            success: function (result) {
                $('#operationOtherFeelAdjustCheck').html(result.data.ct)
                //$('#operationOtherFeelAdjustCheck_warn').html(result.data.ctWarn)
                if(result.data.ctWarn==0){
                    $('#operationOtherFeelAdjustCheck_warn').addClass('disnone')

                }else{
                    $('#operationOtherFeelAdjustCheck_warn').html(result.data.ctWarn)
                }
            }
        });
    }

    /**
     * 指导价审核
     */
    function getOperationGuidePriceCheckCount() {
        $.ajax({
            url: ctx + "system/main/operation/guide_price_adjust_check_ct",
            method: 'GET',
            success: function (result) {
                $('#operationGuidePriceCheckCt').html(result.data.ct)
            }
        });
    }

    /**
     * 指导价审核 页面
     * @constructor
     */
    function checkGuidePriceTab() {
        $.modal.openTab("指导价审核",ctx + "check/guidePrice" );
    }

    /**
     * 异常审核
     */
    function getCheckExpCount() {
        $.ajax({
            url: ctx + "system/main/operation/exp_check_ct",
            method: 'GET',
            success: function (result) {
                $('#expCheckCt').html(result.data.ct)
            }
        });
    }

    /**
     * 异常审核 页面
     * @constructor
     */
    function checkExpTab() {
        $.modal.openTab("指导价审核",ctx + "check/exp" );
    }

    /**
     * 保证金审核
     */
    function getCheckMarginCount() {
        $.ajax({
            url: ctx + "system/main/operation/margin_check_ct",
            method: 'GET',
            success: function (result) {
                $('#marginCheckCt').html(result.data.ct)
            }
        });
    }

    /**
     * 保证金审核 页面
     * @constructor
     */
    function checkMarginTab() {
        $.modal.openTab("保证金审核",ctx + "check/margin" );
    }

    /**
     * 定金审核
     */
    function getCheckDepositCount() {
        $.ajax({
            url: ctx + "system/main/operation/deposit_check_ct",
            method: 'GET',
            success: function (result) {
                $('#depositCheckCt').html(result.data.ct)
            }
        });
    }

    /**
     * 定金审核 页面
     * @constructor
     */
    function checkDepositTab() {
        $.modal.openTab("定金审核",ctx + "check/deposit" );
    }

    /**
     * 代付审核
     */
    function getCheckPayPassCount() {
        $.ajax({
            url: ctx + "system/main/operation/pay_pass_check_ct",
            method: 'GET',
            success: function (result) {
                $('#payPassCheckCt').html(result.data.ct)
            }
        });
    }

    /**
     * 代付审核 页面
     * @constructor
     */
    function checkPayPassTab() {
        $.modal.openTab("代付审核",ctx + "payPass/check" );
    }

    /**
     * 调度一级审核
     */
    function getSegmentCheckCount(type,id) {
        $.ajax({
            url: ctx + "system/main/operation/segment_check_ct",
            method: 'POST',
            data: {type:type},
            success: function (result) {
                $('#'+id).html(result.data.ct)
            }
        });
    }

    /**
     * 调度审核 页面
     * @constructor
     */
    function segmentCheckTab(type) {
        if (1 === type) {
            $.modal.openTab("调度一级审核",ctx + "tms/segmentCheck/first" );
        }else if(2 === type){
            $.modal.openTab("调度二级审核",ctx + "tms/segmentCheck/second" );

        }
    }


    /**
     * 登记异常订单
     */
    function getExceptionTotalCount() {
        $.ajax({
            url: ctx + "system/main/operation/exception_total_ct",
            method: 'GET',
            success: function (result) {
                $('#exceptionTotal').html(result.data.ct)
            }
        });
    }

    /**
     * 登记异常订单 页面
     * @constructor
     */
    function exceptionTotalTab() {
            $.modal.openTab("异常统计",ctx + "trace/exceptionTotal" );
    }

    /**
     * 多种运输方式金额统计
     */
    function getYsfsChats(dateType) {
        $.ajax({
            url: ctx + "system/main/operation/ysfschats",
            method: 'POST',
            data: {dateType:dateType},
            success: function (result) {
                let res = result.data

                let data = []
                for (const re of res.list) {
                    data.push({ value: re.costAmount, name: re.trans })
                }
                ysfsChatsOption.series[0].data = data
                ysfsChatsOption.legend.data = res.payMethodName

                ysfsChats.setOption(ysfsChatsOption);
            }
        });
    }

    /**
     * 项目部应收账款总额统计表
     */
    function getYszkChats() {
        $.ajax({
            url: ctx + "system/main/operation/yszkchats",
            method: 'GET',
            success: function (result) {
                let res = result.data

                var date = new Date();
                //今年年份
                let thisYear = date .getFullYear();
                //去年年份
                let lastYear = thisYear - 1;

                yszkChatsOption.legend.data = [thisYear+'年', lastYear+'年', '同比增长率', '环比增长率']
                yszkChatsOption.series = [{
                    name: lastYear+'年',
                    type: 'bar',
                    tooltip: {
                        valueFormatter: function (value) {
                            if(value<10000){
                                return value + '元';
                            }else{
                                return bigNumberTransform(value) + '元';
                            }

                        }
                    },
                    data: res.lastTransFeeCount
                },
                    {
                        name: thisYear+'年',
                        type: 'bar',
                        tooltip: {
                            valueFormatter: function (value) {
                                if(value<10000){
                                    return value + '元';
                                }else{
                                    return bigNumberTransform(value) + '元';
                                }

                            }
                        },
                        data: res.thisTransFeeCount
                    },
                    {
                        name: '同比增长率',
                        type: 'line',
                        yAxisIndex: 1,
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + '%';
                            }
                        },
                        data: res.yearOnYearRate
                    },
                    {
                        name: '环比增长率',
                        type: 'line',
                        yAxisIndex: 1,
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + '%';
                            }
                        },
                        data: res.monthOnMonthRate
                    }]
                yszkMyChart.setOption(yszkChatsOption);

            }
        });
    }

    /*=======================审核==========================================*/
    /**
     * 待审核承运商
     */
    function getCarrierCheckCount() {
        $.ajax({
            url: ctx + "system/main/review/carrier_check_ct",
            method: 'GET',
            success: function (result) {
                $('#carrierCheckCt').html(result.data.ct)
            }
        });
    }

    /**
     * 承运商管理 页面
     * @constructor
     */
    function carrierCheckTab() {
        $.modal.openTab("承运商管理",ctx + "basic/carrier?checkStatus=0" );
    }

    /**
     * 客户信息
     */
    function getClientCheckCount() {
        $.ajax({
            url: ctx + "system/main/review/client_check_ct",
            method: 'GET',
            success: function (result) {
                $('#clientCheckCt').html(result.data.ct)
            }
        });
    }

    /**
     * 客户信息 页面
     * @constructor
     */
    function clientCheckTab() {
        $.modal.openTab("客户信息",ctx + "client?checkStatus=0" );
    }

    /**
     * 货品信息
     */
    function getGoodsCheckCount() {
        $.ajax({
            url: ctx + "system/main/review/goods_check_ct",
            method: 'GET',
            success: function (result) {
                $('#goodsCheckCt').html(result.data.ct)
            }
        });
    }

    /**
     * 货品信息 页面
     * @constructor
     */
    function goodsCheckTab() {
        $.modal.openTab("货品信息",ctx + "basic/goods?checkStatus=0" );
    }

    /*=======================公告==========================================*/
    /**
     * 打开公告明细
     */
    function noticesTab(id) {
        var options = [];
        options.url = ctx + "system/notice/detail/" + id;
        options.title = "公告详情";
        options.width = ($(window).width() - 100);
        options.height = ($(window).height() - 50);
        options.btn = ['<i class="fa fa-close"></i> 关闭'];
        options.yes = function (index, layero) {
            layer.close(index);
        };
        $.modal.openOptions(options);
    }



    /*===========================================================================*/
    function switchDateType(e, type) {
        $("#dateType button").removeClass("active");
        $("#dateType").find(e).addClass("active");

        //集团
        $.ajax({
            url: ctx + "system/getRank",
            type: "post",
            dataType: "json",
            data: {type: type},
            success: function (result) {
                if (result.code == 0) {
                    /*
                     * 运营部
                     */
                    var salesList = result.data.salesRankList;
                    var salesDeptNames = salesList.map(function(x){return x.DEPT_NAME});
                    var salesCount = salesList.map(function(x){return x.COUNT});
                    //刷新数据
                    var salesRankOption = salesRankChart.getOption();
                    salesRankOption.yAxis[0].data = salesDeptNames;
                    salesRankOption.series[0].data = salesCount;
                    if (type === 0) {
                        salesRankOption.title[0].subtext = '当月数据';
                    } else {
                        salesRankOption.title[0].subtext = '当日数据';
                    }
                    salesRankChart.setOption(salesRankOption);
                    /*
                     * 调度组
                     */
                    var dispatchList = result.data.dispatchRankList;
                    var dispatchDeptNames = dispatchList.map(function(x){return x.DEPT_NAME});
                    var dispatchCount = dispatchList.map(function(x){return x.COUNT});
                    //刷新数据
                    var dispatcherOption = dispatcherRank.getOption();
                    dispatcherOption.yAxis[0].data = dispatchDeptNames;
                    dispatcherOption.series[0].data = dispatchCount;
                    if (type === 0) {
                        dispatcherOption.title[0].subtext = '当月数据';
                    } else {
                        dispatcherOption.title[0].subtext = '当日数据';
                    }
                    dispatcherRank.setOption(dispatcherOption);
                }
            }
        });
    }


    /**
     * 待调度
     */
    function segmentTab() {
        $.modal.openTab("调度配载",ctx + "tms/segment" );
    }
    /**
     * 应付明细
     */
    function payDetailTab() {
        $.modal.openTab("应付明细",ctx + "payDetail/mainToPayDetail" );
    }

    /**
     * 调账明细
     */
    function adjustTab(adjustRecordType) {
        //跳转应付
        if(adjustRecordType == 0){
            var url = ctx + "payDetailAdjustCheck?adjustCheckStatusArr="+adjustCheckStatusArr;
            $.modal.openTab("应付调整单审核",url);
        }
        //跳转第三方
        if(adjustRecordType == 1){
            var url = ctx + "otherFeeAdjustCheck?adjustCheckStatusArr="+adjustCheckStatusArr;
            $.modal.openTab("第三方调整单审核",url);
        }
        //跳转应收
        if(adjustRecordType == 2){
            var url = ctx + "receiveDetailAdjustCheck?adjustCheckStatusArr="+adjustCheckStatusArr;
            $.modal.openTab("应收调整单审核",url);
        }
        //跳转应收对账
        if(adjustRecordType == 3){
            var url = ctx + "receiveCheckSheetCheck?adjustCheckStatusArr="+adjustCheckStatusArr;
            $.modal.openTab("应收对账调整单审核",url);
        }
        //跳转应收对账
        if(adjustRecordType == 4){
            var url = ctx + "payCheckSheetAdjustCheck?adjustCheckStatusArr="+adjustCheckStatusArr;
            $.modal.openTab("应收对账调整单审核",url);
        }
    }


</script>

</body>
</html>