<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="Description" content="tms物流系统" />
    <title>畅运通TMS云平台</title>
    <link rel="stylesheet" type="text/css" href="css/main.css" />
    <link rel="stylesheet" type="text/css" href="fonts/iconfont.css" />
    <link href="../static/ruoyi/css/ry-ui.css" th:href="@{/ruoyi/css/ry-ui.css?v=3.4.0}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>

    <!--[if lt IE 9]>
    <meta http-equiv="refresh" content="0;ie.html" />
    <![endif]-->
    <link rel="shortcut icon" href="../static/favicon.ico" th:href="@{favicon.ico}"/>
    <style type="text/css">label.error { position:inherit;  }</style>
    <script>
        if(window.top!==window.self){window.top.location=window.location};
    </script>
</head>

<style>
    .password-container {
        position: relative;
    }
    .toggle-password {
        position: absolute;
        right: 10px;
        top: 70%;
        transform: translateY(-50%);
        cursor: pointer;
    }


</style>
<body>

<!--header begin-->
<div class="header-box">
    <div class="wrap">
        <div class="logo-box">
            <a href="#" class="logo"><img src="img/logo_car.png" alt=""></a>
            <span class="web-title">畅运通TMS云平台</span>
            <span class="web-subtitle">欢迎登录</span>
        </div>
        <div class="nav-box">
            <ul class="nav-list" id="selectUserType">
                <li><a href="#" class="current" onclick="selectUserType(this,0);">平台用户</a></li>
                <li><a href="#" onclick="selectUserType(this,1);">货主入口</a></li>
                <li><a href="#" onclick="selectUserType(this,2);">承运商入口</a></li>
            </ul>
        </div>
    </div>
</div>
<!--header end-->

<!--banner begin-->
<div class="banner-box">
    <img src="img/banner.jpg" alt="">
    <div class="login-box">
        <form id="signupForm">
            <div class="login-form">
                <div class="form-item" >
                    <h2 class="login-title" >
                        <span id="login-title">平台入口</span>
                        <div id="title" hidden><a href="#" id="register" class="register" onclick="register(this.name)">注册</a></div>
                    </h2>
                </div>

                <div class="form-item">
                    <label>登录账号</label>
                    <input type="text" name="username" class="text-input uname" placeholder="用户名"/>
                </div>
                <div class="form-item password-container">
                    <label>登录密码</label>
                    <input type="password" name="password" class="text-input pword" placeholder="密码" autocomplete="off"/>
                    <span class="toggle-password fa fa-eye-slash" aria-hidden="true"></span>

                </div>
                <!--<div class="form-item" th:if="${captchaEnabled==true}">
                    <label>验证码</label>
                    <input type="text" class="text-input" name="validateCode" style="width: 158px;">
                    <img th:src="@{captcha/captchaImage(type=${captchaType})}" class="vcode" width="85%"/>
                </div>-->
                <div class="form-item" th:classappend="${captchaEnabled==false} ? 'm-t'">
                    <input type="checkbox" id="rememberme" name="rememberme"><span class="remember checked">记住密码</span>
                </div>
                <div class="form-item">
                    <button class="submit-btn btn-success btn-block"  id="btnSubmit" data-loading="正在验证登录，请稍后...">登录</button>
                </div>
            </div>
        </form>

    </div>
</div>
<!--banner end-->


<!--info begin-->
<div class="info-box">
    <div class="wrap">
        <ul class="info-list">
            <li>
                <dl class="info-item">
                    <dt><span class="iconfont">&#xe604;</span></dt>
                    <dd>
                        <p class="tit">联系电话</p>
                        <p class="text">0513-69890090</p>
                    </dd>
                </dl>
            </li>
            <li>
                <dl class="info-item">
                    <dt><span class="iconfont">&#xe642;</span></dt>
                    <dd>
                        <p class="tit">办公地址</p>
                        <p class="text">江苏省南通市港闸区安顺路2号</p>
                    </dd>
                </dl>
            </li>
        </ul>
    </div>
</div>
<!--info end-->

<!--about begin-->
<div class="about-box">
    <div class="wrap">
        <dl class="about">
            <dt><p class="about-title">公司介绍</p></dt>
            <dd>南通琦欣供应链管理有限公司成立于2016年09月09日，注册地位于南通市港闸区安顺路2号，以物联网、区块链、大数据、云计算等最新智能技术为依托，专注物流科技数字化应用和服务领域，为企业提供供应链一体化管理和网络货运新业态的全生态服务。</dd>
            <dd>琦欣以SaaS化产品架构模式为客户提供TMS、SCM、WMS、OMS等系列软件应用，为客户输出网络货运、运输管理、仓储管理、订单管理、供应链管理等多个解决方案，并能根据客户需求定制开发特色软件应用，琦欣部分系统产品已经达到国内领先水平，助力客户优化供应链管理，实现了智能、专业、规范和降本增效的目标，并使传统企业快速向数字化企业转型。</dd>
        </dl>
    </div>
</div>
<!--about end-->


<!--service begin-->
<div class="about-box service-box">
    <div class="wrap">
        <dl class="about">
            <dt><p class="about-title">产品和服务</p></dt>
            <dd>我们致力于物联网和互联网技术的融合应用，实现人、车、货的实时感知和上下游企业间的高效协同，以信息技术促进物流产业变革。</dd>
        </dl>
        <ul class="service">
            <li>
                <span class="iconfont">&#xe66b;</span><br>
                <span class="tit">Platform | 无车承运人平台</span><br>
                <span class="desc">物流交易、过程管控、供应商考核，和物流金融业务支持</span>
            </li>
            <li>
                <span class="iconfont">&#xe60b;</span><br>
                <span class="tit">TMS | 运输管理系统</span><br>
                <span class="desc">Web和APP应用，数百功能按需订阅，高起点实施无物流信息化</span>
            </li>
            <li>
                <span class="iconfont">&#xe610;</span><br>
                <span class="tit">APP | 运输圈</span><br>
                <span class="desc">访问无车承运人平台和运输管理系统的APP和微信小程序</span>
            </li>
        </ul>
    </div>
</div>
<!--service end-->


<!--track begin-->
<!--<div class="about-box">-->
<!--    <div class="wrap">-->
<!--        <dl class="about">-->
<!--            <dt><p class="about-title">什么是“无车承运人”？</p></dt>-->
<!--            <dd>“无车承运人”是由美国 track broker （货车经纪人）这一词汇演变而来，是无船承运人在陆地的延伸。</dd>-->
<!--            <dd>“无车承运人”指的是不拥有车辆而从事货物运输的个人或单位。</dd>-->
<!--            <dd>“无车承运人”具有双重身份，对于真正的托运人来说，其是承运人；但是对于实际承运人而言，其又是托运人。</dd>-->
<!--            <dd>“无车承运人”一般不从事具体的运输业务，只从事运输组织、货物分拨、运输方式和运输线路的选择等工作，其收入来源主要是规模化的“批发”运输而产生的运费差价。</dd>-->
<!--        </dl>-->
<!--    </div>-->
<!--</div>-->
<!--track end-->

<!--footer begin-->
<div class="footer-box">
    <div class="wrap">
        <div class="footer-nav" style="text-align: center">
            <!--<p>-->
                <!--<a href="#">平台用户</a><span>|</span>-->
                <!--<a href="#">货主入口</a><span>|</span>-->
                <!--<a href="#">承运商入口</a>-->
            <!--</p>-->
            <p>Copyright 南通琦欣供应链管理有限公司 版权所有 |
            <a href="https://beian.miit.gov.cn/" target="_blank">苏ICP备18051282号-2</a>
            </p>
            <p>增值电信业务经营许可证：<a href="https://tms.qixin56.com/static/upload/137733ea1cdf2a3ea6ae632080779ae2.jpg"
                              target="_blank">苏B2-20200629</a>
                |
                <a href="https://beian.mps.gov.cn/#/query/websearch?code=32060202001182" rel="noreferrer" target="_blank">苏公网安备32060202001182</a>
            </p>
        </div>
    </div>
</div>
<!--footer end-->

<script th:inline="javascript"> var ctx = [[@{/}]]; var captchaType = [[${captchaType}]]; </script>
<!-- 全局js -->
<script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
<script src="../static/js/bootstrap.min.js" th:src="@{/js/bootstrap.min.js}"></script>
<!-- 验证插件 -->
<script src="../static/ajax/libs/validate/jquery.validate.min.js" th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
<script src="../static/ajax/libs/validate/messages_zh.min.js" th:src="@{/ajax/libs/validate/messages_zh.min.js}"></script>
<script src="../static/ajax/libs/layer/layer.min.js" th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script src="../static/ajax/libs/blockUI/jquery.blockUI.js" th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
<script src="../static/ruoyi/js/ry-ui.js" th:src="@{/ruoyi/js/ry-ui.js?v=3.4.0}"></script>
<script src="../static/ruoyi/login.js" th:src="@{/ruoyi/login.js}"></script>
<script>
    if(window.top!==window.self){window.top.location=window.location};

    $(document).ready(function() {
        $(".toggle-password").click(function() {
            $(this).toggleClass("fa-eye fa-eye-slash");
            var input = $(this).parent().find("input");
            if (input.attr("type") === "password") {
                input.attr("type", "text");
            } else {
                input.attr("type", "password");
            }
        });
    });

</script>

</body>

</html>