<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>TMS</title>
    <!--[if lt IE 9]>
    <meta http-equiv="refresh" content="0;ie.html"/>
    <![endif]-->
    <link th:href="@{favicon.ico}" rel="stylesheet"/>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/jquery.contextMenu.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/animate.css}" rel="stylesheet"/>
    <link th:href="@{/css/style.css}" rel="stylesheet"/>
    <link th:href="@{/css/skins.css}" rel="stylesheet"/>
    <link th:href="@{/ruoyi/css/ry-ui.css?v=3.4.0}" rel="stylesheet"/>
    <th:block th:include="include :: toastr-css"/>
</head>
<style>
    #side-menu::-webkit-scrollbar-track {
        background-color: #2f4050;
    }
    .skin-tms .navbar{
        background: url('/img/bgdlT.png') no-repeat 36%;
        background-size: auto 84%;
    }
    /* .skin-tms .navbar .nav>li>a {
        color: #fff;
    } */
</style>
<body class="fixed-sidebar full-height-layout gray-bg" style="overflow: hidden"
      th:classappend="${@config.getKey('sys.index.skinName')}">
<div id="wrapper">

    <!--左侧导航开始-->
    <nav class="navbar-default navbar-static-side" role="navigation">
        <div class="nav-close">
            <i class="fa fa-times-circle"></i>
        </div>
        <div class="sidebar-collapse" style="position: relative">
            <div class="nav-header" style="padding: 15px 25px 15px 25px;position: absolute;top: 0;width: 100%;left: 0;height: 85px;">
                <div class="dropdown profile-element" style="text-align: center">
                    <!--                        <span class="logo logo-lg">TMS</span>-->
                    <img style="width: 120px;height: 55px" th:src="@{/img/logo_my.png}" alt="">
                </div>
                <div class="logo-element"><img style="width: 25px;height: 25px" th:src="@{/img/logo_sm.png}" alt="">
                </div>
            </div>
            <ul class="nav" id="side-menu" style="height: calc(100% - 85px);position: absolute;width: 100%;top: 85px;left: 0;overflow: auto;">
                <li class="active">
                    <a href="index.html"><i class="fa fa-home"></i> <span class="nav-label">主页</span> <span
                            class="fa arrow"></span></a>
                    <ul class="nav nav-second-level">
                        <li class="active"><a class="menuItem" th:href="@{/system/main}">我的工作</a></li>
<!--                        <li class="" th:if="${user.userType} == '00'"><a class="menuItem" th:href="@{/wecom-sp/fybx}">我的付款申请</a></li>-->
                    </ul>
                </li>

                <li th:each="menu : ${menus}">
                    <a href="#" th:if="${menu.url == '#'}">
                        <i class="fa fa-bar-chart-o" th:class="${menu.icon}"></i>
                        <span class="nav-label" th:text="${menu.menuName}">一级菜单</span>
                        <span class="fa arrow"></span>
                    </a>

                    <ul class="nav nav-second-level collapse" th:if="${menu.url == '#'}">
                        <li th:each="cmenu : ${menu.children}">
                            <a th:id="${cmenu.menuId}" th:if="${#lists.isEmpty(cmenu.children)}"
                               th:class="${cmenu.target == ''} ? |menuItem| : ${cmenu.target}"
                               th:utext="${cmenu.menuName}" th:href="@{${cmenu.url}}">二级菜单</a>
                            <a th:if="${not #lists.isEmpty(cmenu.children)}" href="#">[[${cmenu.menuName}]]<span
                                    class="fa arrow"></span></a>
                            <ul th:if="${not #lists.isEmpty(cmenu.children)}" class="nav nav-third-level">
                                <li th:each="emenu : ${cmenu.children}"><a
                                        th:class="${emenu.target == ''} ? |menuItem| : ${emenu.target}"
                                        th:text="${emenu.menuName}" th:href="@{${emenu.url}}">三级菜单</a></li>
                            </ul>
                        </li>
                    </ul>

                    <a th:unless="${menu.url == '#'}" th:class="${menu.target == ''} ? |menuItem| : ${menu.target}"
                       th:href="${menu.url}">
                        <i class="fa fa-bar-chart-o" th:class="${menu.icon}"></i>
                        <span class="nav-label" th:text="${menu.menuName}">一级菜单</span>
                    </a>
                </li>

                <li th:if="${demoEnabled}">
                    <a href="#"><i class="fa fa-desktop"></i><span class="nav-label">实例演示</span><span
                            class="fa arrow"></span></a>
                    <ul class="nav nav-second-level collapse">
                        <li><a>表单<span class="fa arrow"></span></a>
                            <ul class="nav nav-third-level">
                                <li><a class="menuItem" th:href="@{/demo/form/button}">按钮</a></li>
                                <li><a class="menuItem" th:href="@{/demo/form/grid}">栅格</a></li>
                                <li><a class="menuItem" th:href="@{/demo/form/select}">下拉框</a></li>
                                <li><a class="menuItem" th:href="@{/demo/form/basic}">基本表单</a></li>
                                <li><a class="menuItem" th:href="@{/demo/form/cards}">卡片列表</a></li>
                                <li><a class="menuItem" th:href="@{/demo/form/jasny}">功能扩展</a></li>
                                <li><a class="menuItem" th:href="@{/demo/form/sortable}">拖动排序</a></li>
                                <li><a class="menuItem" th:href="@{/demo/form/tabs_panels}">选项卡 & 面板</a></li>
                                <li><a class="menuItem" th:href="@{/demo/form/validate}">表单校验</a></li>
                                <li><a class="menuItem" th:href="@{/demo/form/wizard}">表单向导</a></li>
                                <li><a class="menuItem" th:href="@{/demo/form/upload}">文件上传</a></li>
                                <li><a class="menuItem" th:href="@{/demo/form/datetime}">日期和时间</a></li>
                                <li><a class="menuItem" th:href="@{/demo/form/summernote}">富文本编辑器</a></li>
                                <li><a class="menuItem" th:href="@{/demo/form/duallistbox}">左右互选组件</a></li>
                                <li><a class="menuItem" th:href="@{/demo/form/autocomplete}">搜索自动补全</a></li>
                            </ul>
                        </li>
                        <li><a>表格<span class="fa arrow"></span></a>
                            <ul class="nav nav-third-level">
                                <li><a class="menuItem" th:href="@{/demo/table/search}">查询条件</a></li>
                                <li><a class="menuItem" th:href="@{/demo/table/footer}">数据汇总</a></li>
                                <li><a class="menuItem" th:href="@{/demo/table/groupHeader}">组合表头</a></li>
                                <li><a class="menuItem" th:href="@{/demo/table/export}">表格导出</a></li>
                                <li><a class="menuItem" th:href="@{/demo/table/remember}">翻页记住选择</a></li>
                                <li><a class="menuItem" th:href="@{/demo/table/pageGo}">跳转至指定页</a></li>
                                <li><a class="menuItem" th:href="@{/demo/table/params}">自定义查询参数</a></li>
                                <li><a class="menuItem" th:href="@{/demo/table/multi}">初始多表格</a></li>
                                <li><a class="menuItem" th:href="@{/demo/table/button}">点击按钮加载表格</a></li>
                                <li><a class="menuItem" th:href="@{/demo/table/fixedColumns}">表格冻结列</a></li>
                                <li><a class="menuItem" th:href="@{/demo/table/event}">自定义触发事件</a></li>
                                <li><a class="menuItem" th:href="@{/demo/table/detail}">表格细节视图</a></li>
                                <li><a class="menuItem" th:href="@{/demo/table/image}">表格图片预览</a></li>
                                <li><a class="menuItem" th:href="@{/demo/table/curd}">动态增删改查</a></li>
                                <li><a class="menuItem" th:href="@{/demo/table/reorder}">表格拖拽操作</a></li>
                                <li><a class="menuItem" th:href="@{/demo/table/other}">表格其他操作</a></li>
                            </ul>
                        </li>
                        <li><a>弹框<span class="fa arrow"></span></a>
                            <ul class="nav nav-third-level">
                                <li><a class="menuItem" th:href="@{/demo/modal/dialog}">模态窗口</a></li>
                                <li><a class="menuItem" th:href="@{/demo/modal/layer}">弹层组件</a></li>
                                <li><a class="menuItem" th:href="@{/demo/modal/table}">弹层表格</a></li>
                            </ul>
                        </li>
                        <li><a>操作<span class="fa arrow"></span></a>
                            <ul class="nav nav-third-level">
                                <li><a class="menuItem" th:href="@{/demo/operate/table}">表格</a></li>
                                <li><a class="menuItem" th:href="@{/demo/operate/other}">其他</a></li>
                            </ul>
                        </li>
                        <li><a>报表<span class="fa arrow"></span></a>
                            <ul class="nav nav-third-level">
                                <li><a class="menuItem" th:href="@{/demo/report/echarts}">百度ECharts</a></li>
                                <li><a class="menuItem" th:href="@{/demo/report/peity}">peity</a></li>
                                <li><a class="menuItem" th:href="@{/demo/report/sparkline}">sparkline</a></li>
                                <li><a class="menuItem" th:href="@{/demo/report/metrics}">图表组合</a></li>
                            </ul>
                        </li>
                        <li><a>图标<span class="fa arrow"></span></a>
                            <ul class="nav nav-third-level">
                                <li><a class="menuItem" th:href="@{/demo/icon/fontawesome}">Font Awesome</a></li>
                                <li><a class="menuItem" th:href="@{/demo/icon/glyphicons}">Glyphicons</a></li>
                            </ul>
                        </li>
                        <li>
                            <a href="#"><i class="fa fa-sitemap"></i> <span class="nav-label">四层菜单 </span><span
                                    class="fa arrow"></span></a>
                            <ul class="nav nav-second-level collapse">
                                <li>
                                    <a href="#" id="damian">三级菜单1<span class="fa arrow"></span></a>
                                    <ul class="nav nav-third-level">
                                        <li>
                                            <a href="#">四级菜单1</a>
                                        </li>
                                        <li>
                                            <a href="#">四级菜单2</a>
                                        </li>
                                    </ul>
                                </li>
                                <li><a href="#">三级菜单2</a></li>
                            </ul>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </nav>
    <!--左侧导航结束-->

    <!--右侧部分开始-->
    <div id="page-wrapper" class="gray-bg dashbard-1">
        <div class="row border-bottom">
            <nav class="navbar navbar-static-top" role="navigation" style="margin-bottom: 0;">
                <div class="navbar-header">
                    <a class="navbar-minimalize minimalize-styl-2 btn" style="color:#1BB393;font-size: 18px;" href="#"
                       title="收起菜单">
                        <i class="fa fa-bars"></i>
                    </a>
                    <div class="navbar-form-custom"></div>
                </div>
<!--                <div style="flex: 1;padding: 8px 0">-->
<!--                    <img th:src="@{/img/dl.png}" style="display: block;margin: 0px auto" />-->
<!--                </div>-->
                <ul class="nav navbar-top-links navbar-right welcome-message">
                    <li><a title="全屏显示" href="javascript:void(0)" id="fullScreen"><i class="fa fa-arrows-alt"></i> 全屏显示</a>
                    </li>
                    <li class="dropdown">
                        <a class="dropdown-toggle count-info" data-toggle="dropdown" href="#" onclick="openMsg()">
                            <i class="fa fa-bell"></i> <span class="label label-primary" id="count" th:text="${unreadCount}"></span>
                        </a>
                    </li>
                    <li class="dropdown user-menu">
                        <a href="javascript:void(0)" class="dropdown-toggle" data-hover="dropdown">
                            <img th:src="(${user.avatar} == '') ? @{/img/profile.jpg} : @{${user.avatar}}"
                                 class="user-image">
                            <span class="hidden-xs">[[${user.userName}]]</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li class="mt5">
                                <a th:href="@{/system/user/profile}" class="menuItem">
                                    <i class="fa fa-user"></i> 个人中心</a>
                            </li>
                            <li>
                                <a onclick="resetPwd()" class="menuItem">
                                    <i class="fa fa-key"></i> 修改密码</a>
                            </li>
                            <li>
                                <a onclick="changeCall(0)" class="menuItem">
                                    <i class="fa fa-volume-control-phone"></i> 耳麦接听</a>
                            </li>
                            <li>
                                <a onclick="changeCall(1)" class="menuItem">
                                    <i class="fa fa-signal"></i> 手机接听</a>
                            </li>
                            <li class="divider"></li>
                            <li>
                                <a th:href="@{logout}">
                                    <i class="fa fa-sign-out"></i> 退出登录</a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </div>
        <div class="row content-tabs">
            <button class="roll-nav roll-left tabLeft">
                <i class="fa fa-backward"></i>
            </button>
            <nav class="page-tabs menuTabs">
                <div class="page-tabs-content">
                    <a href="javascript:;" class="active menuTab" data-id="/system/main">首页</a>
                </div>
            </nav>
            <button class="roll-nav roll-right tabRight">
                <i class="fa fa-forward"></i>
            </button>
            <a href="#" class="roll-nav roll-right tabReload"><i class="fa fa-refresh"></i> 刷新</a>
        </div>

        <a id="ax_close_max" class="ax_close_max" href="#" title="关闭全屏"> <i class="fa fa-times-circle-o"></i> </a>

        <div class="row mainContent" id="content-main">
            <iframe class="RuoYi_iframe" name="iframe0" width="100%" height="100%" data-id="/system/main"
                    th:src="@{/system/main}" frameborder="0" seamless></iframe>
        </div>
        <!--<div class="footer">-->
        <!--&lt;!&ndash;<div class="pull-right">© [[${copyrightYear}]] </div>&ndash;&gt;-->
        <!--</div>-->
    </div>
    <!--右侧部分结束-->
</div>
<!-- 全局js -->
<script th:src="@{/js/jquery.min.js}"></script>
<script th:src="@{/js/bootstrap.min.js}"></script>
<script th:src="@{/js/plugins/metisMenu/jquery.metisMenu.js}"></script>
<script th:src="@{/js/plugins/slimscroll/jquery.slimscroll.min.js}"></script>
<script th:src="@{/js/jquery.contextMenu.min.js}"></script>
<script th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
<script th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script th:src="@{/ruoyi/js/ry-ui.js?v=3.4.0}"></script>
<script th:src="@{/ruoyi/js/common.js?v=3.4.0}"></script>
<script th:src="@{/ruoyi/index.js}"></script>
<script th:src="@{/ajax/libs/fullscreen/jquery.fullscreen.js}"></script>
<th:block th:include="include :: sockjs"/>
<th:block th:include="include :: toastr-js"/>
<script th:inline="javascript">
    var ctx = [[@{/}]];
        /*用户管理-重置密码*/
        function resetPwd() {
            var url = ctx + 'system/user/profile/resetPwd';
            $.modal.open("重置密码", url, '800', '500');
        }

        function openMsg(){
            $.modal.openTab("消息中心", ctx + "tms/sysMessage");
        }


        $(function () {
            toastr.options = {
                "closeButton": true,
                "debug": false,
                "progressBar": false,
                "positionClass": "toast-top-right",
                "onclick": null,
                "showDuration": "400",
                "hideDuration": "1000",
                "timeOut": "7000",
                "extendedTimeOut": "1000",
                "showEasing": "swing",
                "hideEasing": "linear",
                "showMethod": "fadeIn",
                "hideMethod": "fadeOut"
            };
            //显示数量
            var menuList = [[${menus}]];
            for (var i = 0 ; i < menuList.length ;i++) {
                for (var j = 0 ; j < menuList[i].children.length; j++) {
                    if (menuList[i].children[j].count !== 0) {
                        $("#"+menuList[i].children[j].menuId).append(
                            '<span class="label label-primary" style="float:right;" id="count">'
                            +menuList[i].children[j].count+'</span>')
                    }
                }
            }

            var socket = new SockJS(ctx+'stomp-websocket'); //连接上端点(基站)
            var stompClient = Stomp.over(socket);
            stompClient.heartbeat.outgoing = 20000;
            stompClient.debug = null;
            stompClient.connect({}, function (frame) {
                stompClient.subscribe('/user/topic/index', function (result) {
                    var map = JSON.parse(result.body);

                    send(map);
                });
                stompClient.subscribe('/topic/index', function (result) {
                    var map = JSON.parse(result.body);
                    send(map);
                });
            });

        });

    function send(map) {
        if (map.count !== null) {
            $("#count").text(map.count);
        };

        if (map.openTab === 1) {
            //点击消息的回调
            toastr.options.onclick = function() {
                $.modal.openTab(map.urlTitle, ctx + map.url);
            };
            toastr.options.timeOut = 0;
            toastr.options.extendedTimeOut = 0;
        }else {
            toastr.options.timeOut = 7000;
            toastr.options.extendedTimeOut = 2000;
        }

        if (map.type === 1) {
            toastr.info(map.content);
        }else if (map.type === 2) {
            toastr.success(map.content);
        }else if (map.type === 3) {
            toastr.warning(map.content);
        }else if (map.type === 4) {
            toastr.error(map.content);
        }

    }

    function topOpenTab(title,url){
        window.setTimeout(function(){
        $.modal.openTab(title,url);
        },200);

    }

    function changeCall(type){
        var data = {};
        data.type = type;
        $.ajax({
            url:ctx + "trace/changeCall",
            type:"post",
            timeout : 5000,
            dataType:"json",
            data:data,
            success: function (result) {
                console.log(result);
                if(result.code == 0){
                    $.modal.alertSuccess(result.msg);
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });
    }

</script>
</body>
</html>
