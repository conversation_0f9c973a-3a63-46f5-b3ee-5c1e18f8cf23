<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('在线用户列表')" />
</head>
<body class="gray-bg">
    <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="online-form">
					<div class="select-list">
						<ul>
							<li>
								登录名称：<input type="text" name="loginName"/>
							</li>
							<li>
								用户名称：<input type="text" name="userName"/>
							</li>
							<li>
								手机号：<input type="text" name="phonenumber"/>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
			
	        <div class="btn-group-sm" id="toolbar" role="group">
	            <a class="btn btn-danger btn-del disabled" onclick="javascript:batchForceLogout()" shiro:hasPermission="monitor:online:batchForceLogout">
	                <i class="fa fa-sign-out"></i> 强退
	            </a>
	        </div>
	        
	        <div class="col-sm-12 select-table table-striped">
			    <table id="bootstrap-table" data-mobile-responsive="true"></table>
			</div>
		</div>
	</div>
	<th:block th:include="include :: footer" />
	<script th:inline="javascript">
		var forceFlag = [[${@permission.hasPermi('monitor:online:forceLogout')}]];
		var prefix = ctx + "monitor/api/online";

		$(function() {
		    var options = {
		        url: prefix + "/list",
		        exportUrl: prefix + "/export",
		        showExport: true,
		        escape: true,
		        columns: [{
		            checkbox: true
		        },
		        {
		            field: 'LOGIN_NAME',
		            title: '登录名称'
		        },
				{
					field: 'USER_NAME',
					title: '用户名称'
				},
				{
					field: 'PHONENUMBER',
					title: '手机号'
				},
		        {
		            field: 'DEPT_NAME',
		            title: '部门名称'
		        },

		        {
		            field: 'IPADDR',
		            title: '主机'
		        },
		        {
		            field: 'LOGIN_LOCATION',
		            title: '登录地点'
		        },
		        {
		            field: 'BROWSER',
		            title: '浏览器'
		        },
		        {
		            field: 'OS',
		            title: '操作系统'
		        },
		        {
					title: '会话状态',
		            formatter: function() {
						return '<span class="badge badge-primary">在线</span>';
		            }
		        },
		        {
		            field: 'LOGIN_TIME',
		            title: '登录时间'
		        },
		        {
		            title: '操作',
		            align: 'center',
		            formatter: function(value, row, index) {
		                var msg = '<a class="btn btn-danger btn-xs ' + forceFlag + '" href="javascript:void(0)" onclick="forceLogout(\'' + row.LOGIN_NAME + '\')"><i class="fa fa-sign-out"></i>强退</a> ';
		                return msg;
		            }
		        }]
		    };
		    $.table.init(options);
		});

		// 单条强退
		function forceLogout(login_name) {
		    $.modal.confirm("确定要强制选中用户下线吗？", function() {
		    	var data = { "loginName": login_name };
		        $.operate.post(prefix + "/forceLogout", data);
		    })
		}

		// 批量强退
		function batchForceLogout() {
		    var rows = $.table.selectColumns("LOGIN_NAME");
		    if (rows.length == 0) {
		        $.modal.alertWarning("请选择要强退的用户");
		        return;
		    }
		    $.modal.confirm("确认要强退选中的" + rows.length + "条数据吗?", function() {
		        var url = prefix + "/batchForceLogout";
		        var data = { "loginNames": rows };
		        $.operate.post(url, data);
		    });
		}
	</script>
</body>
</html>