<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="utf-8"/>
    <meta name="author" content="winsou.cn"/>
    <meta name="Keywords" content=""/>
    <meta name="Description" content=""/>
    <title>畅运通无车承运人云平台-用户注册</title>
    <th:block th:include="include :: header('注冊：add')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 100px;
        line-height: 20px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .fw{
        font-weight: bold;
    }
    .fcff {
        color: #ff1f1f;
    }
    .basicTile{
        border-left: 2px #3b84f3 solid;
        display: inline-block;
        padding-left: 10px;
        height: 15px;
        line-height: 15px;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .wid90{
        width: 90px;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .file-input .btn-default {
        border: 0px;
        color: #808080;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .btn.btn-file {
        padding: 0px 8px !important;
    }
    .input-group{
        width: 100%;
    }
    .theme-explorer{
        width: 100%;
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .eye .file-drop-zone-title{
        background: url('../../img/eye.png') no-repeat center 10px;
        background-size: 40px 40px;
        height: 100%;
        line-height: 120px;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
    .show{
        display: block;
    }
    .hide{
        display: none;
    }

    .nav-list_item{
        width: 100%;
        margin-top: 10px;
        text-align: center;
        padding: 10px 0;
        border-radius: 10px;
        border: 1px #eee solid;
        font-weight: bold;
        font-size: 15px;
    }
    .choose-title{
        font-weight: bold;
        line-height: 30px;
    }
    .act{
        color: #1ab394;
        border: 1px #1ab394 solid;
    }

    .text{
        color: #fff;
    }
    .fc1a{
        color: #1ab394 !important;
    }
</style>
<body class="gray-bg">
<div class="form-content">
    <div class="">
        <form id="register" class="form-horizontal" >
            <div class="panel-group" id="accordionOne">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title basicTile">
                            <a data-toggle="collapse" data-parent="#accordion"
                               href="tabs_panels.html#collapseOne">基础信息</a>
                        </h5>
                    </div>
                    <div id="collapseOne" class="panel-collapse collapse in">
                        <div class="panel-body" style="padding-bottom: 10px">
                            <div class="row">
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left" style="line-height: 30px"><span class="fcff">*</span>  承运商性质：</label>
                                        <div class="flex_right">
                                            <div class="radio-box" th:each="mapS,status:${mobileUserClass}">
                                                <input type="radio" name="userClass" th:value="${mapS.value}" th:checked="${mapS.value == 2}">
                                                <label th:text="${mapS.context}"></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left"><span class="fcff">*</span>  运输类型：</label>
                                        <div class="flex_right">
                                            <select name="transportType" class="form-control" th:with="type=${@dict.getType('transport_type')}" required >
                                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt10" >
                                <div class="col-md-6 col-sm-6" th:each="dict : ${picList}" th:if="${dict.context == '法人/个体户身份证正面'}">
                                    <div class="flex">
                                        <div class="flex_left">
                                            <span class="fcff">*</span>  身份证正面：
                                        </div>
                                        <div class="flex_right">
                                            <div class="">
                                                <div class="">
                                                    <input th:id="'image'+${dict.value}" class="form-control"
                                                           th:name="'image'+${dict.value}" type="file">
                                                    <input th:id="'tid'+${dict.value}" th:name="'tid'+${dict.value}"
                                                           type="hidden">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="col-md-6 col-sm-6" th:each="dict : ${picList}" th:if="${dict.context == '法人/个体户身份证反面'}">
                                    <div class="flex">
                                        <div class="flex_left">
                                            <span class="fcff">*</span>  身份证反面：
                                        </div>
                                        <div class="flex_right">
                                            <div class="">
                                                <div class="">
                                                    <input th:id="'image'+${dict.value}" class="form-control"
                                                           th:name="'image'+${dict.value}" type="file">
                                                    <input th:id="'tid'+${dict.value}" th:name="'tid'+${dict.value}"
                                                           type="hidden">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="row mt10">
                                <div class="col-md-3 col-sm-4">
                                    <div class="flex">
                                        <label class="flex_left" style="width: 60px"><span class="fcff">*</span>  姓名：</label>
                                        <div class="flex_right">
                                            <input class="form-control" type="text" name="userName" id="userName"
                                                   maxlength="25" required autocomplete="off">
                                            <input class="form-control" id="userType" name="userType"
                                                   type="hidden" th:value="${userType}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-4">
                                    <div class="flex">
                                        <label class="flex_left" style="width: 80px"><span class="fcff">*</span> 身份证：</label>
                                        <div class="flex_right">
                                            <input class="form-control" type="text" id="cardId" name="cardId"
                                                   maxlength="18" aria-required="true">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-4">
                                    <div class="flex">
                                        <label class="flex_left" style="width: 80px"><span class="fcff">*</span> 详细地址：</label>
                                        <div class="flex_right">
                                            <input class="form-control" type="text" id="address" name="address"
                                                   maxlength="25" aria-required="true">
                                        </div>
                                    </div>
                                </div>
                                <!--                        <div class="col-md-4 col-sm-4">-->
                                <!--                            <div class="flex">-->
                                <!--                                <label class="flex_left" style="width: 80px"><span class="fcff">*</span>  公司名称：</label>-->
                                <!--                                <div class="flex_right">-->
                                <!--                                    <input class="form-control" type="text" id="companyName" name="companyName"-->
                                <!--                                           maxlength="50" required autocomplete="off">-->
                                <!--                                </div>-->
                                <!--                            </div>-->
                                <!--                        </div>-->

                            </div>
                            <!--                    <div class="row mt10">-->
                            <!--                        <div class="col-md-4 col-sm-4">-->
                            <!--                            <div class="flex">-->
                            <!--                                <label class="flex_left">承运商性质：</label>-->
                            <!--                                <div class="flex_right">-->
                            <!--                                    <select name="userClass" id="userClass"  class="form-control valid" aria-invalid="false">-->
                            <!--                                        <option th:each="mapS,status:${mobileUserClass}" th:value="${mapS.value}" th:text="${mapS.context}"></option>-->
                            <!--                                    </select>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                            <!--                        </div>-->
                            <!--                        <div class="col-md-4 col-sm-4">-->
                            <!--                            <div class="flex">-->
                            <!--                                <label class="flex_left"><span class="fcff">*</span>  营业执照号码：</label>-->
                            <!--                                <div class="flex_right">-->
                            <!--                                    <input class="form-control" type="text" id="businessLicense" name="businessLicense"-->
                            <!--                                           maxlength="50" aria-required="true">-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                            <!--                        </div>-->
                            <!--                    </div>-->

                            <!--                    <div class="row mt10">-->
                            <!--                        <div class="col-md-3 col-sm-6">-->
                            <!--                            <div class="flex">-->
                            <!--                                <label class="flex_left">地址：</label>-->
                            <!--                                <div class="flex_right">-->
                            <!--                                    <select name="provinceId" id="provinceId" class="form-control valid" aria-invalid="false"></select>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                            <!--                        </div>-->
                            <!--                        <div class="col-sm-3">-->
                            <!--                            <select name="cityId" id="cityId" class="form-control valid" aria-invalid="false"></select>-->
                            <!--                        </div>-->
                            <!--                        <div class="col-sm-3">-->
                            <!--                            <select name="areaId" id="areaId" class="form-control valid" aria-invalid="false"></select>-->
                            <!--                        </div>-->
                            <!--                    </div>-->
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordionThree">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title basicTile">
                            <a data-toggle="collapse" data-parent="#accordion"
                               href="tabs_panels.html#collapseThree">登录信息</a>
                        </h5>
                    </div>
                    <div id="collapseThree" class="panel-collapse collapse in">
                        <div class="panel-body">

                            <div class="row">
                                <div class="col-md-4 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left wid90"><span class="fcff">*</span>  电话号码：</label>
                                        <div class="flex_right">
                                            <input class="form-control" type="text" id="phone" name="phone"
                                                   maxlength="11" required autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 col-sm-6">
                                    <div class="col-sm-8" style="padding: 0">
                                        <div class="flex">
                                            <label class="flex_left wid90"><span class="fcff">*</span>  图形验证码：</label>
                                            <div class="flex_right">
                                                <input class="form-control" type="text" id="validateCode" name="validateCode"
                                                       maxlength="6" required autocomplete="off">
                                            </div>

                                        </div>
                                    </div>
                                    <div class="col-sm-4"><img th:src="@{/captcha/captchaImage(type=math)}" class="vcode" style="height:39px"/></div>
                                </div>

                            </div>
                            <div class="row mt10">
                                <div class="col-md-4 col-sm-6">
                                    <div class="col-sm-9" style="padding: 0">
                                        <div class="flex">
                                            <label class="flex_left wid90"><span class="fcff">*</span>  短信验证码：</label>
                                            <div class="flex_right">
                                                <input class="form-control" type="text" id="code" name="code"
                                                       maxlength="6" required autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3" id="title">
                                        <input type="button"  id="send" value="获取"
                                               class="btn btn-primary dropdown-toggle"></div>
                                </div>

                                <div class="col-md-4 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left wid90"><span class="fcff">*</span>  登录密码：</label>
                                        <div class="flex_right">
                                            <input class="form-control" type="password" id="password" name="password"
                                                   maxlength="25" required autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordionTwo">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title basicTile">
                            <a data-toggle="collapse" data-parent="#accordion"
                               href="tabs_panels.html#collapsePic">证件上传</a>
                        </h5>
                    </div>
                    <div id="collapsePic" class="panel-collapse collapse in">
                        <div class="panel-body" id="picType" style="padding-bottom: 10px">
                            <div class="row" >
                                <div class="col-md-6 col-sm-6" th:each="dict : ${picList}" th:if="${dict.context != '法人/个体户身份证正面' and dict.context != '法人/个体户身份证反面'}">
                                    <div class="mt10">
                                        <div class="">
                                            <label class="" th:text="${dict.context}+'：'">
                                            </label>
                                        </div>
                                    </div>
                                    <div class="">
                                        <div class="">
                                            <div class="">
                                                <input th:id="'image'+${dict.value}" class="form-control"
                                                       th:name="'image'+${dict.value}" type="file">
                                                <input th:id="'tid'+${dict.value}" th:name="'tid'+${dict.value}"
                                                       type="hidden">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
<!--            <div class="" style="position: fixed;bottom: 0px;width: 200px;right: 100px">-->
<!--                <input type="checkbox" id="isorno" name="isorno">-->
<!--                <span class="remember checked">&nbsp;我已看过并同意</span>-->
<!--                <a href="javascript:;" id="show" class="forget-pw">《隐私协议》</a>-->
<!--            </div>-->
        </form>

    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    //上传图片类型
    var picList = [[${picList}]];
    $(function () {
        $('.vcode').click(function() {
            var url = ctx + "captcha/captchaImage?type=math&s=" + Math.random();
            $(".vcode").attr("src", url);
        });

        /**
         * 校验
         */
        $("#register").validate({
            onkeyup: false,
            rules:{
                phone:{
                    isPhone:true,
                    remote: {
                        url: ctx + "system/user/checkPhoneUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "phonenumber": function () {
                                return $.common.trim($("#phone").val());
                            },
                            "userType": $.common.trim($("#userType").val())
                        },
                        dataFilter: function (data, type) {
                            return $.validate.unique(data);
                        }
                    }
                },
                cardId:{
                    isIdentity:true
                }
            },
            messages: {
                "phone":{
                    remote: "手机号码已经存在"
                }
            },
            focusCleanup: true
        });


        // 加载省市区
        $.provinces.init("provinceId", "cityId","areaId");

        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');

        //循环字典表中图片类型，初始化图片上传区域
        for (var i = 0; i < picList.length; i++) {
            var dictValue = picList[i].value;
            var publishFlag = "done" + dictValue;
            var picParam = {
                maxFileCount: 1,
                publish: publishFlag,  //用于绑定下一步方法
                fileType: null//文件类型
            };
            var tid = "tid" + dictValue;
            var imageId = "image" + dictValue;
            $.file.initAddFiles(imageId, tid, picParam);
        }
    });
    var prefix = ctx + "appUser";
    // 确定后提交的方法
    function submitHandler() {
        if ($.validate.form()) {
            for (var i = 0; i < picList.length; i++) {
                var dictValue = picList[i].value;
                if ($("#image" + dictValue).val() != "") {
                    $("#image" + dictValue).fileinput('upload');
                }
            }
            jQuery.subscribe(setTimeout("commit()", "1000"));
        }
    }

    //获取验证码
    $("#send").click(function () {
        //验证手机号码
        var element= "#phone";
       if ( $("#register").validate().element($(element))){
           /*if ($("#validateCode").val() === ""){
               $.modal.alertWarning("请输入图片验证码!");
               return;
           }*/
           var telephone = $(element).val();
           var codeType = '1';
           var userType = $("#userType").val();
           var validateCode = $("input[name='validateCode']").val();
           $(this).attr("disabled",true);
           timeCount();
           $.ajax({
               url: prefix + "/getMessageCode",
               method: 'POST',
               data: {"telephone": telephone, "codeType": codeType, "userType": userType,"validateCode": validateCode},
               success: function (data) {
                   $.modal.alertWarning(data.msg);
               },
               error: function () {
                   $.modal.alertWarning("验证码获取失败，请稍后重试!");
               }
           })
       }

    });
    var timeleft = 60;
    // 获取验证码按钮样式改变的定时器
    function timeCount() {
        var send =  $("#send");
        timeleft -= 1;
        if (timeleft > 0) {
            send.val(timeleft + " 秒后重发");
            setTimeout(timeCount, 1000)
        } else {
            send.val("重新发送");
            //重置等待时间
            timeleft = 60;
            send.removeAttr("disabled");
        }
    }

    $("#show").click(function () {
        window.open(prefix + "/getMobilePrivacyPolicy?userType="+$("#userType").val());
    });

    function cmtFile_1(){
        console.log("身份证正面识别开始")
        var data = {};
        data.tid = $("#tid1").val();
        data.side = 1;
        $.ajax({
            url : ctx + "common/ocr/idcardByTid",
            method : 'POST',
            data : data,
            success:function (result) {
                console.log(result);
                if(result.code == 0){
                    //驾驶证号码
                    $("#userName").val(result.data.name);
                    //身份证号码
                    $("#cardId").val(result.data.num);
                    //性别
                    $("#address").val(result.data.address);
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });
        console.log("身份证正面识别结束")
    }

    function cmtFile_2(){
        console.log("身份证反面识别开始")
        var data = {};
        data.tid = $("#tid2").val();
        data.side = 2;
        $.ajax({
            url : ctx + "common/ocr/idcardByTid",
            method : 'POST',
            data : data,
            success:function (result) {
                console.log(result);
                if(result.code == 0){
                    //身份证有效期
                    var end_date = result.data.end_date;
                    if(end_date != "") {
                        $("#cardDate").val(end_date.substring(0, 4) + "-" + end_date.substring(4, 6) + "-" + end_date.substring(6, 8));
                    }
                    //发证机关
                    $("#issuingorganizations").val(result.data.issue);
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });
        console.log("身份证反面识别结束")
    }

    // 提交方法
    function commit() {
        if (!$("#isorno").is(":checked")) {
            $.modal.alertError("请仔细阅读隐私协议,并勾选!");
            return false;
        }
        var config = {
            url: prefix+"/saveRegister",
            type: "post",
            dataType: "json",
            data: $("#register").serializeArray(),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
                $.modal.disable();
            },
            success: function(result) {
                if (result.code === 500){
                    $.modal.alertError(result.msg);
                    $.modal.closeLoading();
                }else {
                    $.modal.msgReload("注册已提交,请耐心等待审核", modal_status.SUCCESS);
                }
            }
        };
        $.ajax(config)
    }


</script>
</body>


</html>