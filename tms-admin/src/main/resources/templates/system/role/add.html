<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('新增角色')" />
	<th:block th:include="include :: ztree-css" />
</head>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<form class="form-horizontal m" id="form-role-add">
			<div class="form-group">
				<label class="col-sm-3 control-label ">角色名称：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="roleName" id="roleName" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">权限字符：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="roleKey" id="roleKey" required>
					<span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 控制器中定义的权限字符，如：@RequiresRoles("")</span>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">显示顺序：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="roleSort" id="roleSort" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">状态：</label>
				<div class="col-sm-8">
			        <label class="toggle-switch switch-solid">
                        <input type="checkbox" id="status" checked>
                        <span></span>
                    </label>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">备注：</label>
				<div class="col-sm-8">
					<input id="remark" name="remark" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">菜单权限</label>
				<div class="col-sm-8">
					<div id="menuTrees" class="ztree"></div>
				</div>
			</div>
		</form>
	</div>
	<th:block th:include="include :: footer" />
	<th:block th:include="include :: ztree-js" />
	<script type="text/javascript">
	    $(function() {
			var url = ctx + "system/menu/roleMenuTreeData";
			var options = {
				id: "menuTrees",
		        url: url,
		        check: { enable: true },
		        expandLevel: 0
		    };
			$.tree.init(options);
		});
		
		$("#form-role-add").validate({
			rules:{
				onkeyup: false,
				roleName:{
					remote: {
		                url: ctx + "system/role/checkRoleNameUnique",
		                type: "post",
		                dataType: "json",
		                data: {
		                	"roleName" : function() {
		                		return $.common.trim($("#roleName").val());
		                    }
		                },
		                dataFilter: function(data, type) {
		                	return $.validate.unique(data);
		                }
		            }
				},
				roleKey:{
					remote: {
		                url: ctx + "system/role/checkRoleKeyUnique",
		                type: "post",
		                dataType: "json",
		                data: {
		                	"roleName" : function() {
		                        return $.common.trim($("#roleName").val());
		                    }
		                },
		                dataFilter: function(data, type) {
		                	return $.validate.unique(data);
		                }
		            }
				},
				roleSort:{
					digits:true
				},
			},
			messages: {
		        "roleName": {
		            remote: "角色名称已经存在"
		        },
		        "roleKey": {
		            remote: "角色权限已经存在"
		        }
		    },
		    focusCleanup: true
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
	        	add();
	        }
	    }
	
		function add() {
			var roleName = $("input[name='roleName']").val();
			var roleKey = $("input[name='roleKey']").val();
			var roleSort = $("input[name='roleSort']").val();
			var status = $("input[id='status']").is(':checked') == true ? 0 : 1;
			var remark = $("input[name='remark']").val();
			var menuIds = $.tree.getCheckedNodes();
			$.ajax({
				cache : true,
				type : "POST",
				url : ctx + "system/role/add",
				data : {
					"roleName": roleName,
					"roleKey": roleKey,
					"roleSort": roleSort,
					"status": status,
					"remark": remark,
					"menuIds": menuIds
				},
				async : false,
				error : function(request) {
					$.modal.alertError("系统错误");
				},
				success : function(data) {
					$.operate.successCallback(data);
				}
			});
		}
	</script>
</body>
</html>
