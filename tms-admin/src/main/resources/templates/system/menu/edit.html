<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('修改菜单')" />
</head>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<form class="form-horizontal m" id="form-menu-edit" th:object="${menu}">
			<input name="menuId"   type="hidden" th:field="*{menuId}"   />
			<input id="treeId" name="parentId" type="hidden" th:field="*{parentId}" />
			<div class="form-group">
				<label class="col-sm-3 control-label ">上级菜单：</label>
				<div class="col-sm-8">
				    <div class="input-group">
						<input class="form-control" type="text" onclick="selectMenuTree()" id="treeName" readonly="true" th:value="${menu.parentName == null ? '无' : menu.parentName}">
					    <span class="input-group-addon"><i class="fa fa-search"></i></span>
				    </div>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">菜单类型：</label>
				<div class="col-sm-8">
					<label class="radio-box"> <input type="radio" th:field="*{menuType}" name="menuType" value="M" /> 目录 </label> 
					<label class="radio-box"> <input type="radio" th:field="*{menuType}" name="menuType" value="C" /> 菜单 </label> 
					<label class="radio-box"> <input type="radio" th:field="*{menuType}" name="menuType" value="F" /> 按钮 </label>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">菜单名称：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="menuName" id="menuName" th:field="*{menuName}" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">请求地址：</label>
				<div class="col-sm-8">
					<input id="url" name="url" class="form-control" type="text" th:field="*{url}">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">打开方式：</label>
				<div class="col-sm-8">
					<select id="target" name="target" class="form-control m-b">
	                    <option value="menuItem" th:field="*{target}">页签</option>
	                    <option value="menuBlank" th:selected="*{target == 'menuBlank'}">新窗口</option>
	                </select>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">权限标识：</label>
				<div class="col-sm-8">
					<input id="perms" name="perms" class="form-control" type="text" th:field="*{perms}">
				    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 控制器中定义的权限标识，如：@RequiresPermissions("")</span>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">显示排序：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="orderNum" th:field="*{orderNum}" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">图标：</label>
				<div class="col-sm-8">
					<input id="icon" name="icon" class="form-control" type="text" placeholder="选择图标" th:field="*{icon}">
                    <div class="ms-parent" style="width: 100%;">
                        <div class="icon-drop animated flipInX" style="display: none;max-height:200px;overflow-y:auto">
                            <div data-th-include="system/menu/icon"></div>
                        </div>
                    </div>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">菜单状态：</label>
				<div class="col-sm-8">
					<div class="radio-box" th:each="dict : ${@dict.getType('sys_show_hide')}">
						<input type="radio" th:id="${dict.dictCode}" name="visible" th:value="${dict.dictValue}" th:field="*{visible}">
						<label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
					</div>
				</div>
			</div>
		</form>
	</div>
	<th:block th:include="include :: footer" />
	 <script>
        var prefix = ctx + "system/menu";

        $(function() {
            var menuType = $('input[name="menuType"]:checked').val();
            menuVisible(menuType);
        });

        $("#form-menu-edit").validate({
        	onkeyup: false,
        	rules:{
        		menuType:{
        			required:true,
        		},
        		menuName:{
        			remote: {
                        url: prefix + "/checkMenuNameUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                        	"menuId": function() {
                                return $("#menuId").val();
                            },
                            "parentId": function() {
		                		return $("input[name='parentId']").val();
		                    },
                			"menuName": function() {
                                return $.common.trim($("#menuName").val());
                            }
                        },
                        dataFilter: function(data, type) {
                        	return $.validate.unique(data);
                        }
                    }
        		},
        		orderNum:{
        			digits:true
        		},
        	},
        	messages: {
                "menuName": {
                    remote: "菜单已经存在"
                }
            },
            focusCleanup: true
        });
        
        function submitHandler() {
	        if ($.validate.form()) {
	            $.operate.save(prefix + "/edit", $('#form-menu-edit').serialize());
	        }
	    }

        $(function() {
            $("input[name='icon']").focus(function() {
                $(".icon-drop").show();
            });
            $("#form-menu-edit").click(function(event) {
                var obj = event.srcElement || event.target;
                if (!$(obj).is("input[name='icon']")) {
                    $(".icon-drop").hide();
                }
            });
            $(".icon-drop").find(".ico-list i").on("click",
            function() {
                $('#icon').val($(this).attr('class'));
            });
            $('input').on('ifChecked',
            function(event) {
                var menuType = $(event.target).val();
                menuVisible(menuType);
            });
        });

        function menuVisible(menuType) {
            if (menuType == "M") {
                $("#url").parents(".form-group").hide();
                $("#perms").parents(".form-group").hide();
                $("#icon").parents(".form-group").show();
                $("#target").parents(".form-group").hide();
            } else if (menuType == "C") {
                $("#url").parents(".form-group").show();
                $("#perms").parents(".form-group").show();
                $("#icon").parents(".form-group").hide();
                $("#target").parents(".form-group").show();
            } else if (menuType == "F") {
                $("#url").parents(".form-group").hide();
                $("#perms").parents(".form-group").show();
                $("#icon").parents(".form-group").hide();
                $("#target").parents(".form-group").hide();
            }
        }

        /*菜单管理-修改-选择菜单树*/
        function selectMenuTree() {
        	var menuId = $("#treeId").val();
        	if(menuId > 0) {
        		var url = prefix + "/selectMenuTree/" + menuId;
        		$.modal.open("选择菜单", url, '380', '380');
        	} else {
        		$.modal.alertError("主菜单不能选择");
        	}
        }
        
        function selectMenuTree() {
        	var menuId = $("#treeId").val();
        	if(menuId > 0) {
        		var url = prefix + "/selectMenuTree/" + menuId;
        		var options = {
       				title: '菜单选择',
       				width: "380",
       				url: url,
       				callBack: doSubmit
       			};
       			$.modal.openOptions(options);
        	} else {
        		$.modal.alertError("主菜单不能选择");
        	}
		}
		
		function doSubmit(index, layero){
			var body = layer.getChildFrame('body', index);
   			$("#treeId").val(body.find('#treeId').val());
   			$("#treeName").val(body.find('#treeName').val());
   			layer.close(index);
		}
    </script>
</body>
</html>
