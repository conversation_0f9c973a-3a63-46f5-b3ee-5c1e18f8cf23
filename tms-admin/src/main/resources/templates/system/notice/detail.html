<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('公告')" />
</head>
<body class="gray-bg">
    <div th:if="${notice != null}">
        <div class="mail-box-header">
            <h2 th:text="${notice.noticeTitle}"></h2>
            <div class="mail-tools tooltip-demo m-t-md">
                <h5>
                    <span class="pull-right font-noraml" th:text="${#dates.format(notice.createTime, 'yyyy年MM月dd日 HH:mm')}"></span>
                    <div th:if="${notice.noticeType == '1'}">
                        <span class="font-noraml">类型： </span>通知
                    </div>
                </h5>
                <h4 th:if="${not #lists.isEmpty(sysUploadFiles) and #lists.size(sysUploadFiles) > 0}">
                    <div>
                        <span class="font-noraml">附件： </span>
                        <span th:each="mapS,status:${sysUploadFiles}" class="mr10">
                            <a href='#' th:data-file-path="${mapS.filePath}" th:data-file-name="${mapS.fileName}"
                               onclick="openFile(this)">
                               [[${mapS.fileName}]]
                            </a>
                        </span>
                    </div>
                </h4>
            </div>
        </div>
        <div class="mail-box">
            <div class="mail-body" th:utext="${notice.noticeContent}"></div>
        </div>
    </div>
    <div th:if="${notice == null}">
        <div class="middle-box text-center animated fadeInDown">
            <h3 class="font-bold">找不到该公告！</h3>
            <div class="error-desc">
                该公告已过期或已被删除，请刷新首页！
            </div>
        </div>
    </div>


    <script th:inline="javascript">
        function openFile(element) {
            const filePath = element.getAttribute('data-file-path');
            const fileName = element.getAttribute('data-file-name');

            window.open( document.location.protocol + "//" + document.location.host + filePath, fileName);
        }

    </script>
</body>
</html>