<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('部门列表')" />
    <style>
        .bootstrap-tree-table.treetable-tbody tr {
            background-color: #ffffff;
        }
        .bootstrap-tree-table.treetable-tbody tr:nth-of-type(odd) {
            background-color: #f9f9f9;
        }
        .bootstrap-tree-table.treetable-tbody tr:hover {
            background-color: #f5f5f5;
        }
        .select-table {
            border: 1px solid #e7eaec;
            border-radius: 3px;
            background-color: #fff;
        }

        .container {
            padding: 20px;
            padding-top: 10px;
        }

        .radio-disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .form-inline {
            display: flex;
            align-items: center;
        }
        .form-inline.form-group {
            display: flex;
            align-items: center;
        }
        .form-inline.form-group label {
            margin-right: 10px;
        }
        .form-inline.form-group a {
            margin-left: 10px;
        }
    </style>

</head>
<body class="">
<div class="container ">
    <div class="row">
        <div class="col-sm-12">
            <form id="role-form" style="display: flex; align-items: center;">
                <label class="control-label" style="margin-right: 10px;width: 80px">责任人：</label>
                <input id="userName" name="userName" placeholder="请输入责任人，多人英文逗号拼接" class="form-control valid"
                       type="text" aria-required="true" autocomplete="false" style="width: 200px">
                <a class="btn btn-primary btn-rounded btn-xs" onclick="$.treeTable.search()" style="margin-left: 10px;"><i
                        class="fa fa-search"></i> 搜索</a>
            </form>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-tree-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    $(function() {
        var options = {
            code: "userId",
            parentCode: "userName",
            uniqueId: "userId",
            url: ctx + "system/dept/opsGroupTreeZRR/list",
            modalName: "部门",
            showSearch: false,
            showRefresh: false,
            showColumns: false,
            expandAll: true,
            expandFirst: false,
            // 添加多选配置（根据插件要求）
            multiple: true,           // 启用多选模式
            checkbox: true,           // 显示复选框
            onLoadSuccess: function(data) {
                disableParentNodes();
            },
            columns: [
                {
                    field: 'selectItem',
                    checkbox: true,    // 修改为复选框
                    formatter: function(value, row, index) {
                        return '<input type="checkbox">';
                    }
                },
                {
                    field: 'userName',
                    title: '责任人',
                    align: "left"
                },
                {
                    field: 'phonenumber',
                    title: '联系方式',
                    align: "left"
                }]
        };
        $.treeTable.init(options);

        function disableParentNodes() {
            var $table = $('#bootstrap-tree-table');
            var allRows = $table.bootstrapTreeTable('getData');

            allRows.forEach(function(row) {
                var hasChildren = allRows.some(function(potential_child) {
                    return potential_child.parentId === row.deptId;
                });

                if (hasChildren) {
                    var $row = $table.find('tr#' + row.row_id);
                    var $checkbox = $row.find('input[type="checkbox"]');
                    // 禁用复选框并添加样式
                    $checkbox.prop('disabled', true)
                        .addClass('radio-disabled')
                        .on('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                        });
                    // 阻止行点击事件
                    $row.off('click').on('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                    });
                }
            });
        }
    });

    function getChecked() {
        // 这个方法通常会自动处理多选情况
        return $("#bootstrap-tree-table").bootstrapTreeTable('getSelections');
    }
</script>
</body>
</html>