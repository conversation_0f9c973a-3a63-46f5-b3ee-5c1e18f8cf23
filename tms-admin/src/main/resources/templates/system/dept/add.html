<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('新增部门')" />
</head>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<form class="form-horizontal m" id="form-dept-add">
			<input id="treeId" name="parentId" type="hidden" th:value="${dept.deptId}"   />
			<div class="form-group">
				<label class="col-sm-3 control-label ">上级部门：</label>
				<div class="col-sm-8">
				    <div class="input-group">
						<input class="form-control" type="text" onclick="selectDeptTree()" id="treeName" readonly="true" th:value="${dept.deptName}">
					    <span class="input-group-addon"><i class="fa fa-search"></i></span>
				    </div>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">部门名称：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="deptName" id="deptName" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">显示排序：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="orderNum" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">负责人：</label>
				<div class="col-sm-8">
					<div class="input-group">
						<input class="form-control" type="text" name="leader" id="leader" required>
						<div class="input-group-btn">
							<button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
								<span class="caret"></span>
							</button>
							<ul class="dropdown-menu dropdown-menu-right" role="menu">
							</ul>
						</div>
					</div>

				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">联系电话：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="phone" id="phone">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">邮箱：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="email">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">部门状态：</label>
				<div class="col-sm-8">
				    <div class="radio-box" th:each="dict : ${@dict.getType('sys_normal_disable')}">
						<input type="radio" th:id="${dict.dictCode}" name="status" th:value="${dict.dictValue}" th:checked="${dict.default}">
						<label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
					</div>
				</div>
			</div>
		</form>
	</div>
	<th:block th:include="include :: footer" />
	<th:block th:include="include :: bootstrap-suggest-js"/>

	<script type="text/javascript">
		var prefix = ctx + "system/dept";

		$(function() {
			initBsSuggest();
		});

		function initBsSuggest() {
			$(`#leader`).bsSuggest('init', {
				url: ctx + "system/user/findUserInfo?keyword=",
				indexId: 0,
				showBtn: false,
				allowNoKeyword: false,
				getDataMethod: "url",
				keyField: "userName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
				effectiveFields: ["userName", "deptName", "phonenumber"],
				effectiveFieldsAlias: {"userName": "用户名", "deptName": "部门", "phonenumber": "手机号码"},
				delay: 300,
				searchingTip: '搜索中...',
				hideOnSelect: true,
				maxOptionCount: 10,
				inputWarnColor: '',
			}).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参
				$(`#leader`).val(data.userName);
				$(`#phone`).val(data.phonenumber);
			})
		}

		$("#form-dept-add").validate({
			onkeyup: false,
			rules:{
				deptName:{
					remote: {
		                url: prefix + "/checkDeptNameUnique",
		                type: "post",
		                dataType: "json",
		                data: {
		                	"parentId": function() {
		                		return $("input[name='parentId']").val();
		                    },
		                	"deptName" : function() {
		                        return $.common.trim($("#deptName").val());
		                    }
		                },
		                dataFilter: function(data, type) {
		                	return $.validate.unique(data);
		                }
		            }
				},
				orderNum:{
					digits:true
				},
				email:{
                    email:true,
        		},
        		phone:{
        			isPhone:true,
        		},
			},
			messages: {
		        "deptName": {
		            remote: "部门已经存在"
		        }
		    },
		    focusCleanup: true
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
	        	$.operate.save(prefix + "/add", $('#form-dept-add').serialize());
	        }
	    }
	
		/*部门管理-新增-选择父部门树*/
		function selectDeptTree() {
			var options = {
				title: '部门选择',
				width: "380",
				url: prefix + "/selectDeptTree/" + $("#treeId").val(),
				callBack: doSubmit
			};
			$.modal.openOptions(options);
		}
		
		function doSubmit(index, layero){
			var body = layer.getChildFrame('body', index);
   			$("#treeId").val(body.find('#treeId').val());
   			$("#treeName").val(body.find('#treeName').val());
   			layer.close(index);
		}
	</script>
</body>
</html>
