<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('部门列表')" />
    <style>
        .bootstrap-tree-table .treetable-tbody tr {
            background-color: #ffffff;
        }
        .bootstrap-tree-table .treetable-tbody tr:nth-of-type(odd) {
            background-color: #f9f9f9;
        }
        .bootstrap-tree-table .treetable-tbody tr:hover {
            background-color: #f5f5f5;
        }
        .select-table {
            border: 1px solid #e7eaec;
            border-radius: 3px;
            background-color: #fff;
        }

        .container {
            padding: 20px;
        }

        .radio-disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

    </style>

</head>
<body class="">
<div class="container ">
    <div class="row">
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-tree-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    $(function() {
        var options = {
            code: "deptId",
            parentCode: "parentId",
            uniqueId: "deptId",
            url: ctx + "system/dept/opsGroupTreeTrans/list",
            modalName: "部门",
            // 隐藏工具栏
            showSearch: false,
            showRefresh: false,
            showColumns: false,
            expandAll: true,
            expandFirst: false,
            // 添加数据加载完成的回调
            onLoadSuccess: function(data) {
                disableParentNodes();
            },
            columns: [
                {
                    field: 'selectItem',
                    radio: true,
                },
                {
                    field: 'deptName',
                    title: '部门名称',
                    align: "left"
                },
                {
                    field: 'deptId',
                    title: '部门id',
                    align: "left"
                },
                {
                    field: 'leader',
                    title: '负责人',
                    align: "left"
                }]
        };
        $.treeTable.init(options);

        function disableParentNodes() {
            var $table = $('#bootstrap-tree-table');
            var allRows = $table.bootstrapTreeTable('getData');

            // 遍历所有行
            allRows.forEach(function(row) {
                // 检查是否有子节点
                var hasChildren = allRows.some(function(potential_child) {
                    return potential_child.parentId === row.deptId;
                });

                if (hasChildren) {
                    // 找到对应行的单选框并禁用
                    var $row = $table.find('tr#' + row.row_id);
                    var $radio = $row.find('input[type="radio"]');
                    $radio.prop('disabled', true)
                        .addClass('radio-disabled')
                        .click(function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            return false;
                        });
                    
                    // 为整行添加点击事件处理
                    $row.off('click').on('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        return false;
                    });
                }
            });
        }
    });

    function getChecked() {
        var rows = $("#bootstrap-tree-table").bootstrapTreeTable('getSelections');
        return rows;
    }

</script>
</body>
</html>