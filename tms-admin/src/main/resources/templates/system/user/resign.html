<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <th:block th:include="include :: header('离职交接')"/>
    <style>
        .form-content {
            padding: 20px;
        }

        .section-block {
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .section-header {
            display: flex;
            margin-bottom: 5px;
            border-bottom: 1px solid #f0f0f0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }

        .handler-container {
            position: relative;
            margin-left: auto; /* 添加这行，确保容器靠右 */
            width: auto !important;
        }

        .handler-container .input-group {
            float: right;
            width: auto !important;
        }

        .handler-input {
            width: 200px !important;
        }

        .input-group-btn {
            display: inline-block;
        }

        .input-group-btn .dropdown-menu {
            width: 200px !important;
            left: auto !important;
            right: 0;
        }


        .info-item {
            margin: 8px 0;
            color: #666;
        }

        .info-label {
            display: inline-block;
            width: 100px;
            color: #999;
        }

        /* 新增的CSS样式 */
        .app-contact-inputs {
            display: flex;
            flex-direction: row;
            gap: 10px;
        }
        
        .app-contact-input {
            position: relative;
        }
        .cust-checkbox {
            display: inline-flex;
            align-items: center;
            cursor: pointer;
            margin-right: 15px;
        }
        .cust-checkbox input[type="checkbox"] {
            appearance: none;
            width: 18px;
            height: 18px;
            border: 2px solid #595757;
            border-radius: 0%;
            margin-right: 6px;
            outline: none;
            position: relative;
            transition: all 0.2s ease;
        }
        .cust-checkbox input[type="checkbox"]:checked {
            border-color: #2ecc71;
            background-color: #2ecc71;
        }
        .cust-checkbox input[type="checkbox"]:checked::after {
            content: "";
            position: absolute;
            left: 6px;
            top: 3px;
            width: 4px;
            height: 8px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }
        .cust-checkbox span {
            font-size: 14px;
            color: #333;
        }
    </style>
</head>

<body>
<div class="form-content">
    <!-- 基础信息 -->
    <div class="section-block">
        <div class="section-header">
            <span class="section-title">基础信息</span>
            <!--                <input type="text" class="form-control handler-input" name="marketingCenterHandler" placeholder="请输入交接人">-->

        </div>
        <div class="info-item">
            <span class="info-label">所属部门：</span>
            <span th:text="${sysUser.dept?.deptName}"></span>
        </div>
        <div class="info-item">
            <span class="info-label">角色：</span>
            <span th:if="${not #lists.isEmpty(roles)}">
                    <span th:each="role,roleStat : ${roles}">
                        <span th:text="${role.roleName}"></span>
                        <span th:if="${!roleStat.last}">, </span>
                    </span>
                </span>
            <span th:if="${#lists.isEmpty(roles)}">暂无角色</span>
        </div>
    </div>

    <!-- 营销中心交接 -->
    <div class="section-block" th:if="${not #lists.isEmpty(custCrmAclVOS)}">
        <div class="section-header">
            <span class="section-title">营销中心交接</span>
            <div class="handler-container">
                <input type="text" class="form-control handler-input" name="custCrmAclUser" placeholder="请输入交接人">
                <div class="input-group-btn">
                    <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                </div>
            </div>

        </div>
        <div class="sub-item">
                <span th:each="custCrmAcl,custStat : ${custCrmAclVOS}">
                    <label class="cust-checkbox">
                        <input type="checkbox" th:value="${custCrmAcl.id}" th:name="crmAclIds" checked>
                        <span style="padding-right: 5px" th:text="${custCrmAcl.custAbbr}"></span>
                    </label>
                </span>
        </div>
    </div>

    <!-- 运营部交接 -->
    <div class="section-block" th:if="${not #lists.isEmpty(salesGroupList)}">
        <div class="section-header">
            <span class="section-title">运营部负责人交接</span>
            <div class="handler-container">
                <input type="text" class="form-control handler-input" name="salesGroupUser" placeholder="请输入交接人">
                <div class="input-group-btn">
                    <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                </div>
            </div>
        </div>
        <div class="sub-item">
                <span th:each="group,groupStat : ${salesGroupList}">
                     <label class="cust-checkbox">
                        <input type="checkbox" th:value="${group.id}" th:name="groupIds" checked>
                        <span style="padding-right: 5px" th:text="${group.salesName}"></span>
                    </label>
                </span>
        </div>
    </div>

    <!-- 承运商引荐交接 -->
    <div class="section-block" th:if="${not #lists.isEmpty(carrierList)}">
        <div class="section-header">
            <span class="section-title">承运商引荐交接</span>
            <div class="handler-container">
                <input type="text" class="form-control handler-input" name="recommendMan" placeholder="请输入交接人">
                <div class="input-group-btn">
                    <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                </div>
            </div>

        </div>
        <div class="sub-item">
                <span th:each="carrier,carrierStat : ${carrierList}">
                     <label class="cust-checkbox">
                        <input type="checkbox" th:value="${carrier.carrierId}" th:name="recommendCarrierIds" checked>
                        <span style="padding-right: 5px" th:text="${carrier.carrName}"></span>
                    </label>
                </span>
        </div>
    </div>

    <!-- 客服交接 -->
    <div class="section-block" th:if="${not #lists.isEmpty(clientServiceList)}">
        <div class="section-header">
            <span class="section-title">客服交接</span>
            <div class="handler-container">
                <input type="text" class="form-control handler-input" name="clientServiceUser"
                       placeholder="请输入交接人">
                <div class="input-group-btn">
                    <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                </div>
            </div>

        </div>
        <div class="sub-item">
                <span th:each="client,clientStat : ${clientServiceList}">

                     <label class="cust-checkbox">
                        <input type="checkbox" th:value="${client.customerId}" th:name="clientServiceIds" checked>
                        <span style="padding-right: 5px" th:text="${client.custAbbr}"></span>([[${client.saleDeptParentName}]][[${client.saleDeptName}]]
                         <span th:if="${client.isEnabled == 1}" style="color:red">禁用</span>
                         )
                    </label>

                </span>
        </div>
    </div>

    <!-- 客户app联系人 -->
    <div class="section-block" th:if="${not #lists.isEmpty(appDeliContactList)}">
        <div class="section-header">
            <span class="section-title">客户app联系人交接</span>
            <div class="handler-container">
                <div class="app-contact-inputs">
                    <div class="app-contact-input">
                        <input type="text" class="form-control handler-input" id="appDeliContact" name="appDeliContact"
                               placeholder="请输入交接人">
                        <div class="input-group-btn">
                            <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                        </div>
                    </div>
                    <div class="app-contact-input">
                        <input type="text" class="form-control handler-input" id="appDeliMobile" name="appDeliMobile"
                               placeholder="联系人手机">
                    </div>
                </div>
            </div>
        </div>
        <div class="sub-item">
                <span th:each="client,clientStat : ${appDeliContactList}">
                      <label class="cust-checkbox">
                        <input type="checkbox" th:value="${client.customerId}" th:name="appDeliContactIds" checked>
                         <span style="padding-right: 5px" th:text="${client.custAbbr}"></span>([[${client.saleDeptParentName}]][[${client.saleDeptName}]]
                         <span th:if="${client.isEnabled == 1}" style="color:red">禁用</span>
                         )
                    </label>
                </span>
        </div>
    </div>

    <!-- 部门负责人交接 -->
    <div class="section-block" th:if="${not #lists.isEmpty(deptLeaderList)}">
        <div class="section-header">
            <span class="section-title">部门负责人交接</span>
            <div class="handler-container">
                <input type="text" class="form-control handler-input" name="deptLeaderUser" placeholder="请输入交接人">
                <div class="input-group-btn">
                    <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                </div>
            </div>

        </div>
        <div class="sub-item">
                <span th:each="deptLeader,deptLeaderStat : ${deptLeaderList}">
                     <label class="cust-checkbox">
                        <input type="checkbox" th:value="${deptLeader.deptId}" th:name="deptLeaderUserIds" checked>
                        <span style="padding-right: 5px" th:text="${deptLeader.deptName}"></span>
                    </label>
                </span>
        </div>
    </div>

</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-suggest-js"/>

<script th:inline="javascript">
    var userId = [[${sysUser.userId}]]

    $(function () {
        initHandlerSuggest("custCrmAclUser")
        initHandlerSuggest("salesGroupUser")
        initHandlerSuggest("recommendMan")
        initHandlerSuggest("clientServiceUser")
        initHandlerSuggest("deptLeaderUser")
        initAppDeliContactBsSuggest()
    });


    function initAppDeliContactBsSuggest() {
        $(`#appDeliContact`).bsSuggest('init', {
            url: ctx + "system/user/findUserInfo?keyword=",
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "userName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: ["userName", "deptName", "phonenumber"],
            effectiveFieldsAlias: {"userName": "用户名", "deptName": "部门", "phonenumber": "手机号码"},
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参
            $(`#appDeliContact`).val(data.userName);
            $(`#appDeliMobile`).val(data.phonenumber);
        })
    }


    function initHandlerSuggest(inputName) {
        $(`input[name='${inputName}']`).bsSuggest('init', {
            url: ctx + "system/user/findUserInfo?keyword=",
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "userName",
            effectiveFields: ["userName", "deptName", "phonenumber"],
            effectiveFieldsAlias: {
                "userName": "用户名",
                "deptName": "部门",
                "phonenumber": "手机号码"
            },
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',

        }).on('onSetSelectValue', function (e, keyword, data) {
            $(this).val(data.userName);
            // 添加一个隐藏字段存储用户ID
            let hiddenInput = $(`<input type="hidden" name="${inputName}Id">`);
            hiddenInput.val(data.userId);
            $(this).after(hiddenInput);
        });
    }

    function getSelectedCrmAclIds(val) {
        return $('input[name="'+val+'"]:checked') // 选择所有name为crmAclIds且已选中的checkbox
            .map(function() {
                return $(this).val(); // 获取每个选中项的值
            })
            .get() // 将jQuery对象转换为普通数组
            .join(','); // 用逗号拼接数组元素
    }

    function submitHandler(callback) {
        // 构建提交数据
        var data = {
            userId: userId,
            crmAclIds: getSelectedCrmAclIds("crmAclIds"),
            custCrmAclUserId: $("input[name='custCrmAclUserId']").val(),
            groupIds: getSelectedCrmAclIds("groupIds"),
            salesGroupUserId: $("input[name='salesGroupUserId']").val(),
            recommendCarrierIds: getSelectedCrmAclIds("recommendCarrierIds"),
            recommendManId: $("input[name='recommendManId']").val(),
            clientServiceIds: getSelectedCrmAclIds("clientServiceIds"),
            clientServiceUserId: $("input[name='clientServiceUserId']").val(),
            deptLeaderUserId: $("input[name='deptLeaderUserId']").val(),
            appDeliContactIds: getSelectedCrmAclIds("appDeliContactIds"),
            deptLeaderUserId: $("input[name='deptLeaderUserId']").val(),
            deptLeaderUserIds: getSelectedCrmAclIds("deptLeaderUserIds"),
            appDeliContact: $("input[name='appDeliContact']").val(),
            appDeliMobile: $("input[name='appDeliMobile']").val()
        };

        // 发送请求
        $.ajax({
            type: "POST",
            url: ctx + "system/user/resign",
            contentType: "application/json",
            data: JSON.stringify(data),
            success: function (result) {
                if (callback) {
                    callback(result);
                }
                if (result.code == 0) {
                    $.modal.msgSuccess("离职交接成功");
                } else {
                    $.modal.msgError(result.msg);
                }
            },
            error: function () {
                $.modal.msgError("系统错误");
            }
        });
    }


</script>
</body>
</html>