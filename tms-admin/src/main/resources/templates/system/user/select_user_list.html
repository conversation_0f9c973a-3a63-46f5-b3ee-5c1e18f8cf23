<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('用户列表')" />
    <th:block th:include="include :: layout-latest-css" />
	<th:block th:include="include :: ztree-css" />
</head>

<body class="gray-bg">
	<div class="ui-layout-west">
		<div class="main-content">
			<div class="box box-main">
				<div class="box-header">
					<div class="box-title">
						<i class="fa icon-grid"></i> 部门选择
					</div>
					<div class="box-tools pull-right">
						<button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i class="fa fa-chevron-up"></i></button>
						<button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
						<button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新部门"><i class="fa fa-refresh"></i></button>
					</div>
				</div>
				<div class="ui-layout-content">
					<div id="tree" class="ztree"></div>
				</div>
			</div>
		</div>
	</div>

	<div class="ui-layout-center">
		<div class="container-div">
			<div class="row">
				<div class="col-sm-12 search-collapse">
					<form id="user-form">
						<input type="hidden" id="deptId" name="deptId">
		                <input type="hidden" id="parentId" name="parentId">
						<div class="select-list">
                            <ul>
                                <li>
                                    用户名称：<input type="text" name="userName"/>
                                </li>
                                <li>
                                    手机号码：<input type="text" name="phonenumber"/>
                                </li>
                                <li>
                                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                </li>
                            </ul>
						</div>
					</form>
				</div>

		        <div class="col-sm-12 select-table table-striped">
				    <table id="bootstrap-table" data-mobile-responsive="true"></table>
				</div>
			</div>
		</div>
	</div>

	<th:block th:include="include :: footer" />
	<th:block th:include="include :: layout-latest-js" />
	<th:block th:include="include :: ztree-js" />
	<script th:inline="javascript">
		var editFlag = [[${@permission.hasPermi('system:user:edit')}]];
		var removeFlag = [[${@permission.hasPermi('system:user:remove')}]];
		var resetPwdFlag = [[${@permission.hasPermi('system:user:resetPwd')}]];
		var prefix = ctx + "system/user";

        var method = [[${method}]];

		$(function() {
		    var panehHidden = false;
		    if ($(this).width() < 769) {
		        panehHidden = true;
		    }
		    $('body').layout({ initClosed: panehHidden, west__size: 185 });
		    queryUserList();
		    queryDeptTree();
		});

		function queryUserList() {
		    var options = {
		        url: prefix + "/"+method,
		        createUrl: prefix + "/add",
		        updateUrl: prefix + "/edit/{id}",
		        removeUrl: prefix + "/remove",
		        exportUrl: prefix + "/export",
		        importUrl: prefix + "/importData",
		        importTemplateUrl: prefix + "/importTemplate",
		        sortName: "createTime",
		        sortOrder: "desc",
		        modalName: "用户",
                showSearch:false,
                showRefresh:false,
                showToggle:false,
                showColumns:false,
		        columns: [{
		            checkbox: true
		        },
                {
                        field: 'userName',
                        title: '用户名称'
                    },
                    {
                        field: 'dept.deptName',
                        title: '部门'
                    },
                    {
                        field: 'email',
                        title: '邮箱'
                    },
                    {
                        field: 'phonenumber',
                        title: '手机'
                    }]
		    };
		    $.table.init(options);
		}

		function queryDeptTree(){

			var options = {
		        url: ctx + "system/dept/treeData",
		        expandLevel: 1,
                check: { enable: true,nocheckInherit: true },
		        // onClick : zOnClick
                onCheck : zOnCheck
		    };
			$.tree.init(options);

			// function zOnClick(event, treeId, treeNode) {
			// 	$("#deptId").val(treeNode.id);
			// 	$("#parentId").val(treeNode.pId);
			// 	$.table.search();
			// }
            let deptId=[];
            let parentId=[];
            function zOnCheck(event, treeId, treeNode){
                if(treeNode.checked){
                    deptId.push(treeNode.id)
                    parentId.push(treeNode.pId)
                }else{
                    deptId.forEach((res,i)=>{
                        if(res==treeNode.id){
                            deptId.splice(i, 1)
                        }
                    })
                    parentId.forEach((res,i)=>{
                        if(res==treeNode.pId){
                            parentId.splice(i, 1)
                        }
                    })
                }
                $("#deptId").val(deptId.join(','));
				$("#parentId").val(parentId.join(','));
                $.table.search();
            }
		}

		$('#btnExpand').click(function() {
			$._tree.expandAll(true);
		    $(this).hide();
		    $('#btnCollapse').show();
		});

		$('#btnCollapse').click(function() {
			$._tree.expandAll(false);
		    $(this).hide();
		    $('#btnExpand').show();
		});

		$('#btnRefresh').click(function() {
			queryDeptTree();
		});


		  /** 获取选中行*/
          function getChecked() {
            return $.btTable.bootstrapTable('getSelections');
        }

        /**
         * 选择后提交
         */
        function submitHandler() {
            var rows = $.table.selectFirstColumns();
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            $.modal.close();
            var target = [[${param.target == null?null:param.target[0]}]];
            if (!target) {
                parent.$("#userId").val($.table.selectColumns("userId").join());
                parent.$("#userName").val($.table.selectColumns("userName").join());
            } else {
                parent.$("#"+target+"Id").val($.table.selectColumns("userId").join());
                parent.$("#"+target+"Name").val($.table.selectColumns("userName").join());
            }
        }

	</script>
</body>
</html>