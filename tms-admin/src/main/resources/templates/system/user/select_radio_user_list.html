<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('用户列表')" />
</head>

<body class="gray-bg">

<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="user-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                用户名称：<input type="text" name="userName"/>
                            </li>
                            <li>
                                手机号码：<input type="text" name="phonenumber"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
</div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/user";
        var method = [[${method}]];


        $(function() {
            $(document).keyup(function(e){
                var key = e.which;
                if(key==13){
                    $.table.search();
                }
            });

            var options = {
                url: prefix + "/" + method,
                modalName: "用户",
                clickToSelect:true,
                columns: [{
                    radio: true
                },
                    {
                        field: 'userName',
                        title: '用户名称'
                    },
                    {
                        field: 'dept.deptName',
                        title: '部门'
                    },
                    {
                        field: 'email',
                        title: '邮箱'
                    },
                    {
                        field: 'phonenumber',
                        title: '手机'
                    },]
            };
            $.table.init(options);
        });

        /** 获取选中行*/
        function getChecked() {
            return $.btTable.bootstrapTable('getSelections');
        }

        /**
         * 选择后提交
         */
        function submitHandler() {
            var rows = $.table.selectFirstColumns();
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            $.modal.close();
            parent.$("#userId").val($.table.selectColumns("userId").join());
            parent.$("#userName").val($.table.selectColumns("userName").join());
        }

    </script>
</body>
</html>