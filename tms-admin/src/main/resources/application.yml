# 项目相关配置
ruoyi:
  # 名称
  name: TMS
  # 版本
  version: 3.4.0
  # 版权年份
  copyrightYear: 2019
  # 实例演示开关
  demoEnabled: false
  # 文件路径
  profile: D:/static/
  # 获取ip地址开关
  addressEnabled: true
  # 项目路径
  projectPath:

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为80
  port: 8980
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30
    max-connections: 30000

# 日志配置
logging:
  #config: logback.xml
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码错误{maxRetryCount}次锁定10分钟
    maxRetryCount: 5

# Spring配置
spring:
  aop:
    auto: true
    proxy-target-class: true
  # 模板引擎
  thymeleaf:
    mode: HTML
    encoding: utf-8
    # 禁用缓存
    cache: false
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: static/i18n/messages
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  profiles:
    active: druid
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size: 150MB
       # 设置总上传的文件大小
       max-request-size: 200MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true

# MyBatis
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.ruoyi
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath*:mapper/**/*Mapper.xml
    # 加载全局的配置文件
    configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: oracle
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# Shiro
shiro:
  user:
    # 登录地址
    loginUrl: /login
    # 权限认证失败地址
    unauthorizedUrl: /unauth
    # 首页地址
    indexUrl: /index
    # 验证码开关
    captchaEnabled: true
    # 验证码类型 math 数组计算 char 字符
    captchaType: math
  cookie:
    # 设置Cookie的域名 默认空，即当前访问的域名
    domain:
    # 设置cookie的有效访问路径
    path: /
    # 设置HttpOnly属性
    httpOnly: true
    # 设置Cookie的过期时间，天为单位
    maxAge: 60
  session:
    # Session超时时间（默认30分钟）
    expireTime: 60
    # 同步session到数据库的周期（默认1分钟）
    dbSyncPeriod: 1
    # 相隔多久检查一次session的有效性，默认就是10分钟
    validationInterval: 10
    # 同一个用户最大会话数，比如2的意思是同一个账号允许最多同时两个人登录（默认-1不限制）
    maxSession: 1
    # 踢出之前登录的/之后登录的用户，默认踢出之前登录的用户
    kickoutAfter: false

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*,/tms/mobilePrivacyPolicy/*,/activiti/model/\d+/save,/basic/contractTemplate/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*,/*

redis:
  # Redis服务器地址
  host: 127.0.0.1
  # Redis服务器连接端口
  port: 6379
  # Redis服务器连接密码（默认为空）
  password: 123456
  # 连接超时时间（毫秒）
  timeout: 10000
  pool:
    # 连接池最大连接数（使用负值表示没有限制）
    max-active: 200
    # 连接池最大阻塞等待时间（使用负值表示没有限制）
    max-wait: -1
    # 连接池中的最大空闲连接
    max-idle: 8
    # 连接池中的最小空闲连接
    min-idle: 0

#activiti数据库类型
activitidataType: oracle

#支付宝支付
alipay:
  gateway: https://openapi.alipay.com/gateway.do
  app_id: 2019111569213258
  p_id: 2088131316308554
  private_key: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCRyiKMBAXCLFYBOos8iEoFRUenuaZpJPENnVexfaKWbCnMNzF5LwL3rhQ5rp7l6NHmreKH3dU+iShOfVFVUH6PrdDu9dh2uSJizTj7Eb1U0o7nbeT0s0CvBXLPb8tGU3CKqc9USRYMXUzhdr+TVwOxkQ+LSI8j1pDVnvHVD8iaiWSoOAkXZEhmZrx1CcznoOSH9gyIo5kb+hZ0ezV+xvxh4WGOZNIvW0JM6KCwGeNvRV/HwlOitYDPgylVZMNhH+6LovUijfJH/hiwcoIiNv4iWJKvAFzj603alngcuxjSnP0VBGg2XkAW8NsllkzlRKHrBjJWOryCWa1UwV35YNbjAgMBAAECggEALGCYJYCv9Awk3djhOzrNNU+CYUa8pfLNwXQslmWE0T75diUy1Q1/XL9LfsdtrNQy2IIDm5Hl8FQ1xbaVxF9tNvUUzThefL0b/3EysBIVTLWPNFkaQXYy8VO6P/RqruyFLGBGMQTSRk88MMFBzWrGQA3E6oPatnEAw1XPjShxysAkCvGItohhzCN/rwpP4ZhL4hmTCg+75ml62md+yG+vpiaMDz8kXKfTvobTYs3hQ8txmNzc1Muw/s28EHM7xrYb0yiiyzhH48JGW4fYeh8nJ6nxgYyBK2CcFReYpYTg6MgeChfL6v4AWCaLXbRVSVqq7p/l89e279chU/mr3gfCsQKBgQDHwiCTYAtBTsjLNxPP9FUYF3Pyo6O+qf7t55AQNwA6KwMUqKtgbAAI+ZM9qUh7ixS8iubalSWWenGnTZvcW0vI3hKq7MGpSjG4uLaswWXEGen+fibvEQtyLHof7brqjSZZwQVv4z9u8OhkxYB8x6EYktEgMJvJF0/2I5sWAWPr2wKBgQC61iIW+KpEduix+401vILLI2dA2LVl+TutjOAlRLdYb7BrstPE8oozC7g5v55eg8tYC/9K/OYJ19ZZzCW0rkJ53ruTRMkgel0MrEmb/Zy8v9lLKpLxRVUUQ2+41pOVsdKWNdqgqsTXqmduRlIqVXY1KHwN2fARv1+uCnjCUdjzmQKBgCP8Jp4l6SEkritdiqYln8atQufs7qyENzZoRp2FKvdPlEh+chD2o7L6Mroi6rnGvj+QzjGGOLU47Yx0AKXblHVk+/vqlqCGixp5u/63ADwpZuu/kgfzDBrPRFjIHKnwL35vtSkIyP6VS2gL5C+WTsD8gcvIteet2Wu/0kpOic+NAoGARLm9wmtn3lVLFSUt9UVJa0WzlnPgjgkWvakjTjIvqxUydPtL0JdS5KH7+yoULDoYk2OeXcwXJinBzDg+6m5rP38BypMz1IGONrotPXcVr4x2dqLLeHqDaDfCIeC0Ww0VJHHc4NrQG3CwKS0uzimvmLAyqbmwCCc4H8H0V65ZuokCgYBQzKNeGMI1SZe1L0Z1yoMpoTWHvnID1bEQW4sbQXlDz5Gq4N1eHyI0rBBDMfAN7Ec9PbU47jw1YY3cXXm0G31MR6x69skGl5qLCEWoUaSW3P3YsmxKuP/t3uOhEUjiD1qxmZClSU52tCb96ugJpw/PHMMXSj9ccI8q1dC/42mB1Q==
  public_key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkcoijAQFwixWATqLPIhKBUVHp7mmaSTxDZ1XsX2ilmwpzDcxeS8C964UOa6e5ejR5q3ih93VPokoTn1RVVB+j63Q7vXYdrkiYs04+xG9VNKO523k9LNArwVyz2/LRlNwiqnPVEkWDF1M4Xa/k1cDsZEPi0iPI9aQ1Z7x1Q/ImolkqDgJF2RIZma8dQnM56Dkh/YMiKOZG/oWdHs1fsb8YeFhjmTSL1tCTOigsBnjb0Vfx8JTorWAz4MpVWTDYR/ui6L1Io3yR/4YsHKCIjb+IliSrwBc4+tN2pZ4HLsY0pz9FQRoNl5AFvDbJZZM5USh6wYyVjq8glmtVMFd+WDW4wIDAQAB
  alipay_public_key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhPzYYYXCDkvsLBGtS1LloqIhkOtjoFh/do8WmM9pOSMoyyv+HMicK34EH0bea3BUIk1AhNUNXJEIplbN0FKCdMS4LOptkvA4mMzDSrMX3+CVr2bPiBH/UcCUGPUcUhYSJ8FW3jyZZLnDQ4koIQ3yYjDD/eCKAoDojtZKqbWXEKwBs/YRhjg0HG9bQHONhK5v4rn38OZG0mpbBOs4cypNFCqytO+0srhFQOuKtPtyf3DgX9XuQeWaRY9BkhIKEzv8nauhotu2Asuq+1SqG/tgAOzvXlK6RKQ82oQN5sPiIpFf4Xjtk5Qvqxe6unHq+vheVN98ZVyGvVu453Li85V87wIDAQAB
  charset: utf-8
  sign_type: RSA2
  format: json
  notify_url: http://www.ntchst.com:8980/tms-admin/alipay/notify

#微信支付
wxpay:
  appkey: wx8ca1e121669603b8
  appsecrect: 351d9a47daa8cec30d79c042eea1b045
  mchid: 1559499691
  key: 9923cMywl9923cMywl9923cMywl9923c
  notify_url: http://www.ntchst.com:8980/tms-admin/wxpay/notify

#中交线路
openApi:
  user: 9a6d669f-378e-4da8-bcbe-e004d20f0934
  pwd: 4lp7g2P4V7m78a925261Fid5tJycMh
  srt: f5f862eb-1963-4fc9-9f55-14f969556b8d
  cid: 616e995b-0bda-4a77-9ff3-4bab210415f9

#wkhtmltopdf在系统中的路径
wkhtmltopdf:
  basePath: D:\Program Files\wkhtmltopdf\bin\wkhtmltopdf.exe

#农行银企平台
abcPay:
  sendIp: 127.0.0.1
  sendPort: 15999
  corpNo: ****************
  opNo: "0004"

#短信
messageCode:
  account: N6554520
  password: 8hqHU7vPYj0356
  # 短信发送的URL 请登录zz.253.com 获取完整的URL接口信息
  smsSingleRequestServerUrl: http://smssh1.253.com/msg/send/json
  # 其中“【】”中括号为运营商签名符号
  sign: 【亿鼎物流】

#帆软发布端口
fineHttpHead: "https://tms.qixin56.com"
fineReportPort: ""

#U-Push
U-Push:
  #货主
  appkey-Android-consignor: 5dc38eab4ca3578de20007ef
  appMasterSecret-Android-consignor: mnav7yq0mylyk5u9p0cp5ede1tskvjtz
  appkey-iOS-consignor: 5dc38e71570df3c1ce00015b
  appMasterSecret-iOS-consignor: s7x8ffskioxbbyy8a2psvcg9ic7ojfvd
  #承运商
  appkey-Android-carrier: 5dc38f130cafb275b6000541
  appMasterSecret-Android-carrier: tufipeozhay9hf6sviufhjphzmnwkrf9
  appkey-iOS-carrier: 5dc38ee04ca357c4000001bf
  appMasterSecret-iOS-carrier: hjghjepwz4chb0tc2zybqwa7n5uvff4d
  #tms移动端
  appkey-Android-tms: 5ec7687d167eddd0ce000225
  appMasterSecret-Android-tms: cx1ssuxak6pfgdvilikisztcydlw0xfv

#货源推送范围设置 1推送给所有承运商用户，2推送给对应关注线路的用户
source_of_goods_range: 1

# 发货单保险
insurance:
  company: 中国平安财产保险股份有限公司
  code: 11878003901148112693

#UIBOOT请求路径
uiboot:
  car: http://127.0.0.1:11111/vehicle/task
  driver: http://127.0.0.1:11111/staff/task

danger_file_ext: html,htm,php,jsp,jspx,asp,php,php5,php4,php3,php2,phtml,pht,aspx
  pwd
#阿里云AppCode
aliyun:
  appcode: e0df9dee85864bd8941dc9cc53fbf2ff

#图片前缀
picPre: https://tms.qixin56.com

#G7配置
g7:
  #正式
  apiUrl: openapi.huoyunren.com
  map:
    "JH":
      accessId: h6uzdq
      secretKey: gV6VlsUcDr9ayNZZfXoV4vORs28VDBQT
    "MY":
      accessId: zfv3p5
      secretKey: IanrqoqMW9ngui1Rpq9arPcblZljV4kJ
    "DW":
      accessID: pl7bhc
      secretKey: xekh92botn591ixy49ahzgbyf0pjgi7y
    "DH":
      accessID: xzud3f
      secretKey: vtf21iqprpttbjgew5sbnf6flyy4nqle

#测试
#  apiUrl: demo.dsp.chinawayltd.com/altair/rest
#  map:
#    "JH":
#      accessId: 7rhyyi
#      secretKey: BpsgQnwgSqzI7Ahw14wb1PRm2QjKRBUA
#    "MY":
#      accessId: bldhwd
#      secretKey: JgTyfmXFSOYaReJ51k6uDHU9UxXSxGZY

location:
  user: 6e62e246-ac8b-486a-a9c0-615c179419de
  pwd: 2XLH208B75N03HC5m0571PJ242p8c0
  srt: 1e058570-5ddb-44c0-94df-7b4bbb57b2f8
  cid: 7d1d861a-2585-4653-9c3f-ad6cd7ba900a

nfp:
  jh:
    baseUrl: "http://localhost"
    authorization: "bmZwOiQlVFUqNzQx"
    tenantId: "761819"
    username: "admin"
    password: "21232f297a57a5a743894a0e4a801fc3"
  my:
    baseUrl: "http://localhost"
    authorization: "bmZwOiQlVFUqNzQx"
    tenantId: "041435"
    username: "admin"
    password: "21232f297a57a5a743894a0e4a801fc3"

#微信开发相关配置
wechat:
  #是否线上环境
  online: false
  #公众号
  public:
    appid: wxcde279709688ebb7
    secret: 89a33848d998345da7841573f2f15f8e
    templateId: LklOHUFZiMi-_VBfz4M2ljz8KgHdRGqQaca58XujBSk
  #小程序
  applet:
    appid: wx79d24665756bde69
    secret: ee9c7ba9dd1e2e5e67c7389633911eac
    templateId: wR-p2k0cMLLs5bxT5QehoKaq-paeg9HuMLXhheHuTqw

ymm:
  clientId: QtRrJhEHpLpNxqdBxBLE
  clientSecret: 03e812c34a514ab6b66dde2cb7aa3f45HX1yGhTu
  encryptSecret: Am27sflr6B5rMdru0TwFwQ==

wecom: #企业微信
  corpid: wwbaa3fdf071b5e87e
  sp_secret: pfgsQDrqPflbJxnBX8fAqyKBFVCELXIb8qog1NAhUak #审批secret
  hr_secret: eRfC3AdFOK5-m9tgiMePDd0ZOjGMdVMVOr5M5H8WKhU #通讯录secret
  msgSender: #琦欣小秘自建应用
    agentid: 1000029
    secret: 10xqM3LczPRT5jzaJUE_lGP9NVDAPiFDWl2q_ZfsyOs
    test: true

use_redis_session: true

#小黑卡相关配置
tuqiang:
  appKey: 8FB345B8693CCD008FE25E600DA01BD9
  appSecret: fff0fa28bb424a60b419652e4d3fde9b
  userId: MYWL
  userPwd: 1234567aA!
  fenceRadius: 1000.0 #围栏半径(200～999900；单位：米)
  redisHost: ${redis.host}
  redisPort: ${redis.port}

cargo:
  JH:
    baseUrl: "http://localhost"
    authorization: "c2FiZXI6c2FiZXJfc2VjcmV0"
    tenantId: "469243"
    username: "admin"
    password: "21232f297a57a5a743894a0e4a801fc3"
    signKey: WO810K9VsPRMf016A464mv93

psbc:
  merchId: "320002307149"
  sendUrl: "https://tms.qixin56.com/custagent/customer/msg"

kimi:
  baseUrl: https://api.moonshot.cn/v1
  apiKey: sk-cTc1E69SVxYjNADOGLP9DeCCJBmvwx0uPO5csbS2sZvsgyqe
kimi-jsjsd: #江山结算单
  baseUrl: https://api.moonshot.cn/v1
  apiKey: sk-cTc1E69SVxYjNADOGLP9DeCCJBmvwx0uPO5csbS2sZvsgyqe

#RedisSubConfig的开关
redisSub:
  enable: true