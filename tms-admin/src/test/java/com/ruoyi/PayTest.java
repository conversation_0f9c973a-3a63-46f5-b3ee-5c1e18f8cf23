package com.ruoyi;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ruoyi.g7.constant.ContentType;
import com.ruoyi.g7.constant.G7Constants;
import com.ruoyi.g7.constant.HttpHeader;
import com.ruoyi.g7.constant.HttpSchema;
import com.ruoyi.g7.dao.PaymentDao;
import com.ruoyi.g7.domain.R;
import com.ruoyi.g7.domain.RData;
import com.ruoyi.g7.enums.Method;
import com.ruoyi.g7.util.G7Client;
import com.ruoyi.g7.util.MessageDigestUtil;
import com.ruoyi.g7.util.Request;
import com.ruoyi.g7.util.Response;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;

import java.security.KeyPair;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest
@Rollback(false)
public class PayTest {

    @Autowired
    PaymentDao paymentDao;

    @Test
    public void testStart() {
        Map<String, Object> map = new HashMap<>();
        //
        //pay("f87aec9f32aa4b41b9991283bc44a81c", "123456", null);
        // R(code=0, msg=succ, sub_code=800001, sub_msg=；系统查询不到本运单[f87aec9f32aa4b41b9991283bc44a81c]（运单未推送成功）, data=null)
        // R(code=0, msg=succ, sub_code=900010, sub_msg=动态密码验证失败!, data=null)

    }

    @Test
    public void testRsa() {
        KeyPair pair = SecureUtil.generateKeyPair("RSA");
        RSA rsa = new RSA();
        rsa.setPrivateKey(pair.getPrivate());
        rsa.setPublicKey(pair.getPublic());
        byte[] encrypt = rsa.encrypt("我是一段测试aaaa".getBytes(CharsetUtil.CHARSET_UTF_8), KeyType.PublicKey);
        System.out.println(Base64.encode(encrypt));
    }

    private void pay(String encryptData, String payTypes) {


        String path = "/v2/isnb/router/ntocc-waybill/iwaybill_pay/pay_encrypt_ex";
        Map<String,String> dataMap = new HashMap<>();
        dataMap.put("encryptData",encryptData);

        String requestBody = JSON.toJSONString(dataMap);
        String baseUrl = "openapi.huoyunren.com";

        R<List<RData>> retData = new R<>();
        retData.setCode(500);

        //调用远程接口
        try {
            String retStr = postJson(path, requestBody, baseUrl, "h6uzdq", "gV6VlsUcDr9ayNZZfXoV4vORs28VDBQT");
            retData = JSONObject.parseObject(retStr,new TypeReference<R<List<RData>>>(){});
        }catch (Exception e){
            e.printStackTrace();
            retData.setMsg(e.getMessage());
        }
    }

    public String postJson(String path,String body,String baseUrl,String accessId,String secretKey) throws Exception {
        Map<String, String> headers = new HashMap<String, String>();

        headers.put(HttpHeader.HTTP_HEADER_CONTENT_MD5, MessageDigestUtil.base64AndMD5(body));
        //（POST/PUT请求必选）请求Body内容格式
        headers.put(HttpHeader.HTTP_HEADER_CONTENT_TYPE, ContentType.CONTENT_TYPE_JSON);
        headers.put(HttpHeader.HTTP_HEADER_G7_TIMESTAMP, "" + System.currentTimeMillis());
        Request request = new Request(Method.POST_JSON, HttpSchema.HTTP + baseUrl, path, accessId, secretKey, G7Constants.DEFAULT_TIMEOUT);
        request.setHeaders(headers);
        request.setJsonStrBody(body);

        //调用服务端
        Response response = G7Client.execute(request);
        return response.getBody();
    }
}
