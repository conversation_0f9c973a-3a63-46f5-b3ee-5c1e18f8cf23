package com.ruoyi;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.framework.web.domain.server.Sys;
import com.ruoyi.tms.domain.finance.PayDetail;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class MyTest {

    @Test
    public void testRemove() {
        /*List<PayDetail> payDetailList = new ArrayList<>();
        PayDetail payDetail = new PayDetail();
        payDetail.setTransFeeCount(new BigDecimal(2));
        payDetailList.add(payDetail);

        PayDetail payDetail2 = new PayDetail();
        payDetail2.setTransFeeCount(new BigDecimal(4));
        payDetailList.add(payDetail2);

        PayDetail payDetail3 = new PayDetail();
        payDetail3.setTransFeeCount(new BigDecimal(0));
        payDetailList.add(payDetail3);


        PayDetail payDetail4 = new PayDetail();
        //payDetail.setTransFeeCount(new BigDecimal(0));
        payDetailList.add(payDetail4);

        System.out.println("111111111111="+payDetailList.size());
        payDetailList.removeIf(x -> x.getTransFeeCount() == null
                || x.getTransFeeCount().compareTo(BigDecimal.ZERO) == 0);

        System.out.println("222222222222="+payDetailList.size());*/

        /*Date date = DateUtil.parse("2022-06-01 00:00:00");
        System.out.println(DateUtil.offsetDay(date,1));*/

        List<String> list = new ArrayList<>();
        list.add("111");list.add(null);list.add("222");
        list = list.stream().filter(item->item !=null).collect(Collectors.toList());
        list.forEach(item->{
            if(null == item){
                //list.remove(item);
                System.out.println("item is null");
            }else{
                System.out.println(item);
            }
        });
    }
}
