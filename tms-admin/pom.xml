<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>tms</artifactId>
        <groupId>com.ntchst</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>war</packaging>
    <artifactId>tms-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>

        <!-- SpringBoot集成thymeleaf模板 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!-- spring-boot-devtools
         注释原因: https://blog.csdn.net/qq_32957067/article/details/105708140
         -->
        <!--<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> &lt;!&ndash; 表示依赖不会传递 &ndash;&gt;
        </dependency>-->

        <!-- swagger2-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>

        <!--防止进入swagger页面报类型转换错误，排除2.9.2中的引用，手动增加1.5.21版本-->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.5.21</version>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.5.21</version>
        </dependency>

        <!-- swagger2-UI-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.ntchst</groupId>
            <artifactId>tms-framework</artifactId>
        </dependency>

        <!-- 定时任务-->
        <dependency>
            <groupId>com.ntchst</groupId>
            <artifactId>tms-quartz</artifactId>
        </dependency>

        <!-- 代码生成-->
        <dependency>
            <groupId>com.ntchst</groupId>
            <artifactId>tms-generator</artifactId>
        </dependency>

        <!--hutool-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>



        <!-- 工作流-->
        <dependency>
            <groupId>com.ntchst</groupId>
            <artifactId>tms-activiti</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>

        <dependency>
            <groupId>openapi-sdk</groupId>
            <artifactId>openapi-sdk</artifactId>
            <version>1.0-SNAPSHORT</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/openapi-sdk-6.0.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>2.4</version>
            <classifier>jdk15</classifier>
        </dependency>


    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.4</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${artifactId}</warName>
                    <webResources>
                        <resource>
                            <directory>lib</directory>
                            <targetPath>WEB-INF/lib/</targetPath>
                            <includes>
                                <include>**/*.jar</include>
                            </includes>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>

            <!-- screws 生成数据库文档  -->
<!--            <plugin>-->
<!--                <groupId>cn.smallbun.screw</groupId>-->
<!--                <artifactId>screw-maven-plugin</artifactId>-->
<!--                <version>1.0.5</version>-->
<!--                <dependencies>-->
<!--                    &lt;!&ndash; 数据库连接 &ndash;&gt;-->
<!--                    <dependency>-->
<!--                        <groupId>com.alibaba</groupId>-->
<!--                        <artifactId>druid-spring-boot-starter</artifactId>-->
<!--                        <version>${druid.version}</version>-->
<!--                    </dependency>-->
<!--                    <dependency>-->
<!--                        <groupId>com.oracle.database.jdbc</groupId>-->
<!--                        <artifactId>ojdbc8</artifactId>-->
<!--                        <version>12.2.0.1</version>-->
<!--                    </dependency>-->
<!--                </dependencies>-->
<!--                <configuration>-->
<!--                    &lt;!&ndash; 数据库相关配置 &ndash;&gt;-->
<!--                    <driverClassName>oracle.jdbc.driver.OracleDriver</driverClassName>-->
<!--                    <jdbcUrl>*****************************************</jdbcUrl>-->
<!--                    <username>tmsuser</username>-->
<!--                    <password>tms</password>-->
<!--                    &lt;!&ndash; screw 配置 &ndash;&gt;-->
<!--                    <fileType>HTML</fileType>-->
<!--                    <title>tms数据库文档</title> &lt;!&ndash;标题&ndash;&gt;-->
<!--                    <fileName>tms数据库文档24-02-23</fileName> &lt;!&ndash;文档名称 为空时:将采用[数据库名称-描述-版本号]作为文档名称&ndash;&gt;-->
<!--                    <description>tms数据库文档</description> &lt;!&ndash;描述&ndash;&gt;-->
<!--                    <version>${project.version}</version> &lt;!&ndash;版本&ndash;&gt;-->
<!--                    <openOutputDir>false</openOutputDir> &lt;!&ndash;打开文件输出目录&ndash;&gt;-->
<!--                    <produceType>freemarker</produceType> &lt;!&ndash;生成模板&ndash;&gt;-->
<!--                </configuration>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <phase>compile</phase>-->
<!--                        <goals>-->
<!--                            <goal>run</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->

        </plugins>
        <finalName>${artifactId}</finalName>
    </build>

</project>