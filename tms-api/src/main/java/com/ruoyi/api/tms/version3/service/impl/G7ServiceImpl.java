package com.ruoyi.api.tms.version3.service.impl;

import com.ruoyi.api.tms.version1.domain.dto.ApiCarrBankListDTO;
import com.ruoyi.api.tms.version1.domain.vo.ApiCarrBankListVO;
import com.ruoyi.api.tms.version1.domain.vo.ApiMiniProgramInformation;
import com.ruoyi.api.tms.version1.mapper.ApiCarrBankMapper;
import com.ruoyi.api.tms.version1.mapper.ApiMiniProgramMapper;
import com.ruoyi.api.tms.version3.service.IG7Service;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.constant.ApiResult;
import com.ruoyi.g7.config.G7Account;
import com.ruoyi.g7.config.G7ConfigX;
import com.ruoyi.g7.dao.PayeeDao;
import com.ruoyi.g7.dao.TeamDao;
import com.ruoyi.g7.dao.TokenDao;
import com.ruoyi.g7.domain.R;
import com.ruoyi.g7.domain.Team;
import com.ruoyi.tms.domain.Payee;
import com.ruoyi.tms.domain.g7.PayeeX;
import com.ruoyi.tms.mapper.g7.G7PayeeMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;

import static com.ruoyi.tms.constant.G7Constant.CORPS;

@Service
public class G7ServiceImpl implements IG7Service {

    @Resource
    private G7PayeeMapper g7PayeeMapper;
    @Resource
    private ApiMiniProgramMapper apiMiniProgramMapper;
    @Resource
    private TeamDao teamDao; // 车队长
    @Resource
    private PayeeDao payeeDao; // 收款人
    @Resource
    private TokenDao tokenDao;
    @Resource
    private G7ConfigX g7ConfigX;
    @Resource
    private ApiCarrBankMapper apiCarrBankMapper;

    private static Map<String, Field> payeeXFieldCache = new HashMap<>();

    static {
        Field[] fields = PayeeX.class.getDeclaredFields();
        for (int i = 0; i < CORPS.length; i++) {
            for (int j = 0; j < fields.length; j++) {
                if (fields[j].getName().equalsIgnoreCase(CORPS[i])) {
                    payeeXFieldCache.put(CORPS[i], fields[j]);
                    fields[j].setAccessible(true);
                    break;
                }
            }
        }
    }

    @Transactional
    @Override
    public ApiResult<Boolean> walletCheck(Long userId) {
        ApiMiniProgramInformation information = apiMiniProgramMapper.selectInformationOfMine(userId.toString());
        if (information == null) {
            return ApiResult.fail("未找到您的相关信息");
        }
        // 判断是否开通钱包前提条件：明确哪个公司主体，明确姓名、身份证、[银行卡]
        PayeeX payeeX = g7PayeeMapper.getPayeeXByIdCard(information.getLegalCard());
        String corp = null;
        if (payeeX != null) {
            List<String> corps = findPayeeG7Corps(payeeX);
            if (corps.size() > 0) {
                corp = corps.get(0);
                G7Account g7Account = g7ConfigX.getG7ConfigMap().get(corp);
                final R<List<Map<String, Object>>> listR = payeeDao.fuzzyQuery(information.getUserName(), g7Account.getAccessId(), g7Account.getSecretKey());
                if (!listR.isSuccess()) {
                    throw new RuntimeException(listR.errMsg());
                }
                for (int i = 0; i < listR.getData().size(); i++) {
                    Map<String, Object> g7Payee = listR.getData().get(i);
                    if (g7Payee.get("payeeCard").equals(information.getLegalCard())) {
                        return ApiResult.success(g7Payee.get("openWallet"));
                    }
                }
            }
        }
        if (payeeX == null || corp == null) {
            // 拿当前收款人的信息创建车队长数据
            corp = "MY"; // 默认铭源
            G7Account g7Account = g7ConfigX.getG7ConfigMap().get(corp);

            ApiCarrBankListDTO carrBankListDTO = new ApiCarrBankListDTO();
            carrBankListDTO.setUserId(userId.toString());
            final List<ApiCarrBankListVO> bankList = apiCarrBankMapper.selectCarrBankList(carrBankListDTO);
            boolean find = false;
            Team team = new Team();
            // 如果银行卡有多个，找到当前人名、身份证一致的银行卡 TODO
            for (int i = 0; i < bankList.size(); i++) {
                if (information.getLegalCard().equalsIgnoreCase(bankList.get(i).getIdcard())) {
                    team.setBankName(bankList.get(i).getBankName());
                    team.setBankCardNumber(bankList.get(i).getBankCard());
                    team.setPayeeMobile(bankList.get(i).getPhone());
                    team.setPayeeCard(bankList.get(i).getIdcard().toUpperCase());
                    team.setPayeeName(bankList.get(i).getBankAccount());
                    find = true;
                    break;
                }
            }
            if (!find) {
                return ApiResult.fail("请添一张本人姓名的银行卡");
            }
            final R<Boolean> booleanR = teamDao.addTeam(team, g7Account.getAccessId(), g7Account.getSecretKey());
            if (!booleanR.isSuccess()) {
                throw new RuntimeException(booleanR.errMsg());
            }
            // 收款人创建成功后，使用该银行卡判断是否已开通钱包
            final R<PayeeDao.G7BankCard> oneR = payeeDao.findOne(team.getBankCardNumber(), g7Account.getAccessId(), g7Account.getSecretKey());
            if (!oneR.isSuccess()) {
                throw new RuntimeException(oneR.errMsg());
            }
            return ApiResult.success(oneR.getData().getOpenWallet());
        }
        return ApiResult.fail("未知错误");
    }

    @Override
    public ApiResult getToken(Long userId) {
        ApiMiniProgramInformation information = apiMiniProgramMapper.selectInformationOfMine(userId.toString());
        if (information == null) {
            return ApiResult.fail("未找到您的相关信息");
        }
        //System.out.println(information);//(userName=***, groupName=***, carrierName=***1234, carrType=2, carrierName1=**, legalCard=320623**, avatar=null, phoneNumber=158**, vehicleCount=0, driverCount=0, contractCount=0, bankCount=1, carriertype=1)
        PayeeX payeeX = g7PayeeMapper.getPayeeXByIdCard(information.getLegalCard());
        /*List<String> corps = findPayeeG7Corps(payeeX);
        if (payeeX == null || corps.size() == 0) {
            String corp = "MY";
            corps.add(corp);
            G7Account g7Account = g7ConfigX.getG7ConfigMap().get("MY");
            this.addTeam(userId.toString(), information.getLegalCard(), g7Account);
        }
        G7Account g7Account = g7ConfigX.getG7ConfigMap().get(corps.get(0));
        final R<String> tokenR = tokenDao.getToken(information.getUserName(), information.getPhoneNumber(), information.getLegalCard(), 1, g7Account.getAccessId(), g7Account.getSecretKey());
        if (!tokenR.isSuccess()) {
            throw new RuntimeException(tokenR.errMsg());
        }*/
        G7Account g7Account = g7ConfigX.getG7ConfigMap().get("JH");
        //final R<String> tokenR = tokenDao.getToken("赵羽桐", "***********", "341223198910112580", 1, g7Account.getAccessId(), g7Account.getSecretKey());
        final R<String> tokenR = tokenDao.getToken("张佳佳", "***********", "341204198204251613", 1, g7Account.getAccessId(), g7Account.getSecretKey());
        if (!tokenR.isSuccess()) {
            throw new BusinessException(tokenR.errMsg());
        }
        return ApiResult.success((Object) tokenR.getData());
    }

    private List<String> findPayeeG7Corps(PayeeX payeeX) {
        List<String> corps = new ArrayList<>();
        if (payeeX != null) {
            try {
                for (int i = 0; i < CORPS.length; i++) {
                    if (Objects.equals(payeeXFieldCache.get(CORPS[i]).get(payeeX), 1)) {// payeeX.getJh() == 1
                        corps.add(CORPS[i]);
                        break;
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return corps;
    }

    private void addTeam(String userId, String idcard, G7Account g7Account) {
        ApiCarrBankListDTO carrBankListDTO = new ApiCarrBankListDTO();
        carrBankListDTO.setUserId(userId);
        final List<ApiCarrBankListVO> bankList = apiCarrBankMapper.selectCarrBankList(carrBankListDTO);
        boolean find = false;
        Team team = new Team();
        // 如果银行卡有多个，找到当前人名、身份证一致的银行卡
        for (int i = 0; i < bankList.size(); i++) {
            if (idcard.equalsIgnoreCase(bankList.get(i).getIdcard())) {
                team.setBankName(bankList.get(i).getBankName());
                team.setBankCardNumber(bankList.get(i).getBankCard());
                team.setPayeeMobile(bankList.get(i).getPhone());
                team.setPayeeCard(bankList.get(i).getIdcard().toUpperCase());
                team.setPayeeName(bankList.get(i).getBankAccount());
                find = true;
                break;
            }
        }
        if (!find) {
            throw new RuntimeException("请添一张本人姓名的银行卡");
        }
        final R<Boolean> booleanR = teamDao.addTeam(team, g7Account.getAccessId(), g7Account.getSecretKey());
        if (!booleanR.isSuccess()) {
            throw new RuntimeException(booleanR.errMsg());
        }
    }
}
