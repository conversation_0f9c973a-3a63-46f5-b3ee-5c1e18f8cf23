package com.ruoyi.g7.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ruoyi.g7.domain.G7Payee;
import com.ruoyi.g7.domain.R;
import com.ruoyi.g7.domain.RData;
import com.ruoyi.g7.util.ConfigUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class PayeeDao extends BaseDao{
    @Resource
    private ConfigUtil configUtil;

    /**
     * 批量导入收款人数据
     */
    public R<List<RData>> importPayee(List<G7Payee> payeeList, String accessId, String secretKey){
        String path = "/v2/isnb/router/ntocc-capacity/common/batch_import_payee";
        String requestBody = JSON.toJSONString(payeeList);
        //调用远程接口
        try {
            String retStr = postJson(path, requestBody, configUtil.getBaseUrl(), accessId, secretKey);
            return JSONObject.parseObject(retStr, new TypeReference<R<List<RData>>>() {
            });
        } catch (Exception e) {
            log.error("", e);
            throw new RuntimeException(e);
        }

    }

    /**
     * 收款人列表查询 https://www.showdoc.com.cn/ntocc/****************
     * @param bank_card_number	否	string	银行卡号（全模糊匹配）
     * @param payee_name	否	string	收款人姓名（全模糊匹配）
     * @param current	否	integer	当前页号
     * @param size	否	integer	每页显示条数，默认 20
     * @param ascs	否	string	排序 ASC 数组
     * @param descs	否	string	排序 DESC 数组
     * @param accessId
     * @param secretKey
     * @return
     */
    public R<Map<String, Object>> listPayee(String bank_card_number, String payee_name, int current, int size, String ascs, String descs, String accessId, String secretKey) {
        String path = "/v2/isnb/router/ntocc-capacity/ipayee/list";

        String baseUrl = configUtil.getBaseUrl();

        Map<String, Object> param = new HashMap<>();
        param.put("bank_card_number", bank_card_number);
        param.put("payee_name", payee_name);
        param.put("current", current);
        param.put("size", size);
        param.put("ascs", ascs);
        param.put("descs", descs);
        String requestBody = JSON.toJSONString(param);
        R<Map<String, Object>> retData = new R<>();
        retData.setCode(500);

        //调用远程接口
        try {
            String retStr = postJson(path, requestBody, baseUrl, accessId, secretKey);
            retData = JSONObject.parseObject(retStr, new TypeReference<R<Map<String, Object>>>() {
            });
        } catch (Exception e) {
            log.error("", e);
            retData.setMsg(e.getMessage());
        }

        return retData;
    }

    /**
     * 收款人删除
     *
     * @param bank_card_number 银行卡
     * @param accessId
     * @param secretKey
     * @return
     */
    public R delete(String bank_card_number, String accessId, String secretKey) {
        String path = "/v2/isnb/router/ntocc-capacity/ipayee/delete";
        String baseUrl = configUtil.getBaseUrl();

        Map<String, Object> param = new HashMap<>();
        param.put("bank_card_number", bank_card_number);
        String requestBody = JSON.toJSONString(param);
        R<Map<String, Object>> retData = new R<>();
        retData.setCode(500);

        //调用远程接口
        try {
            String retStr = postJson(path, requestBody, baseUrl, accessId, secretKey);
            retData = JSONObject.parseObject(retStr, new TypeReference<R>() {
            });
        } catch (Exception e) {
            log.error("", e);
            retData.setMsg(e.getMessage());
        }

        return retData;
    }

    /**
     * 收款人精确查询 https://www.showdoc.com.cn/ntocc/****************
     *
     * @param bankCardNumber
     * @param accessId
     * @param secretKey
     * @return
     */
    public R<G7BankCard> findOne(String bankCardNumber, String accessId, String secretKey) {
        String path = "/v2/isnb/router/ntocc-capacity/ipayee/find_one";

        String baseUrl = configUtil.getBaseUrl();

        Map<String, Object> param = new HashMap<>();
        param.put("bank_card_number", bankCardNumber);

        String requestBody = JSON.toJSONString(param);
        R<G7BankCard> retData = new R<>();
        retData.setCode(500);

        //调用远程接口
        try {
            String retStr = postJson(path, requestBody, baseUrl, accessId, secretKey);
            // {"code":0,"data":{"gmtModified":*************,"isCollector":0,"collectorStatus":0,"bankCardNumber":"6230520420002536272","payeeCard":"320624196809218019","orgcode":"201XW32LQZ","collectorBank":0,"bankName":"中国农业银行","gmtCreate":*************,"openWallet":false,"payeeName":"郭立新","orgroot":"201XW3","payeeMobile":"***********"},"msg":"succ","req_id":"6d41664b3ab70dfe50522b9978316ba4","sub_code":0,"sub_msg":"调用成功"}
            retData = JSONObject.parseObject(retStr, new TypeReference<R<G7BankCard>>() {
            });
        } catch (Exception e) {
            log.error("", e);
            retData.setMsg(e.getMessage());
        }

        return retData;
    }

    @Data
    public static class G7BankCard {
        private Long gmtCreate;//	long	创建时间(时间戳13位)
        private Long gmtModified;//	long	更新时间(时间戳13位)
        private String orgroot;//	string	顶级机构
        private String orgcode;//	string	子级机构
        private String bankName;//	string	开户银行
        private String bankCardNumber;//	string	银行卡号
        private String payeeName;//	string	收款人姓名
        private String payeeMobile;//	string	收款人电话（银行预留手机号）
        private String payeeCard;//	string	收款人身份证号
        private Integer collectorStatus;//	Integer	是否启动车队长收款：0:不启用，1:启用
        private Integer collectorBank;//	Integer	是否车队长收款卡，即使用该卡辅助开通的银行钱包。 0：不是，1：是
        private Integer isCollector;//	Integer	是否车队长 0：不是，1：是
        private Boolean openWallet;//	Boolean	是否开通钱包 false：未开通，true：已开通 。注意：是否开通钱包是根据收款人身份证号判断
    }

    /**
     * 批量按收款人精确查询（最大支持 20 张卡号）
     *
     * @param bankCardNumbers
     * @param accessId
     * @param secretKey
     * @return
     */
    public List<G7BankCard> bulkFind(List<String> bankCardNumbers, String accessId, String secretKey) {
        String path = "/v2/isnb/router/ntocc-capacity/ipayee/bulk_find";
        Map<String, Object> param = new HashMap<>();
        param.put("bank_card_numbers", String.join(",", bankCardNumbers));
        String requestBody = JSON.toJSONString(param);
        try {
            String str = postJson(path, requestBody, configUtil.getBaseUrl(), accessId, secretKey);
            R<List<G7BankCard>> result = JSONObject.parseObject(str, new TypeReference<R<List<G7BankCard>>>() {});
            if (result.isSuccess()) {
                return result.getData();
            } else {
                throw new RuntimeException(result.getMsg());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 收款人模糊查询 https://www.showdoc.com.cn/ntocc/****************
     *
     * @param keyword 关键字（银行卡号或者收款人姓名全模糊匹配）
     * @param accessId
     * @param secretKey
     * @return
     */
    public R<List<Map<String, Object>>> fuzzyQuery(String keyword, String accessId, String secretKey) {
        String path = "/v2/isnb/router/ntocc-capacity/ipayee/fuzzy_query";

        Map<String, Object> param = new HashMap<>();
        param.put("keyword", keyword);
        String requestBody = JSON.toJSONString(param);
        try {
            String retStr = postJson(path, requestBody, configUtil.getBaseUrl(), accessId, secretKey);
            // {"code":0,"data":[
            //      {"gmtModified":*************,"isCollector":0,"collectorStatus":0,"bankCardNumber":"6230520420002536272","payeeCard":"320624196809218019","orgcode":"201XW32LQZ","collectorBank":0,"bankName":"中国农业银行","gmtCreate":*************,"openWallet":false,"payeeName":"郭立新","orgroot":"201XW3","payeeMobile":"***********"}
            // ],"msg":"succ","req_id":"0b20e114b65d2f2a3f05f947fa3c6f54","sub_code":0,"sub_msg":"调用成功"}
            // {"code":0,"data":[
            //      {"gmtModified":*************,"isCollector":0,"collectorStatus":0,"bankCardNumber":"6213363169915273871","payeeCard":"342225198502262858","orgcode":"201XW32LQZ","collectorBank":0,"bankName":"中国农业银行","gmtCreate":*************,"openWallet":true,"payeeName":"丁伟","orgroot":"201XW3","payeeMobile":"***********"},
            //      {"gmtModified":*************,"isCollector":0,"collectorStatus":0,"bankCardNumber":"6228480401934596511","payeeCard":"320321197111270279","orgcode":"201XW32LQZ","collectorBank":0,"bankName":"中国农业银行","gmtCreate":*************,"openWallet":false,"payeeName":"丁伟","orgroot":"201XW3","payeeMobile":"***********"},
            //      {"gmtModified":*************,"isCollector":0,"collectorStatus":0,"bankCardNumber":"6228480400647532516","payeeCard":"342201197708122855","orgcode":"201XW32LQZ","collectorBank":0,"bankName":"中国农业银行","gmtCreate":*************,"openWallet":false,"payeeName":"丁伟","orgroot":"201XW3","payeeMobile":"***********"}
            //  ],"msg":"succ","req_id":"217aa49ea32387ee8f375024eb79d28f","sub_code":0,"sub_msg":"调用成功"}
            return JSONObject.parseObject(retStr, new TypeReference<R<List<Map<String, Object>>>>() {
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
