package com.ruoyi.tms.service.finance.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.domain.SysDictData;
import com.ruoyi.system.domain.SysUploadFile;
import com.ruoyi.system.mapper.SysUploadFileMapper;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.tms.constant.AdjustCheckStatusEnum;
import com.ruoyi.tms.constant.AdjustRecordEnum;
import com.ruoyi.tms.constant.BillingMethod;
import com.ruoyi.tms.constant.InvoiceStatusEnum;
import com.ruoyi.tms.constant.finance.*;
import com.ruoyi.tms.constant.trace.DriverCollectionStatusEnum;
import com.ruoyi.tms.domain.carrier.Entrust;
import com.ruoyi.tms.domain.carrier.EntrustLot;
import com.ruoyi.tms.domain.client.CustContractpc;
import com.ruoyi.tms.domain.finance.*;
import com.ruoyi.tms.domain.invoice.InvPackGoods;
import com.ruoyi.tms.domain.invoice.Invoice;
import com.ruoyi.tms.domain.invoice.MultipleShippingAddress;
import com.ruoyi.tms.domain.trace.Allocation;
import com.ruoyi.tms.domain.trace.EntPackGoods;
import com.ruoyi.tms.mapper.carrier.EntrustLotMapper;
import com.ruoyi.tms.mapper.carrier.EntrustMapper;
import com.ruoyi.tms.mapper.client.ClientMapper;
import com.ruoyi.tms.mapper.finance.*;
import com.ruoyi.tms.mapper.invoice.InvPackGoodsMapper;
import com.ruoyi.tms.mapper.invoice.InvoiceMapper;
import com.ruoyi.tms.mapper.invoice.MultipleShippingAddressMapper;
import com.ruoyi.tms.mapper.segment.SegmentMapper;
import com.ruoyi.tms.mapper.trace.AllocationMapper;
import com.ruoyi.tms.mapper.trace.EntrustCostMapper;
import com.ruoyi.tms.mapper.trace.EntrustWorkMapper;
import com.ruoyi.tms.mapper.trace.TraceMapper;
import com.ruoyi.tms.service.finance.IPayDetailService;
import com.ruoyi.tms.service.finance.IReceiveDetailService;
import com.ruoyi.tms.service.invoice.IInvoiceService;
import com.ruoyi.tms.service.trace.IEntPackGoodsService;
import com.ruoyi.tms.service.trace.ITraceService;
import com.ruoyi.tms.service.wecom.IWecomService;
import com.ruoyi.tms.vo.client.ClientPopupVO;
import com.ruoyi.tms.vo.finance.*;
import com.ruoyi.tms.vo.finance.receive.ReceiveAmountCountVO;
import com.ruoyi.tms.vo.finance.receive.ReceiveDetailAdjustVO;
import com.ruoyi.tms.vo.finance.receive.ReceiveExportVO;
import com.ruoyi.tms.vo.finance.receive.ReceiveVO;
import com.ruoyi.tms.vo.main.YslrwcChatsDataVO;
import com.ruoyi.tms.vo.main.ZkxxChatsDataVO;
import com.ruoyi.tms.vo.part.PartReceiveApplicationVO;
import com.ruoyi.tms.vo.trace.AllocationVO;
import com.ruoyi.tms.vo.trace.EntrustDto;
import com.ruoyi.util.ShiroUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.ui.ModelMap;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 应收明细 服务层实现
 *
 * <AUTHOR>
 * @date 2019-09-20
 */
@Service
public class ReceiveDetailServiceImpl implements IReceiveDetailService {

    private static String SCRID = "ReceiveDetail";
    @Autowired
    private ReceiveDetailMapper receiveDetailMapper;

    @Autowired
    private ReceCheckSheetMapper receCheckSheetMapper;

    @Autowired
    private InvoiceMapper invoiceMapper;

    @Autowired
    private ReceRecordMapper receRecordMapper;
    @Autowired
    private ISysDictDataService iSysDictDataService;
    @Autowired
    private AccountConsumeMapper accountConsumeMapper;
    @Autowired
    private AccountMapper accountMapper;
    @Autowired
    private EntrustMapper entrustMapper;
    @Autowired
    @Lazy
    private ITraceService traceService;
    @Autowired
    private IEntPackGoodsService entPackGoodsService;
    @Autowired
    private EntrustLotMapper entrustLotMapper;
    @Autowired
    private AllocationMapper allocationMapper;
    @Autowired
    private SegmentMapper segmentMapper;
    @Autowired
    private OtherFeeMapper otherFeeMapper;
    @Autowired
    private TraceMapper traceMapper;
    @Autowired
    private ShiroUtils shiroUtils;
    @Autowired
    private CloseAccountMapper closeAccountMapper;
    @Autowired
    private ClientMapper clientMapper;
    @Autowired
    private PayDetailMapper payDetailMapper;
    @Autowired
    @Lazy
    private IPayDetailService payDetailService;
    @Autowired
    private ReceiveDetailAdjustHistoryMapper receiveDetailAdjustHistoryMapper;
    @Autowired
    private ReceSheetRecordMapper receSheetRecordMapper;
    @Autowired
    private ReceBillingMapper receBillingMapper;
    @Resource
    private ISysConfigService sysConfigService;
    @Autowired
    private EntrustWorkMapper entrustWorkMapper;
    @Autowired
    private InvPackGoodsMapper invPackGoodsMapper;
    @Resource
    private IWecomService wecomService;
    @Resource
    private DriverCollectionMapper driverCollectionMapper;
    @Autowired
    @Lazy
    private IInvoiceService invoiceService;
    @Autowired
    private MultipleShippingAddressMapper multipleShippingAddressMapper;
    @Autowired
    private SysUploadFileMapper sysUploadFileMapper;
    @Autowired
    private EntrustCostMapper entrustCostMapper;

    private static final Logger log = LoggerFactory.getLogger(ReceiveDetailServiceImpl.class);
    private static ReentrantLock lock = new ReentrantLock();


    /**
     * 应收明细列表
     *
     * @param receiveVO 应收明细对象
     * @return List<receiveVO>
     */
    @Override
    @DataScope(deptAlias = "t4.SALES_DEPT"/*, userAlias = "t.PSNDOC"*/)
    public List<ReceiveVO> selectReceiveList(ReceiveVO receiveVO) {
        if (receiveVO.getReceiveVbillstatus() != null && receiveVO.getReceiveVbillstatus() == 0) {
            receiveVO.getParams().put("receiveVbillstatus", ReceiveDetailStatusEnum.NEW.getValue() + "");
        } else if (receiveVO.getReceiveVbillstatus() != null && receiveVO.getReceiveVbillstatus() == 1) {
            receiveVO.getParams().put("receiveVbillstatus", ReceiveDetailStatusEnum.AFFIRM.getValue() + "");
        } else {
            receiveVO.getParams().put("receiveVbillstatus", ReceiveDetailStatusEnum.NEW.getValue() + "," + ReceiveDetailStatusEnum.AFFIRM.getValue());
        }

        if (StringUtils.isNotEmpty(receiveVO.getVbillno()) && receiveVO.getVbillno().contains("；")) {
            receiveVO.setVbillno(receiveVO.getVbillno().replace("；", ";"));
        }

        List<ReceiveVO> list = receiveDetailMapper.selectReceiveVOList(receiveVO);

        for (ReceiveVO vo : list) {
            //回单
            List<SysUploadFile> sysUploadFiles = entrustWorkMapper.selectReceiptPicByInvoiceId(vo.getInvoiceId());
            vo.setReceiptUploadFiles(sysUploadFiles);
            //货品
            InvPackGoods invPackGoods = new InvPackGoods();
            invPackGoods.setDelFlag(0);
            invPackGoods.setInvoiceId(vo.getInvoiceId());
            List<InvPackGoods> packGoodsList = invPackGoodsMapper.selectInvPackGoodsList(invPackGoods);
            String goodsName = packGoodsList.stream().map(InvPackGoods::getGoodsName).distinct().collect(Collectors.joining(","));
            vo.setGoodsName(goodsName);

            //获取多装多卸地址
            List<MultipleShippingAddress> multipleShippingAddressList = multipleShippingAddressMapper.selectListByInvoiceId(vo.getInvoiceId());
            vo.setMultipleShippingAddressList(multipleShippingAddressList);
        }

        return list;
    }

    @Override
    public List<ReceiveVO> selectReceiveListExport(ReceiveDetailVO receiveDetail) {
        /*if (receiveVO.getReceiveVbillstatus() != null && receiveVO.getReceiveVbillstatus() == 0) {
            receiveVO.getParams().put("receiveVbillstatus", ReceiveDetailStatusEnum.NEW.getValue() + "");
        } else if (receiveVO.getReceiveVbillstatus() != null && receiveVO.getReceiveVbillstatus() == 1) {
            receiveVO.getParams().put("receiveVbillstatus", ReceiveDetailStatusEnum.AFFIRM.getValue() + "");
        } else {
            receiveVO.getParams().put("receiveVbillstatus", ReceiveDetailStatusEnum.NEW.getValue() + "," + ReceiveDetailStatusEnum.AFFIRM.getValue());
        }*/
        List<ReceiveVO> receiveVOList = receiveDetailMapper.selectReceiveVOListExport(receiveDetail);
        return receiveVOList;
    }

    /**
     * 应收明细列表
     *
     * @param receiveVO 应收明细对象
     * @return List<receiveVO>
     */
    @Override
    public List<ReceiveVO> selectReceiveAdjustListForCheck(ReceiveVO receiveVO) {
        List<ReceiveVO> receiveVOList = receiveDetailMapper.selectReceiveAdjustListForCheck(receiveVO);
        if (receiveVO.getAdjustCheckStatus() == AdjustCheckStatusEnum.CHECK_ALLOW.getValue()) {
            for (ReceiveVO receive : receiveVOList) {
                //运费
                BigDecimal freightTotal = receive.getFreightTotal();
                BigDecimal freightTotalAfter = receive.getFreightTotalAfter();
                BigDecimal freightDiff = freightTotalAfter.subtract(freightTotal);
                receive.setFreightTotal(freightTotal.subtract(freightDiff));
                receive.setFreightTotalAfter(freightTotal);

                //在途
                BigDecimal onWayTotal = receive.getOnWayTotal();
                BigDecimal onWayTotalAfter = receive.getOnWayTotalAfter();
                BigDecimal onWayTotalDiff = onWayTotalAfter.subtract(onWayTotal);
                receive.setOnWayTotal(onWayTotal.subtract(onWayTotalDiff));
                receive.setOnWayTotalAfter(onWayTotal);

                //总金额
                BigDecimal transFeeCount = receive.getTransFeeCount();
                BigDecimal transFeeCountAfter = receive.getTransFeeCountAfter();
                BigDecimal transFeeCountDiff = transFeeCountAfter.subtract(transFeeCount);
                receive.setTransFeeCount(transFeeCount.subtract(transFeeCountDiff));
                receive.setTransFeeCountAfter(transFeeCount);
            }
        }
        return receiveVOList;
    }

    /**
     * 应收明细列表
     *
     * @param receiveVO 应收明细对象
     * @return List<receiveVO>
     */
    @Override
    @DataScope(deptAlias = "t.SALES_DEPT,cust.sales_dept", userAlias = "t.PSNDOC")
    public List<ReceiveVO> selectReceiveAdjustList(ReceiveVO receiveVO) {
        if (receiveVO.getReceiveVbillstatus() != null && receiveVO.getReceiveVbillstatus() == 0) {
            receiveVO.getParams().put("receiveVbillstatus", ReceiveDetailStatusEnum.NEW.getValue() + "");
        } else if (receiveVO.getReceiveVbillstatus() != null && receiveVO.getReceiveVbillstatus() == 1) {
            receiveVO.getParams().put("receiveVbillstatus", ReceiveDetailStatusEnum.AFFIRM.getValue() + "");
        }
        List<ReceiveVO> receiveVOList = receiveDetailMapper.selectReceiveVOAdjustList(receiveVO);
        return receiveVOList;
    }

    /**
     * 应收明细列表
     *
     * @param receiveVO 应收明细对象
     * @return List<receiveVO>
     */
    @Override
    @DataScope(deptAlias = "t.SALES_DEPT", userAlias = "t.PSNDOC")
    public List<ReceiveExportVO> selectReceiveAdjustExportList(ReceiveVO receiveVO) {
        if (receiveVO.getReceiveVbillstatus() != null && receiveVO.getReceiveVbillstatus() == 0) {
            receiveVO.getParams().put("receiveVbillstatus", ReceiveDetailStatusEnum.NEW.getValue() + "");
        } else if (receiveVO.getReceiveVbillstatus() != null && receiveVO.getReceiveVbillstatus() == 1) {
            receiveVO.getParams().put("receiveVbillstatus", ReceiveDetailStatusEnum.AFFIRM.getValue() + "");
        }
        List<ReceiveExportVO> receiveVOList = receiveDetailMapper.selectReceiveVOAdjustExportList(receiveVO);
        return receiveVOList;
    }

    /**
     * 应收明细列表
     *
     * @param receiveVO 应收明细对象
     * @return List<receiveVO>
     */
    @Override
    public List<ReceiveVO> selectReceiveListAdjust(ReceiveVO receiveVO) {
        List<ReceiveVO> receiveVOList = receiveDetailMapper.selectReceiveVOListAdjust(receiveVO);
        return receiveVOList;
    }

    /**
     * 新增应收明细
     *
     * @param receiveDetail 页面参数
     * @return
     */
    @Override
    public int addReceive(ReceiveDetailVO receiveDetail) {
        // 应收表主键
        receiveDetail.setReceiveDetailId(IdUtil.simpleUUID());
        // 应收单编号
        receiveDetail.setVbillno(createReceiveDetailVbillno(0));
        // 在途费用
        receiveDetail.setFreeType(FreeTypeEnum.PREPAID_OIL_CARD.getValue());
        receiveDetail.setRegScrId(SCRID);
        receiveDetail.setCorScrId(SCRID);
        // 新增应收单信息
        return receiveDetailMapper.insertReceive(receiveDetail);
    }

    /**
     * 根据Id查询应收明细
     *
     * @param receiveDetailId 主键
     * @return ReceiveDetailVO
     */
    @Override
    public ReceiveDetailVO selectReceiveById(String receiveDetailId) {
        ReceiveDetailVO receiveDetail = receiveDetailMapper.selectReceiveById(receiveDetailId);
        List<SysDictData> dictData = iSysDictDataService.selectDictDataByType("bala_corp");
        dictData.stream().filter(dictDatum -> dictDatum.getDictValue().equals(receiveDetail.getBalaCorp()))
                .forEach(dictDatum -> receiveDetail.setBalaCorpName(dictDatum.getDictLabel()));
        return receiveDetail;
    }

    /**
     * 根据List<Id>查询应收记录
     *
     * @param list
     * @return
     */
    @Override
    public List<ReceiveDetailVO> selectReceiveByIds(List<String> list) {
        return receiveDetailMapper.selectReceiveByIds(list);
    }

    /**
     * 根据发货单id查询
     *
     * @param invoiceId
     * @return
     */
    @Override
    public List<ReceiveDetailVO> selectReceiveByInvoiceId(String invoiceId) {
        return receiveDetailMapper.selectReceiveByInvoiceId(invoiceId);
    }

    /**
     * 修改应收明细
     *
     * @param receiveDetail 应收明细对象
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public AjaxResult editReceive(ReceiveDetailVO receiveDetail) {
        /*
         * 更新客户发货单号  1、更新发货单 2、更新发货单下所有应收
         */
        Invoice invoice = new Invoice();
        invoice.setInvoiceId(receiveDetail.getInvoiceId());
        invoice.setCustOrderno(receiveDetail.getCustOrderno());
        invoice.setCorScrId(SCRID);
        invoiceMapper.updateInvoice(invoice);

        //更新所有应收的客户订单号
        ReceiveDetailVO receiveDetailVO = new ReceiveDetailVO();
        receiveDetailVO.setCorScrId(SCRID);
        receiveDetailVO.setCustOrderno(receiveDetail.getCustOrderno());
        receiveDetailVO.setInvoiceId(receiveDetail.getInvoiceId());
        receiveDetailMapper.updateReceiveDetailByInvoiceId(receiveDetailVO);

        //更新应收
        receiveDetail.setCorScrId(SCRID);
        int row = receiveDetailMapper.editReceiveByNewStatus(receiveDetail);
        if (row == 0) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("状态发生改变，请刷新页面");
        }
        return AjaxResult.success();
    }

    /**
     * 生成对账单的方法
     *
     * @param receCheckSheet   应收对账实体类
     * @param receiveDetailIds 应收明细Id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int addReceCheck(ReceCheckSheet receCheckSheet, String receiveDetailIds) {
        //判断当前月份是否关账
        String year = receCheckSheet.getYear().toString();
        String month = +receCheckSheet.getMonth() < 10 ? "0" + receCheckSheet.getMonth().toString() : receCheckSheet.getMonth().toString();
        /*Integer cnt = closeAccountMapper.selectCloseAccountByYearMonth(year+month);
        if(cnt != null && cnt > 0){
            throw new BusinessException("该月份已关账，无法生成对账单");
        }*/
        String isFleetData = "0";
        // 生成对账单之前 校验数据库应收明细状态
        List<String> list = Arrays.stream(receiveDetailIds.split(",")).collect(Collectors.toList());
        List<ReceiveDetailVO> receiveDetailList = receiveDetailMapper.selectReceiveByIds(list);
        receiveDetailList = receiveDetailList.stream().filter(x -> x.getIsCollect() == 0).collect(Collectors.toList());

        list = receiveDetailList.stream().map(ReceiveDetailVO::getReceiveDetailId).collect(Collectors.toList());

        if (list.size() == 0) {
            throw new BusinessException("暂无有效的数据，无法生成对账单。");
        }

//        List<String> invoiceIdList = receiveDetailList.stream()
//                .map(ReceiveDetailVO::getInvoiceId).distinct().collect(Collectors.toList());

        //发货单集合
        String[] invoiceIds = receiveDetailList.stream()
                .map(ReceiveDetailVO::getInvoiceId).distinct().toArray(String[]::new);

        //运费类型的数量
        long countFreight = receiveDetailList.stream().filter(x -> "0".equals(x.getFreeType())).count();
        //审核校验
        String settlementCheckBool = sysConfigService.selectConfigByKey("settlement_check");

        if ("1".equals(settlementCheckBool) && countFreight > 0) {
            List<Invoice> invoices = invoiceMapper.selectInvoiceByIds(invoiceIds);

            List<String> errInvoiceNo = new ArrayList<>();
            for (Invoice invoice : invoices) {
                ClientPopupVO clientPopupVO = clientMapper.selectClientById(invoice.getCustomerId());

                if (0 == invoice.getSettlementCheck() && clientPopupVO.getSettlementCheck() == 1) {
                    errInvoiceNo.add(invoice.getVbillno());
                }
            }
            if (errInvoiceNo.size() > 0) {
                String errNo = String.join("，", errInvoiceNo);
                throw new BusinessException("存在【结算管理】审核未通过的发货单，无法生成对账单。</br>" +
                        "发货单号【" + errNo + "】请联系结算部门进行审核。");
            }
        }

        for (ReceiveDetailVO receiveDetail : receiveDetailList) {
            // 若应收明细存在不为已确认状态的情况直接返回0 表示本次修改失败
            if (receiveDetail.getVbillstatus() != ReceiveDetailStatusEnum.AFFIRM.getValue()) {
                throw new BusinessException("数据被更改，请刷新列表后重试！");
            }
            // 若应收存在单笔申请的情况，则无法创建
            if (receiveDetail.getIsApply() != null && receiveDetail.getIsApply() == 1) {
                throw new BusinessException("存在单笔申请的应收，请刷新列表后重试！");
            }
            isFleetData = receiveDetail.getIsFleetData();
        }
        //是否是车队数据
        receCheckSheet.setIsFleetData(isFleetData);

        // 新增一条应收对账
        String seq = StrUtil.fillBefore(receCheckSheetMapper.getSeq() + "", '0', 4);
        receCheckSheet.setVbillno("YSDZ" + DateUtil.format(new Date(), "yyyyMMdd") + seq);
        receCheckSheet.setRegScrId(SCRID);
        receCheckSheet.setCorScrId(SCRID);
        receCheckSheet.setSalesDept(receiveDetailList.get(0).getSalesDept());
        // 对账名称 ：生成规则 客户简称 + yyyyMM + (页面输入的特殊标记)。如果页面没有输入则不加
        String ym = receCheckSheet.getYear() + "" + StrUtil.fillBefore(receCheckSheet.getMonth() + "", '0', 2);
        String receCheckSheetNameOld = receCheckSheet.getReceCheckSheetName();
        String receCheckSheetNameNew = receCheckSheet.getCustName() + "-" + ym;
        int count = receCheckSheetMapper.getSheetNameCount(receCheckSheetNameNew) + 1;
        receCheckSheetNameNew = receCheckSheetNameNew + "-" + count;
        if (StringUtils.isNotEmpty(receCheckSheetNameOld)) {
            receCheckSheetNameNew = receCheckSheetNameNew + "(" + receCheckSheetNameOld + ")";
        }
        receCheckSheet.setReceCheckSheetName(receCheckSheetNameNew);

        receCheckSheet.setApplicationAmount(BigDecimal.ZERO);
        receCheckSheet.setApplicationStatus(0);

        //四舍五入总金额，未收金额 保留两位小数
        receCheckSheet.setTotalAmount(receCheckSheet.getTotalAmount().setScale(2, RoundingMode.HALF_UP));
        receCheckSheet.setUngotAmount(receCheckSheet.getUngotAmount().setScale(2, RoundingMode.HALF_UP));
        int rows = receiveDetailMapper.insertReceCheck(receCheckSheet);

        // 插入中间表
        editReceiveDetailStatus(receCheckSheet, list);
        // 修改应收单据状态为 已对账
        ReceiveDetailVO receiveDetail = new ReceiveDetailVO();
        receiveDetail.setCorUserId(shiroUtils.getUserId().toString());
        receiveDetail.setCorScrId(SCRID);
        receiveDetail.setVbillstatus(ReceiveDetailStatusEnum.RECONCILED.getValue());
        receiveDetail.setCorDate(new Date());
        int result = receiveDetailMapper.updateReceiveDetailStatusByList(receiveDetail, list, ReceiveDetailStatusEnum.AFFIRM.getValue());
        if (result != list.size()) {
            //TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new BusinessException("应收明细状态以改变，请刷新后重试！");
        }


        //修改发货单状态
        for (String id : invoiceIds) {
            List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveByInvoiceId(id);

            //已对账的数量
            long reconciledCount = receiveDetailVOS.stream()
                    .filter(x -> ReceiveDetailStatusEnum.RECONCILED.getValue() == x.getVbillstatus()).count();

            Invoice invoice = new Invoice();
            invoice.setInvoiceId(id);
            if (reconciledCount == receiveDetailVOS.size()) {
                //已全部加入对账包
                invoice.setIsAddReceCheck(2);
                invoiceMapper.updateInvoice(invoice);
            } else if (reconciledCount < receiveDetailVOS.size() && reconciledCount >0) {
                //部分加入对账包
                invoice.setIsAddReceCheck(1);
                invoiceMapper.updateInvoice(invoice);
            }else{
                invoice.setIsAddReceCheck(0);
                invoiceMapper.updateInvoice(invoice);
            }
        }

        return rows;
    }

    /**
     * 加入应收对账单
     *
     * @param receCheckSheet 应收对账实体类
     * @param receiveDetails 应收明细
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int saveChecking(ReceCheckSheet receCheckSheet, List<ReceiveDetailVO> receiveDetails, String receiveDetailIds) throws Exception {
        List<String> list = receiveDetails.stream().map(ReceiveDetailVO::getReceiveDetailId)
                .collect(Collectors.toList());
        //lock.lock(); lock锁注释
        //查询应收记录
        List<ReceiveDetailVO> receiveDetailList = receiveDetailMapper.selectReceiveByIds(list);
        for (ReceiveDetailVO receiveDetail : receiveDetailList) {
            //应收单据状态
            int vbillstatus = receiveDetail.getVbillstatus();
            // 若应收明细存在不为已确认状态的情况直接修改失败
            if (vbillstatus != ReceiveDetailStatusEnum.AFFIRM.getValue()) {
                throw new Exception("加入对账单的应收单据只能为已确认状态");
            }
//            if (receiveDetail.getIsApply() == 1) {
//                throw new Exception("存在单笔申请的应收，无法加入！");
//            }
            //应收明细
            ReceiveDetailVO receiveDetailVO = new ReceiveDetailVO();
            receiveDetailVO.setCorUserId(shiroUtils.getUserId().toString());
            receiveDetailVO.setCorDate(new Date());
            receiveDetailVO.setCorScrId("ReceiveDetail");
            //应收新已确认状态修改为->已对账
            if (vbillstatus == ReceiveDetailStatusEnum.AFFIRM.getValue()) {
                receiveDetailVO.setReceiveDetailId(receiveDetail.getReceiveDetailId());
                receiveDetailVO.setVbillstatus(ReceiveDetailStatusEnum.RECONCILED.getValue());
                int rows2 = receiveDetailMapper.updateReceiveDetailStatusByReceiveDetailId(receiveDetailVO);
                if (rows2 == 0) {
                    throw new Exception("加入对账单失败，修改应收单据状态失败");
                }
            }
            //应收已核销状态修改为->部分核销 暂不修改状态
//            if(vbillstatus == ReceiveDetailStatusEnum.HAS_BEEN_WRITTEN_OFF.getValue()){
//                receiveDetailVO.setReceiveDetailId(receiveDetail.getReceiveDetailId());
//                receiveDetailVO.setVbillstatus(ReceiveDetailStatusEnum.PARTIAL_WRITE_OFF.getValue());
//                int rows3 = receiveDetailMapper.updateReceiveDetailStatusByReceiveDetailId(receiveDetailVO);
//                if(rows3 == 0){
//                    throw new Exception("加入对账单失败，修改应收单据状态失败");
//                }
//            }
        }
        receCheckSheet.setCorDate(new Date());
        receCheckSheet.setCorUserId(String.valueOf(shiroUtils.getUserId()));
        receCheckSheet.setCorScrId("ReceiveDetail");
        // 更改应收对账单
        int rows1 = receCheckSheetMapper.updateCheckSheetAmount(receCheckSheet, Convert.toStrArray(receiveDetailIds));
        if (rows1 == 0) {
            throw new Exception("加入对账单失败，修改客户对账单失败");
        }
        // 插入中间表 receCheckSheetB
        editReceiveDetailStatus(receCheckSheet, list);



        //修改发货单状态
        List<String> invoiceIdList = receiveDetailList.stream()
                .map(ReceiveDetailVO::getInvoiceId).distinct().collect(Collectors.toList());

        for (String id : invoiceIdList) {
            List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveByInvoiceId(id);

            //已对账的数量
            long reconciledCount = receiveDetailVOS.stream()
                    .filter(x -> ReceiveDetailStatusEnum.RECONCILED.getValue() == x.getVbillstatus()).count();

            Invoice invoice = new Invoice();
            invoice.setInvoiceId(id);
            if (reconciledCount == receiveDetailVOS.size()) {
                //已全部加入对账包
                invoice.setIsAddReceCheck(2);
                invoiceMapper.updateInvoice(invoice);
            } else if (reconciledCount < receiveDetailVOS.size() && reconciledCount >0) {
                //部分加入对账包
                invoice.setIsAddReceCheck(1);
                invoiceMapper.updateInvoice(invoice);
            }else{
                invoice.setIsAddReceCheck(0);
                invoiceMapper.updateInvoice(invoice);
            }
        }

        return rows1;
    }

    /**
     * 将应收单修改为已对账
     *
     * @param list 应收明细Id集合
     * @return
     */
    @Override
    public void editReceiveDetailStatus(ReceCheckSheet receCheckSheet, List<String> list) {
        // 插入中间表
        for (String receiveDetailId : list) {
            ReceCheckSheetB receCheckSheetB = new ReceCheckSheetB();
            receCheckSheetB.setReceCheckSheetBId(IdUtil.simpleUUID());
            receCheckSheetB.setCorUserId(shiroUtils.getUserId().toString());
            receCheckSheetB.setReceCheckSheetId(receCheckSheet.getReceCheckSheetId());
            receCheckSheetB.setReceiveDetailId(receiveDetailId);
            receCheckSheetB.setDelFlag(0);
            receCheckSheetB.setCorDate(new Date());
            receCheckSheetB.setCorScrId(SCRID);
            receCheckSheetB.setRegScrId(SCRID);
            receCheckSheetB.setRegUserId(shiroUtils.getUserId().toString());
            receCheckSheetB.setRegDate(new Date());
            receiveDetailMapper.insertReceCheckSheet(receCheckSheetB);
        }
    }


    /**
     * 批量确认应收明细
     *
     * @param receiveDetailIds 应收明细ID 集合
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateReceiveDetailStatusByList(List<String> receiveDetailIds) {
        // 将应收明细状态改为已确认
        ReceiveDetailVO receiveDetail = new ReceiveDetailVO();
        receiveDetail.setVbillstatus(ReceiveDetailStatusEnum.AFFIRM.getValue());
        receiveDetail.setCorDate(new Date());
        receiveDetail.setCorUserId(shiroUtils.getUserId().toString());
        receiveDetail.setConfirmTime(new Date());
        receiveDetail.setConfirmUser(shiroUtils.getUserId().toString());

        List<ReceiveDetailVO> detailList = receiveDetailMapper.selectReceiveByIds(receiveDetailIds);
        for (ReceiveDetailVO detail : detailList) {
            receiveDetail.setReceiveDetailId(detail.getReceiveDetailId());
            receiveDetail.setUngotAmount(detail.getTransFeeCount());
            //修改应收明细(排他处理，只有新建的才能更新成已确认)
            receiveDetailMapper.updateReceiveByIdRX(receiveDetail, ReceiveDetailStatusEnum.NEW.getValue());

            //如果存在
            if ("1".equals(detail.getIsFleetData()) && "1".equals(detail.getIsFleetAssign())) {
                PayDetail payDetail = new PayDetail();
                payDetail.setPayDetailId(detail.getFleetPayDetailId());
                payDetail.setVbillstatus(PayDetailStatusEnum.AFFIRM.getValue());
                payDetail.setRegScrId(SCRID);
                payDetail.setCorScrId(SCRID);
                payDetailMapper.updatePayDetailById(payDetail);
            }

        }
        return 1;
    }

    /**
     * 批量确认应收明细
     *
     * @param receiveDetailIds 应收明细ID 集合
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateReceiveDetailStatusByListXXL(List<String> receiveDetailIds) {
        // 将应收明细状态改为已确认
        ReceiveDetailVO receiveDetail = new ReceiveDetailVO();
        receiveDetail.setVbillstatus(ReceiveDetailStatusEnum.AFFIRM.getValue());
        receiveDetail.setCorDate(new Date());
        receiveDetail.setCorScrId("XXL_confirm");
        receiveDetail.setConfirmTime(new Date());

        List<ReceiveDetailVO> detailList = receiveDetailMapper.selectReceiveByIds(receiveDetailIds);
        for (ReceiveDetailVO detail : detailList) {
            receiveDetail.setReceiveDetailId(detail.getReceiveDetailId());
            receiveDetail.setUngotAmount(detail.getTransFeeCount());
            //修改应收明细(排他处理，只有新建的才能更新成已确认)
            receiveDetailMapper.updateReceiveByIdRX(receiveDetail, ReceiveDetailStatusEnum.NEW.getValue());

            //如果存在
            if ("1".equals(detail.getIsFleetData()) && "1".equals(detail.getIsFleetAssign())) {
                PayDetail payDetail = new PayDetail();
                payDetail.setPayDetailId(detail.getFleetPayDetailId());
                payDetail.setVbillstatus(PayDetailStatusEnum.AFFIRM.getValue());
                payDetail.setRegScrId("xxl_confirm");
                payDetail.setCorScrId("xxl_confirm");
                payDetailMapper.updatePayDetailById(payDetail);
            }

        }
        return 1;
    }


    /**
     * 批量反确认应收明细
     *
     * @param receiveDetailList 应收明细list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult backConfirm(List<ReceiveDetailVO> receiveDetailList) {
        // 将应收明细状态改为 新建
        for (ReceiveDetailVO receiveDetail : receiveDetailList) {
            receiveDetail.setVbillstatus(ReceiveDetailStatusEnum.NEW.getValue());
            receiveDetail.setCorDate(new Date());
            receiveDetail.setCorUserId(shiroUtils.getUserId().toString());
            receiveDetail.setUngotAmount(BigDecimal.ZERO);
            int i = receiveDetailMapper.updateReceiveByIdRX(receiveDetail, ReceiveDetailStatusEnum.AFFIRM.getValue());
            if (i == 0) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                AjaxResult.error("更新失败，请刷新后重试！");
            }

            if ("1".equals(receiveDetail.getIsFleetData()) && "1".equals(receiveDetail.getIsFleetAssign())) {
                PayDetail payDetail = new PayDetail();
                payDetail.setPayDetailId(receiveDetail.getFleetPayDetailId());
                payDetail.setVbillstatus(PayDetailStatusEnum.NEW.getValue());
                payDetail.setRegScrId(SCRID);
                payDetail.setCorScrId(SCRID);
                payDetailMapper.updatePayDetailById(payDetail);
            }
        }

        return AjaxResult.success();
    }

    /**
     * 应收明细删除
     *
     * @param ids 应收明细Ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteReceiveDetailByIds(String ids) {
        String[] receiveDetailId = Convert.toStrArray(ids);

        //循环查询 如果待删除的应收 是车队数据并且为分配车队生成的  则需要删除对应的业务应付
        List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveByIds(Arrays.asList(receiveDetailId));

        for(ReceiveDetailVO receiveDetailVO : receiveDetailVOS){
            if(StringUtils.isNotBlank(receiveDetailVO.getEntrustCostId())){
                entrustCostMapper.deleteById(receiveDetailVO.getEntrustCostId());
            }
        }

        String fleetPayDetailIds = receiveDetailVOS.stream()
                .filter(x -> "1".equals(x.getIsFleetData()) && "1".equals(x.getIsFleetAssign()))
                .map(ReceiveDetailVO::getFleetPayDetailId)
                .collect(Collectors.joining(","));

        if (StringUtils.isNotEmpty(fleetPayDetailIds)) {
            payDetailService.deletePayDetailByIds(fleetPayDetailIds, null);
        }

        ReceiveDetailVO receiveDetail = new ReceiveDetailVO();
        receiveDetail.setDelFlag(1);
        receiveDetail.setDelDate(new Date());
        receiveDetail.setVbillstatus(ReceiveDetailStatusEnum.NEW.getValue());
        receiveDetail.setDelUserId(shiroUtils.getUserId().toString());
        //删除t_entrust_cost下对应的应收
        traceMapper.deleteEntrustCostByReceiveId(receiveDetailId);

        // 删除应收明细
        int i = receiveDetailMapper.deleteReceiveDetailByIds(receiveDetail, receiveDetailId);

        if (i != receiveDetailId.length) {
            throw new BusinessException("删除失败。");
        }

        for (ReceiveDetailVO receiveDetailVO : receiveDetailVOS) {
            invoiceService.synInvoiceCostAmount(receiveDetailVO.getInvoiceId(),"deleteReceiveDetailByIds");
        }

        return 1;
    }


    /**
     * 新增应收明细
     *
     * @param receiveDetail 应收明细信息
     * @return 结果
     */
    @Override
    public int insertReceiveDetail(ReceiveDetailVO receiveDetail) {
        return receiveDetailMapper.insertReceiveDetail(receiveDetail);
    }

    /**
     * 新增应收明细 判断是否超过五天
     *
     * @param receiveDetail 应收明细信息
     * @return 结果
     */
    @Override
    public int insertReceiveDetailAndAdjustRecord(ReceiveDetailVO receiveDetail) {
        return insertReceiveDetailAndAdjustRecordImpl(receiveDetail,true);
    }

    /**
     * 新增应收明细 判断是否超过五天
     *
     * @param receiveDetail 应收明细信息
     * @param needCheck     是否需要校验  true 需要  false 不需要
     * @return 结果
     */
    @Override
    public int insertReceiveDetailAndAdjustRecord(ReceiveDetailVO receiveDetail,boolean needCheck) {
        return insertReceiveDetailAndAdjustRecordImpl(receiveDetail,needCheck);
    }

    /**
     * 新增应收明细 判断是否超过五天
     *
     * @param receiveDetail 应收明细信息
     * @param needCheck     是否需要校验  true 需要  false 不需要
     * @return
     */
    private int insertReceiveDetailAndAdjustRecordImpl(ReceiveDetailVO receiveDetail,boolean needCheck) {
        //判断发货单是否超过五天
        Invoice invoice = invoiceMapper.selectInvoiceById(receiveDetail.getInvoiceId());

        if (needCheck) {
            //查询委托单
            List<Entrust> entrusts = entrustMapper.selectEntrustListByInvoiceId(invoice.getInvoiceId());
            if(entrusts != null && entrusts.size()>0) {
                Boolean ifAllReceipt = true;
                for (Entrust entrust : entrusts) {
                    if (entrust.getReceiptConfirmFlag() != 1 && !"1".equals(entrust.getIfReceipt())) {
                        ifAllReceipt = false;
                    }
                }
                if (ifAllReceipt) {
                    throw new BusinessException("单据已全部影像回单，无法新增应收");
                }
            }
        }

        if(DateUtils.checkDateDistinctMoreDays(invoice.getReqDeliDate())) {
            //设置应收单据为调整单
            receiveDetail.setIsAdjust(1);

            //保存T_ADJUST_RECORD
            String adjustRecordId = IdUtil.simpleUUID();
            //插入调整主表T_ADJUST_RECORD
            AdjustRecord adjustRecord = new AdjustRecord();
            adjustRecord.setAdjustRecordId(adjustRecordId);
            adjustRecord.setAdjustRecordType(AdjustRecordEnum.RECEIVE_DETAIL.getValue());
            adjustRecord.setAdjustCheckStatus(AdjustCheckStatusEnum.CHECK_ALLOW.getValue());
            adjustRecord.setMemo("超过五天未回单应收系统自动作为调整单");
            adjustRecord.setRegUserName(shiroUtils.getSysUser() == null ? null : shiroUtils.getSysUser().getUserName());
            adjustRecord.setDelFlag(0);
            adjustRecord.setIsCostAdjust("否");
            adjustRecord.setInvoiceId(invoice.getInvoiceId());
            adjustRecord.setInvoiceVbillno(invoice.getVbillno());
            payDetailMapper.insertAdjustRecord(adjustRecord);

            //保存T_RECEIVE_DETAIL_CHECK
            ReceiveDetailCheck receiveDetailCheck = new ReceiveDetailCheck();
            BeanUtils.copyBeanProp(receiveDetailCheck, receiveDetail);
            receiveDetailCheck.setReceiveDetailCheckId(receiveDetail.getReceiveDetailId());
            receiveDetailCheck.setAdjustRecordId(adjustRecordId);
            receiveDetailMapper.insertReceiveDetailCheck(receiveDetailCheck);

            ReceiveDetailAdjustHistory receiveDetailAdjustHistory = new ReceiveDetailAdjustHistory();
            receiveDetailAdjustHistory.setId(IdUtil.simpleUUID());
            receiveDetailAdjustHistory.setAdjustRecordId(adjustRecordId);
            receiveDetailAdjustHistory.setInvoiceId(invoice.getInvoiceId());
            receiveDetailAdjustHistory.setInvoiceNo(invoice.getVbillno());
            receiveDetailAdjustHistory.setNumBefore(invoice.getNumCount());
            receiveDetailAdjustHistory.setWeightBefore(invoice.getWeightCount());
            receiveDetailAdjustHistory.setVolumeBefore(invoice.getVolumeCount());
            receiveDetailAdjustHistory.setFreightBefore(BigDecimal.ZERO);
            receiveDetailAdjustHistory.setOnWayBefore(BigDecimal.ZERO);
            receiveDetailAdjustHistory.setNumAfter(invoice.getNumCount());
            receiveDetailAdjustHistory.setWeightAfter(invoice.getWeightCount());
            receiveDetailAdjustHistory.setVolumeAfter(invoice.getVolumeCount());
            receiveDetailAdjustHistory.setFreightAfter(BigDecimal.ZERO);
            receiveDetailAdjustHistory.setOnWayAfter(BigDecimal.ZERO);
            receiveDetailAdjustHistory.setRegScrId("receiveDetailAdjust");
            receiveDetailAdjustHistory.setCorScrId("receiveDetailAdjust");

            if("0".equals(receiveDetail.getFreeType())){
                //运费
                receiveDetailAdjustHistory.setFreightAfter(BigDecimal.ZERO);
            }else{
                //在途
                receiveDetailAdjustHistory.setOnWayAfter(BigDecimal.ZERO);
            }
            receiveDetailAdjustHistoryMapper.insertReceiveDetailAdjustRecord(receiveDetailAdjustHistory);
        }

        int i =  receiveDetailMapper.insertReceiveDetail(receiveDetail);

        //更新发货单信息
        invoiceService.synInvoiceCostAmount(receiveDetail.getInvoiceId(),receiveDetail.getRegScrId());

        return  i;
    }

    /**
     * 生成应收明细编号
     *
     * @param isFleet 添加的类型  0：业务下单   1：车队下单
     * @return
     */
    @Override
    public String createReceiveDetailVbillno(int isFleet) {
        String seq = StrUtil.fillBefore(receiveDetailMapper.getSeq() + "", '0', 4);
        return isFleet == 1
                ? "CD-YSMX" + DateUtil.format(new Date(), "yyyyMMdd") + seq
                : "YSMX" + DateUtil.format(new Date(), "yyyyMMdd") + seq;
    }

    /**
     * 以客户为单位展示应收明细列表
     *
     * @param receiveDetail 应收明细对象
     * @return 结果
     */
    @Override
    @DataScope(deptAlias = "cust.SALES_DEPT", userAlias = "receive.PSNDOC")
    public List<ReceiveDetailVO> selectReceiveListByCust(ReceiveDetailVO receiveDetail) {
        return receiveDetailMapper.selectReceiveListByCust(receiveDetail);
    }

    /**
     * 统计金额
     *
     * @param receiveDetail
     * @return
     */
    @Override
    @DataScope(deptAlias = "cust.sales_dept")
    public ReceiveDetailVO countReceiveListByCust(ReceiveDetailVO receiveDetail) {
        return receiveDetailMapper.countReceiveListByCust(receiveDetail);
    }

    /**
     * 调整金额合计
     *
     * @param receiveVO
     * @return
     */
    @Override
    @DataScope(deptAlias = "t.SALES_DEPT", userAlias = "t.PSNDOC")
    public ReceiveVO countReceiveAdjustListByCust(ReceiveVO receiveVO) {
        ReceiveVO receiveVOList = receiveDetailMapper.countReceiveAdjustListByCust(receiveVO);
        return receiveVOList;
    }

    /**
     * 生成调整单
     *
     * @param receiveDetail 应收明细对象
     * @return 结果
     */
    @Override
    @Transactional
    public int insertAdjust(ReceiveDetailVO receiveDetail) {
        ReceiveDetailVO receive = receiveDetailMapper.selectReceiveById(receiveDetail.getReceiveDetailId());
        //制作NR数据，一条为原始金额*-1，另一条为原始金额加调整金额
        //原始金额 为 应收金额加上调整单金额

        BigDecimal money = receive.getTransFeeCount();
        //调整单
        ReceiveDetailVO receiveDetailVO = new ReceiveDetailVO();
        receiveDetailVO.setAdjustReceiveDetailId(receiveDetail.getReceiveDetailId());
        List<ReceiveDetailVO> adjustList = receiveDetailMapper.selectReceiveListByStatus(receiveDetailVO, null);
        if (adjustList != null && adjustList.size() > 0) {
            BigDecimal transFeeCnt = adjustList.stream().map(ReceiveDetailVO::getTransFeeCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            money = money.add(transFeeCnt);
        }
        //调整单对应应收
        receive.setAdjustReceiveDetailId(receive.getReceiveDetailId());
        // 应收表主键
        receive.setReceiveDetailId(IdUtil.simpleUUID());
        // 应收明细对象为新建状态
        receive.setVbillstatus(ReceiveDetailStatusEnum.AFFIRM.getValue());
        // 调整费
        receive.setFreeType(FreeTypeEnum.IS_ADJUST.getValue());
        receive.setRegScrId(SCRID);
        // 是否为调整单
        receive.setIsAdjust(1);
        // 调整金额
        receive.setTransFeeCount(money.multiply(new BigDecimal(-1)));
        receive.setUngotAmount(money.multiply(new BigDecimal(-1)));
        receive.setGotAmount(null);
        receive.setMemo("");
        receive.setAdjustMemo(receiveDetail.getAdjustMemo());
        receive.setVbillno(createReceiveDetailVbillno(0));
        receive.setRegUserId(shiroUtils.getUserId().toString());
        receive.setRegDate(new Date());
        receive.setCorDate(new Date());
        receive.setCorUserId(shiroUtils.getUserId().toString());
        receive.setIsClose(1);

        //新增应收单信息
        receiveDetailMapper.insertReceive(receive);

        //红冲
        receive.setReceiveDetailId(IdUtil.simpleUUID());
        receive.setVbillno(createReceiveDetailVbillno(0));
        receive.setTransFeeCount(receiveDetail.getTransFeeCount());
        receive.setUngotAmount(receiveDetail.getTransFeeCount());
        // 新增应收单信息
        return receiveDetailMapper.insertReceive(receive);
    }

    /**
     * 保存收款记录
     *
     * @param receRecord 收款记录信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertReceRecord(ReceRecord receRecord) throws Exception {
        ReceiveDetailVO receiveDetail = receiveDetailMapper.selectReceiveById(receRecord.getReceiveDetailId());
        int flag = receiveDetail.getUngotAmount().compareTo(receRecord.getReceivableAmount());
        // 判断本次收款是否满足总金额
        if (flag == 0) {
            // 当已收金额满足总金额(视为已核销)
            receiveDetail.setVbillstatus(ReceiveDetailStatusEnum.HAS_BEEN_WRITTEN_OFF.getValue());

            //更新发货单 是否收款，收款金额
            Invoice invoice = new Invoice();
            invoice.setInvoiceId(receiveDetail.getInvoiceId());
            //1已收款
            invoice.setIfInsReceipt("1");
            invoice.setReceiptAmount(receiveDetail.getTransFeeCount());
            invoiceMapper.updateInvoice(invoice);
        } else if (flag > 0) {
            // 未满足总金额(视为部分核销)
            receiveDetail.setVbillstatus(ReceiveDetailStatusEnum.PARTIAL_WRITE_OFF.getValue());
        } else {
            //收款总金额大于应收总金额
            throw new Exception("收款总金额大于应收总金额");
        }
        // 修改该应收明细
        receiveDetail.setCorScrId("saveReceive");
        receiveDetail.setCorUserId(shiroUtils.getUserId().toString());
        int updateResult = receiveDetailMapper.updateReceive(receiveDetail, receRecord.getReceivableAmount());
        if (updateResult == 0) {
            throw new Exception("保存收款记录失败");
        }
        // 增加一条收款记录
        receRecord.setReceRecordId(IdUtil.simpleUUID());
        receRecord.setReceivableMan(shiroUtils.getSysUser().getUserName());

        //新增消费明细
        /*Account account = accountMapper.selectAccountById(receRecord.getInAccount());
        AccountConsume accountConsume = new AccountConsume();
        accountConsume.setAccountConsumeId(IdUtil.simpleUUID());
        //对应单据号
        accountConsume.setVbillno(receiveDetail.getVbillno());
        //账户id
        accountConsume.setAccountId(receRecord.getInAccount());
        //交易类型
        accountConsume.setConsumeType(AccountConsumeEnum.TRANSFER.getValue());
        //转出金额
        accountConsume.setPayMoney(new BigDecimal(0));
        //转入金额
        accountConsume.setIncomeMoney(receRecord.getReceivableAmount());
        //账户余额
        accountConsume.setAccountBala(NumberUtil.add(account.getAccountBala(), receRecord.getReceivableAmount()));
        //对应单据id
        accountConsume.setVbillId(receiveDetail.getReceiveDetailId());
        //交易时间
        accountConsume.setTransDt(new Date());
        //对应模块
        accountConsume.setBusinessType(AccountConsumeBusinessTypeEnum.RECE_DETAIL.getValue());
        //交易时间
        accountConsume.setTransDt(new Date());
        //画面id
        accountConsume.setRegScrId("receDetail");
        accountConsume.setCorScrId("receDetail");
        accountConsumeMapper.insertAccountConsume(accountConsume);

        //修改账户余额
        account.setAccountBala(accountConsume.getAccountBala());
        accountMapper.updateAccount(account);*/

        return receRecordMapper.insertReceRecord(receRecord);
    }

    /**
     * 校验当年当月是否已经存在对账单
     *
     * @param receCheckSheet 对账单对象
     * @return 0  不存在  1 存在
     */
    @Override
    public Integer checkReceiveCheckSheetByCustomerId(ReceCheckSheet receCheckSheet) {
        List<ReceCheckSheet> checkSheetList = receCheckSheetMapper.selectReceiveCheckSheetByCustomerId(receCheckSheet.getCustomerId());
        // 校验当年当月是否已经存在对账单
        for (ReceCheckSheet checkSheet : checkSheetList) {
            if (checkSheet.getYear().equals(receCheckSheet.getYear()) &&
                    checkSheet.getMonth().equals(receCheckSheet.getMonth())) {
                // 存在则返回 1
                return 1;
            }
        }
        return 0;
    }

    /**
     * 封装 复核页面需要的信息
     */
    @Override
    public void getReexamineInfo(ModelMap map, List<ReceiveDetailVO> receiveDetailList, List<CustContractpc> contractpcList) {

        contractpcList = contractpcList.stream()
                .filter((CustContractpc s) -> s.getBillingMethod() != null)
                .collect(Collectors.toList());
        // 封装计费方式名称
        List<Map<String, Object>> billingMethod = BillingMethod.getAllToMap();
        for (CustContractpc contractpc : contractpcList) {
            for (Map<String, Object> billing : billingMethod) {
                if (contractpc.getBillingMethod().equals(billing.get("value"))) {
                    contractpc.setBillingName(String.valueOf(billing.get("context")));
                }
            }
        }
        map.put("contractpcs", contractpcList);
        // 过滤相同的发货单号
        receiveDetailList = receiveDetailList.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ReceiveDetailVO::getInvoiceId))), ArrayList::new));

        // 复核页面对象 ReceiveReexamineVO
        List<ReceiveReexamineVO> reexamineList = new ArrayList<>();
        // 遍历应收List 用发货单ID 查询 每个发货单下的所有委托单
        EntPackGoods entPackGoods = new EntPackGoods();
        Allocation allocation = new Allocation();
        for (ReceiveDetailVO receiveDetail : receiveDetailList) {
            // 循环取发货单ID查询信息 封装到复核页面对象
            ReceiveReexamineVO receiveReexamineVO = new ReceiveReexamineVO();
            receiveReexamineVO.setInvoiceNo(receiveDetail.getInvoiceVbillno());
            List<EntrustDto> entrustList = new ArrayList<>();
            List<EntPackGoods> entPackGoodsList = new ArrayList<>();
            List<EntrustLot> entrustLotList = new ArrayList<>();
            // 发货单下的所有委托单
            List<Entrust> entrusts = entrustMapper.selectEntrustListByInvoiceId(receiveDetail.getInvoiceId());
            for (Entrust entrust : entrusts) {
                // 封装委托单信息
                entrustList.add(traceService.selectEntrustWorkById(entrust.getEntrustId()));
                entPackGoods.setEntrustId(entrust.getEntrustId());
                entPackGoods.setDelFlag(0);
                // 封装委托单货品信息
                entPackGoodsList.addAll(entPackGoodsService.selectEntPackGoodsList(entPackGoods));
                // 封装运单信息
                entrustLotList.add(entrustLotMapper.selectEntrustLotById(entrust.getLotId()));
            }
            // 成本分摊信息
            allocation.setInvoiceId(receiveDetail.getInvoiceId());
            List<AllocationVO> allocationList = new ArrayList<>(allocationMapper.selectList(allocation));
            // 获取发货单下的所有应收信息
            List<ReceiveDetailVO> receiveDetails = new ArrayList<>(receiveDetailMapper.selectReceiveByInvoiceId(receiveDetail.getInvoiceId()));

            receiveReexamineVO.setEntrustList(entrustList);
            receiveReexamineVO.setEntPackGoodsList(entPackGoodsList);
            receiveReexamineVO.setEntrustLotList(entrustLotList);
            receiveReexamineVO.setAllocationList(allocationList);
            receiveReexamineVO.setReceiveDetails(receiveDetails);
            // 将发货单下的成本分摊费用求和
            BigDecimal totalCostShare = allocationList.stream()
                    .map(AllocationVO::getCostShare)
                    // 使用reduce聚合函数,实现累加器
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            receiveReexamineVO.setTotalCostShare(totalCostShare);
            // 将发货单下的应收明细费用求和
            BigDecimal totalTransFeeCount = receiveDetails.stream()
                    .map(ReceiveDetailVO::getTransFeeCount)
                    // 使用reduce聚合函数,实现累加器
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            receiveReexamineVO.setTotalTransFeeCount(totalTransFeeCount);

            /*
             * 第三方费用
             */
            String invoiceId = receiveDetail.getInvoiceId();
            //根据发货单id查询所有第三方费用
            OtherFee otherFee = new OtherFee();
            otherFee.setLotId(invoiceId);
            otherFee.setDelFlag(0);
            List<OtherFee> otherFeeList = otherFeeMapper.selectOtherFeeList(otherFee);
            receiveReexamineVO.setOtherFeeList(otherFeeList);
            //计算第三方费用合计
            BigDecimal totalOtherFee = otherFeeList.stream().map(OtherFee::getFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            receiveReexamineVO.setTotalOtherFee(totalOtherFee);
            /*
             * 毛利
             */
            BigDecimal margin = totalTransFeeCount.subtract(totalCostShare);
            receiveReexamineVO.setMargin(NumberUtil.sub(margin, totalOtherFee));

            reexamineList.add(receiveReexamineVO);
        }
        map.put("reexamineList", reexamineList);

        // 所有应收明细费用求和
        BigDecimal totalTransFeeCount = reexamineList.stream()
                .map(ReceiveReexamineVO::getTotalTransFeeCount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        map.put("totalTransFeeCount", totalTransFeeCount);


    }

    @Override
    public List<ReceiveDetail> selectReceiveDetailListByInvoiceIds(String[] invoiceIds) {
        return receiveDetailMapper.selectReceiveDetailListByInvoiceIds(invoiceIds);
    }

    @Override
    @DataScope(deptAlias = "t.SALES_DEPT,cust.sales_dept", userAlias = "t.PSNDOC")
    public List<ReceiveDetailVO> selectBusinessReceiveList(ReceiveDetailVO receiveDetail) {
        List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveListByStatus(receiveDetail, null);
        return receiveDetailVOS;
    }

    /**
     * 导出应收明细数据
     *
     * @param receiveDetailVO
     * @return
     */
    @Override
    public List<ReceiveDetailVO> exportReceiveDetailList(ReceiveDetailVO receiveDetailVO) {
        return receiveDetailMapper.exportReceiveDetailList(receiveDetailVO);
    }

    @Override
    public List<ReceiveDetailVO> selectReceiveListByStatus(ReceiveDetailVO receiveDetailVO, List<Integer> statusList) {
        List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveListByStatus(receiveDetailVO, statusList);
        for (ReceiveDetailVO detailVO : receiveDetailVOS) {
            String entrustCostFileId = detailVO.getEntrustCostFileId();
            Map<String, Object> params = new AjaxResult();
            if (StringUtils.isNotEmpty(entrustCostFileId)) {
                List<SysUploadFile> files = sysUploadFileMapper.selectSysUploadFileByTid(entrustCostFileId);
                params.put("fileList", files);
                detailVO.setParams(params);

            }

        }
        return receiveDetailVOS;
    }

    @Override
    public List<ReceiveDetailVO> selectReceiveByInvoiceIds(List<String> ids) {
        return receiveDetailMapper.selectReceiveByInvoiceIds(ids);
    }

    @Override
    public List<ReceiveDetailVO> selectReceiveByInvoiceIdsAndStatus(List<String> ids, int status, Integer isApply) {
        return receiveDetailMapper.selectReceiveByInvoiceIdsAndStatus(ids, status, isApply);
    }

    @Override
    @DataScope(deptAlias = "t4.SALES_DEPT"/*, userAlias = "t.PSNDOC"*/)
    public ReceiveAmountCountVO selectReceiveAmountCount(ReceiveVO receiveVO) {
        if (receiveVO.getReceiveVbillstatus() != null && receiveVO.getReceiveVbillstatus() == 0) {
            receiveVO.getParams().put("receiveVbillstatus", ReceiveDetailStatusEnum.NEW.getValue() + "");
        } else if (receiveVO.getReceiveVbillstatus() != null && receiveVO.getReceiveVbillstatus() == 1) {
            receiveVO.getParams().put("receiveVbillstatus", ReceiveDetailStatusEnum.AFFIRM.getValue() + "");
        } else {
            receiveVO.getParams().put("receiveVbillstatus", ReceiveDetailStatusEnum.NEW.getValue() + "," + ReceiveDetailStatusEnum.AFFIRM.getValue());
        }
        return receiveDetailMapper.selectReceiveAmountCount(receiveVO);
    }

    @Override
    public List<ReceiveDetailVO> listReceiveListByReceCheckSheetId(ReceiveDetailVO receiveDetailVO, String receCheckSheetId) {
        return receiveDetailMapper.listReceiveListByReceCheckSheetId(receiveDetailVO, receCheckSheetId);
    }

    @Override
    public ReceiveAmountCountVO selectBusinessReceiveAmountCount(ReceiveDetailVO receiveDetail) {
        return receiveDetailMapper.selectBusinessReceiveAmountCount(receiveDetail, null);
    }

    /**
     * 调整单导入
     *
     * @param file
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult adjustImport(MultipartFile file) {
        wecomService.checkAndSaveWecomUser(shiroUtils.getSysUser()); // 提交前检查当前用户是否有对应企业微信帐号
        InputStream inputStream = null;
        Workbook workbook = null;
        List<AdjustRecord> list = new ArrayList<>(); // 已成功添加到数据库的数据
        try {
            inputStream = file.getInputStream();
            workbook = WorkbookFactory.create(inputStream);
            //获取第一个sheet
            Sheet sheet = workbook.getSheetAt(0);

            //从第6行开始读取
            for (int rowIndex = 4; rowIndex < 99999; rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                //发货单号
                if(row == null || row.getCell(2) == null){
                    break;
                }
                String invoiceNo = row.getCell(2).getStringCellValue();
                if (StringUtils.isBlank(invoiceNo)) {
                    break;
                }

                //验证是否存在待审核的信息
                Integer cnt = payDetailMapper.selectReceiveDetailUncheckCntByInvoiceNo(invoiceNo);
                if (cnt != null && cnt > 0) {
                    throw new BusinessException("发货单存在待审核记录，无法调整，发货单号：" + invoiceNo);
                }

                //获取调整原因
                String adjustMemo = row.getCell(20).getStringCellValue();
                if (StringUtils.isBlank(adjustMemo)) {
                    throw new BusinessException("请在Excel中填写调整原因");
                }

                String isCostAdjust = row.getCell(21).getStringCellValue();

                //获取是否成本调整和调整描述
                /*String isCostAdjust = sheet.getRow(2).getCell(2).getStringCellValue();
                if (!"是".equals(isCostAdjust) && !"否".equals(isCostAdjust)) {
                    throw new BusinessException("成本是否调整只能填写“是”或“否”，请使用最新模板下载。");
                }
                String costAdjustMemo = "";
                if ("是".equals(isCostAdjust)) {
                    //成本调整描述必填
                    costAdjustMemo = sheet.getRow(2).getCell(5).getStringCellValue();
                    if (StringUtils.isBlank(costAdjustMemo)) {
                        throw new BusinessException("若成本调整为是，请在Excel中填写成本调整描述");
                    }
                }*/

                //获取发货单信息
                Invoice invoice = invoiceMapper.selectInvoiceByInvoiceNo(invoiceNo);
                if(invoice == null){
                    throw new BusinessException("发货单查询失败" + invoiceNo);
                }
                /*if (invoice.getIsClose() != null && invoice.getIsClose() == 0) {
                    throw new BusinessException("发货单需要关账后才能进行调整，未关账单号：" + invoiceNo);
                }*/

                //调整ID
                String adjustRecordId = IdUtil.simpleUUID();
                //插入调整主表T_ADJUST_RECORD
                AdjustRecord adjustRecord = new AdjustRecord();
                adjustRecord.setAdjustRecordId(adjustRecordId);
                adjustRecord.setAdjustRecordType(AdjustRecordEnum.RECEIVE_DETAIL.getValue());
                adjustRecord.setAdjustCheckStatus(AdjustCheckStatusEnum.WAIT_CHECK_FIRST.getValue());
                adjustRecord.setMemo(adjustMemo);
                adjustRecord.setRegUserName(shiroUtils.getSysUser().getUserName());
                adjustRecord.setDelFlag(0);
                adjustRecord.setIsCostAdjust(isCostAdjust);
                adjustRecord.setInvoiceId(invoice.getInvoiceId());
                adjustRecord.setInvoiceVbillno(invoice.getVbillno());
                adjustRecord.setRegScrId("excel_import");
                /*adjustRecord.setCostAdjustMemo(costAdjustMemo);*/
                payDetailMapper.insertAdjustRecord(adjustRecord);

                traceService.checkAdjustRecordSettleGroup(adjustRecordId);
                list.add(adjustRecord); // 添加到返回数据

                //如果是车队数据 并且为分配车队生成的  无
                //
                // 法调整
                if ("1".equals(invoice.getIsFleetData()) && "1".equals(invoice.getIsFleetAssign())) {
                    throw new BusinessException("分配车队的数据无法调整，发货单单号：" + invoiceNo);
                }

                //更新费用
                ReceiveVO receiveVO = new ReceiveVO();
                receiveVO.setInvoiceId(invoice.getInvoiceId());
                receiveVO.setCustomerId(invoice.getCustomerId());
                List<ReceiveVO> receiveVOList = receiveDetailMapper.selectReceiveVOListAdjust(receiveVO);
                ClientPopupVO client = clientMapper.selectClientById(invoice.getCustomerId());
                //获取Excel调整后运费和在途
                Double adjustFreightDouble = row.getCell(18).getNumericCellValue();
                Double adjustOnTheWayDouble = row.getCell(19).getNumericCellValue();
                BigDecimal adjustFreight = new BigDecimal(adjustFreightDouble.toString()).setScale(2,BigDecimal.ROUND_HALF_UP);
                BigDecimal adjustOnTheWay = new BigDecimal(adjustOnTheWayDouble.toString()).setScale(2,BigDecimal.ROUND_HALF_UP);

                //修改前
                BigDecimal freightTotal = receiveVOList.get(0).getFreightTotal();
                BigDecimal onWayTotal = receiveVOList.get(0).getOnWayTotal();

                //需要插入空数据
                int insertPayDetail = 0;

                if (freightTotal.compareTo(adjustFreight) != 0) {
                    //保存应收
                    ReceiveDetailCheck receiveDetail = new ReceiveDetailCheck();
                    receiveDetail.setReceiveDetailCheckId(IdUtil.simpleUUID());
                    receiveDetail.setVbillno(createReceiveDetailVbillno(0));
                    receiveDetail.setVbillstatus(ReceiveDetailStatusEnum.AFFIRM.getValue());//应收单据类型-新建
                    receiveDetail.setFreeType(FreeTypeEnum.PREPAID_CASH.getValue());//费用类型
                    receiveDetail.setCustomerId(invoice.getCustomerId());//客户ID
                    receiveDetail.setBalatype(invoice.getBalaType());//结算方式
                    receiveDetail.setTransFeeCount(freightTotal.multiply(new BigDecimal(-1)));//总金额
                    receiveDetail.setUngotAmount(freightTotal.multiply(new BigDecimal(-1)));//未收金额
                    receiveDetail.setDelFlag(0);
                    receiveDetail.setRegScrId("Receive_adjust");
                    receiveDetail.setCorScrId("Receive_adjust");
                    receiveDetail.setCustOrderno(invoice.getCustOrderno());//客户订单号
                    receiveDetail.setReqDeliDate(invoice.getReqDeliDate());//要求提货日期
                    receiveDetail.setReqArriDate(invoice.getReqArriDate());//要求到货日期
                    receiveDetail.setCustCode(invoice.getCustCode());//客户编码
                    receiveDetail.setCustName(invoice.getCustAbbr());//客户名称
                    receiveDetail.setBalaCorp(invoice.getBillingCorp());//结算公司
                    receiveDetail.setInvoiceVbillno(invoice.getVbillno());//发货单号
                    receiveDetail.setPsndoc(invoice.getPsndoc());//业务员
                    receiveDetail.setSalesDept(invoice.getSalesDept());//运营部
                    receiveDetail.setBalaDept(invoice.getBalaDept());//结算组
                    receiveDetail.setPaymentDays(client.getPaymentDays());//账期
                    receiveDetail.setInvoiceId(invoice.getInvoiceId());
                    receiveDetail.setBalaCustomer(invoice.getBalaCustomerId());
                    receiveDetail.setBalaCode(invoice.getBalaCode());
                    receiveDetail.setBalaName(invoice.getBalaName());
                    receiveDetail.setNumCount(invoice.getNumCount());
                    receiveDetail.setVolumeCount(invoice.getVolumeCount());
                    receiveDetail.setFeeWeightCount(invoice.getWeightCount());
                    receiveDetail.setIsAdjust(1);
                    receiveDetail.setIsClose(1);
                    receiveDetail.setAdjustRecordId(adjustRecordId);

                    receiveDetail.setIsFleetData(invoice.getIsFleetData());
                    receiveDetail.setIsFleetAssign(invoice.getIsFleetAssign());
                    if (receiveDetail.getTransFeeCount().compareTo(BigDecimal.ZERO) != 0) {
                        receiveDetailMapper.insertReceiveDetailCheck(receiveDetail);
                    }

                    //制作NR数据
                    receiveDetail.setReceiveDetailCheckId(IdUtil.simpleUUID());
                    receiveDetail.setVbillno(createReceiveDetailVbillno(0));
                    receiveDetail.setTransFeeCount(adjustFreight);//总金额
                    receiveDetail.setUngotAmount(adjustFreight);//未收金额
                    if (receiveDetail.getTransFeeCount().compareTo(BigDecimal.ZERO) != 0) {
                        receiveDetailMapper.insertReceiveDetailCheck(receiveDetail);
                    }

                    insertPayDetail++;
                }

                if (onWayTotal.compareTo(adjustOnTheWay) != 0) {
                    //保存应收
                    ReceiveDetailCheck receiveDetail = new ReceiveDetailCheck();
                    receiveDetail.setReceiveDetailCheckId(IdUtil.simpleUUID());
                    receiveDetail.setVbillno(createReceiveDetailVbillno(0));
                    receiveDetail.setVbillstatus(ReceiveDetailStatusEnum.AFFIRM.getValue());//应收单据类型-新建
                    receiveDetail.setFreeType(FreeTypeEnum.PREPAID_OIL_CARD.getValue());//费用类型
                    receiveDetail.setCustomerId(invoice.getCustomerId());//客户ID
                    receiveDetail.setBalatype(invoice.getBalaType());//结算方式
                    receiveDetail.setTransFeeCount(receiveVOList.get(0).getOnWayTotal().multiply(new BigDecimal(-1)));//总金额
                    receiveDetail.setUngotAmount(receiveVOList.get(0).getOnWayTotal().multiply(new BigDecimal(-1)));//未收金额
                    receiveDetail.setDelFlag(0);
                    receiveDetail.setRegScrId("Receive_adjust");
                    receiveDetail.setCorScrId("Receive_adjust");
                    receiveDetail.setCustOrderno(invoice.getCustOrderno());//客户订单号
                    receiveDetail.setReqDeliDate(invoice.getReqDeliDate());//要求提货日期
                    receiveDetail.setReqArriDate(invoice.getReqArriDate());//要求到货日期
                    receiveDetail.setCustCode(invoice.getCustCode());//客户编码
                    receiveDetail.setCustName(invoice.getCustAbbr());//客户名称
                    receiveDetail.setBalaCorp(invoice.getBillingCorp());//结算公司
                    receiveDetail.setInvoiceVbillno(invoice.getVbillno());//发货单号
                    receiveDetail.setPsndoc(client.getPsndoc());//业务员
                    receiveDetail.setSalesDept(invoice.getSalesDept());//运营部
                    receiveDetail.setBalaDept(invoice.getBalaDept());//结算组
                    receiveDetail.setPaymentDays(client.getPaymentDays());//账期
                    receiveDetail.setInvoiceId(invoice.getInvoiceId());
                    receiveDetail.setBalaCustomer(invoice.getBalaCustomerId());
                    receiveDetail.setBalaCode(invoice.getBalaCode());
                    receiveDetail.setBalaName(invoice.getBalaName());
                    receiveDetail.setNumCount(invoice.getNumCount());
                    receiveDetail.setVolumeCount(invoice.getVolumeCount());
                    receiveDetail.setFeeWeightCount(invoice.getWeightCount());
                    receiveDetail.setIsAdjust(1);
                    receiveDetail.setIsClose(1);
                    receiveDetail.setAdjustRecordId(adjustRecordId);

                    receiveDetail.setIsFleetData(invoice.getIsFleetData());

                    if (receiveDetail.getTransFeeCount().compareTo(BigDecimal.ZERO) != 0) {
                        receiveDetailMapper.insertReceiveDetailCheck(receiveDetail);
                    }

                    //制作NR数据
                    receiveDetail.setReceiveDetailCheckId(IdUtil.simpleUUID());
                    receiveDetail.setVbillno(createReceiveDetailVbillno(0));
                    receiveDetail.setTransFeeCount(adjustOnTheWay);//总金额
                    receiveDetail.setUngotAmount(adjustOnTheWay);//未收金额
                    if (receiveDetail.getTransFeeCount().compareTo(BigDecimal.ZERO) != 0) {
                        receiveDetailMapper.insertReceiveDetailCheck(receiveDetail);
                    }

                    insertPayDetail++;
                }

                Boolean receiveAdjustHistoryFlag = false;

                //更新货量
                //总重量、总件数、总体积
                Double totalWeight = row.getCell(15).getNumericCellValue();
                Double totalCnt = row.getCell(16).getNumericCellValue();
                Double totalVol = row.getCell(17).getNumericCellValue();
                if (insertPayDetail > 0) {
                    invoiceMapper.updateAdjustInfo(invoiceNo, totalWeight, totalCnt, totalVol);
                    receiveAdjustHistoryFlag = true;
                } else {
                    if (totalWeight.compareTo(invoice.getWeightCount()) != 0 || totalCnt.compareTo(invoice.getNumCount()) != 0
                            || totalVol.compareTo(invoice.getVolumeCount()) != 0) {
                        invoiceMapper.updateAdjustInfo(invoiceNo, totalWeight, totalCnt, totalVol);
                        //插入一条空应收
                        ReceiveDetailCheck receiveDetail = new ReceiveDetailCheck();
                        receiveDetail.setReceiveDetailCheckId(IdUtil.simpleUUID());
                        receiveDetail.setVbillno(createReceiveDetailVbillno(0));
                        receiveDetail.setVbillstatus(ReceiveDetailStatusEnum.AFFIRM.getValue());//应收单据类型-新建
                        receiveDetail.setFreeType(FreeTypeEnum.PREPAID_CASH.getValue());//费用类型
                        receiveDetail.setCustomerId(invoice.getCustomerId());//客户ID
                        receiveDetail.setBalatype(invoice.getBalaType());//结算方式
                        receiveDetail.setTransFeeCount(BigDecimal.ZERO);//总金额
                        receiveDetail.setUngotAmount(BigDecimal.ZERO);//未收金额
                        receiveDetail.setDelFlag(0);
                        receiveDetail.setRegScrId("Receive_adjust");
                        receiveDetail.setCorScrId("Receive_adjust");
                        receiveDetail.setCustOrderno(invoice.getCustOrderno());//客户订单号
                        receiveDetail.setReqDeliDate(invoice.getReqDeliDate());//要求提货日期
                        receiveDetail.setReqArriDate(invoice.getReqArriDate());//要求到货日期
                        receiveDetail.setCustCode(invoice.getCustCode());//客户编码
                        receiveDetail.setCustName(invoice.getCustAbbr());//客户名称
                        receiveDetail.setBalaCorp(invoice.getBillingCorp());//结算公司
                        receiveDetail.setInvoiceVbillno(invoice.getVbillno());//发货单号
                        receiveDetail.setPsndoc(client.getPsndoc());//业务员
                        receiveDetail.setSalesDept(invoice.getSalesDept());//运营部
                        receiveDetail.setBalaDept(invoice.getBalaDept());//结算组
                        receiveDetail.setPaymentDays(client.getPaymentDays());//账期
                        receiveDetail.setInvoiceId(invoice.getInvoiceId());
                        receiveDetail.setBalaCustomer(invoice.getBalaCustomerId());
                        receiveDetail.setBalaCode(invoice.getBalaCode());
                        receiveDetail.setBalaName(invoice.getBalaName());
                        receiveDetail.setNumCount(invoice.getNumCount());
                        receiveDetail.setVolumeCount(invoice.getVolumeCount());
                        receiveDetail.setFeeWeightCount(invoice.getWeightCount());
                        receiveDetail.setIsAdjust(1);
                        receiveDetail.setIsClose(1);
                        receiveDetail.setAdjustRecordId(adjustRecordId);

                        receiveDetail.setIsFleetData(invoice.getIsFleetData());
                        receiveDetailMapper.insertReceiveDetailCheck(receiveDetail);

                        receiveAdjustHistoryFlag = true;
                    }else{
                        throw new BusinessException("提交的excel没有调整，请重新确认。");
                    }
                }

                if (receiveAdjustHistoryFlag) {
                    ReceiveDetailAdjustHistory receiveDetailAdjustHistory = new ReceiveDetailAdjustHistory();
                    receiveDetailAdjustHistory.setId(IdUtil.simpleUUID());
                    receiveDetailAdjustHistory.setAdjustRecordId(adjustRecordId);
                    receiveDetailAdjustHistory.setInvoiceId(invoice.getInvoiceId());
                    receiveDetailAdjustHistory.setInvoiceNo(invoice.getVbillno());
                    receiveDetailAdjustHistory.setNumBefore(invoice.getNumCount());
                    receiveDetailAdjustHistory.setWeightBefore(invoice.getWeightCount());
                    receiveDetailAdjustHistory.setVolumeBefore(invoice.getVolumeCount());
                    receiveDetailAdjustHistory.setFreightBefore(freightTotal);
                    receiveDetailAdjustHistory.setOnWayBefore(onWayTotal);
                    receiveDetailAdjustHistory.setNumAfter(totalCnt);
                    receiveDetailAdjustHistory.setWeightAfter(totalWeight);
                    receiveDetailAdjustHistory.setVolumeAfter(totalVol);
                    receiveDetailAdjustHistory.setFreightAfter(adjustFreight);
                    receiveDetailAdjustHistory.setOnWayAfter(adjustOnTheWay);
                    receiveDetailAdjustHistory.setRegScrId("receiveDetailAdjust");
                    receiveDetailAdjustHistory.setCorScrId("receiveDetailAdjust");
                  /*  receiveDetailAdjustHistory.setMemo(adjustMemo);
                    receiveDetailAdjustHistory.setIsCostAdjust(isCostAdjust);
                    receiveDetailAdjustHistory.setCostAdjustMemo(costAdjustMemo);*/
                    receiveDetailAdjustHistoryMapper.insertReceiveDetailAdjustRecord(receiveDetailAdjustHistory);
                }
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("导入调整单出错：", e);
            throw new BusinessException(e.getMessage() == null ? e.toString() : e.getMessage());
        } finally {
            try {
                if (workbook != null) {
                    workbook.close();
                }
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                throw new BusinessException("IO流关闭失败");
            }
        }
        return AjaxResult.success(list);
    }

    /**
     * 业务审核保存
     *
     * @param adjustRecord
     * @return
     */
    @Override
    @Transactional
    public AjaxResult saveInvoiceCheck(AdjustRecord adjustRecord) {
        adjustRecord.setFirstCheckUserId(shiroUtils.getUserId().toString());
        adjustRecord.setFirstCheckUserName(shiroUtils.getSysUser().getUserName());
        payDetailMapper.saveInvoiceCheck(adjustRecord);
        if (adjustRecord.getCheckStatus() != 1) {
            List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveDetailListByAdjustRecordId(adjustRecord.getAdjustRecordId());
            for (ReceiveDetailVO receiveDetailVO : receiveDetailVOS) {
                invoiceMapper.cleanAdjustInfo(receiveDetailVO.getInvoiceVbillno());
            }
        }
        return AjaxResult.success();
    }

    /**
     * 业务审核保存
     *
     * @param adjustRecord
     * @return
     */
    @Override
    @Transactional
    public AjaxResult saveLeaderCheck(AdjustRecord adjustRecord) {
        adjustRecord.setSecondCheckUserId(shiroUtils.getUserId().toString());
        adjustRecord.setSecondCheckUserName(shiroUtils.getSysUser().getUserName());
        payDetailMapper.saveLeaderCheck(adjustRecord);
        if (adjustRecord.getCheckStatus() != 1) {
            List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveDetailListByAdjustRecordId(adjustRecord.getAdjustRecordId());
            for (ReceiveDetailVO receiveDetailVO : receiveDetailVOS) {
                invoiceMapper.cleanAdjustInfo(receiveDetailVO.getInvoiceVbillno());
            }
        }
        return AjaxResult.success();
    }

    /**
     * 财务确认保存
     *
     * @param adjustRecord
     * @return
     */
    @Override
    @Transactional
    public AjaxResult saveFinanceCheck(AdjustRecord adjustRecord) {
        adjustRecord.setThirdCheckUserId(shiroUtils.getUserId().toString());
        adjustRecord.setThirdCheckUserName(shiroUtils.getSysUser().getUserName());
        payDetailMapper.saveFinanceCheck(adjustRecord);
        //全部确认，插入到receive_detail表中
        List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveDetailListByAdjustRecordId(adjustRecord.getAdjustRecordId());
        if (adjustRecord.getCheckStatus() == 1) {
            for (ReceiveDetailVO receiveDetailVO : receiveDetailVOS) {
                if (receiveDetailVO.getTransFeeCount().compareTo(BigDecimal.ZERO) != 0) {
                    ReceiveDetailAdjustVO receiveDetailAdjustVO = new ReceiveDetailAdjustVO();
                    //判断发货单是否删除
                    Invoice invoice = invoiceMapper.selectInvoiceById(receiveDetailAdjustVO.getInvoiceId());
                    if (invoice == null || invoice.getVbillstatus().equals(InvoiceStatusEnum.CLOSE.getValue()) ) {
                        throw new BusinessException("发货单不存在或已关闭，请审核不通过！");
                    }
                    BeanUtils.copyBeanProp(receiveDetailAdjustVO, receiveDetailVO);
                    receiveDetailMapper.insertReceiveDetailAdjust(receiveDetailAdjustVO);

                    //更新发货单信息
                    invoiceService.synInvoiceCostAmount(receiveDetailAdjustVO.getInvoiceId(),"receive_adjust");
                }

            }
        } else {
            for (ReceiveDetailVO receiveDetailVO : receiveDetailVOS) {
                invoiceMapper.cleanAdjustInfo(receiveDetailVO.getInvoiceVbillno());
            }
        }
        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult partDataReceiveApplication(PartReceiveApplicationVO partReceiveApplication) {
        List<String> list = Arrays.stream(partReceiveApplication.getReceiveDetailIds().split(",")).collect(Collectors.toList());
        List<ReceiveDetailVO> receiveDetailList = receiveDetailMapper.selectReceiveByIds(list);
        String balaCustomer = receiveDetailList.get(0) == null ? "" : receiveDetailList.get(0).getBalaCustomer();

        for (ReceiveDetailVO receiveDetail : receiveDetailList) {
            if (!balaCustomer.equals(receiveDetail.getBalaCustomer())) {
                throw new BusinessException("请选择相同客户的数据！");
            }
            // 若应收明细存在不为已确认状态的情况直接返回0 表示本次修改失败
            if (receiveDetail.getVbillstatus() != ReceiveDetailStatusEnum.AFFIRM.getValue()
                    && receiveDetail.getVbillstatus() != ReceiveDetailStatusEnum.NEW.getValue()) {
                throw new BusinessException("应收状态已发生改变，无法申请！");
            }
            // 若应收存在单笔申请的情况，则无法创建
            if (receiveDetail.getIsApply() != null && receiveDetail.getIsApply() == 1) {
                throw new BusinessException("存在单笔申请的应收，请刷新列表后重试！");
            }
        }

        //开票总金额
        BigDecimal billingAmount = BigDecimal.ZERO;
        for (ReceBilling receBilling : partReceiveApplication.getReceBillingList()) {
            billingAmount = NumberUtil.add(receBilling.getBillingAmount(), billingAmount);
        }

        // 总金额
        BigDecimal transFeeCount = BigDecimal.valueOf(0);
        // 未收金额
        BigDecimal ungotAmount = BigDecimal.valueOf(0);
        // 累计应收明细List中的金额
        for (ReceiveDetailVO receiveDetail : receiveDetailList) {
            transFeeCount = NumberUtil.add(receiveDetail.getTransFeeCount(), transFeeCount);
            ungotAmount = NumberUtil.add(receiveDetail.getUngotAmount(), ungotAmount);
        }

        //判断开票金额与未收金额是否相等
        if (billingAmount.compareTo(ungotAmount) != 0) {
            throw new BusinessException("开票金额合计与未收金额不符！");
        }

        //申请表id
        String receSheetRecordId = IdUtil.simpleUUID();

        /*
         * 生成对账单
         */
        ReceCheckSheet receCheckSheet = new ReceCheckSheet();
        BeanUtils.copyBeanProp(receCheckSheet, partReceiveApplication);
        // 主键
        receCheckSheet.setReceCheckSheetId(IdUtil.simpleUUID());
        // 对账单状态(新建)
        receCheckSheet.setVbillstatus(CheckSheetStatus.AFFIRM.getValue());
        receCheckSheet.setIsFleetData("0");
        receCheckSheet.setRegScrId("partDataReceiveApplication");
        receCheckSheet.setSalesDept(receiveDetailList.get(0).getSalesDept());

        String seq = StrUtil.fillBefore(receCheckSheetMapper.getSeq() + "", '0', 4);
        receCheckSheet.setVbillno("YSDZ" + DateUtil.format(new Date(), "yyyyMMdd") + seq);
        // 对账名称 ：生成规则 客户简称 + yyyyMM + (页面输入的特殊标记)。如果页面没有输入则不加
        String ym = receCheckSheet.getYear() + "" + StrUtil.fillBefore(receCheckSheet.getMonth() + "", '0', 2);
        String receCheckSheetNameOld = receCheckSheet.getReceCheckSheetName();
        String receCheckSheetNameNew = receCheckSheet.getCustName() + "-" + ym;
        int count = receCheckSheetMapper.getSheetNameCount(receCheckSheetNameNew) + 1;
        receCheckSheetNameNew = receCheckSheetNameNew + "-" + count;
        if (StringUtils.isNotEmpty(receCheckSheetNameOld)) {
            receCheckSheetNameNew = receCheckSheetNameNew + "(" + receCheckSheetNameOld + ")";
        }
        receCheckSheet.setReceCheckSheetName(receCheckSheetNameNew);

        //四舍五入总金额，未收金额 保留两位小数
        receCheckSheet.setTotalAmount(transFeeCount.setScale(2, RoundingMode.HALF_UP));
        receCheckSheet.setUngotAmount(ungotAmount.setScale(2, RoundingMode.HALF_UP));
        //申请开票金额
        receCheckSheet.setApplCheckAmount(transFeeCount);
        //申请金额
        receCheckSheet.setApplicationAmount(transFeeCount);
        //申请状态
        receCheckSheet.setApplicationStatus(2);
        //调整金额
        receCheckSheet.setAdjustAmount(transFeeCount);
        //申请表id
        receCheckSheet.setReceSheetRecordId(receSheetRecordId);

        receiveDetailMapper.insertReceCheck(receCheckSheet);

        /*
          * 插入中间表
         */
        editReceiveDetailStatus(receCheckSheet, list);

        // 修改应收单据状态为 已对账
        ReceiveDetailVO receiveDetailVO = new ReceiveDetailVO();
        receiveDetailVO.setCorUserId(shiroUtils.getUserId().toString());
        receiveDetailVO.setCorScrId("partDataReceiveApplication");
        receiveDetailVO.setVbillstatus(ReceiveDetailStatusEnum.RECONCILED.getValue());
        receiveDetailVO.setCorDate(new Date());
        int result = receiveDetailMapper.updateReceiveDetailStatusByList(receiveDetailVO, list, ReceiveDetailStatusEnum.AFFIRM.getValue());
        if (result != list.size()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new BusinessException("应收明细状态以改变，请刷新后重试！");
        }

        /*
         * 申请表信息
         */
        ReceSheetRecord receSheetRecord = new ReceSheetRecord();
        receSheetRecord.setReceSheetRecordId(receSheetRecordId);
        receSheetRecord.setRegScrId("partDataReceiveApplication");
        //客户ID
        receSheetRecord.setCustomerId(partReceiveApplication.getCustomerId());
        //结算客户ID
        receSheetRecord.setBalaCustomerId(partReceiveApplication.getBalaCustomer());
        //收款方式 默认为 0 转账
        receSheetRecord.setReceivableMethod(0);

        receSheetRecord.setReceivableDate(new Date());
        receSheetRecord.setApplyUser(shiroUtils.getSysUser().getUserName());
        //已收
        receSheetRecord.setGotAmount(new BigDecimal(0));
        //未收
        receSheetRecord.setUngotAmount(ungotAmount);
        //
        receSheetRecord.setReceivableAmount(ungotAmount);
        //
        receSheetRecord.setReceivableMan(shiroUtils.getSysUser().getUserName());

        //保存收款类型 	0对账收款 1直接收款
        receSheetRecord.setReceivableType(0);
        //初始状态为已审核
        receSheetRecord.setVbillstatus(ReceSheetRecordEnum.PASS_CHECK.getValue());
        //是否开票
        receSheetRecord.setIsCheck(1);
        //调整金额
        receSheetRecord.setAdjustAmount(ungotAmount);
        receSheetRecord.setIsFleetData("0");

        int rows2 = receSheetRecordMapper.insertReceSheetRecord(receSheetRecord);
        if (rows2 == 0) {
            throw new BusinessException("保存收款申请失败");
        }

        /*
         * 开票信息
         */
        List<ReceBilling> receBillingList = partReceiveApplication.getReceBillingList();
        receBillingList.removeIf(x -> StringUtils.isEmpty(x.getBillingPayable()));

        //申请开票金额
        BigDecimal applCheckAmount = BigDecimal.ZERO;
        //开票申请
        for (ReceBilling receBilling : receBillingList) {
            receBilling.setReceBillingId(IdUtil.simpleUUID());
            //收款申请id
            receBilling.setReceSheetRecordId(receSheetRecordId);
            //客户简称
            receBilling.setCustAbbr(receCheckSheet.getCustName());
            //申请人
            receBilling.setApplyUser(receSheetRecord.getApplyUser());
            //申请时间
            receBilling.setApplyDate(new Date());
            receBilling.setRegScrId("partDataReceiveApplication");
            receBilling.setCorScrId("partDataReceiveApplication");
            receBilling.setIsFleetData("0");
            //收款类型 	0对账收款 1直接收款
            receBilling.setReceivableType(0);
            //是否是车队数据
            receBilling.setIsFleetData("0");
            //判断开票金额是否等于0
            int flag = receBilling.getBillingAmount().compareTo(BigDecimal.ZERO);
            if (flag == 0) {
                throw new BusinessException("开票金额不能为0");
            }

            int rows3 = receBillingMapper.insertReceBilling(receBilling);
            if (rows3 == 0) {
                throw new BusinessException("保存开票申请失败");
            }
            List<ReceBillingQd> qdList = receBilling.getQdList();
            if (qdList != null) {
                int seqx = 0;
                for (int j = 0; j < qdList.size(); j++) {
                    if (StringUtils.isNotEmpty(qdList.get(j).getSpmc())) {
                        qdList.get(j).setId(IdUtil.simpleUUID());
                        qdList.get(j).setSeq(++seqx);
                        qdList.get(j).setReceBillingId(receBilling.getReceBillingId());
                        receBillingMapper.saveReceBillingQd(qdList.get(j));
                    }
                }
                receBillingMapper.saveReceBillingQdCount(receBilling.getReceBillingId());
            }
            applCheckAmount = NumberUtil.add(applCheckAmount, receBilling.getBillingAmount());
        }

        //比较申请金额与调整金额
        int flag = applCheckAmount.compareTo(receSheetRecord.getAdjustAmount());
        if (flag != 0) {
            throw new BusinessException("申请开票总金额与申请金额不符");
        }

        /*
         * 修改应收明细
         */
        for (ReceiveDetailVO rd : receiveDetailList) {
            ReceiveDetail receiveDetail = new ReceiveDetail();
            receiveDetail.setReceiveDetailId(rd.getReceiveDetailId());
            //调整金额
            receiveDetail.setAdjustAmount(rd.getTransFeeCount());
            receiveDetailMapper.updateReceiveDetail(receiveDetail);
        }

        /*
         * 修改发货单是否加入对账单状态
         */
        List<String> invoiceIdList = receiveDetailList.stream()
                .map(ReceiveDetailVO::getInvoiceId).distinct().collect(Collectors.toList());

        for (String id : invoiceIdList) {
            List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveByInvoiceId(id);

            //已对账的数量
            long reconciledCount = receiveDetailVOS.stream()
                    .filter(x -> ReceiveDetailStatusEnum.RECONCILED.getValue() == x.getVbillstatus()).count();

            Invoice invoice = new Invoice();
            invoice.setInvoiceId(id);
            if (reconciledCount == receiveDetailVOS.size()) {
                //已全部加入对账包
                invoice.setIsAddReceCheck(2);
                invoiceMapper.updateInvoice(invoice);
            } else if (reconciledCount < receiveDetailVOS.size() && reconciledCount >0) {
                //部分加入对账包
                invoice.setIsAddReceCheck(1);
                invoiceMapper.updateInvoice(invoice);
            }else{
                invoice.setIsAddReceCheck(0);
                invoiceMapper.updateInvoice(invoice);
            }
        }



        return AjaxResult.success();
    }

    @Override
    public String getMostYearMonthOfReqDeliDate(List<String> invoiceIds, List<String> receiveDetailIds) {
        if (invoiceIds != null) {
            return invoiceMapper.getMostYearMonthOfReqDeliDateByInvoice(invoiceIds);
        } else if (receiveDetailIds != null) {
            return invoiceMapper.getMostYearMonthOfReqDeliDateByReceiveDetail(receiveDetailIds);
        }
        return null;
    }

    @Override
    public List<UnBillingDetail> listUnbilling(ReceiveDetailVO receiveDetailVO) {
        return receiveDetailMapper.listUnbillingDetail(receiveDetailVO);
    }

    @Override
    public Map<String, Object> sumUnbilling(ReceiveDetailVO receiveDetailVO) {
        return receiveDetailMapper.sumUnbilling(receiveDetailVO);
    }

    @Override
    @DataScope(deptAlias = "a.SALES_DEPT", userAlias = "a.PSNDOC")
    public List<CustTransFeeCountRankVO> calculateTransFeeCountRateOnTimeRate(ReceiveDetail receiveDetail) {
        return receiveDetailMapper.calculateTransFeeCountRateOnTimeRate(receiveDetail);
    }

    @Override
    @DataScope(deptAlias = "a.SALES_DEPT", userAlias = "a.PSNDOC")
    public Double calculateTransFeeCountRateOnTimeAllRate(ReceiveDetail receiveDetail) {
        return receiveDetailMapper.calculateTransFeeCountRateOnTimeAllRate(receiveDetail);
    }

    @Override
    public List<UnBilling> listSumUnbilling(ReceiveDetailVO receiveDetailVO) {
        return receiveDetailMapper.listSumUnbilling(receiveDetailVO);
    }

    @Override
    @DataScope(deptAlias = "receive.SALES_DEPT", userAlias = "receive.PSNDOC")
    public Map<String, Object> calculateCustomerOnTimeCount(ReceiveDetail receiveDetail) {
        return receiveDetailMapper.calculateCustomerOnTimeCount(receiveDetail);
    }

    @Override
    @DataScope(deptAlias = "receive.SALES_DEPT", userAlias = "receive.PSNDOC")
    public Map<String, Object> calculateBillingOnTimeCount(ReceiveDetail receiveDetail) {
        return receiveDetailMapper.calculateBillingOnTimeCount(receiveDetail);
    }

    @Override
    public List<ZkxxChatsDataVO> selectZkxxchatsData() {
        return receiveDetailMapper.selectZkxxchatsData();
    }

    @Override
    public List<UnApplyDetail> listUnApplyDtl(ReceiveDetailVO receiveDetailVO) {
        return receiveDetailMapper.listUnApplyDtl(receiveDetailVO);
    }

    @Override
    @DataScope(deptAlias = "cust.sales_dept")
    public Map<String, Object> loadWarnInfo(ReceiveDetailVO receiveDetail) {
        return receiveDetailMapper.loadWarnInfo(receiveDetail);
    }

    @Override
    public List<YslrwcChatsDataVO> selectYslrwcChats() {
        return receiveDetailMapper.selectYslrwcChats();
    }

    @Override
    public List<ReceiveDetailVO> selectReceiveListByReceCheckSheetId(String tReceCheckSheetId) {
        return receiveDetailMapper.selectReceiveListByReceCheckSheetId(tReceCheckSheetId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult splitReceiveDetail(SplitReceiveDetailVO splitReceiveDetailVO) {
        String receiveDetailId = splitReceiveDetailVO.getReceiveDetailId();
        BigDecimal splitTransFeeCount = splitReceiveDetailVO.getTransFeeCount();

        ReceiveDetailVO receiveDetailVO = receiveDetailMapper.selectReceiveById(receiveDetailId);
        if (receiveDetailVO == null) {
            return AjaxResult.error("请选择正确的应收明细。");
        }

        if (ReceiveDetailStatusEnum.NEW.getValue() != receiveDetailVO.getVbillstatus()) {
            return AjaxResult.error("请选择新建状态的应收明细。");
        }

        if (!"0".equals(receiveDetailVO.getFreeType())) {
            return AjaxResult.error("请选择“运费”类型的应收明细。");
        }

        //总金额
        BigDecimal transFeeCount = receiveDetailVO.getTransFeeCount();

        if (!(NumberUtil.isGreater(transFeeCount, splitTransFeeCount)
                && NumberUtil.isGreater(splitTransFeeCount, BigDecimal.ZERO))) {
            return AjaxResult.error("请输入正确的金额。");
        }

        ReceiveDetailNoAutoFillVO receiveDetailNoAutoFillVO = new ReceiveDetailNoAutoFillVO();
        BeanUtils.copyProperties(receiveDetailVO, receiveDetailNoAutoFillVO);
        receiveDetailNoAutoFillVO.setRegScrId("receiveDetailService.splitReceiveDetail");
        receiveDetailNoAutoFillVO.setRegUserId(shiroUtils.getUserId().toString());
        receiveDetailNoAutoFillVO.setRegUserName(shiroUtils.getSysUser().getUserName());

        receiveDetailNoAutoFillVO.setReceiveDetailId(IdUtil.simpleUUID());
        receiveDetailNoAutoFillVO.setVbillno(createReceiveDetailVbillno(0));
        receiveDetailNoAutoFillVO.setTransFeeCount(splitTransFeeCount);
        receiveDetailMapper.insertReceiveDetailNoAutoFill(receiveDetailNoAutoFillVO);

        receiveDetailNoAutoFillVO.setReceiveDetailId(IdUtil.simpleUUID());
        receiveDetailNoAutoFillVO.setVbillno(createReceiveDetailVbillno(0));
        receiveDetailNoAutoFillVO.setTransFeeCount(NumberUtil.sub(transFeeCount,splitTransFeeCount));
        receiveDetailMapper.insertReceiveDetailNoAutoFill(receiveDetailNoAutoFillVO);

        ReceiveDetailVO updateVO = new ReceiveDetailVO();
        updateVO.setReceiveDetailId(receiveDetailVO.getReceiveDetailId());
        updateVO.setDelDate(new Date());
        updateVO.setDelFlag(1);
        updateVO.setDelUserId(shiroUtils.getUserId().toString());
        int i = receiveDetailMapper.updateReceiveByIdRX(updateVO, ReceiveDetailStatusEnum.NEW.getValue());
        if (i == 0) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("更新失败，请刷新后重试！");
        }

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult revokeDriverCollection(String receiveDetailIds) {
        String[] ids = Convert.toStrArray(receiveDetailIds);

        List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveByIds(Arrays.asList(ids));
        if (receiveDetailVOS == null || receiveDetailVOS.size() ==0) {
            throw new BusinessException("未查询到应收。");
        }

        long count = receiveDetailVOS.stream()
                .filter(x -> ReceiveDetailStatusEnum.NEW.getValue() != x.getVbillstatus()
                        && ReceiveDetailStatusEnum.AFFIRM.getValue() != x.getVbillstatus()).count();
        if (count > 0) {
            throw new BusinessException("请选择“新建”或者““已确认””状态的应收。");
        }


        for (ReceiveDetailVO receiveDetailVO : receiveDetailVOS) {
            if (receiveDetailVO.getIsCollect() == 1) {
                ReceiveDetail receiveDetail = new ReceiveDetail();
                receiveDetail.setReceiveDetailId(receiveDetailVO.getReceiveDetailId());
                receiveDetail.setIsCollect(0);
                receiveDetail.setCorScrId("ReceiveDetailService.revokeDriverCollection");
                receiveDetailMapper.updateReceiveDetail(receiveDetail);
            }
        }

        List<String> invIds = receiveDetailVOS.stream()
                .map(ReceiveDetailVO::getInvoiceId).distinct().collect(Collectors.toList());

        for (String invId : invIds) {
            //
            List<DriverCollection> driverCollectionList = driverCollectionMapper.selectAllByInvoiceId(invId);
            if (driverCollectionList != null && driverCollectionList.size() > 0) {
                throw new BusinessException("存在司机代收申请记录，无法撤销。");
            }

            List<ReceiveDetailVO> rds = receiveDetailMapper.selectReceiveByInvoiceId(invId);
            boolean allIsCollectZero = rds.stream()
                    .allMatch(rd -> rd.getIsCollect() == 0);

            if (allIsCollectZero) {
                Entrust entrustUpdate = new Entrust();
                entrustUpdate.setOrderno(invId);
                entrustUpdate.setCollectAmount(null);
                entrustMapper.updateEntrustCollectAmountByInvoiceId(entrustUpdate);
            }
        }

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult cancelDriverCollection(String invoiceId) {
        Invoice invoice = invoiceMapper.selectInvoiceById(invoiceId);
        if (invoice == null) {
            throw new BusinessException("发货单不存在。");
        }

        List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveByInvoiceId(invoiceId);

        //所有代收应付
        List<ReceiveDetailVO> collectList = receiveDetailVOS.stream()
                .filter(x -> x.getIsCollect() == 1)
                .collect(Collectors.toList());

        //司机代收所有数据
        List<DriverCollection> driverCollectionList = driverCollectionMapper.selectAllByInvoiceId(invoiceId);

        //非已核销的司机代收   需要删除
        List<DriverCollection> driverCollectionWirteOffList = driverCollectionList.stream()
                .filter(x -> !DriverCollectionStatusEnum.WIRTE_OFF.getValue().equals(x.getStatus()))
                .collect(Collectors.toList());

        for (DriverCollection dc : driverCollectionWirteOffList) {
            DriverCollection driverCollection = new DriverCollection();
            driverCollection.setDelDate(new Date());
            driverCollection.setDelUserId(shiroUtils.getUserId().toString());
            driverCollection.setDelFlag(1);
            driverCollection.setCorScrId("ReceiveDetailService.cancelDriverCollection");
            List<Integer> statusList = Arrays.asList(DriverCollectionStatusEnum.NEW.getValue(), DriverCollectionStatusEnum.AFFIRM.getValue());
            int i = driverCollectionMapper.updateByIdAndStatus(driverCollection, dc.getPayDetailId(), statusList);

            if (i == 0) {
                throw new BusinessException("删除失败。");
            }
        }


        //已核销的代收应付
        List<ReceiveDetailVO> collectWirteOffList = collectList.stream()
                .filter(x -> ReceiveDetailStatusEnum.HAS_BEEN_WRITTEN_OFF.getValue() == x.getVbillstatus())
                .collect(Collectors.toList());

        //未核销的代收应付
        List<ReceiveDetailVO> collectUnWirteOffList = collectList.stream()
                .filter(x -> ReceiveDetailStatusEnum.HAS_BEEN_WRITTEN_OFF.getValue() != x.getVbillstatus())
                .collect(Collectors.toList());

        //改为不是代收
        for (ReceiveDetailVO re : collectUnWirteOffList) {
            ReceiveDetail receiveDetail = new ReceiveDetail();
            receiveDetail.setReceiveDetailId(re.getReceiveDetailId());
            receiveDetail.setIsCollect(0);
            receiveDetail.setCorScrId("ReceiveDetailService.cancelDriverCollection");
            receiveDetailMapper.updateReceiveDetail(receiveDetail);
        }

        //没有已核销的司机代收  就修改发货单的结算方式 改为回单结算
        if (driverCollectionList.size() == driverCollectionWirteOffList.size() && collectWirteOffList.isEmpty()) {
            Invoice invoiceUpdate = new Invoice();
            invoiceUpdate.setInvoiceId(invoiceId);
            invoiceUpdate.setBalaType("1");
            invoiceUpdate.setCollectAmount(BigDecimal.ZERO);
            invoiceMapper.updateInvoice(invoiceUpdate);

        }

        List<ReceiveDetailVO> rds = receiveDetailMapper.selectReceiveByInvoiceId(invoiceId);
        BigDecimal reduce = rds.stream()
                .filter(x -> x.getIsCollect() == 1)
                .map(ReceiveDetailVO::getTransFeeCount)
                .reduce(BigDecimal::add)
                .orElse(null);

        Entrust entrustUpdate = new Entrust();
        entrustUpdate.setOrderno(invoiceId);
        entrustUpdate.setCollectAmount(reduce);
        entrustMapper.updateEntrustCollectAmountByInvoiceId(entrustUpdate);

        return AjaxResult.success();
    }

}
