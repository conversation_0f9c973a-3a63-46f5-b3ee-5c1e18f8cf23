package com.ruoyi.tms.service.invoice.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.config.Global;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.security.Md5Utils;
import com.ruoyi.system.domain.SysDept;
import com.ruoyi.system.domain.SysDictData;
import com.ruoyi.system.domain.SysUploadFile;
import com.ruoyi.system.domain.SysUser;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysDictDataMapper;
import com.ruoyi.system.mapper.SysUploadFileMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.tms.constant.BillingMethod;
import com.ruoyi.tms.constant.InvoiceStatusEnum;
import com.ruoyi.tms.constant.SegmentStatusEnum;
import com.ruoyi.tms.constant.basic.CarrierProtocolPricingMethodEnum;
import com.ruoyi.tms.constant.check.CheckStatusEnum;
import com.ruoyi.tms.constant.dept.DeptIdConstant;
import com.ruoyi.tms.constant.finance.CheckSheetStatus;
import com.ruoyi.tms.constant.finance.FreeTypeEnum;
import com.ruoyi.tms.constant.finance.OtherFeeStatusEnum;
import com.ruoyi.tms.constant.finance.ReceiveDetailStatusEnum;
import com.ruoyi.tms.constant.guideprice.ReferencePriceTypeConstant;
import com.ruoyi.tms.constant.invoice.InvoiceSegmentEnum;
import com.ruoyi.tms.constant.trace.EntrustCostCheckStatusEnum;
import com.ruoyi.tms.domain.basic.*;
import com.ruoyi.tms.domain.business.OtherFeeCheckSheet;
import com.ruoyi.tms.domain.business.OtherFeeCheckSheetB;
import com.ruoyi.tms.domain.carrier.Entrust;
import com.ruoyi.tms.domain.carrier.EntrustLot;
import com.ruoyi.tms.domain.client.*;
import com.ruoyi.tms.domain.enquiry.RfqEnquiry;
import com.ruoyi.tms.domain.enquiry.RfqEnquiryLine;
import com.ruoyi.tms.domain.enquiry.TInquiryRecord;
import com.ruoyi.tms.domain.finance.OtherFee;
import com.ruoyi.tms.domain.finance.PayDetail;
import com.ruoyi.tms.domain.finance.ReceCheckSheet;
import com.ruoyi.tms.domain.finance.SettlementCheckLog;
import com.ruoyi.tms.domain.guideprice.CalculateGuidePriceDTO;
import com.ruoyi.tms.domain.guideprice.GuidePriceDetail;
import com.ruoyi.tms.domain.guideprice.ReferencePrice;
import com.ruoyi.tms.domain.guideprice.ReferencePriceHistory;
import com.ruoyi.tms.domain.invoice.*;
import com.ruoyi.tms.domain.pushmessagetoapp.PushMessageDTO;
import com.ruoyi.tms.domain.segment.SegPackGoods;
import com.ruoyi.tms.domain.segment.Segment;
import com.ruoyi.tms.domain.trace.Allocation;
import com.ruoyi.tms.domain.trace.EntrustCost;
import com.ruoyi.tms.domain.trace.EntrustCostMain;
import com.ruoyi.tms.domain.trace.Unconfirm;
import com.ruoyi.tms.domain.utils.StringUtil;
import com.ruoyi.tms.handler.RedisLock;
import com.ruoyi.tms.handler.WecomHandler;
import com.ruoyi.tms.mapper.basic.*;
import com.ruoyi.tms.mapper.business.OtherFeeCheckSheetBMapper;
import com.ruoyi.tms.mapper.business.OtherFeeCheckSheetMapper;
import com.ruoyi.tms.mapper.carrier.EntrustLotMapper;
import com.ruoyi.tms.mapper.carrier.EntrustMapper;
import com.ruoyi.tms.mapper.client.*;
import com.ruoyi.tms.mapper.contract.MContractBusinessMapper;
import com.ruoyi.tms.mapper.finance.*;
import com.ruoyi.tms.mapper.guideprice.*;
import com.ruoyi.tms.mapper.invoice.*;
import com.ruoyi.tms.mapper.segment.SegPackGoodsMapper;
import com.ruoyi.tms.mapper.segment.SegmentMapper;
import com.ruoyi.tms.mapper.trace.*;
import com.ruoyi.tms.mapper.wecom.WecomSpMapper;
import com.ruoyi.tms.service.basic.IUrgentContactService;
import com.ruoyi.tms.service.client.IAutoDispatchConfigService;
import com.ruoyi.tms.service.client.ICustMiscFeeConfigService;
import com.ruoyi.tms.service.enquiry.IInquiryRecordService;
import com.ruoyi.tms.service.enquiry.IRfqService;
import com.ruoyi.tms.service.finance.IOtherFeeService;
import com.ruoyi.tms.service.finance.IPayDetailService;
import com.ruoyi.tms.service.finance.IReceiveDetailService;
import com.ruoyi.tms.service.guideprice.ICustGuidePriceService;
import com.ruoyi.tms.service.invoice.IInvoiceService;
import com.ruoyi.tms.service.invoice.IMultipleShippingAddressService;
import com.ruoyi.tms.service.message.ISysMessageService;
import com.ruoyi.tms.service.message.IWechatHookService;
import com.ruoyi.tms.service.pushmessagetoapp.PushMessageService;
import com.ruoyi.tms.service.segment.ISegPackGoodsService;
import com.ruoyi.tms.service.segment.ISegmentService;
import com.ruoyi.tms.service.trace.ITraceService;
import com.ruoyi.tms.service.trace.impl.AsyncJobService;
import com.ruoyi.tms.service.wecom.IWecomService;
import com.ruoyi.tms.vo.basic.SysUploadFileVO;
import com.ruoyi.tms.vo.basic.UrgentContactVO;
import com.ruoyi.tms.vo.carrier.EntrustLotDTO;
import com.ruoyi.tms.vo.client.ClientPopupVO;
import com.ruoyi.tms.vo.client.CustAddressDto;
import com.ruoyi.tms.vo.client.CustMiscFeeVO;
import com.ruoyi.tms.vo.contract.ContractBusinessVO;
import com.ruoyi.tms.vo.finance.*;
import com.ruoyi.tms.vo.invoice.*;
import com.ruoyi.tms.vo.main.CzdjChatsDataVO;
import com.ruoyi.tms.vo.main.DdblChatsDataVO;
import com.ruoyi.tms.vo.main.DdzbChatsDataVO;
import com.ruoyi.tms.vo.part.PartInvoiceDataVO;
import com.ruoyi.tms.vo.segment.CalculateReferencePriceVO;
import com.ruoyi.tms.vo.segment.DispatchVO;
import com.ruoyi.tms.vo.segment.SubsectionAddressVO;
import com.ruoyi.tms.vo.trace.EntrustDto;
import com.ruoyi.tms.vo.trace.InvoiceVO;
import com.ruoyi.util.ShiroUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.shiro.crypto.SecureRandomNumberGenerator;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.Future;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.ruoyi.common.config.Global.getConfig;

/**
 * 发货单 服务层实现
 *
 * <AUTHOR>
 * @date 2019-09-09
 */
@Slf4j
@Service
public class InvoiceServiceImpl implements IInvoiceService {
    private static final Logger logger = LoggerFactory.getLogger(InvoiceServiceImpl.class);
    /**
     * 整车
     */
    private static final HashMap FULL_VEHICLE_MAP = new HashMap<String, String>() {
        {
            put("0", "公路整车");
            put("4", "冷链整车");
            put("15", "危化整车");
        }
    };
    @Resource
    private EntrustCostMainMapper entrustCostMainMapper;
    @Resource
    private EntrustCostMapper entrustCostMapper;
    @Autowired
    private ICustGuidePriceService custGuidePriceService;
    @Autowired
    private InvoiceMapper invoiceMapper;
    @Autowired
    private InvPackGoodsMapper invPackGoodsMapper;
    @Autowired
    private CustContractpcMapper custContractpcMapper;
    @Autowired
    private ContractpcSectionMapper contractpcSectionMapper;
    @Autowired
    private SegmentMapper segmentMapper;
    @Autowired
    private ReceiveDetailMapper receiveDetailMapper;
    @Autowired
    private EntrustMapper entrustMapper;
    @Autowired
    private SegPackGoodsMapper segPackGoodsMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private ClientMapper clientMapper;
    @Autowired
    private SysUploadFileMapper sysUploadFileMapper;
    @Autowired
    private OtherFeeMapper otherFeeMapper;
    @Autowired
    @Lazy
    private IOtherFeeService otherFeeService;
    @Autowired
    private ResidentsInvoiceMapper residentsInvoiceMapper;
    @Autowired
    private TransLineMapper transLineMapper;
    @Autowired
    private CustGuidePriceMapper custGuidePriceMapper;
    @Autowired
    private GuidePriceDetailMapper guidePriceDetailMapper;
    @Autowired
    private ISegPackGoodsService segPackGoodsService;
    @Autowired
    private IReceiveDetailService receiveDetailService;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private ShiroUtils shiroUtils;
    @Autowired
    private PushMessageService pushMessageService;
    @Autowired
    private IUrgentContactService urgentContactService;
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private TraceMapper traceMapper;
    @Autowired
    private GoodsMapper goodsMapper;
    @Autowired
    private PayDetailMapper payDetailMapper;
    @Autowired
    private EntrustLotMapper entrustLotMapper;
    @Autowired
    private AllocationMapper allocationMapper;
    @Resource
    OtherFeeCheckSheetBMapper otherFeeCheckSheetBMapper;
    @Resource
    OtherFeeCheckSheetMapper otherFeeCheckSheetMapper;
    @Autowired
    private ISysMessageService sysMessageService;
    @Autowired
    private IWechatHookService wechatHookService;
    @Autowired
    private MultipleShippingAddressMapper multipleShippingAddressMapper;
    @Autowired
    private MultipleShippingGoodsMapper multipleShippingGoodsMapper;
    @Autowired
    private UnconfirmMapper unconfirmMapper;
    @Autowired
    private ProvinceMapper provinceMapper;
    @Autowired
    private ISysDictDataService dictService;
    @Autowired
    private InvoiceImportMapper invoiceImportMapper;
    @Autowired
    private IInquiryRecordService inquiryRecordService;
    @Autowired
    private EntrustWorkMapper entrustWorkMapper;
    @Autowired
    private SettlementCheckLogMapper settlementCheckLogMapper;
    @Autowired
    private ReferencePriceMapper referencePriceMapper;
    @Autowired
    private ReferencePriceHistoryMapper referencePriceHistoryMapper;
    @Autowired
    private SpecialReferencePriceMapper specialReferencePriceMapper;
    @Autowired
    private InvoiceDetailMapper invoiceDetailMapper;
    @Autowired
    private InvoiceDetailAddrMapper invoiceDetailAddrMapper;
    @Autowired
    private ReceivableReconciliationMapper receivableReconciliationMapper;
    @Resource
    private ISysConfigService sysConfigService;
    @Autowired
    @Lazy
    private ISegmentService segmentService;

    @Autowired
    private SysDictDataMapper sysDictDataMapper;
    @Autowired
    private MSalesGroupMapper salesGroupMapper;
    @Resource
    private AddressMapper addressMapper;
    @Resource
    private CustAddressMapper custAddressMapper;
    @Resource
    private IAutoDispatchConfigService autoDispatchConfigService;
    @Resource
    private CarrierMapper carrierMapper;
    @Resource
    private ReceCheckSheetMapper receCheckSheetMapper;
    @Autowired
    private WecomSpMapper wecomSpMapper;
    @Autowired
    private IWecomService wecomService;
    @Lazy
    @Resource
    private IPayDetailService payDetailService;
    @Resource
    private BatchAdjustMapper batchAdjustMapper;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private DriverMapper driverMapper;
    @Resource
    private WecomHandler wecomHandler;
    @Resource
    private CarMapper carMapper;
    @Lazy
    @Resource
    private ITraceService traceService;
    @Resource
    private RedisLock redisLock;
    @Resource
    @Lazy
    private IRfqService rfqService;
    @Autowired
    @Lazy
    private AsyncJobService asyncJobService;
    @Autowired
    private AddressLonlatMapper addressLonlatMapper;
    @Autowired
    private ContractpcVersionMapper contractpcVersionMapper;
    @Autowired
    private AutoDispatchVersionMapper autoDispatchVersionMapper;
    @Autowired
    private MContractBusinessMapper contractBusinessMapper;
    @Resource
    private ICustMiscFeeConfigService custMiscFeeConfigService;
    @Resource
    private ContractpcCostPriceLogMapper contractpcCostPriceLogMapper;

    /**
     * 画面id
     */
    private String pageId = "invoice";

    /**
     * 查询发货单信息
     *
     * @param invoiceId 发货单ID
     * @return 发货单信息
     */
    @Override
    public Invoice selectInvoiceById(String invoiceId) {
        //获取发货单主表信息
        Invoice invoice = invoiceMapper.selectByPrimaryKey(invoiceId);
        if (invoice != null) {
            //获取相关货品信息
            InvPackGoods invPackGoods = new InvPackGoods();
            invPackGoods.setInvoiceId(invoiceId);
            invPackGoods.setDelFlag(0);
            List<InvPackGoods> invPackGoodsList = invPackGoodsMapper.selectInvPackGoodsList(invPackGoods);
            invoice.setInvPackGoodsList(invPackGoodsList);
        }
        return invoice;
    }

    @Override
    public List<Invoice> selectInvoiceByInvoiceVbillnos(String[] invoiceVbillnos) {
        return invoiceMapper.selectInvoiceByInvoiceVbillnos(invoiceVbillnos);
    }

    /**
     * 查询发货单列表
     *
     * @param invoice 发货单信息
     * @return 发货单集合
     */
    @Override
    @DataScope(deptAlias = "t1.sales_dept,t.sales_dept", userAlias = "t.psndoc",custIdAlias="t.customer_id")
    public List<InvoicePageVO> selectInvoicePageList(InvoicePageVO invoice) {
        if (StringUtils.isNotBlank(invoice.getInvoiceIds())) {
            invoice.setInvoiceIdArr(invoice.getInvoiceIds().split(","));
        }
        List<InvoicePageVO> invoices = invoiceMapper.selectInvoiceListPermission(invoice);
        for (InvoicePageVO i : invoices) {
//            if ("1".equals(i.getIsFleetData()) && "0".equals(i.getIsFleetAssign())) {
//                List<ReceiveDetailVO> receiveDetailVOS = receiveDetailService.selectReceiveByInvoiceId(i.getInvoiceId());
//
//                boolean b = receiveDetailVOS.stream()
//                        .anyMatch(x -> ReceiveDetailStatusEnum.RECONCILED.getValue() == x.getVbillstatus()
//                                || ReceiveDetailStatusEnum.PARTIAL_WRITE_OFF.getValue() == x.getVbillstatus()
//                                || ReceiveDetailStatusEnum.HAS_BEEN_WRITTEN_OFF.getValue() == x.getVbillstatus());
//                i.setIsAddReceCheck(b ? "1" : "0");
//            }
//        }
//        for (Invoice i : invoices) {
            //多装多卸 地址与货品信息
            List<MultipleShippingAddress> addresses = multipleShippingAddressMapper.selectListByInvoiceId(i.getInvoiceId());
//
//            for (MultipleShippingAddress address : addresses) {
//                List<MultipleShippingGoods> goods = multipleShippingGoodsMapper
//                        .selectListByAddressId(address.getMultipleShippingAddressId());
//                address.setShippingGoodsList(goods);
//            }
            i.setShippingAddressList(addresses);

//            String[] ids = {i.getInvoiceId()};
//            List<InvPackGoods> invPackGoods = invPackGoodsMapper.selectInvPackGoodsListByInvoiceId(ids);
//
//            String goodsName = invPackGoods.stream().map(InvPackGoods::getGoodsName).collect(Collectors.joining("，"));
//            i.getParams().put("goodsName", goodsName);

            //查询发货单下委托单
//            List<Entrust> entrusts = entrustMapper.selectEntrustListByInvoiceId(i.getInvoiceId());
//
//            List<SysUploadFile> receiptUploadFiles = new ArrayList<>();
//            //查询回单照片
//            for (Entrust entrust : entrusts) {
//                if (StringUtils.isNotBlank(entrust.getReceiptUploadTid())) {
//                    List<SysUploadFile> sysUploadFiles = sysUploadFileMapper.selectSysUploadFileByTid(entrust.getReceiptUploadTid());
//                    receiptUploadFiles.addAll(sysUploadFiles);
//                }
//            }
//            i.setReceiptUploadFiles(receiptUploadFiles);

            if (StringUtils.isNotEmpty(i.getRfqEnquiryId())) {
                RfqEnquiry rfqEnquiry = rfqService.getEnquiryXById(i.getRfqEnquiryId());
                i.setEnquiryNo(rfqEnquiry.getVbillno());
            }

            if (!i.getVbillstatus().equals("0") && !i.getVbillstatus().equals("8") && !i.getVbillstatus().equals("9")) {
                List<Segment> segments = segmentMapper.selectSegmentByInvoiceId(i.getInvoiceId());

                //
                String segTransLineName = segments.stream()
                        .filter(x -> x.getSegMark() == 2 && !x.getTransLineName().equals(i.getTransLineName()))
                        .map(Segment::getTransLineName)
                        .collect(Collectors.joining(","));
                i.setSegTransLineName(segTransLineName);
            }
        }
        return invoices;
    }

    @Override
    @DataScope(deptAlias = "t1.sales_dept,t.sales_dept", userAlias = "t.psndoc",custIdAlias="t.customer_id")
    public List<InvoiceExportVO> selectInvoiceListExport(Invoice invoice) {
        return invoiceMapper.selectInvoiceListExport(invoice);
    }

    /**
     * 新增发货单
     *
     * @param invoiceAddVO 发货单信息
     * @param isFleet      添加的类型  0：业务下单   1：车队下单
     * @return 结果 1:成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult insertInvoice(InvoiceAddVO invoiceAddVO, int isFleet) {
        Invoice invoice = new Invoice();
        BeanUtils.copyProperties(invoiceAddVO, invoice);

        //查询客户信息
        ClientPopupVO clientPopup = clientMapper.selectClientById(invoice.getCustomerId());

//        Integer crtGuidePrice = clientPopup.getCrtGuidePrice();
//        if (FULL_VEHICLE_MAP.containsKey(invoice.getTransCode())
//                && invoice.getIsMultiple() == 0 && crtGuidePrice == 1
//                && (invoice.getGuidingPrice() == null || invoice.getGuidingPrice().compareTo(BigDecimal.ZERO) == 0)) {
//            return AjaxResult.error("请填入非零的指导价！");
//        }

        if (invoice.getIfBargain() != null && invoice.getIfBargain() == 0
                && !checkCustomerContractValid(clientPopup.getCustomerId())) {
            return AjaxResult.error("客户合同暂无或已失效，无法下单。");
        }

        if (StringUtils.isEmpty(invoiceAddVO.getRfqEnquiryLineId())
                && clientPopup.getContractPriceType() != null && clientPopup.getContractPriceType() == 0
                && invoiceAddVO.getIfBargain() != 0) {
            return AjaxResult.error("该客户只可以选择合同价下单。");
        }

        if (StringUtils.isEmpty(invoiceAddVO.getRfqEnquiryLineId()) && isFleet == 0 && clientPopup.getIfBargain() == 0
                && invoiceAddVO.getIfBargain() == 1 && StringUtils.isEmpty(invoiceAddVO.getBargainMemo())) {
            return AjaxResult.error("议价类型需填写议价说明。");
        }

        if (StringUtils.isEmpty(clientPopup.getOperateCorp())) {
            return AjaxResult.error("客户缺少结算公司。");
        }
        invoice.setOperateCorp(clientPopup.getOperateCorp());

        //多装多卸 地址信息
        List<MultipleShippingAddress> shippingAddressList = invoice.getShippingAddressList();
        //过滤空数据
        shippingAddressList.removeIf(x -> x.getShippingGoodsList() == null || x.getShippingGoodsList().isEmpty());

        /*
         * 判断货品是否相等
         */
        Map<String, List<MultipleShippingGoods>> deliGoodsMap = shippingAddressList.stream()
                .filter(x -> x.getAddressType() == 0)
                .flatMap(a -> a.getShippingGoodsList().stream())
                .filter(x -> StringUtils.isNotEmpty(x.getGoodsName()))
                .collect(Collectors.groupingBy(g -> g.getGoodsName() + "_" + g.getCustOrderno()));

        Map<String, String> deliGoodsCtMap = deliGoodsMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            MultipleShippingGoods reduce = entry.getValue().stream()
                                    .reduce(new MultipleShippingGoods(), (sum, goods) -> {
                                        sum.setNum(NumberUtil.add(sum.getNum(), goods.getNum()));
                                        sum.setWeight(NumberUtil.add(sum.getWeight(), goods.getWeight()));
                                        sum.setVolume(NumberUtil.add(sum.getVolume(), goods.getVolume()));
                                        return sum;
                                    });
                            return Convert.toStr(reduce.getNum(), "-") + "_"
                                    + Convert.toStr(reduce.getWeight(), "-") + "_"
                                    + Convert.toStr(reduce.getVolume(), "-");
                        }
                ));

        Map<String, List<MultipleShippingGoods>> arriGoodsMap = shippingAddressList.stream()
                .filter(x -> x.getAddressType() == 1)
                .flatMap(a -> a.getShippingGoodsList().stream())
                .filter(x -> StringUtils.isNotEmpty(x.getGoodsName()))
                .collect(Collectors.groupingBy(g -> g.getGoodsName() + "_" + g.getCustOrderno()));

        Map<String, String> arriGoodsCtMap = arriGoodsMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            MultipleShippingGoods reduce = entry.getValue().stream()
                                    .reduce(new MultipleShippingGoods(), (sum, goods) -> {
                                        sum.setNum(NumberUtil.add(sum.getNum(), goods.getNum()));
                                        sum.setWeight(NumberUtil.add(sum.getWeight(), goods.getWeight()));
                                        sum.setVolume(NumberUtil.add(sum.getVolume(), goods.getVolume()));
                                        return sum;
                                    });
                            return Convert.toStr(reduce.getNum(), "-") + "_"
                                    + Convert.toStr(reduce.getWeight(), "-") + "_"
                                    + Convert.toStr(reduce.getVolume(), "-");
                        }
                ));

        if (!arriGoodsCtMap.equals(deliGoodsCtMap)) {
            return AjaxResult.error("提货和到货相关的货物信息（含客户单号）需要保持一致。");
        }


        //货品信息  去提货地址中 对应的货品信息
        List<InvPackGoods> goodsList = new ArrayList<>();
        //初始化
        invoice.setIsChangeAddress(0);
        for (MultipleShippingAddress address : shippingAddressList) {
            String provinceName = address.getProvinceName();
            String provinceId = address.getProvinceId();
            //
            String cityName = address.getCityName();
            String cityId = address.getCityId();
            //
            String areaName = address.getAreaName();
            String areaId = address.getAreaId();

            //省份
            String provinceCode = provinceMapper.getProvinceCode(provinceName);
            //
            String cityCode = provinceMapper.getCityCode(provinceId, cityName);
            //
            String areaCode = provinceMapper.getAreaCode(cityId, areaName);

            if (provinceCode == null || !provinceCode.equals(provinceId)) {
                return AjaxResult.error("收发货地址省份不存在");
            }

            if (cityCode == null || !cityCode.equals(cityId)) {
                return AjaxResult.error("收发货地址城市不存在");
            }

            if (areaCode == null || !areaCode.equals(areaId)) {
                return AjaxResult.error("收发货地址区域不存在");
            }

            if(address.getIsChangeAddress() != null && address.getIsChangeAddress() == 1){
                invoice.setIsChangeAddress(1);
            }

            //地址id
            String addressId = IdUtil.simpleUUID();
            address.setMultipleShippingAddressId(addressId);

            //地址对应的货品信息
            List<MultipleShippingGoods> shippingGoodsList = address.getShippingGoodsList();
//            shippingGoodsList.removeIf(x -> StringUtils.isEmpty(x.getGoodsId()));

            //件数、重量、体积合计
            double numSum = shippingGoodsList.stream().filter(x -> x.getNum() != null).mapToDouble(MultipleShippingGoods::getNum).sum();
            double volumeSum = shippingGoodsList.stream().filter(x -> x.getVolume() != null).mapToDouble(MultipleShippingGoods::getVolume).sum();
            double weightSum = shippingGoodsList.stream().filter(x -> x.getWeight() != null).mapToDouble(MultipleShippingGoods::getWeight).sum();
            String goodsName = shippingGoodsList.stream().map(MultipleShippingGoods::getGoodsName).distinct().collect(Collectors.joining(","));
            address.setNumCount(numSum);
            address.setVolumeCount(volumeSum);
            address.setWeightCount(weightSum);
            address.setGoodsName(goodsName);

            //关联地址id
            shippingGoodsList.forEach(x -> {
                String goodsId = IdUtil.simpleUUID();
                x.setMultipleShippingAddressId(addressId);
                x.setMultipleShippingGoodsId(goodsId);
            });
            address.setShippingGoodsList(shippingGoodsList);

            /*
             * 将发货对应的货品 存入InvPackGoods 中
             */
            if (address.getAddressType() == 0) {
                for (MultipleShippingGoods multipleShippingGoods : shippingGoodsList) {
                    InvPackGoods invPackGoods = new InvPackGoods();
                    BeanUtils.copyBeanProp(invPackGoods, multipleShippingGoods);
                    goodsList.add(invPackGoods);
                }
            }
        }
        invoice.setInvPackGoodsList(goodsList);

        //客户单号
        String custOrdernos = goodsList.stream()
                .map(InvPackGoods::getCustOrderno)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.joining(","));
        invoice.setCustOrderno(custOrdernos);

        //货品
        String goodsName = goodsList.stream()
                .map(InvPackGoods::getGoodsName)
                .distinct()
                .collect(Collectors.joining(","));
        invoice.setGoodsName(goodsName);

        invoice.setNumCount(goodsList.stream().map(InvPackGoods::getNum).filter(Objects::nonNull).reduce(0.0, Double::sum));
        invoice.setWeightCount(goodsList.stream().map(InvPackGoods::getWeight).filter(Objects::nonNull).reduce(0.0, Double::sum));
        invoice.setVolumeCount(goodsList.stream().map(InvPackGoods::getVolume).filter(Objects::nonNull).reduce(0.0, Double::sum));

        //取提货类型的第一条 作为发货单里的发货信息
        MultipleShippingAddress deliAddress = shippingAddressList.stream()
                .filter(x -> x.getAddressType() == 0).limit(1).findFirst().orElse(new MultipleShippingAddress());

        //提货地址
//        invoice.setDeliveryId(deliAddress.getDeliveryId());
//        invoice.setDeliAddrCode(deliAddress.getAddrCode());
        invoice.setDeliAddrName(deliAddress.getAddrName());
        //提货(省)
        invoice.setDeliProvinceId(deliAddress.getProvinceId());
        invoice.setDeliProName(deliAddress.getProvinceName());
        //提货（市）
        invoice.setDeliCityId(deliAddress.getCityId());
        invoice.setDeliCityName(deliAddress.getCityName());
        //提货(区)
        invoice.setDeliAreaId(deliAddress.getAreaId());
        invoice.setDeliAreaName(deliAddress.getAreaName());
        //提货详细地址
        invoice.setDeliDetailAddr(deliAddress.getDetailAddr());
        //提货联系人
        invoice.setDeliContact(deliAddress.getContact());
        //联系人手机
        invoice.setDeliMobile(deliAddress.getMobile());

        //取到货类型的第一条 作为发货单里的到货信息
        MultipleShippingAddress arriAddress = shippingAddressList.stream()
                .filter(x -> x.getAddressType() == 1).limit(1).findFirst().orElse(new MultipleShippingAddress());
        //到货地址
//        invoice.setArrivalId(arriAddress.getDeliveryId());
//        invoice.setArriAddrCode(arriAddress.getAddrCode());
        invoice.setArriAddrName(arriAddress.getAddrName());
        //到货(省)
        invoice.setArriProvinceId(arriAddress.getProvinceId());
        invoice.setArriProName(arriAddress.getProvinceName());
        //到货地址（市）
        invoice.setArriCityId(arriAddress.getCityId());
        invoice.setArriCityName(arriAddress.getCityName());
        //到货地址（区）
        invoice.setArriAreaId(arriAddress.getAreaId());
        invoice.setArriAreaName(arriAddress.getAreaName());
        //到货地址详细地址
        invoice.setArriDetailAddr(arriAddress.getDetailAddr());
        //到货联系人
        invoice.setArriContact(arriAddress.getContact());
        //到货联系人手机
        invoice.setArriMobile(arriAddress.getMobile());

        //是否多装多卸
        invoice.setIsMultiple(shippingAddressList.size() > 2 ? 1 : 0);

        //不为多装多卸时  设置指导价
//        if (invoice.getIsMultiple() == 0) {
//            //置空
//            invoice.setGuidingPrice(null);
//            //线路、新增指导价
//            generateTransLineAndGuide(invoice.getDeliProvinceId(), invoice.getDeliProName(), invoice.getDeliCityId()
//                    , invoice.getDeliCityName(), invoice.getDeliAreaId(), invoice.getDeliAreaName()
//                    , invoice.getArriProvinceId(), invoice.getArriProName(), invoice.getArriCityId()
//                    , invoice.getArriCityName(), invoice.getArriAreaId(), invoice.getArriAreaName(), invoice.getTransCode()
//                    , invoice.getCarLen(), invoice.getCarLenName(), invoice.getCarType(), invoice.getCarTypeName(), true
//                    , invoiceAddVO.getCostPrice(), "invoiceService.insertInvoice");
//
//            //指导价浮动比率
//            invoice.setReferenceRate(clientPopup.getReferenceRate());
//        }

        invoice.setUnloadPlaceNum((int) shippingAddressList.stream().filter(x -> x.getAddressType() == 1).count());
        invoice.setLoadPlaceNum((int) shippingAddressList.stream().filter(x -> x.getAddressType() == 0).count());

        //是否往返
        if (invoice.getIsRoundTrip() == null) {
            invoice.setIsRoundTrip(0);
        }
        if ("4".equals(invoice.getBillingMethod())) {
            invoice.setIsRoundTrip(1);
        }


        //判断要求提货日期是否关账
        String yearMonth = DateFormatUtils.format(invoice.getReqDeliDate(), "yyyy-MM");
        Integer cnt = invoiceMapper.selectInvoiceCloseYearMonth(yearMonth);
        if (cnt != null && cnt > 0) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("要求提货日期的月份已关账，无法操作！");
        }
        //验证客户下是否存在已经关账，但是还未加入对账单的应收明细
        /*Integer cnt = invoiceMapper.selectReceiveDetailByCustomerAndIsClose(invoice.getCustomerId());
        if(cnt != null && cnt > 0){
            return AjaxResult.error("该客户发货单下存在"+cnt+"条已关账但未对账的应收明细，请先处理！");
        }
        //判断客户下是否存在没确认的应收对账单
        Integer checkSheetCnt = invoiceMapper.selectReceiveCheckSheetByCustomerAndIsClose(invoice.getCustomerId());
        if(checkSheetCnt != null && checkSheetCnt > 0){
            return AjaxResult.error("该客户下存在"+checkSheetCnt+"条未确认的应收对账单，请先处理！");
        }*/

        /*
         * copy时验证
         *      1.客户：验证运营组，业务员
         *      2.调度组，调度人员，驻场组，驻场人员
         *      3.货品是否删除
         */
        if (clientPopup.getIsEnabled() == 1) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("该客户已停用，请重新选择客户！");
        }

        if (StringUtils.isNotBlank(clientPopup.getSalesDept())) {
            if (!invoice.getSalesDept().equals(clientPopup.getSalesDept())) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return AjaxResult.error("该客户已更换运营组，请重新选择客户！");
            }
        }
        if (StringUtils.isNotBlank(clientPopup.getPsndoc())) {
            if (!invoice.getPsndoc().equals(clientPopup.getPsndoc())) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return AjaxResult.error("该客户已更换业务员，请重新选择客户！");
            }
        }
//        List<InvPackGoods> invPackGoodsList1 = invoice.getInvPackGoodsList();
//        for (InvPackGoods goods : invPackGoodsList1) {
//            if (goods.getSum() != null) {
//                Goods goodsHistory = goodsMapper.selectGoodsById(StringUtils.isBlank(goods.getGoodsId()) ? "-1" : goods.getGoodsId());
//                if (StringUtils.isNull(goodsHistory)) {
//                    return AjaxResult.error("货品：" + goods.getGoodsName() + "已被删除，请重新选择货品！");
//                }
//            }
//        }

        //调度组
        List<SysDept> transLineDept;
        if (isFleet == 0) {
            transLineDept = deptService.selectDeptByParentIds(DeptIdConstant.getDispatcherDeptId());
        } else {
            transLineDept = deptService.selectDeptByParentIds(DeptIdConstant.getFleetDispatcherDeptId());
            SysDept zmy = deptService.selectDeptById(1741L);
            if (zmy != null){
                transLineDept.add(zmy);
            }

        }

        long transLineCount = transLineDept.stream().filter(x -> invoice.getTransLineId().equals(x.getDeptId().toString())).count();
        if (transLineCount == 0) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("调度组不存在！");
        }
        //驻场组
        if (StringUtils.isNotBlank(invoice.getStationDept())) {
            List<SysDept> stationDept = deptService.selectDeptByParentId(DeptIdConstant.STATION_DEPT_ID);
            long stationCount = stationDept.stream().filter(x -> invoice.getStationDept().equals(x.getDeptId().toString())).count();
            if (stationCount == 0) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return AjaxResult.error("驻场组不存在！");
            }
        }
        //驻场人员
        if (StringUtils.isNotBlank(invoice.getResidentsId())) {
            String[] residentsIdArray = com.ruoyi.common.core.text.Convert.toStrArray(invoice.getResidentsId());

            List<SysUser> stationUserlist = userService.selectUserListByRoles("station", new SysUser());
            for (String residentsId : residentsIdArray) {
                long stationUserCount = stationUserlist.stream().filter(x -> residentsId.equals(x.getUserId().toString())).count();
                if (stationUserCount == 0) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return AjaxResult.error("驻场人员不存在！");
                }
            }
        }

        //
        String deliContact = invoice.getDeliContact().trim();
        if (deliContact.length() < 2) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("发货方联系人名称必须大于一个字符！");
        }
        //
        String arriContact = invoice.getArriContact().trim();
        if (arriContact.length() < 2) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("收货方联系人名称必须大于一个字符！");
        }

        SysDictData billingTypeDictData = sysDictDataMapper
                .selectDictDataByTypeAndValue("billing_type", invoice.getBillingType());
        if (billingTypeDictData == null) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("请选择正确的开票类型！");
        }

        //合同价计算
        if (invoice.getIfBargain() != null && invoice.getIfBargain() == 0) {
            //查询合同价
            SearchContractPriceVO priceVO = new SearchContractPriceVO();
            List<String> deliAreaIdList = shippingAddressList.stream()
                    .filter(x -> x.getAddressType() == 0
                            && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                    .map(MultipleShippingAddress::getAreaId)
                    .collect(Collectors.toList());
            priceVO.setDeliAreaIdList(deliAreaIdList);

            List<String> deliAddrNameList = shippingAddressList.stream()
                    .filter(x -> x.getAddressType() == 0
                            && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                    .map(MultipleShippingAddress::getAddrName)
                    .collect(Collectors.toList());
            priceVO.setDeliAddrNameList(deliAddrNameList);

          /*  Integer isChangeAddress = invoice.getIsChangeAddress();
            if (isChangeAddress != null && isChangeAddress == 1) {
                List<String> arriAreaIdList = new ArrayList<>();
                arriAreaIdList.add(invoice.getCaArriAreaId());
                priceVO.setArriAreaIdList(arriAreaIdList);
            } else {
                List<String> arriAreaIdList = shippingAddressList.stream()
                        .filter(x -> x.getAddressType() == 1
                                && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                        .map(MultipleShippingAddress::getAreaId)
                        .collect(Collectors.toList());

                priceVO.setArriAreaIdList(arriAreaIdList);
            }*/

            List<String> arriAreaIdList = new ArrayList<>();
            List<String> arriArriNameList = new ArrayList<>();

            for(MultipleShippingAddress multipleShippingAddress : shippingAddressList){
                if(multipleShippingAddress.getAddressType() == 1
                        && multipleShippingAddress.getIsGetContractPrice() != null
                        && multipleShippingAddress.getIsGetContractPrice() == 1){
                    if(multipleShippingAddress.getIsChangeAddress() != null && multipleShippingAddress.getIsChangeAddress() == 1){
                        arriAreaIdList.add(multipleShippingAddress.getCaArriAreaId());
                        arriArriNameList.add(multipleShippingAddress.getCaArriAddrName());
                    }else{
                        arriAreaIdList.add(multipleShippingAddress.getAreaId());
                        arriArriNameList.add(multipleShippingAddress.getAddrName());
                    }
                }
            }
            priceVO.setArriAreaIdList(arriAreaIdList);
            priceVO.setArriAddrNameList(arriArriNameList);

            priceVO.setCustomerId(invoice.getCustomerId());

            priceVO.setBillingMethod(Integer.valueOf(invoice.getBillingMethod()));


            List<String> goodsNameList = goodsList.stream()
                    .map(InvPackGoods::getGoodsName)
                    .distinct()
                    .collect(Collectors.toList());
            if (goodsNameList.size() == 1) {
                priceVO.setGoodsName(goodsNameList.get(0));
            }

            //货品特性
//            String goodsCharacter = "15".equals(invoice.getTransCode()) || "16".equals(invoice.getTransCode()) ? "1" : "0";
            String goodsCharacter;
            if ("15".equals(invoice.getTransCode()) || "16".equals(invoice.getTransCode())) {
                goodsCharacter = "1";
            } else if ("4".equals(invoice.getTransCode())) {
                goodsCharacter = "2";
            }else {
                goodsCharacter = "0";
            }
            priceVO.setGoodsCharacter(goodsCharacter);
            //车长
            priceVO.setCarLen(invoice.getCarLen());
            priceVO.setCarType(invoice.getCarType());

            priceVO.setNum(Convert.toBigDecimal(invoice.getNumCount(),BigDecimal.ZERO));
            priceVO.setWeight(com.ruoyi.common.core.text.Convert.toBigDecimal(invoice.getWeightCount(),BigDecimal.ZERO));
            priceVO.setVolume(Convert.toBigDecimal(invoice.getVolumeCount(),BigDecimal.ZERO));
            priceVO.setMileage(invoice.getMileage() == null ? BigDecimal.ZERO : BigDecimal.valueOf(invoice.getMileage()));

            priceVO.setIsOversize(invoice.getIsOversize());

            priceVO.setTransCode(invoice.getTransCode());

            priceVO.setIsRoundTrip(invoice.getIsRoundTrip());

            Map<String, String> priceMap = getPrice(priceVO);
            if ("0".equals(priceMap.get("type"))) {
                return AjaxResult.error("未查询到合同价。");
            }

            if ("".equals(priceMap.get("costPrice"))) {
                return AjaxResult.error("未查询到成本价。");
            }

            //税率
            BigDecimal rate = billingTypeDictData.getNumVal1();

            //是否含税  0不含税  1含税*
            String isIncludeTax = priceMap.get("isIncludeTax");

            //总评价
            BigDecimal totalPrice = Convert.toBigDecimal(priceMap.get("totalPrice"));
            //单价
            BigDecimal price = Convert.toBigDecimal(priceMap.get("price"));
            //成本价
            BigDecimal totalCostPrice = Convert.toBigDecimal(priceMap.get("totalCostPrice"));
            String costBillingType = priceMap.get("costBillingType");

            Integer isFixedPrice = Convert.toInt(priceMap.get("isFixedPrice"), null);

            String contractpcVersionId = priceMap.get("versionId");

            invoice.setContractIsFixedPrice(isFixedPrice);
            //
            invoice.setContractPrice(price);
            //成本价
            invoice.setCostPrice(totalCostPrice);
            invoice.setCostBillingType(totalCostPrice == null ? null : costBillingType);

            invoice.setContractPriceTotal(totalPrice);
            //合同价版本id
            invoice.setContractpcVersionId(contractpcVersionId);

            //是否含税  0不含税  1含税*
            if ("0".equals(isIncludeTax)) {
                //不含税
                invoice.setCostAmountIncludeTax(totalPrice);
                invoice.setUnitPriceIncludeTax(price);

                if (rate != null && rate.compareTo(BigDecimal.ZERO) != 0) {
                    invoice.setCostAmount(NumberUtil.round(NumberUtil.mul(totalPrice, rate), 2));
                    invoice.setUnitPrice(NumberUtil.round(NumberUtil.mul(price, rate), 2));
                }else {
                    invoice.setCostAmount(totalPrice);
                    invoice.setUnitPrice(price);
                }
            }else {
                //含税
                invoice.setCostAmount(totalPrice);
                invoice.setUnitPrice(price);
                if (rate != null && rate.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal costAmountIncludeTax = NumberUtil.div(invoice.getCostAmount(), rate, 2);
                    invoice.setCostAmountIncludeTax(costAmountIncludeTax);

                    BigDecimal unitPriceIncludeTax = NumberUtil.div(invoice.getUnitPrice(), rate, 2);
                    invoice.setUnitPriceIncludeTax(unitPriceIncludeTax);

                }else {
                    invoice.setCostAmountIncludeTax(totalPrice);
                    invoice.setUnitPriceIncludeTax(price);
                }
            }

            //送货费
            invoice.setDeliveryFee(Convert.toBigDecimal(priceMap.get("deliveryFee")));

            //置空议价说明
            invoice.setBargainMemo(null);
        }

        //计算总金额
        invoice.setTotalFee(NumberUtil.add(invoice.getCostAmount(), invoice.getOtherFee(), invoice.getDeliveryFee()));
        //在途费用
        invoice.setOnWayAmountFee(NumberUtil.add(invoice.getOtherFee(), invoice.getDeliveryFee()));


        //设置是否为 车队数据  0是业务下单数据  1是车队下单数据
        if (isFleet == 0) {
            invoice.setIsFleetData("0");
        } else {
            invoice.setIsFleetData("1");
        }
        //是否存在对应的业务或者车队数据  0不存在
        invoice.setIsFleetAssign("0");


        //运营部
        String salesDeptId = clientPopup.getSalesDept();
        SysDept custSales = sysDeptMapper.getParentDeptInfo(salesDeptId);
        invoice.setCustSalesId(String.valueOf(custSales.getDeptId()));
        invoice.setCustSalesName(custSales.getDeptName());
        //管理部
        SysDept mgmtDept = sysDeptMapper.getParentDeptInfo(String.valueOf(custSales.getDeptId()));
        invoice.setMgmtDeptId(String.valueOf(mgmtDept.getDeptId()));
        invoice.setMgmtDeptName(mgmtDept.getDeptName());

//        //客户运营部
//        String salesId = clientPopup.getSalesId();
//        if (StringUtils.isNotEmpty(salesId)) {
//            MSalesGroup mSalesGroup = salesGroupMapper.selectByPrimaryKey(salesId);
//
//            invoice.setCustSalesId(salesId);
//            invoice.setCustSalesName(mSalesGroup.getSalesName());
//        }

        //三超类型
        if (invoiceAddVO.getIsOversize() == 1) {
            if ((invoiceAddVO.getGoodsLength() != null && invoiceAddVO.getGoodsLength().compareTo(BigDecimal.ZERO) != 0)
                    || (invoiceAddVO.getGoodsWidth() != null && invoiceAddVO.getGoodsWidth().compareTo(BigDecimal.ZERO) != 0)
                    || (invoiceAddVO.getGoodsHeight() != null && invoiceAddVO.getGoodsHeight().compareTo(BigDecimal.ZERO) != 0) ) {
                //0超体积
                invoice.setOversizeType(0);
            }else {
                //1超重
                invoice.setOversizeType(1);
            }
        }

        //设置发货单id uuid
        String invoiceId = IdUtil.simpleUUID();
        invoice.setInvoiceId(invoiceId);

        //删除标记 0 未删除
        invoice.setDelFlag(0);

        //发货单新建
        invoice.setVbillstatus(InvoiceStatusEnum.NEW.getValue());

        //结算公司
        String balaCorpId = invoice.getBalaCorpId();

        //设置发货单号 逻辑：结算公司编号 + 当前年月日 + 四位Sequence。例如：MY201909150001
        invoice.setVbillno(createInvoiceVbillno(invoice.getOperateCorp(), isFleet));
        //发货单来源 （系统端1；货主端2）
        invoice.setSrcType("1");
        //业务类型代码（参照数据字典 默认保存城市配送 1003997）
        invoice.setBusinesstypename("1003997");
        //设置调度状态
        invoice.setSegmentStatus(InvoiceSegmentEnum.NO_DISPATCH.getValue());
        //是否加入对账单 0未加入 1部分加入 2全部加入
        invoice.setIsAddReceCheck(0);
        //应收核销状态 0未核销  1部分核销 2已核销
        invoice.setReceivableWriteOffStatus("0");
        //是否开票
        invoice.setIfBilling("6".equals(invoice.getBillingType()) ? "0" : "1");

        invoiceMapper.insertSelective(invoice);

        /*
         * 插入驻场人员与发货单关联表
         */
        if (StringUtils.isNotEmpty(invoice.getResidentsId())) {
            String residentsId = invoice.getResidentsId();
            String[] residentsIds = com.ruoyi.common.core.text.Convert.toStrArray(residentsId);
            for (String id : residentsIds) {
                if (StringUtils.isNotBlank(id)) {
                    SysUser sysUser = sysUserMapper.selectUserById(Long.valueOf(id));
                    ResidentsInvoice residentsInvoice = new ResidentsInvoice();
                    residentsInvoice.setResidentsInvoiceId(IdUtil.simpleUUID());
                    //发货单id
                    residentsInvoice.setInvoiceId(invoiceId);
                    //驻场人员id
                    residentsInvoice.setResidentsId(id);
                    //驻场人员姓名
                    residentsInvoice.setResidentsName(sysUser.getUserName());
                    //画面id
                    residentsInvoice.setRegScrId(invoice.getRegScrId());
                    residentsInvoice.setCorScrId(invoice.getCorScrId());
                    residentsInvoiceMapper.insertResidentsInvoice(residentsInvoice);
                }
            }
        }

        /*
         * 添加发货单货品明细
         */
        List<InvPackGoods> invPackGoodsList = invoice.getInvPackGoodsList();
        for (InvPackGoods invPackGoods : invPackGoodsList) {
            //发货单货品id
            invPackGoods.setInvPackGoodsId(IdUtil.simpleUUID());
            //发货单货品 删除标记 0 未删除
            invPackGoods.setDelFlag(0);
            //发货单 id
            invPackGoods.setInvoiceId(invoiceId);
            //重量必须>0
            if (invPackGoods.getWeight() == null) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return AjaxResult.error("货品重量必须大于0！");
            }
            if (invPackGoods.getWeight() == 0) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return AjaxResult.error("货品重量必须大于0！");
            }
            //画面id
            invPackGoods.setRegScrId(invoice.getRegScrId());
            invPackGoods.setCorScrId(invoice.getCorScrId());

            invPackGoodsMapper.insertInvPackGoods(invPackGoods);
        }

        /*
         * 运输方式为多装多卸时   保存对应的 t_multiple_shipping_address和t_multiple_shipping_goods
         */
        for (MultipleShippingAddress multipleShippingAddress : shippingAddressList) {
            multipleShippingAddress.setInvoiceId(invoice.getInvoiceId());
            multipleShippingAddress.setInvoiceNo(invoice.getVbillno());
            multipleShippingAddress.setIsFleetData(invoice.getIsFleetData());
            multipleShippingAddress.setIsFleetAssign(invoice.getIsFleetAssign());
            multipleShippingAddress.setRegScrId(pageId);
            multipleShippingAddressMapper.insertSelective(multipleShippingAddress);
            //插入货品
            for (MultipleShippingGoods multipleShippingGoods : multipleShippingAddress.getShippingGoodsList()) {
                multipleShippingGoods.setRegScrId(pageId);
                multipleShippingGoodsMapper.insertSelective(multipleShippingGoods);
            }
        }

        // 0普通 1紧急
        if ("1".equals(invoice.getUrgentLevel())) {
            //消息推送tms移动端
            try {
                sendTmsMobile(invoice);
            } catch (Exception e) {
                logger.error("新增发货单消息推送异常：{}", e.getMessage());
                e.printStackTrace();
            }
        }

        //TODO 询价管理页面创建发货单
        if (StringUtils.isNotEmpty(invoice.getInquiryRecordId())) {
            TInquiryRecord inquiryRecord = new TInquiryRecord();
            inquiryRecord.setId(invoice.getInquiryRecordId());
            inquiryRecord.setInvoiceStatus((short) 1);
            Future<String> stringFuture = inquiryRecordService.updateInvoiceStatus(inquiryRecord);
        }

        //推送企业微信机器人
        wechatHookService.sendMsgCreateInvoiceData(invoice.getTransLineId(), invoice, invPackGoodsList, shippingAddressList);

//        wechatHookService.sendMsgUrgentData(invoice,shiroUtils.getSysUser().getUserName());

        if (invoice.getIsUrgent() != null && invoice.getIsUrgent() == 1) {
            logger.debug("加急单推送，IsUrgent参数为：{}", invoice.getIsUrgent());
            wechatHookService.sendMsgUrgentData(invoice, shiroUtils.getUserId().toString(), shiroUtils.getSysUser().getUserName());
        } else {
            logger.debug("加急单不推送，IsUrgent参数为：{}", invoice.getIsUrgent());
        }
        Map<String, String> resMap = new HashMap<>();
        resMap.put("invoiceId", invoice.getInvoiceId());
        resMap.put("invoiceNo", invoice.getVbillno());

        return AjaxResult.success("操作成功", resMap);
    }


    /**
     * 校验客户合同是否有效
     *
     * @param customerId 客户id
     * @return true=合同有效，false=合同无效
     */
    public Boolean checkCustomerContractValid(String customerId) {
        // 查询客户信息
        ClientPopupVO clientPopup = clientMapper.selectClientById(customerId);

        if (clientPopup != null && clientPopup.getContractNeedType() != null && clientPopup.getContractNeedType() == 1) {
            // 不需要合同
            return true;
        }

        // 查询合同有效期（格式：'长期有效'、'有合同无有效期'、'yyyy-MM-dd'，或null）
        String validDateStr = contractBusinessMapper.getCustomerContractValidDateByCustomerId(customerId);

        if (StrUtil.isEmpty(validDateStr)) {
            // 没查到合同
            return false;
        }

        if ("长期有效".equals(validDateStr)) {
            return true;
        }

        if ("有合同无有效期".equals(validDateStr)) {
            return false;
        }

        // 否则尝试解析为日期并比较是否过期
        try {
            Date validDate = DateUtil.parse(validDateStr, "yyyy-MM-dd");
            Date now = DateUtil.parse(DateUtil.today(), "yyyy-MM-dd");
            return DateUtil.compare(validDate, now) >= 0;
        } catch (Exception e) {
            log.error("合同有效期格式解析失败，customerId=" + customerId + "，validDateStr=" + validDateStr, e);
            return false;
        }

    /*    // 查询该客户合同
        List<ContractBusinessVO> contractBusinessVOS = contractBusinessMapper.selectContractBusinessByCustomerId(customerId);

        if (CollUtil.isEmpty(contractBusinessVOS)) {
            // 没有合同
            return false;
        }

        // 是否有长期有效
        boolean hasLongTerm = contractBusinessVOS.stream()
                .anyMatch(c -> "1".equals(c.getLongTermEffective()));
        if (hasLongTerm) {
            return true;
        }

        // 计算最大有效日期
        Date maxValidDate = null;
        for (ContractBusinessVO contract : contractBusinessVOS) {
            Date endDate = contract.getEndDate();
            Date extensionDate = contract.getExtensionDate();
            Date validDate = null;

            if (endDate == null && extensionDate == null) {
                validDate = null;
            } else if (endDate == null) {
                validDate = extensionDate;
            } else if (extensionDate == null) {
                validDate = endDate;
            } else {
                validDate = DateUtil.compare(endDate, extensionDate) >= 0 ? endDate : extensionDate;
            }

            if (validDate != null) {
                if (maxValidDate == null || DateUtil.compare(validDate, maxValidDate) > 0) {
                    maxValidDate = validDate;
                }
            }
        }

        // 如果最大有效日期不为空且大于等于今天，合同有效
        if (maxValidDate != null && DateUtil.compare(maxValidDate, new Date()) >= 0) {
            return true;
        }

        return false;*/
    }


    private void handlingCharges(Invoice invoice, String handlingChargesType, BigDecimal handlingCharges) {
        if (StringUtils.isNotEmpty(handlingChargesType) && handlingCharges != null) {
            BigDecimal feeAmount = null;

            if ("0".equals(handlingChargesType)) {
                //按件
                Double numCount = invoice.getNumCount();
                if (numCount != null) {
                    feeAmount = NumberUtil.round(NumberUtil.mul(new BigDecimal(numCount), handlingCharges), 2);
                }
            } else if ("1".equals(handlingChargesType)) {
                //按方
                Double volumeCount = invoice.getVolumeCount();
                if (volumeCount != null) {
                    feeAmount = NumberUtil.round(NumberUtil.mul(new BigDecimal(volumeCount), handlingCharges), 2);
                }
            } else if ("2".equals(handlingChargesType)) {
                //按吨
                Double weightCount = invoice.getWeightCount();
                if (weightCount != null) {
                    feeAmount = NumberUtil.round(NumberUtil.mul(new BigDecimal(weightCount), handlingCharges), 2);
                }
            }

            if (feeAmount != null && feeAmount.compareTo(BigDecimal.ZERO) > 0) {
                OtherFee otherFee = new OtherFee();
                //发货单id
                otherFee.setLotId(invoice.getInvoiceId());
                //费用类型 1=装卸费
                otherFee.setFeeType("1");
                //付款方式 42=现金支付
                otherFee.setPayMethod(42);
                //金额
                otherFee.setFeeAmount(feeAmount);
                //单价
                otherFee.setPrice(handlingCharges);
                //付款类型 0：现金
                otherFee.setPayType(0);
                otherFee.setOtherFeeId(IdUtil.simpleUUID());
                String seq = StrUtil.fillBefore(otherFeeMapper.getSeq() + "", '0', 4);
                otherFee.setVbillno("DSF" + DateUtil.format(new Date(), "yyyyMMdd") + seq);
                //0-新建
                otherFee.setVbillstatus(OtherFeeStatusEnum.NEW.getValue());
                otherFee.setLotId(invoice.getInvoiceId());
                otherFee.setLotno(invoice.getVbillno());
                otherFee.setCorScrId(pageId);
                otherFee.setRegScrId(pageId);
                otherFeeService.insertOtherFeeAndAdjustRecord(otherFee);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult insertInvoiceMany(InvoiceAddVO invoice, Integer invoiceCt, int isFleet) {
        for (int i = 0; i < invoiceCt; i++) {
            InvoiceAddVO inv = new InvoiceAddVO();
            BeanUtils.copyBeanProp(inv, invoice);
            AjaxResult ajaxResult = this.insertInvoice(inv, isFleet);
            if (ajaxResult.getCode() == 500) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return ajaxResult;
            }
        }
        return AjaxResult.success();
    }

    /**
     * 获取发货单指导价
     *
     * @param invoice
     * @return
     */
    private BigDecimal getInvoiceGuidingPrice(Invoice invoice) {
        CalculateGuidePriceDTO calculateGuidePrice = new CalculateGuidePriceDTO();
        calculateGuidePrice.setCustomerId(invoice.getCustomerId());
        calculateGuidePrice.setStartProvinceId(invoice.getDeliProvinceId());
        calculateGuidePrice.setStartCityId(invoice.getDeliCityId());
        calculateGuidePrice.setStartAreaId(invoice.getDeliAreaId());
        calculateGuidePrice.setEndProvinceId(invoice.getArriProvinceId());
        calculateGuidePrice.setEndCityId(invoice.getArriCityId());
        calculateGuidePrice.setEndAreaId(invoice.getArriAreaId());
//        calculateGuidePrice.setNumCount(BigDecimal.ZERO);
//        calculateGuidePrice.setVolumeCount(BigDecimal.ZERO);
//        calculateGuidePrice.setWeightCount(BigDecimal.ZERO);
//        calculateGuidePrice.setFullVehicleFlag(0);
//        if (FULL_VEHICLE_MAP.containsKey(invoice.getTransCode())) {
        //整车，只有整车才需要车长车型
        calculateGuidePrice.setFullVehicleFlag(1);
        calculateGuidePrice.setCarType(invoice.getCarType());
        calculateGuidePrice.setCarLen(invoice.getCarLen());

        calculateGuidePrice.setTransType(invoice.getTransCode());
//        } else {
//            calculateGuidePrice.setNumCount(new BigDecimal(invoice.getNumCount()));
//            calculateGuidePrice.setWeightCount(new BigDecimal(invoice.getWeightCount()));
//            calculateGuidePrice.setVolumeCount(new BigDecimal(invoice.getVolumeCount()));
//        }
        return custGuidePriceService.calculateGuidePrice(calculateGuidePrice);
    }

    /**
     * 修改发货单
     *
     * @param editVO  发货单信息
     * @param isFleet 添加的类型  0：业务下单   1：车队下单
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateInvoice(InvoiceEditVO editVO, int isFleet) {
        Invoice invoice = new Invoice();
        BeanUtils.copyProperties(editVO, invoice);

        Invoice invoiceOld = invoiceMapper.selectByPrimaryKey(editVO.getInvoiceId());
        invoice.setVbillno(invoiceOld.getVbillno());

        //查询客户信息
        ClientPopupVO clientPopupVO = clientMapper.selectClientById(invoice.getCustomerId());
//        Integer crtGuidePrice = clientPopupVO.getCrtGuidePrice();

//        if (FULL_VEHICLE_MAP.containsKey(invoice.getTransCode())
//                && invoice.getIsMultiple() == 0 && crtGuidePrice == 1
//                && (invoice.getGuidingPrice() == null || invoice.getGuidingPrice().compareTo(BigDecimal.ZERO) == 0)) {
//            return AjaxResult.error("请填入非零的指导价！");
//        }

        if (invoice.getIfBargain() != null && invoice.getIfBargain() == 0
                && !checkCustomerContractValid(clientPopupVO.getCustomerId())) {
            return AjaxResult.error("客户合同暂无或已失效，无法下单。");
        }

        if (StringUtils.isEmpty(editVO.getRfqEnquiryLineId())
                && clientPopupVO.getContractPriceType() != null && clientPopupVO.getContractPriceType() == 0
                && editVO.getIfBargain() != 0) {
            return AjaxResult.error("该客户只可以选择合同价下单。");
        }

        if (StringUtils.isEmpty(editVO.getRfqEnquiryLineId())
                && isFleet == 0 && clientPopupVO.getIfBargain() == 0
                && editVO.getIfBargain() == 1 && StringUtils.isEmpty(editVO.getBargainMemo())) {
            return AjaxResult.error("议价类型需填写议价说明。");
        }

        if (StringUtils.isEmpty(clientPopupVO.getOperateCorp())) {
            return AjaxResult.error("客户缺少结算公司。");
        }
        invoice.setOperateCorp(clientPopupVO.getOperateCorp());



        //多装多卸 地址信息
        List<MultipleShippingAddress> shippingAddressList = invoice.getShippingAddressList();
        //过滤空数据
        shippingAddressList.removeIf(x -> x.getShippingGoodsList() == null || x.getShippingGoodsList().isEmpty());

        /*
         * 判断货品是否相等
         */
        Map<String, List<MultipleShippingGoods>> deliGoodsMap = shippingAddressList.stream()
                .filter(x -> x.getAddressType() == 0)
                .flatMap(a -> a.getShippingGoodsList().stream())
                .filter(x -> StringUtils.isNotEmpty(x.getGoodsName()))
                .collect(Collectors.groupingBy(g -> g.getGoodsName() + "_" + g.getCustOrderno()));

        Map<String, String> deliGoodsCtMap = deliGoodsMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            MultipleShippingGoods reduce = entry.getValue().stream()
                                    .reduce(new MultipleShippingGoods(), (sum, goods) -> {
                                        sum.setNum(NumberUtil.add(sum.getNum(), goods.getNum()));
                                        sum.setWeight(NumberUtil.add(sum.getWeight(), goods.getWeight()));
                                        sum.setVolume(NumberUtil.add(sum.getVolume(), goods.getVolume()));
                                        return sum;
                                    });
                            return Convert.toStr(reduce.getNum(), "-") + "_"
                                    + Convert.toStr(reduce.getWeight(), "-") + "_"
                                    + Convert.toStr(reduce.getVolume(), "-");
                        }
                ));

        Map<String, List<MultipleShippingGoods>> arriGoodsMap = shippingAddressList.stream()
                .filter(x -> x.getAddressType() == 1)
                .flatMap(a -> a.getShippingGoodsList().stream())
                .filter(x -> StringUtils.isNotEmpty(x.getGoodsName()))
                .collect(Collectors.groupingBy(g -> g.getGoodsName() + "_" + g.getCustOrderno()));

        Map<String, String> arriGoodsCtMap = arriGoodsMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            MultipleShippingGoods reduce = entry.getValue().stream()
                                    .reduce(new MultipleShippingGoods(), (sum, goods) -> {
                                        sum.setNum(NumberUtil.add(sum.getNum(), goods.getNum()));
                                        sum.setWeight(NumberUtil.add(sum.getWeight(), goods.getWeight()));
                                        sum.setVolume(NumberUtil.add(sum.getVolume(), goods.getVolume()));
                                        return sum;
                                    });
                            return Convert.toStr(reduce.getNum(), "-") + "_"
                                    + Convert.toStr(reduce.getWeight(), "-") + "_"
                                    + Convert.toStr(reduce.getVolume(), "-");
                        }
                ));

        if (!arriGoodsCtMap.equals(deliGoodsCtMap)) {
            return AjaxResult.error("提货和到货相关的货物信息（含客户单号）需要保持一致。");
        }


        //货品信息  去提货地址中 对应的货品信息
        List<InvPackGoods> goodsList = new ArrayList<>();
        invoice.setIsChangeAddress(0);
        for (MultipleShippingAddress address : shippingAddressList) {
            String provinceName = address.getProvinceName();
            String provinceId = address.getProvinceId();
            //
            String cityName = address.getCityName();
            String cityId = address.getCityId();
            //
            String areaName = address.getAreaName();
            String areaId = address.getAreaId();

            //省份
            String provinceCode = provinceMapper.getProvinceCode(provinceName);
            //
            String cityCode = provinceMapper.getCityCode(provinceId, cityName);
            //
            String areaCode = provinceMapper.getAreaCode(cityId, areaName);

            if (provinceCode == null || !provinceCode.equals(provinceId)) {
                return AjaxResult.error("收发货地址省份不存在");
            }

            if (cityCode == null || !cityCode.equals(cityId)) {
                return AjaxResult.error("收发货地址城市不存在");
            }

            if (areaCode == null || !areaCode.equals(areaId)) {
                return AjaxResult.error("收发货地址区域不存在");
            }

            if(address.getIsChangeAddress() != null && address.getIsChangeAddress() == 1){
                invoice.setIsChangeAddress(1);
            }

            //地址id
            String addressId = IdUtil.simpleUUID();
            address.setMultipleShippingAddressId(addressId);

            //地址对应的货品信息
            List<MultipleShippingGoods> shippingGoodsList = address.getShippingGoodsList();
            shippingGoodsList.removeIf(x -> StringUtils.isEmpty(x.getGoodsName()));

            //件数、重量、体积合计
            double numSum = shippingGoodsList.stream().filter(x -> x.getNum() != null).mapToDouble(MultipleShippingGoods::getNum).sum();
            double volumeSum = shippingGoodsList.stream().filter(x -> x.getVolume() != null).mapToDouble(MultipleShippingGoods::getVolume).sum();
            double weightSum = shippingGoodsList.stream().filter(x -> x.getWeight() != null).mapToDouble(MultipleShippingGoods::getWeight).sum();
            String goodsName = shippingGoodsList.stream().map(MultipleShippingGoods::getGoodsName).distinct().collect(Collectors.joining(","));
            address.setNumCount(numSum);
            address.setVolumeCount(volumeSum);
            address.setWeightCount(weightSum);
            address.setGoodsName(goodsName);

            //关联地址id
            shippingGoodsList.forEach(x -> {
                String goodsId = IdUtil.simpleUUID();
                x.setMultipleShippingAddressId(addressId);
                x.setMultipleShippingGoodsId(goodsId);
            });
            address.setShippingGoodsList(shippingGoodsList);

            /*
             * 将发货对应的货品 存入InvPackGoods 中
             */
            if (address.getAddressType() == 0) {
                for (MultipleShippingGoods multipleShippingGoods : shippingGoodsList) {
                    InvPackGoods invPackGoods = new InvPackGoods();
                    BeanUtils.copyBeanProp(invPackGoods, multipleShippingGoods);
                    goodsList.add(invPackGoods);
                }
            }
        }
        invoice.setInvPackGoodsList(goodsList);

        //客户单号
        String custOrdernos = goodsList.stream()
                .map(InvPackGoods::getCustOrderno)
                .distinct()
                .collect(Collectors.joining(","));
        invoice.setCustOrderno(custOrdernos);

        //货品
        String goodsName = goodsList.stream()
                .map(InvPackGoods::getGoodsName)
                .distinct()
                .collect(Collectors.joining(","));
        invoice.setGoodsName(goodsName);


        //取提货类型的第一条 作为发货单里的发货信息
        MultipleShippingAddress deliAddress = shippingAddressList.stream()
                .filter(x -> x.getAddressType() == 0).limit(1).findFirst().orElse(new MultipleShippingAddress());

        //提货地址
//        invoice.setDeliveryId(deliAddress.getDeliveryId());
//        invoice.setDeliAddrCode(deliAddress.getAddrCode());
        invoice.setDeliAddrName(deliAddress.getAddrName());
        //提货(省)
        invoice.setDeliProvinceId(deliAddress.getProvinceId());
        invoice.setDeliProName(deliAddress.getProvinceName());
        //提货（市）
        invoice.setDeliCityId(deliAddress.getCityId());
        invoice.setDeliCityName(deliAddress.getCityName());
        //提货(区)
        invoice.setDeliAreaId(deliAddress.getAreaId());
        invoice.setDeliAreaName(deliAddress.getAreaName());
        //提货详细地址
        invoice.setDeliDetailAddr(deliAddress.getDetailAddr());
        //提货联系人
        invoice.setDeliContact(deliAddress.getContact());
        //联系人手机
        invoice.setDeliMobile(deliAddress.getMobile());

        //取到货类型的第一条 作为发货单里的到货信息
        MultipleShippingAddress arriAddress = shippingAddressList.stream()
                .filter(x -> x.getAddressType() == 1).limit(1).findFirst().orElse(new MultipleShippingAddress());
        //到货地址
//        invoice.setArrivalId(arriAddress.getDeliveryId());
//        invoice.setArriAddrCode(arriAddress.getAddrCode());
        invoice.setArriAddrName(arriAddress.getAddrName());
        //到货(省)
        invoice.setArriProvinceId(arriAddress.getProvinceId());
        invoice.setArriProName(arriAddress.getProvinceName());
        //到货地址（市）
        invoice.setArriCityId(arriAddress.getCityId());
        invoice.setArriCityName(arriAddress.getCityName());
        //到货地址（区）
        invoice.setArriAreaId(arriAddress.getAreaId());
        invoice.setArriAreaName(arriAddress.getAreaName());
        //到货地址详细地址
        invoice.setArriDetailAddr(arriAddress.getDetailAddr());
        //到货联系人
        invoice.setArriContact(arriAddress.getContact());
        //到货联系人手机
        invoice.setArriMobile(arriAddress.getMobile());

        //不为多装多卸时  设置指导价
//        if (invoice.getIsMultiple() == 0) {
//            //置空
//            invoice.setGuidingPrice(null);
//            //线路、新增指导价
//            generateTransLineAndGuide(invoice.getDeliProvinceId(), invoice.getDeliProName(), invoice.getDeliCityId()
//                    , invoice.getDeliCityName(), invoice.getDeliAreaId(), invoice.getDeliAreaName()
//                    , invoice.getArriProvinceId(), invoice.getArriProName(), invoice.getArriCityId()
//                    , invoice.getArriCityName(), invoice.getArriAreaId(), invoice.getArriAreaName(), invoice.getTransCode()
//                    , invoice.getCarLen(), invoice.getCarLenName(), invoice.getCarType(), invoice.getCarTypeName(), true
//                    , editVO.getCostPrice(), "invoiceService.updateInvoice");
//            //指导价浮动比率
//            invoice.setReferenceRate(clientPopupVO.getReferenceRate());
//
//        }

        invoice.setUnloadPlaceNum((int) shippingAddressList.stream().filter(x -> x.getAddressType() == 1).count());
        invoice.setLoadPlaceNum((int) shippingAddressList.stream().filter(x -> x.getAddressType() == 0).count());

        //是否往返
        if (invoice.getIsRoundTrip() == null) {
            invoice.setIsRoundTrip(0);
        }
        if ("4".equals(invoice.getBillingMethod())) {
            invoice.setIsRoundTrip(1);
        }

        //判断要求提货日期是否关账
        String yearMonth = DateFormatUtils.format(invoice.getReqDeliDate(), "yyyy-MM");
        Integer cnt = invoiceMapper.selectInvoiceCloseYearMonth(yearMonth);
        if (cnt != null && cnt > 0) {
            return AjaxResult.error("要求提货日期的月份已关账，无法操作！");
        }
        //验证客户下是否存在已经关账，但是还未加入对账单的应收明细
        /*Integer cnt = invoiceMapper.selectReceiveDetailByCustomerAndIsClose(invoice.getCustomerId());
        if(cnt != null && cnt > 0){
            return AjaxResult.error("该客户发货单下存在"+cnt+"条已关账但未对账的应收明细，请先处理！");
        }
        //判断客户下是否存在没确认的应收对账单
        Integer checkSheetCnt = invoiceMapper.selectReceiveCheckSheetByCustomerAndIsClose(invoice.getCustomerId());
        if(checkSheetCnt != null && checkSheetCnt > 0){
            return AjaxResult.error("该客户下存在"+checkSheetCnt+"条未确认的应收对账单，请先处理！");
        }*/

        Invoice invoice1All = invoiceMapper.selectInvoiceById(invoice.getInvoiceId());


        if (invoice1All == null) {
            return AjaxResult.error("该数据不存在，请刷新后重试！");
        }
        //校验
        if (!InvoiceStatusEnum.NEW.getValue().equals(invoice1All.getVbillstatus())) {
            return AjaxResult.error("只有新建状态下的发货单才能修改！");
        }
        if (invoice1All.getIsClose() == 1) {
            return AjaxResult.error("该发货单已关账，无法修改！");
        }
        if (!invoice.getCorDate().equals(invoice1All.getCorDate())) {
            return AjaxResult.error("该发货单已被他人修改，请刷新查看最新数据！");
        }
        //判断是否是车队数据 并且数据为分配过来的
        if ("1".equals(invoice1All.getIsFleetData()) && "1".equals(invoice1All.getIsFleetAssign())) {
            return AjaxResult.error("分配给车队的发货单无法修改！");
        }

        SysDictData billingTypeDictData = sysDictDataMapper
                .selectDictDataByTypeAndValue("billing_type", invoice.getBillingType());
        if (billingTypeDictData == null) {
            return AjaxResult.error("请选择正确的开票类型！");
        }

        //合同价计算
        if (invoice.getIfBargain() != null && invoice.getIfBargain() == 0) {
            //查询合同价
            SearchContractPriceVO priceVO = new SearchContractPriceVO();
            List<String> deliAreaIdList = shippingAddressList.stream()
                    .filter(x -> x.getAddressType() == 0
                            && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                    .map(MultipleShippingAddress::getAreaId)
                    .collect(Collectors.toList());
            priceVO.setDeliAreaIdList(deliAreaIdList);

            List<String> deliAddrNameList = shippingAddressList.stream()
                    .filter(x -> x.getAddressType() == 0
                            && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                    .map(MultipleShippingAddress::getAddrName)
                    .collect(Collectors.toList());
            priceVO.setDeliAddrNameList(deliAddrNameList);


           /* Integer isChangeAddress = invoice.getIsChangeAddress();
            if (isChangeAddress != null && isChangeAddress == 1) {
                List<String> arriAreaIdList = new ArrayList<>();
                arriAreaIdList.add(invoice.getCaArriAreaId());
                priceVO.setArriAreaIdList(arriAreaIdList);
            } else {
                List<String> arriAreaIdList = shippingAddressList.stream()
                        .filter(x -> x.getAddressType() == 1
                                && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                        .map(MultipleShippingAddress::getAreaId)
                        .collect(Collectors.toList());

                priceVO.setArriAreaIdList(arriAreaIdList);
            }*/

            List<String> arriAreaIdList = new ArrayList<>();
            List<String> arriArriNameList = new ArrayList<>();

            for(MultipleShippingAddress multipleShippingAddress : shippingAddressList){
                if(multipleShippingAddress.getAddressType() == 1
                        && multipleShippingAddress.getIsGetContractPrice() != null
                        && multipleShippingAddress.getIsGetContractPrice() == 1){
                    if(multipleShippingAddress.getIsChangeAddress() != null && multipleShippingAddress.getIsChangeAddress() == 1){
                        arriAreaIdList.add(multipleShippingAddress.getCaArriAreaId());
                        arriArriNameList.add(multipleShippingAddress.getCaArriAddrName());

                    }else{
                        arriAreaIdList.add(multipleShippingAddress.getAreaId());
                        arriArriNameList.add(multipleShippingAddress.getAddrName());

                    }
                }
            }
            priceVO.setArriAreaIdList(arriAreaIdList);
            priceVO.setArriAddrNameList(arriArriNameList);

            priceVO.setCustomerId(invoice.getCustomerId());

            priceVO.setBillingMethod(Integer.valueOf(invoice.getBillingMethod()));

            List<String> goodsNameList = goodsList.stream()
                    .map(InvPackGoods::getGoodsName)
                    .distinct()
                    .collect(Collectors.toList());
            if (goodsNameList.size() == 1) {
                priceVO.setGoodsName(goodsNameList.get(0));
            }

            //货品特性
//            String goodsCharacter = "15".equals(invoice.getTransCode()) || "16".equals(invoice.getTransCode()) ? "1" : "0";
            String goodsCharacter;
            if ("15".equals(invoice.getTransCode()) || "16".equals(invoice.getTransCode())) {
                goodsCharacter = "1";
            } else if ("4".equals(invoice.getTransCode())) {
                goodsCharacter = "2";
            }else {
                goodsCharacter = "0";

            }

            priceVO.setGoodsCharacter(goodsCharacter);
            //车长
            priceVO.setCarLen(invoice.getCarLen());
            priceVO.setCarType(invoice.getCarType());

            priceVO.setNum(Convert.toBigDecimal(invoice.getNumCount(),BigDecimal.ZERO));
            priceVO.setWeight(Convert.toBigDecimal(invoice.getWeightCount(),BigDecimal.ZERO));
            priceVO.setVolume(Convert.toBigDecimal(invoice.getVolumeCount(),BigDecimal.ZERO));
            priceVO.setMileage(invoice.getMileage() == null ? BigDecimal.ZERO : BigDecimal.valueOf(invoice.getMileage()));

            priceVO.setIsOversize(invoice.getIsOversize());
            priceVO.setTransCode(invoice.getTransCode());

            priceVO.setIsRoundTrip(invoice.getIsRoundTrip());

            Map<String, String> priceMap = getPrice(priceVO);
            if ("0".equals(priceMap.get("type"))) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return AjaxResult.error("未查询到合同价。");
            }

            if ("".equals(priceMap.get("costPrice"))) {
                return AjaxResult.error("未查询到成本价。");
            }

            //税率
            BigDecimal rate = billingTypeDictData.getNumVal1();

            //是否含税  0不含税  1含税*
            String isIncludeTax = priceMap.get("isIncludeTax");

            //总评价
            BigDecimal totalPrice = Convert.toBigDecimal(priceMap.get("totalPrice"));
            //单价
            BigDecimal price = Convert.toBigDecimal(priceMap.get("price"));
            //成本价
            BigDecimal totalCostPrice = Convert.toBigDecimal(priceMap.get("totalCostPrice"));
            String costBillingType = priceMap.get("costBillingType");

            Integer isFixedPrice = Convert.toInt(priceMap.get("isFixedPrice"), null);

            String contractpcVersionId = priceMap.get("versionId");

            invoice.setContractIsFixedPrice(isFixedPrice);

            //
            invoice.setContractPrice(price);
            //成本价
            invoice.setCostPrice(totalCostPrice);
            invoice.setCostBillingType(totalCostPrice == null ? null : costBillingType);

            invoice.setContractPriceTotal(totalPrice);
            //合同价版本id
            invoice.setContractpcVersionId(contractpcVersionId);

            //是否含税  0不含税  1含税*
            if ("0".equals(isIncludeTax)) {
                //不含税
                invoice.setCostAmountIncludeTax(totalPrice);
                invoice.setUnitPriceIncludeTax(price);

                if (rate != null && rate.compareTo(BigDecimal.ZERO) != 0) {
                    invoice.setCostAmount(NumberUtil.round(NumberUtil.mul(totalPrice, rate), 2));
                    invoice.setUnitPrice(NumberUtil.round(NumberUtil.mul(price, rate), 2));
                }else {
                    invoice.setCostAmount(totalPrice);
                    invoice.setUnitPrice(price);
                }
            }else {
                //含税
                invoice.setCostAmount(totalPrice);
                invoice.setUnitPrice(price);
                if (rate != null && rate.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal costAmountIncludeTax = NumberUtil.div(invoice.getCostAmount(), rate, 2);
                    invoice.setCostAmountIncludeTax(costAmountIncludeTax);

                    BigDecimal unitPriceIncludeTax = NumberUtil.div(invoice.getUnitPrice(), rate, 2);
                    invoice.setUnitPriceIncludeTax(unitPriceIncludeTax);

                }else {
                    invoice.setCostAmountIncludeTax(totalPrice);
                    invoice.setUnitPriceIncludeTax(price);
                }
            }

            //送货费
            invoice.setDeliveryFee(Convert.toBigDecimal(priceMap.get("deliveryFee")));

            //置空议价说明
            invoice.setBargainMemo(null);
        }

        //运营部
        String salesDeptId = clientPopupVO.getSalesDept();
        SysDept custSales = sysDeptMapper.getParentDeptInfo(salesDeptId);
        invoice.setCustSalesId(String.valueOf(custSales.getDeptId()));
        invoice.setCustSalesName(custSales.getDeptName());
        //管理部
        SysDept mgmtDept = sysDeptMapper.getParentDeptInfo(String.valueOf(custSales.getDeptId()));
        invoice.setMgmtDeptId(String.valueOf(mgmtDept.getDeptId()));
        invoice.setMgmtDeptName(mgmtDept.getDeptName());


//        //客户运营部
//        String salesId = clientPopupVO.getSalesId();
//        if (StringUtils.isNotEmpty(salesId)) {
//            MSalesGroup mSalesGroup = salesGroupMapper.selectByPrimaryKey(salesId);
//
//            invoice.setCustSalesId(salesId);
//            invoice.setCustSalesName(mSalesGroup.getSalesName());
//        }

        //三超类型
        if (editVO.getIsOversize() == 1) {
            if ((editVO.getGoodsLength() != null && editVO.getGoodsLength().compareTo(BigDecimal.ZERO) != 0)
                    || (editVO.getGoodsWidth() != null && editVO.getGoodsWidth().compareTo(BigDecimal.ZERO) != 0)
                    || (editVO.getGoodsHeight() != null && editVO.getGoodsHeight().compareTo(BigDecimal.ZERO) != 0) ) {
                //0超体积
                invoice.setOversizeType(0);
            }else {
                //1超重
                invoice.setOversizeType(1);
            }
        }

        //设置是否为 车队数据  0是业务下单数据  1是车队下单数据
        if (isFleet == 0) {
            invoice.setIsFleetData("0");
        } else {
            invoice.setIsFleetData("1");
        }
        invoice.setIsFleetAssign("0");
        //是否开票
        invoice.setIfBilling("6".equals(invoice.getBillingType()) ? "0" : "1");

        //计算总金额
        invoice.setTotalFee(NumberUtil.add(invoice.getCostAmount(), invoice.getOtherFee(), invoice.getDeliveryFee()));
        //在途费用
        invoice.setOnWayAmountFee(NumberUtil.add(invoice.getOtherFee(), invoice.getDeliveryFee()));

        //更新发货单
        invoiceMapper.updateInvoiceWithNull(invoice);

        //驻场人员ids
        String residentsId = invoice.getResidentsId();
        /*
         * 删除驻场人员与发货单关联表
         */
        if (StringUtils.isNotBlank(residentsId)) {
            ResidentsInvoice residentsInvoice = new ResidentsInvoice();
            residentsInvoice.setInvoiceId(invoice1All.getInvoiceId());
            residentsInvoice.setCorScrId(invoice.getCorScrId());
            residentsInvoice.setDelUserid(shiroUtils.getUserId().toString());
            residentsInvoiceMapper.deleteResidentsInvoiceByInvoiceId(residentsInvoice);
        }

        /*
         * 插入驻场人员与发货单关联表
         */
        String[] residentsIds = com.ruoyi.common.core.text.Convert.toStrArray(residentsId);
        for (String id : residentsIds) {
            if (StringUtils.isNotBlank(id)) {
                SysUser sysUser = sysUserMapper.selectUserById(Long.valueOf(id));
                ResidentsInvoice ri = new ResidentsInvoice();
                ri.setResidentsInvoiceId(IdUtil.simpleUUID());
                //发货单id
                ri.setInvoiceId(invoice.getInvoiceId());
                //驻场人员id
                ri.setResidentsId(id);
                //驻场人员姓名
                ri.setResidentsName(sysUser.getUserName());
                //画面id
                ri.setRegScrId(invoice.getCorScrId());
                ri.setCorScrId(invoice.getCorScrId());
                residentsInvoiceMapper.insertResidentsInvoice(ri);
            }
        }

        //删除老的货品明细数据
        InvPackGoods invPackGoods = new InvPackGoods();
        invPackGoods.setInvoiceId(invoice.getInvoiceId());
        invPackGoods.setDelFlag(1);
        invPackGoods.setDelDate(new Date());
        invPackGoods.setCorScrId(invoice.getCorScrId());
        invPackGoodsMapper.updateInvPackGoodsByInvoiceId(invPackGoods);

        //删除 t_multiple_shipping_address和t_multiple_shipping_goods
        List<MultipleShippingAddress> multipleShippingAddresses = multipleShippingAddressMapper.selectListByInvoiceId(invoice.getInvoiceId());
        for (MultipleShippingAddress multipleShippingAddress : multipleShippingAddresses) {
            //删除地址
            MultipleShippingAddress delAddr = new MultipleShippingAddress();
            delAddr.setMultipleShippingAddressId(multipleShippingAddress.getMultipleShippingAddressId());
            delAddr.setDelFlag(1);
            delAddr.setDelDate(new Date());
            delAddr.setCorScrId(pageId);
            multipleShippingAddressMapper.updateByPrimaryKeySelective(delAddr);

            //删除地址对应的货品
            MultipleShippingGoods delGoods = new MultipleShippingGoods();
            delGoods.setMultipleShippingAddressId(multipleShippingAddress.getMultipleShippingAddressId());
            delGoods.setDelFlag(1);
            delGoods.setDelDate(new Date());
            delGoods.setCorScrId(pageId);
            multipleShippingGoodsMapper.updateByAddressIdSelective(delGoods);
        }

        //插入新的货品明细数据
        List<InvPackGoods> invPackGoodsList = invoice.getInvPackGoodsList();
        for (InvPackGoods packGoods : invPackGoodsList) {
            //重量必须>0
            if (packGoods.getWeight() == null) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return AjaxResult.error("货品重量必须大于0！");
            }
            if (packGoods.getWeight() == 0) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return AjaxResult.error("货品重量必须大于0！");
            }
            //发货单货品id
            packGoods.setInvPackGoodsId(IdUtil.simpleUUID());
            //发货单货品 删除标记 0 未删除
            packGoods.setDelFlag(0);
            //发货单 id
            packGoods.setInvoiceId(invoice.getInvoiceId());
            packGoods.setRegScrId(invoice.getCorScrId());
            packGoods.setCorScrId(invoice.getCorScrId());
            invPackGoodsMapper.insertInvPackGoods(packGoods);

        }

        /*
         * 运输方式为多装多卸时   保存对应的 t_multiple_shipping_address和t_multiple_shipping_goods
         */
        for (MultipleShippingAddress multipleShippingAddress : shippingAddressList) {
            multipleShippingAddress.setInvoiceId(invoice.getInvoiceId());
            multipleShippingAddress.setInvoiceNo(invoice.getVbillno());
            multipleShippingAddress.setIsFleetData(invoice.getIsFleetData());
            multipleShippingAddress.setIsFleetAssign(invoice.getIsFleetAssign());
            multipleShippingAddress.setRegScrId(pageId);
            multipleShippingAddressMapper.insertSelective(multipleShippingAddress);
            //插入货品
            for (MultipleShippingGoods multipleShippingGoods : multipleShippingAddress.getShippingGoodsList()) {
                multipleShippingGoods.setRegScrId(pageId);
                multipleShippingGoodsMapper.insertSelective(multipleShippingGoods);
            }
        }

        ClientPopupVO clientPopup = clientMapper.selectClientById(invoice.getCustomerId());

        /*
         * 修改第三方费用 装卸费
         */
        //单价
/*
        BigDecimal handlingCharges = clientPopup.getHandlingCharges();
        if (StringUtils.isNotEmpty(clientPopup.getHandlingChargesType()) && handlingCharges != null) {
            //查询现有的装卸费数据
            OtherFee otherFeeSele = new OtherFee();
            otherFeeSele.setLotId(invoice.getInvoiceId());
            otherFeeSele.setFeeType("1");
            List<OtherFee> otherFeeList = otherFeeService.selectOtherFeeList(otherFeeSele);
            BigDecimal feeTypeOldAll = otherFeeList.stream()
                    .map(OtherFee::getFeeAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            //计算新的装卸费
            BigDecimal feeAmount = null;

            if ("0".equals(clientPopup.getHandlingChargesType())) {
                //按件
                Double numCount = invoice.getNumCount();
                if (numCount != null) {
                    feeAmount = NumberUtil.round(NumberUtil.mul(new BigDecimal(numCount), handlingCharges), 2);
                }
            } else if ("1".equals(clientPopup.getHandlingChargesType())) {
                //按方
                Double volumeCount = invoice.getVolumeCount();
                if (volumeCount != null) {
                    feeAmount = NumberUtil.round(NumberUtil.mul(new BigDecimal(volumeCount), handlingCharges), 2);
                }
            } else if ("2".equals(clientPopup.getHandlingChargesType())) {
                //按吨
                Double weightCount = invoice.getWeightCount();
                if (weightCount != null) {
                    feeAmount = NumberUtil.round(NumberUtil.mul(new BigDecimal(weightCount), handlingCharges), 2);
                }
            }

            if (feeAmount != null && feeAmount.compareTo(feeTypeOldAll) != 0) {

                BigDecimal sub = NumberUtil.sub(feeAmount, feeTypeOldAll);

                OtherFee otherFee = new OtherFee();
                //发货单id
                otherFee.setLotId(invoice.getInvoiceId());
                //费用类型 1=装卸费
                otherFee.setFeeType("1");
                //付款方式 42=现金支付
                otherFee.setPayMethod(42);
                //金额
                otherFee.setFeeAmount(sub);
                //付款类型 0：现金
                otherFee.setPayType(0);
                otherFee.setOtherFeeId(IdUtil.simpleUUID());
                String seq = StrUtil.fillBefore(otherFeeMapper.getSeq() + "", '0', 4);
                otherFee.setVbillno("DSF" + DateUtil.format(new Date(), "yyyyMMdd") + seq);
                //0-新建
                otherFee.setVbillstatus(OtherFeeStatusEnum.NEW.getValue());
                otherFee.setLotId(invoice.getInvoiceId());
                otherFee.setLotno(invoice.getVbillno());
                otherFee.setCorScrId(pageId);
                otherFee.setRegScrId(pageId);
                otherFeeService.insertOtherFeeAndAdjustRecord(otherFee);
            }


        }
*/

        // 0普通 1紧急
        if ("1".equals(invoice.getUrgentLevel())) {
            //消息推送tms移动端
            try {
                sendTmsMobile(invoice);
            } catch (Exception e) {
                logger.error("编辑发货单消息推送异常：{}", e.getMessage());
                e.printStackTrace();
            }
        }
        return AjaxResult.success();
    }

    /**
     * 删除发货单对象
     *
     * @param ids      需要删除的数据ID
     * @param corScrId
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteInvoiceByIds(String ids, String corScrId) {
        String[] idList = com.ruoyi.common.core.text.Convert.toStrArray(ids);
        //根据ids查询所有发货单
        List<Invoice> invoiceList = invoiceMapper.getInvoiceListByIds(idList);

        //校验是否为空
        if (invoiceList.size() == 0) {
            return AjaxResult.error("该数据不存在，请刷新后重试！");
        }

        //校验是否存在新建状态的发货单
        long newCount = invoiceList.stream().filter(x -> !InvoiceStatusEnum.NEW.getValue().equals(x.getVbillstatus())).count();
        if (newCount > 0) {
            return AjaxResult.error("存在非新建状态下的发货单，无法删除！");
        }
        //校验是否存在已关账的发货单 20220224 关账可以删除
        /*long closeCount = invoiceList.stream().filter(x -> x.getIsClose() == 1).count();
        if (closeCount > 0) {
            return AjaxResult.error("存在已关账的发货单，无法删除！");
        }*/

        /*
         * 删除 发货单下 所有 货品明细信息，逻辑删除
         */
        InvPackGoods invPackGoods = new InvPackGoods();
        invPackGoods.setDelFlag(1);
        invPackGoods.setDelDate(new Date());
        for (String id : idList) {
            invPackGoods.setInvoiceId(id);
            invPackGoods.setCorScrId(corScrId);
            invPackGoodsMapper.updateInvPackGoodsByInvoiceId(invPackGoods);
        }

        for (Invoice invoice : invoiceList) {
            //删除 t_multiple_shipping_address和t_multiple_shipping_goods
            List<MultipleShippingAddress> multipleShippingAddresses = multipleShippingAddressMapper.selectListByInvoiceId(invoice.getInvoiceId());
            for (MultipleShippingAddress multipleShippingAddress : multipleShippingAddresses) {
                //删除地址
                MultipleShippingAddress delAddr = new MultipleShippingAddress();
                delAddr.setMultipleShippingAddressId(multipleShippingAddress.getMultipleShippingAddressId());
                delAddr.setDelFlag(1);
                delAddr.setDelDate(new Date());
                delAddr.setCorScrId(pageId);
                multipleShippingAddressMapper.updateByPrimaryKeySelective(delAddr);

                //删除地址对应的货品
                MultipleShippingGoods delGoods = new MultipleShippingGoods();
                delGoods.setMultipleShippingAddressId(multipleShippingAddress.getMultipleShippingAddressId());
                delGoods.setDelFlag(1);
                delGoods.setDelDate(new Date());
                delGoods.setCorScrId(pageId);
                multipleShippingGoodsMapper.updateByAddressIdSelective(delGoods);
            }
        }

        /*
         * 如果是车队数据 并且数据是分配车队生成的  删除时，需对应的修改业务的运段、运单、应付、发货单状态
         */
        for (Invoice invoice : invoiceList) {
            if ("1".equals(invoice.getIsFleetData()) && "1".equals(invoice.getIsFleetAssign())) {
                /*
                 * 修改运段状态
                 */
                Segment segmentUpdate = new Segment();
                segmentUpdate.setSegmentId(invoice.getBizSegmentId());
                segmentUpdate.setIsFleetAssign("0");
                segmentUpdate.setVbillstatus(SegmentStatusEnum.TO_DISPATCH.getValue());
                segmentUpdate.setCorUserId(shiroUtils.getUserId().toString());
                segmentUpdate.setCorUserName(shiroUtils.getSysUser().getUserName());
                segmentUpdate.setCorDate(new Date());

                int i = segmentMapper.updateSegmentByLock(segmentUpdate, SegmentStatusEnum.ASSIGN_FLEET.getValue(), 2, 2);
                if (i == 0) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return AjaxResult.error("该数据已被他人操作，请列表刷新后重试！");
                }

                /*
                 * 删除相关委托单
                 */
                Entrust entrustUpdate = new Entrust();
                entrustUpdate.setLotId(invoice.getBizEntrustLotId());
                entrustUpdate.setCorScrId(pageId);
                entrustUpdate.setDelDate(new Date());
                entrustUpdate.setCorUserId(shiroUtils.getUserId().toString());
                entrustUpdate.setCorUserName(shiroUtils.getSysUser().getUserName());
                entrustUpdate.setCorDate(new Date());
                int i1 = entrustMapper.deleteEntrustByLotIdLogic(entrustUpdate, null);

                if (i1 == 0) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return AjaxResult.error("该数据已删除，请刷新后重试！");
                }


                /*
                 * 删除应付明细
                 */
                PayDetail payDetailSearch = new PayDetail();
                payDetailSearch.setLotId(invoice.getBizEntrustLotId());
                payDetailSearch.setDelFlag(0);
                List<PayDetail> payDetails = payDetailMapper.selectPayDetailList(payDetailSearch);

                for (PayDetail payDetail : payDetails) {
                    //删除运单下应付
                    payDetail.setDelFlag(1);
                    payDetail.setDelUserId(shiroUtils.getUserId().toString());
                    payDetail.setDelDate(new Date());
                    payDetail.setCorScrId(pageId);
                    payDetailMapper.updatePayDetailById(payDetail);
                }

                /*
                 * 删除运单
                 */
                EntrustLot entrustLot = new EntrustLot();
                entrustLot.setEntrustLotId(invoice.getBizEntrustLotId());
                entrustLot.setDelFlag(1);
                entrustLot.setDelDate(new Date());
                entrustLot.setCorScrId(pageId);
                entrustLotMapper.updateEntrustLot(entrustLot);

                //删除委托单下成本分摊
                allocationMapper.deleteAllocationByLot(invoice.getBizEntrustLotId(), shiroUtils.getUserId().toString(), pageId);

                /*
                 * 更新发货单状态
                 */
                //修改发货单运段状态
                Segment segmentSel = new Segment();
                segmentSel.setInvoiceId(invoice.getBizInvoiceId());
                segmentSel.setSegMark(2);
                segmentSel.setDelFlag(0);
                List<Segment> segmentListInv = segmentMapper.selectSegmentList(segmentSel);
                //是否都为待调度状态
                boolean toDispatchBl = segmentListInv
                        .stream().allMatch(x -> SegmentStatusEnum.TO_DISPATCH.getValue().equals(x.getVbillstatus()));
                //是否分配车队
                boolean isFleetAssign = segmentListInv.stream().anyMatch(x -> "1".equals(x.getIsFleetAssign()));

                Invoice invoiceUpdate = new Invoice();
                invoiceUpdate.setInvoiceId(invoice.getBizInvoiceId());
                invoiceUpdate.setCorScrId(pageId);

                if (toDispatchBl) {
                    //更新发货单为待调度
                    invoiceUpdate.setSegmentStatus(InvoiceSegmentEnum.NO_DISPATCH.getValue());
                } else {
                    //更新发货单为部分调度
                    invoiceUpdate.setSegmentStatus(InvoiceSegmentEnum.DISPATCH_ING.getValue());
                }
                invoiceUpdate.setIsFleetAssign(isFleetAssign ? "1" : "0");
                invoiceMapper.updateInvoice(invoiceUpdate);
            }
        }

        //删除发货单 逻辑删除
        int i = invoiceMapper.deleteInvoiceByIdsLogic(idList, shiroUtils.getUserId().toString(), corScrId, InvoiceStatusEnum.NEW.getValue());
        if (i != idList.length) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("部分发货单状态发生改变，请刷新后重试！");
        }

        return AjaxResult.success();
    }

    /**
     * 根据 客户id、提货方省市、收货方省市、车长(包车、一日往返包车需要)、计价方式、货品特性、件数/重量/体积 获取单价
     *
     * @param
     * @return Map key: price 单价；totalPrice 总价
     */
    @Override
    public Map<String, String> getPrice(SearchContractPriceVO priceVO) {
        String logId = IdUtil.fastSimpleUUID();
        logger.info("{}获取合同价：{}", logId, priceVO.toString());

        if (StringUtils.isEmpty(priceVO.getCustomerId()) || StringUtils.isEmpty(priceVO.getTransCode())
                || priceVO.getDeliAreaIdList() == null || priceVO.getDeliAreaIdList().isEmpty()
                || priceVO.getArriAreaIdList() == null || priceVO.getArriAreaIdList().isEmpty()
                || priceVO.getDeliAddrNameList() == null || priceVO.getDeliAddrNameList().isEmpty()
                || priceVO.getArriAddrNameList() == null || priceVO.getArriAddrNameList().isEmpty()) {
            logger.info("{}获取合同价失败：缺少参数", logId);

            Map<String, String> result = new HashMap<>();
            result.put("type", "0");
            result.put("msg", "缺少参数");
            return result;
        }

        String customerId = priceVO.getCustomerId();

        String transCode = priceVO.getTransCode();

        //普通品 or 危险品 or 冷链
//        String goodsCharacter = Arrays.asList("15", "16").contains(transCode) ? "1" : "0";
        String goodsCharacter;
        if (Arrays.asList("15", "16").contains(transCode)) {
            goodsCharacter = "1";
        } else if ("4".equals(transCode)) {
            goodsCharacter = "2";
        }else {
            goodsCharacter = "0";
        }

        priceVO.setGoodsCharacter(goodsCharacter);

        Integer isFtl;
        if (Arrays.asList("0", "4", "15").contains(transCode)) {
            //整车
            isFtl = 1;
        } else if (Arrays.asList("1", "16").contains(transCode)) {
            //零担
            isFtl = 0;
        } else {
            isFtl = null;
        }

        ContractpcVersion cv;
        if (StringUtils.isEmpty(priceVO.getVersionId())) {
            cv = contractpcVersionMapper.selectCurrentVersionByCustomerId(customerId);
        }else {
            cv = contractpcVersionMapper.selectByPrimaryKey(priceVO.getVersionId());
        }

        Date currentDate = new Date();
        if (cv.getValidDate() != null && currentDate.after(cv.getValidDate())) {
            logger.info("{}获取合同价失败：版本已过期，有效期{}", logId, cv.getValidDate());

            Map<String, String> result = new HashMap<>();
            result.put("type", "0");
            result.put("msg", "版本已过期");
            return result;
        }


        //计价方式
        Integer billingMethod = priceVO.getBillingMethod();

        //校验参数是否完整
        if (billingMethod == null
                || ((CollectionUtils.isEmpty(priceVO.getDeliAreaIdList()) || CollectionUtils.isEmpty(priceVO.getArriAreaIdList()))
                    && BillingMethod.UNIT_KM.getValue() != billingMethod)
                || (cv == null || StringUtils.isEmpty(cv.getId()))) {
            logger.info("{}获取合同价失败：未查询到合同价-0，参数不完整", logId);

            Map<String, String> result = new HashMap<>();
            result.put("type", "0");
            result.put("msg", "未查询到合同价-0");
            return result;
        }

        //前台传过来的 件数
        BigDecimal num = priceVO.getNum() == null ? BigDecimal.ZERO : priceVO.getNum();
        //前台传过来的 重量
        BigDecimal weight = priceVO.getWeight() == null ? BigDecimal.ZERO : priceVO.getWeight();
        //前台传过来的 体积
        BigDecimal volume = priceVO.getVolume() == null ? BigDecimal.ZERO : priceVO.getVolume();
        //公里数
        BigDecimal mileage = priceVO.getMileage() == null ? BigDecimal.ZERO : priceVO.getMileage();

        /*
         * 数量 根据 计价方式 的不同，取值逻辑也不同，逻辑如下：
         * 1、重量（元/吨） 与 单价（元/吨．公里） ：根据前台传来的 重量 判断
         * 2、体积（元/立方米） ： 根据前台传来的 体积 判断
         * 3、件（元/件） ： 根据前台传来的 件数 判断
         * 4、单价（元/公里） ： 根据公里数判断
         */
        BigDecimal count = BigDecimal.valueOf(0);
        if (BillingMethod.WEIGHT.getValue() == billingMethod) {
            count = weight;
        } else if (BillingMethod.VOLUME.getValue() == billingMethod) {
            count = volume;
        } else if (BillingMethod.PIECE.getValue() == billingMethod) {
            count = num;
        } else if (BillingMethod.UNIT_KM.getValue() == billingMethod) {
            count = mileage;
        } else if (BillingMethod.UNIT_KM_WEIGHT.getValue() == billingMethod) {
            count = weight;
        } else if (BillingMethod.UNIT_KM_PIECE.getValue() == billingMethod) {
            count = num;
        }

        /*
         * 处理合同数据
         */
        //判断计价方式为 包车或一日往返包车时，需加入车长作为条件查询；其余情况不加入车长
//        if (BillingMethod.BUS.getValue() != billingMethod && BillingMethod.DAY.getValue() != billingMethod) {
//            priceVO.setCarLen(null);
//        }
//        String carType = priceVO.getCarType();
//
//        priceVO.setCarType();

        String carType = priceVO.getCarType();
        String carLen = priceVO.getCarLen();

        priceVO.setCarLen(priceVO.getCarLen() == null ? "" : priceVO.getCarLen());
        priceVO.setCarType(priceVO.getCarType() == null ? "" : priceVO.getCarType());

/*
        //合同价精确到市或区  0区 1市
        if (accurateRegion != null && accurateRegion == 1) {
            //精确到市  则把区信息置空
            priceVO.setArriAreaId(null);
            priceVO.setDeliAreaId(null);
        }

        //合同价精确到到货详细地址 0否  1是
        if (accurateArriDetail != null && accurateArriDetail == 0) {
            //不精确到详细地址，则把到货详细地址id置空
            priceVO.setArrivalId(null);
        } else {
            //精确到详细地址   把到货省市区id置空
            priceVO.setArriProvinceId(null);
            priceVO.setArriCityId(null);
            priceVO.setArriAreaId(null);
        }

        //合同价精确到货品 0否  1是
        if (accurateGoods != null && accurateGoods == 0) {
            priceVO.setGoodsId(null);

        }
*/

        //已审核
//        priceVO.setPriceReview(1);

        String goodsName = priceVO.getGoodsName();

        priceVO.setGoodsName(null);

        priceVO.setVersionId(cv.getId());
        List<CustContractpc> custContractpcList = custContractpcMapper.getCustContractpcBySearchContractPriceVO(priceVO);

        logger.info("{}数据库查询到合同价：{}", logId, custContractpcList);

        if (custContractpcList.size() == 0
                && (BillingMethod.UNIT_KM.getValue() == billingMethod
                    || BillingMethod.UNIT_KM_WEIGHT.getValue() == billingMethod
                    || BillingMethod.UNIT_KM_PIECE.getValue() == billingMethod)) {
            priceVO.setDeliAreaIdList(new ArrayList<>());
            priceVO.setArriAreaIdList(new ArrayList<>());

            custContractpcList = custContractpcMapper.getCustContractpcBySearchContractPriceVO(priceVO);
        }

        /*
         * 过滤特定地址
         */
        List<String> arriAddrNameList = priceVO.getArriAddrNameList();
        if (arriAddrNameList != null) {
            List<CustContractpc> filteredList = custContractpcList.stream()
                    .filter(custContractpc -> arriAddrNameList.contains(custContractpc.getArriAddrName()))
                    .collect(Collectors.toList());
            if (!filteredList.isEmpty()) {
                custContractpcList = filteredList;
            }else {
                custContractpcList = custContractpcList.stream().filter(x -> x.getArriAddrName() == null).collect(Collectors.toList());
            }
        }

        List<String> deliAddrNameList = priceVO.getDeliAddrNameList();
        if (deliAddrNameList != null) {
            List<CustContractpc> filteredList = custContractpcList.stream()
                    .filter(custContractpc -> deliAddrNameList.contains(custContractpc.getDeliAddrName()))
                    .collect(Collectors.toList());
            if (!filteredList.isEmpty()) {
                custContractpcList = filteredList;
            }else {
                custContractpcList = custContractpcList.stream().filter(x -> x.getDeliAddrName() == null).collect(Collectors.toList());
            }
        }

        /*
         * 过滤特定货品
         */
        List<CustContractpc> collect = custContractpcList.stream()
                .filter(x -> x.getGoodsName() != null && x.getGoodsName().equals(goodsName))
                .collect(Collectors.toList());
        if (collect.size() > 0) {
            custContractpcList = collect;
        }else {
            custContractpcList = custContractpcList.stream().filter(x -> x.getGoodsName() == null).collect(Collectors.toList());
        }

        /*
         * 过滤车型
         */
        if (StringUtils.isNotEmpty(carType)) {
            List<CustContractpc> filteredList = custContractpcList.stream()
                    .filter(x -> StringUtils.isNotEmpty(x.getCarType()) && carType.equals(x.getCarType()))
                    .collect(Collectors.toList());

            if (!filteredList.isEmpty()) {
                custContractpcList = filteredList;
            }else {
                custContractpcList = custContractpcList.stream()
                        .filter(x -> StringUtils.isEmpty(x.getCarType()))
                        .collect(Collectors.toList());
            }
        }

        /*
         * 过滤车长
         */
        if (StringUtils.isNotEmpty(carLen)) {
            List<CustContractpc> filteredList = custContractpcList.stream()
                    .filter(x -> StringUtils.isNotEmpty(x.getCarLen()) && carLen.equals(x.getCarLen()))
                    .collect(Collectors.toList());

            if (!filteredList.isEmpty()) {
                custContractpcList = filteredList;
            }
        }


        /*
         * 过滤是否往返  优先使用非通用
         */
        List<CustContractpc> isRoundTripList = custContractpcList.stream()
                .filter(x -> x.getIsRoundTrip() != null && x.getIsRoundTrip() != 2)
                .collect(Collectors.toList());
        if (isRoundTripList.size() > 0) {
            custContractpcList = isRoundTripList;
        }

        /*
         * 过滤是否大件  优先使用非通用
         */
        List<CustContractpc> isOversizeList = custContractpcList.stream()
                .filter(x -> x.getIsOversize() != null && x.getIsOversize() != 2)
                .collect(Collectors.toList());
        if (isOversizeList.size() > 0) {
            custContractpcList = isOversizeList;
        }

        /*
         * 过滤整车零担
         */
        List<CustContractpc> isFtlList = custContractpcList.stream()
                .filter(x -> x.getIsFtl() != null && x.getIsFtl().equals(isFtl))
                .collect(Collectors.toList());
        if (!isFtlList.isEmpty()) {
            custContractpcList = isFtlList;
        } else {
            custContractpcList = custContractpcList.stream()
                    .filter(x -> x.getIsFtl() == null)
                    .collect(Collectors.toList());
        }

        logger.info("{}过滤后的合同价：{}", logId, custContractpcList);

        //如果合同价数据为空，则返回空
        if (custContractpcList.size() == 0) {
            logger.info("{}获取合同价失败：未查询到合同价-1", logId);

            Map<String, String> result = new HashMap<>();
            result.put("type", "0");
            result.put("msg", "未查询到合同价-1");
            return result;
        }

        Set<String> deliAreaSet = new HashSet<>(priceVO.getDeliAreaIdList());
        Set<String> arriAreaSet = new HashSet<>(priceVO.getArriAreaIdList());

        int routeCount = deliAreaSet.size() * arriAreaSet.size();
        if (routeCount > custContractpcList.size()) {
            logger.info("{}获取合同价失败：部分线路未查询到合同价或查出多条合同价", logId);

            Map<String, String> result = new HashMap<>();
            result.put("type", "0");
            result.put("msg", "部分线路未查询到合同价或查出多条合同价");
            return result;
        }

        List<Map<String, String>> mapList = new ArrayList<>();

        for (CustContractpc custContractpc : custContractpcList) {
            //获取第一条合同价数据
//            CustContractpc custContractpc = custContractpcList.get(0);

            Map<String, String> result = new HashMap<>();

            result.put("versionId", cv.getId());

            Integer isIncludeTax = custContractpc.getIsIncludeTax();
            //是否含税
            result.put("isIncludeTax", isIncludeTax == null ? "1" : String.valueOf(isIncludeTax));

            //合同价
//            BigDecimal guidingPrice = isInFloatDate ? custContractpc.getSpecialPrice() : custContractpc.getGuidingPrice();
            BigDecimal guidingPrice = custContractpc.getGuidingPrice();
            //保底价
            BigDecimal reservePrice = custContractpc.getReservePrice();
            //成本价
            BigDecimal costPrice = custContractpc.getCostPrice();
            //成本价计价方式
            String costBillingType = custContractpc.getCostBillingType();
            //送货费
            BigDecimal deliveryFee = custContractpc.getDeliveryFee();
            //公里数
//            Integer mileage = custContractpc.getMileage();
            //是否有区间价格 0：没有 1：有
            String ifSection = custContractpc.getIfSection();

            //是否四舍五入
            Integer isKilRound = custContractpc.getIsKilRound();

            //计算价格时，是否忽略乘以公里数 只有billingMethod为8、9的时候使用   0否  1是
            Integer isSkipMileage = custContractpc.getIsSkipMileage();

            result.put("isKilRound", Convert.toStr(isKilRound, "0"));
            result.put("isSkipMileage", Convert.toStr(isSkipMileage, "0"));

            int totalPriceScale = 2;
            //总价是否四舍五入到个位
            Integer isRound = custContractpc.getIsRound();
            if (isRound != null && isRound == 1) {
                totalPriceScale = 0;
            }

            /*
             * 1、如果计价方式为 包车（车型）、一日往返包车（车型）、单价（元/公里） 类型，则 单价与总价 直接返回合同价目表中的 合同价
             * 2、如果计价方式为 单价（元/吨．公里） 类型，则 单价：将合同价目表中的 合同价与公里数相乘 并返回；总价：单价乘以数量（重量）
             * 3、其他类型的计价方式 ：
             *              1、如果合同价目表中的 IF_SECTION 为 0，单价与总价 取合同价目表中的 合同价
             *              2、如果合同价目表中的 IF_SECTION 为 1，则根据 合同价目表id 获取价格区间list，再根据 前台输入的数量计算 所属的价格
             */
            if (BillingMethod.BUS.getValue() == billingMethod
                || BillingMethod.DAY.getValue() == billingMethod
//                    || BillingMethod.UNIT_KM.getValue() == billingMethod
            ) {
                if ("0".equals(ifSection)) {
                    result.put("type", "1");
                    result.put("msg", "查询成功");

                    //如果金额小于保底价 则去保底价
                    if (reservePrice != null) {
                        if (reservePrice.compareTo(guidingPrice) > 0) {
                            guidingPrice = reservePrice;
                        }
                    }

                    //合同价 四舍五入保留两位 返回
                    result.put("price", Convert.toStr(NumberUtil.round(guidingPrice, 2)));
                    //总合同价 同 合同价
                    result.put("totalPrice", Convert.toStr(NumberUtil.round(guidingPrice, totalPriceScale)));

                    result.put("totalPriceScale", totalPriceScale == 2 ? "0" : "1");

                    //成本价 四舍五入保留两位 返回
                    result.put("costPrice", costPrice == null ? "" : Convert.toStr(NumberUtil.round(costPrice, 2)));
                    //总成本价 同 成本价
                    result.put("totalCostPrice", costPrice == null ? "" : Convert.toStr(NumberUtil.round(costPrice, 2)));
                    //成本价计价方式
                    result.put("costBillingType", costPrice == null ? "" : costBillingType);

                    //送货费
                    result.put("deliveryFee", Convert.toStr(NumberUtil.round(deliveryFee, 2)));


                    mapList.add(result);

                }
            }
//            else if (BillingMethod.UNIT_KM.getValue() == billingMethod) {
//                //单价：将 合同价 乘以 公里数 四舍五入保留两位
//                BigDecimal price = NumberUtil.mul(guidingPrice, mileage);
//
//                return packPriceAndTotalPrice(result, price, count, 0);
//            }
            else {
                if ("0".equals(ifSection)) {
                    result.put("type", "1");
                    result.put("msg", "查询成功");

                    BigDecimal orGuidingPrice = guidingPrice;
                    BigDecimal orCostPrice = costPrice;
                    if (BillingMethod.UNIT_KM_WEIGHT.getValue() == billingMethod
                            || BillingMethod.UNIT_KM_PIECE.getValue() == billingMethod) {

                        if (isSkipMileage != null && isSkipMileage == 0) {
                            guidingPrice = NumberUtil.mul(mileage, guidingPrice);
                            costPrice = costPrice == null ? null : NumberUtil.mul(mileage, costPrice);
                        }

                        if (isKilRound != null && isKilRound == 1) {
                            guidingPrice = NumberUtil.round(guidingPrice, 0);
                            costPrice = costPrice == null ? null : NumberUtil.round(costPrice, 0);
                        }
                    }



                    /*IF_SECTION 为 0 没有价格区间情况，将 合同价 四舍五入保留两位 返回*/
                    //合同价 四舍五入保留两位 返回
                    result.put("price", Convert.toStr(NumberUtil.round(orGuidingPrice, 2)));
                    //总价： 用单价乘以数量 四舍五入保留两位
                    BigDecimal totalPrice = NumberUtil.mul(guidingPrice, count);
                    String tp = Convert.toStr(NumberUtil.round(totalPrice, totalPriceScale));
                    //如果金额小于保底价 总价则取保底价
                    if (reservePrice != null) {
                        if (reservePrice.compareTo(totalPrice) > 0) {
                            tp = Convert.toStr(reservePrice);
                        }
                    }
                    result.put("totalPrice", tp);

                    result.put("totalPriceScale", totalPriceScale == 2 ? "0" : "1");

                    //成本价 四舍五入保留两位 返回
                    result.put("costPrice", orCostPrice == null ? "" : Convert.toStr(NumberUtil.round(orCostPrice, 2)));
                    //总成本价： 用单价乘以数量 四舍五入保留两位
                    BigDecimal totalCostPrice = NumberUtil.mul(costPrice, count);
                    result.put("totalCostPrice", costPrice == null ? "" : Convert.toStr(NumberUtil.round(totalCostPrice, 2)));
                    //成本价计价方式
                    result.put("costBillingType", orCostPrice == null ? "" : costBillingType);

                    //送货费
                    result.put("deliveryFee", Convert.toStr(NumberUtil.round(deliveryFee, 2)));

                    mapList.add(result);
                } else {
                    /* IF_SECTION 为 1 有价格区间情况 */
                    //根据 合同价目表id 查询所属价格区间
                    List<ContractpcSection> contractpcSectionList
                            = contractpcSectionMapper.getContractpcSectionListByCustContractpcId(custContractpc.getCustContractpcId());

                    if (BillingMethod.UNIT_KM_WEIGHT.getValue() == billingMethod
                            || BillingMethod.UNIT_KM_PIECE.getValue() == billingMethod) {

                        //过滤公里数
                        contractpcSectionList = contractpcSectionList.stream()
                                .filter(section -> {
                                    BigDecimal startKil = Convert.toBigDecimal(section.getStartKil(), BigDecimal.ZERO);
                                    BigDecimal endKil = Convert.toBigDecimal(section.getEndKil(), new BigDecimal(Integer.MAX_VALUE));

                                    // 判断mileage是否在startKil和endKil之间
                                    return mileage.compareTo(startKil) >= 0 && mileage.compareTo(endKil) < 0;
                                })
                                .collect(Collectors.toList());
                    }


                    // 起步价
                    ContractpcSection startPrice = contractpcSectionList.stream()
                            .filter(x -> x.getIsFixedPrice() != null && x.getIsFixedPrice() == 2)
                            .min((x1, x2) -> x2.getEndSection().compareTo(x1.getEndSection())).orElse(null);


                    //剔除起步价
                    contractpcSectionList = contractpcSectionList.stream()
                            .filter(x -> x.getIsFixedPrice() != null && x.getIsFixedPrice() != 2)
                            .collect(Collectors.toList());

                    //剔除起步价之后 如果区间为空  并且存在起步价  则取起步价
                    if (startPrice != null
                            && (contractpcSectionList.isEmpty() || NumberUtil.isLessOrEqual(count, startPrice.getEndSection()))) {
                        result.put("type", "1");
                        result.put("msg", "查询成功");

                        /*
                         * 合同价
                         */
                        result.put("price", Convert.toStr(NumberUtil.round(startPrice.getGuidingPrice(), 2)));
                        result.put("totalPrice", Convert.toStr(NumberUtil.round(startPrice.getGuidingPrice(), totalPriceScale)));

                        result.put("totalPriceScale", totalPriceScale == 2 ? "0" : "1");

                        /*
                         * 成本价
                         */
                        result.put("costPrice", startPrice.getCostPrice() == null ? "" : Convert.toStr(NumberUtil.round(startPrice.getCostPrice(), 2)));
                        result.put("totalCostPrice", startPrice.getCostPrice() == null ? "" : Convert.toStr(NumberUtil.round(startPrice.getCostPrice(), 2)));
                        //成本价计价方式
                        result.put("costBillingType",startPrice.getCostPrice() == null ? "" : startPrice.getCostBillingType());

                        //是否是固定价格（0单价  1一口价  2起步价）
                        result.put("isFixedPrice", "2");

                        //送货费
                        result.put("deliveryFee", Convert.toStr(NumberUtil.round(deliveryFee, 2)));

                        mapList.add(result);
                    }



                    /*
                     * 判断所属的价格区间 判断逻辑如下：
                     * 1、根据起始数值的 start_operator 字段，判断 是大于还是大于等于
                     * 2、根据 数量count（重量/体积/件数） 与 字段start_section 比较大小，为true则返回 该条数据的guiding_price字段的值
                     */
                    for (ContractpcSection section : contractpcSectionList) {
                        //是否是固定价格（0单价  1一口价  2起步价）
                        Integer isFixedPrice = section.getIsFixedPrice();

                        //起始数值的大于or大于等于 0:大于 1: 大于等于
                        int startOperator = section.getStartOperator();
                        //区间开始值
                        BigDecimal startSection = Convert.toBigDecimal(section.getStartSection(), BigDecimal.ZERO);
                        //区间结束运算符  2:小于 3 小于等于
                        int endOperator = section.getEndOperator();
                        //区间结束值
                        BigDecimal endSection = Convert.toBigDecimal(section.getEndSection(), new BigDecimal(Integer.MAX_VALUE));

                        boolean isValid = true;

                        if (startOperator == 0 && endOperator == 2) {
                            //  startSection < x < endSection
                            isValid = !(NumberUtil.isGreater(count, startSection) && NumberUtil.isLess(count, endSection));
                        } else if (startOperator == 0 && endOperator == 3) {
                            //  startSection < x <= endSection
                            isValid = !(NumberUtil.isGreater(count, startSection) && NumberUtil.isLessOrEqual(count, endSection));
                        } else if (startOperator == 1 && endOperator == 2) {
                            //  startSection <= x < endSection
                            isValid = !(NumberUtil.isGreaterOrEqual(count, startSection) && NumberUtil.isLess(count, endSection));
                        } else if (startOperator == 1 && endOperator == 3) {
                            //  startSection <= x <= endSection
                            isValid = !(NumberUtil.isGreaterOrEqual(count, startSection) && NumberUtil.isLessOrEqual(count, endSection));
                        }

                        if (isValid) {
                            continue;
                        }

                        result.put("type", "1");
                        result.put("msg", "查询成功");

                        //区间的合同价
                        BigDecimal sectionGuidingPrice = section.getGuidingPrice();
                        //区间的成本价
                        BigDecimal sectionCostPrice = section.getCostPrice();
                        //成本价计价方式
                        String sectionCostBillingType = section.getCostBillingType();
                        //送货费
                        BigDecimal sectionDeliveryFee = section.getDeliveryFee();


                        if (isFixedPrice == 0) {
                            BigDecimal orSectionGuidingPrice = sectionGuidingPrice;
                            BigDecimal orSectionCostPrice = sectionCostPrice;

                            /*
                             * 单价模式
                             */
                            if (BillingMethod.UNIT_KM_WEIGHT.getValue() == billingMethod
                                    || BillingMethod.UNIT_KM_PIECE.getValue() == billingMethod) {
                                if (isSkipMileage != null && isSkipMileage == 0) {
                                    sectionGuidingPrice = NumberUtil.mul(sectionGuidingPrice, mileage);
                                    sectionCostPrice = sectionCostPrice == null ? null : NumberUtil.mul(sectionCostPrice, mileage);
                                }

                                if (isKilRound != null && isKilRound == 1) {
                                    sectionGuidingPrice = NumberUtil.round(sectionGuidingPrice, 0);
                                    sectionCostPrice = sectionCostPrice == null ? null : NumberUtil.round(sectionCostPrice, 0);
                                }
                            }

                            /*
                             * 合同价
                             */
                            //单价：四舍五入保留两位
                            result.put("price", Convert.toStr(NumberUtil.round(orSectionGuidingPrice, 2)));

                            //存在起步价  总价需要加上起步价
                            if (startPrice != null) {
                                //总价： 用单价乘以数量(总数量减去起步价数量) 四舍五入保留两位
                                BigDecimal totalPrice = NumberUtil.mul(sectionGuidingPrice
                                        , NumberUtil.sub(count, startPrice.getEndSection()));
                                //起步价+总价
                                totalPrice = NumberUtil.add(startPrice.getGuidingPrice(), totalPrice);
                                result.put("totalPrice", Convert.toStr(NumberUtil.round(totalPrice, totalPriceScale)));
                            } else {
                                //总价： 用单价乘以数量 四舍五入保留两位
                                BigDecimal totalPrice = NumberUtil.mul(sectionGuidingPrice, count);
                                result.put("totalPrice", Convert.toStr(NumberUtil.round(totalPrice, totalPriceScale)));
                            }
                            result.put("totalPriceScale", totalPriceScale == 2 ? "0" : "1");

                            /*
                             * 成本价
                             */
                            if (sectionCostPrice == null) {
                                //单价
                                result.put("costPrice", "");
                                //总价
                                result.put("totalCostPrice", "");
                                //成本价计价方式
                                result.put("costBillingType", "");

                            } else {
                                //单价：四舍五入保留两位
                                result.put("costPrice", Convert.toStr(NumberUtil.round(orSectionCostPrice, 2)));
                                if (startPrice != null) {
                                    //总价： 用单价乘以数量(总数量减去起步价数量) 四舍五入保留两位
                                    BigDecimal totalCostPrice = NumberUtil.mul(sectionCostPrice
                                            , NumberUtil.sub(count, startPrice.getEndSection()));
                                    totalCostPrice = NumberUtil.add(startPrice.getCostPrice(), totalCostPrice);
                                    result.put("totalCostPrice", Convert.toStr(NumberUtil.round(totalCostPrice, 2)));

                                } else {
                                    //总价： 用单价乘以数量 四舍五入保留两位
                                    BigDecimal totalCostPrice = NumberUtil.mul(sectionCostPrice, count);
                                    result.put("totalCostPrice", Convert.toStr(NumberUtil.round(totalCostPrice, 2)));
                                }
                                //成本价计价方式
                                result.put("costBillingType", sectionCostBillingType);

                            }

                            //送货费
                            result.put("deliveryFee", Convert.toStr(NumberUtil.round(sectionDeliveryFee, 2)));


                        }else {
                            /*
                             * 一口价
                             */
                            result.put("price", Convert.toStr(NumberUtil.round(sectionGuidingPrice, 2)));
                            if (startPrice != null) {
                                //有起步价情况 需要加上起步价
                                BigDecimal totalPrice = NumberUtil.add(startPrice.getGuidingPrice(), sectionGuidingPrice);
                                result.put("totalPrice", Convert.toStr(NumberUtil.round(totalPrice, totalPriceScale)));
                            } else {
                                result.put("totalPrice", Convert.toStr(NumberUtil.round(sectionGuidingPrice, totalPriceScale)));
                            }
                            result.put("totalPriceScale", totalPriceScale == 2 ? "0" : "1");

                            /*
                             * 成本价
                             */
                            if (sectionCostPrice == null) {
                                result.put("costPrice", "");
                                result.put("totalCostPrice", "");
                                //成本价计价方式
                                result.put("costBillingType", "");

                            }else {
                                result.put("costPrice", Convert.toStr(NumberUtil.round(sectionCostPrice, 2)));
                                if (startPrice != null) {
                                    //有起步价情况 需要加上起步价
                                    BigDecimal totalPrice = NumberUtil.add(startPrice.getCostPrice(), sectionCostPrice);
                                    result.put("totalCostPrice", Convert.toStr(NumberUtil.round(totalPrice, 2)));
                                } else {
                                    result.put("totalCostPrice", Convert.toStr(NumberUtil.round(sectionCostPrice, 2)));
                                }
                                //成本价计价方式
                                result.put("costBillingType", sectionCostBillingType);

                            }

                            //送货费
                            result.put("deliveryFee", Convert.toStr(NumberUtil.round(sectionDeliveryFee, 2)));

                        }

                        //是否是固定价格（0单价  1一口价  2起步价）
                        result.put("isFixedPrice", String.valueOf(isFixedPrice));
                        mapList.add(result);

                        break;
                    }
                }
            }
        }
        if (mapList.size() == 0) {
            logger.info("{}获取合同价失败：未匹配到合同价-2", logId);

            Map<String, String> result = new HashMap<>();
            result.put("type", "0");
            result.put("msg", "未查询到合同价-2");
            return result;
        }

        boolean b = mapList.stream()
                .map(map -> map.get("price") + ":" + map.get("totalPrice"))
                .distinct()
                .count() == 1;
        if (!b) {
            logger.info("{}获取合同价失败：匹配到多条重复合同价", logId);

            Map<String, String> result = new HashMap<>();
            result.put("type", "0");
            result.put("msg", "查询到多条重复合同价");
            return result;
        }

        logger.info("{}获取合同价成功：{}", logId, mapList.get(0));
        return mapList.get(0);
    }

    @Override
    public AjaxResult confirmInvoiceCheck(Invoice invoice) {
        if (invoice == null || StringUtil.isEmpty(invoice.getInvoiceId())) {
            throw new BusinessException("该数据不存在，请刷新后重试！");
        }
        if (StringUtil.isEmpty(invoice.getCarLen())) {
            throw new BusinessException("车长为空无法确认，请先修改发货单！");
        }

        ClientPopupVO clientPopup = clientMapper.selectClientById(invoice.getCustomerId());
        List<MultipleShippingAddress> multipleShippingAddresses = multipleShippingAddressMapper
                .selectListByInvoiceId(invoice.getInvoiceId());

        if (invoice.getOtherFee() != null && StringUtils.isEmpty(invoice.getOtherFeeType())) {
            throw new BusinessException("其他费费用类型不能为空。");
        }

        if (StringUtil.isEmpty(invoice.getCarType())) {
            throw new BusinessException("车型为空无法确认，请先修改发货单！");
        }
        if (StringUtil.isEmpty(invoice.getTransLineId())) {
            throw new BusinessException("调度组为空无法确认，请先修改发货单！");
        }
        //校验发货单总金额 金额为空时
        if (invoice.getCostAmount() == null) {
            throw new BusinessException("金额为空无法确认，请先修改发货单！");
        }

        //判断发货单状态，只有 新建 状态下的发货单才能确认
        String vbillstatus = invoice.getVbillstatus();
        if (!InvoiceStatusEnum.NEW.getValue().equals(vbillstatus) && !InvoiceStatusEnum.IN_APPROVAL.getValue().equals(vbillstatus)) { // 新建或审批中
            throw new BusinessException("只有新建状态下的发货单才能确认！");
        }
        if (StringUtils.isBlank(invoice.getArriMobile())) {
            throw new BusinessException("收货人手机号码不能为空！");
        }

        String rfqEnquiryLineId = invoice.getRfqEnquiryLineId();

        if ((invoice.getIfBargain() == 0 || StringUtils.isNotEmpty(rfqEnquiryLineId)) && (invoice.getCostPrice() == null)) {
            throw new BusinessException("成本价为空无法确认，请先修改发货单！");
        }

        if (clientPopup.getIsCheckLonlat() != null && clientPopup.getIsCheckLonlat() == 1) {
            for (MultipleShippingAddress multipleShippingAddress : multipleShippingAddresses) {
                AddressLonlat lonlat = new AddressLonlat();
                lonlat.setDelFlag(0);
                lonlat.setAreaId(multipleShippingAddress.getAreaId());
                lonlat.setDetailAddr(multipleShippingAddress.getDetailAddr());
                List<AddressLonlat> addressLonlats = addressLonlatMapper.selectByAll(lonlat);
                if (addressLonlats == null || addressLonlats.isEmpty()) {
                    throw new BusinessException("地址未经确认，请先点击修改订单，点击地址左侧红色坐标点，确认收发货地址。");
                }
            }
        }

        Integer invContactCheck = clientPopup.getInvContactCheck();

        if (invContactCheck == null || invContactCheck != 0) {
            Pattern contactPattern = Pattern.compile("^[\u4e00-\u9fa5]{2,}$");
            Pattern mobilePattern = Pattern.compile("^1[3-9]\\d{9}$");
            for (MultipleShippingAddress addr : multipleShippingAddresses) {
                String contact = addr.getContact();
                String mobile = addr.getMobile();

                if (contact == null || !contactPattern.matcher(contact).matches()) {
                    throw new BusinessException("无效联系人,请查看地址联系人姓名。");

                }

                if (mobile == null || !mobilePattern.matcher(mobile).matches()) {
                    throw new BusinessException("无效手机号，请查看地址联系人手机");
                }
            }
        }

/*
        Integer isOversize = invoice.getIsOversize();
        String transCode = invoice.getTransCode();
        Integer isMultiple = invoice.getIsMultiple();
        Integer isCustomsClearance = invoice.getIsCustomsClearance();

        ReferencePrice priceSearch = new ReferencePrice();
        priceSearch.setCarLen(invoice.getCarLen());
        priceSearch.setCarType(invoice.getCarType());
        priceSearch.setDeliAreaId(invoice.getDeliAreaId());
        priceSearch.setArriAreaId(invoice.getArriAreaId());
        List<ReferencePrice> referencePriceList = referencePriceMapper.selectList(priceSearch);

        //指导价是否为空
        boolean rfpB = true;
        if (!referencePriceList.isEmpty()) {
            if ("0".equals(transCode)) {
                rfpB = referencePriceList.get(0).getPriceBasic() == null;
            } else if ("4".equals(transCode)) {
                rfpB = referencePriceList.get(0).getPriceColdChain() == null;
            } else if ("15".equals(transCode)) {
                rfpB = referencePriceList.get(0).getPriceDangerousGoods() == null;
            }
        }

        //是否不为分配车队的
        boolean fb = !("1".equals(invoice.getIsFleetData()) && "1".equals(invoice.getIsFleetAssign()));

        //是否需要CostPrice
        boolean isNeedCostPriceB = isOversize != null && isOversize == 0
                && isCustomsClearance != null && isCustomsClearance == 0
                && ("0".equals(transCode) || "4".equals(transCode) || "15".equals(transCode))
                && isMultiple != null && isMultiple == 0
                && rfpB
                && fb;

        if (isNeedCostPriceB && invoice.getCostPrice() == null) {
            throw new BusinessException("成本价为空无法确认，请先修改发货单！");

        }*/

        return AjaxResult.success();
    }

    /**
     * 确认发货单
     *
     * @param invoice
     * @param isFleet 添加的类型  0：业务下单   1：车队下单
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult confirmInvoice(InvoiceAffirmVO invoiceAffirm) {
        List<InvoiceAffirmVO.AffirmVO> affirmList = invoiceAffirm.getAffirmList();

        for (InvoiceAffirmVO.AffirmVO affirmVO : affirmList) {
            //查询该发货单信息
            Invoice invoice1All = invoiceMapper.selectByPrimaryKey(affirmVO.getInvoiceId());

            //查询客户信息
            ClientPopupVO clientPopup = clientMapper.selectClientById(invoice1All.getCustomerId());

            //地址
            List<MultipleShippingAddress> multipleShippingAddresses = multipleShippingAddressMapper.selectListByInvoiceId(invoice1All.getInvoiceId());

            AjaxResult check = this.confirmInvoiceCheck(invoice1All);
            if (check.getCode() != 0) {
                throw new BusinessException(check.getMsg());
            }

            generateTransLineAndGuide(invoice1All.getDeliProvinceId(), invoice1All.getDeliProName(), invoice1All.getDeliCityId()
                    , invoice1All.getDeliCityName(), invoice1All.getDeliAreaId(), invoice1All.getDeliAreaName()
                    , invoice1All.getArriProvinceId(), invoice1All.getArriProName(), invoice1All.getArriCityId()
                    , invoice1All.getArriCityName(), invoice1All.getArriAreaId(), invoice1All.getArriAreaName()
                    , invoice1All.getTransCode(), invoice1All.getCarLen(), invoice1All.getCarLenName()
                    , invoice1All.getCarType(), invoice1All.getCarTypeName(), true, invoice1All.getCostPrice()
                    , "invoiceService.confirmInvoice");

            /*
             * 生成运段,将发货单信息 拷贝至 运段对象中，并插入数据库。
             */
            Segment segment = new Segment();
            BeanUtils.copyBeanProp(segment, invoice1All);

            int isFleet = Integer.parseInt(invoice1All.getIsFleetData());

            //运段号 逻辑：YDPZ + 当前时间 yyyyMMdd + 四位Sequence。例如 ： YDPZ201909240001。 如果是车队运段 则在编号前面加上“CD-”
            String seqSegment = StrUtil.fillBefore(segmentMapper.getSeqSegment() + "", '0', 4);
            segment.setVbillno(isFleet == 1
                    ? "CD-YDPZ" + DateUtil.format(new Date(), "yyyyMMdd") + seqSegment
                    : "YDPZ" + DateUtil.format(new Date(), "yyyyMMdd") + seqSegment);
            //结算方式
            segment.setBalatype(invoice1All.getBalaType());
            //运段状态 待调度
            segment.setVbillstatus(SegmentStatusEnum.TO_DISPATCH.getValue());
            //发货单号
            segment.setInvoiceVbillno(invoice1All.getVbillno());
            //运段类型 2 默认原始单据
            segment.setSegType(2);
            //设置为有效数据 2
            segment.setSegMark(2);
            //运段id
            String segmentId = IdUtil.simpleUUID();
            segment.setSegmentId(segmentId);
            //设置外发状态 默认不外发
            segment.setOutGoType(2);
            //画面id
            segment.setRegScrId("invoiceService.confirmInvoice");
            segment.setCorScrId("invoiceService.confirmInvoice");

            //设置是否为 车队数据  0是业务下单数据  1是车队下单数据
            if (isFleet == 0) {
                segment.setIsFleetData("0");
            } else {
                segment.setIsFleetData("1");
            }

            //是否存在业务或者车队数据 0：不存在  1：存在
            segment.setIsFleetAssign(invoice1All.getIsFleetAssign());

            //查询客户信息
            ClientPopupVO clientPopupVO = clientMapper.selectClientById(invoice1All.getCustomerId());
//        Integer crtGuidePrice = clientPopupVO.getCrtGuidePrice();

            //获取发货单货品
            InvPackGoods invPackGoods = new InvPackGoods();
            invPackGoods.setInvoiceId(invoice1All.getInvoiceId());
            invPackGoods.setDelFlag(0);
            List<InvPackGoods> invPackGoodsList = invPackGoodsMapper.selectInvPackGoodsList(invPackGoods);


            //插入运段
            segmentMapper.insertSegment(segment);


            /*
             * 将 发货单货品详情数据 拷贝至 运段货品信息对象中,并插入数据库
             */
            //查询 发货单货品详情数据
/*        InvPackGoods invPackGoods = new InvPackGoods();
        invPackGoods.setInvoiceId(invoice.getInvoiceId());
        List<InvPackGoods> invPackGoodsList = invPackGoodsMapper.selectInvPackGoodsList(invPackGoods);*/

            //循环插入运段货品明细
            for (InvPackGoods packGoods : invPackGoodsList) {
                segPackGoodsService.insertSegPackGoodsToCoypObject(packGoods, segmentId, "invoiceService.confirmInvoice");
            }


            /*
             * 生成 应收明细
             */
            ReceiveDetailVO receiveDetail = new ReceiveDetailVO();
            //拷贝发货单信息
            BeanUtils.copyBeanProp(receiveDetail, invoice1All);

            receiveDetail.setMemo(null);
            //结算客户id
            receiveDetail.setBalaCustomer(invoice1All.getBalaCustomerId());
            //客户名称存客户简称
            receiveDetail.setCustName(invoice1All.getCustAbbr());
            //结算方式
            receiveDetail.setBalatype(invoice1All.getBalaType());
            //设置费用类型 为 运费：0
            receiveDetail.setFreeType(FreeTypeEnum.PREPAID_CASH.getValue());

            //发货单号
            receiveDetail.setInvoiceVbillno(invoice1All.getVbillno());
            //总重量
            receiveDetail.setFeeWeightCount(invoice1All.getWeightCount());
            //是否原始单据 默认原始单据
            receiveDetail.setVbillType("1");
            receiveDetail.setDelFlag(0);
            //结算公司id
            receiveDetail.setBalaCorp(invoice1All.getBalaCorpId());
            //账期  暂时使用ClientPopupVO对象，后期cjm优化改回Client对象
            receiveDetail.setPaymentDays(clientPopupVO.getPaymentDays());

            receiveDetail.setRegScrId("invoiceService.confirmInvoice");
            receiveDetail.setCorScrId("invoiceService.confirmInvoice");

            //设置是否为 车队数据  0是业务下单数据  1是车队下单数据
            if (isFleet == 0) {
                receiveDetail.setIsFleetData("0");
            } else {
                receiveDetail.setIsFleetData("1");
            }

            //总金额
            BigDecimal costAmount = invoice1All.getCostAmount();
            //代收金额
            BigDecimal collectAmount = invoice1All.getCollectAmount();

            //是否存在业务或者车队数据 0：不存在
            if ("1".equals(invoice1All.getIsFleetData()) && "1".equals(invoice1All.getIsFleetAssign())) {
                //查询业务的应付
                String bizEntrustLotId = invoice1All.getBizEntrustLotId();
                List<PayDetail> payDetailList = payDetailMapper.selectPayDetailListByLotId(bizEntrustLotId);

                for (PayDetail payDetail : payDetailList) {
                    String receiveDetailId = IdUtil.simpleUUID();
                    receiveDetail.setReceiveDetailId(receiveDetailId);
                    //应收单据号
                    receiveDetail.setVbillno(receiveDetailService.createReceiveDetailVbillno(isFleet));

                    //应收单据状态
                    receiveDetail.setVbillstatus(payDetail.getVbillstatus());

                    receiveDetail.setTransFeeCount(payDetail.getTransFeeCount());
                    receiveDetail.setFreeType(payDetail.getFreeType());

                    receiveDetail.setFleetPayDetailId(payDetail.getPayDetailId());
                    receiveDetail.setIsFleetAssign("1");
                    receiveDetailService.insertReceiveDetailAndAdjustRecord(receiveDetail);

                    payDetail.setIsFleetAssign("1");
                    payDetail.setFleetReceiveDetailId(receiveDetailId);
                    payDetailMapper.updatePayDetailById(payDetail);
                }

            } else {
                //应收单据状态
                receiveDetail.setVbillstatus(ReceiveDetailStatusEnum.NEW.getValue());
                receiveDetail.setIsFleetAssign("0");

                //如果有代收金额  则生成两条应收


                if (collectAmount != null && collectAmount.compareTo(BigDecimal.ZERO) > 0) {

                    //本条应收金额
                    BigDecimal thisAmount;
                    //代收金额 >= 总金额
                    if (NumberUtil.isGreaterOrEqual(collectAmount, costAmount)) {
                        thisAmount = costAmount;

                        //剩余代收金额 = 代收金额 - 总金额
                        collectAmount = NumberUtil.sub(collectAmount, costAmount);
                        //剩余总金额
                        costAmount = BigDecimal.ZERO;

                    }else {
                        thisAmount = collectAmount;

                        //剩余总金额 = 总金额 - 代收金额
                        costAmount = NumberUtil.sub(costAmount, collectAmount);
                        //剩余代收金额
                        collectAmount = BigDecimal.ZERO;
                    }

                    //总金额
                    receiveDetail.setTransFeeCount(thisAmount);
                    //是否司机代收
                    receiveDetail.setIsCollect(1);
                    receiveDetail.setReceiveDetailId(IdUtil.simpleUUID());
                    //应收单据号
                    receiveDetail.setVbillno(receiveDetailService.createReceiveDetailVbillno(isFleet));

                    receiveDetailService.insertReceiveDetailAndAdjustRecord(receiveDetail);

                }

                if (BigDecimal.ZERO.compareTo(costAmount) != 0) {
                    //总金额
                    receiveDetail.setTransFeeCount(costAmount);
                    //是否司机代收
                    receiveDetail.setIsCollect(0);
                    receiveDetail.setReceiveDetailId(IdUtil.simpleUUID());
                    //应收单据号
                    receiveDetail.setVbillno(receiveDetailService.createReceiveDetailVbillno(isFleet));

                    receiveDetailService.insertReceiveDetailAndAdjustRecord(receiveDetail);
                }

            }

            /*
             * 其他费用 应收在途费  otherFeeType、otherFee
             */
            String otherFeeType = invoice1All.getOtherFeeType();
            BigDecimal otherFee = invoice1All.getOtherFee();
            String otherFeeMemo = invoice1All.getOtherFeeMemo();

            if (StringUtils.isNotEmpty(otherFeeType) && otherFee != null && otherFee.compareTo(BigDecimal.ZERO) != 0) {
                ReceiveDetailVO otherReceiveDetail = new ReceiveDetailVO();
                //拷贝发货单信息
                BeanUtils.copyBeanProp(otherReceiveDetail, invoice1All);

                //结算客户id
                otherReceiveDetail.setBalaCustomer(invoice1All.getBalaCustomerId());
                //客户名称存客户简称
                otherReceiveDetail.setCustName(invoice1All.getCustAbbr());
                //结算方式
                otherReceiveDetail.setBalatype(invoice1All.getBalaType());
                //设置费用类型
                otherReceiveDetail.setFreeType(FreeTypeEnum.PREPAID_OIL_CARD.getValue());

                //发货单号
                otherReceiveDetail.setInvoiceVbillno(invoice1All.getVbillno());
                //总重量
                otherReceiveDetail.setFeeWeightCount(invoice1All.getWeightCount());
                otherReceiveDetail.setDelFlag(0);
                //结算公司id
                otherReceiveDetail.setBalaCorp(invoice1All.getBalaCorpId());
                //账期  暂时使用ClientPopupVO对象，后期cjm优化改回Client对象
                otherReceiveDetail.setPaymentDays(clientPopupVO.getPaymentDays());

                otherReceiveDetail.setMemo(otherFeeMemo);

                otherReceiveDetail.setRegScrId("invoiceService.confirmInvoice");
                otherReceiveDetail.setCorScrId("invoiceService.confirmInvoice");

//                otherReceiveDetail.setIsFleetData("0");
//                otherReceiveDetail.setIsFleetAssign("0");

                if (isFleet == 0) {
                    otherReceiveDetail.setIsFleetData("0");
                } else {
                    otherReceiveDetail.setIsFleetData("1");
                }
                if ("1".equals(invoice1All.getIsFleetData()) && "1".equals(invoice1All.getIsFleetAssign())) {
                    otherReceiveDetail.setIsFleetAssign("1");
                }else {
                    otherReceiveDetail.setIsFleetAssign("0");
                }

                otherReceiveDetail.setVbillstatus(ReceiveDetailStatusEnum.NEW.getValue());
                otherReceiveDetail.setCostTypeOnWay(otherFeeType);


                if (collectAmount != null && collectAmount.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal thisFee;
                    if (NumberUtil.isGreater(collectAmount, otherFee)) {
                        throw new BusinessException("到付应收金额应小于等于总金额。");
                    }else if (NumberUtil.isGreater(otherFee, collectAmount)){
                        thisFee = collectAmount;
                        otherFee = NumberUtil.sub(otherFee, collectAmount);
                    }else {
                        thisFee = otherFee;
                        otherFee = BigDecimal.ZERO;
                    }


                    otherReceiveDetail.setReceiveDetailId(IdUtil.simpleUUID());
                    //应收单据号
                    otherReceiveDetail.setVbillno(receiveDetailService.createReceiveDetailVbillno(isFleet));
                    //总金额
                    otherReceiveDetail.setTransFeeCount(thisFee);
                    //是否司机代收
                    otherReceiveDetail.setIsCollect(1);

                    receiveDetailService.insertReceiveDetailAndAdjustRecord(otherReceiveDetail);

                }
                if (otherFee.compareTo(BigDecimal.ZERO) != 0) {
                    otherReceiveDetail.setReceiveDetailId(IdUtil.simpleUUID());
                    //应收单据号
                    otherReceiveDetail.setVbillno(receiveDetailService.createReceiveDetailVbillno(isFleet));
                    //总金额
                    otherReceiveDetail.setTransFeeCount(otherFee);
                    //是否司机代收
                    otherReceiveDetail.setIsCollect(0);

                    receiveDetailService.insertReceiveDetailAndAdjustRecord(otherReceiveDetail);
                }
            }

            BigDecimal deliveryFee = invoice1All.getDeliveryFee();
            if (deliveryFee != null && deliveryFee.compareTo(BigDecimal.ZERO) != 0) {
                ReceiveDetailVO deliveryFeeRD = new ReceiveDetailVO();
                //拷贝发货单信息
                BeanUtils.copyBeanProp(deliveryFeeRD, invoice1All);

                //结算客户id
                deliveryFeeRD.setBalaCustomer(invoice1All.getBalaCustomerId());
                //客户名称存客户简称
                deliveryFeeRD.setCustName(invoice1All.getCustAbbr());
                //结算方式
                deliveryFeeRD.setBalatype(invoice1All.getBalaType());
                //设置费用类型
                deliveryFeeRD.setFreeType(FreeTypeEnum.PREPAID_OIL_CARD.getValue());
                deliveryFeeRD.setCostTypeOnWay("22");

                //发货单号
                deliveryFeeRD.setInvoiceVbillno(invoice1All.getVbillno());
                //总重量
                deliveryFeeRD.setFeeWeightCount(invoice1All.getWeightCount());
                deliveryFeeRD.setDelFlag(0);
                //结算公司id
                deliveryFeeRD.setBalaCorp(invoice1All.getBalaCorpId());
                //账期  暂时使用ClientPopupVO对象，后期cjm优化改回Client对象
                deliveryFeeRD.setPaymentDays(clientPopupVO.getPaymentDays());


                deliveryFeeRD.setRegScrId("invoiceService.confirmInvoice");
                deliveryFeeRD.setCorScrId("invoiceService.confirmInvoice");

//                deliveryFeeRD.setIsFleetData("0");
//                deliveryFeeRD.setIsFleetAssign("0");
                if (isFleet == 0) {
                    deliveryFeeRD.setIsFleetData("0");
                } else {
                    deliveryFeeRD.setIsFleetData("1");
                }
                if ("1".equals(invoice1All.getIsFleetData()) && "1".equals(invoice1All.getIsFleetAssign())) {
                    deliveryFeeRD.setIsFleetAssign("1");
                }else {
                    deliveryFeeRD.setIsFleetAssign("0");
                }

                deliveryFeeRD.setVbillstatus(ReceiveDetailStatusEnum.NEW.getValue());

                deliveryFeeRD.setReceiveDetailId(IdUtil.simpleUUID());
                //应收单据号
                deliveryFeeRD.setVbillno(receiveDetailService.createReceiveDetailVbillno(isFleet));
                //总金额
                deliveryFeeRD.setTransFeeCount(deliveryFee);
                //是否司机代收
                deliveryFeeRD.setIsCollect(0);

                receiveDetailService.insertReceiveDetailAndAdjustRecord(deliveryFeeRD);
            }

            /*
             * 在途费
             */
            List<CustMiscFeeVO> miscFee = custMiscFeeConfigService.getYsPriceByInvoiceId(invoice1All.getInvoiceId());

            for (CustMiscFeeVO custMiscFeeVO : miscFee) {
                ReceiveDetailVO custMiscFeeRD = new ReceiveDetailVO();
                //拷贝发货单信息
                BeanUtils.copyBeanProp(custMiscFeeRD, invoice1All);

                //结算客户id
                custMiscFeeRD.setBalaCustomer(invoice1All.getBalaCustomerId());
                //客户名称存客户简称
                custMiscFeeRD.setCustName(invoice1All.getCustAbbr());
                //结算方式
                custMiscFeeRD.setBalatype(invoice1All.getBalaType());
                //设置费用类型
                custMiscFeeRD.setFreeType(FreeTypeEnum.PREPAID_OIL_CARD.getValue());
                custMiscFeeRD.setCostTypeOnWay(custMiscFeeVO.getCostType());

                //发货单号
                custMiscFeeRD.setInvoiceVbillno(invoice1All.getVbillno());
                //总重量
                custMiscFeeRD.setFeeWeightCount(invoice1All.getWeightCount());
                custMiscFeeRD.setDelFlag(0);
                //结算公司id
                custMiscFeeRD.setBalaCorp(invoice1All.getBalaCorpId());
                //账期  暂时使用ClientPopupVO对象，后期cjm优化改回Client对象
                custMiscFeeRD.setPaymentDays(clientPopupVO.getPaymentDays());


                custMiscFeeRD.setRegScrId("invoiceService.confirmInvoice");
                custMiscFeeRD.setCorScrId("invoiceService.confirmInvoice");

                if (isFleet == 0) {
                    custMiscFeeRD.setIsFleetData("0");
                } else {
                    custMiscFeeRD.setIsFleetData("1");
                }
                if ("1".equals(invoice1All.getIsFleetData()) && "1".equals(invoice1All.getIsFleetAssign())) {
                    custMiscFeeRD.setIsFleetAssign("1");
                }else {
                    custMiscFeeRD.setIsFleetAssign("0");
                }

                custMiscFeeRD.setVbillstatus(ReceiveDetailStatusEnum.NEW.getValue());

                custMiscFeeRD.setReceiveDetailId(IdUtil.simpleUUID());
                //应收单据号
                custMiscFeeRD.setVbillno(receiveDetailService.createReceiveDetailVbillno(isFleet));
                //总金额
                custMiscFeeRD.setTransFeeCount(custMiscFeeVO.getMiscYsAmountTotal());
                //是否司机代收
                custMiscFeeRD.setIsCollect(0);
                //1自动配置
                custMiscFeeRD.setSourceType(1);

                receiveDetailService.insertReceiveDetailAndAdjustRecord(custMiscFeeRD);

                if (custMiscFeeVO.getYsBillingType() != null) {
                    receiveDetailMapper.addRecvInitTax(custMiscFeeRD.getReceiveDetailId()
                            , custMiscFeeVO.getYsBillingType(), custMiscFeeRD.getTransFeeCount()
                            , custMiscFeeRD.getRegDate(), receiveDetail.getRegUserId(), "invoiceService.confirmInvoice");
                }


            }

            /*
             * 添加第三方费用 装卸费
             */

            //单价
            BigDecimal handlingCharges = clientPopup.getHandlingCharges();
            String handlingChargesType = clientPopup.getHandlingChargesType();
            this.handlingCharges(invoice1All, handlingChargesType, handlingCharges);


            /*
             * 插入用户信息  货主或收货方不为空时取货主；货主为空 收货方不为空取收货方；都为空则新建收货方用户
             *
             */
            String lockKey = "lock:addUserByPhone:" + invoice1All.getArriMobile();
            redisLock.synclock(lockKey, 30);
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                // 事务结束后至少回收一次锁
                @Override
                public void afterCompletion(int status) {
                    //STATUS_COMMITTED = 0、STATUS_ROLLED_BACK = 1、STATUS_UNKNOWN = 2
                    redisLock.releaselock(lockKey);
                }
            });
            //收货方用户
            SysUser receiptUser = sysUserMapper.checkPhoneUnique(invoice1All.getArriMobile(), "5");
            //货主用户
            SysUser ownerUser = sysUserMapper.checkPhoneUnique(invoice1All.getArriMobile(), "1");
            //用户id
            Long userId;
            if (StringUtils.isNotNull(ownerUser)) {
                redisLock.releaselock(lockKey);
                userId = ownerUser.getUserId();
            } else if (StringUtils.isNotNull(receiptUser)) {
                redisLock.releaselock(lockKey);
                userId = receiptUser.getUserId();
            } else {
                SysUser sysUser = new SysUser();
                sysUser.setLoginName(IdUtil.simpleUUID());
                sysUser.setUserName(invoice1All.getArriContact());
                sysUser.setUserName(invoice1All.getArriContact());
                sysUser.setPhonenumber(invoice1All.getArriMobile());

                SecureRandomNumberGenerator secureRandom = new SecureRandomNumberGenerator();
                String hex = secureRandom.nextBytes(3).toHex();
                sysUser.setSalt(hex);

                String password = Md5Utils.hash(sysUser.getLoginName()
                        + configService.selectConfigByKey("sys.user.initPassword") + hex);
                sysUser.setPassword(password);
                //用户设置为 5 收货方
                sysUser.setUserType("5");
                sysUserMapper.insertUser(sysUser);
                userId = sysUser.getUserId();
            }

            /*
             * 添加发货单主表信息
             */
            Invoice invoice = new Invoice();
            invoice.setInvoiceId(invoice1All.getInvoiceId());
            //保险公司名称为空时，默认填入 '中国平安财产保险股份有限公司'
            if (StringUtils.isEmpty(invoice1All.getInsuranceCompany())) {
                invoice.setInsuranceCompany(getConfig("insurance.company"));
            }

            //保险单号为空时，默认填入 '11878003901148112693'
            if (StringUtils.isEmpty(invoice1All.getInsuranceNo())) {
                invoice.setInsuranceNo(getConfig("insurance.code"));
            }

            //保险附件为空，则默认 上传 insurance_contract.pdf
            if (StringUtils.isEmpty(invoice1All.getInsuranceAppendixId())) {
                String tid;
                try {
                    tid = this.uploadInsuranceContract();
                } catch (IOException e) {
//                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    throw new BusinessException("设置默认保险附件出错！");
                }
                invoice.setInsuranceAppendixId(tid);
            }

            //收货方用户ID
            invoice.setArriUserId(userId);
            //更新发货单状态
            invoice.setVbillstatus(InvoiceStatusEnum.AFFIRM.getValue());
            invoice.setCorUserId(shiroUtils.getUserId().toString());
            invoice.setCorUserName(shiroUtils.getSysUser().getUserName());
            invoice.setCorDate(new Date());
            //确认人
            invoice.setConfirmUserid(shiroUtils.getUserId().toString());
            invoice.setConfirmUserName(shiroUtils.getSysUser().getUserName());
            //确认时间
            invoice.setConfirmDate(new Date());
            //指导价
//            if (guidePriceNeed != null) {
//                invoice.setGuidingPrice(guidePriceNeed);
//            }

            //业务类型代码（参照数据字典 默认保存城市配送 1003997）
            invoice.setBusinesstypename("1003997");
            int i = invoiceMapper.updateInvoiceCheckStatus(invoice, InvoiceStatusEnum.NEW.getValue(), InvoiceStatusEnum.IN_APPROVAL.getValue());
            if (i != 1) {
//                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                throw new BusinessException("发货单状态发生改变，请刷新后重试！");
            }

            /*
             * 多装多卸地址表  插入运段id
             */
            for (MultipleShippingAddress multipleShippingAddress : multipleShippingAddresses) {
                //地址id
                String oldAddressId = multipleShippingAddress.getMultipleShippingAddressId();

                multipleShippingAddress.setMultipleShippingAddressId(IdUtil.simpleUUID());
                multipleShippingAddress.setSegmentId(segment.getSegmentId());
                multipleShippingAddress.setSegmentNo(segment.getVbillno());
                multipleShippingAddress.setInvoiceId(null);
                multipleShippingAddress.setInvoiceNo(null);
                multipleShippingAddress.setRegScrId("invoiceService.confirmInvoice");
                multipleShippingAddress.setCorScrId("invoiceService.confirmInvoice");

                multipleShippingAddressMapper.insertSelective(multipleShippingAddress);

                List<MultipleShippingGoods> goodsList = multipleShippingGoodsMapper.selectListByAddressId(oldAddressId);

                for (MultipleShippingGoods goods : goodsList) {
                    goods.setInvoiceGoodsId(goods.getMultipleShippingGoodsId());
                    goods.setMultipleShippingGoodsId(IdUtil.simpleUUID());
                    goods.setMultipleShippingAddressId(multipleShippingAddress.getMultipleShippingAddressId());
                    goods.setRegScrId("invoiceService.confirmInvoice");
                    goods.setCorScrId("invoiceService.confirmInvoice");
                    multipleShippingGoodsMapper.insertSelective(goods);
                }
            }


            CalculateReferencePriceVO refPrice = segmentService.isNeedReferencePriceV2(segment.getSegmentId());
            if (refPrice.getCode() == 1 && refPrice.getReferencePrice() != null) {
                Segment segmentUpdate = new Segment();
                segmentUpdate.setGuidingPrice(refPrice.getReferencePrice());
                segmentUpdate.setSegmentId(segment.getSegmentId());
                segmentUpdate.setRefPriceUserId(refPrice.getCheckUserId());
                segmentUpdate.setRefPriceUserName(refPrice.getCheckUserName());
                segmentUpdate.setRefPriceDate(refPrice.getCheckDate());
                segmentMapper.updateSegment(segmentUpdate);
            }

            String transferStation = "";
            /*
             * 自动拆段  拆中转站
             */
            if (affirmVO.getIsSubsection() != null && affirmVO.getIsSubsection() == 1) {

                String autoSubsectionAddrId = clientPopup.getAutoSubsectionAddrId();
                if (StringUtils.isEmpty(autoSubsectionAddrId)) {
                    throw new BusinessException("未配置中转站，无法自动拆段。请前往【基础信息->客户管理->客户信息->自动调度配置】模块配置中转站。");
                }

                Address address = addressMapper.selectAddressById(autoSubsectionAddrId);
                if (address == null) {
                    throw new BusinessException("未查询到中转站，无法自动拆段。");
                }

//                String stationName = sysConfigService.selectConfigByKey("ltl_def_station"); // 零担默认中转点
//                Address condition = new Address();
//                condition.setAddrName(stationName);
//                condition.setAddrType("3");
//                condition.setCheckStatus(1);
//                condition.setDelFlag(0);
//
//                List<Address> addressList = addressMapper.getAddressList(condition);
//                if (addressList.size() != 1) {
//                    throw new BusinessException("未查询到中转站，无法自动拆段。");
//                }

                transferStation = address.getAddrName();

                String[] segmentIdList = {segment.getSegmentId()};

                List<SubsectionAddressVO> subsectionAddressList = new ArrayList<>();
                SubsectionAddressVO subsectionAddress = new SubsectionAddressVO();
                BeanUtils.copyProperties(address, subsectionAddress);

                subsectionAddress.setReqArriDate(segment.getReqDeliDate());

                CustAddress query = new CustAddress();
                query.setDelFlag(0);
                query.setAddressId(address.getAddressId());
                List<CustAddress> custAddressList = custAddressMapper.selectCustAddressList(query);
                if (custAddressList.size() > 0) {
                    subsectionAddress.setContact(custAddressList.get(0).getContactName());
                    subsectionAddress.setMobile(custAddressList.get(0).getContactMobilephone());
                }
                subsectionAddressList.add(subsectionAddress);

                AjaxResult subsection = segmentService.subsection(segmentIdList, subsectionAddressList, "invoiceService.confirmInvoice"
                        , Integer.parseInt(segment.getIsFleetData()));

                if (subsection.getCode() != 0) {
                    throw new BusinessException("自动调度失败，原因：" + subsection.getMsg());
                }
            }

            /*
             * 自动调度
             */
            if (affirmVO.getIsAutoDispatch() != null && affirmVO.getIsAutoDispatch() == 1) {

                List<Segment> disSegmentList = segmentService.selectSegmentByInvoiceId(invoice1All.getInvoiceId());
                disSegmentList = disSegmentList.stream().filter(x -> x.getSegMark() == 2).collect(Collectors.toList());

                Segment disSegment;
                if (disSegmentList.size() == 1) {
                    disSegment = disSegmentList.get(0);
                }else {
                    String finalTransferStation = transferStation;

                    disSegment = disSegmentList.stream()
                            .filter(x -> finalTransferStation.equals(x.getDeliAddrName()))
                            .findFirst().orElse(null);
                }

                if (disSegment == null) {
                    throw new BusinessException(invoice1All.getVbillno() + ":未查询到运段信息，无法调度。");
                }

//                AutoDispatchConfig autoDispatchConfig = autoDispatchConfigService
//                        .selectByPrimaryKey(affirmVO.getAutoDispatchConfigId());

                Map<String, Object> price = autoDispatchConfigService
                        .getPriceByInvIdOrSegId(affirmVO.getBillingMethod().toString(), invoice1All.getInvoiceId()
                                , 0, affirmVO.getAutoDispatchConfigId(), null);
                String vbillNo = invoice1All.getVbillno();

                // 校验价格类型
                if ("0".equals(price.get("type"))) {
                    throw new BusinessException(vbillNo + ":未查询到自动调度配置信息，请刷新后重试。");
                }

                List<Map<String, String>> configDataList = cn.hutool.core.convert.Convert
                        .convert(new TypeReference<List<Map<String, String>>>() {}, price.get("dataList"));
                Map<String, String> stringStringMap = configDataList.get(0);

                String priceConfigId = Convert.toStr(stringStringMap.get("configId"));
                String priceVersionId = Convert.toStr(stringStringMap.get("versionId"));
                BigDecimal pricePayTotal = Convert.toBigDecimal(stringStringMap.get("payTotalFee"));
                BigDecimal pricePayUnitPrice = Convert.toBigDecimal(stringStringMap.get("payUnitPrice"));
                BigDecimal priceCash = Convert.toBigDecimal(stringStringMap.get("cashAmount"));
                BigDecimal priceOil = Convert.toBigDecimal(stringStringMap.get("oilAmount"));
                String priceOilCostType = Convert.toStr(stringStringMap.get("oilCostType"));
                String priceHasOnWay = Convert.toStr(stringStringMap.get("hasOnWay"));
                String priceOtherFeeType = Convert.toStr(stringStringMap.get("otherFeeType"));
                BigDecimal priceOtherFee = Convert.toBigDecimal(stringStringMap.get("otherFee"));
                Integer priceBillingType = Convert.toInt(stringStringMap.get("billingType"));
                String priceCarrierId = Convert.toStr(stringStringMap.get("carrierId"));

                BigDecimal voPayTotal = affirmVO.getPayTotalFee();
                BigDecimal voCash = affirmVO.getCashAmount();
                BigDecimal voOil = affirmVO.getOilAmount();


                // 校验自动调度价格
                if (pricePayTotal == null || priceCash == null || priceOil == null) {
                    throw new BusinessException(vbillNo + ":未能获取到自动调度配置价格，请刷新后重试。");
                }

                // 校验应付金额
                if (voPayTotal == null || voCash == null || voOil == null) {
                    throw new BusinessException(vbillNo + ":应付金额不能为空，请刷新后重试。");
                }


                // 校验协议后价格
                if (voPayTotal.compareTo(pricePayTotal) > 0) {
                    throw new BusinessException(vbillNo + ":协议后价格不能高于自动调度配置价格，请刷新后重试。");
                }

                // 赋值逻辑
                BigDecimal payTotalFee, payUnitPrice, cashAmount, oilAmount;
                //油卡类型 1预付油卡  3到付油卡  5回付油卡  默认5
                String oilCostType = StringUtil.isEmpty(priceOilCostType) ? "5" : priceOilCostType;

                //是否自动调度生成  0不是  1是  2自动调度修改金额
                int isAutoDis = 1;

                Subject subject = ShiroUtils.getSubject();
                boolean permitted = subject.isPermitted("tms:segment:autoDisEditPrice");
                if (permitted
                        && (voPayTotal.compareTo(pricePayTotal) != 0 || voCash.compareTo(priceCash) != 0
                            || voOil.compareTo(priceOil) != 0)) {
                    payTotalFee = voPayTotal;
                    payUnitPrice = null;
                    cashAmount = voCash;
                    oilAmount = voOil;

                    isAutoDis = 2;
                } else {
                    payTotalFee = pricePayTotal;
                    payUnitPrice = pricePayUnitPrice;
                    cashAmount = priceCash;
                    oilAmount = priceOil;
                }

                // 校验油卡 + 现金 = 应付总金额
                if (NumberUtil.add(cashAmount, oilAmount).compareTo(payTotalFee) != 0) {
                    throw new BusinessException(vbillNo + ":油卡金额加现金不等于应付总金额。");
                }



//                AutoDispatchVersion autoDispatchVersion = autoDispatchVersionMapper
//                        .selectByPrimaryKey(autoDispatchConfig.getVersionId());
//
//                if (autoDispatchVersion.getIsLatest() == 0) {
//                    throw new BusinessException("自动调度配置发生改变，请刷新后重试。");
//                }
//                Date currentDate = new Date();
//                if (autoDispatchVersion.getValidDate() != null && currentDate.after(autoDispatchVersion.getValidDate())) {
//                    throw new BusinessException("自动调度版本已过期，请刷新后重试。");
//                }

                Carrier carrier = carrierMapper.selectCarrierById(priceCarrierId);

                DispatchVO dispatchVO = new DispatchVO();
                dispatchVO.setSegmentIds(disSegment.getSegmentId());
                //承运商ID
                dispatchVO.setCarrierId(carrier.getCarrierId());
                //承运商编码
                dispatchVO.setCarrCode(carrier.getCarrCode());
                //承运商名称
                dispatchVO.setCarrName(carrier.getCarrName());
                //承运商联系电话
                dispatchVO.setCarrierPhone(carrier.getPhone());
                //结算方式 枚举1单笔 2月度
                dispatchVO.setBalaType1(carrier.getBalaType());
                //承运商类别（常量）
                dispatchVO.setCarrType(carrier.getCarrType() == null ? null : Integer.valueOf(carrier.getCarrType()));

                //收款人
                if (StringUtils.isNotEmpty(affirmVO.getCarrBankId())) {
                    dispatchVO.setCarrBankId(affirmVO.getCarrBankId());
                }

                if (StringUtils.isNotEmpty(affirmVO.getCarnoId())) {
                    Car car = carMapper.selectCarById(affirmVO.getCarnoId());
                    //车牌号ID
                    dispatchVO.setCarnoId(car.getCarId());
                    //车牌号
                    dispatchVO.setCarno(car.getCarno());
                    //车长
                    dispatchVO.setCarLenId(car.getCarLengthId());
                    //车长名称
                    dispatchVO.setCarLenName(car.getCarLenName());
                    //车型编码
                    dispatchVO.setCarTypeCode(car.getVehicleclassificationcode());
                    dispatchVO.setCarTypeName(car.getCarTypeName());
                }
                if (StringUtils.isNotEmpty(affirmVO.getDriverId())) {

                    Driver driver = driverMapper.selectDriverById(affirmVO.getDriverId());
                    //司机id
                    dispatchVO.setDriverId(driver.getDriverId());
                    //司机名称
                    dispatchVO.setDriverName(driver.getDriverName());
                    //手机号码
                    dispatchVO.setDriverMobile(driver.getPhone());
                    //身份证号码
                    dispatchVO.setCardId(driver.getCardId());
                }

                //运输方式编码
                dispatchVO.setTransCode(invoice1All.getTransCode());
                //要求提货时间
                dispatchVO.setReqDeliDate(disSegment.getReqDeliDate());
                //要求到货时间
                dispatchVO.setReqArriDate(disSegment.getReqArriDate());
                //总件数
                dispatchVO.setNumCount(disSegment.getNumCount());
                //总重量
                dispatchVO.setWeightCount(disSegment.getWeightCount());
                //总体积
                dispatchVO.setVolumeCount(disSegment.getVolumeCount());
                //结算金额
                dispatchVO.setCostAmount(payTotalFee);
                //
                dispatchVO.setCostAmountFreight(payTotalFee);
                //单价
                dispatchVO.setUnitPrice(payUnitPrice);
                //应付明细
                List<PayDetail> payDetailList = new ArrayList<>();

                //油卡
                if (oilAmount.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal oilCardRate = NumberUtil.mul(NumberUtil.div(oilAmount, payTotalFee, 2), 100);

                    dispatchVO.setOilRatio(Double.valueOf(oilCardRate.toString()));
                    dispatchVO.setOilAmount(oilAmount);

                    PayDetail payDetailOil = new PayDetail();
                    payDetailOil.setTransFeeCount(dispatchVO.getOilAmount());
                    payDetailOil.setCostTypeFreight(oilCostType);
                    payDetailList.add(payDetailOil);

//                    PayDetail payDetail = new PayDetail();
//                    payDetail.setTransFeeCount(cashAmount);
//                    payDetail.setCostTypeFreight("4");
//                    payDetailList.add(payDetail);
                }

                PayDetail payDetail = new PayDetail();
                payDetail.setTransFeeCount(cashAmount);
                payDetail.setCostTypeFreight("4");
                payDetailList.add(payDetail);
                //应付明细
                dispatchVO.setPayDetailList(payDetailList);

                List<EntrustCost> entrustCostList = new ArrayList<>();
                //送货费
                Integer autoDispatchDeliFee = clientPopup.getAutoDispatchDeliFee();
                if (autoDispatchDeliFee != null && autoDispatchDeliFee == 1
                        && affirmVO.getDeliveryFee() != null
                        && affirmVO.getDeliveryFee().compareTo(BigDecimal.ZERO) != 0) {
                    EntrustCost entrustCost = new EntrustCost();
                    entrustCost.setCostType("22");
                    entrustCost.setCost(affirmVO.getDeliveryFee());
                    entrustCostList.add(entrustCost);
                }
                //提货费
                Integer autoDispatchPickUpFee = clientPopup.getAutoDispatchPickUpFee();
                if (autoDispatchPickUpFee != null && autoDispatchPickUpFee == 1
                        && affirmVO.getPickUpFee() != null
                        && affirmVO.getPickUpFee().compareTo(BigDecimal.ZERO) != 0) {
                    EntrustCost entrustCost = new EntrustCost();
                    entrustCost.setCostType("2");
                    entrustCost.setCost(affirmVO.getPickUpFee());
                    entrustCostList.add(entrustCost);
                }

                //其他费用
                if ("1".equals(priceHasOnWay) && priceOtherFee != null && priceOtherFee.compareTo(BigDecimal.ZERO) != 0
                        && priceOtherFeeType != null) {
                    EntrustCost entrustCost = new EntrustCost();
                    entrustCost.setCostType(priceOtherFeeType);
                    entrustCost.setCost(priceOtherFee);
                    entrustCostList.add(entrustCost);

                }

                dispatchVO.setEntrustCostList(entrustCostList);

                // 成本分摊类型  0件数  1重量 2体积 3按票 4自定义
                dispatchVO.setAllocationType(3);

                //计价方式
                Integer billingMethod = affirmVO.getBillingMethod();
                Integer pricingMethod = null;

                if (billingMethod == BillingMethod.WEIGHT.getValue()) {
                    pricingMethod = CarrierProtocolPricingMethodEnum.BY_TON.getValue();

                } else if (billingMethod == BillingMethod.VOLUME.getValue()) {
                    pricingMethod = CarrierProtocolPricingMethodEnum.BY_STERE.getValue();

                } else if (billingMethod == BillingMethod.PIECE.getValue()) {
                    pricingMethod = CarrierProtocolPricingMethodEnum.BY_PIECE.getValue();

                } else if (billingMethod == BillingMethod.BUS.getValue()) {
                    pricingMethod = CarrierProtocolPricingMethodEnum.BY_FACE.getValue();

                }
                dispatchVO.setPricingMethod(pricingMethod);

                // 零担运段类型 0提货段  1干线段  2送货段
                dispatchVO.setLtlType(1);
                //开票类型
                dispatchVO.setBillingType(priceBillingType);

//                dispatchVO.setCostAmountBilling(affirmVO.getPayTotalFee());
                //
//                dispatchVO.setFreightFeeRate();
                //不需要运单审核
                dispatchVO.setIsNotReviewLot(1);
                //是否大件运输（三超） 0不是 1是
                dispatchVO.setIsOversize(0);
                //是否报关 默认不报关
                dispatchVO.setIsCustomsClearance(0);
                dispatchVO.setMemo(invoice1All.getMemo());

                dispatchVO.setAutoDispatchConfigId(priceConfigId);

                dispatchVO.setIsAutoDis(isAutoDis);
                dispatchVO.setAutoDisVersion(priceVersionId);

                AjaxResult dispatch = segmentService.dispatch(dispatchVO, "invoiceService.confirmInvoice"
                        , Integer.parseInt(disSegment.getIsFleetData()));

                if (dispatch.getCode() != 0) {
                    throw new BusinessException("自动调度失败，原因：" + dispatch.getMsg());
                }

            }


            String reqDeliDate = invoice1All.getReqDeliDate() == null
                    ? "-" : DateUtil.format(invoice1All.getReqDeliDate(),"yyyy-MM-dd HH点");
            String reqArriDate = invoice1All.getReqArriDate() == null
                    ? "-" : DateUtil.format(invoice1All.getReqArriDate(),"yyyy-MM-dd HH点");

            List<String> directMunicipalities = Arrays.asList("上海市", "北京市", "天津市", "重庆市");


            List<String> deliList = new ArrayList<>();
            List<String> arriList = new ArrayList<>();
            for (MultipleShippingAddress multipleShippingAddress : multipleShippingAddresses) {
                String provinceName = multipleShippingAddress.getProvinceName();
                String cityName = multipleShippingAddress.getCityName();
                String areaName = multipleShippingAddress.getAreaName();
                String detailAddr = multipleShippingAddress.getDetailAddr();

                String addr = directMunicipalities.contains(provinceName)
                        ? provinceName + areaName + detailAddr : provinceName + cityName + areaName + detailAddr;

                if (multipleShippingAddress.getAddressType() == 0) {
                    deliList.add(addr);
                }else {
                    arriList.add(addr);
                }
            }
            //线路
//            String deliProvinceName = invoice1All.getDeliProName();
//            String deliCityName = invoice1All.getDeliCityName();
//            String deliAreaName = invoice1All.getDeliAreaName();
//            String deliDetailAddr = invoice1All.getDeliDetailAddr();
//
//            String deli = directMunicipalities.contains(deliProvinceName)
//                    ? deliProvinceName + deliAreaName : deliCityName + deliAreaName;
//            deli = StringUtils.isEmpty(deli) ? "-" : deli + deliDetailAddr;
//
//            String arriProvinceName = invoice1All.getArriProName();
//            String arriCityName = invoice1All.getArriCityName();
//            String arriAreaName = invoice1All.getArriAreaName();
//            String arriDetailAddr = invoice1All.getArriDetailAddr();
//
//            String arri = directMunicipalities.contains(arriProvinceName)
//                    ? arriProvinceName + arriAreaName : arriCityName + arriAreaName;
//            arri = StringUtils.isEmpty(arri) ? "-" : arri + arriDetailAddr;


            String goodsName = invoice1All.getGoodsName();
            String goodsNmu = "";
            if (invoice1All.getNumCount() != null) {
                goodsNmu = goodsNmu + invoice1All.getNumCount() + "件";
            }
            if (invoice1All.getWeightCount() != null) {
                if (StringUtils.isNotEmpty(goodsNmu)) {
                    goodsNmu = goodsNmu + "|";
                }
                goodsNmu = goodsNmu + invoice1All.getWeightCount() + "吨";
            }

            if (invoice1All.getVolumeCount() != null) {
                if (StringUtils.isNotEmpty(goodsNmu)) {
                    goodsNmu = goodsNmu + "|";
                }
                goodsNmu = goodsNmu + invoice1All.getVolumeCount() + "方";
            }

            StringBuffer sb = new StringBuffer();
            sb.append("<font color=\"info\">发货单确认").append("</font>\n");
            sb.append("单号:").append(invoice1All.getVbillno()).append("\n");
            sb.append("客户:").append(invoice1All.getCustAbbr()).append("\n");
            sb.append("要求提货日期:").append(reqDeliDate).append("\n");
            sb.append("要求到货日期:").append(reqArriDate).append("\n");
//            sb.append("线路:").append(deli).append("->").append(arri).append("\n");

            for (int j = 0; j < deliList.size(); j++) {
                int idx = j + 1;
                sb.append("提货").append(idx).append(":").append(deliList.get(j)).append("\n");
            }

            for (int j = 0; j < arriList.size(); j++) {
                int idx = j + 1;
                sb.append("卸货").append(idx).append(":").append(arriList.get(j)).append("\n");
            }

            sb.append("货品:").append(goodsName).append(" ").append(goodsNmu).append("\n");
            sb.append("要求车长车型:").append(invoice1All.getCarLenName()).append("米").append(invoice1All.getCarTypeName()).append("\n");

            sb.append("备注:").append(invoice1All.getMemo()).append("\n");

            SysDept sysDept = deptService.selectDeptById(Long.valueOf(invoice1All.getTransLineId()));

            if (StringUtils.isNotEmpty(sysDept.getLeader())) {
                List<SysUser> sysUsers = sysUserMapper.selectUserByUserName(sysDept.getLeader(), "00");

                for (SysUser sysUser : sysUsers) {
                    wecomHandler.pushMarkdown(sysUser.getUserId(), sb.toString());
                }
            }
        }

        return AjaxResult.success();
    }

    /**
     * 反确认发货单
     *
     * @param unconfirm
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult backConfirmInvoice(Unconfirm unconfirm) {
        Invoice invoice1All = invoiceMapper.selectInvoiceById(unconfirm.getInvoiceId());

        //校验是否为空
        if (invoice1All == null) {
            return AjaxResult.error("该数据不存在，请刷新后重试！");
        }
        //判断发货单是否是新建状态，是则无法反确认
        if (!InvoiceStatusEnum.AFFIRM.getValue().equals(invoice1All.getVbillstatus())) {
            return AjaxResult.error("已确认状态下才能反确认！");
        }

        //判断是否关账
        if (invoice1All.getIsClose() == 1) {
            return AjaxResult.error("该发货单已关账，无法反确认！");
        }

        //判断对应应收明细是否都为新建状态
        List<ReceiveDetailVO> receiveDetailList = receiveDetailMapper.selectReceiveByInvoiceId(unconfirm.getInvoiceId());
        for (ReceiveDetailVO receiveDetail : receiveDetailList) {
            if (ReceiveDetailStatusEnum.NEW.getValue() != receiveDetail.getVbillstatus()) {
                return AjaxResult.error("该发货单对应应收单【" + receiveDetail.getVbillno() + "】不为新建状态，无法反确认！");
            }
        }

        /*
         * 判断所属运段是否符合反确认条件 逻辑：
         *                      1、多条运段代表已经拆段拆量过，无法反确认
         *                      2、只有一条运段，判断运段状态是否 为待调度状态 是则可以反确认         *
         */
        Segment segment = new Segment();
        segment.setInvoiceVbillno(invoice1All.getVbillno());
        segment.setDelFlag(0);
        List<Segment> segmentList = segmentMapper.selectSegmentList(segment);
        long count = segmentList.stream().filter(x -> x.getSegType() != 2 && x.getSegMark() == 2).count();

        //只有一条运段 运段状态是否 为待调度状态
//        boolean isToDispatch = segmentList.size() == 1 &&
//                (SegmentStatusEnum.TO_DISPATCH.getValue().equals(segmentList.get(0).getVbillstatus()));
        long isNotToDispatchCt = segmentList.stream()
                .filter(x -> !SegmentStatusEnum.TO_DISPATCH.getValue().equals(x.getVbillstatus())).count();

        if (count > 0 || isNotToDispatchCt > 0) {
            return AjaxResult.error("存在已调度或已拆段拆量的运段，无法反确认！");
        }

        long count1 = segmentList.stream().filter(x -> x.getOutGoType() != null && x.getOutGoType() != 2).count();
        if (count1 > 0) {
            return AjaxResult.error("存在已外发报价的数据，无法反确认！");
        }

        for (Segment seg : segmentList) {

            //删除所属运段
            segmentMapper.deleteSegmentById(seg.getSegmentId());
        }

        //删除所属运段对应的 运段货品明细
        String[] segmentIds = segmentList.stream().map(Segment::getSegmentId).toArray(String[]::new);
        segPackGoodsMapper.deleteSegPackGoodsBySegmentId(segmentIds);

        /*
         * 如果是车队数据 并且是分配车队生成的  则需要修改对应的应付
         */
        for (ReceiveDetailVO receiveDetailVO : receiveDetailList) {
            if ("1".equals(receiveDetailVO.getIsFleetData()) && "1".equals(receiveDetailVO.getIsFleetAssign())) {
                PayDetail payDetail = new PayDetail();
                payDetail.setPayDetailId(receiveDetailVO.getFleetPayDetailId());
                payDetail.setIsFleetAssign("2");
                payDetail.setFleetReceiveDetailId("");
                payDetailMapper.updatePayDetailById(payDetail);
            }
        }

        /*
         * 删除应收明细
         */
        ReceiveDetailVO receiveDetail = new ReceiveDetailVO();
        receiveDetail.setDelFlag(1);
        receiveDetail.setDelDate(new Date());
        receiveDetail.setInvoiceId(invoice1All.getInvoiceId());
        receiveDetail.setCorScrId(pageId);
        receiveDetail.setDelUserId(shiroUtils.getUserId().toString());
        //新建状态
        Map<String, Object> params = new HashMap<>(4);
        params.put("vbillstatus", ReceiveDetailStatusEnum.NEW.getValue());
        receiveDetail.setParams(params);
        int row = receiveDetailMapper.updateReceiveDetailByInvoiceId(receiveDetail);
        if (row != receiveDetailList.size()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("对应应收明细不为新建状态，无法反确认！");
        }

        /*
         * 删除第三方费用
         */
        List<OtherFee> otherFeeList = otherFeeMapper.selectOtherFeeListByInvoiceId(unconfirm.getInvoiceId());

        OtherFee otherFee = new OtherFee();
        otherFee.setLotId(unconfirm.getInvoiceId());
        otherFee.setDelDate(new Date());
        otherFee.setDelFlag(1);
        //新建状态
        Map<String, Object> otherFeeParams = new HashMap<>(4);
        otherFeeParams.put("vbillstatus", 0);
        otherFee.setParams(otherFeeParams);
        int i1 = otherFeeMapper.updateOtherFeeByInvoice(otherFee);
        if (i1 != otherFeeList.size()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("第三方费用不为新建状态，无法反确认！");
        }

        /*
         * 更新发货单状态
         */
        Invoice invoice = new Invoice();
        invoice.setInvoiceId(unconfirm.getInvoiceId());//共用同一个页面 发货单委托单id共用同一个字段
        invoice.setVbillstatus(InvoiceStatusEnum.NEW.getValue());
//        invoice.setUnconfirmDate(new Date());
//        invoice.setUnconfirmUserid(shiroUtils.getUserId().toString());
        invoice.setSegmentStatus(Integer.parseInt(SegmentStatusEnum.TO_DISPATCH.getValue()));
        invoice.setCorScrId(pageId);
        invoice.setCorUserId(shiroUtils.getUserId().toString());
        invoice.setCorUserName(shiroUtils.getSysUser().getUserName());
        invoice.setCorDate(new Date());

        int i = invoiceMapper.updateInvoiceCheckStatus(invoice, InvoiceStatusEnum.AFFIRM.getValue());
        if (i != 1) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("发货单状态发生改变，请刷新后重试！");
        }

        //反确认表插入一条数据
        unconfirm.setUnconfirmId(IdUtil.simpleUUID());
        //unconfirm.setUnconfirmFunction(UnconfirmFunctionEnum.PICK.getValue());//反确认功能模块key
        //unconfirm.setUnconfirmFunctionName(UnconfirmFunctionEnum.PICK.getContext());//反确认功能模块名称
        //unconfirm.setUnconfirmType(entrust.getUnconfirmType());//反确认类型
        //unconfirm.setUnconfiirmMemo(entrust.getUnconfirmMemo());//反确认说明
        unconfirm.setUnconfirmUserid(shiroUtils.getUserId().toString());
        unconfirm.setUnconfirmDate(new Date());
        unconfirm.setRegScrId("invoice");
        unconfirm.setEntrustId(invoice1All.getInvoiceId());//发货单id
        unconfirm.setSalesDept(invoice1All.getSalesDept());//运营组
        unconfirmMapper.insertUnconfirm(unconfirm);


        return AjaxResult.success();
    }

    /**
     * 关闭发货单
     *
     * @param invoice 发货单
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult close(Invoice invoice,Unconfirm unconfirm) {
        //获取发货单所有信息
        Invoice invoiceAll = invoiceMapper.selectInvoiceById(invoice.getInvoiceId());
//
//        //查询委托单
//        Entrust entrust = new Entrust();
//        entrust.setDelFlag(0);
//        entrust.setOrderno(invoice.getInvoiceId());
//        List<Entrust> entrustList = entrustMapper.selectEntrustList(entrust);

        /*
         * 校验
         */
        if (invoiceAll == null) {
            return AjaxResult.error("该数据不存在，请刷新后重试！");
        }
//        //未关闭的委托单  大于0则无法关闭
//        long closeCount = entrustList.stream().filter(x -> !x.getVbillstatus().equals(EntrustStatusEnum.CLOSE.getValue())).count();
//        if (closeCount > 0) {
//            return AjaxResult.error("存在未关闭的委托单，无法关闭！");
//        }

        //校验发货单状态是否是 新建 状态
        if (!InvoiceStatusEnum.NEW.getValue().equals(invoiceAll.getVbillstatus()) && 0 != invoiceAll.getSegmentStatus()) {
            return AjaxResult.error("只有新建状态下或者已确认并且未调度才可以关闭！");
        }

        //校验是否关账
        /*if (invoiceAll.getIsClose() == 1) {
            return AjaxResult.error("该发货单已关账，无法关闭！");
        }*/

        invoice.setCorScrId(pageId);
        invoice.setVbillstatus(InvoiceStatusEnum.CLOSE.getValue());
        invoice.setCorUserId(shiroUtils.getUserId().toString());
        invoice.setCorUserName(shiroUtils.getSysUser().getUserName());
        invoice.setCorDate(new Date());
        int i = invoiceMapper.updateInvoice(invoice);
//        if (i != 1) {
//            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//            return AjaxResult.error("发货单状态发生改变，请刷新后重新尝试关闭！");
//        }

        //更新运段状态
        List<Segment> segments = segmentMapper.selectSegmentByInvoiceId(invoice.getInvoiceId());
        for (Segment segment : segments) {
            segment.setVbillstatus(SegmentStatusEnum.CLOSE.getValue());
            segmentMapper.updateSegment(segment);
        }

        //插入反确认记录
        unconfirm.setUnconfirmId(IdUtil.simpleUUID());
        if (StringUtils.isEmpty(unconfirm.getUnconfirmApplicationName())) {
            unconfirm.setUnconfirmApplicationName(shiroUtils.getSysUser().getUserName());
        }
        unconfirm.setUnconfirmMemo(invoice.getCloseNote());
        unconfirm.setDeConfirmation("9");
        unconfirm.setUnconfirmUserid(shiroUtils.getUserId().toString());
        unconfirm.setUnconfirmDate(new Date());
        unconfirm.setRegScrId("invoice");
        unconfirm.setEntrustId(invoiceAll.getInvoiceId());//发货单id
        unconfirm.setSalesDept(invoiceAll.getSalesDept());//运营组
        unconfirmMapper.insertUnconfirm(unconfirm);
        return AjaxResult.success();
    }

    @Override
    @DataScope(deptAlias = "sales_dept", userAlias = "psndoc")
    public int selectInvoiceCountByStatusPermission(Invoice invoice) {
        return invoiceMapper.selectInvoiceCountByStatusPermission(invoice);
    }

    @Override
    @DataScope(deptAlias = "t.sales_dept", userAlias = "t.psndoc")
    public int selectNewReceiveDetailCountByInvoicePermission(Invoice invoice) {
        return invoiceMapper.selectNewReceiveDetailCountByInvoicePermission(invoice, ReceiveDetailStatusEnum.NEW.getValue());
    }

    @Override
    public List<Map<String, Object>> getSalesRank(Long[] salesDeptIds, int type) {
        return invoiceMapper.getSalesRank(salesDeptIds, type);
    }

    /**
     * 上传保险附件
     *
     * @return
     * @throws IOException
     */
    private String uploadInsuranceContract() throws IOException {
        //tid
        String tid = IdUtil.simpleUUID();
        //基础路径
        String baseDir = Global.getUploadPath();
        //文件名称
        String fileName = DateUtils.datePath() + "/" + IdUtil.simpleUUID() + ".pdf";
        //文件url
        String currentDir = StringUtils.substring(baseDir, baseDir.lastIndexOf("/") + 1);
        String url = "/static/" + currentDir + "/" + fileName;

        File desc = new File(baseDir + File.separator + fileName);
        ClassPathResource classPathResource = new ClassPathResource("static/insurance_contract.pdf");
        InputStream inputStream = null;
        try {
            inputStream = classPathResource.getInputStream();
            FileUtils.copyInputStreamToFile(inputStream, desc);
        } finally {
            IOUtils.closeQuietly(inputStream);
        }

        SysUploadFile sysUploadFile = new SysUploadFile();
        sysUploadFile.setFileName("保险合同.pdf");
        sysUploadFile.setFilePath(url);
        sysUploadFile.setTid(tid);
        sysUploadFileMapper.insertSysUploadFile(sysUploadFile);

        return tid;
    }

    /**
     * 根据 单价与数量 计算出 单价与总价 四舍五入保留两位后封装进map
     *
     * @param map          返回的map key: price 单价；totalPrice 总价
     * @param guidingPrice 合同价
     * @param costPrice    成本价
     * @param count        数量（重量/体积/件数）
     * @param isFixedPrice 是否是固定价格（0否 1是）
     * @return
     */
    private Map<String, String> packPriceAndTotalPrice(Map<String, String> map, BigDecimal sectionGuidingPrice
            , BigDecimal sectionCostPrice, BigDecimal goodsCt, int isFixedPrice) {
        map.put("type", "1");
        map.put("msg", "查询成功");

        if (isFixedPrice == 0) {
            /*
            * 合同价
            */
            //单价：四舍五入保留两位
            map.put("price", Convert.toStr(NumberUtil.round(sectionGuidingPrice, 2)));
            //总价： 用单价乘以数量 四舍五入保留两位
            BigDecimal totalPrice = NumberUtil.mul(sectionGuidingPrice, goodsCt);
            map.put("totalPrice", Convert.toStr(NumberUtil.round(totalPrice, 2)));

            /*
             * 成本价
             */
            //单价：四舍五入保留两位
            map.put("costPrice", Convert.toStr(NumberUtil.round(sectionCostPrice, 2)));
            //总价： 用单价乘以数量 四舍五入保留两位
            BigDecimal totalCostPrice = NumberUtil.mul(sectionCostPrice, goodsCt);
            map.put("totalCostPrice", Convert.toStr(NumberUtil.round(totalCostPrice, 2)));
        } else {
            /*
             * 合同价
             */
            map.put("price", Convert.toStr(NumberUtil.round(sectionGuidingPrice, 2)));
            map.put("totalPrice", Convert.toStr(NumberUtil.round(sectionGuidingPrice, 2)));

            /*
             * 成本价
             */
            map.put("costPrice", Convert.toStr(NumberUtil.round(sectionCostPrice, 2)));
            map.put("totalCostPrice", Convert.toStr(NumberUtil.round(sectionCostPrice, 2)));

        }

        return map;
    }

    /**
     * 生成发货单号 规则：结算公司编号 + 当前年月日 + 四位Sequence。例如：MY201909150001
     * 如果是车队发货单 需要在编号最前面加个 “CD-”
     *
     * @param balaCorpId 结算公司编号
     * @param isFleet    添加的类型  0：业务下单   1：车队下单
     * @return
     */
    @Override
    public String createInvoiceVbillno(String balaCorpId, int isFleet) {
        //获取Sequence，不足用0补齐
        String seqInvoice = StrUtil.fillBefore(invoiceMapper.getSeqInvoice() + "", '0', 4);

        return isFleet == 1
                ? "CD-" + balaCorpId + DateUtil.format(new Date(), "yyyyMMdd") + seqInvoice
                : balaCorpId + DateUtil.format(new Date(), "yyyyMMdd") + seqInvoice;
    }

    /**
     * 查询发货单列表（弹窗使用）
     *
     * @param invoice 发货单信息
     * @return 发货单集合
     */
    @Override
    @DataScope(deptAlias = "sales_dept", userAlias = "psndoc")
    public List<Invoice> selectInvoicePopupList(Invoice invoice) {
        return invoiceMapper.selectInvoicePopupListPermission(invoice);
    }

    @Override
    public AjaxResult insertFeeEntry(OtherFee otherFee) {
        //发货单id
        String invoiceId = otherFee.getLotId();
        Invoice invoice = invoiceMapper.selectInvoiceById(invoiceId);
        if (invoice == null) {
            return AjaxResult.error("该发货单数据不存在，请刷新后重试！");
        }
        //客户信息
        ClientPopupVO clientPopupVO = clientMapper.selectClientById(invoice.getCustomerId());
        //20200925增加判断 如果一个发货单有多个委托单需要回单影像确认（中转站不需要），判断都影像确认了，才追加限制，不允许新增修改第三方费用；
        EntrustDto entrustDto = new EntrustDto();
        entrustDto.setInvoiceVbillno(invoice.getVbillno());
        List<EntrustDto> entrustDtoList = traceMapper.selectTraceListPermission(entrustDto);

        /*Boolean flag = true;
        if (entrustDtoList == null || entrustDtoList.size() == 0) {
            flag = false;
        } else {
            flag = false;
            for (EntrustDto entrust : entrustDtoList) {
                //存在非中转站，未回单确认就可以新增第三方费用
                if (entrust.getReceiptConfirmFlag() == 1) {
                    flag = true;
                }
            }
            if (flag && clientPopupVO.getIsLockOtherFee() == 1) {
                throw new BusinessException("该发货单下委托单都已回单确认，无法新增第三方费用！");

            }
        }*/

        //添加第三方费用
        otherFee.setOtherFeeId(IdUtil.simpleUUID());
        String seq = StrUtil.fillBefore(otherFeeMapper.getSeq() + "", '0', 4);
        otherFee.setVbillno("DSF" + DateUtil.format(new Date(), "yyyyMMdd") + seq);
        //0-新建
        otherFee.setVbillstatus(OtherFeeStatusEnum.NEW.getValue());
        otherFee.setLotId(invoice.getInvoiceId());
        otherFee.setLotno(invoice.getVbillno());
        otherFee.setCorScrId(pageId);
        otherFee.setRegScrId(pageId);
        otherFeeService.insertOtherFeeAndAdjustRecord(otherFee, false);
        return AjaxResult.success();
    }

    @Override
    public List<OtherFee> selectOtherFeeApplyListByInvoiceId(OtherFee otherFee) {
        return otherFeeMapper.selectOtherFeeList(otherFee);

    }

    @Override
    public AjaxResult updateOtherFee(OtherFee otherFee) {
        OtherFee oldOtherFee = otherFeeMapper.selectOtherFeeById(otherFee.getOtherFeeId());
        //20200925增加判断 如果一个发货单有多个委托单需要回单影像确认（中转站不需要），判断都影像确认了，才追加限制，不允许新增修改第三方费用；
        EntrustDto entrustDto = new EntrustDto();
        entrustDto.setInvoiceVbillno(oldOtherFee.getLotno());
        List<EntrustDto> entrustDtoList = traceMapper.selectTraceListPermission(entrustDto);

        //发货单信息
        Invoice invoice = invoiceMapper.selectInvoiceById(oldOtherFee.getLotId());
        //客户信息
        ClientPopupVO clientPopupVO = clientMapper.selectClientById(invoice.getCustomerId());

        Boolean flag = true;
        if (entrustDtoList == null || entrustDtoList.size() == 0) {
            flag = false;
        }
        for (EntrustDto entrust : entrustDtoList) {
            //存在非中转站，未回单确认就可以新增第三方费用
            if (!"3".equals(entrust.getAddrType()) && entrust.getReceiptConfirmFlag() == 0) {
                flag = false;
            }
        }
        if (flag && clientPopupVO.getIsLockOtherFee() !=null && clientPopupVO.getIsLockOtherFee() == 1) {
            return AjaxResult.error("该发货单下委托单都已回单确认，无法新增第三方费用！");
        }


        //只有新建状态才可以更新
        otherFee.getParams().put("byStatus", OtherFeeStatusEnum.NEW.getValue() + "");
        otherFee.setCorScrId(pageId);

        int i = otherFeeMapper.updateOtherFeeByIdAndStatus(otherFee);
        if (i != 1) {
            return AjaxResult.error("更新失败！请确认数据有效性或状态后重试！");
        }
        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult otherFeeApply(OtherFee otherFee) {
        OtherFee otherFeeOld = otherFeeMapper.selectOtherFeeById(otherFee.getOtherFeeId());
        Integer lockOtherFee = otherFeeOld.getLockOtherFee();
        if (lockOtherFee != null && lockOtherFee == 1) {
            throw new BusinessException("该三方数据已锁定，无法申请。");
        }

        //更新为申请状态
        otherFee.setVbillstatus(OtherFeeStatusEnum.APPLY_FOR.getValue());
        otherFee.setApplyUserId(shiroUtils.getUserId().toString());
        otherFee.setApplyDate(new Date());
        otherFee.setCorScrId(pageId);
        //221019  改为直接审核通过
/*        //更新为待审核状态
        otherFee.setCheckStatus(CheckStatusEnum.PENDING_REVIEW.getValue());
        */
        // 230911 改为审核中，并提交企业微信审批
        // otherFee.setCheckStatus(CheckStatusEnum.PASS.getValue());
        otherFee.setCheckStatus(CheckStatusEnum.PENDING_REVIEW.getValue());

        //只有新建状态才可以更新
        otherFee.getParams().put("byStatus", OtherFeeStatusEnum.NEW.getValue() + "");
        int i = otherFeeMapper.updateOtherFeeByIdAndStatus(otherFee);
        if (i != 1) {
            throw new BusinessException("申请失败！请确认数据有效性或状态后重试！");
        }
        if (StringUtils.isNotBlank(otherFee.getTidReceipt())) {
            // 保存发票至t_invoice_receipt
            otherFeeMapper.saveOtherFeeReceipt(otherFee.getOtherFeeId(), "第三方费用申请", shiroUtils.getUserId().toString(), otherFee.getTidReceipt(), new Date());
        }
        if (StringUtils.isNotBlank(otherFee.getTaxTxt())) {
            String[] taxTxtArr = otherFee.getTaxTxt().split(",");
            for (int j = 0; j < taxTxtArr.length; j++) {
                String[] t = taxTxtArr[j].split(":");
                otherFeeMapper.initOtherFeeTaxTxt(IdUtil.simpleUUID(), otherFee.getOtherFeeId(), t[0], new BigDecimal(t[1]), shiroUtils.getUserId().toString(), "第三方费用申请", new Date());
            }
        }
        submitWecomSpAsDsf(otherFee.getOtherFeeId());
        return AjaxResult.success();
    }

    @Transactional
    @Override
    public void submitWecomSpAsDsf(String otherFeeId) {
        String biz = "dsf";
        String templateId = wecomSpMapper.getTemplateIdByBiz(biz);
        if (templateId == null) {
            logger.debug("第三方费用申请企业微信审批templateId未配置，跳过提交企业微信");
            return;
        }

        Map<String, Object> dynaParam = new LinkedHashMap<>();
        OtherFee otherFee = otherFeeService.selectOtherFeeById(otherFeeId);
        dynaParam.put("id", otherFeeId);
        Invoice invoice = this.selectInvoiceById(otherFee.getLotId());
        dynaParam.put("khmc", invoice.getCustAbbr()); // 客户名称
        dynaParam.put("jsgs", dictService.selectDictLabel("bala_corp", invoice.getBalaCorpId())); // 结算公司
        SysDept sysDept = deptService.selectDeptById(Long.valueOf(invoice.getSalesDept()));
        dynaParam.put("ywz", sysDept == null ? "" : sysDept.getDeptName()); // 运营组

        // customerId,custAbbr,yyzId,yyzName,yyzLeader,yybId,yybName,yybLeader,glbId,glbName,glbLeader
        Map<String, Object> info = clientMapper.selectYyDeptInfo(invoice.getCustomerId());

        List<SysUser> xszfzr = new ArrayList<>();

        String yybName = (String) info.get("yybName");
        if (yybName == null) {
            throw new RuntimeException("单据客户【" + info.get("custAbbr") + "】未配置运营部，请与管理员联系");
        }
        String yybLeader = (String) info.get("yybLeader");
        if (yybLeader == null) {
            throw new RuntimeException("运营部【" + yybName + "】未配置负责人");
        }

        List<SysUser> users = sysUserMapper.getSysUserByUserName(yybLeader);
        if (users.size() == 0) {
            throw new RuntimeException("运营部【" + yybName + "】负责人【" + yybLeader + "】未匹配到用户");
        } else if (users.size() > 1) {
            throw new RuntimeException("运营部【" + yybName + "】负责人【" + yybLeader + "】匹配到多个用户");
        } else {
            xszfzr.add(users.get(0));
        }

        dynaParam.put("xszfzr", xszfzr); // 运营部负责人

        // 结算组成员{customerId,custAbbr,deptName,leader,balaDept}
        List<Map<String, Object>> customerMaps = invoiceMapper.balaDeptOfCustomer(invoice.getCustomerId());
        if (customerMaps.size() == 0) {
            throw new RuntimeException("未找到该调整单的客户");
        }
        Set<String> jsz = new HashSet<>(); // 结算组
        Set<String> jszDeptId = new HashSet<>();
        for (int i = 0; i < customerMaps.size(); i++) {
            //{customerId,custAbbr,deptName,leader,balaDept}
            Map<String, Object> map = customerMaps.get(i);
            String deptName = (String) map.get("deptName");
            if (StringUtils.isBlank(deptName)) {
                throw new RuntimeException("单据客户“" + map.get("custAbbr") + "”未配置结算组，请与管理员联系");
            }
            jsz.add(deptName);
            jszDeptId.add((String) map.get("balaDept"));
        }
        SysUser condition = new SysUser();
        condition.getParams().put("deptIds", jszDeptId);
        List<SysUser> jszcy = sysUserMapper.listSystemUser(condition);
        if (jszcy.size() == 0) {
            throw new RuntimeException("结算组“" + StringUtils.join(jsz, ",") + "”没有成员，请与管理员联系");
        }
        dynaParam.put("jszcy", jszcy); // 结算组成员：用于二级节点审核

        String feeTypeNm = dictService.selectDictLabel("cost_type_on_way", otherFee.getFeeType()); //费用类型
        StringBuilder djxx = new StringBuilder();
        djxx.append(invoice.getVbillno()).append("\n");
        djxx.append(new SimpleDateFormat("yyyy-MM-dd").format(invoice.getReqDeliDate())).append(" ")
                .append(invoice.getDeliCityName().equals("市辖区") ? invoice.getDeliProName() : invoice.getDeliCityName())
                .append("~")
                .append(invoice.getArriCityName().equals("市辖区") ? invoice.getArriProName() : invoice.getArriCityName())
        .append("\n").append(invoice.getGoodsName());
        if (invoice.getNumCount() != null && invoice.getNumCount() > 0) {
            djxx.append(" ").append(invoice.getNumCount()).append("件");
        }
        if (invoice.getWeightCount() != null && invoice.getWeightCount() > 0) {
            djxx.append(" ").append(invoice.getWeightCount()).append("吨");
        }
        if (invoice.getVolumeCount() != null && invoice.getVolumeCount() > 0) {
            djxx.append(" ").append(invoice.getVolumeCount()).append("方");
        }
        djxx.append("\n").append(invoice.getCarLenName()).append(invoice.getCarTypeName());
        //应收
        List<ReceiveDetailVO> receiveDetailVOList = receiveDetailService.selectReceiveByInvoiceId(invoice.getInvoiceId());
        BigDecimal sumRecv = receiveDetailVOList.stream().map(ReceiveDetailVO::getTransFeeCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        djxx.append("\n总应收：").append(sumRecv);
        dynaParam.put("djxx", djxx);

        StringBuilder sqxx = new StringBuilder();
        sqxx.append(feeTypeNm).append(otherFee.getFeeAmount()).append("元（").append(otherFee.getPayType() == 0 ? "现金" : "油卡").append("）");
        if (otherFee.getRecAccount() != null) {
            sqxx.append("\n收款人：").append(otherFee.getRecAccount());
            if (otherFee.getRecCardNo() != null) {
                sqxx.append(" ").append(otherFee.getRecCardNo());
            }
        }
        if (otherFee.getMemo() != null) {
            sqxx.append("\n备注：").append(otherFee.getMemo());
        }
        // 含税构成
        /*Map<String, Object> tax = otherFeeMapper.getOtherFeeTax(otherFeeId);
        if (tax != null && tax.get("taxTxt") != null) {
            String[] taxTxt = ((String) tax.get("taxTxt")).split(","); //4:222,6:222
            sqxx.append("\n含税构成：");
            List<SysDictData> billingTypes = sysDictDataMapper.selectDictDataByType("billing_type");
            for (int i = 0; i < taxTxt.length; i++) {
                String[] typeAmount = taxTxt[i].split(":");
                if (i > 0) {
                    sqxx.append("；");
                }
                for (int j = 0; j < billingTypes.size(); j++) {
                    if (Objects.equals(typeAmount[0], billingTypes.get(j).getDictValue())) {
                        String label = billingTypes.remove(j).getDictLabel();
                        sqxx.append(label).append(":").append(typeAmount[1]).append("元");
                        break;
                    }
                }
            }

        }*/
        dynaParam.put("sqxx", sqxx.toString()); // 申请信息

        //应付
        List<EntrustLotDTO> payDetailList = payDetailService.selectPayDetailListGroupByLotId(invoice.getInvoiceId());

        // 三方
        //OtherFee query = new OtherFee();
        //query.setLotId(invoice.getInvoiceId());
        //List<OtherFee> otherFees = otherFeeService.selectOtherFeeList(query);

        //String net_profits_oil_tax = sysConfigService.selectConfigByKey("net_profits_oil_tax");
        Map<String, Object> netInfo = receivableReconciliationMapper.netProfitsInfoA(invoice.getInvoiceId(), null);
        StringBuilder hj = new StringBuilder();
        BigDecimal receiveTransFee = (BigDecimal) netInfo.get("ys");
        if (receiveTransFee == null) {
            receiveTransFee = BigDecimal.ZERO;
        } else {
            receiveTransFee = receiveTransFee.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        BigDecimal payTransFee = (BigDecimal) netInfo.get("yf");
        if (payTransFee == null) {
            payTransFee = BigDecimal.ZERO;
        } else {
            payTransFee = payTransFee.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        BigDecimal otherFeeAmount = (BigDecimal) netInfo.get("dsf");
        if (otherFeeAmount == null) {
            otherFeeAmount = BigDecimal.ZERO;
        } else {
            otherFeeAmount = otherFeeAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        BigDecimal tax = BigDecimal.ZERO;
        if (netInfo.get("yfsf") != null) {
            tax = tax.add((BigDecimal) netInfo.get("yfsf"));
        }
        if (netInfo.get("dsfsf") != null) {
            tax = tax.add((BigDecimal) netInfo.get("dsfsf"));
        }
        hj.append("应收:").append(receiveTransFee)
                .append("元 成本:").append(payTransFee.add(otherFeeAmount))
                .append("元 税金:").append(tax.setScale(2, BigDecimal.ROUND_HALF_UP))
                .append("元 利润:").append(receiveTransFee.subtract(payTransFee).subtract(otherFeeAmount).subtract(tax.setScale(2, BigDecimal.ROUND_HALF_UP))).append("元\n");

        BigDecimal glkk = invoiceMapper.getOtherRelatedDeduct(invoice.getInvoiceId());
        hj.append("其它单据关联扣款:").append(glkk.setScale(2, BigDecimal.ROUND_HALF_UP));

        dynaParam.put("hj", hj.toString()); // 发货单费用合计
//        StringBuilder ysmx = new StringBuilder();
//        for (int i = 0; i < receiveDetailVOList.size(); i++) {
//            if (i > 0) {
//                ysmx.append("\n");
//            }
//            ReceiveDetailVO recv = receiveDetailVOList.get(i);
//            ysmx.append(recv.getVbillno()).append(" ");
//            if ("0".equals(recv.getFreeType())) {
//                ysmx.append("运费");
//            } else if ("1".equals(recv.getFreeType())) {
//                ysmx.append("在途");
//            } else {
//                ysmx.append("未知");
//            }
//            ysmx.append(recv.getTransFeeCount()).append("元");
//
//        }
//        dynaParam.put("ysmx", ysmx.toString()); // 应收明细
        StringBuilder yfmx = new StringBuilder();
        for (int i = 0; i < payDetailList.size(); i++) {
            if (i > 0) {
                yfmx.append("\n");
            }
            EntrustLotDTO pd = payDetailList.get(i);
            //yfmx.append(pd.getLot()).append("\n").append(pd.getCarrierName());
            if (pd.getCarNo() != null) {
                yfmx.append(pd.getCarNo());
            }
            yfmx.append("\n");
            if (pd.getYfShare() != null && pd.getYfShare().doubleValue() != 0) {
                yfmx.append("总运费：").append(pd.getYfShare()).append("(");//    .append("/已核销：").append(pd.getYfGot()).append("；");
                if (pd.getYf().compareTo(pd.getYfGot()) == 0) {
                    yfmx.append("已核销");
                } else if (pd.getYfGot().compareTo(BigDecimal.ZERO) == 0) {
                    yfmx.append("未核销");
                } else {
                    yfmx.append("部分核销");
                }
                yfmx.append(")；");
            }
            if (pd.getKkShare() != null && pd.getKkShare().doubleValue() != 0) {
                yfmx.append("扣款：").append(pd.getKkShare()).append("(");
                if (pd.getKk().compareTo(pd.getKkGot()) == 0) {
                    yfmx.append("已核销");
                } else if (pd.getKkGot().compareTo(BigDecimal.ZERO) == 0) {
                    yfmx.append("未核销");
                } else {
                    yfmx.append("部分核销");
                }
                yfmx.append(")；");
            }
        }
        dynaParam.put("yfmx", yfmx.toString()); // 应付明细

        //StringBuilder sffy = new StringBuilder();
        //for (int i = 0; i < otherFees.size(); i++) {
        //    if (i > 0) {
        //        sffy.append("\n");
        //    }
        //    OtherFee of = otherFees.get(i);
        //    sffy.append(of.getVbillno()).append(" ").append(dictService.selectDictLabel("cost_type_on_way", of.getFeeType())).append(of.getFeeAmount()).append("元");
        //}
        //dynaParam.put("sffy", sffy.toString()); // 三方费用

        wecomSpMapper.clearSpFile(null, "fj", otherFeeId, biz);
        List<String> receiptTids = otherFeeMapper.listOtherFeeReceiptTids(otherFeeId);
        if (otherFee.getTid() != null || receiptTids.size() > 0) {
            // 根据tid查询所有附件，插入t_sp_files
            String[] tids = null;
            if (otherFee.getTid() != null) {
                tids = new String[receiptTids.size() + 1];
                tids[0] = otherFee.getTid();
                for (int i = 0; i < receiptTids.size(); i++) {
                    tids[i + 1] = receiptTids.get(i);
                }
            } else {
                tids = new String[receiptTids.size()];
                receiptTids.toArray(tids);
            }
            wecomSpMapper.batchAddByTid(tids, "", "fj", otherFeeId, biz, new Date());
        }
        //int x = 1 / 0;
        String spNo = wecomService.submitWecomSp(dynaParam, biz, templateId, shiroUtils.getSysUser());
        try {
            // 将spNo回写到AdjustRecord
            otherFeeMapper.writeSpNo(otherFee.getOtherFeeId(), spNo);
        } catch (Exception e) {
            logger.error("回写审批单号异常（此处不触发回滚）：", e);
        }
    }

    @Override
    public AjaxResult deleteOtherFeeApply(String otherFeeId) {
        OtherFee otherFeeCur = otherFeeMapper.selectOtherFeeById(otherFeeId);
        if(StringUtils.isNotBlank(otherFeeCur.getEntrustCostId())){
            entrustCostMapper.deleteById(otherFeeCur.getEntrustCostId());
        }

        OtherFee otherFee = new OtherFee();
        otherFee.setOtherFeeId(otherFeeId);
        otherFee.setDelFlag(1);
        otherFee.setDelUserId(shiroUtils.getUserId().toString());
        otherFee.setDelDate(new Date());
        //只有新建状态才可以删除
        otherFee.getParams().put("byStatus", OtherFeeStatusEnum.NEW.getValue() + "");
        int i = otherFeeMapper.updateOtherFeeByIdAndStatus(otherFee);
        if (i != 1) {
            return AjaxResult.error("删除失败！请确认数据有效性或状态后重试！");
        }
        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult editOrderNo(String invoiceId, String custOrderno) {
        /*
         * 修改发货单
         */
        Invoice invoice = new Invoice();
        invoice.setInvoiceId(invoiceId);
        invoice.setCustOrderno(custOrderno);
        invoiceMapper.updateInvoice(invoice);

        /*
         * 修改运段 客户发货单号
         */
        Segment segment = new Segment();
        segment.setCustOrderno(custOrderno);
        segment.setInvoiceId(invoiceId);
        segmentMapper.updateSegmentByInvoiceId(segment);

        /*
         * 更新委托单号
         */
        Entrust entrust = new Entrust();
        entrust.setCustOrderno(custOrderno);
        entrust.setOrderno(invoiceId);
        entrustMapper.updateEntrustByInvoiceId(entrust);

        /*
         * 更新应收明细
         */
        ReceiveDetailVO receiveDetail = new ReceiveDetailVO();
        receiveDetail.setInvoiceId(invoiceId);
        receiveDetail.setCustOrderno(custOrderno);
        receiveDetailMapper.updateReceiveDetailByInvoiceId(receiveDetail);

        return AjaxResult.success();
    }

    @Override
    public List<GuidePriceDetail> getGuidingPriceList(Map<String, String> param) {
        //查询指导价-查询审核通过和待审核的
        List<GuidePriceDetail> guidePriceDetails = guidePriceDetailMapper.getGuidePriceList(param);

        guidePriceDetails = guidePriceDetails.stream()
                .filter(x -> x.getCheckStatus() != CheckStatusEnum.NOT_PASS.getValue())
                .collect(Collectors.toList());

        return guidePriceDetails;
    }

/*    @Override
    public Map<String, String> getGuidingPrice(Map<String, String> param) {
        Map<String, String> map = new HashMap<>();

        //查询指导价-查询审核通过和待审核的
        List<GuidePriceDetail> guidePriceDetails = guidePriceDetailMapper.getGuidePriceList(param);

        //查询客户合同信息下维护的指导价 与 上方列表计算的指导价比较，两者取低
        String billingMethod = param.get("billingMethod");
        if (billingMethod != null && !"-1".equals(billingMethod)) {
            //根据计价方式，车长车型，线路，计费方式，客户ID查询是否存在计价数据
            List<CustContractpc> custContractpcs = custContractpcMapper.getCustContractpcByParams(param);

            if (custContractpcs != null && custContractpcs.size() > 0) {
                //判断计价方式
                if (billingMethod.equals(String.valueOf(BillingMethod.BUS.getValue())) || billingMethod.equals(String.valueOf(BillingMethod.DAY.getValue())) || billingMethod.equals(String.valueOf(BillingMethod.UNIT.getValue()))) {

                    //固定价格 乘以 (1-毛利率）
                    BigDecimal guidingPriceHT = custContractpcs.get(0).getGuidingPrice();
                    BigDecimal profit = custContractpcs.get(0).getProfit() == null ? BigDecimal.ZERO : custContractpcs.get(0).getProfit();
                    profit = new BigDecimal("100").subtract(profit);
                    guidingPriceHT = guidingPriceHT.multiply(profit).divide(new BigDecimal("100"), 5, BigDecimal.ROUND_HALF_UP);

                    //两个价格比较
                    return compareGuidPriceDetail(guidingPriceHT, guidePriceDetails);

                } else {
                    //获取区间判断
                    ContractpcSection contractpcSection = new ContractpcSection();
                    contractpcSection.setCustContractpcId(custContractpcs.get(0).getCustContractpcId());
                    List<ContractpcSection> contractpcSections = contractpcSectionMapper.selectContractpcSectionList(contractpcSection);

                    BigDecimal calCount = null;

                    //根据件数重量体积分别判断
                    if (billingMethod.equals(String.valueOf(BillingMethod.PIECE.getValue()))) {
                        calCount = new BigDecimal(param.get("numCount"));
                    } else if (billingMethod.equals(String.valueOf(BillingMethod.WEIGHT.getValue()))) {
                        calCount = new BigDecimal(param.get("weightCount"));
                    } else if (billingMethod.equals(String.valueOf(BillingMethod.VOLUME.getValue()))) {
                        calCount = new BigDecimal(param.get("volumeCount"));
                    }

                    //判断件数是否符合范围
                    for (ContractpcSection contractpcS : contractpcSections) {
                        BigDecimal startSection = contractpcS.getStartSection();
                        //取出运算符 0 大于 1 大于等于
                        Integer startOperator = contractpcS.getStartOperator();

                        //大于判断标识
                        Boolean startFlag = false;
                        if (startOperator == 0) {
                            if (calCount.compareTo(startSection) == 1) {
                                startFlag = true;
                            }
                        } else if (startOperator == 1) {
                            if (calCount.compareTo(startSection) >= 0) {
                                startFlag = true;
                            }
                        }

                        //判断小于区间是否满足
                        Boolean endFlag = false;
                        if (startFlag) {
                            BigDecimal endSection = contractpcS.getEndSection();
                            //取出运算符 2 小于 3 小于等于
                            Integer endOperator = contractpcS.getEndOperator();
                            if (endOperator == 2) {
                                if (calCount.compareTo(endSection) == -1) {
                                    endFlag = true;
                                }
                            } else if (endOperator == 3) {
                                if (calCount.compareTo(endSection) <= 0) {
                                    endFlag = true;
                                }
                            }
                        } else {
                            continue;
                        }
                        //区间都满足计算价格
                        if (endFlag) {
                            //价格 乘以 数量 (1-毛利率）
                            BigDecimal guidingPriceHT = contractpcS.getGuidingPrice();
                            BigDecimal profit = contractpcS.getProfit() == null ? BigDecimal.ZERO : custContractpcs.get(0).getProfit();
                            profit = new BigDecimal("100").subtract(profit);
                            guidingPriceHT = guidingPriceHT.multiply(profit).multiply(calCount).divide(new BigDecimal("100"), 5, BigDecimal.ROUND_HALF_UP);

                            //两个价格比较
                            return compareGuidPriceDetail(guidingPriceHT, guidePriceDetails);
                        }
                    }
                }

            } else {
                return getGuidPriceDetail(guidePriceDetails);
            }
        } else {
            return getGuidPriceDetail(guidePriceDetails);
        }
        return map;
    }*/

    /**
     * 获取页面指导价
     *
     * @param ids
     * @return
     */
//    @Override
//    public BigDecimal getPageGuidingPrice(String ids) {
//        String[] idsArray = com.ruoyi.common.core.text.Convert.toStrArray(ids);
//        BigDecimal guidePriceTotal = BigDecimal.ZERO;
//        for (String id : idsArray) {
//            BigDecimal guidePriceNeed = getGuidePriceNeed(id);
//            if (guidePriceNeed != null) {
//                guidePriceTotal = NumberUtil.add(guidePriceTotal, guidePriceNeed);
//            }
//        }
//        return guidePriceTotal;
//    }

    /**
     * 保存发货单指导价
     *
     * @param ids 发货单id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult saveGuidePrice(String ids) {
        String[] idsArray = com.ruoyi.common.core.text.Convert.toStrArray(ids);
        for (String id : idsArray) {
            BigDecimal guidePriceNeed = getGuidePriceNeed(id);
            if (guidePriceNeed != null) {
                Invoice invoice = new Invoice();
                invoice.setInvoiceId(id);
                invoice.setGuidingPrice(guidePriceNeed);
                invoice.setCorScrId("saveGuidePrice");
                invoiceMapper.updateInvoice(invoice);
            }
        }
        return AjaxResult.success();
    }


    @Override
    @DataScope(deptAlias = "t.sales_dept", userAlias = "t.regUserId")
    public List<PartInvoiceDataVO> selectPartDataInvoiceList(PartInvoiceDataVO partInvoiceDataVO) {
        List<PartInvoiceDataVO> partInvoiceDataVOS = invoiceMapper.selectPartDataInvoiceList(partInvoiceDataVO);
        return partInvoiceDataVOS;
    }

    @Override
    @DataScope(deptAlias = "t.sales_dept", userAlias = "t.regUserId")
    public Map<String, BigDecimal> selectPartDataStatistics(PartInvoiceDataVO partInvoiceDataVO) {
        return invoiceMapper.selectPartDataStatistics(partInvoiceDataVO);
    }

    /**
     * 封装共同指导价查询
     *
     * @param invoiceId
     * @return
     */
    @Override
    public BigDecimal getGuidePriceNeed(String invoiceId) {
        Invoice invoice1All = invoiceMapper.selectInvoiceById(invoiceId);
        //获取保存的指导价
//        Map<String, String> param = new HashMap<>();
//        param.put("customerId", invoice1All.getCustomerId());
//        param.put("carType", invoice1All.getCarType());
//        param.put("carLen", invoice1All.getCarLen());
//        param.put("transType", invoice1All.getTransCode());
//        param.put("deliAreaId", invoice1All.getDeliAreaId());
//        param.put("arriAreaId", invoice1All.getArriAreaId());
//        param.put("numCount", invoice1All.getNumCount().toString());
//        param.put("weightCount", invoice1All.getWeightCount().toString());
//        param.put("volumeCount", invoice1All.getVolumeCount().toString());
//        //获取发货单货品
//        InvPackGoods invPackGoods = new InvPackGoods();
//        invPackGoods.setInvoiceId(invoice1All.getInvoiceId());
//        invPackGoods.setDelFlag(0);
//        List<InvPackGoods> invPackGoodsList = invPackGoodsMapper.selectInvPackGoodsList(invPackGoods);
//        String firstBillingMethod = "-1";
//        for (InvPackGoods invPackGoodsVo : invPackGoodsList) {
//            if ("-1".equals(firstBillingMethod)) {
//                firstBillingMethod = invPackGoodsVo.getBillingMethod();
//            } else if (!firstBillingMethod.equals(invPackGoodsVo.getBillingMethod())) {
//                firstBillingMethod = "-1";
//                break;
//            }
//        }
//        param.put("billingMethod", firstBillingMethod);
//        param.put("pricingMethod", null);
//        if (FULL_VEHICLE_MAP.containsKey(invoice1All.getTransCode())) {
//            param.put("pricingMethod", "1");
//        }
//        Map<String, String> returnMap = getGuidingPrice(param);
//        BigDecimal guidePriceNeed = null;

//        BigDecimal guidePriceCal = null;
//        if (returnMap.containsKey("guidePrice") && StringUtils.isNotBlank(returnMap.get("guidePrice"))) {
//            guidePriceCal = new BigDecimal(returnMap.get("guidePrice"));
//        }

        //获取审核通过的指导价
        BigDecimal guidePriceCal2 = getInvoiceGuidingPrice(invoice1All);


//        if (guidePriceCal != null && guidePriceCal2 != null) {
//            if (guidePriceCal.compareTo(guidePriceCal2) == -1) {
//                guidePriceNeed = guidePriceCal;
//            } else {
//                guidePriceNeed = guidePriceCal2;
//            }
//        } else if (guidePriceCal != null && guidePriceCal2 == null) {
//            guidePriceNeed = guidePriceCal;
//        } else if (guidePriceCal == null && guidePriceCal2 != null) {
//            guidePriceNeed = guidePriceCal2;
//        } else {
//            guidePriceNeed = BigDecimal.ZERO;
//        }
//        return guidePriceNeed;

        return guidePriceCal2;
    }


    /**
     * 获取指导价共同
     *
     * @return
     */
    public Map<String, String> getGuidPriceDetail(List<GuidePriceDetail> guidePriceDetails) {
        Map<String, String> map = new HashMap<>();
        //不为空则封装相关信息
        if (!guidePriceDetails.isEmpty()) {
            //id
            map.put("guidePriceDetailId", guidePriceDetails.get(0).getGuidePriceDetailId());
            //指导价
            BigDecimal guidingPrice = guidePriceDetails.get(0).getGuidingPrice();
            String guidePrice = guidingPrice == null ? "" : guidingPrice.toString();
            map.put("guidePrice", guidePrice);
            //开始日期
            map.put("startDate", DateUtil.format(guidePriceDetails.get(0).getStartDate(), "yyyy-MM-dd"));
            //结束日起
            map.put("endDate", DateUtil.format(guidePriceDetails.get(0).getEndDate(), "yyyy-MM-dd"));
        }
        return map;
    }

    /**
     * 比较指导价共同
     *
     * @return
     */
    public Map<String, String> compareGuidPriceDetail(BigDecimal guidingPriceHT, List<GuidePriceDetail> guidePriceDetails) {
        Map<String, String> map = new HashMap<>();
        //价格为空或者0 记为无效
        if (guidingPriceHT == null || guidingPriceHT.compareTo(BigDecimal.ZERO) == 0) {
            return getGuidPriceDetail(guidePriceDetails);
        } else {
            //和另一个指导价进行价格笔记取小
            if (guidePriceDetails != null && guidePriceDetails.size() > 0) {
                BigDecimal guidingPrice = guidePriceDetails.get(0).getGuidingPrice() == null ? BigDecimal.ZERO : guidePriceDetails.get(0).getGuidingPrice();
                if (guidingPrice.compareTo(guidingPriceHT) == -1) {
                    return getGuidPriceDetail(guidePriceDetails);
                }
            }
            map.put("guidePriceDetailId", "");
            //指导价
            map.put("guidePrice", guidingPriceHT.toString());
            //开始日期
            map.put("startDate", "");
            //结束日起
            map.put("endDate", "");
            return map;
        }
    }


    /**
     * 判断是否生成线路，抓取指导价，指导价明细
     *
     * @param deliProvinceId    发货省id
     * @param deliProName       发货省名称
     * @param deliCityId        发货市id
     * @param deliCityName      发货市名称
     * @param deliAreaId        发货区id
     * @param deliAreaName      发货区名称
     * @param arriProvinceId    到货省id
     * @param arriProName       到货省名称
     * @param arriCityId        到货市id
     * @param arriCityName      到货市名称
     * @param arriAreaId        到货区id
     * @param arriAreaName      到货区名称
     * @param transCode         运输方式编码
     * @param carLen            车长ID
     * @param carLenName        车长名称
     * @param carType           车型ID
     * @param carTypeName       车型名称
     * @param isAddGuide        是否生成指导价
     * @param guidingPrice      指导价价格
     * @param regScrId          画面id
     */
    private void generateTransLineAndGuide(String deliProvinceId, String deliProName, String deliCityId, String deliCityName
            , String deliAreaId, String deliAreaName, String arriProvinceId, String arriProName, String arriCityId
            , String arriCityName, String arriAreaId, String arriAreaName, String transCode, String carLen
            , String carLenName, String carType, String carTypeName,boolean isAddGuide, BigDecimal guidingPrice, String regScrId) {

        //生成一条线路 当存在相同提货省市区、到货省市区时，运输方式时 不需要插入
        TransLine transLine = new TransLine();
        //起始地省市区
        transLine.setStartProvinceId(deliProvinceId);
        transLine.setStartProvinceName(deliProName);
        transLine.setStartCityId(deliCityId);
        transLine.setStartCityName(deliCityName);
        transLine.setStartAreaId(deliAreaId);
        transLine.setStartAreaName(deliAreaName);
        //目的地省市区
        transLine.setEndProvinceId(arriProvinceId);
        transLine.setEndProvinceName(arriProName);
        transLine.setEndCityId(arriCityId);
        transLine.setEndCityName(arriCityName);
        transLine.setEndAreaId(arriAreaId);
        transLine.setEndAreaName(arriAreaName);
        //未删除
        transLine.setDelFlag(0);
        //运输方式
//        transLine.setTransType(invoice1All.getTransCode());
        //线路id
        String transLineId;
        //线路名称 提货省市区-到货省市区
        String lineName;
        //线路编码
//        String lineCode = null;

//        List<TransLine> transLineList = transLineMapper.selectTransLineListByAddr(transLine);
        //判断是否已经存在
        List<TransLine> transLineList = transLineMapper.getTransLineList(transLine);
        if (transLineList.size() == 0) {
            transLineId = IdUtil.simpleUUID();
            transLine.setTransLineId(transLineId);
            lineName = deliProName + deliCityName + deliAreaName + "-" + arriProName + arriCityName + arriAreaName;
            //线路名称
            transLine.setLineName(lineName);
            transLine.setRegScrId(regScrId);
            transLine.setCorScrId(regScrId);
            transLineMapper.insertTransLine(transLine);
        } else {
            //用于查询和生成指导价信息
            transLineId = transLineList.get(0).getTransLineId();
        }

/*
        不再插入指导价

        //页面输入的指导价
//        BigDecimal guidePrice = invoice1All.getGuidingPrice();


        if (guidingPrice == null || guidingPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        if (!FULL_VEHICLE_MAP.containsKey(transCode) || !isAddGuide) {
            return;
        }

//        String transCode = invoice1All.getTransCode();
        Integer priceType = 0;

        if ("0".equals(transCode)) {
            priceType = ReferencePriceTypeConstant.PRICE_BASIC;
        } else if ("4".equals(transCode)) {
            priceType = ReferencePriceTypeConstant.PRICE_COLD_CHAIN;
        } else if ("15".equals(transCode)) {
            priceType = ReferencePriceTypeConstant.PRICE_DANGEROUS_GOODS;
        }

        //页面输入的价格

        //查询指导价
        ReferencePrice priceSearch = new ReferencePrice();
        priceSearch.setCarLen(carLen);
        priceSearch.setCarType(carType);
        priceSearch.setDeliAreaId(deliAreaId);
        priceSearch.setArriAreaId(arriAreaId);
        List<ReferencePrice> referencePriceList = referencePriceMapper.selectList(priceSearch);


        if (referencePriceList != null && referencePriceList.size() > 0) {
            ReferencePrice referencePrice = referencePriceList.get(0);

            ReferencePrice referencePriceUpdate = new ReferencePrice();
            boolean b = false;

            if ("0".equals(transCode)) {
                b = referencePrice.getPriceBasic() == null;

                referencePriceUpdate.setPriceBasic(guidingPrice);
                referencePriceUpdate.setPriceBasicRegUser(shiroUtils.getSysUser().getUserName());
                referencePriceUpdate.setPriceBasicRegDate(new Date());
                referencePriceUpdate.setPriceBasicCkUser(shiroUtils.getSysUser().getUserName());
                referencePriceUpdate.setPriceBasicCkDate(new Date());

            } else if ("4".equals(transCode)) {
                b = referencePrice.getPriceColdChain() == null;

                referencePriceUpdate.setPriceColdChain(guidingPrice);

            } else if ("15".equals(transCode)) {
                b = referencePrice.getPriceDangerousGoods() == null;

                referencePriceUpdate.setPriceDangerousGoods(guidingPrice);
                referencePriceUpdate.setPriceDangerousGoodsRegUser(shiroUtils.getSysUser().getUserName());
                referencePriceUpdate.setPriceDangerousGoodsRegDate(new Date());
                referencePriceUpdate.setPriceDangerousGoodsCkUser(shiroUtils.getSysUser().getUserName());
                referencePriceUpdate.setPriceDangerousGoodsCkDate(new Date());

            }

            if (b) {
                */
/*
                 * 修改金额
                 *//*

                referencePriceUpdate.setId(referencePrice.getId());
                referencePriceUpdate.setCorScrId(regScrId);
                referencePriceMapper.updateByPrimaryKeySelective(referencePriceUpdate);

                */
/*
                 * 历史价格表
                 *//*

                ReferencePriceHistory history = new ReferencePriceHistory();
                history.setId(IdUtil.simpleUUID());
                history.setReferencePriceId(referencePrice.getId());
                //0待审核
                history.setCheckStatus(2);
                history.setCheckUserId(shiroUtils.getUserId().toString());
                history.setCheckUserName(shiroUtils.getSysUser().getUserName());
                history.setCheckDate(new Date());
                //价格类型
                history.setPriceType(priceType);
                //价格
                history.setPrice(guidingPrice);
                history.setRegScrId(regScrId);
                referencePriceHistoryMapper.insertSelective(history);
            }

        } else {
            */
/*
             * 不存在指导价数据 则插入指导价数据  并且 历史表中插入待审核数据
             *//*

            ReferencePrice price = new ReferencePrice();
            String id = IdUtil.simpleUUID();
            price.setId(id);
            //车长车型
            price.setCarLen(carLen);
            price.setCarLenName(carLenName);
            price.setCarType(carType);
            price.setCarTypeName(carTypeName);
            //地址
            price.setDeliProvinceId(deliProvinceId);
            price.setDeliCityId(deliCityId);
            price.setDeliAreaId(deliAreaId);
            price.setDeliProName(deliProName);
            price.setDeliCityName(deliCityName);
            price.setDeliAreaName(deliAreaName);

            price.setArriProvinceId(arriProvinceId);
            price.setArriCityId(arriCityId);
            price.setArriAreaId(arriAreaId);
            price.setArriProName(arriProName);
            price.setArriCityName(arriCityName);
            price.setArriAreaName(arriAreaName);
            //线路id
            price.setTransLineId(transLineId);
            price.setRegScrId(regScrId);


            if ("0".equals(transCode)) {
                price.setPriceBasic(guidingPrice);

                price.setPriceBasicRegUser(shiroUtils.getSysUser().getUserName());
                price.setPriceBasicRegDate(new Date());
                price.setPriceBasicCkUser(shiroUtils.getSysUser().getUserName());
                price.setPriceBasicCkDate(new Date());

            } else if ("4".equals(transCode)) {
                price.setPriceColdChain(guidingPrice);


            } else if ("15".equals(transCode)) {
                price.setPriceDangerousGoods(guidingPrice);

                price.setPriceDangerousGoodsRegUser(shiroUtils.getSysUser().getUserName());
                price.setPriceDangerousGoodsRegDate(new Date());
                price.setPriceDangerousGoodsCkUser(shiroUtils.getSysUser().getUserName());
                price.setPriceDangerousGoodsCkDate(new Date());
            }

            referencePriceMapper.insertSelective(price);

            */
/*
             * 历史价格表
             *//*

            ReferencePriceHistory history = new ReferencePriceHistory();
            history.setId(IdUtil.simpleUUID());
            history.setReferencePriceId(id);
            //0待审核
            history.setCheckStatus(2);
            history.setCheckUserId(shiroUtils.getUserId().toString());
            history.setCheckUserName(shiroUtils.getSysUser().getUserName());
            history.setCheckDate(new Date());
            //价格类型
            history.setPriceType(priceType);
            //价格
            history.setPrice(guidingPrice);
            history.setRegScrId(regScrId);
            referencePriceHistoryMapper.insertSelective(history);
        }
*/

    }

    /**
     * 内容格式：（by石老师-2020年5月22日）
     * <p>
     * 岳少俊，2020-05-22，江苏省-南通市-港闸区 -》内蒙古自治区-乌兰察布市-察哈尔右翼前旗，17.5冷藏车，
     * 货品相关信息【货品名称，1.0件/2.0吨/3.0立方米；货品名称，1.0件/2.0吨/3.0立方米；】，单号：MD202005222002，
     * 录单人：qwl测试业务员，录单时间：2020-05-22 16:49:19
     */
    @Override
    public void sendTmsMobile(Invoice invoice) {
        PushMessageDTO pushMessageDTO = new PushMessageDTO();
        //要求提货日
        String reqDeliDate = new SimpleDateFormat("yyyy-MM-dd").format(invoice.getReqDeliDate());
        //录单时间
        String insertTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        //货品信息
        List<InvPackGoods> invPackGoodsList = invoice.getInvPackGoodsList();
        StringBuilder goodInfomation = new StringBuilder();
        for (InvPackGoods invPackGoods : invPackGoodsList) {
            goodInfomation.append(invPackGoods.getGoodsName()).append("，")
                    .append(null == invPackGoods.getNum() ? "未知" : invPackGoods.getNum()).append("件/")
                    .append(null == invPackGoods.getWeight() ? "未知" : invPackGoods.getWeight()).append("吨/")
                    .append(null == invPackGoods.getVolume() ? "未知" : invPackGoods.getVolume()).append("立方米；");
        }
        //设置推送内容
        String content = invoice.getCustAbbr() + "," + reqDeliDate + "," + invoice.getDeliProName() + "-"
                + invoice.getDeliCityName() + "-" + invoice.getDeliAreaName() + "-》" + invoice.getArriProName()
                + "-" + invoice.getArriCityName() + "-" + invoice.getArriAreaName() + "," + invoice.getCarLenName()
                + invoice.getCarTypeName() + ",货品相关信息【" + goodInfomation + "】，单号：" + invoice.getVbillno()
                + ",录单人：" + shiroUtils.getSysUser().getUserName() + "，录单时间：" + insertTime;
        pushMessageDTO.setContent(content);
        //设置标题
        pushMessageDTO.setTitle("紧急托运单消息");
        List<UrgentContactVO> urgentContactList = urgentContactService.selectUrgentContactVOList(new UrgentContact());
        List<String> userIdList = new ArrayList<>();
        for (UrgentContactVO urgentContact : urgentContactList) {
            userIdList.add(urgentContact.getUserId().toString());
        }
        pushMessageDTO.setUserIdList(userIdList);
        //设置推送人ID
        pushMessageDTO.setCreaterId(shiroUtils.getSysUser().getUserId().toString());
        //设置接受消息的用户类型为tms移动端
        pushMessageDTO.setUserType(3);
        pushMessageService.pushByUserId(pushMessageDTO);
    }

    @Override
    //@DataScope(deptAlias = "sales_dept", userAlias = "psndoc")
    @DataScope(deptAlias = "t.sales_dept", userAlias = "t.psndoc")
    public List<Map<String, Object>> listReceiptForExport(Invoice param) {
        return invoiceMapper.selectInvoiceListPermissionForReceiptExport(param);
    }

    /**
     * 根据
     *
     * @param entrustLotId
     * @return
     */
    @Override
    public List<Invoice> selectInvoiceByLotId(String entrustLotId) {
        return invoiceMapper.selectInvoiceByLotId(entrustLotId);
    }

    @Override
    @DataScope(deptAlias = "t.sales_dept", userAlias = "t.regUserId")
    public int getParkDataCountPermission(PartInvoiceDataVO partInvoiceDataVO) {
        return invoiceMapper.getParkDataCountPermission(partInvoiceDataVO);
    }

    @Override
    @DataScope(deptAlias = "sales_dept", userAlias = "psndoc")
    public Double calculateOrderTimeRateOnTimeRate(Invoice invoice) {
        return invoiceMapper.calculateOrderTimeRateOnTimeRate(invoice);
    }

    @Override
    @Transactional
    public AjaxResult saveOtherFee(InvoiceVO vo) {
        //插入第三方费用表 t_other_fee,check
        List<OtherFee> otherFeeList = vo.getOtherFeeList();

        BigDecimal numCount = BigDecimal.ZERO;
        BigDecimal weightCount = BigDecimal.ZERO;
        BigDecimal volumeCount = BigDecimal.ZERO;

        for (OtherFee otherFee : otherFeeList) {
            //发货单id
            String invoiceId = otherFee.getLotId();
            Invoice invoice = invoiceMapper.selectInvoiceById(invoiceId);
            if (invoice == null) {
                return AjaxResult.error("该发货单数据不存在，请刷新后重试！");
            }
            //客户信息
            ClientPopupVO clientPopupVO = clientMapper.selectClientById(invoice.getCustomerId());
            //20200925增加判断 如果一个发货单有多个委托单需要回单影像确认（中转站不需要），判断都影像确认了，才追加限制，不允许新增修改第三方费用；
            EntrustDto entrustDto = new EntrustDto();
            entrustDto.setInvoiceVbillno(invoice.getVbillno());
            List<EntrustDto> entrustDtoList = traceMapper.selectTraceListPermission(entrustDto);

            Boolean flag = true;
            if (entrustDtoList == null || entrustDtoList.size() == 0) {
                flag = false;
            } else {
                flag = false;
                for (EntrustDto entrust : entrustDtoList) {
                    //存在非中转站，未回单确认就可以新增第三方费用
                    if (entrust.getReceiptConfirmFlag() == 1) {
                        flag = true;
                    }
                }
                if (flag && clientPopupVO.getIsLockOtherFee() == 1) {
                    return AjaxResult.error("该发货单下委托单都已回单确认，无法新增第三方费用！");
                }
            }

            if (null != otherFee.getNumCount()) {
                numCount = numCount.add(otherFee.getNumCount());
            }

            if (null != otherFee.getWeightCount()) {
                weightCount = weightCount.add(otherFee.getWeightCount());
            }

            if (null != otherFee.getVolumeCount()) {
                volumeCount = volumeCount.add(otherFee.getVolumeCount());
            }

            //添加第三方费用
            otherFee.setOtherFeeId(IdUtil.simpleUUID());
            String seq = StrUtil.fillBefore(otherFeeMapper.getSeq() + "", '0', 4);
            otherFee.setVbillno("DSF" + DateUtil.format(new Date(), "yyyyMMdd") + seq);
            //0-新建
            otherFee.setVbillstatus(OtherFeeStatusEnum.NEW.getValue());
            otherFee.setLotId(invoice.getInvoiceId());
            otherFee.setLotno(invoice.getVbillno());
            otherFee.setCorScrId(pageId);
            otherFee.setRegScrId(pageId);
            otherFee.setPriceType(vo.getPriceType());
            otherFee.setPrice(vo.getPrice());
            otherFee.setIsInvoice(vo.getIsInvoice());
            otherFeeService.insertOtherFeeAndAdjustRecord(otherFee);
        }

        //插入第三方费用对账表 t_other_fee_check_sheet
        OtherFeeCheckSheet otherFeeCheckSheet = new OtherFeeCheckSheet();

        String srcId = pageId + "-GenerateOtherFeeCheckSheet";
        //获取第三方费用id

        String[] otherFeeIdArray = otherFeeList.stream().map(OtherFee::getOtherFeeId).toArray(String[]::new);
        Calendar cal = Calendar.getInstance();
        otherFeeCheckSheet.setYear(cal.get(Calendar.YEAR));
        otherFeeCheckSheet.setMonth(cal.get(Calendar.MONTH) + 1);
        otherFeeCheckSheet.setCustAbbr(vo.getCustAbbr());
        otherFeeCheckSheet.setCustomerId(vo.getCustomerId());
        otherFeeCheckSheet.setTotalAmount(vo.getSumMoney());
        otherFeeCheckSheet.setUngotAmount(vo.getSumMoney());
        otherFeeCheckSheet.setNumCount(numCount);
        otherFeeCheckSheet.setWeightCount(weightCount);
        otherFeeCheckSheet.setVolumeCount(volumeCount);
        otherFeeCheckSheet.setPrice(vo.getPrice());
        otherFeeCheckSheet.setPriceType(vo.getPriceType());

        /*
        插入第三方对账
         */
        otherFeeCheckSheet.setOtherFeeCheckSheetId(IdUtil.simpleUUID());
        // 对账名称 ：生成规则 客户简称 + yyyyMM + (页面输入的特殊标记)。如果页面没有输入则不加
        String ym = otherFeeCheckSheet.getYear() + "" + StrUtil.fillBefore(otherFeeCheckSheet.getMonth() + "", '0', 2);
        String otherFeeCheckSheetNameOld = otherFeeCheckSheet.getOtherFeeCheckSheetName();
        String otherFeeCheckSheetNameNew = otherFeeCheckSheet.getCustAbbr() + "-" + ym;
        if (StringUtils.isNotBlank(otherFeeCheckSheetNameOld)) {
            otherFeeCheckSheetNameNew = otherFeeCheckSheetNameNew + "(" + otherFeeCheckSheetNameOld + ")";
        }

        otherFeeCheckSheet.setVbillstatus(CheckSheetStatus.NEW.getValue());
        otherFeeCheckSheet.setOtherFeeCheckSheetName(otherFeeCheckSheetNameNew);
        otherFeeCheckSheet.setRegScrId(srcId);
        otherFeeCheckSheet.setCorScrId(srcId);
        otherFeeCheckSheetMapper.insertOtherFeeCheckSheet(otherFeeCheckSheet);

        /*
        更新第三方对账 插入中间表
         */
        for (String otherFeeId : otherFeeIdArray) {
            updateOtherFeeCheckSheetAndOtherFeeCheckSheetB(otherFeeId, otherFeeCheckSheet, srcId);
        }

        return AjaxResult.success();
    }

    @Override
    public List<CzdjChatsDataVO> selectCzdjChatsData(Map<String, Object> param) {
        return invoiceMapper.selectCzdjChatsData(param);
    }

    @Override
    @DataScope(deptAlias = "sales_dept", userAlias = "psndoc")
    public List<DdblChatsDataVO> selectDdblChatsData(Invoice invoice) {
        return invoiceMapper.selectDdblChatsData(invoice);
    }

    @Override
    @DataScope(deptAlias = "t1.sales_dept", userAlias = "t1.psndoc")
    public List<DdzbChatsDataVO> selectDdzbChatsData(Invoice invoice) {
        return invoiceMapper.selectDdzbChatsData(invoice);
    }

    @Override
    public Invoice selectInvoiceByBizSegmentId(String bizSegmentId) {
        return invoiceMapper.selectInvoiceByBizSegmentId(bizSegmentId);
    }

    /**
     * 更新第三方对账 插入中间表
     *
     * @param otherFeeId         第三方费用id
     * @param otherFeeCheckSheet 第三方对账id
     * @param srcId              画面id
     */
    private void updateOtherFeeCheckSheetAndOtherFeeCheckSheetB(String otherFeeId, OtherFeeCheckSheet otherFeeCheckSheet, String srcId) {
        /*
         *更新第三方费用
         */
        OtherFee otherFee = new OtherFee();
        //只有新建状态才可以更新
        otherFee.getParams().put("byStatus", OtherFeeStatusEnum.NEW.getValue() + "");
        //状态更新为已对账
        otherFee.setVbillstatus(OtherFeeStatusEnum.RECONCILED.getValue());
        otherFee.setCorScrId(srcId);
        otherFee.setOtherFeeId(otherFeeId);
        otherFeeMapper.updateOtherFeeByIdAndStatus(otherFee);

        /*
         * 插入第三方对账中间表
         */
        OtherFeeCheckSheetB otherFeeCheckSheetB = new OtherFeeCheckSheetB();
        otherFeeCheckSheetB.setOtherFeeCheckSheetId(otherFeeCheckSheet.getOtherFeeCheckSheetId());
        otherFeeCheckSheetB.setRegScrId(srcId);
        otherFeeCheckSheetB.setCorScrId(srcId);
        otherFeeCheckSheetB.setOtherFeeCheckSheetBId(IdUtil.simpleUUID());
        otherFeeCheckSheetB.setOtherFeeId(otherFeeId);
        otherFeeCheckSheetBMapper.insertOtherFeeCheckSheetB(otherFeeCheckSheetB);

    }

    class NWV {
        BigDecimal num = BigDecimal.ZERO;
        BigDecimal weight = BigDecimal.ZERO;
        BigDecimal volume = BigDecimal.ZERO;

        void add(String numStr, String weightStr, String volumeStr) {
            if (StringUtils.isNotBlank(numStr)) {
                num = num.add(new BigDecimal(numStr));
            }
            if (StringUtils.isNotBlank(weightStr)) {
                weight = weight.add(new BigDecimal(weightStr));
            }
            if (StringUtils.isNotBlank(volumeStr)) {
                volume = volume.add(new BigDecimal(volumeStr));
            }
        }

        boolean eq(NWV nwv) {
            if (nwv == null) {
                return false;
            }
            BigDecimal[] left = {num, weight, volume};
            BigDecimal[] right = {nwv.num, nwv.weight, nwv.volume};
            for (int i = 0; i < left.length; i++) {
                if (left[i] == null && right[i] == null) {
                    continue;
                } else if (left[i] != null && right[i] != null) {
                    if (left[i].compareTo(right[i]) != 0) {
                        return false;
                    }
                } else {
                    return false;
                }
            }
            return true;
        }
    }

    @Transactional
    @Override
    public AjaxResult importBatch(List<Map<String, Object>> invoiceList, boolean fetchUnitPrice) throws ParseException {
        //TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        /*{
            "custAbbr": "东成工具",
            "reqDeliDate": "2022/8/16 16:00",
            "reqArriDate": "2023/8/20 0:00",
            "carLenName": "不限",
            "carTypeName": "海运集装箱",
            "transName": "公路整车",
            "urgentLevelName": "普通",
            "transLineName": "调度1组（苏南/苏北线）",
            "billingTypeName": "增值税专用发票（13%）",
            "balaTypeName": "现金到付（外调司机代收）",
            "collectAmount": "10000.00",
            "start": [{
                "addrName": "单位1",
                "contact": "xxx",
                "mobile": "xxxxxxxxx",
                "goods": [{
                    "goodsName": "货物",
                    "pack": "包",
                    "num": "1",
                    "weight": "2",
                    "volume": "3",
                    "billingMethod": "体积（元/立方米）",
                    "pc": "123.01",
                    "sum": "223.71"
                }]
            }],
            "end": [{
                "addrName": "单位2",
                "contact": "xxx",
                "mobile": "xxxxxxxxx",
                "goods": [{
                    "goodsName": "货物",
                    "pack": "包",
                    "num": "4",
                    "weight": "5",
                    "volume": "6",
                    "billingMethod": "包车（车型）",
                    "pc": "323.11",
                    "sum": "423.72"
                }]
            }],
            "guidingPrice": "852.62"
        }*/
        // 准备全局字段数据：车长、车型、运输方式、紧急程度、调度组、开票类型、结算方式、包装、计价方式
        Map<String, String> carLenDict = dictService.selectDictDataByType("car_len").stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue));
        Map<String, String> carTypeDict = dictService.selectDictDataByType("car_type").stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue));
        Map<String, String> transCodeDict = dictService.selectDictDataByType("trans_code").stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue));
        Map<String, String> urgentLevelDict = dictService.selectDictDataByType("urgent_level").stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue));
        Map<String, Long> transLineMap = deptService.selectDeptByParentIds(DeptIdConstant.getDispatcherDeptId()).stream().collect(Collectors.toMap(SysDept::getDeptName, SysDept::getDeptId));
        Map<String, String> billingTypeDict = dictService.selectDictDataByType("billing_type").stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue));
        Map<String, String> balaTypeDict = dictService.selectDictDataByType("bala_type").stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue));
        Map<String, String> packageTypeDict = dictService.selectDictDataByType("package_type").stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue));
        Map<String, Integer> billingMethodMap = BillingMethod.getAllToMap().stream().collect(Collectors.toMap(map -> (String) map.get("context"), map -> (Integer) map.get("value")));

        // 下5个参数长度要一致；第一步校验：主要字段（统一校验必填字段、字典标签转键值）
        String[] fields = {"custAbbr", "reqDeliDate", "reqArriDate", "carLenName", "carTypeName", "transName",
                "urgentLevelName", "transLineName", "billingTypeName", "balaTypeName", "billingMethod", "unitPrice"}; // 第一步的字段
        boolean[] required = {true, true, true, true, true, true, true, true, false, true, true, true}; // 是否必填
        String[] fieldNames = {"客户简称", "要求提货日期", "要求到货日期", "车长", "车型", "运输方式", "紧急程度", "调度组", "开票类型", "结算方式", "计价方式", "单价"}; // 用于错误信息显示
        Map[] dicts = {null, null, null, carLenDict, carTypeDict, transCodeDict, urgentLevelDict, transLineMap, billingTypeDict, balaTypeDict, billingMethodMap, null}; // 用于匹配字段对应字典
        String[] dictValueFields = {null, null, null, "carLen", "carType", "transCode", "urgentLevel", "transLineId", "billingType", "balaType", "billingMethod", null}; // 用于存字典匹配出的键值
        StringBuilder errMsg = new StringBuilder();
        for (int i = 0; i < invoiceList.size(); i++) {
            StringBuilder rowErr = new StringBuilder();
            Map<String, Object> item = invoiceList.get(i);
            for (int j = 0; j < fields.length; j++) {
                String s = (String) item.get(fields[j]); // label
                if (StringUtils.isBlank(s)) {
                    if (required[j]) {
                        if (!fields[j].equals("unitPrice") || !fetchUnitPrice) {
                            rowErr.append("、").append(fieldNames[j]).append("不能为空");
                        }
                    }
                } else if (dicts[j] != null) {
                    item.put(dictValueFields[j], dicts[j].get(s)); // put(字段, dictValue)
                    if (item.get(dictValueFields[j]) == null) {
                        rowErr.append("、").append(fieldNames[j]).append("所填的“" + s + "”未匹配到字典");
                    }
                }
            }

            //collectAmount	到付应收金额
            String balaTypeName = (String) item.get("balaTypeName");
            if (StringUtils.isNotBlank(balaTypeName)) {
                String balaType = balaTypeDict.get(balaTypeName);
                // 2、6的时候，到付应收金额必填
                if (balaType.equals("2") || balaType.equals("5") || balaType.equals("6")) {
                    String collectAmount = (String) item.get("collectAmount");
                    if (StringUtils.isBlank(collectAmount)) {
                        rowErr.append("、到付应收金额不能为空");
                    }
                }
            }

            final Integer billingMethod = (Integer) item.get("billingMethod");
            if (Objects.equals(billingMethod, 7) || Objects.equals(billingMethod, 8) || Objects.equals(billingMethod, 9)) {
                final String mileage = (String) item.get("mileage");
                if (StringUtils.isBlank(mileage)) {
                    rowErr.append("、公里数不能为空");
                }
            }

            if (rowErr.length() > 0) {
                if (errMsg.length() > 0) {
                    errMsg.append("；<br>");
                }
                errMsg.append("第" + (i + 1) + "条发货单：").append(rowErr.deleteCharAt(0));
            }
        }

        if (errMsg.length() > 0) {
            return AjaxResult.error(errMsg.toString());
        }

//        Calendar calendar = Calendar.getInstance();
//        calendar.set(Calendar.HOUR_OF_DAY, 0);
//        calendar.set(Calendar.MINUTE, 0);
//        calendar.set(Calendar.SECOND, 0);
//        calendar.set(Calendar.MILLISECOND, 0);
//        Date today = calendar.getTime();
//        Date yestoday = new Date(today.getTime() - 24l * 60l * 60l * 1000l);
        Date yesterday = DateUtil.beginOfDay(DateUtil.yesterday());
        Date today = DateUtil.beginOfDay(new Date());

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm");

        // 第二步校验：根据客户关联出：结算客户、APP联系人、手机、驻场组...
        for (int i = 0; i < invoiceList.size(); i++) {
            StringBuilder rowErr = new StringBuilder();
            Map<String, Object> item = invoiceList.get(i);
            String custAbbr = (String) item.get("custAbbr");
            List<Map<String, Object>> custInfoList = invoiceImportMapper.customerInfoForInvoice(custAbbr);
            if (custInfoList.size() == 0) {
                rowErr.append("、未找到“").append(custAbbr).append("”对应的客户");
            } else if (custInfoList.size() > 1) {
                rowErr.append("、找到").append(custInfoList.size()).append("条“").append(custAbbr).append("”对应的客户");
            } else {
                Map<String, Object> custInfo = custInfoList.get(0);
                if (item.get("billingType") != null) { // 前端录入了票点，去除客户自带的票点，不然会覆盖前端选择的票点
                    custInfo.remove("billingType");
                }
                // {customerId,custCode,custName,custAbbr,salesDept,balaDept,stationDept,balaCorp,psndoc,
                //  billingCorp,appDeliContact,appDeliMobile,crtGuidePrice,balaCustomerId,balaCode,balaName,handlingCharges,handlingChargesType,billingType,operateCorp,
                //  custSalesId,custSalesName,mgmtDeptId,mgmtDeptName}
                item.putAll(custInfo);
            }
            Date reqDeliDate = sdf.parse((String) item.get("reqDeliDate"));
            item.put("reqDeliDate", reqDeliDate);
            Date reqArriDate = sdf.parse((String) item.get("reqArriDate"));
            if (reqDeliDate.before(yesterday)) {
                rowErr.append("、提货时间不能在昨天之前");
            } else if (reqArriDate.before(reqDeliDate)) {
                rowErr.append("、到货时间不能在提货时间之前");
            }
            item.put("reqArriDate", reqArriDate);
            if (rowErr.length() > 0) {
                if (errMsg.length() > 0) {
                    errMsg.append("；<br>");
                }
                errMsg.append("第" + (i + 1) + "条发货单：").append(rowErr.deleteCharAt(0));
            }
        }
        if (errMsg.length() > 0) {
            return AjaxResult.error(errMsg.toString());
        }

        // 第三部校验：货物、路线等是否存在，必填字段、关联出发货、收货点的联系人、省市区等
        for (int i = 0; i < invoiceList.size(); i++) {
            Map<String, NWV> goodsStartQuentity = new HashMap<>();
            Map<String, NWV> goodsEndQuentity = new HashMap<>();

            BigDecimal costAmount = BigDecimal.ZERO;
            //BigDecimal costAmount = new BigDecimal((String) invoiceList.get(i).get("costAmount"));

            BigDecimal numCount = BigDecimal.ZERO;
            BigDecimal weightCount = BigDecimal.ZERO;
            BigDecimal volumeCount = BigDecimal.ZERO;
            String customerId = (String) invoiceList.get(i).get("customerId");
            StringBuilder rowErr = new StringBuilder();
            Set<String> addrNames = new LinkedHashSet<>(); // 收集地址名称，统一校验
            Set<String> goodsNames = new LinkedHashSet<>(); // 收集货品，统一校验
            Set<String> custOrdernoGoodsNames = new LinkedHashSet<>();
            List<?> start = (List<?>) invoiceList.get(i).get("start");
            for (int j = 0; j < start.size(); j++) {
                Map<String, Object> item = (Map<String, Object>) start.get(j);
                if (StringUtils.isNotBlank((String) item.get("addrName"))) {
                    addrNames.add((String) item.get("addrName"));
                }
                List<Map<String, Object>> goods = (List<Map<String, Object>>) item.get("goods");
                for (int k = 0; k < goods.size(); k++) {
                    String goodsName = (String) goods.get(k).get("goodsName");
                    if (StringUtils.isNotBlank(goodsName)) {
                        goodsNames.add(goodsName);
                        if (StringUtils.isNotBlank((String) goods.get(k).get("custOrderno"))) {
                            custOrdernoGoodsNames.add(goodsName + " " + goods.get(k).get("custOrderno"));
                        }
                    }
                }
            }
            // 当前客户的对应的货品
            List<Map<String, Object>> goods_match = invoiceImportMapper.listCustomerGoods(customerId, goodsNames);
            //[{goodsId,goodsCode,goodsName,goodsType,goodsCharacter,goodsTypeName}]

            // 发货方地址校验
            List<Map<String, Object>> start_match = invoiceImportMapper.listCustomerAddr(customerId, addrNames, "1");
            for (int j = 0; j < start.size(); j++) {
                Map<String, Object> item = (Map<String, Object>) start.get(j);
                item.put("customerId", customerId);
                AjaxResult ar = this.checkAddreseUnique(item, start_match, "1");
                if (ar.getCode() == 0) {
                    item.putAll((Map<String, Object>) ar.getData());
                } else {
                    rowErr.append("、").append(ar.getMsg());
                }
                // 校验发货货品
                List<Map<String, Object>> goods = (List<Map<String, Object>>) item.get("goods");
                for (int k = 0; k < goods.size(); k++) {
                    Map<String, Object> goodsItem = goods.get(k);
                    AjaxResult ar2 = this.checkGoods(goodsItem, goods_match);
                    if (ar2.getCode() == 0) {
                        goodsItem.putAll((Map<? extends String, ?>) ar2.getData());
                    } else {
                        rowErr.append("、").append(ar2.getMsg());
                    }
                    String goodsName = (String) goodsItem.get("goodsName");
                    if (StringUtils.isNotBlank(goodsName)) {
                        String custOrderno = (String) goodsItem.get("custOrderno");
                        if (StringUtils.isNotBlank(custOrderno)) {
                            goodsName = goodsName + " " + custOrderno;
                        }
                        NWV nwv = goodsStartQuentity.get(goodsName);
                        if (nwv == null) {
                            nwv = new NWV();
                            goodsStartQuentity.put(goodsName, nwv);
                        }
                        String pack = (String) goodsItem.get("pack");
                        if (StringUtils.isBlank(pack)) {
                            rowErr.append("、").append("发货包装未填");
                        } else {
                            String packId = packageTypeDict.get(pack);
                            if (packId == null) {
                                rowErr.append("、").append("发货包装“" + pack + "”匹配失败");
                            } else {
                                goodsItem.put("packId", packId);
                            }
                        }
                        String num = (String) goodsItem.get("num");
                        if (StringUtils.isNotBlank(num)) {
                            numCount = numCount.add(new BigDecimal(num));
                        }
                        String weight = (String) goodsItem.get("weight");
                        if (StringUtils.isBlank(weight)) {
                            rowErr.append("、").append("发货重量不能为空");
                        } else {
                            weightCount = weightCount.add(new BigDecimal(weight));
                        }
                        String volume = (String) goodsItem.get("volume");
                        if (StringUtils.isNotBlank(volume)) {
                            volumeCount = volumeCount.add(new BigDecimal(volume));
                        }
                        nwv.add(num, weight, volume);

//                        String billingMethod = (String) goodsItem.get("billingMethod");
//                        if (StringUtils.isBlank(billingMethod)) {
//                            rowErr.append("、").append("发货计价方式未填");
//                        } else {
//                            Integer bm = billingMethodMap.get(billingMethod);
//                            if (bm == null) {
//                                rowErr.append("、").append("发货计价方式“" + billingMethod + "”匹配失败");
//                            } else {
//                                goodsItem.put("billingMethod", bm);
//                            }
//                        }
                        //String pc = (String) goodsItem.get("pc");
//                        String sum = (String) goodsItem.get("sum");
//                        if (StringUtils.isBlank(sum)) {
//                            rowErr.append("、").append("请输入发货金额");
//                        } else {
//                            costAmount = costAmount.add(new BigDecimal(sum));
//                        }
                    }
                }
            }

            // 根据计价方式和单价计算costAmount
            Integer billingMethod = (Integer) invoiceList.get(i).get("billingMethod");
            if (!fetchUnitPrice) { // 单价直接用合同价时，不计算总价，后续方法直接赋值
                BigDecimal unitPrice = new BigDecimal((String) invoiceList.get(i).get("unitPrice"));
                if (Objects.equals(billingMethod, 1)) {//重量（元/吨）
                    costAmount = unitPrice.multiply(weightCount).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (Objects.equals(billingMethod, 2)) {//体积（元/立方米）
                    costAmount = unitPrice.multiply(volumeCount).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (Objects.equals(billingMethod, 3)) {//包车（车型）
                    costAmount = unitPrice;
                } else if (Objects.equals(billingMethod, 4)) {//往返包车（车型）
                    costAmount = unitPrice;
                } else if (Objects.equals(billingMethod, 5)) {//件（元/件）
                    costAmount = unitPrice.multiply(numCount).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (Objects.equals(billingMethod, 7)) {//单价（元/公里）
                    BigDecimal mileage = new BigDecimal((String) invoiceList.get(i).get("mileage"));
                    costAmount = unitPrice.multiply(mileage).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (Objects.equals(billingMethod, 8)) {//单价（元/重量/公里）
                    BigDecimal mileage = new BigDecimal((String) invoiceList.get(i).get("mileage"));
                    costAmount = unitPrice.multiply(mileage).multiply(weightCount).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (Objects.equals(billingMethod, 9)) {//单价（元/件/公里）
                    BigDecimal mileage = new BigDecimal((String) invoiceList.get(i).get("mileage"));
                    costAmount = unitPrice.multiply(mileage).multiply(numCount).setScale(2, BigDecimal.ROUND_HALF_UP);
                }
            }

            String collectAmount = (String) invoiceList.get(i).get("collectAmount");
            if (StringUtils.isNotBlank(collectAmount)) {
                if (new BigDecimal(collectAmount).compareTo(costAmount) > 0) {
                    // fetchUnitPrice=true时，此时会有问题，costAmount需要先取合同价作为unitPrice计算
                    rowErr.append("、到付应收(").append(collectAmount).append(")不能超过发货金额总和：").append(costAmount);
                }
            }

            invoiceList.get(i).put("costAmount", costAmount);
            invoiceList.get(i).put("numCount", numCount);
            invoiceList.get(i).put("weightCount", weightCount);
            invoiceList.get(i).put("volumeCount", volumeCount);

            addrNames = new LinkedHashSet<>();
            List<?> end = (List<?>) invoiceList.get(i).get("end");
            for (int j = 0; j < end.size(); j++) {
                Map<String, Object> item = (Map<String, Object>) end.get(j);
                if (StringUtils.isNotBlank((String) item.get("addrName"))) {
                    addrNames.add((String) item.get("addrName"));
                }
            }
            // 收货方地址校验
            List<Map<String, Object>> end_match = invoiceImportMapper.listCustomerAddr(customerId, addrNames, "0");
            for (int j = 0; j < end.size(); j++) {
                Map<String, Object> item = (Map<String, Object>) end.get(j);
                item.put("customerId", customerId);
                AjaxResult ar = this.checkAddreseUnique(item, end_match, "0");
                if (ar.getCode() == 0) {
                    item.putAll((Map<String, Object>) ar.getData());
                } else {
                    rowErr.append("、").append(ar.getMsg());
                }
                // 收货货品是否存在与发货货品中
                List<Map<String, Object>> goods = (List<Map<String, Object>>) item.get("goods");
                for (int k = 0; k < goods.size(); k++) {
                    Map<String, Object> goodsItem = goods.get(k);
                    // "goodsName", "pack", "num", "weight", "volume", "billingMethod", "pc", "sum"
                    String custOrdernoGoodsName = (String) goodsItem.get("goodsName");
                    final String custOrderno = (String) goodsItem.get("custOrderno");
                    if (StringUtils.isNotBlank(custOrderno)) {
                        custOrdernoGoodsName = custOrdernoGoodsName + " " + custOrderno;
                    }
                    if (!custOrdernoGoodsNames.contains(custOrdernoGoodsName)) {
                        rowErr.append("、收货货品【").append(custOrdernoGoodsName).append("】未存在于发货货品中");
                    } else {
                        AjaxResult ar2 = this.checkGoods(goodsItem, goods_match);
                        if (ar2.getCode() == 0) {
                            goodsItem.putAll((Map<? extends String, ?>) ar2.getData());
                        } else {
                            // 不判断收货货品有效性，由发货货品有效性控制
                        }
                    }

                    if (StringUtils.isNotBlank(custOrdernoGoodsName)) {
                        NWV nwv = goodsEndQuentity.get(custOrdernoGoodsName);
                        if (nwv == null) {
                            nwv = new NWV();
                            goodsEndQuentity.put(custOrdernoGoodsName, nwv);
                        }
                        String pack = (String) goodsItem.get("pack");
                        if (StringUtils.isBlank(pack)) {
                            rowErr.append("、").append("收货包装未填");
                        } else {
                            String packId = packageTypeDict.get(pack);
                            if (packId == null) {
                                rowErr.append("、").append("收货包装“" + pack + "”匹配失败");
                            } else {
                                goodsItem.put("packId", packId);
                            }
                        }
                        String num = (String) goodsItem.get("num");
                        String weight = (String) goodsItem.get("weight");
                        String volume = (String) goodsItem.get("volume");
                        nwv.add(num, weight, volume);
                        if (StringUtils.isBlank(weight)) {
                            rowErr.append("、").append("收货重量不能为空");
                        }
//                        String billingMethod = (String) goodsItem.get("billingMethod");
//                        if (StringUtils.isBlank(billingMethod)) {
//                            rowErr.append("、").append("收货计价方式未填");
//                        } else {
//                            Integer bm = billingMethodMap.get(billingMethod);
//                            if (bm == null) {
//                                rowErr.append("、").append("收货计价方式“" + billingMethod + "”匹配失败");
//                            } else {
//                                goodsItem.put("billingMethod", bm);
//                            }
//                        }
                        //String pc = (String) goodsItem.get("pc");
//                        String sum = (String) goodsItem.get("sum");
//                        if (StringUtils.isBlank(sum)) {
//                            rowErr.append("、").append("请输入收货金额");
//                        }
                    }
                }
            }

            // 第四步校验：判断发货、收货量是否一致
            Set<Map.Entry<String, NWV>> setStart = goodsStartQuentity.entrySet();
            for (Map.Entry<String, NWV> entry : setStart) {
                if (!entry.getValue().eq(goodsEndQuentity.get(entry.getKey()))) {
                    rowErr.append("、【").append(entry.getKey()).append("】发货量与收货量不一致");
                }
            }

            if (rowErr.length() > 0) {
                if (errMsg.length() > 0) {
                    errMsg.append("；<br>");
                }
                errMsg.append("第" + (i + 1) + "条发货单：").append(rowErr.deleteCharAt(0));
            }

        }
        if (errMsg.length() > 0) {
            // 没有throw出去是调用了高德，防止下次导入再解析一遍
            return AjaxResult.error(errMsg.toString());
            //throw new BusinessException(errMsg.toString());
        }

        /*Map<String, GuidePriceDetail> cache = new LinkedHashMap<>();

        for (int i = 0; i < invoiceList.size(); i++) {
            StringBuilder rowErr = new StringBuilder();
            // 第五步校验：判断是否多装多卸并保存，非多装多卸时，关联出指导价，如果关联不出指导价，返回错误
            Map<String, Object> row = invoiceList.get(i);
            Integer crtGuidePrice = (Integer) row.get("crtGuidePrice");
            List<Map<String, Object>> start = (List<Map<String, Object>>) row.get("start");
            List<Map<String, Object>> end = (List<Map<String, Object>>) row.get("end");

            Boolean isMultiple = start.size() != 1 || end.size() != 1; // 是否多装多卸
            String guidingPrice = (String) row.get("guidingPrice");

            if (FULL_VEHICLE_MAP.containsKey(row.get("transCode")) && !isMultiple && crtGuidePrice == 1) {
                // 获取默认指导价
                Map<String, String> param = new LinkedHashMap<>();
                param.put("pricingMethod", "1");
                param.put("customerId", (String) row.get("customerId"));
                param.put("carType", (String) row.get("carType"));
                param.put("carLen", (String) row.get("carLen"));
                param.put("transType", (String) row.get("transCode"));
                param.put("deliAreaId", (String) start.get(0).get("areaId"));
                param.put("arriAreaId", (String) end.get(0).get("areaId"));
                GuidePriceDetail gpd = this.getCachedGuidingPrice(cache, param);
                if (gpd != null) {
                    BigDecimal oldGuidingPrice = gpd.getGuidingPrice();
                    if (StringUtils.isBlank(guidingPrice)) {
                        guidingPrice = oldGuidingPrice.toString();
                        row.put("guidingPrice", guidingPrice);
                    } else {
                        if (gpd.getCheckStatus() == 0 || ((gpd.getStartDate() == null || !gpd.getStartDate().after(today)) && (gpd.getEndDate() == null || !gpd.getEndDate().before(today)))) {
                            if (oldGuidingPrice.compareTo(new BigDecimal(guidingPrice)) != 0) {
                                if (StringUtils.isNotBlank(gpd.getGuidePriceDetailId())) {
                                    rowErr.append("、审核中或在有效期内的指导价(").append(oldGuidingPrice).append(")不允许修改");
                                } else {
                                    rowErr.append("、指导价").append(guidingPrice).append("与上方同（客户|车长|车型|运输方式|收发货地区）发货单指导价（").append(oldGuidingPrice).append("）冲突");
                                }
                            }
                        }
                    }
                    String guidePriceDetailId = gpd.getGuidePriceDetailId();
                    if (StringUtils.isNotBlank(guidePriceDetailId)) { // 临时存放的指导价没有该主键字段
                        row.put("params", new HashMap() {{
                            put("guidePriceDetailId", guidePriceDetailId);
                        }});
                    }
                } else if (StringUtils.isNotBlank(guidingPrice)) {
                    this.fetchCachedGuidingPrice(cache, param, guidingPrice);
                }
                if (StringUtils.isBlank(guidingPrice) || new BigDecimal(guidingPrice).compareTo(BigDecimal.ZERO) <= 0) {
//                    rowErr.append("、请填入大于0的指导价");
                }
            }

            if (rowErr.length() > 0) {
                if (errMsg.length() > 0) {
                    errMsg.append("；<br>");
                }
                errMsg.append("第" + (i + 1) + "条发货单：").append(rowErr.deleteCharAt(0));
            }
        }

        if (errMsg.length() > 0) {
            return AjaxResult.error(errMsg.toString());
        }*/

        List<Invoice> data = new ArrayList<>(); // 记录保存成功的发货单，用于推送消息
        // 第六步：保存数据
        for (int i = 0; i < invoiceList.size(); i++) {
            Map<String, Object> item = invoiceList.get(i);
            Invoice invoice = null;
            try {
                invoice = this.convertImportMapToInvoice(item, fetchUnitPrice);
            } catch (RuntimeException e) {
                log.error("map转发货单异常：", e);
                throw new RuntimeException("第" + (i + 1) + "条发货单：" + (e.getMessage() == null ? e.getClass().getSimpleName() : e.getMessage()));
            }
            // 指导价处理
//            if (invoice.getIsMultiple() == 0) {
//                //置空
//                invoice.setGuidingPrice(null);
//                //线路、新增指导价
//                generateTransLineAndGuide(invoice.getDeliProvinceId(), invoice.getDeliProName(), invoice.getDeliCityId()
//                        , invoice.getDeliCityName(), invoice.getDeliAreaId(), invoice.getDeliAreaName()
//                        , invoice.getArriProvinceId(), invoice.getArriProName(), invoice.getArriCityId()
//                        , invoice.getArriCityName(), invoice.getArriAreaId(), invoice.getArriAreaName(), invoice.getTransCode()
//                        , invoice.getCarLen(), invoice.getCarLenName(), invoice.getCarType(), invoice.getCarTypeName(), true
//                        , invoice.getCostPrice(), "invoiceService.importBatch");
//            }

            data.add(invoice);
            // 保存发货单
            invoiceMapper.insertSelective(invoice);
            // 保存发货单货品
            List<InvPackGoods> invPackGoodsList = invoice.getInvPackGoodsList();
            for (int j = 0; j < invPackGoodsList.size(); j++) {
                invPackGoodsMapper.insertInvPackGoods(invPackGoodsList.get(j));
            }

            // 保存发货、收货地址与货品
            List<MultipleShippingAddress> multipleShippingAddressesList = invoice.getShippingAddressList();
            for (int j = 0; j < multipleShippingAddressesList.size(); j++) {
                multipleShippingAddressMapper.insertSelective(multipleShippingAddressesList.get(j));
                //插入货品
                for (MultipleShippingGoods multipleShippingGoods : multipleShippingAddressesList.get(j).getShippingGoodsList()) {
                    multipleShippingGoodsMapper.insertSelective(multipleShippingGoods);
                }
            }

            // 第三方费用：装卸费
            //String handlingChargesType = (String) invoiceList.get(i).get("handlingChargesType");
            //BigDecimal handlingCharges = (BigDecimal) invoiceList.get(i).get("handlingCharges");
            //this.handlingCharges(invoice, handlingChargesType, handlingCharges);
        }
        //int x = 1 / 0;
        return AjaxResult.success(data);
    }

    @Override
    @DataScope(deptAlias = "t1.sales_dept,t.sales_dept", userAlias = "t.psndoc")
    public List<InvoiceLtlVO> selectInvoiceLtlAllList(InvoiceLtlVO invoiceLtlVO) {
        List<InvoiceLtlVO> invoicesLtlList = invoiceMapper.selectInvoiceLtlList(invoiceLtlVO);

//        for (InvoiceLtlVO invoice : invoicesLtlList) {
//            //查询发货单下所有委托单
//            List<Entrust> entrustList = entrustMapper.selectEntrustListByInvoiceId(invoice.getInvoiceId());
//
//            //将委托单分类成 提货段/干线段/送货段  0提货段  1干线段  2送货段
//            List<InvoiceLtlVO.ShippingSectionVO> shippingSectionList = new ArrayList<>();
//            shippingSectionList.add(packageShippingSection(entrustList, 0));
//            shippingSectionList.add(packageShippingSection(entrustList, 1));
//            shippingSectionList.add(packageShippingSection(entrustList, 2));
//            invoice.setShippingSectionList(shippingSectionList);
//
//            List<ReceiveDetailVO> receiveDetailVOS = receiveDetailService.selectReceiveByInvoiceId(invoice.getInvoiceId());
//
//            //送货费
//            BigDecimal transFeeCount = receiveDetailVOS.stream()
//                    .filter(x -> "1".equals(x.getFreeType()) && "22".equals(x.getCostTypeOnWay()))
//                    .map(ReceiveDetailVO::getTransFeeCount)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            invoice.setDeliveryFee(transFeeCount);
//
//
//        }
        return invoicesLtlList;
    }

    @Override
    public List<PayManageInvoiceVO> selectPayManageInvoiceVOList(PayManageInvoiceVO payManageInvoiceVO) {
        List<PayManageInvoiceVO> payManageInvoiceList = invoiceMapper
                .selectPayManageInvoiceVOListByPayCheckSheetId(payManageInvoiceVO);

        String net_profits_g7_rate = sysConfigService.selectConfigByKey("net_profits_g7_rate");
        String net_profits_oil_tax = sysConfigService.selectConfigByKey("net_profits_oil_tax");

        for (PayManageInvoiceVO manageInvoiceVO : payManageInvoiceList) {
            String invoiceId = manageInvoiceVO.getInvoiceId();

            Map<String, Object> dtl = receivableReconciliationMapper.netProfitsInfoA(invoiceId, null);

            BigDecimal ys = new BigDecimal(dtl.get("ys").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal yf = new BigDecimal(dtl.get("yf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal yfsf = new BigDecimal(dtl.get("yfsf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal dsf = new BigDecimal(dtl.get("dsf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal dsfsf = new BigDecimal(dtl.get("dsfsf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal ptf = new BigDecimal(dtl.get("ptf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);

            BigDecimal profit = NumberUtil.sub(ys, yf, yfsf, dsf, dsfsf, ptf);
            manageInvoiceVO.setYs(ys);
            manageInvoiceVO.setYf(yf);
            manageInvoiceVO.setYfsf(yfsf);
            manageInvoiceVO.setDsf(dsf);
            manageInvoiceVO.setDsfsf(dsfsf);
            manageInvoiceVO.setPtf(ptf);
            manageInvoiceVO.setProfit(profit);

//            Map<String, Object> netInfo = receivableReconciliationMapper.netProfitsInfo(invoiceId, net_profits_g7_rate, net_profits_oil_tax);
//            manageInvoiceVO.setNetInfo(netInfo);

            //货品
            InvPackGoods invPackGoods = new InvPackGoods();
            invPackGoods.setDelFlag(0);
            invPackGoods.setInvoiceId(invoiceId);
            List<InvPackGoods> packGoodsList = invPackGoodsMapper.selectInvPackGoodsList(invPackGoods);
            String goodsName = packGoodsList.stream().map(InvPackGoods::getGoodsName).collect(Collectors.joining(","));
            manageInvoiceVO.setGoodsName(goodsName);

//            //应付金额 取成本分摊金额
//            List<Allocation> allocationList = allocationMapper.selectAllocationListByInvoiceId(invoiceId);
//            BigDecimal payAmount = allocationList.stream()
//                    .map(Allocation::getCostShare)
//                    .filter(Objects::nonNull)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            manageInvoiceVO.setPayAmount(payAmount);

//            //应收金额
//            List<ReceiveDetailVO> receiveDetails = receiveDetailMapper.selectReceiveByInvoiceId(invoiceId);
//            BigDecimal sumReceiveDetail = receiveDetails.stream()
//                    .map(x -> x.getAdjustAmount() == null ? x.getTransFeeCount() : x.getAdjustAmount())
//                    .filter(Objects::nonNull)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            manageInvoiceVO.setReceivableAmount(sumReceiveDetail);

            //三方金额
//            List<OtherFee> otherFeeList = otherFeeMapper.selectOtherFeeListByInvoiceId(invoiceId);
//            BigDecimal sumOtherFee = otherFeeList.stream()
//                    .map(OtherFee::getFeeAmount)
//                    .filter(Objects::nonNull)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            manageInvoiceVO.setOtherAmount(sumOtherFee);

            //对账单id
            manageInvoiceVO.setPayCheckSheetId(payManageInvoiceVO.getPayCheckSheetId());


            //回单
            List<SysUploadFile> sysUploadFiles = entrustWorkMapper.selectReceiptPicByInvoiceId(manageInvoiceVO.getInvoiceId());
            manageInvoiceVO.setReceiptUploadFiles(sysUploadFiles);

        }
        return payManageInvoiceList;
    }

    @Override
    public List<PayManageAllocationVO> selectPayManageAllocationVO(PayManageAllocationVO payManageAllocationVO) {
        List<PayManageAllocationVO> list = allocationMapper.selectPayManageAllocationVO(payManageAllocationVO);
        return list;
    }

    @Override
    @DataScope(deptAlias = "t1.bala_dept")
    public List<SettlementManageVO> selectSettlementManageVOList(SettlementManageVO settlementManageVO) {
        TimeInterval timer = DateUtil.timer();
        List<SettlementManageVO> list = invoiceMapper.selectSettlementManageVOList(settlementManageVO);

        logger.info("======================--结算管理列表SQL查询：" + timer.interval());

        List<String> attachNeedList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            SettlementManageVO manageVO = list.get(i);
            String invoiceId = manageVO.getInvoiceId();

            //查询净利润
//            String net_profits_g7_rate = configService.selectConfigByKey("net_profits_g7_rate");
//            String net_profits_oil_tax = configService.selectConfigByKey("net_profits_oil_tax");
//            Map<String, Object> netInfo = receivableReconciliationMapper.netProfitsInfo(invoiceId, net_profits_g7_rate, net_profits_oil_tax);

            /*Map<String, Object> dtl = receivableReconciliationMapper.netProfitsInfoA(invoiceId, null);

            BigDecimal ys = new BigDecimal(dtl.get("ys").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal yf = new BigDecimal(dtl.get("yf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal yfsf = new BigDecimal(dtl.get("yfsf").toString());
            BigDecimal dsf = new BigDecimal(dtl.get("dsf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal dsfsf = new BigDecimal(dtl.get("dsfsf").toString());
            BigDecimal ptf = new BigDecimal(dtl.get("ptf").toString());*/
            BigDecimal profit = NumberUtil.sub(manageVO.getReceivableAmount(), manageVO.getPayAmount(), manageVO.getYfsf(), manageVO.getOtherAmount(), manageVO.getDsfsf(), manageVO.getPtf());
            manageVO.setProfit(profit);
            //dtl.put("profit", profit);
            //manageVO.setNetInfo(dtl);

            /*List<ReceCheckSheet> checkSheetList = receCheckSheetMapper.selectReceCheckSheetByInvoiceId(manageVO.getInvoiceId());
            String receCheckSheetNo = checkSheetList.stream().map(ReceCheckSheet::getVbillno).distinct().collect(Collectors.joining(","));
            manageVO.setReceCheckSheetNo(receCheckSheetNo);*/

            //查询发货单货品
//            InvPackGoods invPackGoodsSel = new InvPackGoods();
//            invPackGoodsSel.setInvoiceId(invoiceId);
//            List<InvPackGoods> invPackGoodsList = invPackGoodsMapper.selectInvPackGoodsList(invPackGoodsSel);
//            if (invPackGoodsList != null && invPackGoodsList.size() > 0) {
                //取第一条的计价方式
                String billingMethod = manageVO.getBillingMethod();

                if (StringUtils.isNotBlank(billingMethod)) {
                    manageVO.setBillingMethod(BillingMethod.getContext(Integer.parseInt(billingMethod)));
                }

                //是否固定价
                Integer contractIsFixedPrice = manageVO.getContractIsFixedPrice();

//                Integer goodsCharacter = invPackGoodsList.get(0).getGoodsCharacter();
                //运费
                BigDecimal receivableAmountFreight = manageVO.getReceivableAmountFreight();
                if (contractIsFixedPrice != null && contractIsFixedPrice == 1) {
                    manageVO.setSystemAmount(receivableAmountFreight);
                } else {
                    /*if ("1".equals(billingMethod)) {
                        manageVO.setBillingMethod(BillingMethod.WEIGHT.getContext());
                    } else if ("2".equals(billingMethod)) {
                        manageVO.setBillingMethod(BillingMethod.VOLUME.getContext());
                    } else if ("3".equals(billingMethod)) {
                        manageVO.setBillingMethod(BillingMethod.BUS.getContext());
                    } else if ("4".equals(billingMethod)) {
                        manageVO.setBillingMethod(BillingMethod.DAY.getContext());
                    } else if ("5".equals(billingMethod)) {
                        manageVO.setBillingMethod(BillingMethod.PIECE.getContext());
                    } else if ("6".equals(billingMethod)) {
                        manageVO.setBillingMethod(BillingMethod.UNIT.getContext());
                    } else if ("7".equals(billingMethod)) {
                        manageVO.setBillingMethod(BillingMethod.UNIT_KM.getContext());
                    }*/
                    Double numTotal = manageVO.getNumCount();
                    Double weightTotal = manageVO.getWeightCount();
                    Double volumeTotal = manageVO.getVolumeCount();
                    Double mileage = manageVO.getMileage();
                    //合计件数重量体积
//                    for (InvPackGoods invPackGoods : invPackGoodsList) {
//                        numTotal = numTotal.add(BigDecimal.valueOf(invPackGoods.getNum()));
//                        weightTotal = weightTotal.add(BigDecimal.valueOf(invPackGoods.getWeight()));
//                        volumeTotal = volumeTotal.add(BigDecimal.valueOf(invPackGoods.getVolume()));
//                    }


//                    SearchContractPriceVO searchContractPriceVO = new SearchContractPriceVO();
//                    List<MultipleShippingAddress> multipleShippingAddresses = multipleShippingAddressMapper
//                            .selectListByInvoiceId(invoiceId);
//
//                    List<String> deliAreaIdList = multipleShippingAddresses.stream()
//                            .filter(x -> x.getAddressType() == 0
//                                    && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
//                            .map(MultipleShippingAddress::getAreaId)
//                            .collect(Collectors.toList());
//                    searchContractPriceVO.setDeliAreaIdList(deliAreaIdList);
//
//                    List<String> arriAreaIdList = multipleShippingAddresses.stream()
//                            .filter(x -> x.getAddressType() == 1
//                                    && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
//                            .map(MultipleShippingAddress::getAreaId)
//                            .collect(Collectors.toList());
//                    searchContractPriceVO.setArriAreaIdList(arriAreaIdList);
//
//                    searchContractPriceVO.setCustomerId(manageVO.getCustomerId());
//                    searchContractPriceVO.setCarLen(manageVO.getCarLen());
//                    searchContractPriceVO.setCarType(manageVO.getCarType());
//
//                    searchContractPriceVO.setBillingMethod(Integer.valueOf(billingMethod));
//                    searchContractPriceVO.setGoodsCharacter("15".equals(manageVO.getTransCode()) || "16".equals(manageVO.getTransCode()) ? "1" : "0");
//                    searchContractPriceVO.setNum(Convert.toBigDecimal(numTotal));
//                    searchContractPriceVO.setWeight(Convert.toBigDecimal(weightTotal));
//                    searchContractPriceVO.setVolume(Convert.toBigDecimal(volumeTotal));
//                    searchContractPriceVO.setMileage(manageVO.getMileage() == null ? BigDecimal.ZERO : BigDecimal.valueOf(manageVO.getMileage()));
//                    //合同价
//                    Map<String, String> price = getPrice(searchContractPriceVO);
//
//                    String totalPrice = price.get("price");
//                    if (StringUtils.isNotBlank(totalPrice)) {
//                        String isIncludeTax = price.get("isIncludeTax");
//                        BigDecimal contractAmount = new BigDecimal(totalPrice);
//
//                        //如果是不含税，则需要计算一下含税价
//                        if ("0".equals(isIncludeTax)) {
//                            SysDictData billingTypeDictData = sysDictDataMapper
//                                    .selectDictDataByTypeAndValue("billing_type", manageVO.getBillingType());
//                            BigDecimal rate = billingTypeDictData.getNumVal1();
//
//                            if (rate != null && rate.compareTo(BigDecimal.ZERO) != 0) {
//                                manageVO.setContractAmount(NumberUtil.round(NumberUtil.mul(contractAmount, rate), 2));
//                            }
//                        }else {
//                            manageVO.setContractAmount(contractAmount);
//                        }
//                    }
//
//                    String isFixedPrice = price.get("isFixedPrice");
//
//                    if (isFixedPrice != null && "1".equals(isFixedPrice)) {
//                        manageVO.setSystemAmount(receivableAmountFreight);
//                    }else {
                        if ("1".equals(billingMethod)) {
                            //按重量
                            if (weightTotal != null && weightTotal != 0) {

                                BigDecimal systemAmount = NumberUtil.div(receivableAmountFreight, weightTotal, 2);
                                manageVO.setSystemAmount(systemAmount);
                            }
                        } else if ("2".equals(billingMethod)) {
                            //按体积
                            if (volumeTotal != null && volumeTotal != 0) {
                                BigDecimal systemAmount = NumberUtil.div(receivableAmountFreight, volumeTotal, 2);
                                manageVO.setSystemAmount(systemAmount);
                            }
                        } else if ("5".equals(billingMethod)) {
                            //按件数
                            if (numTotal != null && numTotal != 0) {
                                BigDecimal systemAmount = NumberUtil.div(receivableAmountFreight, numTotal, 2);
                                manageVO.setSystemAmount(systemAmount);
                            }
                        } else if ("7".equals(billingMethod)) {
                            //公里数
                            if (mileage != null && mileage != 0) {
                                BigDecimal systemAmount = NumberUtil.div(receivableAmountFreight, mileage, 2);
                                manageVO.setSystemAmount(systemAmount);
                            }
                        } else {
                            manageVO.setSystemAmount(manageVO.getUnitPrice());
                        }

//                    }
                }
//            }



            //货品
            /* 集成到列表查询
            InvPackGoods invPackGoods = new InvPackGoods();
            invPackGoods.setDelFlag(0);
            invPackGoods.setInvoiceId(invoiceId);
            List<InvPackGoods> packGoodsList = invPackGoodsMapper.selectInvPackGoodsList(invPackGoods);
            String goodsName = packGoodsList.stream().map(InvPackGoods::getGoodsName).distinct().collect(Collectors.joining(","));
            manageVO.setGoodsName(goodsName);*/

            /*
             *  应付金额 取成本分摊金额
             */
/*
            List<Allocation> allocationList = allocationMapper.selectAllocationListByInvoiceId(invoiceId);
            BigDecimal payAmount = allocationList.stream()
                    .map(Allocation::getCostShare)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            manageVO.setPayAmount(payAmount);

            BigDecimal payAmountFreight = allocationList.stream()
                    .filter(x -> "0".equals(x.getFreeType()))
                    .map(Allocation::getCostShare)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            manageVO.setPayAmountFreight(payAmountFreight);
            BigDecimal payAmountOnTheWay = allocationList.stream()
                    .filter(x -> "1".equals(x.getFreeType()))
                    .map(Allocation::getCostShare)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            manageVO.setPayAmountOnTheWay(payAmountOnTheWay);

            //查询发货单对应的应付
            List<PayDetail> payDetailList = payDetailMapper.selectPayDetailListByInvoiceId(invoiceId);
            //已申请数量
            long applyCount = payDetailList.stream()
                    .filter(x -> PayDetailStatusEnum.APPLY.getValue() == x.getVbillstatus()).count();
            //是否存在单笔付款复核数据  0不存在 1存在
            manageVO.setIsPayDetailApply(applyCount > 0 ? 1 : 0);
*/

            //List<PayDetail> payDetailList = payDetailMapper.selectPayDetailListByInvoiceId(invoiceId);
            /*集成到列表
            BigDecimal payAmount = payDetailList.stream()
                    .map(PayDetail::getTransFeeCount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            manageVO.setPayAmount(payAmount);*/
            //运费
            /*集成到列表
            BigDecimal payAmountFreight = payDetailList.stream()
                    .filter(x -> "0".equals(x.getFreeType()))
                    .map(PayDetail::getTransFeeCount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            manageVO.setPayAmountFreight(payAmountFreight);*/
            //在途
            /*集成到列表
            BigDecimal payAmountOnTheWay = payDetailList.stream()
                    .filter(x -> "1".equals(x.getFreeType()))
                    .map(PayDetail::getTransFeeCount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            manageVO.setPayAmountOnTheWay(payAmountOnTheWay);*/

            //应付 运费集合(用于弹出显示)
            //前端懒加载 List<PayDetail> payFreightList = payDetailList.stream().filter(x -> "0".equals(x.getFreeType())).collect(Collectors.toList());
            //manageVO.setPayFreightList(payFreightList);
            //应付 在途费集合(用于弹出显示)
            //前端懒加载List<PayDetail> payOnTheWayList = payDetailList.stream().filter(x -> "1".equals(x.getFreeType())).collect(Collectors.toList());
            //manageVO.setPayOnTheWayList(payOnTheWayList);

            //已申请数量
            /*集成到列表
            long applyCount = payDetailList.stream()
                    .filter(x -> PayDetailStatusEnum.APPLY.getValue() == x.getVbillstatus()).count();
            //是否存在单笔付款复核数据  0不存在 1存在
            manageVO.setIsPayDetailApply(applyCount > 0 ? 1 : 0);*/

            //是否调整单
//            long count = payDetailList.stream().filter(x -> x.getIsAdjust() != null && x.getIsAdjust() == 1).count();
//            manageVO.setIsAdjust(count > 0 ? 1 : 0);

            /*
             * 应收金额
             */
            //集成到列表List<ReceiveDetailVO> receiveDetails = receiveDetailMapper.selectReceiveByInvoiceId(invoiceId);
            /*集成到列表
            BigDecimal sumReceiveDetail = receiveDetails.stream()
                    .map(x -> x.getAdjustAmount() == null ? x.getTransFeeCount() : x.getAdjustAmount())
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            manageVO.setReceivableAmount(sumReceiveDetail);*/

            /*集成到列表
            BigDecimal sumReceiveDetailFreight = receiveDetails.stream()
                    .filter(x -> "0".equals(x.getFreeType()))
                    .map(x -> x.getAdjustAmount() == null ? x.getTransFeeCount() : x.getAdjustAmount())
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            manageVO.setReceivableAmountFreight(sumReceiveDetailFreight);*/
            /*集成到列表
            BigDecimal sumReceiveDetailOnTheWay = receiveDetails.stream()
                    .filter(x -> "1".equals(x.getFreeType()))
                    .map(x -> x.getAdjustAmount() == null ? x.getTransFeeCount() : x.getAdjustAmount())
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            manageVO.setReceivableAmountOnTheWay(sumReceiveDetailOnTheWay);*/
            //前端懒加载 manageVO.setReceiveDetailList(receiveDetails);

            //是否调整单
            /* 集成到列表
            long count = receiveDetails.stream().filter(x -> x.getIsAdjust() != null && x.getIsAdjust() == 1).count();
            manageVO.setIsAdjust(count > 0 ? 1 : 0);*/

            //三方金额
            //List<OtherFee> otherFeeList = otherFeeMapper.selectOtherFeeListByInvoiceId(invoiceId);
            /* 集成到列表
            BigDecimal sumOtherFee = otherFeeList.stream()
                    .map(OtherFee::getFeeAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            manageVO.setOtherAmount(sumOtherFee);*/
            //前端懒加载 manageVO.setOtherFeeList(otherFeeList);

            /*
             * 回单
             */
            /*List<SysUploadFile> sysUploadFiles = new ArrayList<>();

            List<SysUploadFile> receiptUploadFiles = entrustWorkMapper.selectReceiptPicByInvoiceId(invoiceId);
            if (receiptUploadFiles != null && receiptUploadFiles.size() > 0) {
                sysUploadFiles.addAll(receiptUploadFiles);
            }
            //查询车队的回单
            List<Invoice> fleetInvoices = invoiceMapper.selectInvoiceByBizInvoiceId(invoiceId);
            for (Invoice fleetInvoice : fleetInvoices) {
                List<SysUploadFile> fleetReceiptFiles = entrustWorkMapper.selectReceiptPicByInvoiceId(fleetInvoice.getInvoiceId());

                if (fleetReceiptFiles != null && fleetReceiptFiles.size() > 0) {
                    sysUploadFiles.addAll(fleetReceiptFiles);
                }
            }*/

            attachNeedList.add(invoiceId);
            int batchSize = 200;
            if ((i + 1) % batchSize == 0) {
                List<SysUploadFileVO> sysUploadFiles = entrustWorkMapper.selectBizAndFleetReceiptPicByInvoiceId(attachNeedList.toArray(new String[attachNeedList.size()]));
                for (int j = i - batchSize + 1; j <= i; j++) {
                    SettlementManageVO t = list.get(j);
                    String iid = t.getInvoiceId();
                    String xid;
                    List<SysUploadFile> files = new ArrayList<>();
                    for (int k = 0; k < sysUploadFiles.size(); k++) {
                        xid = sysUploadFiles.get(k).getXid();
                        if (iid.equals(xid)) {
                            files.add(sysUploadFiles.remove(k));
                            k--;
                        } else if (files.size() > 0 || iid.compareTo(xid) < 0) { // 依赖于invoiceId升序排序
                            break;
                        }
                    }
                    t.setReceiptUploadFiles(files);
                }
                if (sysUploadFiles.size() > 0) {
                    throw new RuntimeException("混入了奇怪的东西");
                }
                attachNeedList.clear();
            }

            /*
             * 委托单数量
             */
            /* 集成到列表
            List<Entrust> entrustList = entrustMapper.selectEntrustListByInvoiceId(invoiceId);
            manageVO.setEntrustCount(entrustList.size());
            if (entrustList.size() == 1) {
                manageVO.setLotId(entrustList.get(0).getLotId()); // 由前端触发查询
            }*/


/*
            //
            List<Entrust> entrustList = entrustMapper.selectEntrustListByInvoiceId(invoiceId);
            //
            int size = entrustList.size();
            //已回单确认
            long receiptConfirmFlagCount = entrustList.stream()
                    .filter(x -> x.getReceiptConfirmFlag() != null && x.getReceiptConfirmFlag() == 1).count();
            //已上传回单
            long receiptStatusCount = entrustList.stream()
                    .filter(x -> x.getReceiptStatus() != null && x.getReceiptStatus() == 1).count();

            if (receiptConfirmFlagCount > 0 && receiptConfirmFlagCount == size) {
                manageVO.setReceiptStatus(0);
            } else if (receiptConfirmFlagCount > 0 && receiptConfirmFlagCount < size) {
                manageVO.setReceiptStatus(1);
            } else if (receiptStatusCount > 0 && receiptStatusCount == size) {
                manageVO.setReceiptStatus(2);
            } else if (receiptStatusCount > 0 && receiptStatusCount < size) {
                manageVO.setReceiptStatus(3);
            } else {
                manageVO.setReceiptStatus(4);
            }
*/
        }

        logger.info("======================--净利润、货品、合同价、系统价查询：" + timer.interval());

        if (attachNeedList.size() > 0) {
            List<SysUploadFileVO> sysUploadFiles = entrustWorkMapper.selectBizAndFleetReceiptPicByInvoiceId(attachNeedList.toArray(new String[attachNeedList.size()]));
            for (int j = list.size() - attachNeedList.size(); j < list.size(); j++) {
                SettlementManageVO t = list.get(j);
                String iid = t.getInvoiceId();
                String xid;
                List<SysUploadFile> files = new ArrayList<>();
                for (int k = 0; k < sysUploadFiles.size(); k++) {
                    xid = sysUploadFiles.get(k).getXid();
                    if (iid.equals(xid)) {
                        files.add(sysUploadFiles.remove(k));
                        k--;
                    } else if (files.size() > 0 || iid.compareTo(xid) < 0) { // 依赖于invoiceId升序排序
                        break;
                    }
                }
                t.setReceiptUploadFiles(files);
            }
            if (sysUploadFiles.size() > 0) {
                throw new RuntimeException("混入了奇怪的东西");
            }
        }

        Subject subject = ShiroUtils.getSubject();

        if (!subject.isPermitted("tms:finance:settlement_manage:list_profit")) {
            for (SettlementManageVO manageVO : list) {
                manageVO.setPayAmountFreight(null);
                manageVO.setPayAmountOnTheWay(null);
                manageVO.setOtherAmount(null);
                manageVO.setPtf(null);
                manageVO.setYfsf(null);
                manageVO.setDsfsf(null);
                manageVO.setProfit(null);

            }

        }


        logger.info("======================--回单照片查询查询：" + timer.interval());
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult settlementManageCheck(SettlementCheckVO settlementCheckVO) {
        List<String> invoiceIds = settlementCheckVO.getInvoiceIds();
        Integer settlementCheck = settlementCheckVO.getSettlementCheck();

        for (String invoiceId : invoiceIds) {
            Invoice invoice = invoiceMapper.selectInvoiceById(invoiceId);

            Invoice invoiceUpdate = new Invoice();
            invoiceUpdate.setInvoiceId(invoiceId);

            if (1 == settlementCheck) {
                //审核通过
                invoiceUpdate.setSettlementCheck(1);
                invoiceMapper.updateInvoice(invoiceUpdate);
            } else {
                //审核通过
                invoiceUpdate.setSettlementCheck(0);
                invoiceMapper.updateInvoice(invoiceUpdate);
            }

            /*
             * 插入日志
             */
            SettlementCheckLog settlementCheckLog = new SettlementCheckLog();
            settlementCheckLog.setId(IdUtil.simpleUUID());

            settlementCheckLog.setInvoiceId(invoice.getInvoiceId());
            settlementCheckLog.setInvoiceNo(invoice.getVbillno());
            settlementCheckLog.setCheckStatus(1 == settlementCheck ? 1 : 0);
            settlementCheckLog.setCheckDate(new Date());
            settlementCheckLog.setCheckUserId(shiroUtils.getUserId().toString());
            settlementCheckLog.setCheckUserName(shiroUtils.getLoginName());
            settlementCheckLogMapper.insertSelective(settlementCheckLog);
        }

        return AjaxResult.success();
    }

    @Override
    @DataScope(deptAlias = "t9.bala_dept")
    public List<SettlementManageVO> selectSettlementManageVOListExport(SettlementManageVO settlementManageVO) {
        List<SettlementManageVO> list = invoiceMapper.selectSettlementManageVOListExport(settlementManageVO);

        for (SettlementManageVO manageVO : list) {
            List<ReceCheckSheet> checkSheetList = receCheckSheetMapper.selectReceCheckSheetByInvoiceId(manageVO.getInvoiceId());
            String receCheckSheetNo = checkSheetList.stream().map(ReceCheckSheet::getVbillno).distinct().collect(Collectors.joining(","));
            manageVO.setReceCheckSheetNo(receCheckSheetNo);

            Map<String, Object> dtl = receivableReconciliationMapper.netProfitsInfoA(manageVO.getInvoiceId(), null);

            BigDecimal ys = new BigDecimal(dtl.get("ys").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal yf = new BigDecimal(dtl.get("yf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal yfsf = new BigDecimal(dtl.get("yfsf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal dsf = new BigDecimal(dtl.get("dsf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal dsfsf = new BigDecimal(dtl.get("dsfsf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal ptf = new BigDecimal(dtl.get("ptf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal profit = NumberUtil.sub(ys, yf, yfsf, dsf, dsfsf, ptf);

            dtl.put("profit", profit);

            manageVO.setYfsf(yfsf);
            manageVO.setDsfsf(dsfsf);
            manageVO.setPtf(ptf);
            manageVO.setProfit(profit);

            BigDecimal profitRate = ys.compareTo(BigDecimal.ZERO) == 0
                    ? BigDecimal.ZERO : NumberUtil.round(NumberUtil.mul(NumberUtil.div(profit, ys), 100), 2);
            manageVO.setProfitRate(profitRate);

            List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveByInvoiceId(manageVO.getInvoiceId());
            BigDecimal ccf = receiveDetailVOS.stream()
                    .filter(x -> x.getCostTypeOnWay() != null && x.getCostTypeOnWay().equals("9") && x.getTransFeeCount() != null)
                    .map(ReceiveDetailVO::getTransFeeCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            manageVO.setCcf(ccf);

//            BigDecimal netcb = NumberUtil.add(manageVO.getNetyf(), manageVO.getNetptf(), manageVO.getNetdsf());
//            manageVO.setNetcb(netcb);
//
//
//            BigDecimal netlr = NumberUtil.sub(manageVO.getNetys(), netcb);
//            manageVO.setNetlr(netlr);
//
//            if (manageVO.getNetys() != null && manageVO.getNetys().compareTo(BigDecimal.ZERO) == 0) {
//                manageVO.setNetlrl(BigDecimal.ZERO);
//            } else {
//
//                BigDecimal netlrl = NumberUtil.div(netlr, manageVO.getNetys(), 4);
//                manageVO.setNetlrl(netlrl);
//            }
//            String invoiceId = manageVO.getInvoiceId();
//            //回单
//            List<SysUploadFile> sysUploadFiles = entrustWorkMapper.selectReceiptPicByInvoiceId(invoiceId);
//            manageVO.setReceiptUploadFiles(sysUploadFiles);
       }


        return list;
    }

    /**
     * 封装委托单信息  将委托单转换为 提货段/干线段/送货段
     *
     * @param entrustList 委托单
     * @param type        零担运段类型 0提货段  1干线段  2送货段
     * @return
     */
    private InvoiceLtlVO.ShippingSectionVO packageShippingSection(List<Entrust> entrustList, int type) {
        InvoiceLtlVO.ShippingSectionVO shippingSectionVO = new InvoiceLtlVO.ShippingSectionVO();
        shippingSectionVO.setLtlType(type);
        //车牌 如果多个 用逗号拼接
        shippingSectionVO.setCarno(entrustList.stream()
                .filter(x -> x.getLtlType() != null && x.getLtlType() == type)
                .map(Entrust::getCarno).distinct().collect(Collectors.joining(",")));
        //司机 如果多个 用逗号拼接
        shippingSectionVO.setDriverName(entrustList.stream()
                .filter(x -> x.getLtlType() != null && x.getLtlType() == type)
                .map(Entrust::getDriverName).distinct().collect(Collectors.joining(",")));
        //承运商 如果多个 用逗号拼接
        shippingSectionVO.setCarrName(entrustList.stream()
                .filter(x -> x.getLtlType() != null && x.getLtlType() == type)
                .map(Entrust::getCarrName).distinct().collect(Collectors.joining(",")));
        //金额 从成本分摊中取
        List<String> ids = entrustList.stream()
                .filter(x -> x.getLtlType() != null && x.getLtlType() == type)
                .map(Entrust::getEntrustId).collect(Collectors.toList());
        if (ids.size() > 0) {
            List<Allocation> allocationList = allocationMapper.selectAllocationListByEntrustIds(ids);
            shippingSectionVO.setFreight(allocationList.stream()
                    .map(Allocation::getCostShare)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        return shippingSectionVO;
    }

    /**
     *
     *
     * @param address
     * @param matchAble
     * @param addrType 1=发货方，0=收货方
     * @return
     */
    private AjaxResult checkAddreseUnique(Map<String, Object> address, List<Map<String, Object>> matchAble, String addrType) {
        String flag = addrType.equals("1") ? "发" : "收";
        /*
        "customerId": "*******"
        "addrName": "单位2",
        "contact": "xxx",
        "mobile": "xxxxxxxxx",
        "goods": [{
            "goodsName": "货物",
            "pack": "包",
            "num": "4",
            "weight": "5",
            "volume": "6",
            "isGetContractPrice":"是"
            "custOrderno": ""
        }]
        "address": "xxxxxx"
        */
        // {addrCode,addrName,provinceId,cityId,areaId,provinceName,cityName,areaName,detailAddr,contact,mobile,addressType}
        String addrName = (String) address.get("addrName");
        if (StringUtils.isBlank(addrName)) {
            return AjaxResult.error("未填写" + flag + "货方单位名称");
        }
        String contact = (String) address.get("contact");
        String mobile = (String) address.get("mobile");
        String addressStr = (String) address.get("address");
        int find = 0;
        Map<String, Object> last = null;
        for (int i = 0; i < matchAble.size(); i++) {
            String itemAddrName = (String) matchAble.get(i).get("addrName");
            if (itemAddrName.equals(addrName)) {
                boolean match = true;
                if (StringUtils.isNotBlank(contact)) {
                    match = contact.equals(matchAble.get(i).get("contact"));
                }
                if (match && StringUtils.isNotBlank(mobile)) {
                    match = mobile.equals(matchAble.get(i).get("mobile"));
                }
                if (match && StringUtils.isNotBlank(addressStr)) {
                    match = addressStr.endsWith((String) matchAble.get(i).get("detailAddr"));
                }
                if (match) {
                    find++;
                    last = matchAble.get(i);
                }
            }
        }
        List<String> params = new ArrayList<>();
        params.add(addrName);
        if (StringUtils.isNotBlank(contact)) {
            params.add(contact);
        }
        if (StringUtils.isNotBlank(mobile)) {
            params.add(mobile);
        }
        if (StringUtils.isNotBlank(addressStr)) {
            params.add(addressStr);
        }
        if (find == 0) {
            if (StringUtils.isNotBlank(addressStr)) {
                // 新增地址
                if (StringUtils.isBlank(contact) || StringUtils.isBlank(mobile)) {
                    return AjaxResult.error("缺少" + flag + "货方联系人或电话，生成地址失败");
                }
                final RfqEnquiryLine.Addr addr = rfqService.formatAddr(addressStr);
                String customerId = (String) address.get("customerId");
                CustAddressVO custAddress = this.initCustAddress(addrName, addr.getProvinceId(), addr.getCityId(), addr.getAreaId(),
                        addr.getProvinceName(), addr.getCityName(), addr.getAreaName(), addr.getDetailAddr(),
                        addrType, customerId, contact, mobile, "ipt-invoice", new Date(), shiroUtils.getUserId().toString());
                return AjaxResult.success(new HashMap<String, Object>() {{
                    put("addressId", custAddress.getAddressId());
                    put("addrCode", custAddress.getAddrCode());
                    put("addrName", addrName);
                    put("provinceId", addr.getProvinceId());
                    put("cityId", addr.getCityId());
                    put("areaId", addr.getAreaId());
                    put("provinceName", addr.getProvinceName());
                    put("cityName", addr.getCityName());
                    put("areaName", addr.getAreaName());
                    put("detailAddr", addr.getDetailAddr());
                    put("contact", contact.trim());
                    put("mobile", mobile.trim());
                }});
            }
            return AjaxResult.error("未匹配到【" + StringUtils.join(params, "、") + "】的" + flag + "货方");
        } else if (find > 1) {
            return AjaxResult.error("【" + StringUtils.join(params, "、") + "】匹配到多条" + flag + "货方，请补充联系人或电话");
        } else {
            return AjaxResult.success(last);
        }
    }

    /**
     * 新增客户地址客户
     *
     * @param provinceId
     * @param cityId
     * @param areaId
     * @param provinceName
     * @param cityName
     * @param areaName
     * @param detailAddr
     * @param type 1=发货方，0=收货方，2=收发货，3=中转站
     * @param customerId
     * @param contact
     * @param mobile
     */
    private CustAddressVO initCustAddress(String addrName, String provinceId, String cityId, String areaId,
                                 String provinceName, String cityName, String areaName, String detailAddr,
                                 String type, String customerId, String contact, String mobile, String scrId, Date time, String userId) {
        Address query = new Address();
        query.setDelFlag(0);
        query.setAddrName(addrName);
        query.setProvinceId(provinceId);
        query.setCityId(cityId);
        query.setAreaId(areaId);
        query.setDetailAddr(detailAddr.trim());
        final List<Address> addressList = addressMapper.getAddressList(query);
        String addressId;
        if (addressList.size() > 0) {
            addressId = addressList.get(0).getAddressId();
            query = addressList.get(0);
        } else {
            addressId = IdUtil.simpleUUID();
            query.setAddressId(addressId);
            query.setAddrType(type);
            query.setAddrName(addrName);
            query.setRegDate(time);
            query.setRegScrId(scrId);
            query.setRegUserId(userId);
            query.setCorDate(time);
            query.setCorScrId(scrId);
            query.setCorUserId(userId);
            query.setProvinceName(provinceName);
            query.setCityName(cityName);
            query.setAreaName(areaName);
            query.setCheckStatus(1);
            addressMapper.insertAddr(query);
        }
        CustAddressDto query2 = new CustAddressDto();
        query2.setCustomerId(customerId);
        query2.setAddressId(addressId);
        query2.setContact(contact.trim());
        query2.setMobile(mobile.trim());
        final List<CustAddressVO> custAddressVOS = custAddressMapper.selectCustAddressVOList(query2);
        if (custAddressVOS.size() > 0) {
            return custAddressVOS.get(0);
        }
        CustAddress custAddress = new CustAddress();
        custAddress.setCustomerId(customerId);
        custAddress.setAddressId(addressId);
        custAddress.setCustAddressId(IdUtil.simpleUUID());
        custAddress.setContactName(contact.trim());
        custAddress.setContactMobilephone(mobile.trim());
        custAddress.setRegDate(time);
        custAddress.setRegScrId(scrId);
        custAddress.setRegUserId(userId);
        custAddress.setCorDate(time);
        custAddress.setCorScrId(scrId);
        custAddress.setCorUserId(userId);
        custAddressMapper.insertCustAddress(custAddress);
        CustAddressVO custAddressVO = new CustAddressVO();
        BeanUtils.copyProperties(custAddress, custAddressVO);
        custAddressVO.setAddrCode(query.getAddrCode());
        return custAddressVO;
    }

    private AjaxResult checkGoods(Map<String, Object> map, List<Map<String, Object>> goods_match) {
        /*
        {"goodsName", "pack", "num", "weight", "volume", "billingMethod", "pc", "sum"}
        */
        // [{goodsId,goodsCode,goodsName,goodsType,goodsCharacter,goodsTypeName}]
        String goodsName = (String) map.get("goodsName");
        if (StringUtils.isBlank(goodsName)) {
            return AjaxResult.error("未填写货品名称");
        }
        for (int i = 0; i < goods_match.size(); i++) {
            if (goodsName.equals(goods_match.get(i).get("goodsName"))) {
                return AjaxResult.success(goods_match.get(i));
            }
        }
        return AjaxResult.error("未在此客户下找到名称为【" + goodsName + "】的货品");
    }

    private Invoice convertImportMapToInvoice(Map<String, Object> map, boolean fetchUnitPrice) {
        String isFleetAssign = "0";
        String isFleetData = "0";
        String pageId = "importInvoice";
        String json = JSONObject.toJSONString(map);
        log.debug("excel解析后数据：{}", json);
        Invoice invoice = JSONObject.parseObject(json, Invoice.class);
        //Invoice invoice = new Invoice();
        String invoiceId = IdUtil.simpleUUID();
        invoice.setCorScrId(pageId);
        invoice.setRegScrId(pageId);
        // 以下代码参照发货单新增
        invoice.setIsFleetData("0");
        //是否存在对应的业务或者车队数据  0不存在
        invoice.setIsFleetAssign("0");
        //设置发货单id uuid
        invoice.setInvoiceId(invoiceId);
        //删除标记 0 未删除
        invoice.setDelFlag(0);
        //发货单新建
        invoice.setVbillstatus(InvoiceStatusEnum.NEW.getValue());
        //结算公司
        //String balaCorpId = invoice.getBalaCorpId();
        //设置发货单号 逻辑：结算公司编号 + 当前年月日 + 四位Sequence。例如：MY201909150001
        invoice.setVbillno(createInvoiceVbillno(invoice.getOperateCorp(), 0));
        //发货单来源 （系统端1；货主端2）
        invoice.setSrcType("1");
        //业务类型代码（参照数据字典 默认保存城市配送 1003997）
        invoice.setBusinesstypename("1003997");
        //设置调度状态
        invoice.setSegmentStatus(InvoiceSegmentEnum.NO_DISPATCH.getValue());
        //是否加入对账单 0未加入 1部分加入 2全部加入
        invoice.setIsAddReceCheck(0);
        //应收核销状态 0未核销  1部分核销 2已核销
        invoice.setReceivableWriteOffStatus("0");
        //是否开票
        invoice.setIfBilling("6".equals(invoice.getBillingType()) ? "0" : "1");

        List<Map<String, Object>> start = (List<Map<String, Object>>) map.get("start");
        List<Map<String, Object>> end = (List<Map<String, Object>>) map.get("end");

        if (start.size() == 1 && end.size() == 1) {
            invoice.setIsMultiple(0);
        } else {
            invoice.setIsMultiple(1);
        }

        List<InvPackGoods> invPackGoodsList = new ArrayList<>();
        //多装多卸 地址信息
        List<MultipleShippingAddress> shippingAddressList = new ArrayList<>();
        for (int i = 0; i < start.size(); i++) {
            Map<String, Object> startItem = start.get(i);
            if (i == 0) {
                // 保存到发货单发货地址
//                invoice.setDeliAddrCode((String) startItem.get("addrCode"));
                invoice.setDeliAddrName((String) startItem.get("addrName"));
                invoice.setDeliAreaId((String) startItem.get("areaId"));
                invoice.setDeliAreaName((String) startItem.get("areaName"));
                invoice.setDeliCityId((String) startItem.get("cityId"));
                invoice.setDeliCityName((String) startItem.get("cityName"));
                invoice.setDeliContact((String) startItem.get("contact"));
                invoice.setDeliDetailAddr((String) startItem.get("detailAddr"));
                invoice.setDeliMobile((String) startItem.get("mobile"));
                invoice.setDeliProName((String) startItem.get("provinceName"));
                invoice.setDeliProvinceId((String) startItem.get("provinceId"));
//                invoice.setDeliveryId((String) startItem.get("addressId"));
            }
            MultipleShippingAddress mAddress = this.convertToMultipleShippingAddress(startItem);
            shippingAddressList.add(mAddress);
            String msaId = IdUtil.simpleUUID();
            mAddress.setMultipleShippingAddressId(msaId);
            mAddress.setAddressType(0);
            mAddress.setInvoiceId(invoiceId);
            mAddress.setInvoiceNo(invoice.getVbillno());
            mAddress.setIsFleetAssign(isFleetAssign);
            mAddress.setIsFleetData(isFleetData);
            mAddress.setRegScrId(pageId);
            List<MultipleShippingGoods> shippingGoodsList = new ArrayList<>();
            mAddress.setShippingGoodsList(shippingGoodsList);
            List<Map<String, Object>> goodsList = (List<Map<String, Object>>) startItem.get("goods");
            for (int j = 0; j < goodsList.size(); j++) {
                Map<String, Object> goodsItem = goodsList.get(j);
                MultipleShippingGoods mGoods = this.convertToMultipleShippingGoods(goodsItem);
                mGoods.setMultipleShippingAddressId(msaId);
                mGoods.setMultipleShippingGoodsId(IdUtil.simpleUUID());
                mGoods.setRegScrId(pageId);
                shippingGoodsList.add(mGoods);

                InvPackGoods invPackGoods = new InvPackGoods();
                BeanUtils.copyBeanProp(invPackGoods, mGoods);
                invPackGoods.setInvoiceId(invoiceId);
                invPackGoods.setInvPackGoodsId(IdUtil.simpleUUID());
                invPackGoods.setRegScrId(pageId);
                invPackGoods.setCorScrId(pageId);
                invPackGoodsList.add(invPackGoods);
            }
        }
        invoice.setInvPackGoodsList(invPackGoodsList);
        for (int i = 0; i < end.size(); i++) {
            Map<String, Object> endItem = end.get(i);
            if (i == 0) {
                // 保存到发货单收货地址
//                invoice.setArriAddrCode((String) endItem.get("addrCode"));
                invoice.setArriAddrName((String) endItem.get("addrName"));
                invoice.setArriAreaId((String) endItem.get("areaId"));
                invoice.setArriAreaName((String) endItem.get("areaName"));
                invoice.setArriCityId((String) endItem.get("cityId"));
                invoice.setArriCityName((String) endItem.get("cityName"));
                invoice.setArriContact((String) endItem.get("contact"));
                invoice.setArriDetailAddr((String) endItem.get("detailAddr"));
                invoice.setArriMobile((String) endItem.get("mobile"));
                invoice.setArriProName((String) endItem.get("provinceName"));
                invoice.setArriProvinceId((String) endItem.get("provinceId"));
//                invoice.setArrivalId((String) endItem.get("addressId"));
            }
            MultipleShippingAddress mAddress = this.convertToMultipleShippingAddress(endItem);
            shippingAddressList.add(mAddress);
            String msaId = IdUtil.simpleUUID();
            mAddress.setMultipleShippingAddressId(msaId);
            mAddress.setAddressType(1);
            mAddress.setInvoiceId(invoiceId);
            mAddress.setInvoiceNo(invoice.getVbillno());
            mAddress.setIsFleetAssign(isFleetAssign);
            mAddress.setIsFleetData(isFleetData);
            mAddress.setRegScrId(pageId);
            List<MultipleShippingGoods> shippingGoodsList = new ArrayList<>();
            mAddress.setShippingGoodsList(shippingGoodsList);
            List<Map<String, Object>> goodsList = (List<Map<String, Object>>) endItem.get("goods");
            for (int j = 0; j < goodsList.size(); j++) {
                Map<String, Object> goodsItem = goodsList.get(j);
                MultipleShippingGoods mGoods = this.convertToMultipleShippingGoods(goodsItem);
                mGoods.setMultipleShippingAddressId(msaId);
                mGoods.setMultipleShippingGoodsId(IdUtil.simpleUUID());
                mGoods.setRegScrId(pageId);
                shippingGoodsList.add(mGoods);
            }
        }

        invoice.setShippingAddressList(shippingAddressList);

        //客户单号
        String custOrdernos = invPackGoodsList.stream()
                .map(InvPackGoods::getCustOrderno)
                .distinct()
                .collect(Collectors.joining(","));
        invoice.setCustOrderno(custOrdernos);

        //货品
        String goodsName = invPackGoodsList.stream()
                .map(InvPackGoods::getGoodsName)
                .distinct()
                .collect(Collectors.joining(","));
        invoice.setGoodsName(goodsName);
        invoice.setIsOversize(0);

        //是否往返
        if (invoice.getIsRoundTrip() == null) {
            invoice.setIsRoundTrip(0);
        }
        if ("4".equals(invoice.getBillingMethod())) {
            invoice.setIsRoundTrip(1);
        }

        if (Objects.equals(invoice.getIfBargain() , 0)) { // 客户是合同价时，判断结算价格是否与合同价一致
            final List<InvPackGoods> goodsList = invoice.getInvPackGoodsList();
            SysDictData billingTypeDictData = sysDictDataMapper.selectDictDataByTypeAndValue("billing_type", invoice.getBillingType());
            //查询合同价
            SearchContractPriceVO priceVO = new SearchContractPriceVO();
            List<String> deliAreaIdList = shippingAddressList.stream()
                    .filter(x -> x.getAddressType() == 0
                            && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                    .map(MultipleShippingAddress::getAreaId)
                    .collect(Collectors.toList());
            priceVO.setDeliAreaIdList(deliAreaIdList);

            List<String> deliAddrNameList = shippingAddressList.stream()
                    .filter(x -> x.getAddressType() == 0
                            && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                    .map(MultipleShippingAddress::getAddrName)
                    .collect(Collectors.toList());
            priceVO.setDeliAddrNameList(deliAddrNameList);

            List<String> arriAreaIdList = new ArrayList<>();
            List<String> arriArriNameList = new ArrayList<>();

            for (MultipleShippingAddress multipleShippingAddress : shippingAddressList) {
                if (multipleShippingAddress.getAddressType() == 1
                        && multipleShippingAddress.getIsGetContractPrice() != null
                        && multipleShippingAddress.getIsGetContractPrice() == 1) {
                    if (multipleShippingAddress.getIsChangeAddress() != null && multipleShippingAddress.getIsChangeAddress() == 1) {
                        arriAreaIdList.add(multipleShippingAddress.getCaArriAreaId());
                        arriArriNameList.add(multipleShippingAddress.getCaArriAddrName());
                    } else {
                        arriAreaIdList.add(multipleShippingAddress.getAreaId());
                        arriArriNameList.add(multipleShippingAddress.getAddrName());
                    }
                }
            }
            priceVO.setArriAreaIdList(arriAreaIdList);
            priceVO.setArriAddrNameList(arriArriNameList);

            priceVO.setCustomerId(invoice.getCustomerId());

            priceVO.setBillingMethod(Integer.valueOf(invoice.getBillingMethod()));

            List<String> goodsNameList = goodsList.stream()
                    .map(InvPackGoods::getGoodsName)
                    .distinct()
                    .collect(Collectors.toList());
            if (goodsNameList.size() == 1) {
                priceVO.setGoodsName(goodsNameList.get(0));
            }

            //货品特性
//            String goodsCharacter = "15".equals(invoice.getTransCode()) || "16".equals(invoice.getTransCode()) ? "1" : "0";
            String goodsCharacter;
            if ("15".equals(invoice.getTransCode()) || "16".equals(invoice.getTransCode())) {
                goodsCharacter = "1";
            } else if ("4".equals(invoice.getTransCode())) {
                goodsCharacter = "2";
            }else {
                goodsCharacter = "0";

            }
            priceVO.setGoodsCharacter(goodsCharacter);
            //车长
            priceVO.setCarLen(invoice.getCarLen());
            priceVO.setCarType(invoice.getCarType());

            priceVO.setNum(Convert.toBigDecimal(invoice.getNumCount(), BigDecimal.ZERO));
            priceVO.setWeight(Convert.toBigDecimal(invoice.getWeightCount(), BigDecimal.ZERO));
            priceVO.setVolume(Convert.toBigDecimal(invoice.getVolumeCount(), BigDecimal.ZERO));
            priceVO.setMileage(invoice.getMileage() == null ? BigDecimal.ZERO : BigDecimal.valueOf(invoice.getMileage()));

            priceVO.setIsOversize(invoice.getIsOversize());
            priceVO.setTransCode(invoice.getTransCode());

            priceVO.setIsRoundTrip(invoice.getIsRoundTrip());

            Map<String, String> priceMap = getPrice(priceVO);
            if ("0".equals(priceMap.get("type"))) {
                //TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                throw new RuntimeException("未查询到合同价");
            }
            if ("".equals(priceMap.get("costPrice")) || priceMap.get("costPrice") == null) {
                throw new RuntimeException("未查询到成本价");
            }

            //税率
            BigDecimal rate = billingTypeDictData.getNumVal1();

            //是否含税  0不含税  1含税*
            String isIncludeTax = priceMap.get("isIncludeTax");

            //总评价
            BigDecimal totalPrice = Convert.toBigDecimal(priceMap.get("totalPrice"));
            //单价
            BigDecimal price = Convert.toBigDecimal(priceMap.get("price"));
            //成本价
            BigDecimal totalCostPrice = Convert.toBigDecimal(priceMap.get("totalCostPrice"));
            String costBillingType = priceMap.get("costBillingType");

            Integer isFixedPrice = Convert.toInt(priceMap.get("isFixedPrice"), null);

            String contractpcVersionId = priceMap.get("versionId");

            invoice.setContractIsFixedPrice(isFixedPrice);

            invoice.setContractPrice(price);

            if (fetchUnitPrice) { // 用合同价赋值单价和总价
                invoice.setUnitPrice(price);
                invoice.setCostAmount(totalPrice);
            }
            //成本价
            invoice.setCostPrice(totalCostPrice);
            invoice.setCostBillingType(totalCostPrice == null ? null : costBillingType);

            invoice.setContractPriceTotal(totalPrice);
            // 合同单价与录入的单价不一致时，判断为议价
            if (!NumberUtil.equals(invoice.getUnitPrice(), price)) {
                invoice.setIfBargain(1);
            }

            //合同价版本id
            invoice.setContractpcVersionId(contractpcVersionId);

            //是否含税  0不含税  1含税*
            if ("0".equals(isIncludeTax)) {
                //不含税
                invoice.setCostAmountIncludeTax(totalPrice);
                invoice.setUnitPriceIncludeTax(price);

                if (rate != null && rate.compareTo(BigDecimal.ZERO) != 0) {
                    //invoice.setCostAmount(NumberUtil.round(NumberUtil.mul(totalPrice, rate), 2));
                    //invoice.setUnitPrice(NumberUtil.round(NumberUtil.mul(price, rate), 2));
                } else {
                    //invoice.setCostAmount(totalPrice);
                    //invoice.setUnitPrice(price);
                }
            } else {
                //含税
                //invoice.setCostAmount(totalPrice);
                //invoice.setUnitPrice(price);
                if (rate != null && rate.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal costAmountIncludeTax = NumberUtil.div(invoice.getCostAmount(), rate, 2);
                    invoice.setCostAmountIncludeTax(costAmountIncludeTax);

                    BigDecimal unitPriceIncludeTax = NumberUtil.div(invoice.getUnitPrice(), rate, 2);
                    invoice.setUnitPriceIncludeTax(unitPriceIncludeTax);

                } else {
                    invoice.setCostAmountIncludeTax(totalPrice);
                    invoice.setUnitPriceIncludeTax(price);
                }
            }
        }

        log.debug("转换后发货单：{}", JSONObject.toJSONString(invoice));
        return invoice;
    }

    private MultipleShippingAddress convertToMultipleShippingAddress(Map<String, Object> map) {
        /* {
            "addrName":"上海寅西","contact":"徐耀华","mobile":"15862700960","goods":[{...}],
            "detailAddr":"寅西路","addrCode":"ADDR2203044504","areaId":"310117","cityName":"市辖区","areaName":"松江区",
            "cityId":"310100","provinceName":"上海市","provinceId":"310000","addressId":"a9e22703e9484f7684c66fd76f358295",
            "isGetContractPrice":"是"
        } */
        MultipleShippingAddress msa = new MultipleShippingAddress();
//        msa.setAddrCode((String) map.get("addrCode"));
        msa.setAddrName((String) map.get("addrName"));
        msa.setAreaId((String) map.get("areaId"));
        msa.setAreaName((String) map.get("areaName"));
        msa.setCityId((String) map.get("cityId"));
        msa.setCityName((String) map.get("cityName"));
        msa.setContact((String) map.get("contact"));
//        msa.setDeliveryId((String) map.get("addressId"));
        msa.setDetailAddr((String) map.get("detailAddr"));
        msa.setMobile((String) map.get("mobile"));
        msa.setProvinceId((String) map.get("provinceId"));
        msa.setProvinceName((String) map.get("provinceName"));
        final String isGetContractPrice = (String) map.get("isGetContractPrice");
        if ("是".equals(isGetContractPrice)) {
            msa.setIsGetContractPrice(1);
        } else if ("否".equals(isGetContractPrice)) {
            msa.setIsGetContractPrice(0);
        } else {
            throw new RuntimeException("【参与合同价计算】错误值【" + isGetContractPrice + "】，只能选择【是】或【否】");
        }
        return msa;
    }

    private MultipleShippingGoods convertToMultipleShippingGoods(Map<String, Object> goodsItem) {
        /* {
            "goodsName":"电动工具","pack":"包","num":"4","weight":"5","volume":"6","billingMethod":3,"pc":"323.11","sum":"423.72",
            "goodsId":"bdd509445fd44371946a9d3d2eddf03e","goodsTypeName":"机电产品","goodsCharacter":0,
            "goodsCode":"GOOD2003020632","goodsType":"9aa7b5a1c871419692885da65abaaeb3","packId":"0"
        } */
        MultipleShippingGoods msg = new MultipleShippingGoods();
//        msg.setBillingMethod(goodsItem.get("billingMethod").toString()); // 原Integer
        msg.setGoodsCharacter((Integer) goodsItem.get("goodsCharacter"));
//        msg.setGoodsCode((String) goodsItem.get("goodsCode"));
//        msg.setGoodsId((String) goodsItem.get("goodsId"));
        msg.setGoodsName((String) goodsItem.get("goodsName"));
        msg.setGoodsType((String) goodsItem.get("goodsType"));
        msg.setGoodsTypeName((String) goodsItem.get("goodsTypeName"));
        if (StringUtils.isNotBlank((String) goodsItem.get("num"))) {
            msg.setNum(Double.parseDouble((String) goodsItem.get("num")));
        }
        msg.setPackId((String) goodsItem.get("packId"));
//        msg.setSum(new BigDecimal((String) goodsItem.get("sum")));
        if (StringUtils.isNotBlank((String) goodsItem.get("volume"))) {
            msg.setVolume(Double.parseDouble((String) goodsItem.get("volume")));
        }
        if (StringUtils.isNotBlank((String) goodsItem.get("weight"))) {
            msg.setWeight(Double.parseDouble((String) goodsItem.get("weight")));
        }
//        if (StringUtils.isNotBlank((String) goodsItem.get("pc"))) {
//            msg.setPc(new BigDecimal((String) goodsItem.get("pc")));
//        }
        if (StringUtils.isNotBlank((String) goodsItem.get("custOrderno"))) {
            msg.setCustOrderno((String) goodsItem.get("custOrderno"));
        }
        return msg;
    }

    /**
     * 同一批次发货单指导价
     *
     * @param cache
     * @param param
     * @return
     */
    private GuidePriceDetail getCachedGuidingPrice(Map<String, GuidePriceDetail> cache, Map<String, String> param) {
        String[] keyFields = {"pricingMethod", "customerId", "carType", "carLen", "transType", "deliAreaId", "arriAreaId"};
        StringBuilder key = new StringBuilder();
        for (int i = 0; i < keyFields.length; i++) {
            key.append("@").append(param.get(keyFields[i]));
        }
        GuidePriceDetail gpd = cache.get(key.toString());
        if (gpd == null && !cache.containsKey(key.toString())) {
            List<GuidePriceDetail> guidePriceDetails = this.getGuidingPriceList(param);
            if (guidePriceDetails.size() > 0) {
                gpd = guidePriceDetails.get(0);
                cache.put(key.toString(), gpd);
            } else {
                cache.put(key.toString(), null);
            }
        }
        return gpd;
    }

    private void fetchCachedGuidingPrice(Map<String, GuidePriceDetail> cache, Map<String, String> param, String guidingPrice) {
        String[] keyFields = {"pricingMethod", "customerId", "carType", "carLen", "transType", "deliAreaId", "arriAreaId"};
        StringBuilder key = new StringBuilder();
        for (int i = 0; i < keyFields.length; i++) {
            key.append("@").append(param.get(keyFields[i]));
        }
        GuidePriceDetail gpd = new GuidePriceDetail();
        gpd.setCheckStatus(0);
        gpd.setGuidingPrice(new BigDecimal(guidingPrice));
        cache.put(key.toString(), gpd);
    }

    @Override
    public List<Entrust> selectEntrustListByInvoiceId(String invoiceId) {
        return entrustMapper.selectEntrustListByInvoiceId(invoiceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult projectAdd(ProjectInvoiceAddVO projectInvoice) {
        //查询客户信息
        ClientPopupVO clientPopupVO = clientMapper.selectClientById(projectInvoice.getCustomerId());


        Invoice invoice = new Invoice();
        BeanUtils.copyProperties(projectInvoice, invoice);

        if (StringUtils.isEmpty(clientPopupVO.getOperateCorp())) {
            return AjaxResult.error("客户缺少运营公司。");
        }
        invoice.setOperateCorp(clientPopupVO.getOperateCorp());


        String invoiceId = IdUtil.simpleUUID();
        //发货单id
        invoice.setInvoiceId(invoiceId);

        if (invoice.getEstimatedGrossMargin() != null) {
            invoice.setEstimatedGrossMargin(NumberUtil.round(NumberUtil.div(invoice.getEstimatedGrossMargin(), 100),4));
        }
        //总金额
        invoice.setTotalFee(invoice.getCostAmount());

        //发货单新建
        invoice.setVbillstatus(InvoiceStatusEnum.NEW.getValue());

        //结算公司
        String balaCorpId = invoice.getBalaCorpId();
        //设置发货单号 逻辑：结算公司编号 + 当前年月日 + 四位Sequence。例如：MY201909150001
        invoice.setVbillno(createInvoiceVbillno(invoice.getOperateCorp(), 0));
        //发货单来源 （系统端1；货主端2）
        invoice.setSrcType("1");
        //业务类型代码（参照数据字典 默认保存城市配送 1003997）
        invoice.setBusinesstypename("1003997");
        //设置调度状态
        invoice.setSegmentStatus(InvoiceSegmentEnum.NO_DISPATCH.getValue());
        //是否加入对账单 0未加入 1部分加入 2全部加入
        invoice.setIsAddReceCheck(0);
        //应收核销状态 0未核销  1部分核销 2已核销
        invoice.setReceivableWriteOffStatus("0");

        //运营部
        String salesDeptId = clientPopupVO.getSalesDept();
        SysDept custSales = sysDeptMapper.getParentDeptInfo(salesDeptId);
        invoice.setCustSalesId(String.valueOf(custSales.getDeptId()));
        invoice.setCustSalesName(custSales.getDeptName());
        //管理部
        SysDept mgmtDept = sysDeptMapper.getParentDeptInfo(String.valueOf(custSales.getDeptId()));
        invoice.setMgmtDeptId(String.valueOf(mgmtDept.getDeptId()));
        invoice.setMgmtDeptName(mgmtDept.getDeptName());

        //客户运营部
//        String salesId = clientPopupVO.getSalesId();
//        if (StringUtils.isNotEmpty(salesId)) {
//            MSalesGroup mSalesGroup = salesGroupMapper.selectByPrimaryKey(salesId);
//
//            invoice.setCustSalesId(salesId);
//            invoice.setCustSalesName(mSalesGroup.getSalesName());
//        }

        //发货单类型 0普通下单 1项目制下单
        invoice.setInvoiceType(1);
        invoice.setIsFleetData("0");
        invoice.setIsFleetAssign("0");
        invoice.setRegScrId("InvoiceService.projectAdd");
        invoiceMapper.insertSelective(invoice);

        List<ProjectInvoiceAddVO.InvoiceDetailAddVO> invoiceDetailList = projectInvoice.getInvoiceDetailList();
        invoiceDetailList.removeIf(x -> x.getReqDeliDate() == null);

        for (ProjectInvoiceAddVO.InvoiceDetailAddVO invoiceDetailAddVO : invoiceDetailList) {
            SysDictData carLenDict = sysDictDataMapper.selectDictDataByTypeAndValue("car_len",
                    invoiceDetailAddVO.getCarLen());
            invoiceDetailAddVO.setCarLenName(carLenDict.getDictLabel());

            SysDictData carTypeDict = sysDictDataMapper.selectDictDataByTypeAndValue("car_type",
                    invoiceDetailAddVO.getCarType());
            invoiceDetailAddVO.setCarTypeName(carTypeDict.getDictLabel());

            SysDictData transCodeDict = sysDictDataMapper.selectDictDataByTypeAndValue("trans_code",
                    invoiceDetailAddVO.getTransCode());
            invoiceDetailAddVO.setTransName(transCodeDict.getDictLabel());

            InvoiceDetail invoiceDetail = new InvoiceDetail();
            BeanUtils.copyProperties(invoiceDetailAddVO, invoiceDetail);

            SysDept sysDepts = deptService.selectDeptById(Long.valueOf(invoiceDetailAddVO.getTransLineId()));

            invoiceDetail.setTransLineName(sysDepts.getDeptName());

            String invoiceDetailId = IdUtil.simpleUUID();

            invoiceDetail.setInvoiceDetailId(invoiceDetailId);
            invoiceDetail.setInvoiceId(invoiceId);
            invoiceDetail.setRegScrId("InvoiceService.projectAdd");
            invoiceDetailMapper.insertSelective(invoiceDetail);


            List<ProjectInvoiceAddVO.InvoiceDetailAddressVO> addressVOList = invoiceDetailAddVO.getAddressVOList();

            addressVOList.removeIf(x -> StringUtils.isEmpty(x.getAddrName()));

            for (ProjectInvoiceAddVO.InvoiceDetailAddressVO invoiceDetailAddressVO : addressVOList) {

                InvoiceDetailAddr invoiceDetailAddr = new InvoiceDetailAddr();
                BeanUtils.copyProperties(invoiceDetailAddressVO, invoiceDetailAddr);
                String invoiceDetailAddrId = IdUtil.simpleUUID();

                invoiceDetailAddr.setInvoiceId(invoiceId);
                invoiceDetailAddr.setInvoiceDetailAddrId(invoiceDetailAddrId);
                invoiceDetailAddr.setInvoiceDetailId(invoiceDetailId);
                invoiceDetailAddr.setRegScrId("InvoiceService.projectAdd");
                invoiceDetailAddrMapper.insertSelective(invoiceDetailAddr);
            }


            //发货地址
            List<ProjectInvoiceAddVO.InvoiceDetailAddressVO> deliAddrList = addressVOList
                    .stream().filter(x -> x.getAddressType() == 0)
                    .collect(Collectors.toList());
            //收货地址
            List<ProjectInvoiceAddVO.InvoiceDetailAddressVO> arriAddrList = addressVOList
                    .stream().filter(x -> x.getAddressType() == 1)
                    .collect(Collectors.toList());


            if (deliAddrList.size() == 1 && arriAddrList.size() == 1) {
                String deliProvinceId = deliAddrList.get(0).getProvinceId();
                String deliProName = deliAddrList.get(0).getProvinceName();

                String deliCityId = deliAddrList.get(0).getCityId();
                String deliCityName = deliAddrList.get(0).getCityName();

                String deliAreaId = deliAddrList.get(0).getAreaId();
                String deliAreaName = deliAddrList.get(0).getAreaName();

                String arriProvinceId = arriAddrList.get(0).getProvinceId();
                String arriProName = arriAddrList.get(0).getProvinceName();

                String arriCityId = arriAddrList.get(0).getCityId();
                String arriCityName = arriAddrList.get(0).getCityName();

                String arriAreaId = arriAddrList.get(0).getAreaId();
                String arriAreaName = arriAddrList.get(0).getAreaName();

                String transCode = invoiceDetailAddVO.getTransCode();

                String carLen = invoiceDetailAddVO.getCarLen();
                String carLenName = invoiceDetailAddVO.getCarLenName();
                String carType = invoiceDetailAddVO.getCarType();
                String carTypeName = invoiceDetailAddVO.getCarTypeName();

                BigDecimal guidingPrice = invoiceDetailAddVO.getGuidingPrice();
//                generateTransLineAndGuide(deliProvinceId, deliProName, deliCityId, deliCityName, deliAreaId, deliAreaName
//                        , arriProvinceId, arriProName, arriCityId, arriCityName, arriAreaId, arriAreaName, transCode
//                        , carLen, carLenName, carType, carTypeName, true, guidingPrice
//                        , "InvoiceService.projectAdd");

            }

        }

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteProjectInvoiceByIds(String ids) {
        String[] idList = com.ruoyi.common.core.text.Convert.toStrArray(ids);
        //根据ids查询所有发货单
        List<Invoice> invoiceList = invoiceMapper.getInvoiceListByIds(idList);

        //校验是否为空
        if (invoiceList.size() == 0) {
            return AjaxResult.error("该数据不存在，请刷新后重试！");
        }

        //校验是否存在新建状态的发货单
        long newCount = invoiceList.stream().filter(x -> !InvoiceStatusEnum.NEW.getValue().equals(x.getVbillstatus())).count();
        if (newCount > 0) {
            return AjaxResult.error("存在非新建状态下的发货单，无法删除！");
        }

        for (String invoiceId : idList) {
            InvoiceDetail invoiceDetail = new InvoiceDetail();
            invoiceDetail.setDelDate(new Date());
            invoiceDetail.setDelUserid(shiroUtils.getUserId().toString());
            invoiceDetail.setDelFlag(1);
            invoiceDetailMapper.updateByInvoiceId(invoiceDetail, invoiceId);

            InvoiceDetailAddr invoiceDetailAddr = new InvoiceDetailAddr();
            invoiceDetailAddr.setDelDate(new Date());
            invoiceDetailAddr.setDelUserid(shiroUtils.getUserId().toString());
            invoiceDetailAddr.setDelFlag(1);
            invoiceDetailAddrMapper.updateByInvoiceId(invoiceDetailAddr, invoiceId);
        }

        //删除发货单 逻辑删除
        int i = invoiceMapper.deleteInvoiceByIdsLogic(idList, shiroUtils.getUserId().toString(), "Invoic" +
                "eService.deleteProjectInvoiceByIds", InvoiceStatusEnum.NEW.getValue());
        if (i != idList.length) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("部分发货单状态发生改变，请刷新后重试！");
        }

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult confirmProjectInvoice(String invoiceIds) {
        String[] idList = com.ruoyi.common.core.text.Convert.toStrArray(invoiceIds);

        for (String invoiceId : idList) {
            //查询该发货单信息
            Invoice invoiceAll = invoiceMapper.selectByPrimaryKey(invoiceId);

            if (invoiceAll == null) {
                return AjaxResult.error("该数据不存在，请刷新后重试！");
            }
            if (invoiceAll.getInvoiceType() != 1) {
                return AjaxResult.error("请选择项目制数据！");
            }
            //校验发货单总金额 金额为空时
            if (invoiceAll.getCostAmount() == null) {
                return AjaxResult.error("金额为空无法确认，请先修改发货单！");
            }

            //判断发货单状态，只有 新建 状态下的发货单才能确认
            String vbillstatus = invoiceAll.getVbillstatus();
            if (!InvoiceStatusEnum.NEW.getValue().equals(vbillstatus)) {
                return AjaxResult.error("只有“新建”状态下的发货单才能确认！");
            }

            //查询客户信息
            ClientPopupVO clientPopupVO = clientMapper.selectClientById(invoiceAll.getCustomerId());

            //查询详情信息
            List<InvoiceDetail> invoiceDetailList = invoiceDetailMapper.selectAllByInvoiceId(invoiceId);

            //要求提货日期
            Date reqDeliDate = invoiceDetailList.stream()
                    .map(InvoiceDetail::getReqDeliDate)
                    .min(Date::compareTo).orElse(null);
            //重量合计
            double weightCount = invoiceDetailList.stream().mapToDouble(InvoiceDetail::getWeightCount).filter(Objects::nonNull).sum();


            for (InvoiceDetail invoiceDetail : invoiceDetailList) {

                if (StringUtil.isEmpty(invoiceDetail.getCarLen())) {
                    throw new BusinessException("车长为空无法确认，请先修改发货单！");
                }
                if (StringUtil.isEmpty(invoiceDetail.getCarType())) {
                    throw new BusinessException("车型为空无法确认，请先修改发货单！");
                }
                if (StringUtil.isEmpty(invoiceDetail.getTransLineId())) {
                    throw new BusinessException("调度组为空无法确认，请先修改发货单！");
                }


                List<InvoiceDetailAddr> invoiceDetailAddrList = invoiceDetailAddrMapper
                        .selectAllByInvoiceDetailId(invoiceDetail.getInvoiceDetailId());
                //发货地址
                List<InvoiceDetailAddr> deliAddrList = invoiceDetailAddrList
                        .stream().filter(x -> x.getAddressType() == 0)
                        .collect(Collectors.toList());
                //收货地址
                List<InvoiceDetailAddr> arriAddrList = invoiceDetailAddrList
                        .stream().filter(x -> x.getAddressType() == 1)
                        .collect(Collectors.toList());

                /*
                 * 生成运段,将发货单信息 拷贝至 运段对象中，并插入数据库。
                 */
                Segment segment = new Segment();
                BeanUtils.copyBeanProp(segment, invoiceAll);
                BeanUtils.copyBeanProp(segment, invoiceDetail);

                //运段号 逻辑：YDPZ + 当前时间 yyyyMMdd + 四位Sequence。例如 ： YDPZ201909240001。 如果是车队运段 则在编号前面加上“CD-”
                String seqSegment = StrUtil.fillBefore(segmentMapper.getSeqSegment() + "", '0', 4);
                segment.setVbillno("YDPZ" + DateUtil.format(new Date(), "yyyyMMdd") + seqSegment);
                //结算方式
//                segment.setBalatype(invoice1All.getBalaType());
                //运段状态 待调度
                segment.setVbillstatus(SegmentStatusEnum.TO_DISPATCH.getValue());
                //发货单号
                segment.setInvoiceVbillno(invoiceAll.getVbillno());
                //运段类型 2 默认原始单据
                segment.setSegType(2);
                //设置为有效数据 2
                segment.setSegMark(2);
                //运段id
                String segmentId = IdUtil.simpleUUID();
                segment.setSegmentId(segmentId);
                //设置外发状态 默认不外发
                segment.setOutGoType(2);
                //画面id
                segment.setRegScrId("invoiceService.confirmProjectInvoice");
                segment.setCorScrId("invoiceService.confirmProjectInvoice");

                //设置是否为 车队数据  0是业务下单数据  1是车队下单数据
                segment.setIsFleetData("0");

                //是否存在业务或者车队数据 0：不存在  1：存在
                segment.setIsFleetAssign("0");

                //是否多装多卸
                int isMultiple = deliAddrList.size() > 1 || arriAddrList.size() > 1 ? 1 : 0;
                segment.setIsMultiple(isMultiple);


                //发货地址

                segment.setDeliProvinceId(deliAddrList.get(0).getProvinceId());
                segment.setDeliCityId(deliAddrList.get(0).getCityId());
                segment.setDeliAreaId(deliAddrList.get(0).getAreaId());
                segment.setDeliAddrName(deliAddrList.get(0).getAddrName());
                segment.setDeliProName(deliAddrList.get(0).getProvinceName());
                segment.setDeliCityName(deliAddrList.get(0).getCityName());
                segment.setDeliAreaName(deliAddrList.get(0).getAreaName());
                segment.setDeliDetailAddr(deliAddrList.get(0).getDetailAddr());
                segment.setDeliContact(deliAddrList.get(0).getContact());
                segment.setDeliMobile(deliAddrList.get(0).getMobile());
                //到货地址
                segment.setArriProvinceId(arriAddrList.get(0).getProvinceId());
                segment.setArriCityId(arriAddrList.get(0).getCityId());
                segment.setArriAreaId(arriAddrList.get(0).getAreaId());
                segment.setArriAddrName(arriAddrList.get(0).getAddrName());
                segment.setArriProName(arriAddrList.get(0).getProvinceName());
                segment.setArriCityName(arriAddrList.get(0).getCityName());
                segment.setArriAreaName(arriAddrList.get(0).getAreaName());
                segment.setArriDetailAddr(arriAddrList.get(0).getDetailAddr());
                segment.setArriContact(arriAddrList.get(0).getContact());
                segment.setArriMobile(arriAddrList.get(0).getMobile());

         /*       BigDecimal guidePriceNeed = null;
                BigDecimal specialGuidePriceNeed = null;

                List<Segment> segmentList = new ArrayList<>();
                segmentList.add(segment);

                int isToAndFro = String.valueOf(BillingMethod.DAY.getValue()).equals(invoiceAll.getBillingMethod()) ? 1 : 0;

                int needReferencePrice = segmentService.isNeedReferencePrice(segmentList, segment.getTransCode(), isToAndFro);

                if (needReferencePrice == 1) {
                    ReferencePrice referencePrice = new ReferencePrice();
                    referencePrice.setCarLen(segment.getCarLen());
                    referencePrice.setCarType(segment.getCarType());
                    referencePrice.setDeliAreaId(segment.getDeliAreaId());
                    referencePrice.setArriAreaId(segment.getArriAreaId());

                    List<ReferencePrice> referencePrices = referencePriceMapper.selectList(referencePrice);

                    if (referencePrices != null && referencePrices.size() > 0) {
                        //指导价
                        if ("0".equals(segment.getTransCode())) {
                            guidePriceNeed = referencePrices.get(0).getPriceBasic();
                        } else if ("4".equals(segment.getTransCode())) {
                            guidePriceNeed = referencePrices.get(0).getPriceColdChain();
                        } else if ("15".equals(segment.getTransCode())) {
                            guidePriceNeed = referencePrices.get(0).getPriceDangerousGoods();
                        }
                    }

                    segment.setGuidingPrice(guidePriceNeed);
                    //指导价浮动比率
                    segment.setReferenceRate(clientPopupVO.getReferenceRate());

                } else if (needReferencePrice == 2) {
                    SpecialReferencePrice specialReferencePrice = new SpecialReferencePrice();
                    specialReferencePrice.setCarLen(segment.getCarLen());
                    specialReferencePrice.setCarType(segment.getCarType());
                    specialReferencePrice.setDeliAreaId(segment.getDeliAreaId());
                    specialReferencePrice.setArriAreaId(segment.getArriAreaId());

                    specialReferencePrice.setCustomerId(segment.getCustomerId());
                    List<SpecialReferencePrice> specialReferencePriceList = specialReferencePriceMapper.listByAll(specialReferencePrice);

                    if (specialReferencePriceList != null && specialReferencePriceList.size() > 0) {
                        //指导价
                        if ("0".equals(segment.getTransCode())) {
                            specialGuidePriceNeed = specialReferencePriceList.get(0).getPriceBasic();
                        } else if ("4".equals(segment.getTransCode())) {
                            specialGuidePriceNeed = specialReferencePriceList.get(0).getPriceColdChain();
                        } else if ("15".equals(segment.getTransCode())) {
                            specialGuidePriceNeed = specialReferencePriceList.get(0).getPriceDangerousGoods();
                        }
                    }

                    segment.setSpecialGuidingPrice(specialGuidePriceNeed);
                }
*/
                //插入运段
                segmentMapper.insertSegment(segment);

                /*
                 *
                 */
                SegPackGoods segPackGoods = new SegPackGoods();
                segPackGoods.setSegPackGoodsId(IdUtil.simpleUUID());
                segPackGoods.setInvoiceId(invoiceId);
                segPackGoods.setSegmentId(segmentId);
                segPackGoods.setGoodsName(invoiceDetail.getGoodsName());
                segPackGoods.setNum(invoiceDetail.getNumCount());
                segPackGoods.setWeight(invoiceDetail.getWeightCount());
                segPackGoods.setVolume(invoiceDetail.getVolumeCount());
                segPackGoods.setGoodsType(invoiceDetail.getGoodsType());
                segPackGoods.setGoodsTypeName(invoiceDetail.getGoodsTypeName());

                segPackGoods.setRegScrId("invoiceService.confirmProjectInvoice");

                segPackGoodsMapper.insertSegPackGoods(segPackGoods);


                /*
                 * 多装多卸地址表  插入运段id
                 */


                for (InvoiceDetailAddr invoiceDetailAddr : invoiceDetailAddrList) {

                    MultipleShippingAddress multipleShippingAddress = new MultipleShippingAddress();

                    BeanUtils.copyBeanProp(multipleShippingAddress, invoiceDetailAddr);

                    multipleShippingAddress.setMultipleShippingAddressId(IdUtil.simpleUUID());
                    multipleShippingAddress.setSegmentId(segment.getSegmentId());
                    multipleShippingAddress.setSegmentNo(segment.getVbillno());
                    multipleShippingAddress.setInvoiceId(null);
                    multipleShippingAddress.setInvoiceNo(null);
                    multipleShippingAddress.setRegScrId("invoiceService.confirmProjectInvoice");


                    multipleShippingAddressMapper.insertSelective(multipleShippingAddress);


                    MultipleShippingGoods goods = new MultipleShippingGoods();
                    goods.setMultipleShippingGoodsId(IdUtil.simpleUUID());
                    goods.setMultipleShippingAddressId(multipleShippingAddress.getMultipleShippingAddressId());
                    goods.setGoodsType(invoiceDetail.getGoodsType());
                    goods.setGoodsTypeName(invoiceDetail.getGoodsTypeName());
                    goods.setGoodsName(invoiceDetail.getGoodsName());
                    goods.setNum(invoiceDetailAddr.getNum());
                    goods.setWeight(invoiceDetailAddr.getWeight());
                    goods.setVolume(invoiceDetailAddr.getVolume());
                    goods.setCustOrderno(invoiceDetail.getCustOrderno());

                    goods.setRegScrId("invoiceService.confirmProjectInvoice");
                    multipleShippingGoodsMapper.insertSelective(goods);

                }

            }


            Invoice invoice = new Invoice();
            invoice.setInvoiceId(invoiceId);
            /*
             * 添加发货单主表信息
             */
            //保险公司名称为空时，默认填入 '中国平安财产保险股份有限公司'
            if (StringUtils.isEmpty(invoiceAll.getInsuranceCompany())) {
                invoice.setInsuranceCompany(getConfig("insurance.company"));
            }

            //保险单号为空时，默认填入 '11878003901148112693'
            if (StringUtils.isEmpty(invoiceAll.getInsuranceNo())) {
                invoice.setInsuranceNo(getConfig("insurance.code"));
            }

            //保险附件为空，则默认 上传 insurance_contract.pdf
            if (StringUtils.isEmpty(invoiceAll.getInsuranceAppendixId())) {
                String tid;
                try {
                    tid = this.uploadInsuranceContract();
                } catch (IOException e) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return AjaxResult.error("设置默认保险附件出错！", e);
                }
                invoice.setInsuranceAppendixId(tid);
            }
            //收货方用户ID
//            invoice.setArriUserId(userId);
            //更新发货单状态
            invoice.setVbillstatus(InvoiceStatusEnum.AFFIRM.getValue());
            invoice.setCorUserId(shiroUtils.getUserId().toString());
            invoice.setCorUserName(shiroUtils.getSysUser().getUserName());
            invoice.setCorDate(new Date());
            //确认人
            invoice.setConfirmUserid(shiroUtils.getUserId().toString());
            invoice.setConfirmUserName(shiroUtils.getSysUser().getUserName());
            //确认时间
            invoice.setConfirmDate(new Date());
            //指导价
//            if (guidePriceNeed != null) {
//                invoice.setGuidingPrice(guidePriceNeed);
//            }

            //业务类型代码（参照数据字典 默认保存城市配送 1003997）
            invoice.setBusinesstypename("1003997");
            //要求提货日期
            invoice.setReqDeliDate(reqDeliDate);
            //总重量
            invoice.setWeightCount(weightCount);

            int i = invoiceMapper.updateInvoiceCheckStatus(invoice, InvoiceStatusEnum.NEW.getValue());
            if (i != 1) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return AjaxResult.error("发货单状态发生改变，请刷新后重试！");
            }


            /*
             * 生成 应收明细
             */
            ReceiveDetailVO receiveDetail = new ReceiveDetailVO();
            //拷贝发货单信息
            BeanUtils.copyBeanProp(receiveDetail, invoiceAll);

            receiveDetail.setMemo(null);
            //结算客户id
            receiveDetail.setBalaCustomer(invoiceAll.getBalaCustomerId());
            //客户名称存客户简称
            receiveDetail.setCustName(invoiceAll.getCustAbbr());
            //结算方式
//                receiveDetail.setBalatype(invoiceAll.getBalaType());
            //应收单据号
            receiveDetail.setVbillno(receiveDetailService.createReceiveDetailVbillno(0));
            //设置费用类型 为 运费：0
            receiveDetail.setFreeType(FreeTypeEnum.PREPAID_CASH.getValue());

            //发货单号
            receiveDetail.setInvoiceVbillno(invoiceAll.getVbillno());
            //总重量
//                receiveDetail.setFeeWeightCount(invoice1All.getWeightCount());
            //是否原始单据 默认原始单据
            receiveDetail.setVbillType("1");
            receiveDetail.setDelFlag(0);
            //结算公司id
            receiveDetail.setBalaCorp(invoiceAll.getBalaCorpId());
            //账期  暂时使用ClientPopupVO对象，后期cjm优化改回Client对象
            receiveDetail.setPaymentDays(clientPopupVO.getPaymentDays());

            receiveDetail.setRegScrId("invoiceService.confirmProjectInvoice");
            receiveDetail.setCorScrId("invoiceService.confirmProjectInvoice");

            //设置是否为 车队数据  0是业务下单数据  1是车队下单数据
            receiveDetail.setIsFleetData("0");


            //应收单据状态
            receiveDetail.setVbillstatus(ReceiveDetailStatusEnum.NEW.getValue());
            receiveDetail.setIsFleetAssign("0");

            BigDecimal costAmount = invoiceAll.getCostAmount();
            if (BigDecimal.ZERO.compareTo(costAmount) != 0) {
                //总金额
                receiveDetail.setTransFeeCount(costAmount);
                //是否司机代收
                receiveDetail.setIsCollect(0);
                receiveDetail.setReceiveDetailId(IdUtil.simpleUUID());
                receiveDetailService.insertReceiveDetailAndAdjustRecord(receiveDetail);
            }

            /*
             * 添加第三方费用 装卸费
             */
            //查询客户信息
//            ClientPopupVO clientPopup = clientMapper.selectClientById(invoiceAll.getCustomerId());
//
//            //单价
//            BigDecimal handlingCharges = clientPopup.getHandlingCharges();
//            String handlingChargesType = clientPopup.getHandlingChargesType();
//            this.handlingCharges(invoice1All, handlingChargesType, handlingCharges);



        }

        return AjaxResult.success();
    }

    @Override
    public ProjectInvoiceDetailVO selectProjectInvoiceById(String invoiceId) {
        ProjectInvoiceDetailVO detailVO = new ProjectInvoiceDetailVO();

        Invoice invoice = invoiceMapper.selectByPrimaryKey(invoiceId);
        BeanUtils.copyProperties(invoice, detailVO);


        List<InvoiceDetail> invoiceDetails = invoiceDetailMapper.selectAllByInvoiceId(invoiceId);

        List<ProjectInvoiceDetailVO.InvoiceDetailVO> invoiceDetailVOList = new ArrayList<>();
        for (InvoiceDetail invoiceDetail : invoiceDetails) {
            ProjectInvoiceDetailVO.InvoiceDetailVO invoiceDetailVO = new ProjectInvoiceDetailVO.InvoiceDetailVO();
            BeanUtils.copyProperties(invoiceDetail, invoiceDetailVO);

            List<InvoiceDetailAddr> invoiceDetailAddrList = invoiceDetailAddrMapper
                    .selectAllByInvoiceDetailId(invoiceDetail.getInvoiceDetailId());

            List<ProjectInvoiceDetailVO.InvoiceDetailAddressVO> addressVOList = invoiceDetailAddrList.stream().map(x -> {
                ProjectInvoiceDetailVO.InvoiceDetailAddressVO addressVO = new ProjectInvoiceDetailVO.InvoiceDetailAddressVO();
                BeanUtils.copyProperties(x, addressVO);
                return addressVO;
            }).collect(Collectors.toList());

            invoiceDetailVO.setAddressVOList(addressVOList);
            invoiceDetailVOList.add(invoiceDetailVO);
        }

        detailVO.setInvoiceDetailList(invoiceDetailVOList);

        return detailVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeBillingMethod(String invoiceId, String balaType, String corScrId) {
        /*
         * 修改发货单
         */
        Invoice invoice = new Invoice();
        invoice.setInvoiceId(invoiceId);
        invoice.setCorScrId(corScrId);
        invoice.setBalaType(balaType);
        invoiceMapper.updateInvoice(invoice);

        /*
         * 修改运段
         */
        Segment segment = new Segment();
        segment.setInvoiceId(invoiceId);
        segment.setBalatype(balaType);
        segment.setCorScrId(corScrId);
        segmentMapper.updateSegmentByInvoiceId(segment);

        /*
         * 修改应收
         */
        ReceiveDetailVO receiveDetail = new ReceiveDetailVO();
        receiveDetail.setInvoiceId(invoiceId);
        receiveDetail.setBalatype(balaType);
        receiveDetail.setCorScrId(corScrId);
        receiveDetailMapper.updateReceiveDetailByInvoiceId(receiveDetail);
    }

    @Override
    public int synInvoiceCostAmount(String invoiceId,String corScrId) {
        Invoice invoice = invoiceMapper.selectInvoiceById(invoiceId);
        if (invoice == null) {
            throw new BusinessException("未查询到发货单。");
        }

        List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveByInvoiceId(invoiceId);

        //运费
        BigDecimal prepaidCash = receiveDetailVOS.stream()
                .filter(x -> FreeTypeEnum.PREPAID_CASH.getValue().equals(x.getFreeType()))
                .map(ReceiveDetailVO::getTransFeeCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        //在途
        BigDecimal prepaidOilCard = receiveDetailVOS.stream()
                .filter(x -> FreeTypeEnum.PREPAID_OIL_CARD.getValue().equals(x.getFreeType()))
                .map(ReceiveDetailVO::getTransFeeCount).reduce(BigDecimal.ZERO, BigDecimal::add);

        //在途
        BigDecimal totalFee = receiveDetailVOS.stream()
                .filter(Objects::nonNull)
                .map(ReceiveDetailVO::getTransFeeCount).reduce(BigDecimal.ZERO, BigDecimal::add);

        Invoice invoiceUpdate = new Invoice();
        invoiceUpdate.setInvoiceId(invoiceId);
        //在途费用
        invoiceUpdate.setOnWayAmountFee(prepaidOilCard);
        //总金额(应收运费)
        invoiceUpdate.setCostAmount(prepaidCash);
        //总应收
        invoiceUpdate.setTotalFee(totalFee);

        //计算不含税价格
        SysDictData billingTypeDictData = sysDictDataMapper
                .selectDictDataByTypeAndValue("billing_type", invoice.getBillingType());
        BigDecimal rate = billingTypeDictData.getNumVal1();

        //应收运费) 不含税
        if (rate != null && rate.compareTo(BigDecimal.ZERO) != 0) {

            BigDecimal costAmountIncludeTax = NumberUtil.div(prepaidCash, rate, 2);
            invoiceUpdate.setCostAmountIncludeTax(costAmountIncludeTax);
        }else {
            invoiceUpdate.setCostAmountIncludeTax(prepaidCash);
        }

        invoiceUpdate.setCorScrId(corScrId);

        return invoiceMapper.updateInvoice(invoiceUpdate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult projectEdit(ProjectInvoiceEditVO editVO) {
        String invoiceId = editVO.getInvoiceId();
        if (StringUtils.isEmpty(invoiceId)) {
            throw new BusinessException("请输入正确的发货单号。");
        }
        Invoice invoice = invoiceMapper.selectByPrimaryKey(invoiceId);
        if (invoice == null) {
            throw new BusinessException("未查询到发货单，请输入正确的发货单号。");
        }

        if (!InvoiceStatusEnum.NEW.getValue ().equals(invoice.getVbillstatus())) {
            throw new BusinessException("请选择“新建”状态的发货单。");
        }

        Invoice invoiceUpdate = new Invoice();
        BeanUtils.copyProperties(editVO, invoiceUpdate);
        invoiceUpdate.setProjectFileId(editVO.getProjectFileId() == null ? "" : editVO.getProjectFileId());
        invoiceUpdate.setCorScrId("InvoiceService.projectEdit");
        invoiceMapper.updateByPrimaryKeySelective(invoiceUpdate);


        InvoiceDetail updated = new InvoiceDetail();
        updated.setDelFlag(1);
        updated.setDelDate(new Date());
        updated.setDelUserid(shiroUtils.getUserId().toString());
        updated.setCorScrId("InvoiceService.projectEdit");
        invoiceDetailMapper.updateByInvoiceId(updated, invoiceId);


        InvoiceDetailAddr invoiceDetailAddrUpdate = new InvoiceDetailAddr();
        invoiceDetailAddrUpdate.setDelFlag(1);
        invoiceDetailAddrUpdate.setDelDate(new Date());
        invoiceDetailAddrUpdate.setDelUserid(shiroUtils.getUserId().toString());
        invoiceDetailAddrUpdate.setCorScrId("InvoiceService.projectEdit");
        invoiceDetailAddrMapper.updateByInvoiceId(invoiceDetailAddrUpdate, invoiceId);

        List<ProjectInvoiceEditVO.InvoiceDetailEditVO> invoiceDetailList = editVO.getInvoiceDetailList();
        invoiceDetailList.removeIf(x -> x.getReqDeliDate() == null);

        for (ProjectInvoiceEditVO.InvoiceDetailEditVO invoiceDetailEditVO : invoiceDetailList) {
            SysDictData carLenDict = sysDictDataMapper.selectDictDataByTypeAndValue("car_len",
                    invoiceDetailEditVO.getCarLen());
            invoiceDetailEditVO.setCarLenName(carLenDict.getDictLabel());

            SysDictData carTypeDict = sysDictDataMapper.selectDictDataByTypeAndValue("car_type",
                    invoiceDetailEditVO.getCarType());
            invoiceDetailEditVO.setCarTypeName(carTypeDict.getDictLabel());

            SysDictData transCodeDict = sysDictDataMapper.selectDictDataByTypeAndValue("trans_code",
                    invoiceDetailEditVO.getTransCode());
            invoiceDetailEditVO.setTransName(transCodeDict.getDictLabel());

            InvoiceDetail invoiceDetail = new InvoiceDetail();
            BeanUtils.copyProperties(invoiceDetailEditVO, invoiceDetail);

            SysDept sysDepts = deptService.selectDeptById(Long.valueOf(invoiceDetailEditVO.getTransLineId()));

            invoiceDetail.setTransLineName(sysDepts.getDeptName());

            String invoiceDetailId = IdUtil.simpleUUID();

            invoiceDetail.setInvoiceDetailId(invoiceDetailId);
            invoiceDetail.setInvoiceId(invoiceId);
            invoiceDetail.setRegScrId("InvoiceService.projectEdit");
            invoiceDetailMapper.insertSelective(invoiceDetail);


            List<ProjectInvoiceEditVO.InvoiceDetailAddressVO> addressVOList = invoiceDetailEditVO.getAddressVOList();

            addressVOList.removeIf(x -> StringUtils.isEmpty(x.getAddrName()));

            for (ProjectInvoiceEditVO.InvoiceDetailAddressVO invoiceDetailAddressVO : addressVOList) {

                InvoiceDetailAddr invoiceDetailAddr = new InvoiceDetailAddr();
                BeanUtils.copyProperties(invoiceDetailAddressVO, invoiceDetailAddr);
                String invoiceDetailAddrId = IdUtil.simpleUUID();

                invoiceDetailAddr.setInvoiceId(invoiceId);
                invoiceDetailAddr.setInvoiceDetailAddrId(invoiceDetailAddrId);
                invoiceDetailAddr.setInvoiceDetailId(invoiceDetailId);
                invoiceDetailAddr.setRegScrId("InvoiceService.projectEdit");
                invoiceDetailAddrMapper.insertSelective(invoiceDetailAddr);
            }


            //发货地址
            List<ProjectInvoiceEditVO.InvoiceDetailAddressVO> deliAddrList = addressVOList
                    .stream().filter(x -> x.getAddressType() == 0)
                    .collect(Collectors.toList());
            //收货地址
            List<ProjectInvoiceEditVO.InvoiceDetailAddressVO> arriAddrList = addressVOList
                    .stream().filter(x -> x.getAddressType() == 1)
                    .collect(Collectors.toList());


            if (deliAddrList.size() == 1 && arriAddrList.size() == 1) {
                String deliProvinceId = deliAddrList.get(0).getProvinceId();
                String deliProName = deliAddrList.get(0).getProvinceName();

                String deliCityId = deliAddrList.get(0).getCityId();
                String deliCityName = deliAddrList.get(0).getCityName();

                String deliAreaId = deliAddrList.get(0).getAreaId();
                String deliAreaName = deliAddrList.get(0).getAreaName();

                String arriProvinceId = arriAddrList.get(0).getProvinceId();
                String arriProName = arriAddrList.get(0).getProvinceName();

                String arriCityId = arriAddrList.get(0).getCityId();
                String arriCityName = arriAddrList.get(0).getCityName();

                String arriAreaId = arriAddrList.get(0).getAreaId();
                String arriAreaName = arriAddrList.get(0).getAreaName();

                String transCode = invoiceDetailEditVO.getTransCode();

                String carLen = invoiceDetailEditVO.getCarLen();
                String carLenName = invoiceDetailEditVO.getCarLenName();
                String carType = invoiceDetailEditVO.getCarType();
                String carTypeName = invoiceDetailEditVO.getCarTypeName();

                BigDecimal guidingPrice = invoiceDetailEditVO.getGuidingPrice();
//                generateTransLineAndGuide(deliProvinceId, deliProName, deliCityId, deliCityName, deliAreaId, deliAreaName
//                        , arriProvinceId, arriProName, arriCityId, arriCityName, arriAreaId, arriAreaName, transCode
//                        , carLen, carLenName, carType, carTypeName, true, guidingPrice
//                        , "InvoiceService.projectEdit");

            }
        }

        return AjaxResult.success();
    }

    @Override
    @DataScope(deptAlias = "t1.sales_dept,t.sales_dept", userAlias = "t.psndoc")
    public List<Map<String, Object>> ledgerList(Invoice invoice) {
        return invoiceMapper.ledgerList(invoice);
    }

    @Override
    @DataScope(deptAlias = "t1.sales_dept,t.sales_dept", userAlias = "t.psndoc")
    public String downloadReceipt(Invoice invoice, String format) {
        String custAbbr = invoice.getCustAbbr();
        if (StringUtils.isBlank(custAbbr)) {
            throw new BusinessException("请输入客户简称(全匹配)");
        }
        Client client = clientMapper.selectCustomerByCustAbbr(custAbbr);
        if (client == null) {
            throw new BusinessException("未找到“" + custAbbr +"”对应的客户");
        }
        invoice.setCustomerId(client.getCustomerId());
        List<Map<String, Object>> list = invoiceMapper.receiptList(invoice);
        if (list.size() == 0) {
            throw new BusinessException("未找到可下载回单");
        }
        for (int i = 0; i < list.size(); i++) {
            if (list.get(i).get("seq") == null) {
                final Map<String, Object> item = list.get(i);
                String entrustId = (String) item.get("entrustId");
                int total = 1;
                int seq = 1;
                list.get(i).put("seq", seq);
                String deliDate = (String) item.get("deliDate");
                String deliCity = (String) (("市辖区".equals(item.get("deliCityName")) || "县".equals(item.get("deliCityName"))) ? item.get("deliProName") : item.get("deliCityName"));
                String arriCity = (String) (("市辖区".equals(item.get("arriCityName")) || "县".equals(item.get("arriCityName"))) ? item.get("arriProName") : item.get("arriCityName"));
                String carno = (String) item.get("carno");
                String driverName = (String) item.get("driverName");
                for (int j = i + 1; j < list.size(); j++) {
                    final Map<String, Object> item2 = list.get(j);
                    if (item2.get("entrustId").equals(entrustId)) { // 相同委托单的增加第1张、第2张字样
                        item2.put("seq", ++seq);
                        total++;
                    } else {
                        // 不同委托单，同一日期、提到货市、车号、司机增加第1张、第2张子扬
                        String deliDate2 = (String) item2.get("deliDate");
                        String deliCity2 = (String) (("市辖区".equals(item2.get("deliCityName")) || "县".equals(item2.get("deliCityName"))) ? item2.get("deliProName") : item2.get("deliCityName"));
                        String arriCity2 = (String) (("市辖区".equals(item2.get("arriCityName")) || "县".equals(item2.get("arriCityName"))) ? item2.get("arriProName") : item2.get("arriCityName"));
                        String carno2 = (String) item2.get("carno");
                        String driverName2 = (String) item2.get("driverName");
                        if (Objects.equals(deliDate, deliDate2) && Objects.equals(deliCity, deliCity2)
                                && Objects.equals(arriCity, arriCity2) && Objects.equals(carno, carno2) && Objects.equals(driverName, driverName2)) {
                            item2.put("seq", ++seq);
                            total++;
                        }
                    }
                }
                list.get(i).put("total", total);
            }
        }
        String zipFileName = System.currentTimeMillis() + "_" + custAbbr.replaceAll("（", "").replaceAll("）", "") + "回单.zip";
        String zipFilePath = Global.getDownloadPath() + zipFileName;
        File zipFile = new File(zipFilePath);
        if (!zipFile.getParentFile().exists()) {
            zipFile.getParentFile().mkdirs();
        }
        String[] template = (StringUtil.isEmpty(format) ? "date,deli,arri,car,driver" : format).split(",");
        Set<String> checkSame = new HashSet<>();
        int count = 0;
        try (ZipOutputStream zipOut = new ZipOutputStream(new FileOutputStream(zipFile))) {
            int len;
            byte[] b = new byte[1024 * 8];
            for (int i = 0; i < list.size(); i++) {
                //{deliDate=12.04, fileName=null, arriCityName=市辖区, carno=, filePath=, entrustId=, invoiceId=, deliCityName=市辖区, arriProName=上海市, receiptUploadTid=null, deliProName=上海市, fileId=null}
                final Map<String, Object> map = list.get(i);
                String filePath = (String) map.get("filePath");
                if (filePath != null) {
                    filePath = StringUtils.substring(filePath, filePath.indexOf("/") + 8); // 截掉了/static/
                    File image = new File(Global.getProfile() + filePath);
                    //image = new File("C:\\Users\\<USER>\\Pictures\\OIP.jpg");
                    if (image.exists()) {
                        // 10.19 南宁市 河池市 桂AN1857[ 第1张]
                        List<Object> imageNameSplit = new ArrayList<>();
                        for (int j = 0; j < template.length; j++) {
                            if (template[j].equals("date")) {
                                imageNameSplit.add(map.get("deliDate"));
                            } else if (template[j].equals("deli")) {
                                if (map.get("deliCityName") != null) {
                                    imageNameSplit.add((map.get("deliCityName").equals("市辖区") || map.get("deliCityName").equals("县")) ? map.get("deliProName") : map.get("deliCityName"));
                                }
                            } else if (template[j].equals("arri")) {
                                if (map.get("arriCityName") != null) {
                                    imageNameSplit.add((map.get("arriCityName").equals("市辖区") || map.get("arriCityName").equals("县")) ? map.get("arriProName") : map.get("arriCityName"));
                                }
                            } else if (template[j].equals("car")) {
                                if (map.get("carno") != null) {
                                    imageNameSplit.add(map.get("carno"));
                                }
                            } else if (template[j].equals("driver")) {
                                if (map.get("driverName") != null) {
                                    imageNameSplit.add(map.get("driverName"));
                                }
                            } else if (template[j].equals("dateSc")) {
                                if (map.get("dateSc") != null) {
                                    imageNameSplit.add(map.get("dateSc"));
                                }
                            } else if (template[j].equals("arriName")) {
                                if (map.get("arriName") != null) {
                                    imageNameSplit.add(map.get("arriName"));
                                }
                            }
                        }
                        Integer seq = (Integer) map.get("seq");
                        Integer total = (Integer) map.get("total");
                        if (seq > 1 || (total != null && total > 1)) {
                            imageNameSplit.add("第" + seq + "张");
                        }
                        StringBuilder imageName = new StringBuilder(StringUtils.join(imageNameSplit, " "));
                        String fileName = (String) map.get("fileName");
                        if (fileName != null) {
                            int extIndex = fileName.lastIndexOf(".");
                            if (extIndex > 0) {
                                imageName.append(fileName.substring(extIndex));
                            }
                        }
                        if (!checkSame.contains(imageName.toString())) {
                            checkSame.add(imageName.toString());
                            ZipEntry zipEntry = new ZipEntry(imageName.toString()); // 控制压缩包内文件路径及名称
                            zipOut.putNextEntry(zipEntry);
                            try (InputStream fis = new FileInputStream(image)) {
                                while ((len = fis.read(b)) > 0) {
                                    zipOut.write(b, 0, len);
                                }
                            }
                            zipOut.closeEntry();
                            count++;
                        }
                    }
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        if (count == 0) {
            zipFile.delete();
            throw new BusinessException("未找到可下载回单！");
        }
        return zipFileName;
    }

    @Override
    public Map<String, Boolean> affirmProcessCheck(String[] invoiceIds) {
        Map<String, Boolean> result = new HashMap<>();
        String templateId = wecomSpMapper.getTemplateIdByBiz("fhd");
        if (templateId == null) { // 没有审批流程时，标记为“不走审批流程”
            for (int i = 0; i < invoiceIds.length; i++) {
                result.put(invoiceIds[i], false);
            }
            return result;
        }
        List<Map<String, Object>> list = invoiceMapper.listCustomerAndInvoiceBargain(invoiceIds);
        for (int i = 0; i < list.size(); i++) {
            // invoiceId,rfqEnquiryLineId,invoiceBargin,custBargin
            final Map<String, Object> item = list.get(i);
            // 询价单为空 && 合同价客户 && 议价发货单
            result.put((String) item.get("invoiceId"), item.get("rfqEnquiryLineId") == null && item.get("custBargin").equals(0) && item.get("invoiceBargin").equals(1));
        }
        return result;
    }

    @Transactional
    @Override
    public boolean submitWecomProcess(InvoiceAffirmVO.AffirmVO affirmVO) {
        String invoiceId = affirmVO.getInvoiceId();
        Invoice invoice = invoiceMapper.selectByPrimaryKey(invoiceId);
        if (!invoice.getVbillstatus().equals("0")) {
            // 非新建状态不处理，true=后续发货单确认也不处理
            return true;
        }
        String biz = "fhd";
        String templateId = wecomSpMapper.getTemplateIdByBiz(biz);
        if (templateId == null) {
            logger.debug("发货单确认微信审批templateId未配置，跳过提交企业微信");
            return false; // false=执行后续发货单确认
        }

        Map<String, Object> dynaParam = new LinkedHashMap<>();
        dynaParam.put("id", invoiceId);
        Invoice target = new Invoice();
        target.setInvoiceId(invoiceId);
        target.setVbillstatus(InvoiceStatusEnum.IN_APPROVAL.getValue());
        invoiceMapper.updateInvoice(target);

        dynaParam.put("fhdh", invoice.getVbillno()); //发货单号
        dynaParam.put("khjc", invoice.getCustAbbr()); // 客户简称
        dynaParam.put("yqthrq", new SimpleDateFormat("yyyy-MM-dd").format(invoice.getReqDeliDate())); // 要求提货日期
        StringBuilder ssq = new StringBuilder(); // 省市区
        ssq.append(invoice.getDeliProName())
                .append(invoice.getDeliCityName().equals("市辖区") ? "" : invoice.getDeliCityName())
                .append(invoice.getDeliAreaName()).append(" ~ ")
                .append(invoice.getArriProName())
                .append(invoice.getArriCityName().equals("市辖区") ? "" : invoice.getArriCityName())
                .append(invoice.getArriAreaName());
        dynaParam.put("ssq", ssq.toString()); // 提到货省市区
        StringBuilder hl = new StringBuilder();
        if (invoice.getNumCount() != 0) {
            hl.append(invoice.getNumCount()).append("件");
        }
        if (invoice.getWeightCount() != 0) {
            if (hl.length() > 0) {
                hl.append("/");
            }
            hl.append(invoice.getWeightCount()).append("吨");
        }
        if (invoice.getVolumeCount() != 0) {
            if (hl.length() > 0) {
                hl.append("/");
            }
            hl.append(invoice.getVolumeCount()).append("m³");
        }
        dynaParam.put("hl", hl.toString()); // 货量
        dynaParam.put("yqcccx", invoice.getCarLenName() + invoice.getCarTypeName()); // 要求车长车型
        dynaParam.put("jjfs", BillingMethod.getContext(Integer.parseInt(invoice.getBillingMethod()))); // 计价方式
        dynaParam.put("zje", invoice.getTotalFee().toString()); // 总金额
        dynaParam.put("yjsm", invoice.getBargainMemo()); // 议价说明
        //dynaParam.put("yjpz", null); // 议价凭证
        wecomSpMapper.clearSpFile(null, "yjpz", invoiceId, biz);
        if (StringUtils.isNotBlank(invoice.getBargainFileId())) {
            // 根据tid查询所有附件，插入t_sp_files
            wecomSpMapper.batchAddByTid(new String[]{invoice.getBargainFileId()}, "", "yjpz", invoiceId, biz, new Date());
        }

        // customerId,custAbbr,yyzId,yyzName,yyzLeader,yybId,yybName,yybLeader,glbId,glbName,glbLeader
        Map<String, Object> info = clientMapper.selectYyDeptInfo(invoice.getCustomerId());

        List<SysUser> xszfzr = new ArrayList<>();

        String yybName = (String) info.get("yybName");
        if (yybName == null) {
            throw new RuntimeException("单据客户【" + info.get("custAbbr") + "】未配置运营部，请与管理员联系");
        }
        String yybLeader = (String) info.get("yybLeader");
        if (yybLeader == null) {
            throw new RuntimeException("运营部【" + yybName + "】未配置负责人");
        }

        List<SysUser> users = sysUserMapper.getSysUserByUserName(yybLeader);
        if (users.size() == 0) {
            throw new RuntimeException("运营部【" + yybName + "】负责人【" + yybLeader + "】未匹配到用户");
        } else if (users.size() > 1) {
            throw new RuntimeException("运营部【" + yybName + "】负责人【" + yybLeader + "】匹配到多个用户");
        } else {
            xszfzr.add(users.get(0));
        }

        dynaParam.put("xszfzr", xszfzr); // 运营部负责人

        List<Map<String, Object>> customerMaps = invoiceMapper.balaDeptOfCustomer(invoice.getCustomerId());
        if (customerMaps.size() == 0) {
            throw new RuntimeException("未找到该调整单的客户");
        }
        Set<String> jsz = new HashSet<>(); // 结算组
        Set<String> jszDeptId = new HashSet<>();
        for (int i = 0; i < customerMaps.size(); i++) {
            //{customerId,custAbbr,deptName,leader,balaDept}
            Map<String, Object> map = customerMaps.get(i);
            String deptName = (String) map.get("deptName");
            if (StringUtils.isBlank(deptName)) {
                throw new RuntimeException("单据客户“" + map.get("custAbbr") + "”未配置结算组，请与管理员联系");
            }
            jsz.add(deptName);
            jszDeptId.add((String) map.get("balaDept"));
        }
        SysUser condition = new SysUser();
        condition.getParams().put("deptIds", jszDeptId);
        List<SysUser> jszcy = sysUserMapper.listSystemUser(condition);
        if (jszcy.size() == 0) {
            throw new RuntimeException("结算组“" + StringUtils.join(jsz, ",") + "”没有成员，请与管理员联系");
        }
        dynaParam.put("jszcy", jszcy); // 结算组成员
        try {
            String json = objectMapper.writeValueAsString(affirmVO);
            dynaParam.put("$extString", json);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        String spNo = wecomService.submitWecomSp(dynaParam, biz, templateId, shiroUtils.getSysUser());
        try {
            // 将spNo回写到Invoice
            invoiceMapper.writeSpNo(invoiceId, spNo);
        } catch (Exception e) {
            logger.error("回写审批单号异常（此处不触发回滚）：", e);
        }
        return true;
    }

    @Transactional
    @Override
    public int updateInvoice(Invoice invoice) {
        return invoiceMapper.updateInvoice(invoice);
    }

    @Override
    public List<CopyDisDataVO> getCopyDisData(String invoiceNoList) {
        String[] invoiceNos = Convert.toStrArray(invoiceNoList);
        List<CopyDisDataVO> dataList = new ArrayList<>();

        List<Invoice> invoices = invoiceMapper.selectInvoiceByInvoiceVbillnos(invoiceNos);
        for (Invoice invoice : invoices) {
            if (SegmentStatusEnum.TO_DISPATCH.getValue().equals(Convert.toStr(invoice.getSegmentStatus()))) {
                continue;
            }

            CopyDisDataVO copyDisData = new CopyDisDataVO();

            copyDisData.setInvoiceNo(invoice.getVbillno());
            copyDisData.setInvoiceId(invoice.getInvoiceId());

            //线路
            String deliProvinceName = invoice.getDeliProName();
            String deliCityName = invoice.getDeliCityName();
            String deliAreaName = invoice.getDeliAreaName();
            String arriProvinceName = invoice.getArriProName();
            String arriCityName = invoice.getArriCityName();
            String arriAreaName = invoice.getArriAreaName();

            List<String> directMunicipalities = Arrays.asList("上海市", "北京市", "天津市", "重庆市");

            String deli = directMunicipalities.contains(deliProvinceName) ? deliAreaName : deliCityName;
            String arri = directMunicipalities.contains(arriProvinceName) ? arriAreaName : arriCityName;

            copyDisData.setDeliCityName(deli);
            copyDisData.setArriCityName(arri);


            Set<String> lotIdSet = new HashSet<>();
            //
            List<Entrust> entrustList = entrustMapper.selectEntrustListByInvoiceId(invoice.getInvoiceId());

            for (Entrust entrust : entrustList) {
                if ("0".equals(entrust.getIsFleetData()) && "1".equals(entrust.getIsFleetAssign())) {

                    Entrust entSele = new Entrust();
                    entSele.setDelFlag(0);
                    entSele.setBizEntrustId(entrust.getEntrustId());

                    List<Entrust> bizEntrustList = entrustMapper.selectEntrustList(entSele);
                    for (Entrust biz : bizEntrustList) {
                        lotIdSet.add(biz.getLotId());
                    }
                }else {
                    lotIdSet.add(entrust.getLotId());
                }
            }

            String[] lotIds = lotIdSet.toArray(new String[0]);

            List<EntrustLot> entrustLotList = entrustLotMapper.selectEntrustLotByIds(lotIds);

            List<CopyDisDataVO.DisDataVO> disDataList = new ArrayList<>();
            for (EntrustLot entrustLot : entrustLotList) {

                CopyDisDataVO.DisDataVO disData = new CopyDisDataVO.DisDataVO();
                //车长
                disData.setCarLen(StringUtils.isEmpty(entrustLot.getCarLenName()) ? null : entrustLot.getCarLenName() + "米");

                //车牌
                disData.setCarno(entrustLot.getCarNo());

                if (StringUtils.isNotEmpty(entrustLot.getDriverId())) {
                    Driver driver = driverMapper.selectDriverById(entrustLot.getDriverId());

                    disData.setDriverName(driver.getDriverName());
                    disData.setDriverMobile(driver.getPhone());
                    disData.setDriverCardId(driver.getCardId());
                }

                disData.setDriverName2(entrustLot.getDriverName2());
                disData.setDriverMobile2(entrustLot.getDriverMobile2());
                disData.setDriverCardId2(entrustLot.getDriverCardId2());

                disData.setEstimatedArrivalTime(entrustLot.getEstimatedArrivalTime());
                disDataList.add(disData);
            }
            copyDisData.setDisDataList(disDataList);
            dataList.add(copyDisData);
        }

        return dataList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<CopyDisDataVO> copyDisData(String invoiceNoList){

        List<CopyDisDataVO> copyDisData = getCopyDisData(invoiceNoList);

        for (CopyDisDataVO copyDisDatum : copyDisData) {
            Invoice invoiceUpdate = new Invoice();
            invoiceUpdate.setInvoiceId(copyDisDatum.getInvoiceId());
            invoiceUpdate.setIsCopyDisData(1);
            invoiceUpdate.setCopyDisDataUserId(shiroUtils.getUserId().toString());
            invoiceUpdate.setCopyDisDataUserName(shiroUtils.getSysUser().getUserName());
            invoiceUpdate.setCopyDisDataUserDate(new Date());

            invoiceMapper.updateByPrimaryKeySelective(invoiceUpdate);
        }
        return copyDisData;
    }

    @Override
    @Transactional
    public AjaxResult saveOtherFeeCheck(OtherFee otherFee) {
        EntrustCost entrustCost = new EntrustCost();
        entrustCost.setAppendixId(otherFee.getAppendixId());
        entrustCost.setBillingType(otherFee.getBillingType());
        //1第三方费用
        entrustCost.setIsLotFee(1);
        //发货单id
        entrustCost.setInvoiceId(otherFee.getLotId());
        //发货单号
        Invoice invoice = invoiceMapper.selectInvoiceById(otherFee.getLotId());
        /*if(DateUtils.checkDateDistinctMoreDays(invoice.getReqDeliDate())){
            return AjaxResult.error("要求提货日期五天之后无法新增三方费用！");
        }*/
        entrustCost.setInvoiceVbillno(invoice.getVbillno());

        //总金额
        entrustCost.setFeeAmount(otherFee.getFeeAmount());
        //费用类型
        entrustCost.setFeeType(otherFee.getFeeType());
        //备注
        entrustCost.setMemo(otherFee.getMemo());

        entrustCost.setRecBank(otherFee.getRecBank());
        entrustCost.setRecAccount(otherFee.getRecAccount());
        entrustCost.setRecCardNo(otherFee.getRecCardNo());
        entrustCost.setSingleFlag(otherFee.getSingleFlag());

        EntrustCostMain entrustCostMain = new EntrustCostMain();
        BeanUtils.copyProperties(invoice,entrustCostMain);

        //id
        String entrustCostMainId = IdUtil.simpleUUID();
        entrustCostMain.setId(entrustCostMainId);

        entrustCostMain.setInvoiceId(invoice.getInvoiceId());
        //发货单号
        entrustCostMain.setInvoiceNo(invoice.getVbillno());
        //要求车长
        entrustCostMain.setCarLen(invoice.getCarLen());
        entrustCostMain.setCarLenName(invoice.getCarLenName());
        //要求车型ID
        entrustCostMain.setCarType(invoice.getCarType());
        entrustCostMain.setCarTypeName(invoice.getCarTypeName());
        //附件
        entrustCostMain.setAppendixId(otherFee.getAppendixId());
        //新数据
        entrustCostMain.setIsNew(1);

        entrustCostMain.setNumCount(BigDecimal.valueOf(invoice.getNumCount()));
        entrustCostMain.setWeightCount(BigDecimal.valueOf(invoice.getWeightCount()));
        entrustCostMain.setVolumeCount(BigDecimal.valueOf(invoice.getVolumeCount()));

        entrustCostMain.setGoodsName(invoice.getGoodsName());

        entrustCostMain.setCheckStatus(EntrustCostCheckStatusEnum.PENDING_REVIEW_SERVICE.getValue());
        entrustCostMainMapper.insertSelective(entrustCostMain);

        entrustCost.setEntrustCostId(IdUtil.simpleUUID());

        entrustCost.setCheckStatus(EntrustCostCheckStatusEnum.PENDING_REVIEW_SERVICE.getValue());
        entrustCost.setRegScrId("invoiceOtherFee");
        entrustCost.setDelFlag("0");

        entrustCost.setEntrustCostMainId(entrustCostMainId);
        entrustCost.setRowType(2);


        entrustCostMapper.insertSelective(entrustCost);

        traceService.submitWecomSpAsYcfy(entrustCostMain);

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult switchBillingMethod(String invoiceId) {
        Invoice invoice = invoiceMapper.selectByPrimaryKey(invoiceId);

//        if (!invoice.getBillingMethod().equals("3") && !invoice.getBillingMethod().equals("4")) {
//            throw new BusinessException("请选择计价方式为包车（车型）/往返包车（车型）的数据");
//        }

        List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveByInvoiceId(invoiceId);
        //运费
        receiveDetailVOS = receiveDetailVOS.stream().filter(x -> x.getFreeType().equals("0")).collect(Collectors.toList());
//        long count = receiveDetailVOS.stream().filter(x -> ReceiveDetailStatusEnum.AFFIRM.getValue() != x.getVbillstatus()
//                && ReceiveDetailStatusEnum.NEW.getValue() != x.getVbillstatus()).count();
//        if (count > 0) {
//            throw new BusinessException("存在非新建/已确认状态的应收单据");
//        }

        SearchContractPriceVO priceVO = new SearchContractPriceVO();
        List<MultipleShippingAddress> multipleShippingAddresses = multipleShippingAddressMapper
                .selectListByInvoiceId(invoice.getInvoiceId());

        //获取提货区id
        List<String> deliAreaIdList = multipleShippingAddresses.stream()
                .filter(x -> x.getAddressType() == 0
                        && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                .map(MultipleShippingAddress::getAreaId)
                .collect(Collectors.toList());
        priceVO.setDeliAreaIdList(deliAreaIdList);
        //获取提货地址名称
        List<String> deliAddrNameList = multipleShippingAddresses.stream()
                .filter(x -> x.getAddressType() == 0
                        && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                .map(MultipleShippingAddress::getAddrName)
                .collect(Collectors.toList());
        priceVO.setDeliAddrNameList(deliAddrNameList);

        //获取到货区id
        List<String> arriAreaIdList = new ArrayList<>();
        List<String> arriArriNameList = new ArrayList<>();

        for(MultipleShippingAddress multipleShippingAddress : multipleShippingAddresses){
            if(multipleShippingAddress.getAddressType() == 1
                    && multipleShippingAddress.getIsGetContractPrice() != null
                    && multipleShippingAddress.getIsGetContractPrice() == 1){
                if(multipleShippingAddress.getIsChangeAddress() != null && multipleShippingAddress.getIsChangeAddress() == 1){
                    arriAreaIdList.add(multipleShippingAddress.getCaArriAreaId());
                    arriArriNameList.add(multipleShippingAddress.getCaArriAddrName());
                }else{
                    arriAreaIdList.add(multipleShippingAddress.getAreaId());
                    arriArriNameList.add(multipleShippingAddress.getAddrName());
                }
            }
        }
        priceVO.setArriAreaIdList(arriAreaIdList);
        priceVO.setArriAddrNameList(arriArriNameList);


        Set<String> goodsNames = new HashSet<>();
        for (MultipleShippingAddress multipleShippingAddress : multipleShippingAddresses) {
            if (multipleShippingAddress.getAddressType() == 0) {
                List<MultipleShippingGoods> goods = multipleShippingGoodsMapper
                        .selectListByAddressId(multipleShippingAddress.getMultipleShippingAddressId());
                for (MultipleShippingGoods good : goods) {
                    goodsNames.add(good.getGoodsName());
                }
            }
        }

        if (goodsNames.size() == 1) {
            priceVO.setGoodsName(goodsNames.stream().findFirst().get());
        }

        //客户id
        priceVO.setCustomerId(invoice.getCustomerId());
        //车长
        priceVO.setCarLen(invoice.getCarLen());
        priceVO.setCarType(invoice.getCarType());
        //货品特性
//        String goodsCharacter = "15".equals(invoice.getTransCode()) || "16".equals(invoice.getTransCode()) ? "1" : "0";
        String goodsCharacter;
        if ("15".equals(invoice.getTransCode()) || "16".equals(invoice.getTransCode())) {
            goodsCharacter = "1";
        } else if ("4".equals(invoice.getTransCode())) {
            goodsCharacter = "2";
        }else {
            goodsCharacter = "0";

        }

        priceVO.setGoodsCharacter(goodsCharacter);

//        String billingMethod = invoice.getBillingMethod().equals("3") ? "4" : "3";
//        priceVO.setBillingMethod(Integer.valueOf(billingMethod));
        priceVO.setBillingMethod(Integer.valueOf(invoice.getBillingMethod()));

        priceVO.setNum(BigDecimal.valueOf(invoice.getNumCount()));
        priceVO.setWeight(BigDecimal.valueOf(invoice.getWeightCount()));
        priceVO.setVolume(BigDecimal.valueOf(invoice.getVolumeCount()));
        priceVO.setMileage(invoice.getMileage() == null ? BigDecimal.ZERO : BigDecimal.valueOf(invoice.getMileage()));

        priceVO.setIsOversize(invoice.getIsOversize());
        priceVO.setTransCode(invoice.getTransCode());

        int isRoundTrip = invoice.getIsRoundTrip() == 0 ? 1 : 0;
        priceVO.setIsRoundTrip(isRoundTrip);

//        if (billingMethod.equals("4")) {
//            priceVO.setIsRoundTrip(1);
//        }else {
//            priceVO.setIsRoundTrip(0);
//        }

        Map<String, String> priceMap = getPrice(priceVO);
        if ("0".equals(priceMap.get("type"))) {
            throw new BusinessException("未查询到合同价");
        }
        if ("".equals(priceMap.get("costPrice"))) {
            throw new RuntimeException("未查询到成本价");
        }

        Invoice invoiceUpdate = new Invoice();

//        invoiceUpdate.setBillingMethod(billingMethod);
//        if (billingMethod.equals("4")) {
//            invoiceUpdate.setIsRoundTrip(1);
//        }else {
//            invoiceUpdate.setIsRoundTrip(0);
//        }
        invoiceUpdate.setIsRoundTrip(isRoundTrip);


        SysDictData billingTypeDictData = sysDictDataMapper
                .selectDictDataByTypeAndValue("billing_type", invoice.getBillingType());

        //税率
        BigDecimal rate = billingTypeDictData.getNumVal1();

        //是否含税  0不含税  1含税*
        String isIncludeTax = priceMap.get("isIncludeTax");

        //总价
        BigDecimal totalPrice = Convert.toBigDecimal(priceMap.get("totalPrice"));
        //单价
        BigDecimal price = Convert.toBigDecimal(priceMap.get("price"));
        //成本价
        BigDecimal totalCostPrice = Convert.toBigDecimal(priceMap.get("totalCostPrice"));
        String costBillingType = priceMap.get("costBillingType");

        Integer isFixedPrice = Convert.toInt(priceMap.get("isFixedPrice"), null);

        String contractpcVersionId = priceMap.get("versionId");

        invoiceUpdate.setContractIsFixedPrice(isFixedPrice);
        //
        invoiceUpdate.setContractPrice(price);
        //成本价
        invoiceUpdate.setCostPrice(totalCostPrice);
        invoice.setCostBillingType(totalCostPrice == null ? null : costBillingType);

        invoiceUpdate.setContractPriceTotal(totalPrice);
        //合同价版本id
        invoiceUpdate.setContractpcVersionId(contractpcVersionId);

        //是否含税  0不含税  1含税*
        if ("0".equals(isIncludeTax)) {
            //不含税
            invoiceUpdate.setCostAmountIncludeTax(totalPrice);
            invoiceUpdate.setUnitPriceIncludeTax(price);

            if (rate != null && rate.compareTo(BigDecimal.ZERO) != 0) {
                invoiceUpdate.setCostAmount(NumberUtil.round(NumberUtil.mul(totalPrice, rate), 2));
                invoiceUpdate.setUnitPrice(NumberUtil.round(NumberUtil.mul(price, rate), 2));
            }else {
                invoiceUpdate.setCostAmount(totalPrice);
                invoiceUpdate.setUnitPrice(price);
            }
        }else {
            //含税
            invoiceUpdate.setCostAmount(totalPrice);
            invoiceUpdate.setUnitPrice(price);
            if (rate != null && rate.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal costAmountIncludeTax = NumberUtil.div(invoiceUpdate.getCostAmount(), rate, 2);
                invoiceUpdate.setCostAmountIncludeTax(costAmountIncludeTax);

                BigDecimal unitPriceIncludeTax = NumberUtil.div(invoiceUpdate.getUnitPrice(), rate, 2);
                invoiceUpdate.setUnitPriceIncludeTax(unitPriceIncludeTax);

            }else {
                invoiceUpdate.setCostAmountIncludeTax(totalPrice);
                invoiceUpdate.setUnitPriceIncludeTax(price);
            }
        }

        //计算总金额
        invoiceUpdate.setTotalFee(NumberUtil.add(invoiceUpdate.getCostAmount(),invoice.getOnWayAmountFee()));
        invoiceUpdate.setInvoiceId(invoiceId);
        invoiceUpdate.setCorScrId("InvoiceService.switchBillingMethod");

        int i = invoiceMapper.updateInvoice(invoiceUpdate);

        if (i != 1) {
            throw new BusinessException("转换失败，请稍后重试");
        }

        /*
         * 更新应收
         */

        if (!InvoiceStatusEnum.NEW.getValue().equals(invoice.getVbillstatus())) {
            BigDecimal frightAmount = receiveDetailVOS.stream().map(ReceiveDetailVO::getTransFeeCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            ReceiveDetailVO receiveDetailOld = receiveDetailVOS.get(0);
            ReceiveDetailVO receInsert = new ReceiveDetailVO();
            //新应收金额
            BigDecimal sub = NumberUtil.sub(invoiceUpdate.getCostAmount(), frightAmount);

            BeanUtils.copyBeanProp(receInsert, receiveDetailOld);
            receInsert.setVbillstatus(ReceiveDetailStatusEnum.NEW.getValue());
            receInsert.setTransFeeCount(sub);
            receInsert.setGotAmount(null);
            receInsert.setUngotAmount(null);
            receInsert.setReceiveDetailId(IdUtil.simpleUUID());
            receInsert.setVbillno(receiveDetailService.createReceiveDetailVbillno(Integer.parseInt(receiveDetailOld.getIsFleetData())));
            receInsert.setRegScrId("InvoiceService.switchBillingMethod");

            int i1 = receiveDetailService.insertReceiveDetailAndAdjustRecord(receInsert);

            if (i1 != 1) {
                throw new BusinessException("转换失败，请稍后重试");
            }

        }

        return AjaxResult.success();
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateContractPrice(String invoiceIds) {
        String[] ids = Convert.toStrArray(invoiceIds);
        
        // 记录不成功的发货单及原因
        Map<String, String> failedInvoices = new HashMap<>();

        List<Invoice> invoices = invoiceMapper.selectInvoiceByIds(ids);

//        long count = invoices.stream()
//                .filter(x -> x.getIfBargain() != 0)
//                .count();
//        if (count > 0) {
//            throw new BusinessException("请选择”非议价”的数据");
//        }

        for (Invoice i : invoices) {
            try {
                if (i.getIfBargain() != 0) {
                    failedInvoices.put(i.getVbillno(), "议价数据");
                    continue;
                }
                SearchContractPriceVO priceVO = new SearchContractPriceVO();

                List<MultipleShippingAddress> shippingAddressList = multipleShippingAddressMapper
                        .selectListByInvoiceId(i.getInvoiceId());

                List<String> deliAreaIdList = shippingAddressList.stream()
                        .filter(x -> x.getAddressType() == 0
                                && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                        .map(MultipleShippingAddress::getAreaId)
                        .collect(Collectors.toList());
                priceVO.setDeliAreaIdList(deliAreaIdList);

                List<String> deliAddrNameList = shippingAddressList.stream()
                        .filter(x -> x.getAddressType() == 0
                                && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                        .map(MultipleShippingAddress::getAddrName)
                        .collect(Collectors.toList());
                priceVO.setDeliAddrNameList(deliAddrNameList);

                List<String> arriAreaIdList = new ArrayList<>();
                List<String> arriArriNameList = new ArrayList<>();

                for(MultipleShippingAddress multipleShippingAddress : shippingAddressList){
                    if(multipleShippingAddress.getAddressType() == 1
                            && multipleShippingAddress.getIsGetContractPrice() != null
                            && multipleShippingAddress.getIsGetContractPrice() == 1){
                        if(multipleShippingAddress.getIsChangeAddress() != null && multipleShippingAddress.getIsChangeAddress() == 1){
                            arriAreaIdList.add(multipleShippingAddress.getCaArriAreaId());
                            arriArriNameList.add(multipleShippingAddress.getCaArriAddrName());
                        }else{
                            arriAreaIdList.add(multipleShippingAddress.getAreaId());
                            arriArriNameList.add(multipleShippingAddress.getAddrName());
                        }
                    }
                }

                priceVO.setArriAreaIdList(arriAreaIdList);
                priceVO.setArriAddrNameList(arriArriNameList);

                Set<String> goodsNames = new HashSet<>();
                for (MultipleShippingAddress multipleShippingAddress : shippingAddressList) {
                    if (multipleShippingAddress.getAddressType() == 0) {
                        List<MultipleShippingGoods> goods = multipleShippingGoodsMapper
                                .selectListByAddressId(multipleShippingAddress.getMultipleShippingAddressId());
                        for (MultipleShippingGoods good : goods) {
                            goodsNames.add(good.getGoodsName());
                        }
                    }
                }

                if (goodsNames.size() == 1) {
                    priceVO.setGoodsName(goodsNames.stream().findFirst().get());
                }

                priceVO.setCustomerId(i.getCustomerId());
                //车长
                priceVO.setCarLen(i.getCarLen());
                priceVO.setCarType(i.getCarType());
                //货品特性
//                String goodsCharacter = "15".equals(i.getTransCode()) || "16".equals(i.getTransCode()) ? "1" : "0";
                String goodsCharacter;
                if ("15".equals(i.getTransCode()) || "16".equals(i.getTransCode())) {
                    goodsCharacter = "1";
                } else if ("4".equals(i.getTransCode())) {
                    goodsCharacter = "2";
                }else {
                    goodsCharacter = "0";

                }

                priceVO.setGoodsCharacter(goodsCharacter);

                priceVO.setBillingMethod(Integer.valueOf(i.getBillingMethod()));

                priceVO.setNum(BigDecimal.valueOf(i.getNumCount()));
                priceVO.setWeight(BigDecimal.valueOf(i.getWeightCount()));
                priceVO.setVolume(BigDecimal.valueOf(i.getVolumeCount()));
                priceVO.setMileage(i.getMileage() == null ? BigDecimal.ZERO : BigDecimal.valueOf(i.getMileage()));
                priceVO.setIsOversize(i.getIsOversize());
                priceVO.setTransCode(i.getTransCode());

                priceVO.setIsRoundTrip(i.getIsRoundTrip());
                Map<String, String> priceMap = getPrice(priceVO);
                if ("0".equals(priceMap.get("type"))) {
                    // return AjaxResult.error(priceMap.get("msg"));

                    failedInvoices.put(i.getVbillno(), priceMap.get("msg"));
                    continue;
                }

                if ("".equals(priceMap.get("costPrice"))) {
                    // return AjaxResult.error("未查询到成本价。");

                    failedInvoices.put(i.getVbillno(), "未查询到成本价");
                    continue;
                }

                Invoice invoiceUpdate = new Invoice();

                SysDictData billingTypeDictData = sysDictDataMapper
                        .selectDictDataByTypeAndValue("billing_type", i.getBillingType());
                //税率
                BigDecimal rate = billingTypeDictData.getNumVal1();
                //是否含税  0不含税  1含税*
                String isIncludeTax = priceMap.get("isIncludeTax");

                //总价
                BigDecimal totalPrice = Convert.toBigDecimal(priceMap.get("totalPrice"));
                //单价
                BigDecimal price = Convert.toBigDecimal(priceMap.get("price"));
                //成本价
                BigDecimal totalCostPrice = Convert.toBigDecimal(priceMap.get("totalCostPrice"));
                String costBillingType = priceMap.get("costBillingType");

                Integer isFixedPrice = Convert.toInt(priceMap.get("isFixedPrice"), null);

                String contractpcVersionId = priceMap.get("versionId");

                if (!InvoiceStatusEnum.NEW.getValue().equals(i.getVbillstatus())) {
                    //成本价
                    invoiceUpdate.setCostPrice(totalCostPrice);
                    invoiceUpdate.setCostBillingType(totalCostPrice == null ? null : costBillingType);

                } else {

                    //合同价是否是固定价
                    invoiceUpdate.setContractIsFixedPrice(isFixedPrice);
                    //合同单价
                    invoiceUpdate.setContractPrice(price);
                    //合同总价
                    invoiceUpdate.setContractPriceTotal(totalPrice);
                    //合同价版本id
                    invoiceUpdate.setContractpcVersionId(contractpcVersionId);


                    //是否含税  0不含税  1含税*
                    if ("0".equals(isIncludeTax)) {
                        //不含税
                        invoiceUpdate.setCostAmountIncludeTax(totalPrice);
                        invoiceUpdate.setUnitPriceIncludeTax(price);

                        if (rate != null && rate.compareTo(BigDecimal.ZERO) != 0) {
                            invoiceUpdate.setCostAmount(NumberUtil.round(NumberUtil.mul(totalPrice, rate), 2));
                            invoiceUpdate.setUnitPrice(NumberUtil.round(NumberUtil.mul(price, rate), 2));
                        }else {
                            invoiceUpdate.setCostAmount(totalPrice);
                            invoiceUpdate.setUnitPrice(price);
                        }
                    }else {
                        //含税
                        invoiceUpdate.setCostAmount(totalPrice);
                        invoiceUpdate.setUnitPrice(price);
                        if (rate != null && rate.compareTo(BigDecimal.ZERO) != 0) {
                            BigDecimal costAmountIncludeTax = NumberUtil.div(invoiceUpdate.getCostAmount(), rate, 2);
                            invoiceUpdate.setCostAmountIncludeTax(costAmountIncludeTax);

                            BigDecimal unitPriceIncludeTax = NumberUtil.div(invoiceUpdate.getUnitPrice(), rate, 2);
                            invoiceUpdate.setUnitPriceIncludeTax(unitPriceIncludeTax);

                        }else {
                            invoiceUpdate.setCostAmountIncludeTax(totalPrice);
                            invoiceUpdate.setUnitPriceIncludeTax(price);
                        }
                    }

                    //送货费
                    invoiceUpdate.setDeliveryFee(Convert.toBigDecimal(priceMap.get("deliveryFee")));
                    //计算总金额
                    invoiceUpdate.setTotalFee(NumberUtil.add(invoiceUpdate.getCostAmount(), i.getOtherFee()
                            , invoiceUpdate.getDeliveryFee()));
                    //在途费用
                    invoiceUpdate.setOnWayAmountFee(NumberUtil.add(i.getOtherFee(), invoiceUpdate.getDeliveryFee()));

                    //成本价
                    invoiceUpdate.setCostPrice(totalCostPrice);
                    invoiceUpdate.setCostBillingType(totalCostPrice == null ? null : costBillingType);

                }

                invoiceUpdate.setInvoiceId(i.getInvoiceId());
                invoiceUpdate.setCorScrId("InvoiceService.updateContractPrice");

                int ct = invoiceMapper.updateInvoice(invoiceUpdate);

                if (ct != 1) {
                    // throw new BusinessException("更新失败，请稍后重试");
                    failedInvoices.put(i.getVbillno(), "更新失败，请稍后重试");
                }


                List<Segment> segments = segmentMapper.selectSegmentByInvoiceId(i.getInvoiceId());

                if (segments.size() == 1 && segments.get(0).getSegMark() == 2 && segments.get(0).getSegType() == 2) {
                    List<ContractpcCostPriceLog> contractpcCostPriceLogs = contractpcCostPriceLogMapper
                            .selectBySegmentId(segments.get(0).getSegmentId());

                    if (!contractpcCostPriceLogs.isEmpty()) {
                        continue;
                    }

                    Segment segmentUpdate = new Segment();
                    segmentUpdate.setSegmentId(segments.get(0).getSegmentId());
                    segmentUpdate.setCostPrice(invoiceUpdate.getCostPrice());
                    segmentUpdate.setCostBillingType(invoiceUpdate.getCostBillingType());

                    int i1 = segmentMapper.updateSegment(segmentUpdate);

                    if (i1 != 1) {
                        failedInvoices.put(i.getVbillno(), "更新失败，请稍后重试");
                    }
                }


            }catch (Exception e) {
                failedInvoices.put(i.getVbillno(), "处理异常: " + e.getMessage());
            }
        }

        if (failedInvoices.isEmpty()) {
            return AjaxResult.success("所有发货单更新成功");
        } else {
            return AjaxResult.success("部分发货单更新成功", failedInvoices);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateContractPrice_1() {
        logger.info("开始2025-01-01之后发货单的成本价");

        //查询2025-01-01之后的发货单
        List<Invoice> list = invoiceMapper.selectCostPriceIsNullAfter20250101();
        logger.info("共获取到：{}条成本价为空的数据",list.size());

        for (Invoice i : list) {
            logger.info("开始处理：{}",i.getVbillno());

            SearchContractPriceVO priceVO = new SearchContractPriceVO();

            List<MultipleShippingAddress> shippingAddressList = multipleShippingAddressMapper
                    .selectListByInvoiceId(i.getInvoiceId());

            List<String> deliAreaIdList = shippingAddressList.stream()
                    .filter(x -> x.getAddressType() == 0
                            && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                    .map(MultipleShippingAddress::getAreaId)
                    .collect(Collectors.toList());
            priceVO.setDeliAreaIdList(deliAreaIdList);

            List<String> deliAddrNameList = shippingAddressList.stream()
                    .filter(x -> x.getAddressType() == 0
                            && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                    .map(MultipleShippingAddress::getAddrName)
                    .collect(Collectors.toList());
            priceVO.setDeliAddrNameList(deliAddrNameList);

            List<String> arriAreaIdList = new ArrayList<>();
            List<String> arriArriNameList = new ArrayList<>();

            for(MultipleShippingAddress multipleShippingAddress : shippingAddressList){
                if(multipleShippingAddress.getAddressType() == 1
                        && multipleShippingAddress.getIsGetContractPrice() != null
                        && multipleShippingAddress.getIsGetContractPrice() == 1){
                    if(multipleShippingAddress.getIsChangeAddress() != null && multipleShippingAddress.getIsChangeAddress() == 1){
                        arriAreaIdList.add(multipleShippingAddress.getCaArriAreaId());
                        arriArriNameList.add(multipleShippingAddress.getCaArriAddrName());
                    }else{
                        arriAreaIdList.add(multipleShippingAddress.getAreaId());
                        arriArriNameList.add(multipleShippingAddress.getAddrName());
                    }
                }
            }

            priceVO.setArriAreaIdList(arriAreaIdList);
            priceVO.setArriAddrNameList(arriArriNameList);

            Set<String> goodsNames = new HashSet<>();
            for (MultipleShippingAddress multipleShippingAddress : shippingAddressList) {
                if (multipleShippingAddress.getAddressType() == 0) {
                    List<MultipleShippingGoods> goods = multipleShippingGoodsMapper
                            .selectListByAddressId(multipleShippingAddress.getMultipleShippingAddressId());
                    for (MultipleShippingGoods good : goods) {
                        goodsNames.add(good.getGoodsName());
                    }
                }
            }

            if (goodsNames.size() == 1) {
                priceVO.setGoodsName(goodsNames.stream().findFirst().get());
            }

            priceVO.setCustomerId(i.getCustomerId());
            //车长
            priceVO.setCarLen(i.getCarLen());
            priceVO.setCarType(i.getCarType());
            //货品特性
//            String goodsCharacter = "15".equals(i.getTransCode()) || "16".equals(i.getTransCode()) ? "1" : "0";
            String goodsCharacter;
            if ("15".equals(i.getTransCode()) || "16".equals(i.getTransCode())) {
                goodsCharacter = "1";
            } else if ("4".equals(i.getTransCode())) {
                goodsCharacter = "2";
            }else {
                goodsCharacter = "0";
            }

            priceVO.setGoodsCharacter(goodsCharacter);

            priceVO.setBillingMethod(Integer.valueOf(i.getBillingMethod()));

            priceVO.setNum(BigDecimal.valueOf(i.getNumCount()));
            priceVO.setWeight(BigDecimal.valueOf(i.getWeightCount()));
            priceVO.setVolume(BigDecimal.valueOf(i.getVolumeCount()));
            priceVO.setMileage(i.getMileage() == null ? BigDecimal.ZERO : BigDecimal.valueOf(i.getMileage()));
            priceVO.setIsOversize(i.getIsOversize());
            priceVO.setTransCode(i.getTransCode());

            priceVO.setIsRoundTrip(i.getIsRoundTrip());
            Map<String, String> priceMap = getPrice(priceVO);
            if ("0".equals(priceMap.get("type"))) {
                logger.info("未查询到合同价：{}，{}", i.getVbillno(), priceMap);
                continue;
            }

            if ("".equals(priceMap.get("costPrice"))) {
                logger.info("未查询到成本价：{}，{}", i.getVbillno(), priceMap);
                continue;
            }
            logger.info("查询到成本价：{}，{}", i.getVbillno(), priceMap);
            if (i.getContractPrice() == null) {
                logger.info("单据合同价为空：{}", i.getVbillno());
                continue;
            }

            //总价
//            BigDecimal totalPrice = Convert.toBigDecimal(priceMap.get("totalPrice"));
            BigDecimal price = Convert.toBigDecimal(priceMap.get("price"));
            //成本价
            BigDecimal totalCostPrice = Convert.toBigDecimal(priceMap.get("totalCostPrice"));
            String costBillingType = priceMap.get("costBillingType");

            if (price.compareTo(i.getContractPrice()) ==0) {
                Invoice invoiceUpdate = new Invoice();
                //成本价
                invoiceUpdate.setCostPrice(totalCostPrice);
                invoiceUpdate.setCostBillingType(totalCostPrice == null ? null : costBillingType);

                invoiceUpdate.setInvoiceId(i.getInvoiceId());

                invoiceUpdate.setCorScrId("InvoiceService.updateContractPrice_1");

                int ct = invoiceMapper.updateInvoice(invoiceUpdate);

                if (ct == 1) {
                    logger.info("更新成功：{}", i.getVbillno());
                }else {
                    logger.info("更新失败：{}", i.getVbillno());
                }

            }else {
                logger.info("查询到的合同价与单据的合同价不相等，更新失败：{}", i.getVbillno());
            }
        }



        return AjaxResult.success();
    }

}



