package com.ruoyi.tms.service.dispatch.impl;

import cn.hutool.core.util.NumberUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.utils.ExceptionUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.domain.SysDictData;
import com.ruoyi.system.domain.SysUser;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.tms.constant.basic.CarrierProtocolPricingMethodEnum;
import com.ruoyi.tms.domain.basic.Address;
import com.ruoyi.tms.domain.basic.Car;
import com.ruoyi.tms.domain.basic.Carrier;
import com.ruoyi.tms.domain.basic.Driver;
import com.ruoyi.tms.domain.carrier.Entrust;
import com.ruoyi.tms.domain.carrier.EntrustLot;
import com.ruoyi.tms.domain.client.CustAddress;
import com.ruoyi.tms.domain.dispatch.LtlDispatch;
import com.ruoyi.tms.domain.finance.PayDetail;
import com.ruoyi.tms.domain.invoice.MultipleShippingAddress;
import com.ruoyi.tms.domain.message.SysMessage;
import com.ruoyi.tms.domain.segment.Segment;
import com.ruoyi.tms.domain.trace.EntrustCost;
import com.ruoyi.tms.handler.WecomHandler;
import com.ruoyi.tms.mapper.basic.AddressMapper;
import com.ruoyi.tms.mapper.basic.CarMapper;
import com.ruoyi.tms.mapper.basic.CarrierMapper;
import com.ruoyi.tms.mapper.basic.DriverMapper;
import com.ruoyi.tms.mapper.carrier.EntrustLotDepositMapper;
import com.ruoyi.tms.mapper.carrier.EntrustLotMapper;
import com.ruoyi.tms.mapper.carrier.EntrustMapper;
import com.ruoyi.tms.mapper.client.ClientMapper;
import com.ruoyi.tms.mapper.client.CustAddressMapper;
import com.ruoyi.tms.mapper.finance.PayDetailMapper;
import com.ruoyi.tms.mapper.finance.ReceivableReconciliationMapper;
import com.ruoyi.tms.mapper.invoice.MultipleShippingAddressMapper;
import com.ruoyi.tms.mapper.segment.SegmentMapper;
import com.ruoyi.tms.mapper.wecom.WecomSpMapper;
import com.ruoyi.tms.service.dispatch.IDispatchXService;
import com.ruoyi.tms.service.message.ISysMessageService;
import com.ruoyi.tms.service.segment.ISegmentService;
import com.ruoyi.tms.service.wecom.IWecomService;
import com.ruoyi.tms.vo.basic.CarrierProtocolDTO;
import com.ruoyi.tms.vo.message.MsgVO;
import com.ruoyi.tms.vo.segment.DispatchVO;
import com.ruoyi.tms.vo.segment.SubsectionAddressVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DispatchXServiceImpl implements IDispatchXService {

    @Resource
    private SegmentMapper segmentMapper;
    @Resource
    private CarrierMapper carrierMapper;
    @Resource
    private DriverMapper driverMapper;
    @Resource
    private CarMapper carMapper;
    @Lazy
    @Resource
    private ISegmentService segmentService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private AddressMapper addressMapper;
    @Resource
    private CustAddressMapper custAddressMapper;
    //@Resource
    //private ShiroUtils shiroUtils;
    @Resource
    private ISysDictDataService sysDictDataService;
    @Resource
    private ObjectMapper objectMapper;
    //@Resource
    //private CarrierPeriodPriceSQLMapper carrierPeriodPriceSQLMapper;
    //@Resource
    //private CarrierPeriodSQLMapper carrierPeriodSQLMapper;
    @Resource
    private IWecomService wecomService;
    @Resource
    private EntrustLotMapper entrustLotMapper;
    @Resource
    private PayDetailMapper payDetailMapper;
    @Resource
    private ISysUserService userService;
    @Resource
    private EntrustMapper entrustMapper;
    @Resource
    private ReceivableReconciliationMapper receivableReconciliationMapper;
    @Resource
    private WecomHandler wecomHandler;
    @Resource
    private SysDeptMapper deptMapper;
    @Resource
    private WecomSpMapper wecomSpMapper;
    @Resource
    private ClientMapper clientMapper;
    @Resource
    private EntrustLotDepositMapper entrustLotDepositMapper;
    @Resource
    private MultipleShippingAddressMapper multipleShippingAddressMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private ISysMessageService sysMessageService;

    @Transactional
    @Override
    public AjaxResult batchLtlDispatch(List<LtlDispatch> list) {
        List<String> vbillnos = list.stream().map(LtlDispatch::getVbillno).collect(Collectors.toList());
        List<Segment> segments = segmentMapper.selectSegmentByVbillnos(vbillnos);
        List<String> errors = new ArrayList<>();
        // 1、校验
        for (int i = 0; i < list.size(); i++) {
            LtlDispatch ltl = list.get(i);
            List<String> error = new ArrayList<>();
            String vbillno = ltl.getVbillno();
            for (int j = 0; j < segments.size(); j++) {
                if (segments.get(j).getVbillno().equals(vbillno)) {
                    ltl.setSegment(segments.remove(j));
                    break;
                }
            }
            // 校验运段状态
            if (ltl.getSegment() == null) {
                error.add("运段“" + ltl.getVbillno() + "”未匹配到系统数据");
            } else {
                Segment segment = ltl.getSegment();
                if (!segment.getVbillstatus().equals("0")) {
                    error.add("运段“" + ltl.getVbillno() + "”不是待调度状态");
                } else if (segment.getSegMark() != 2) {
                    error.add("运段“" + ltl.getVbillno() + "”不是有效运段");
                } else if (!segment.getTransCode().equals("1") && !segment.getTransCode().equals("16")) {
                    error.add("运段“" + ltl.getVbillno() + "”不是零担数据");
                }
                // t.seg_mark = 2 and t.vbillstatus = '0' and t.trans_line_id = '161'

            }
            // 短驳参数校验
            if (ltl.getShortPrice() != null) { // 有短驳价格视为有短驳数据
                // 承运商校验
                List<Carrier> carrierList = carrierMapper.selectCarrierByAbsoluteCarrName(ltl.getShortCarrier(), null);
                if (carrierList.size() > 1) {
                    error.add("找到多条短驳承运商“" + ltl.getShortCarrier() + "”的信息");
                } else if (carrierList.size() == 0) {
                    error.add("未找到短驳承运商“" + ltl.getShortCarrier() + "”的信息");
                } else {
                    Carrier carrier = carrierList.get(0);
                    ltl.setShortCarrierObj(carrier);
                    List<Car> carList = carMapper.listCarByCarrierIdAndNo(carrier.getCarrierId(), ltl.getShortCar());
                    if (carList.size() > 1) {
                        error.add("短驳承运商“" + ltl.getShortCarrier() + "”下找到多条车号为“" + ltl.getShortCar() + "”的车辆信息");
                    } else if (carList.size() == 0) {
                        error.add("短驳承运商“" + ltl.getShortCarrier() + "”下未找到车号为“" + ltl.getShortCar() + "”的车辆信息");
                    } else {
                        ltl.setShortCarObj(carList.get(0));
                    }
                    List<Driver> driverList = driverMapper.listDriverByCarrierIdAndName(carrier.getCarrierId(), ltl.getShortDriver());
                    if (driverList.size() > 1) {
                        error.add("短驳承运商“" + ltl.getShortCarrier() + "”下找到多条名为“" + ltl.getShortDriver() + "”的司机信息");
                    } else if (driverList.size() == 0) {
                        error.add("短驳承运商“" + ltl.getShortCarrier() + "”下未找到名为“" + ltl.getShortDriver() + "”的司机信息");
                    } else {
                        ltl.setShortDriverObj(driverList.get(0));
                    }
                }
            }
            // 专线参数校验
            List<Carrier> carrierList = carrierMapper.selectCarrierByAbsoluteCarrName(ltl.getCarrName(), null); // 原先carrType="3"承运专线
            if (carrierList.size() > 1) {
                error.add("专线承运商“" + ltl.getCarrName() + "”存在多条信息");
            } else if (carrierList.size() == 0) {
                error.add("未找到专线承运商“" + ltl.getCarrName() + "”的信息");
            } else if (ltl.getSegment() != null) {
                Carrier carrier = carrierList.get(0);
                ltl.setCarrierObj(carrier);
                // 协议价单价校验
                // CarrierProtocolPricingMethodEnum: 0=按吨,1=按方,2=按件,3=按票,4=按吨(包车),5=按方(包车),6=按件(包车)
                // 只有012才有协议价
                BigDecimal fee = null;
                boolean activeMinPrice = false; // 是否触发了保底价
                BigDecimal minPrice = null;
                if (ltl.getPricingMethod() == 0 || ltl.getPricingMethod() == 1 || ltl.getPricingMethod() == 2) {
                    if (ltl.getPricingMethod() == 0 && (ltl.getSegment().getWeightCount() == null || ltl.getSegment().getWeightCount().doubleValue() == 0)) {
                        error.add("运段“" + ltl.getVbillno() + "”按吨计价时没有重量");
                    } else if (ltl.getPricingMethod() == 1 && (ltl.getSegment().getVolumeCount() == null || ltl.getSegment().getVolumeCount().doubleValue() == 0)) {
                        error.add("运段“" + ltl.getVbillno() + "”按方计价时没有方数");
                    } else if (ltl.getPricingMethod() == 2 && (ltl.getSegment().getNumCount() == null || ltl.getSegment().getNumCount().doubleValue() == 0)) {
                        error.add("运段“" + ltl.getVbillno() + "”按件计价时没有件数");
                    } else { // 满足判断协议价的条件
                        String startAreaId = ltl.getShortPrice() == null ? ltl.getSegment().getDeliAreaId() : "320613"; // 没有短驳段时取运段原发货地，否则取默认中转区
                        String endAreaId = ltl.getSegment().getArriAreaId();
                        CarrierProtocolDTO condition = new CarrierProtocolDTO();
                        condition.setStartAreaId(startAreaId);
                        condition.setEndAreaId(endAreaId);
                        condition.setCarrierId(carrier.getCarrierId());
                        condition.setPricingMethod(ltl.getPricingMethod());
                        condition.setGoodsKind("1"); // 视为普货（下方代码运输方式默认为1(公路零担)）
                        if (ltl.getPricingMethod() == 0) {
                            condition.setWeightCount(BigDecimal.valueOf(ltl.getSegment().getWeightCount()));
                        } else if (ltl.getPricingMethod() == 1) {
                            condition.setVolumeCount(BigDecimal.valueOf(ltl.getSegment().getVolumeCount()));
                        } else if (ltl.getPricingMethod() == 2) {
                            condition.setNumCount(BigDecimal.valueOf(ltl.getSegment().getNumCount()));
                        }
                        ISegmentService.PPResult pv = segmentService.getCarrierProtocolPrice_v1(condition);
                        minPrice = pv.getMinPrice();
                        BigDecimal guidingPrice = pv.getGuidingPrice();
                        if (ltl.getPrice() == null && guidingPrice != null && guidingPrice.compareTo(BigDecimal.ZERO) > 0) {
                            ltl.setPrice(guidingPrice);
                        }
                        if (ltl.getPrice() == null) {
                            error.add("运段“" + ltl.getVbillno() + "”请录入单价");
                        } else if (guidingPrice.compareTo(BigDecimal.ZERO) != 0 && ltl.getPrice().compareTo(guidingPrice) != 0) {// 有协议价时判断单价是否与协议价一直
                            error.add("运段“" + ltl.getVbillno() + "”单价" + ltl.getPrice() + "与协议价" + guidingPrice + "不一致");
                        } else {
                            if (ltl.getPricingMethod() == 0) {
                                fee = ltl.getPrice().multiply(BigDecimal.valueOf(ltl.getSegment().getWeightCount()));
                                if (minPrice != null && minPrice.doubleValue() > 0) {
                                    if (fee.compareTo(minPrice) < 0) {
                                        fee = minPrice;
                                        activeMinPrice = true;
                                    }
                                }
                            } else if (ltl.getPricingMethod() == 1) {
                                fee = ltl.getPrice().multiply(BigDecimal.valueOf(ltl.getSegment().getVolumeCount()));
                                if (fee.compareTo(minPrice) < 0) {
                                    fee = minPrice;
                                    activeMinPrice = true;
                                }
                            } else if (ltl.getPricingMethod() == 2) {
                                fee = ltl.getPrice().multiply(BigDecimal.valueOf(ltl.getSegment().getNumCount()));
                                if (fee.compareTo(minPrice) < 0) {
                                    fee = minPrice;
                                    activeMinPrice = true;
                                }
                            }
                        }

                    }
                } else if (ltl.getPricingMethod() == 3 || ltl.getPricingMethod() == 4 || ltl.getPricingMethod() == 5 || ltl.getPricingMethod() == 6) {
                    fee = ltl.getPrice();
                }
                if (fee != null) { // 单价与协议价一致时，fee有值
                    ltl.setCostAmount(fee);
                    if (ltl.getDeliveryFee() != null) {
                        fee = fee.add(ltl.getDeliveryFee());
                    }
                    // 校验总价（填了总价的校验总价，未填总价的系统计算）
                    if (ltl.getTotalFee() != null) {
                        if (fee.compareTo(ltl.getTotalFee()) != 0) {
                            if (activeMinPrice) {
                                error.add("总价" + ltl.getTotalFee() + "与后台计算总价" + fee + "(保底价" + minPrice + (ltl.getDeliveryFee() == null ? "" : ("+送货费" + ltl.getDeliveryFee())) + ")不一致");
                            } else {
                                error.add("总价" + ltl.getTotalFee() + "与后台计算总价" + fee + "(单价*货量" + (ltl.getDeliveryFee() == null ? "" : ("+送货费" + ltl.getDeliveryFee())) + ")不一致");
                            }
                        }
                    } else {
                        ltl.setTotalFee(fee);
                    }
                }
            }

            // 送货段参数校验
            if (ltl.getArriPrice() != null) { // 有短驳价格视为有短驳数据
                // 承运商校验
                List<Carrier> arri_carrierList = carrierMapper.selectCarrierByAbsoluteCarrName(ltl.getArriCarrier(), null);
                if (arri_carrierList.size() > 1) {
                    error.add("找到多条送货承运商“" + ltl.getArriCarrier() + "”的信息");
                } else if (arri_carrierList.size() == 0) {
                    error.add("未找到送货承运商“" + ltl.getArriCarrier() + "”的信息");
                } else {
                    Carrier carrier = arri_carrierList.get(0);
                    ltl.setArriCarrierObj(carrier);
                    List<Car> carList = carMapper.listCarByCarrierIdAndNo(carrier.getCarrierId(), ltl.getArriCar());
                    if (carList.size() > 1) {
                        error.add("送货承运商“" + ltl.getArriCarrier() + "”下找到多条车号为“" + ltl.getArriCar()+ "”的车辆信息");
                    } else if (carList.size() == 0) {
                        error.add("送货承运商“" + ltl.getArriCarrier() + "”下未找到车号为“" + ltl.getArriCar()+ "”的车辆信息");
                    } else {
                        ltl.setArriCarObj(carList.get(0));
                    }
                    List<Driver> driverList = driverMapper.listDriverByCarrierIdAndName(carrier.getCarrierId(), ltl.getArriDriver());
                    if (driverList.size() > 1) {
                        error.add("送货承运商“" + ltl.getArriCarrier() + "”下找到多条名为“" + ltl.getArriDriver()+ "”的司机信息");
                    } else if (driverList.size() == 0) {
                        error.add("送货承运商“" + ltl.getArriCarrier() + "”下未找到名为“" + ltl.getArriDriver()+ "”的司机信息");
                    } else {
                        ltl.setArriDriverObj(driverList.get(0));
                    }
                }
            }

            if (error.size() > 0) {
                errors.add("数据" + (i + 1) + "：" + StringUtils.join(error, "、"));
            }
        }

        if (errors.size() > 0) {
            return AjaxResult.error(StringUtils.join(errors, "<br>"));
        }


        String scrId = "零担调度";
        // 2、有短驳段的要拆段
        SubsectionAddressVO deliStation = null; // 提货中转点(系统配置参数ltl_def_station)

        List<String> deliSubsectionSegmentIds = new ArrayList<>(); // 需要拆段的原运段id
        //Date middleDate = null; // 短驳拆段，要求到货日期同要求提货日期
        for (int i = 0; i < list.size(); i++) {
            LtlDispatch ltl = list.get(i);
            if (ltl.getShortPrice() != null) {
                Date minReqDeliDate = null; // 取合并调度的最晚要求提货日期
                //ltl.setSubsection(true);
                deliSubsectionSegmentIds.add(ltl.getSegment().getSegmentId());
                if (minReqDeliDate == null || minReqDeliDate.after(ltl.getSegment().getReqDeliDate())) {
                    minReqDeliDate = ltl.getSegment().getReqDeliDate();
                }
                // 向下找同flag(合并单元格)的，并找最早的要求提货日期
                int jump = 0;
                for (int j = i + 1; j < list.size(); j++) {
                    if (ltl.getFlag().equals(list.get(j).getFlag())) {
                        deliSubsectionSegmentIds.add(list.get(j).getSegment().getSegmentId());
                        if (minReqDeliDate.after(list.get(j).getSegment().getReqDeliDate())) {
                            minReqDeliDate = list.get(j).getSegment().getReqDeliDate();
                        }
                        //list.get(j).setSubsection(true);
                        jump++;
                    } else {
                        break;
                    }
                }

                if (deliStation == null) {
                    // 初始化提货中转点(只会执行一次)
                    deliStation = this.getDeliSubsectionAddress();
                }

                // 开始拆段
                for (int j = i; j <= i + jump; j++) {
                    List<SubsectionAddressVO> addressVOList = new ArrayList<>();
                    addressVOList.add(deliStation);
                    deliStation.setReqArriDate(list.get(j).getSegment().getReqDeliDate());
                    SubsectionAddressVO arriStation = getArriSubsectionAddress(list.get(j));
                    if (arriStation != null) {
                        addressVOList.add(arriStation);
                    }
                    // 一单一单的拆段
                    AjaxResult ajaxResult = segmentService.subsection(new String[]{list.get(j).getSegment().getSegmentId()}, addressVOList, scrId, 0);
                    if (ajaxResult.getCode() != 0) {
                        throw new BusinessException(ajaxResult.getMsg());
                    }
                    List<Map<String, Object>> childSegmentIds = segmentMapper.listChildSegmentIds(Arrays.asList(list.get(j).getSegment().getSegmentId()));
                    for (int k = 0; k < childSegmentIds.size(); k++) {
                        Map<String, Object> child = childSegmentIds.get(k);
                        String vbillno = (String) child.get("vbillno");
                        if (vbillno.endsWith("-1")) { // 拆段-1结尾、拆量-001结尾
                            // 第一段：提货段
                            list.get(j).set提货段segmentId((String) child.get("segmentId"));
                        } else if (vbillno.endsWith("-2")) {
                            // 第二段：专线段
                            list.get(j).set专线段segmentId((String) child.get("segmentId"));
                        } else if (vbillno.endsWith("-3")) {
                            // 第三段：送货段
                            list.get(j).set送货段segmentId((String) child.get("segmentId"));
                        }
                    }
                }
                i += jump;

            } else {
                // 没有短驳段的
                SubsectionAddressVO arriStation = getArriSubsectionAddress(ltl);
                if (arriStation != null) {
                    // 有送货段的
                    List<SubsectionAddressVO> addressVOList = new ArrayList<>();
                    addressVOList.add(arriStation);
                    // 一单一单的拆段
                    AjaxResult ajaxResult = segmentService.subsection(new String[]{ltl.getSegment().getSegmentId()}, addressVOList, scrId, 0);
                    if (ajaxResult.getCode() != 0) {
                        throw new BusinessException(ajaxResult.getMsg());
                    }
                    List<Map<String, Object>> childSegmentIds = segmentMapper.listChildSegmentIds(Arrays.asList(ltl.getSegment().getSegmentId()));
                    for (int j = 0; j < childSegmentIds.size(); j++) {
                        Map<String, Object> child = childSegmentIds.get(j);
                        String vbillno = (String) child.get("vbillno");
                        if (vbillno.endsWith("-1")) { // 拆段-1结尾、拆量-001结尾
                            // 第一段：专线段
                            ltl.set专线段segmentId((String) child.get("segmentId"));
                        } else if (vbillno.endsWith("-2")) {
                            // 第二段：送货段
                            ltl.set送货段segmentId((String) child.get("segmentId"));
                        }
                    }
                } else {
                    // 没有送货段的不需要拆段
                    ltl.set专线段segmentId(ltl.getSegment().getSegmentId());
                }
            }
        }

        //String userId = shiroUtils.getUserId().toString();
        //String userName = shiroUtils.getSysUser().getUserName();
        String net_profits_oil_tax = sysConfigService.selectConfigByKey("net_profits_oil_tax");
        Map<String, BigDecimal> billingTypeMap = sysDictDataService.selectDictDataByType("billing_type").stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getNumVal1));

        // 短驳调度（同flag合并调度）
        for (int i = 0; i < list.size(); i++) {
            LtlDispatch ltl = list.get(i);
            if (ltl.get提货段segmentId() != null) { // 有短驳段
                Date minReqDeliDate = ltl.getSegment().getReqDeliDate(); // 合并调度中最早要求提货日期
                List<String> 批量调度提货段segmentIds = new ArrayList<>();
                批量调度提货段segmentIds.add(ltl.get提货段segmentId());
                List<String> invoiceIdList = new ArrayList<>();
                invoiceIdList.add(ltl.getSegment().getInvoiceId());
                //Segment shortSegment = segmentMapper.selectSegmentById(ltl.getShortSegmentId());
                //ltl.setShortSegment(shortSegment);
                // 同flag合并调度
                //Date minReqDeliDate = shortSegment.getReqDeliDate(), minReqArriDate = shortSegment.getReqArriDate(); // 拆段后短驳的要求到货日期，专线的提货日期怎么定
                BigDecimal sumNnumCount = BigDecimal.valueOf(ltl.getSegment().getNumCount());
                BigDecimal sumWeightCount = BigDecimal.valueOf(ltl.getSegment().getWeightCount());
                BigDecimal sumVolumeCount = BigDecimal.valueOf(ltl.getSegment().getVolumeCount());

                int jump = 0;
                for (int j = i + 1; j < list.size(); j++) {
                    if (ltl.getFlag().equals(list.get(j).getFlag())) {
                        if (list.get(j).getSegment().getReqDeliDate().before(minReqDeliDate)) {
                            minReqDeliDate = list.get(j).getSegment().getReqDeliDate();
                        }
                        批量调度提货段segmentIds.add(list.get(j).get提货段segmentId());
                        invoiceIdList.add(list.get(j).getSegment().getInvoiceId());
                        jump++;
                        sumNnumCount = sumNnumCount.add(BigDecimal.valueOf(list.get(j).getSegment().getNumCount()));
                        sumWeightCount = sumWeightCount.add(BigDecimal.valueOf(list.get(j).getSegment().getWeightCount()));
                        sumVolumeCount = sumVolumeCount.add(BigDecimal.valueOf(list.get(j).getSegment().getVolumeCount()));
                    } else {
                        break;
                    }
                }
                i += jump;
                DispatchVO dvo = new DispatchVO();

                // 承运商相关
                Carrier carrier = ltl.getShortCarrierObj();
                dvo.setBalaType1(carrier.getBalaType());
                dvo.setCarrierPhone(carrier.getPhone());
                //dvo.setIfHasBill(carrier.getIfHasBill()); 没这字段？
                dvo.setCarrName(carrier.getCarrName());
                dvo.setCarrCode(carrier.getCarrCode());
                dvo.setCarrierId(carrier.getCarrierId());
                dvo.setCarrType(StringUtils.isNotBlank(carrier.getCarrType()) ? Integer.parseInt(carrier.getCarrType()) : null);
                dvo.setBillingType(carrier.getBillingType() == null ? 6 : carrier.getBillingType()); // 空时默认不开票
                dvo.setOilRatio(carrier.getOilCardRate() == null ? null : carrier.getOilCardRate().doubleValue());

                // 车辆相关
                Car car = ltl.getShortCarObj();
                dvo.setCarnoId(car.getCarId());
                dvo.setCarno(car.getCarno());
                dvo.setCarLenId(car.getCarLengthId());
                dvo.setCarLenName(car.getCarLenName());
                dvo.setCarTypeCode(car.getVehicleclassificationcode());
                dvo.setCarTypeName(car.getCarTypeName());

                // 司机相关
                Driver driver = ltl.getShortDriverObj();
                dvo.setDriverId(driver.getDriverId());
                dvo.setDriverName(driver.getDriverName());
                dvo.setDriverMobile(driver.getPhone());
                dvo.setCardId(driver.getCardId());

                // 单据相关
                dvo.setSegmentIds(StringUtils.join(批量调度提货段segmentIds, ","));
                //dvo.setIsNeedReference(0);
                dvo.setTransCode("1");
                dvo.setTransName("公路零担");
                dvo.setLtlType(0); // 提货段
                dvo.setAllocationType(3);
                dvo.setIsOversize(0); // 大件
                dvo.setIsBigCartLoad(0); // 大车配载
                dvo.setIsCustomsClearance(0); // 是否报关
                dvo.setReqDeliDate(minReqDeliDate); // 合并调度取最早要求提货日期
                dvo.setReqArriDate(minReqDeliDate); // 合并调度取最早要求提到货日期
                //dvo.setMemo(); // 短驳合并调度时不好处理
                //dvo.setAppMemo(); // 短驳合并调度时不好处理
                dvo.setNumCount(sumNnumCount.doubleValue());
                dvo.setWeightCount(sumWeightCount.doubleValue());
                dvo.setVolumeCount(sumVolumeCount.doubleValue());
                dvo.setPricingMethod(3); // 按票
                dvo.setCostAmount(ltl.getShortPrice()); // 结算金额
                dvo.setUnitPrice(ltl.getShortPrice()); // 单价
                if (dvo.getOilRatio() != null) { // 计算油卡金额
                    dvo.setOilAmount(ltl.getShortPrice().multiply(BigDecimal.valueOf(dvo.getOilRatio()).divide(BigDecimal.valueOf(100))).setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                dvo.setCostAmountFreight(ltl.getShortPrice());
                BigDecimal costAmountBilling = null;// 不含税计算，油卡金额/oilTax + 现金/税点
                BigDecimal oilAmount = dvo.getOilAmount();
                if (oilAmount == null) {
                    oilAmount = BigDecimal.ZERO;
                }
                BigDecimal cashAmount = ltl.getShortPrice().subtract(oilAmount);
                costAmountBilling = oilAmount.divide(new BigDecimal(net_profits_oil_tax), 2, BigDecimal.ROUND_HALF_UP)
                        .add(cashAmount.divide(billingTypeMap.get(dvo.getBillingType().toString()), 2, BigDecimal.ROUND_HALF_UP));
                dvo.setCostAmountBilling(costAmountBilling);
                List<PayDetail> payDetailList = new ArrayList<>();
                dvo.setPayDetailList(payDetailList);
                if (cashAmount.compareTo(BigDecimal.ZERO) != 0) {
                    // 生成回付现金的一条应付明细(编号对应调度页面顺序)
                    PayDetail pd4 = new PayDetail();
                    pd4.setTransFeeCount(cashAmount);
                    pd4.setCostTypeFreight("4");
                    Map<String, String> shareCost = new LinkedHashMap<>(); // 成本分摊：按票（每个发货单）
                    BigDecimal perAmount = pd4.getTransFeeCount().divide(BigDecimal.valueOf(invoiceIdList.size()), 2, BigDecimal.ROUND_HALF_UP);
                    BigDecimal lastAmount = pd4.getTransFeeCount();
                    for (int j = 0; j < invoiceIdList.size(); j++) {
                        if (j == invoiceIdList.size() - 1) {
                            shareCost.put(invoiceIdList.get(j), lastAmount.toString());
                        } else {
                            shareCost.put(invoiceIdList.get(j), perAmount.toString());
                            lastAmount = lastAmount.subtract(perAmount);
                        }
                    }
                    try {
                        pd4.setShareCost(objectMapper.writeValueAsString(shareCost));
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    payDetailList.add(pd4);
                }
                if (oilAmount.compareTo(BigDecimal.ZERO) != 0) {
                    // 生成回付油卡的一条应付明细
                    PayDetail pd5 = new PayDetail();
                    pd5.setTransFeeCount(oilAmount);
                    pd5.setCostTypeFreight("5");
                    Map<String, String> shareCost = new LinkedHashMap<>(); // 成本分摊：按票（每个发货单）
                    BigDecimal perAmount = pd5.getTransFeeCount().divide(BigDecimal.valueOf(invoiceIdList.size()), 2, BigDecimal.ROUND_HALF_UP);
                    BigDecimal lastAmount = pd5.getTransFeeCount();
                    for (int j = 0; j < invoiceIdList.size(); j++) {
                        if (j == invoiceIdList.size() - 1) {
                            shareCost.put(invoiceIdList.get(j), lastAmount.toString());
                        } else {
                            shareCost.put(invoiceIdList.get(j), perAmount.toString());
                            lastAmount = lastAmount.subtract(perAmount);
                        }
                    }
                    try {
                        pd5.setShareCost(objectMapper.writeValueAsString(shareCost));
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    payDetailList.add(pd5);
                }

                log.debug("开始执行调度提货段");
                AjaxResult ajaxResult = segmentService.dispatch(dvo, scrId, 0);
                if (ajaxResult.getCode() != 0) {
                    throw new BusinessException(ltl.getInvoiceVbillno() + "短驳:" + ajaxResult.getMsg());
                }
            }

        }

        // 处理专线段
        for (int j = 0; j < list.size(); j++) {
            LtlDispatch ltl = list.get(j);
            DispatchVO dvo = new DispatchVO();

            // 承运商相关
            Carrier carrier = ltl.getCarrierObj();
            dvo.setBalaType1(carrier.getBalaType());
            dvo.setCarrierPhone(carrier.getPhone());
            //dvo.setIfHasBill(carrier.getIfHasBill()); 没这字段？
            dvo.setCarrName(carrier.getCarrName());
            dvo.setCarrCode(carrier.getCarrCode());
            dvo.setCarrierId(carrier.getCarrierId());
            dvo.setCarrType(StringUtils.isNotBlank(carrier.getCarrType()) ? Integer.parseInt(carrier.getCarrType()) : null);
            dvo.setBillingType(carrier.getBillingType() == null ? 6 : carrier.getBillingType()); // 空时默认不开票
            dvo.setOilRatio(carrier.getOilCardRate() == null ? null : carrier.getOilCardRate().doubleValue());

            // 单据相关
            dvo.setSegmentIds(ltl.get专线段segmentId());
            dvo.setTransCode("1");
            dvo.setTransName("公路零担");
            dvo.setLtlType(1); // 干线段
            dvo.setAllocationType(3);
            dvo.setIsOversize(0); // 大件
            dvo.setIsBigCartLoad(0); // 大车配载
            dvo.setIsCustomsClearance(0); // 是否报关
            dvo.setReqDeliDate(ltl.getSegment().getReqDeliDate());
            dvo.setReqArriDate(ltl.getSegment().getReqArriDate());
            dvo.setMemo(ltl.getMemo()); // 内部备注
            dvo.setAppMemo(ltl.getRemark()); // 司机注意事项
            dvo.setNumCount(ltl.getSegment().getNumCount());
            dvo.setWeightCount(ltl.getSegment().getWeightCount());
            dvo.setVolumeCount(ltl.getSegment().getVolumeCount());
            dvo.setPricingMethod(ltl.getPricingMethod()); // excel导入计价方式
            dvo.setCostAmount(ltl.getCostAmount()); // 专线段结算金额
            dvo.setUnitPrice(ltl.getPrice()); // excel导入专线单价
            if (dvo.getOilRatio() != null) { // 计算油卡金额
                dvo.setOilAmount(ltl.getCostAmount().multiply(BigDecimal.valueOf(dvo.getOilRatio()).divide(BigDecimal.valueOf(100))).setScale(2, BigDecimal.ROUND_HALF_UP));
            }
            dvo.setCostAmountFreight(ltl.getCostAmount());
            BigDecimal costAmountBilling = null;// 不含税计算，油卡金额/oilTax + 现金/税点
            BigDecimal oilAmount = dvo.getOilAmount();
            if (oilAmount == null) {
                oilAmount = BigDecimal.ZERO;
            }
            BigDecimal cashAmount = ltl.getCostAmount().subtract(oilAmount);
            costAmountBilling = oilAmount.divide(new BigDecimal(net_profits_oil_tax), 2, BigDecimal.ROUND_HALF_UP)
                    .add(cashAmount.divide(billingTypeMap.get(dvo.getBillingType().toString()), 2, BigDecimal.ROUND_HALF_UP));
            dvo.setCostAmountBilling(costAmountBilling);
            List<PayDetail> payDetailList = new ArrayList<>();
            dvo.setPayDetailList(payDetailList);
            if (cashAmount.compareTo(BigDecimal.ZERO) != 0) {
                // 生成回付现金的一条应付明细(编号对应调度页面顺序)
                PayDetail pd4 = new PayDetail();
                pd4.setTransFeeCount(cashAmount);
                pd4.setCostTypeFreight("4");
                Map<String, String> shareCost = new LinkedHashMap<>(); // 成本分摊：按票（每个发货单）
                shareCost.put(ltl.getSegment().getInvoiceId(), pd4.getTransFeeCount().toString());
                try {
                    pd4.setShareCost(objectMapper.writeValueAsString(shareCost));
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                payDetailList.add(pd4);
            }
            if (oilAmount.compareTo(BigDecimal.ZERO) != 0) {
                // 生成回付油卡的一条应付明细
                PayDetail pd5 = new PayDetail();
                pd5.setTransFeeCount(oilAmount);
                pd5.setCostTypeFreight("5");
                Map<String, String> shareCost = new LinkedHashMap<>(); // 成本分摊：按票（每个发货单）
                shareCost.put(ltl.getSegment().getInvoiceId(), pd5.getTransFeeCount().toString());
                try {
                    pd5.setShareCost(objectMapper.writeValueAsString(shareCost));
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                payDetailList.add(pd5);
            }

            List<EntrustCost> entrustCostList = new ArrayList<>();
            dvo.setEntrustCostList(entrustCostList);
            if (ltl.getDeliveryFee() != null && ltl.getDeliveryFee().compareTo(BigDecimal.ZERO) != 0) {
                // 送货费
                EntrustCost cost = new EntrustCost();
                Map<String, String> shareCost = new LinkedHashMap<>(); // 成本分摊：按票（每个发货单）
                shareCost.put(ltl.getSegment().getInvoiceId(), ltl.getDeliveryFee().toString());
                try {
                    cost.setShareCost(objectMapper.writeValueAsString(shareCost));
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                cost.setCostType("22"); // 送货费
                cost.setCost(ltl.getDeliveryFee());
                entrustCostList.add(cost);
            }
            //try {
            //    System.out.println(new ObjectMapper().writeValueAsString(dvo));
            //} catch (IOException e) {
            //    e.printStackTrace();
            //}
            log.debug("开始执行调度专线段");
            AjaxResult ajaxResult = segmentService.dispatch(dvo, scrId, 0);
            if (ajaxResult.getCode() != 0) {
                throw new BusinessException(ltl.getInvoiceVbillno() + "专线:" + ajaxResult.getMsg());
            }
        }

        // 处理送货段
        for (int i = 0; i < list.size(); i++) {
            LtlDispatch ltl = list.get(i);
            if (ltl.get送货段segmentId() != null) {
                if (ltl.getArriCarrierObj() == null) {
                    // 没有承运商时不进行调度
                    continue;
                }
                DispatchVO dvo = new DispatchVO();

                // 承运商相关
                Carrier carrier = ltl.getArriCarrierObj();
                dvo.setBalaType1(carrier.getBalaType());
                dvo.setCarrierPhone(carrier.getPhone());
                //dvo.setIfHasBill(carrier.getIfHasBill()); 没这字段？
                dvo.setCarrName(carrier.getCarrName());
                dvo.setCarrCode(carrier.getCarrCode());
                dvo.setCarrierId(carrier.getCarrierId());
                dvo.setCarrType(StringUtils.isNotBlank(carrier.getCarrType()) ? Integer.parseInt(carrier.getCarrType()) : null);
                dvo.setBillingType(carrier.getBillingType() == null ? 6 : carrier.getBillingType()); // 空时默认不开票
                dvo.setOilRatio(carrier.getOilCardRate() == null ? null : carrier.getOilCardRate().doubleValue());

                // 车辆相关
                Car car = ltl.getArriCarObj();
                dvo.setCarnoId(car.getCarId());
                dvo.setCarno(car.getCarno());
                dvo.setCarLenId(car.getCarLengthId());
                dvo.setCarLenName(car.getCarLenName());
                dvo.setCarTypeCode(car.getVehicleclassificationcode());
                dvo.setCarTypeName(car.getCarTypeName());

                // 司机相关
                Driver driver = ltl.getArriDriverObj();
                dvo.setDriverId(driver.getDriverId());
                dvo.setDriverName(driver.getDriverName());
                dvo.setDriverMobile(driver.getPhone());
                dvo.setCardId(driver.getCardId());

                // 单据相关
                dvo.setSegmentIds(ltl.get送货段segmentId());
                dvo.setTransCode("1");
                dvo.setTransName("公路零担");
                dvo.setLtlType(2); //送货段
                dvo.setAllocationType(3);
                dvo.setIsOversize(0); // 大件
                dvo.setIsBigCartLoad(0); // 大车配载
                dvo.setIsCustomsClearance(0); // 是否报关
                dvo.setReqDeliDate(ltl.getSegment().getReqArriDate());
                dvo.setReqArriDate(ltl.getSegment().getReqArriDate());
                dvo.setMemo(ltl.getMemo()); // 内部备注
                dvo.setAppMemo(ltl.getRemark()); // 司机注意事项
                dvo.setNumCount(ltl.getSegment().getNumCount());
                dvo.setWeightCount(ltl.getSegment().getWeightCount());
                dvo.setVolumeCount(ltl.getSegment().getVolumeCount());
                dvo.setPricingMethod(3); // 按票
                dvo.setCostAmount(ltl.getArriPrice()); // 结算金额
                dvo.setUnitPrice(ltl.getArriPrice()); // 按票单价即总价
                if (dvo.getOilRatio() != null) { // 计算油卡金额
                    dvo.setOilAmount(dvo.getCostAmount().multiply(BigDecimal.valueOf(dvo.getOilRatio()).divide(BigDecimal.valueOf(100))).setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                dvo.setCostAmountFreight(ltl.getArriPrice());
                BigDecimal costAmountBilling = null;// 不含税计算，油卡金额/oilTax + 现金/税点
                BigDecimal oilAmount = dvo.getOilAmount();
                if (oilAmount == null) {
                    oilAmount = BigDecimal.ZERO;
                }
                BigDecimal cashAmount = dvo.getCostAmount().subtract(oilAmount);
                costAmountBilling = oilAmount.divide(new BigDecimal(net_profits_oil_tax), 2, BigDecimal.ROUND_HALF_UP)
                        .add(cashAmount.divide(billingTypeMap.get(dvo.getBillingType().toString()), 2, BigDecimal.ROUND_HALF_UP));
                dvo.setCostAmountBilling(costAmountBilling);
                List<PayDetail> payDetailList = new ArrayList<>();
                dvo.setPayDetailList(payDetailList);
                if (cashAmount.compareTo(BigDecimal.ZERO) != 0) {
                    // 生成回付现金的一条应付明细(编号对应调度页面顺序)
                    PayDetail pd4 = new PayDetail();
                    pd4.setTransFeeCount(cashAmount);
                    pd4.setCostTypeFreight("4");
                    Map<String, String> shareCost = new LinkedHashMap<>(); // 成本分摊：按票（每个发货单）
                    shareCost.put(ltl.getSegment().getInvoiceId(), pd4.getTransFeeCount().toString());
                    try {
                        pd4.setShareCost(objectMapper.writeValueAsString(shareCost));
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    payDetailList.add(pd4);
                }
                if (oilAmount.compareTo(BigDecimal.ZERO) != 0) {
                    // 生成回付油卡的一条应付明细
                    PayDetail pd5 = new PayDetail();
                    pd5.setTransFeeCount(oilAmount);
                    pd5.setCostTypeFreight("5");
                    Map<String, String> shareCost = new LinkedHashMap<>(); // 成本分摊：按票（每个发货单）
                    shareCost.put(ltl.getSegment().getInvoiceId(), pd5.getTransFeeCount().toString());
                    try {
                        pd5.setShareCost(objectMapper.writeValueAsString(shareCost));
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    payDetailList.add(pd5);
                }

                log.debug("开始执行调度送货段");
                AjaxResult ajaxResult = segmentService.dispatch(dvo, scrId, 0);
                if (ajaxResult.getCode() != 0) {
                    throw new BusinessException(ltl.getInvoiceVbillno() + "送货段:" + ajaxResult.getMsg());
                }
            }
        }
//int x = 1 / 0;
        return AjaxResult.success();
    }

    /**
     * 取提货段中转站
     *
     * @return
     */
    private SubsectionAddressVO getDeliSubsectionAddress() {
        String stationName = sysConfigService.selectConfigByKey("ltl_def_station"); // 零担默认提货中转点
        Address condition = new Address();
        condition.setAddrName(stationName);
        condition.setAddrType("3");
        condition.setCheckStatus(1);
        condition.setDelFlag(0);
        List<Address> addressList = addressMapper.getAddressList(condition);
        if (addressList.size() > 1) {
            throw new BusinessException("找到多条名为“" + stationName + "”的中转点");
        } else if (addressList.size() == 0) {
            throw new BusinessException("未找到名为“" + stationName + "”的中转点");
        }
        SubsectionAddressVO deliStation = new SubsectionAddressVO();
        BeanUtils.copyBeanProp(deliStation, addressList.get(0));
        CustAddress query = new CustAddress();
        query.setDelFlag(0);
        query.setAddressId(deliStation.getAddressId());
        List<CustAddress> custAddressList = custAddressMapper.selectCustAddressList(query);
        if (custAddressList.size() > 0) {
            deliStation.setContact(custAddressList.get(0).getContactName());
            deliStation.setMobile(custAddressList.get(0).getContactMobilephone());
        }
        return deliStation;
    }

    /**
     * 取到货段中转站
     *
     * @param ltlDispatch
     * @return
     */
    private SubsectionAddressVO getArriSubsectionAddress(LtlDispatch ltlDispatch) {
        if (ltlDispatch.getArriStation() != null && ltlDispatch.getArriStation().trim().length() > 0) {
            String stationName = ltlDispatch.getArriStation(); // 送货中转点
            Address condition = new Address();
            condition.setAddrName(stationName);
            condition.setAddrType("3");
            condition.setCheckStatus(1);
            condition.setDelFlag(0);
            List<Address> addressList = addressMapper.getAddressList(condition);
            if (addressList.size() > 1) {
                throw new BusinessException("找到多条名为“" + stationName + "”的中转点");
            } else if (addressList.size() == 0) {
                throw new BusinessException("未找到名为“" + stationName + "”的中转点");
            }
            SubsectionAddressVO station = new SubsectionAddressVO();
            station.setReqArriDate(ltlDispatch.getSegment().getReqArriDate());
            BeanUtils.copyBeanProp(station, addressList.get(0));
            CustAddress query = new CustAddress();
            query.setDelFlag(0);
            query.setAddressId(station.getAddressId());
            List<CustAddress> custAddressList = custAddressMapper.selectCustAddressList(query);
            if (custAddressList.size() > 0) {
                station.setContact(custAddressList.get(0).getContactName());
                station.setMobile(custAddressList.get(0).getContactMobilephone());
            }
            return station;
        }
        return null;
    }

    @Override
    public boolean ddSpPreCheck(String carrierId, List<PayDetail> payDetailList, String userId, String transLineName) {
        if (carrierId == null || carrierId.trim().length() == 0) {
            log.debug("没有承运商");
            return false;
        }
        Carrier carrier = carrierMapper.selectCarrierById(carrierId);
        if (carrier == null) {
            throw new RuntimeException("取承运商数据为null");
        }
        /* 20250819170300不分单笔月结都需要运费审核
        if (!"2".equals(carrier.getCarrType())) {
            log.debug("非外协车队，不处理");
            return false;
        }*/
        /* 20250819170300不分单笔月结都需要运费审核
        if (Objects.equals(carrier.getBalaType(), 2)) {
            log.debug("月结承运商，不处理");
            return false;
        }*/
        boolean needCheck = false;
        for (int i = 0; i < payDetailList.size(); i++) {
            if (payDetailList.get(i).getTransFeeCount() != null && payDetailList.get(i).getTransFeeCount().compareTo(BigDecimal.ZERO) != 0) {
                if (!"1".equals(payDetailList.get(i).getCostTypeFreight())) { // 非预付油卡
                    needCheck = true;
                    break;
                }
            }
        }
        if (needCheck) {
            String biz = "dd";
            String templateId = wecomService.getTemplateIdByBiz(biz);
            if (templateId == null) {
                log.debug("未找到调度审批模板id");
                return false;
            }
            // 判断用户是否有微信账户
            if (userId == null || userId.trim().length() == 0) {
                throw new RuntimeException("未取到调度人");
            }
            final SysUser sysUser = sysUserMapper.selectUserById(Long.valueOf(userId));
            try {
                wecomService.checkAndSaveWecomUser(sysUser);
            } catch (RuntimeException e) {
                throw new RuntimeException("取调度人" + sysUser.getLoginName() + "企微id失败：" + e.getMessage());
            }
            // 判断是否调度组配置正确
            int n = wecomSpMapper.countDictByCode(biz, templateId, "ddz", transLineName);
            if (n == 0) {
                throw new RuntimeException("未配置调度组“" + transLineName + "”对应企微字典项");
            }
        }
        return needCheck;
    }

    @Transactional
    @Override
    public void submitWecomSpAsDd(String lotId) {
        String lotNo = null;
        try {
            String biz = "dd";
            String templateId = wecomService.getTemplateIdByBiz(biz);
            if (templateId == null) {
                log.debug("未找到调度审批模板id");
                return;
            }
            EntrustLot lot = entrustLotMapper.selectEntrustLotById(lotId);
            lotNo = lot.getLot();
            if (lot.getCarrierId() == null) {
                log.debug(lot.getLot() + "没有承运商");
                return;
            }
            // 非外协不处理
            Carrier carrier = carrierMapper.selectCarrierById(lot.getCarrierId());
            if (!"2".equals(carrier.getCarrType())) {
                log.debug("非外协车队，不处理");
                return;
            }
            if (Objects.equals(carrier.getBalaType(), 2)) {
                log.debug("月结承运商，不处理");
                return;
            }
            // 费用仅有预付油卡时也不处理
            boolean needCheck = false; // 是否需要审批
            BigDecimal totalFee = BigDecimal.ZERO; // 总运费
            BigDecimal billingFee = BigDecimal.ZERO; // 开票金额
            BigDecimal unBillingFee = BigDecimal.ZERO; // 不开票金额
            BigDecimal oilFee = BigDecimal.ZERO; // 油卡金额
            BigDecimal yf = BigDecimal.ZERO; // 预付
            BigDecimal df = BigDecimal.ZERO; // 到付
            BigDecimal hf = BigDecimal.ZERO; // 回付
            Integer billingType = lot.getBillingType() == null ? 6 : lot.getBillingType();
            List<PayDetail> payDetailList = payDetailMapper.selectPayDetailListByLotIds(Arrays.asList(lotId));
            Map<String, BigDecimal> 在途费 = new LinkedHashMap<>();
            for (int i = 0; i < payDetailList.size(); i++) {
                PayDetail p = payDetailList.get(i);
                totalFee = totalFee.add(p.getTransFeeCount());
                if ("0".equals(p.getFreeType())) { // 运费
                    if ("0".equals(p.getCostTypeFreight()) || "1".equals(p.getCostTypeFreight()) || p.getCostTypeFreight() == null) {
                        yf = yf.add(p.getTransFeeCount());
                    } else if ("2".equals(p.getCostTypeFreight()) || "3".equals(p.getCostTypeFreight())) {
                        df = df.add(p.getTransFeeCount());
                    } else if ("4".equals(p.getCostTypeFreight()) || "5".equals(p.getCostTypeFreight())) {
                        hf = hf.add(p.getTransFeeCount());
                    }
                } else { // 在途
                    BigDecimal d = 在途费.get(p.getCostTypeOnWay());
                    if (d == null) {
                        d = p.getTransFeeCount();
                    } else {
                        d = d.add(p.getTransFeeCount());
                    }
                    在途费.put(p.getCostTypeOnWay(), d);
                }
                if ("1".equals(p.getCostTypeFreight()) || "3".equals(p.getCostTypeFreight()) || "5".equals(p.getCostTypeFreight())) {
                    oilFee = oilFee.add(p.getTransFeeCount());
                } else {
                    // 刚调度生成运单时，此时含税只要考虑运单的含税
                    if (billingType.equals(6)) {
                        unBillingFee = unBillingFee.add(p.getTransFeeCount());
                    } else {
                        billingFee = billingFee.add(p.getTransFeeCount());
                    }
                }
                if (!"1".equals(payDetailList.get(i).getCostTypeFreight())) { // 非预付油卡
                    needCheck = true;
                    PayDetail target = new PayDetail();
                    target.setPayDetailId(payDetailList.get(i).getPayDetailId());
                    target.setLotSpLock(1); // 审核锁定字段
                    payDetailMapper.updatePayDetailById(target);
                }
            }

            if (needCheck) {
                entrustLotDepositMapper.lotLockByLotId(lotId); // 锁定金
            } else {
                log.debug("只有预付油卡，无需审核");
                return;
            }

            Map<String, Object> dynaParam = new LinkedHashMap<>();
            dynaParam.put("id", lotId);
            // 调度人
            SysUser user = userService.selectUserById(Long.parseLong(lot.getRegUserId()));
            //dynaParam.put("ddr", user.getUserName());
            // 总运费（开票/不开票/油卡比例）
            StringBuilder zyf = new StringBuilder();
            /*zyf.append(totalFee).append("(开票: ").append(billingFee).append("/不开票: ").append(unBillingFee).append("/油卡比例: ");
            if (totalFee.compareTo(BigDecimal.ZERO) == 0) {
                zyf.append("0.00");
            } else {
                zyf.append(oilFee.multiply(BigDecimal.valueOf(100)).divide(totalFee, 2, BigDecimal.ROUND_HALF_UP));
            }
            zyf.append("%)");*/
            zyf.append("现金:");
            if (unBillingFee.compareTo(BigDecimal.ZERO) > 0) {
                zyf.append(unBillingFee).append("(不开票)");
            } else if (billingFee.compareTo(BigDecimal.ZERO) > 0) {
                zyf.append(billingFee).append("(开票)");
            } else {
                zyf.append("0.00");
            }

            if ("G7".equals(lot.getPayWay())) {
                zyf.append("(G7)");
            } else {
                zyf.append("(非G7)");
            }

            if (oilFee.compareTo(BigDecimal.ZERO) > 0) {
                zyf.append(" 油卡:").append(oilFee);
            }

            dynaParam.put("zyf", zyf.toString());
            // 指导价
            if (Objects.equals(lot.getPricingMethod(), CarrierProtocolPricingMethodEnum.BY_FACE.getValue())
            || Objects.equals(lot.getPricingMethod(), CarrierProtocolPricingMethodEnum.BY_TO_FRO.getValue())
            ) { // 按票、往返
                StringBuilder zdj = new StringBuilder();
                zdj.append("指导价：").append(lot.getGuidingPrice() == null ? "无" : lot.getGuidingPrice().toString());
                if (lot.getLoadPlaceNum() != null && lot.getUnloadPlaceNum() != null && (lot.getLoadPlaceNum() > 1 || lot.getUnloadPlaceNum() > 1)) {
                    zdj.append("(").append(lot.getLoadPlaceNum()).append("装").append(lot.getUnloadPlaceNum()).append("卸)");
                }
                dynaParam.put("zdj", zdj.toString());
            } else {
                StringBuilder zdj = new StringBuilder();
                zdj.append("单价：").append(lot.getUnitPrice() == null ? "无" : lot.getUnitPrice());
                if (lot.getPricingMethod() != null) {
                    zdj.append(CarrierProtocolPricingMethodEnum.getContextByValue(lot.getPricingMethod()));
                }
                dynaParam.put("zdj", zdj.toString());
            }
            // 运费组成
            StringBuilder yfzc = new StringBuilder();
            List<String> tmp = new ArrayList<>();
            if (yf.compareTo(BigDecimal.ZERO) != 0) {
                tmp.add("预付:" + yf);
            }
            if (df.compareTo(BigDecimal.ZERO) != 0) {
                tmp.add("到付:" + df);
            }
            if (hf.compareTo(BigDecimal.ZERO) != 0) {
                tmp.add("回付:" + hf);
            }
            if (tmp.size() > 0) {
                yfzc.append(StringUtils.join(tmp, "/"));
            } else {
                yfzc.append("运费0");
            }
            if (在途费.size() > 0) {
                yfzc.append("\n");
                for (String costTypeOnWay: 在途费.keySet()) {
                    yfzc.append(costTypeOnWay == null ? "未知" : sysDictDataService.selectDictLabel("cost_type_on_way", costTypeOnWay)).append("：").append(在途费.get(costTypeOnWay)).append("；");
                }
            }
            dynaParam.put("yfzc", yfzc.toString());
            // 运单信息
            StringBuilder ydxx = new StringBuilder();
            ydxx.append(lot.getLot()).append("\n");
            List<Entrust> entrustList = entrustMapper.selectEntrustByLotId(lotId);
            Set<String> custAbbrs = new LinkedHashSet<>();
            //Set<Long> salesDepts = new LinkedHashSet<>();
            List<String> customerIdList = new ArrayList<>();
            for (int i = 0; i < entrustList.size(); i++) {
                custAbbrs.add(entrustList.get(i).getCustAbbr());
                // 取m_customer内最新的sales_dept
                customerIdList.add(entrustList.get(i).getCustomerId());
            }
            //List<Client> clientList = clientMapper.selectClientListByIdList(customerIdList);
            ydxx.append(StringUtils.join(custAbbrs, "、")).append("\n");
            /*ydxx.append(lot.getDeliCityName().equals("市辖区") ? lot.getDeliProvinceName() : lot.getDeliCityName()).append(lot.getDeliAreaName())
                    .append(" ~ ")
                    .append(lot.getArriCityName().equals("市辖区") ? lot.getArriProvinceName() : lot.getArriCityName()).append(lot.getArriAreaName());*/
            final List<MultipleShippingAddress> multipleShippingAddresses = multipleShippingAddressMapper.selectListByLotId(lotId);
            Set<String> ti = new LinkedHashSet<>();
            Set<String> dao = new LinkedHashSet<>();
            for (int j = 0; j < multipleShippingAddresses.size(); j++) {
                final MultipleShippingAddress ma = multipleShippingAddresses.get(j);
                if (ma.getAddressType() == 0) {
                    ti.add(((ma.getCityName().equals("市辖区") || ma.getCityName().equals("县")) ? ma.getProvinceName() : ma.getCityName()) + ma.getAreaName());
                } else if (ma.getAddressType() == 1) {
                    dao.add(((ma.getCityName().equals("市辖区") || ma.getCityName().equals("县")) ? ma.getProvinceName() : ma.getCityName()) + ma.getAreaName());
                }
            }
            ydxx.append(StringUtils.join(ti, "、")).append(" ~ ").append(StringUtils.join(dao, "、"));
            Double d = lot.getActualmileage();
            if (d != null && lot.getActualmileage() != 0) {
                ydxx.append("(").append(BigDecimal.valueOf(lot.getActualmileage()).divide(BigDecimal.valueOf(1000), 2, BigDecimal.ROUND_HALF_UP)).append("km)");
            }

            // 往返标记
            if (Objects.equals(lot.getPricingMethod(), 7)) {
                ydxx.append(" 往返");
            }

            if (lot.getNumCount() != null && lot.getNumCount() > 0) {
                ydxx.append(" ").append(lot.getNumCount()).append("件");
            }
            if (lot.getWeightCount() != null && lot.getWeightCount() > 0) {
                ydxx.append(" ").append(lot.getWeightCount()).append("吨");
            }
            if (lot.getVolumeCount() != null && lot.getVolumeCount() > 0) {
                ydxx.append(" ").append(lot.getVolumeCount()).append("方");
            }
            ydxx.append("\n车型: ");
            if (lot.getCarLenName() != null) {
                ydxx.append(lot.getCarLenName()).append(lot.getCarTypeName());
            } else {
                ydxx.append("无");
            }
            if (lot.getReqCarLenName() != null || lot.getReqCarTypeName() != null) {
                ydxx.append("(要求：");
                if (lot.getReqCarLenName() != null) {
                    ydxx.append(lot.getReqCarLenName());
                }
                if (lot.getReqCarTypeName() != null) {
                    ydxx.append(lot.getReqCarTypeName());
                }
                ydxx.append(")");
            }

            if (Objects.equals(1, lot.getIsOversize())) {
                ydxx.append(" (大件");
                StringBuilder tmp2 = new StringBuilder();
                if (lot.getGoodsLength() != null) {
                    tmp2.append("长").append(lot.getGoodsLength()).append("米");
                }
                if (lot.getGoodsWidth() != null) {
                    if (tmp2.length() > 0) {
                        tmp2.append("/");
                    }
                    tmp2.append("宽").append(lot.getGoodsWidth()).append("米");
                }
                if (lot.getGoodsHeight() != null) {
                    if (tmp2.length() > 0) {
                        tmp2.append("/");
                    }
                    tmp2.append("高").append(lot.getGoodsHeight()).append("米");
                }
                if (tmp2.length() > 0) {
                    ydxx.append(": ").append(tmp2);
                }
                ydxx.append(")");
                dynaParam.put("dj", "1");
            } else {
                dynaParam.put("dj", "2");
            }
            if (Objects.equals(1, lot.getIsBigCartLoad())) {
                ydxx.append(" (配载)");
            }
            if ("0".equals(lot.getTransType()) || "4".equals(lot.getTransType()) || "15".equals(lot.getTransType())) {

            } else {
                ydxx.append(sysDictDataService.selectDictLabel("trans_code", lot.getTransType()));
            }
            /*ydxx.append("\n承运商: ");
            if (lot.getCarrierName() != null) {
                ydxx.append(lot.getCarrierName());
            } else {
                ydxx.append("无");
            }*/
            ydxx.append("\n车号: ");
            if (lot.getCarNo() != null) {
                ydxx.append(lot.getCarNo()).append(" ");
            } else {
                ydxx.append("无 ");
            }
            if (lot.getDriverName() != null) {
                ydxx.append(lot.getDriverName());
            }
            dynaParam.put("ydxx", ydxx.toString());

            // 预警信息
            StringBuilder yjxx = new StringBuilder();
            int n = 0;
            if (lot.getGuidingPrice() != null && lot.getGuidingPrice().doubleValue() > 0 && totalFee.compareTo(lot.getGuidingPrice()) > 0) {
                // 进一步判断是否超指导价：运单9%专票(4)或应付明细是油卡时，运费降低系数再比较
                BigDecimal finalFee = BigDecimal.ZERO;
                final BigDecimal guiding_price_comp_ratio = BigDecimal.ONE.subtract(new BigDecimal(sysConfigService.selectConfigByKey("guiding_price_comp_ratio")));
                final BigDecimal oil_guiding_price_comp_ratio = BigDecimal.ONE.subtract(new BigDecimal(sysConfigService.selectConfigByKey("oil_guiding_price_comp_ratio")));
                String lotBillintType = lot.getBillingType() == null ? "6" : lot.getBillingType().toString();
                for (int i = 0; i < payDetailList.size(); i++) {
                    if ("0".equals(payDetailList.get(i).getFreeType())) { // 只取运费
                        final String costTypeFreight = payDetailList.get(i).getCostTypeFreight();
                        if ("1".equals(costTypeFreight) || "3".equals(costTypeFreight) || "5".equals(costTypeFreight)) { // 油卡
                            finalFee = finalFee.add(payDetailList.get(i).getTransFeeCount().multiply(oil_guiding_price_comp_ratio));
                        } else if (lotBillintType.equals("4")) {// 开票
                            finalFee = finalFee.add(payDetailList.get(i).getTransFeeCount().multiply(guiding_price_comp_ratio));
                        } else {
                            finalFee = finalFee.add(payDetailList.get(i).getTransFeeCount());
                        }
                    }
                }
                finalFee = finalFee.setScale(2, BigDecimal.ROUND_HALF_UP);
                if (finalFee.compareTo(lot.getGuidingPrice()) > 0) {
                    yjxx.append("超指导价: ")
                            .append(finalFee.subtract(lot.getGuidingPrice()))
                            .append("/").append(finalFee.subtract(lot.getGuidingPrice()).multiply(BigDecimal.valueOf(100)).divide(lot.getGuidingPrice(), 2, BigDecimal.ROUND_HALF_UP)).append("%");
                    n++;
                }
            }
            // 未税负毛利
            BigDecimal profitAll = BigDecimal.ZERO; // 最终未税利润
            BigDecimal netYs = BigDecimal.ZERO;
            //String net_profits_oil_tax = sysConfigService.selectConfigByKey("net_profits_oil_tax");
            for (int i = 0; i < entrustList.size(); i++) {
                Map<String, Object> info = receivableReconciliationMapper.netProfitsInfoA(entrustList.get(i).getOrderno(), null);
                BigDecimal _ys = ((BigDecimal) info.get("ys")).setScale(2, BigDecimal.ROUND_HALF_UP);
                netYs = netYs.add(_ys);
                BigDecimal _yf = ((BigDecimal) info.get("yf")).setScale(2, BigDecimal.ROUND_HALF_UP);
                BigDecimal dsf = ((BigDecimal) info.get("dsf")).setScale(2, BigDecimal.ROUND_HALF_UP);
                BigDecimal ptf = ((BigDecimal) info.get("ptf")).setScale(2, BigDecimal.ROUND_HALF_UP);
                BigDecimal yfTax = ((BigDecimal) info.get("yfsf")).setScale(2, BigDecimal.ROUND_HALF_UP);
                BigDecimal sfTax = ((BigDecimal) info.get("dsfsf")).setScale(2, BigDecimal.ROUND_HALF_UP);
                BigDecimal profit = NumberUtil.sub(_ys, _yf, dsf, ptf, yfTax, sfTax);
                profitAll = NumberUtil.add(profitAll, profit);
            }
            if (profitAll.compareTo(BigDecimal.ZERO) < 0) {
                if (n > 0) {
                    yjxx.append("\n");
                }
                yjxx.append("负毛利: ").append(profitAll);/*.append("/");
                if (netYs.compareTo(BigDecimal.ZERO) == 0) {
                    yjxx.append("-100.00%");
                } else {
                    yjxx.append(profitAll.multiply(BigDecimal.valueOf(100)).divide(netYs, 2, BigDecimal.ROUND_HALF_UP)).append("%");
                }*/
            }
            dynaParam.put("yjxx", yjxx.toString());
            if (yjxx.length() > 0 && StringUtils.isNotBlank(lot.getYcyy())) {
                dynaParam.put("ycyy", lot.getYcyy());
            }
            dynaParam.put("ddz", lot.getTransLineName()); // 调度组
            /*Set<String> ywz = new LinkedHashSet<>(); // 运营组
            // 检查运营组是否与字典匹配
            List<Map<String, Object>> dictList = wecomSpMapper.listFieldOptions("dd", Arrays.asList("ywz"));
            for (int i = 0; i < clientList.size(); i++) {
                String salesDeptName = clientList.get(i).getSalesDept();
                if (ywz.contains(salesDeptName)) {
                    continue;
                }
                boolean findDict = false;
                for (int j = 0; j < dictList.size(); j++) {
                    if (Objects.equals(salesDeptName, dictList.get(j).get("value"))) {
                        ywz.add(salesDeptName);
                        findDict = true;
                        break;
                    }
                }
                if (!findDict) {
                    ywz.add(salesDeptName); // 直接将label作为value
                    wecomHandler.pushMarkdown(1L, "调度运单" + lotId + "运营组：" + salesDeptName + "未配置字典");
                }
            }
            if (ywz.size() == 0) {
                // 没在字典中找到任何一个运营组，把前4个运营组塞进去
                //for (int j = 0; j < 4 && j < dictList.size(); j++) {
                    //ywz.add((String) dictList.get(j).get("value"));
                //}
                wecomHandler.pushMarkdown(1L, "调度运单" + lotId + "没有可用运营组字典");
            }
            dynaParam.put("ywz", StringUtils.join(ywz, ","));*/

            // 2024-10-21运费审核调整+总收入、总成本
            Map<String, Object> zsr_zcb = entrustLotMapper.selectZsrZcbOfLot(lot.getEntrustLotId());
            dynaParam.put("zsr", zsr_zcb.get("zsr"));
            dynaParam.put("zcb", zsr_zcb.get("zcb"));
            dynaParam.put("zsf", zsr_zcb.get("zsf"));
            String spNo = wecomService.submitWecomSp(dynaParam, biz, templateId, user); // 这边异常回滚，外部事务也会回滚，因为公用同一事务
            try {
                // 将spNo回写到entrustLot
                entrustLotMapper.writeSpNo(lotId, spNo);
            } catch (Exception e) {
                log.error("回写审批单号异常（不触发回滚）：", e);
                wecomHandler.pushMarkdown(1L, "回写调度运单" + lotId + "单号" + spNo + "异常：" + ExceptionUtil.getExceptionMessage(e));
                pushTms(new SysUser() {{
                    setUserId(1L);
                    setLoginName("admin");
                }}, "回写调度运单" + lotNo + "单号" + spNo + "异常", ExceptionUtil.getExceptionMessage(e));
            }
        } catch (Exception e) {
            log.error("调度运单{}审批异常", lotId, e);
            wecomHandler.pushMarkdown(1L, "调度运单" + lotId + "审批异常：" + ExceptionUtil.getExceptionMessage(e));
            pushTms(new SysUser() {{
                setUserId(1L);
                setLoginName("admin");
            }}, "调度运单" + lotNo + "审批异常", ExceptionUtil.getExceptionMessage(e));
            throw new RuntimeException(e);
        }
    }

    @Override
    @DataScope(deptAlias = "t.trans_line_id", userAlias = "t.dispatcher_id")
    public List<Segment> mList(Segment query) {
        return segmentMapper.mList(query);
    }

    @SneakyThrows
    private void pushTms(SysUser user, String title, final String content) {
        new Thread(() -> {
            try {
                String _content = content;
                if (_content != null) {
                    int byteCount = _content.getBytes("UTF-8").length; //正式服GBK,测试服UTF8
                    if (byteCount > 1000) {
                        int hanCount = byteCount - _content.length();
                        _content = _content.substring(0, 1000 - hanCount);
                    }
                }
                SysMessage sysMessage = new SysMessage();
                sysMessage.setMessageTitle(title);
                sysMessage.setMessageContent(_content);
                sysMessage.setRegScrId("dispatch");
                sysMessage.setCorScrId("dispatch");
                //设为已推送
                sysMessage.setPushStatus(1);
                List<SysUser> userList = Arrays.asList(user);
                sysMessageService.insertSysMessage(sysMessage, userList);
                MsgVO msgVO = new MsgVO();
                msgVO.setType(1);
                msgVO.setContent(_content);
                msgVO.setOpenTab(0);
                msgVO.setUrl("");
                msgVO.setUrlTitle("出现异常");
                sysMessageService.convertAndSendToUser(userList, msgVO);
            } catch (Exception e) {
                log.error("", e);
            }
        }).start();

    }

}
