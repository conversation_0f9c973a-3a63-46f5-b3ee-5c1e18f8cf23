package com.ruoyi.tms.service.trace.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.parser.PdfTextExtractor;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.WechatMessageUtils;
import com.ruoyi.system.domain.SysUploadFile;
import com.ruoyi.system.domain.SysUser;
import com.ruoyi.system.mapper.SysDictDataMapper;
import com.ruoyi.system.mapper.SysUploadFileMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.tms.constant.carrier.EntrustStatusEnum;
import com.ruoyi.tms.domain.basic.*;
import com.ruoyi.tms.domain.carrier.Entrust;
import com.ruoyi.tms.domain.carrier.EntrustLot;
import com.ruoyi.tms.domain.carrier.SunshineBackRecord;
import com.ruoyi.tms.domain.carrier.ZLLGBackRecord;
import com.ruoyi.tms.domain.client.MSalesGroup;
import com.ruoyi.tms.domain.finance.AdjustRecord;
import com.ruoyi.tms.domain.invoice.HYInvoiceRecord;
import com.ruoyi.tms.domain.invoice.Invoice;
import com.ruoyi.tms.domain.invoice.SunshinePdfRecord;
import com.ruoyi.tms.domain.trace.CarLocus;
import com.ruoyi.tms.domain.trace.EntrustCost;
import com.ruoyi.tms.domain.trace.EntrustCostMain;
import com.ruoyi.tms.domain.trace.EntrustWork;
import com.ruoyi.tms.dto.CarSyncDTO;
import com.ruoyi.tms.dto.DriverSyncDTO;
import com.ruoyi.tms.handler.WecomHandler;
import com.ruoyi.tms.mapper.basic.CarDriverPushSbMapper;
import com.ruoyi.tms.mapper.basic.CarMapper;
import com.ruoyi.tms.mapper.basic.CarrierinfoMapper;
import com.ruoyi.tms.mapper.basic.DriverMapper;
import com.ruoyi.tms.mapper.carrier.EntrustLotMapper;
import com.ruoyi.tms.mapper.carrier.EntrustMapper;
import com.ruoyi.tms.mapper.client.ClientMapper;
import com.ruoyi.tms.mapper.client.CustomerServiceMapper;
import com.ruoyi.tms.mapper.client.MSalesGroupMapper;
import com.ruoyi.tms.mapper.invoice.InvoiceMapper;
import com.ruoyi.tms.mapper.trace.CarLocusMapper;
import com.ruoyi.tms.mapper.trace.EntrustWorkMapper;
import com.ruoyi.tms.service.finance.ReceivableReconciliationService;
import com.ruoyi.tms.service.trace.ICarLocusService;
import com.ruoyi.tms.vo.basic.CarPicVO;
import com.ruoyi.tms.vo.basic.DriverPicVO;
import com.ruoyi.tms.vo.client.ClientPopupVO;
import com.ruoyi.tms.vo.finance.ReceiveDetailVO;
import com.ruoyi.tms.vo.finance.receive.ReceiveDetailAdjustVO;
import com.ruoyi.util.AMapUtils;
import com.ruoyi.util.LocationUtils;
import com.ruoyi.util.MapNavUtil;
import com.ruoyi.util.ShiroUtils;
import io.swagger.util.Json;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.util.JedisURIHelper;
import sun.font.BidiUtils;
import redis.clients.jedis.Jedis;
import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLConnection;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.StringUtils.getBigDecimal;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * @description:异步调用方法类
 * @author: baoy
 * @create: 20210329
 **/
@Component
public class AsyncJobService {

	protected final Logger logger = LoggerFactory.getLogger(AsyncJobService.class);

	@Autowired
	private EntrustMapper entrustMapper;
	@Autowired
	private ICarLocusService carLocusService;
	@Autowired
	private CarLocusMapper carLocusMapper;
	@Autowired
	private CarMapper carMapper;
	@Autowired
	private DriverMapper driverMapper;
    @Autowired
	private SysDictDataMapper sysDictDataMapper;
    @Autowired
	private CarrierinfoMapper carrierinfoMapper;
    @Autowired
	private CarDriverPushSbMapper carDriverPushSbMapper;
	@Autowired
	private InvoiceMapper invoiceMapper;
	@Resource
	private WecomHandler wecomHandler;
	@Autowired
	private SysUserMapper sysUserMapper;
	@Autowired
	private EntrustLotMapper entrustLotMapper;
	@Autowired
	@Lazy
	private ReceivableReconciliationService receivableReconciliationService;
	@Resource
	private ISysConfigService sysConfigService;
	@Autowired
	private ClientMapper clientMapper;
	@Autowired
	private MSalesGroupMapper salesGroupMapper;
	@Autowired
	private ShiroUtils shiroUtils;


	@Value("${picPre}")
	private String picPre;

	@Value("${nfp.jh.baseUrl}")
	private String baseUrl;

	@Value("${nfp.jh.authorization}")
	private String authorization;

	@Value("${redis.host}")
	private String redisHost;

	@Value("${redis.port}")
	private String redisPort;

	@Autowired
	private EntrustWorkMapper entrustWorkMapper;
	@Autowired
	private SysUploadFileMapper sysUploadFileMapper;


	/**
	 * 同步车辆到顺邦
	 * @param carId
	 */
	@Async
	public void syncCarToSB(String carId){
		List<PushSBUserInfo> pushSBInfoList = carMapper.getPushSBinfoList();
		for(PushSBUserInfo pushSBUserInfo : pushSBInfoList){
			sysncCar(carId,pushSBUserInfo);
		}
	}

	/**
	 * 同步车辆返回结果
	 * @param carId
	 * @return
	 */
	public boolean syncCarToSBAndReturn(String carId){
		List<PushSBUserInfo> pushSBInfoList = carMapper.getPushSBinfoList();
		for(PushSBUserInfo pushSBUserInfo : pushSBInfoList){
			if(!sysncCar(carId,pushSBUserInfo)){
				return false;
			}
		}
		return true;
	}

	/**
	 * 同步司机到顺邦
	 * @param driverId
	 */
	@Async
	public void syncDriverToSB(String driverId){
		List<PushSBUserInfo> pushSBInfoList = carMapper.getPushSBinfoList();
		for(PushSBUserInfo pushSBUserInfo : pushSBInfoList){
			sysncDriver(driverId,pushSBUserInfo);
		}
	}

	@Async
	public void insertPickArrivalPoint(String entrustId, Date appendTime, String page, String entrustStatus) {
		//委托单信息
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);
		//数据准备
		//保存地址车辆位置信息
		CarLocus carLocus = new CarLocus();
		carLocus.setCarLocusId(IdUtil.simpleUUID());
		carLocus.setLotId(entrust.getLotId());
		carLocus.setVehiclenumber(entrust.getCarno());
		carLocus.setVehiclespeed(0d);//车速
		carLocus.setDirection("");//方向
		carLocus.setGetlocationtime(appendTime);//时间
		carLocus.setTrackingTime(appendTime);
		carLocus.setRegScrId(page);
		carLocus.setCorScrId(page);
		carLocus.setInvoiceId(entrust.getOrderno());
		carLocus.setInvoiceNo(entrust.getInvoiceVbillno());
		carLocus.setEntrustId(entrust.getEntrustId());
		carLocus.setEntrustNo(entrust.getVbillno());
		carLocus.setLotId(entrust.getLotId());
		carLocus.setLotNo(entrust.getLot());
		carLocus.setDataSource("1");//北斗
		carLocus.setDisparity("0");
		//计算两点之间  要求地址和实际地址之间的距离
		if (EntrustStatusEnum.AFFIRM.getValue().equals(entrustStatus)) {
			//0：提货作业 1:到货作业
			carLocus.setLocusType("0");
			//提货地址
			String deliAddress = entrust.getDeliProName() + entrust.getDeliCityName() + entrust.getDeliAreaName() + entrust.getDeliDetailAddr();
			String deliLngLat = AMapUtils.getLngLat(deliAddress);
			//拆分经纬度
			if (deliLngLat != null) {
				String[] lngLatArr = deliLngLat.split(",");
				carLocus.setLongitudedegree(BigDecimal.valueOf(Double.valueOf(lngLatArr[0])));//经度
				carLocus.setLatitudedegree(BigDecimal.valueOf(Double.valueOf(lngLatArr[1])));//纬度
				carLocus.setProCode(entrust.getDeliProvinceId());
				carLocus.setProName(entrust.getDeliProName());
				carLocus.setCityCode(entrust.getDeliCityId());
				carLocus.setCityName(entrust.getDeliCityName());
				carLocus.setAreaCode(entrust.getDeliAddrCode());
				carLocus.setAreaName(entrust.getDeliAreaName());
				carLocus.setDetailAddr(entrust.getDeliDetailAddr());
				carLocusMapper.insertCarLocus(carLocus);


				Integer isManualEntry = entrust.getIsManualEntry();
				Entrust etUpdate = new Entrust();
				etUpdate.setEntrustId(entrustId);
				etUpdate.setIsManualEntry(isManualEntry + 1);
				entrustMapper.updateEntrust(etUpdate);

				if ("1".equals(entrust.getIsFleetData()) && "1".equals(entrust.getIsFleetAssign())) {
					Entrust entrustBiz = entrustMapper.selectEntrustById(entrust.getBizEntrustId());
					Integer isManualEntryBiz = entrustBiz.getIsManualEntry();
					entrustBiz.setIsManualEntry(isManualEntryBiz + 1);
					entrustMapper.updateEntrust(entrustBiz);
				}

			}
		} else if (EntrustStatusEnum.PICK_UP.getValue().equals(entrustStatus)) {
			//0：提货作业 1:到货作业
			carLocus.setLocusType("1");
			//到货
			String arriAddress = entrust.getArriProName() + entrust.getArriCityName() + entrust.getArriAreaName() + entrust.getArriDetailAddr();
			String arriLngLat = AMapUtils.getLngLat(arriAddress);
			if (arriLngLat != null) {
				String[] lngLatArr = arriLngLat.split(",");
				carLocus.setLongitudedegree(BigDecimal.valueOf(Double.valueOf(lngLatArr[0])));//经度
				carLocus.setLatitudedegree(BigDecimal.valueOf(Double.valueOf(lngLatArr[1])));//纬度
				carLocus.setProCode(entrust.getArriProvinceId());
				carLocus.setProName(entrust.getArriProName());
				carLocus.setCityCode(entrust.getArriCityId());
				carLocus.setCityName(entrust.getArriCityName());
				carLocus.setAreaCode(entrust.getArriAddrCode());
				carLocus.setAreaName(entrust.getArriAreaName());
				carLocus.setDetailAddr(entrust.getArriDetailAddr());
				carLocusMapper.insertCarLocus(carLocus);

				Integer isManualEntry = entrust.getIsManualEntry();
				Entrust etUpdate = new Entrust();
				etUpdate.setEntrustId(entrustId);
				etUpdate.setIsManualEntry(isManualEntry + 1);
				entrustMapper.updateEntrust(etUpdate);

				if ("1".equals(entrust.getIsFleetData()) && "1".equals(entrust.getIsFleetAssign())) {
					Entrust entrustBiz = entrustMapper.selectEntrustById(entrust.getBizEntrustId());
					Integer isManualEntryBiz = entrustBiz.getIsManualEntry();
					entrustBiz.setIsManualEntry(isManualEntryBiz + 1);
					entrustMapper.updateEntrust(entrustBiz);
				}

			}

		}
	}


	/**
	 * 同步司机返回结果
	 * @param driverId
	 */
	public boolean syncDriverToSBAndReturn(String driverId){
		List<PushSBUserInfo> pushSBInfoList = carMapper.getPushSBinfoList();
		for(PushSBUserInfo pushSBUserInfo : pushSBInfoList){
			if(!sysncDriver(driverId,pushSBUserInfo)){
				return false;
			}
		}
		return true;
	}

	public void saveCarLocationSync(String entrustId, Date startTime, Date endTime, String page, String entrustStatus){
		//委托单信息
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);

		//根据车牌号，时间段查询车辆位置
		Map<String,Object> location = LocationUtils.getVehicleHistoryByCarNo(entrust.getCarno(),startTime,endTime);

		if(location != null){
			BigDecimal lon =  getBigDecimal(location.get("lngitude"));
			BigDecimal lat =  getBigDecimal(location.get("latitude"));

			//根据经纬度获取省市区
			String lonLat = lon.toString()+","+lat.toString();
			String address = AMapUtils.getAddress(lonLat);
			//地址串
			Map<String,String> addressMap = carLocusService.addressSplit(address);
			//保存地址车辆位置信息
			CarLocus carLocus = new CarLocus();
			carLocus.setCarLocusId(IdUtil.simpleUUID());
			carLocus.setLotId(entrust.getLotId());
			carLocus.setVehiclenumber(entrust.getCarno());
			carLocus.setVehiclespeed(Double.parseDouble(location.get("vehiclespeed").toString()));//车速
			carLocus.setLongitudedegree(lon);//经度
			carLocus.setLatitudedegree(lat);//纬度
			carLocus.setDirection(location.get("direction").toString());//方向
			carLocus.setGetlocationtime((Date)location.get("getlocationtime"));//时间
			carLocus.setTrackingTime((Date)location.get("getlocationtime"));
			carLocus.setRegScrId(page);
			carLocus.setCorScrId(page);
			carLocus.setInvoiceId(entrust.getOrderno());
			carLocus.setInvoiceNo(entrust.getInvoiceVbillno());
			carLocus.setEntrustId(entrust.getEntrustId());
			carLocus.setEntrustNo(entrust.getVbillno());
			carLocus.setLotId(entrust.getLotId());
			carLocus.setLotNo(entrust.getLot());
			carLocus.setDataSource("1");//北斗
			carLocus.setProCode(addressMap.get("proCode"));
			carLocus.setProName(addressMap.get("proName"));
			carLocus.setCityCode(addressMap.get("cityCode"));
			carLocus.setCityName(addressMap.get("cityName"));
			carLocus.setAreaCode(addressMap.get("areaCode"));
			carLocus.setAreaName(addressMap.get("areaName"));
			carLocus.setDetailAddr(addressMap.get("detailAddr"));

			//计算两点之间  要求地址和实际地址之间的距离
			if(EntrustStatusEnum.AFFIRM.getValue().equals(entrustStatus)){
				//提货
				String deliAddress = entrust.getDeliProName()+entrust.getDeliCityName()+entrust.getDeliAreaName()+entrust.getDeliDetailAddr();
				String deliLngLat = AMapUtils.getLngLat(deliAddress);
				MapNavUtil mapResult=new MapNavUtil(lonLat, deliLngLat);
				//获取两点之间的距离
				String distance = mapResult.getResults().getDistance();
				carLocus.setDisparity(distance);
				//0：提货作业 1:到货作业
				carLocus.setLocusType("0");
			}else if(EntrustStatusEnum.PICK_UP.getValue().equals(entrustStatus)){
				//到货
				String arriAddress = entrust.getArriProName()+entrust.getArriCityName()+entrust.getArriAreaName()+entrust.getArriDetailAddr();
				String arriLngLat = AMapUtils.getLngLat(arriAddress);
				MapNavUtil mapResult=new MapNavUtil(lonLat, arriLngLat);
				//获取两点之间的距离
				String distance = mapResult.getResults().getDistance();
				carLocus.setDisparity(distance);
				//0：提货作业 1:到货作业
				carLocus.setLocusType("1");
			}
			carLocusMapper.insertCarLocus(carLocus);
		}
	}

	/**
	 * 同步车辆信息到nfp
	 * @param carId
	 */
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public boolean sysncCar(String carId,PushSBUserInfo pushSBUserInfo){
		//获取车辆信息
		Car car = carMapper.selectCarById(carId);
		//封装车辆对象
		CarSyncDTO syncDTO = new CarSyncDTO();
		if(StringUtils.isNotBlank(car.getOperationType())){
			//车辆分类
			syncDTO.setVehicleClassificationCode(sysDictDataMapper.selectDictLabel("OPERATION_TYPE",car.getOperationType()));
		}
		if(StringUtils.isNotBlank(car.getLicenseplatetypecode())) {
			//牌照类型代码
			syncDTO.setLicensePlateTypeCode(sysDictDataMapper.selectDictLabel("car_plate_type",car.getLicenseplatetypecode()));
		}
		//车辆牌照号
		syncDTO.setVehicleNumber(car.getCarno());
		if(StringUtils.isNotBlank(car.getVehiclelicenseplatecolor())) {
			//车牌颜色
			syncDTO.setVehicleLicensePlateColor(sysDictDataMapper.selectDictLabel("car_plate_color",car.getVehiclelicenseplatecolor()));
		}
		if(StringUtils.isNotBlank(car.getVehicleenergytype())) {
			//车辆能源类型
			syncDTO.setVehicleEnergyType(sysDictDataMapper.selectDictLabel("vehicleenergytype",car.getVehicleenergytype()));
		}
		//车长
		syncDTO.setCarLen(car.getCarLenName());
		//车辆识别代号
		syncDTO.setVin(car.getVin());
		//发证机关
		syncDTO.setIssuingOrganizations(car.getIssuingorganizations());
		//注册日期
		syncDTO.setRegisterDate(car.getRegisterdate());
		//发证日期
		syncDTO.setIssueDate(car.getIssuedate());
		//挂车牌照号
		syncDTO.setTrailerLicenceno(car.getTrailerlicenceno());
		//车辆长度
		if(car.getLength() != null)
			syncDTO.setVehicleLength(BigDecimal.valueOf(car.getLength()));
		//车辆宽度
		if(car.getWidth() != null)
			syncDTO.setVehicleWidth(BigDecimal.valueOf(car.getWidth()));
		//车辆高度
		if(car.getHeight() != null)
			syncDTO.setVehicleHeight(BigDecimal.valueOf(car.getHeight()));
		//满载车辆质量 * 1000 吨 转 千克
		if(car.getVehicleladenweight()!= null)
			syncDTO.setVehicleLadenWeight(BigDecimal.valueOf(car.getVehicleladenweight()*1000));
		//车辆载质量 * 1000 吨 转 千克
		if(car.getVehicletonnage()!= null)
			syncDTO.setVehicleTonnage(BigDecimal.valueOf(car.getVehicletonnage()*1000));
		//道路运输证号
		syncDTO.setRoadTransportCertificateNumber(car.getRoadtransportcertificatenumber());
		//车轴数
		syncDTO.setAxleNum(car.getAxlenum());
		//备注
		syncDTO.setRemark(car.getRemark());
		//年检有效期
		syncDTO.setYearlyInspectionDate(car.getYearlyInspectionDate());

		//车辆照片
		//车辆图片对应TID集合
		List<CarPicVO> carPicVOS = carMapper.selectCarPicById(carId);
		for(CarPicVO carPicVO : carPicVOS){
			if("1".equals(carPicVO.getPicType())){
				//车辆行驶证
				syncDTO.setLicenseUrlFront(picPre+carPicVO.getFilePath());
			}else if("2".equals(carPicVO.getPicType())){
				//道路运输许可证
				syncDTO.setTransportUrlMain(picPre+carPicVO.getFilePath());
			}else if("5".equals(carPicVO.getPicType())){
				//挂车行驶证
				syncDTO.setLicenseUrlBack(picPre+carPicVO.getFilePath());
			}
		}
		//实际承运人信息
		Carrierinfo carrierinfo = carrierinfoMapper.selectCarrierinfoById(car.getActualCarrierid());
		//道路运输经营许可证
		syncDTO.setPermitNumber(carrierinfo.getPermitnumber());
		//承运人名称
		syncDTO.setCarrierName(carrierinfo.getCarriername());
		//承运人证件号码
		syncDTO.setSocialcodeIdCard(carrierinfo.getUnifiedsocialcreditldentifier());
		//联系人
		syncDTO.setContactName(carrierinfo.getContactname());
		//联系人手机号码
		syncDTO.setContactPhoneNumber(carrierinfo.getContactmobiletelephonenumber());
		//所有人
		syncDTO.setOwner(carrierinfo.getCarriername());


		//请求NFP同步接口
		//获取登录信息
		String token = getToken(pushSBUserInfo);
		String postJson = JSONUtil.toJsonStr(syncDTO);
		try {
			//String result = HttpRequest.post("https://nfp.qixin56.com/api/nfp-pushdata-js/syncDriver")
			//String result = HttpRequest.post("http://127.0.0.1/nfp-pushdata-js/syncCar")
			String result = HttpRequest.post(baseUrl+"/nfp-pushdata-js/syncCar")
					.header("Blade-Auth", "bearer " + token)
					.header("Authorization", "Basic "+authorization)
					.header("Tenant-Id", pushSBUserInfo.getTalentId())
					.body(postJson)
					.timeout(60000)//超时，毫秒
					.execute().body();
			if(StringUtils.isNotBlank(result)) {
				JSONObject jsonObject = JSONObject.parseObject(result);
				Integer code = Integer.valueOf(jsonObject.get("code").toString());
				//司机推送
				CarDriverPushSb carDriverPushSb = new CarDriverPushSb();
				carDriverPushSb.setBaseId(car.getCarId());
				carDriverPushSb.setSbId(pushSBUserInfo.getId());
				carDriverPushSb.setCorpId(pushSBUserInfo.getCorpId());
				carDriverPushSb.setSynTime(new Date());
				//删除历史推送记录
				carDriverPushSbMapper.deleteCarDriverPushSbByBaseIdAndSbId(car.getCarId(),pushSBUserInfo.getId());
				if (code == 200) {
					//更新车辆状态为待审验
					carDriverPushSb.setSynFlag(1);
					carDriverPushSbMapper.insertCarDriverPushSb(carDriverPushSb);
					return true;
				}else{
					String msg = jsonObject.get("msg").toString();
					//更新车辆状态为不通过
					carDriverPushSb.setSynFlag(3);
					carDriverPushSb.setSynMsg(msg);
					carDriverPushSbMapper.insertCarDriverPushSb(carDriverPushSb);
				}
			}
		}catch (Exception e){
			logger.error("syncCar:"+e.getMessage());
		}
		return false;
	}

	/**
	 * 同步司机信息到nfp
	 * @param driverId
	 */
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public boolean sysncDriver(String driverId,PushSBUserInfo pushSBUserInfo){

		//获取车辆信息
		Driver driver = driverMapper.selectDriverById(driverId);
		//封装车辆对象
		DriverSyncDTO driverSyncDTO = new DriverSyncDTO();
		//驾驶员姓名
		driverSyncDTO.setNameOfPerson(driver.getDriverName());
		//驾驶员性别
		if(driver.getSex() != null) {
			driverSyncDTO.setGender(Integer.valueOf(driver.getSex()));
		}
		//身份证号码
		driverSyncDTO.setIdcardNumber(driver.getCardId());
		//电话号码
		driverSyncDTO.setMobilephoneNumber(driver.getPhone());
		//准驾车型
		if(StringUtils.isNotBlank(driver.getLicType())){
			driverSyncDTO.setVehicleClass(sysDictDataMapper.selectDictLabel("lic_type",driver.getLicType()));
		}
		//身份证有效期至
		driverSyncDTO.setIdCardValidDateTo(driver.getCardDate());
		//发证机关
		driverSyncDTO.setIssuingOrganizations(driver.getIssuingorganizations());
		//有效期起至
		//driverSyncDTO.setValidperiodFrom(driver.getValidperiodfrom());
		driverSyncDTO.setValidperiodTo(driver.getDriverLicExpiryDate());
		//从业资格证
		driverSyncDTO.setQualificationCertificateNumber(driver.getQualificationcertificatenumber());
		//驾驶证号码
		driverSyncDTO.setDriverLicenseNumber(driver.getDriverLic());
		//查询司机照片
		List<DriverPicVO> driverPicVOS = driverMapper.selectDriverPicById(driverId);
		for(DriverPicVO pic : driverPicVOS){
			if("1".equals(pic.getPicType())){
				//驾驶证
				driverSyncDTO.setDriveUrl(picPre+pic.getFilePath());
			}else if("2".equals(pic.getPicType())){
				//身份证正面
				driverSyncDTO.setIdcardUrlFront(picPre+pic.getFilePath());
			}else if("3".equals(pic.getPicType())){
				//身份证背面
				driverSyncDTO.setIdcardUrlBack(picPre+pic.getFilePath());
			}else if("4".equals(pic.getPicType())){
				//从业资格证
				driverSyncDTO.setCongyeUrlMain(picPre+pic.getFilePath());
			}
		}
		//请求NFP同步接口
		//获取登录信息
		String token = getToken(pushSBUserInfo);
		String postJson = JSONUtil.toJsonStr(driverSyncDTO);
		try {
			//String result = HttpRequest.post("https://nfp.qixin56.com/api/nfp-pushdata-js/syncDriver")
			//String result = HttpRequest.post("http://127.0.0.1/nfp-pushdata-js/syncDriver")
			String result = HttpRequest.post(baseUrl+"/nfp-pushdata-js/syncDriver")
					.header("Blade-Auth", "bearer " + token)
					.header("Authorization", "Basic "+authorization)
					.header("Tenant-Id", pushSBUserInfo.getTalentId())
					.body(postJson)
					.timeout(60000)//超时，毫秒
					.execute().body();
			if(StringUtils.isNotBlank(result)) {
				JSONObject jsonObject = JSONObject.parseObject(result);
				Integer code = Integer.valueOf(jsonObject.get("code").toString());
				//司机推送
				CarDriverPushSb carDriverPushSb = new CarDriverPushSb();
				carDriverPushSb.setBaseId(driver.getDriverId());
				carDriverPushSb.setSbId(pushSBUserInfo.getId());
				carDriverPushSb.setCorpId(pushSBUserInfo.getCorpId());
				carDriverPushSb.setSynTime(new Date());
				//删除历史推送记录
				carDriverPushSbMapper.deleteCarDriverPushSbByBaseIdAndSbId(driver.getDriverId(),pushSBUserInfo.getId());
				if (code == 200) {
					//更新司机状态为待审验
					carDriverPushSb.setSynFlag(1);
					carDriverPushSbMapper.insertCarDriverPushSb(carDriverPushSb);
					return true;
				}else{
					String msg = jsonObject.get("msg").toString();
					//更新司机状态为不通过
					carDriverPushSb.setSynFlag(3);
					carDriverPushSb.setSynMsg(msg);
					carDriverPushSbMapper.insertCarDriverPushSb(carDriverPushSb);
				}
			}
		}catch (Exception e){
			logger.error("sysncDriver:"+e.getMessage());
		}
		return false;
	}

	/**
	 * 获取nfp token
	 * @return
	 */
	public String getToken(PushSBUserInfo pushSBUserInfo){
		Map<String,Object> sendParam = new HashMap<>();
		sendParam.put("grant_type","password");
		sendParam.put("username",pushSBUserInfo.getUsername());
		sendParam.put("password", SecureUtil.md5(pushSBUserInfo.getPassword()));
		sendParam.put("scope","all");
		//String result = HttpRequest.post("https://nfp.qixin56.com/api/blade-auth/oauth/token")
		//String result = HttpRequest.post("http://localhost/blade-auth/oauth/token")
		String result = HttpRequest.post(baseUrl+"/blade-auth/oauth/token")
				.header("Authorization","Basic "+authorization)
				.header("Content-Type","application/x-www-form-urlencoded")
				.header("Tenant-Id",pushSBUserInfo.getTalentId())
				.form(sendParam).timeout(20000)
				.execute().body();
		logger.debug("从{}获取token:{}", baseUrl + "/blade-auth/oauth/token", result);
		JSONObject jsonObject = JSONObject.parseObject(result);
		return jsonObject.getString("access_token");
	}


	/**
	 * 推送负毛利信息
	 * @param invoiceId
	 */
	@Async
	public void pushFMLInfo(String invoiceId){
		try {
			Thread.sleep(5000);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		Invoice invoice = invoiceMapper.selectFMLInvoiceById(invoiceId);

		String oilTax = sysConfigService.selectConfigByKey("net_profits_oil_tax");
		Map<String, Object> dtl = receivableReconciliationService.netProfitsInfo(invoiceId);

		BigDecimal ys = new BigDecimal(dtl.get("ys").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
		BigDecimal yf = new BigDecimal(dtl.get("yf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
		BigDecimal dsf = new BigDecimal(dtl.get("dsf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
		BigDecimal ptf = new BigDecimal(dtl.get("ptf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
		BigDecimal cbsj = new BigDecimal(dtl.get("cbsj").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
		//BigDecimal zyf = yf.add(dsf).add(ptf);

		BigDecimal allCb = invoice.getYfTotal().add(ptf).add(cbsj);

		BigDecimal profit = invoice.getYsTotal().subtract(allCb);

		if(profit.compareTo(BigDecimal.ZERO) < 0) {
			BigDecimal profitRate = BigDecimal.ZERO;
			if(invoice.getYsTotal().compareTo(BigDecimal.ZERO) == 0){
				profitRate =  new BigDecimal(-100);
			}else{
				profitRate =  profit.multiply(new BigDecimal(100)).divide(invoice.getYsTotal(),2, BigDecimal.ROUND_HALF_UP);
			}


			HashSet<Long> userIds = new HashSet<>();
			//周振丰
			userIds.add(223804l);
			//包扬
			userIds.add(236177l);
			//高永芝
			userIds.add(95173l);
			//石晶
			userIds.add(297453l);
			//赵俊刚
			userIds.add(95172l);
			//倪华锋
			userIds.add(264094l);
			//调度人
			userIds.add(shiroUtils.getUserId());
			//发货单创建人
			if(invoice.getRegUserId() != null) {
				userIds.add(Long.valueOf(invoice.getRegUserId()));
			}
			//查询客户运营部
			if(invoice.getCustomerId() != null) {
				ClientPopupVO clientPopupVO = clientMapper.selectClientById(invoice.getCustomerId());
				if(StringUtils.isNotBlank(clientPopupVO.getSalesId())){
					MSalesGroup mSalesGroup = salesGroupMapper.selectByPrimaryKey(clientPopupVO.getSalesId());
					String salesTeamLeader = mSalesGroup.getSalesTeamLeader();
					List<SysUser> sysUserList = sysUserMapper.selectUserByUserName(salesTeamLeader, "00");
					for(SysUser sysUser : sysUserList){
						userIds.add(sysUser.getUserId());
					}
				}
			}
			//根据运营组找人
			SysUser sysUser = sysUserMapper.selectUserByLoginName(invoice.getSalesDept(), "00");
			if(sysUser != null){
				userIds.add(sysUser.getUserId());
			}
			List<EntrustLot> entrustLotList = entrustLotMapper.selectEntrustLotByInvoiceId(invoice.getInvoiceId());
			Set<String> regUserNameSet = new HashSet<>();
			for(EntrustLot entrustLot : entrustLotList){
				regUserNameSet.add(entrustLot.getRegUserName());
				if(entrustLot.getTransType().equals("0") || entrustLot.getTransType().equals("4") || entrustLot.getTransType().equals("15")){
					userIds.add(94851l);
					userIds.add(94880l);
				}else{
					userIds.add(233130l);
				}
			}



			//调度人名称
			String dispatcherName = regUserNameSet.stream().collect(Collectors.joining(","));
			//发货单查询运单
			StringBuffer sb = new StringBuffer();
			sb.append("`负毛利`提醒").append("\n");
			sb.append("<font color=\"comment\">").append(DateFormatUtils.format(invoice.getReqDeliDate(),"yyyy-MM-dd")).append("</font>").append("\n");
			sb.append("<font color=\"comment\">").append(invoice.getSalesDept()).append("</font>").append("\n");
		/*sb.append(">毛利:").append("<font color=\"warning\">").append(invoice.getFmlAmount()).append("</font>").append("\n");
		sb.append(" 毛利率：").append("<font color=\"warning\">").append(invoice.getProfitRate().multiply(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP)).append("%").append("</font>").append("\n");*/
			//sb.append(">总应收:").append(invoice.getYsTotal().setScale(2,BigDecimal.ROUND_HALF_UP)).append("\n");
			//sb.append(">未税应收:").append(ys).append("\n");
			//sb.append("\n");
			sb.append(">总成本:").append(allCb.setScale(2,BigDecimal.ROUND_HALF_UP)).append("\n");
			//sb.append(">未税成本:").append(zyf).append("\n");


			if(dtl.containsKey("xjAmount")){
				List<String> xjAmount= (List<String>) dtl.get("xjAmount");
				for(String xj : xjAmount){
					sb.append("><font color=\"comment\">—现金:").append(xj).append("</font>").append("\n");
				}
			}else{
				sb.append("><font color=\"comment\">—现金:").append("0.00").append("</font>").append("\n");
			}

			if(dtl.containsKey("ykAmount")){
				BigDecimal ykAmount = new BigDecimal(dtl.get("ykAmount").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
				BigDecimal dke = ykAmount.subtract(ykAmount.divide(new BigDecimal(oilTax),2,BigDecimal.ROUND_HALF_UP)).setScale(2, BigDecimal.ROUND_HALF_UP);
				sb.append("><font color=\"comment\">—油卡:").append(ykAmount);
				/*if(dke.compareTo(BigDecimal.ZERO) != 0) {
					sb.append("(13%)").append(";抵扣额:").append(dke);
					sb.append("(13%)");
				}*/
				sb.append("</font>").append("\n");
			}else{
				sb.append("><font color=\"comment\">—油卡:").append("0.00").append("</font>").append("\n");
			}
			if(dtl.containsKey("g7Amount")){
				BigDecimal g7Amount = new BigDecimal(dtl.get("g7Amount").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
				if(g7Amount.compareTo(BigDecimal.ZERO) != 0) {
					String net_profits_g7_rate = sysConfigService.selectConfigByKey("net_profits_g7_rate");
					BigDecimal dke = g7Amount.subtract(g7Amount.divide(new BigDecimal(1.09), 2, BigDecimal.ROUND_HALF_UP).add(g7Amount.multiply(new BigDecimal(net_profits_g7_rate)))).setScale(2, BigDecimal.ROUND_HALF_UP);
					sb.append("><font color=\"comment\">—g7:").append(g7Amount);
					/*if (dke.compareTo(BigDecimal.ZERO) != 0) {
						sb.append(";抵扣额:").append(dke);
					}*/
					sb.append("</font>").append("\n");
				}
			}
			if(dsf.compareTo(BigDecimal.ZERO) != 0) {
				sb.append("><font color=\"comment\">—第三方费用:").append(dsf).append("</font>").append("\n");
			}else{
				sb.append("><font color=\"comment\">—第三方费用:").append("0.00").append("</font>").append("\n");
			}
			if(ptf.compareTo(BigDecimal.ZERO) != 0) {
				sb.append("><font color=\"comment\">—平台费:").append(ptf).append("</font>").append("\n");
			}else{
				sb.append("><font color=\"comment\">—平台费:").append("0.00").append("</font>").append("\n");
			}
			if(cbsj.compareTo(BigDecimal.ZERO) != 0) {
				sb.append("><font color=\"comment\">—成本税金:").append(cbsj).append("</font>").append("\n");
			}else{
				sb.append("><font color=\"comment\">—成本税金:").append("0.00").append("</font>").append("\n");
			}
			sb.append("\n");
			sb.append(">利润:").append("<font color=\"warning\">").append(profit).append("</font>").append("\n");
			sb.append(">利润率:").append("<font color=\"warning\">").append(profitRate).append("%").append("</font>").append("\n");
			sb.append("\n");
			sb.append("**<font color=\"info\">").append(invoice.getVbillno()).append("</font>**").append("\n");
			//sb.append(">客户:").append(invoice.getCustAbbr()).append("\n");
			sb.append(">线路:");
			if (invoice.getDeliProName().equals("北京市") || invoice.getDeliProName().equals("天津市") || invoice.getDeliProName().equals("上海市") || invoice.getDeliProName().equals("重庆市")){
				sb.append(invoice.getDeliProName());
			}else{
				sb.append(invoice.getDeliCityName());
			}
			sb.append("==>");
			if (invoice.getArriProName().equals("北京市") || invoice.getArriProName().equals("天津市") || invoice.getArriProName().equals("上海市") || invoice.getArriProName().equals("重庆市")){
				sb.append(invoice.getArriProName());
			}else{
				sb.append(invoice.getArriCityName());
			}
			sb.append("\n");
			sb.append(">货量:").append(invoice.getGoodsName()).append(" ");
			if(invoice.getNumCount() != null && invoice.getNumCount() != 0){
				sb.append(invoice.getNumCount()).append("件");
			}
			if(invoice.getWeightCount() != null && invoice.getWeightCount() != 0){
				sb.append(invoice.getWeightCount()).append("吨");
			}
			if(invoice.getVolumeCount() != null && invoice.getVolumeCount() != 0){
				sb.append(invoice.getVolumeCount()).append("方");
			}
			sb.append("\n");
			sb.append(">车型:").append(invoice.getCarLenName()).append(invoice.getCarTypeName()).append("\n");
			sb.append(">承运商:").append(invoice.getCarrName()).append("\n");
			sb.append(">调度人:").append(dispatcherName).append("\n");
			for(Long id : userIds){
				wecomHandler.pushMarkdown(id, sb.toString());
			}
		}
	}



	/**
	 * 浙江恒逸回单
	 * @param entrustId
	 */
	@Async
	public void zjhyReceipt(String entrustId){
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);

		//根据发货单号查询是否存在需要同步的单据
		HYInvoiceRecord hyInvoiceRecord = invoiceMapper.selectSuccessHyInvoiceByInvoiceId(entrust.getOrderno());
		//恒逸单据判断
		if(hyInvoiceRecord != null && hyInvoiceRecord.getOriginNo().contains("T")){
			logger.debug("浙江恒逸回单上传发货单号：{}",entrust.getOrderno());
			//登录
			String tokenUrl = "https://tms.hengyi.com/login/login";

			Map<String, Object> sendParams = new HashMap<>();
			sendParams.put("userCode", "990000902");
			sendParams.put("userPassword", "e1a42180caa49f5fabc51836a8bd197d");
			sendParams.put("verificationCode", "");

			String token = null;

			Map<String, List<String>> headers = HttpRequest.post(tokenUrl).body(JSON.toJSONString(sendParams)).execute().headers();
			List<String> cookieList = headers.get("Set-Cookie");
			for(String cookie : cookieList){
				if(cookie.startsWith("RequestToken")){
					token = cookie.split(";")[0].split("=")[1];
				}
			}

			//====================根据恒逸运单号查询列表信息，获取verion版本号====================
			Calendar cal = Calendar.getInstance();
			cal.setTime(new Date());
			cal.add(Calendar.MONTH,-1);

			Map<String, Object> listSendParams = new HashMap<>();
			List<Long> createTimeList = new ArrayList<>();
			createTimeList.add(cal.getTimeInMillis());
			cal.setTime(new Date());
			createTimeList.add(cal.getTimeInMillis());
			listSendParams.put("createTimeList",createTimeList);

			Map<String,Object> pageParamNewVO = new HashMap<>();
			pageParamNewVO.put("curPage",1);
			pageParamNewVO.put("pageSize",20);
			pageParamNewVO.put("sortDir","");
			pageParamNewVO.put("sortIndx","");
			listSendParams.put("pageParamNewVO",pageParamNewVO);
			listSendParams.put("mainStatus","400");

			List<Integer> approvalStatusList = new ArrayList<>();
			approvalStatusList.add(2);
			approvalStatusList.add(5);
			listSendParams.put("approvalStatusList",approvalStatusList);
			listSendParams.put("wayBillNo",hyInvoiceRecord.getOriginNo());

			String listUrl = "https://tms.hengyi.com/msapi3/E6-MS-TMS-BUSI-WEB/waybill/pageProductQueryByCondition";
			String listRes = HttpRequest.post(listUrl).header("Content-Type", "application/json;charset=UTF-8")
					.header("Token", token).body(JSON.toJSONString(listSendParams)).execute().body();

			JSONObject listResJson = JSON.parseObject(listRes);
			JSONArray jsonArray = listResJson.getJSONObject("result").getJSONArray("data");
			if(jsonArray.size() > 0){
				JSONObject jsonObject = jsonArray.getJSONObject(0);
				Long version = jsonObject.getLong("version");
				String id = jsonObject.getString("id");


				//====================获取提交前货源信息，版本号====================
				String beforeUrl = "https://tms.hengyi.com/msapi3/E6-MS-TMS-BUSI-WEB/waybillAction/getOrderBeforeReceipt";
				//请求参数
				sendParams = new HashMap<>();
				sendParams.put("corpId", 248);
				sendParams.put("id", id);
				sendParams.put("plantForm", 1);
				sendParams.put("transport", 2);
				sendParams.put("version", version);

				String beforeRes = HttpRequest.post(beforeUrl).header("Content-Type", "application/json;charset=UTF-8")
						.header("Token", token).body(JSON.toJSONString(sendParams)).execute().body();

				JSONObject beforeResJson = JSON.parseObject(beforeRes);
				version = beforeResJson.getJSONObject("result").getLong("version");

				//订单对象
				List<Map<String,Object>> orderList = new ArrayList<>();

				Boolean uploadErrorFlag = true;

				JSONArray orderListBefore = beforeResJson.getJSONObject("result").getJSONArray("orderList");
				for(int i = 0; i < orderListBefore.size(); i++){
					JSONObject order = orderListBefore.getJSONObject(i);

					Map<String,Object> orderMap = new HashMap<>();
					List<String> unloadingUrlList = new ArrayList<>();
					//上传回单附件
					List<SysUploadFile> sysUploadFiles = entrustWorkMapper.selectReceiptPicByEntrustId(entrust.getEntrustId());
					for(SysUploadFile sysUploadFile : sysUploadFiles){
						String filePath = "D:/"+sysUploadFile.getFilePath();
						String url = "https://tms.hengyi.com/msapi3/COMMON-MODULE-BASE-FILE-PROCESSOR-WEB/api/upload/fildUploadPublicByInputStream?customerId=e6yun3.0&businessTypeKey=waybillPlatform";

						Map<String, String> headersPost = new HashMap<>();
						headersPost.put("Accept", "*/*");
						headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
						headersPost.put("Connection", "keep-alive");
						headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryjfPDtu6h3wMxaApO");
						headersPost.put("Origin", "https://tms.hengyi.com");
						headersPost.put("Referer", "https://tms.hengyi.com/");
						headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
						headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
						headersPost.put("sec-ch-ua-mobile", "?0");
						headersPost.put("sec-ch-ua-platform", "\"Windows\"");
						headersPost.put("token", token);

						HttpRequest request = HttpRequest.post(url)
								.headerMap(headersPost, true)
								.form("files", new File(filePath));

						cn.hutool.http.HttpResponse execute = request.execute();

						String uploadRes = execute.body();
						logger.debug("文件上传结果：{}",uploadRes);
						JSONObject upload = JSONObject.parseObject(uploadRes);
						if(upload.getInteger("code") == 1){
							String fileUrl = upload.getJSONArray("result").getJSONObject(0).getString("fileUrl");
							unloadingUrlList.add(fileUrl);
						}else{
							uploadErrorFlag = false;
						}
					}

					orderMap.put("unloadingUrlList",unloadingUrlList);
					orderMap.put("orderId",order.getString("orderId"));
					orderMap.put("orderNo",order.getString("orderNo"));
					orderMap.put("customerName",order.getString("customerName"));
					orderMap.put("receivingAreaAddress",order.getString("receivingAreaAddress"));
					orderMap.put("unloadingUrlList",unloadingUrlList);
					orderList.add(orderMap);
				}

				//判断上传成继续
				if(uploadErrorFlag){
					String receiptUrl = "https://tms.hengyi.com/msapi3/E6-MS-TMS-BUSI-WEB/waybillAction/receipt";
					//请求参数
					sendParams = new HashMap<>();
					sendParams.put("corpId", 248);
					sendParams.put("id", id);
					sendParams.put("plantForm", 1);
					sendParams.put("transport", 2);
					sendParams.put("version", version);
					sendParams.put("orderList", orderList);


					String receiptRes = HttpRequest.post(receiptUrl).header("Content-Type", "application/json;charset=UTF-8")
							.header("Token", token).body(JSON.toJSONString(sendParams)).execute().body();

					logger.debug("回单上传接口返回信息：{}",receiptRes);

					//更新回单标记
					invoiceMapper.updateHyRecordReceiptFlag(hyInvoiceRecord.getId());
				}
			}else{
				logger.debug("单据未到回单状态：{}",listRes);
			}
		}
	}


	public void zjhyReceiptOnly(String entrustId){
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);

		//根据发货单号查询是否存在需要同步的单据
		HYInvoiceRecord hyInvoiceRecord = invoiceMapper.selectSuccessHyInvoiceByInvoiceId(entrust.getOrderno());
		//恒逸单据判断
		if(hyInvoiceRecord != null && hyInvoiceRecord.getOriginNo().contains("T")){
			logger.debug("浙江恒逸回单上传发货单号：{}",entrust.getOrderno());
			//登录
			String tokenUrl = "https://tms.hengyi.com/login/login";

			Map<String, Object> sendParams = new HashMap<>();
			sendParams.put("userCode", "990000902");
			sendParams.put("userPassword", "e1a42180caa49f5fabc51836a8bd197d");
			sendParams.put("verificationCode", "");

			String token = null;

			Map<String, List<String>> headers = HttpRequest.post(tokenUrl).body(JSON.toJSONString(sendParams)).execute().headers();
			List<String> cookieList = headers.get("Set-Cookie");
			for(String cookie : cookieList){
				if(cookie.startsWith("RequestToken")){
					token = cookie.split(";")[0].split("=")[1];
				}
			}

			//====================根据恒逸运单号查询列表信息，获取verion版本号====================
			Calendar cal = Calendar.getInstance();
			cal.setTime(new Date());
			cal.add(Calendar.MONTH,-1);

			Map<String, Object> listSendParams = new HashMap<>();
			List<Long> createTimeList = new ArrayList<>();
			createTimeList.add(cal.getTimeInMillis());
			cal.setTime(new Date());
			createTimeList.add(cal.getTimeInMillis());
			listSendParams.put("createTimeList",createTimeList);

			Map<String,Object> pageParamNewVO = new HashMap<>();
			pageParamNewVO.put("curPage",1);
			pageParamNewVO.put("pageSize",20);
			pageParamNewVO.put("sortDir","");
			pageParamNewVO.put("sortIndx","");
			listSendParams.put("pageParamNewVO",pageParamNewVO);
			listSendParams.put("mainStatus","400");

			List<Integer> approvalStatusList = new ArrayList<>();
			approvalStatusList.add(2);
			approvalStatusList.add(5);
			listSendParams.put("approvalStatusList",approvalStatusList);
			listSendParams.put("wayBillNo",hyInvoiceRecord.getOriginNo());

			String listUrl = "https://tms.hengyi.com/msapi3/E6-MS-TMS-BUSI-WEB/waybill/pageProductQueryByCondition";
			String listRes = HttpRequest.post(listUrl).header("Content-Type", "application/json;charset=UTF-8")
					.header("Token", token).body(JSON.toJSONString(listSendParams)).execute().body();

			JSONObject listResJson = JSON.parseObject(listRes);
			JSONArray jsonArray = listResJson.getJSONObject("result").getJSONArray("data");
			if(jsonArray.size() > 0){
				JSONObject jsonObject = jsonArray.getJSONObject(0);
				Long version = jsonObject.getLong("version");
				String id = jsonObject.getString("id");


				//====================获取提交前货源信息，版本号====================
				String beforeUrl = "https://tms.hengyi.com/msapi3/E6-MS-TMS-BUSI-WEB/waybillAction/getOrderBeforeReceipt";
				//请求参数
				sendParams = new HashMap<>();
				sendParams.put("corpId", 248);
				sendParams.put("id", id);
				sendParams.put("plantForm", 1);
				sendParams.put("transport", 2);
				sendParams.put("version", version);

				String beforeRes = HttpRequest.post(beforeUrl).header("Content-Type", "application/json;charset=UTF-8")
						.header("Token", token).body(JSON.toJSONString(sendParams)).execute().body();

				JSONObject beforeResJson = JSON.parseObject(beforeRes);
				version = beforeResJson.getJSONObject("result").getLong("version");

				//订单对象
				List<Map<String,Object>> orderList = new ArrayList<>();

				Boolean uploadErrorFlag = true;

				JSONArray orderListBefore = beforeResJson.getJSONObject("result").getJSONArray("orderList");
				for(int i = 0; i < orderListBefore.size(); i++){
					JSONObject order = orderListBefore.getJSONObject(i);

					Map<String,Object> orderMap = new HashMap<>();
					List<String> unloadingUrlList = new ArrayList<>();
					//上传回单附件
					List<SysUploadFile> sysUploadFiles = entrustWorkMapper.selectReceiptPicByEntrustId(entrust.getEntrustId());
					if(sysUploadFiles == null || sysUploadFiles.size() == 0){
						throw new BusinessException("未找到回单附件");
					}
					for(SysUploadFile sysUploadFile : sysUploadFiles){
						String filePath = "D:/"+sysUploadFile.getFilePath();
						String url = "https://tms.hengyi.com/msapi3/COMMON-MODULE-BASE-FILE-PROCESSOR-WEB/api/upload/fildUploadPublicByInputStream?customerId=e6yun3.0&businessTypeKey=waybillPlatform";

						Map<String, String> headersPost = new HashMap<>();
						headersPost.put("Accept", "*/*");
						headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
						headersPost.put("Connection", "keep-alive");
						headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryjfPDtu6h3wMxaApO");
						headersPost.put("Origin", "https://tms.hengyi.com");
						headersPost.put("Referer", "https://tms.hengyi.com/");
						headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
						headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
						headersPost.put("sec-ch-ua-mobile", "?0");
						headersPost.put("sec-ch-ua-platform", "\"Windows\"");
						headersPost.put("token", token);

						HttpRequest request = HttpRequest.post(url)
								.headerMap(headersPost, true)
								.form("files", new File(filePath));

						cn.hutool.http.HttpResponse execute = request.execute();

						String uploadRes = execute.body();
						logger.debug("文件上传结果：{}",uploadRes);
						JSONObject upload = JSONObject.parseObject(uploadRes);
						if(upload.getInteger("code") == 1){
							String fileUrl = upload.getJSONArray("result").getJSONObject(0).getString("fileUrl");
							unloadingUrlList.add(fileUrl);
						}else{
							uploadErrorFlag = false;
						}
					}

					orderMap.put("unloadingUrlList",unloadingUrlList);
					orderMap.put("orderId",order.getString("orderId"));
					orderMap.put("orderNo",order.getString("orderNo"));
					orderMap.put("customerName",order.getString("customerName"));
					orderMap.put("receivingAreaAddress",order.getString("receivingAreaAddress"));
					orderMap.put("unloadingUrlList",unloadingUrlList);
					orderList.add(orderMap);
				}

				//判断上传成继续
				if(uploadErrorFlag){
					String receiptUrl = "https://tms.hengyi.com/msapi3/E6-MS-TMS-BUSI-WEB/waybillAction/receipt";
					//请求参数
					sendParams = new HashMap<>();
					sendParams.put("corpId", 248);
					sendParams.put("id", id);
					sendParams.put("plantForm", 1);
					sendParams.put("transport", 2);
					sendParams.put("version", version);
					sendParams.put("orderList", orderList);


					String receiptRes = HttpRequest.post(receiptUrl).header("Content-Type", "application/json;charset=UTF-8")
							.header("Token", token).body(JSON.toJSONString(sendParams)).execute().body();

					logger.debug("回单上传接口返回信息：{}",receiptRes);

					//更新回单标记
					invoiceMapper.updateHyRecordReceiptFlag(hyInvoiceRecord.getId());
				}else{
					throw new BusinessException("上传附件异常");
				}
			}else{
				throw new BusinessException("单据未到回单状态");
			}
		}else{
			throw new BusinessException("未匹配到抓取单据");
		}
	}

	/**
	 * 提货作业
	 * @param entrustId
	 * @param actArriDate
	 * @param actLeaveDate
	 */
	@Async
	public void sunshinePick(String entrustId,Date actArriDate,Date actLeaveDate){
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);

		//保存信息
		SunshineBackRecord sunshineBackRecord = new SunshineBackRecord();
		sunshineBackRecord.setId(IdUtil.simpleUUID());
		sunshineBackRecord.setEntrustVbillno(entrust.getVbillno());
		sunshineBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
		sunshineBackRecord.setRegDate(new Date());
		sunshineBackRecord.setType(2);
		sunshineBackRecord.setCustOrderNo(entrust.getCustOrderno());

		//判断是否有拆单的情况
		List<Entrust> entrusts = entrustMapper.selectEntrustListByInvoiceId(entrust.getOrderno());
		if(entrusts != null && entrusts.size() == 1){
			//上传到货信息
			Invoice invoice = invoiceMapper.selectInvoiceById(entrust.getOrderno());
			//根据客户单号查询阳光数据
			String token = getSunshineToken(false);
			if(token != null){
				SimpleDateFormat sdfDay = new SimpleDateFormat("yy/MM/dd");
				Calendar cal = Calendar.getInstance();
				cal.setTime(new Date());
				cal.add(Calendar.MONTH,-3);
				String dayStr = sdfDay.format(cal.getTime());

				//请求列表接口
				String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/GetShipmentInfos?defsort=CREATED_DATE%20DESC";
				Map<String,Object> listParams = new HashMap<>();
				listParams.put("_search","true");
				listParams.put("rows","20");
				listParams.put("page","1");
				listParams.put("sord","asc");
				listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"eq\",\"data\":\""+invoice.getCustOrderno()+"\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"}]}");
				String listRes = HttpRequest.post(listUrl)
						.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
						.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
						.form(listParams).execute().body();
				JSONObject listJson = JSONObject.parseObject(listRes);
				JSONArray rows = listJson.getJSONArray("rows");
				if(rows.size() == 1){
					String gids = rows.getJSONObject(0).getString("id");

					JSONArray jsonArray = rows.getJSONObject(0).getJSONArray("cell");
					//SP单号
					String spNum = jsonArray.getString(2);
					//判断状态
					String status =jsonArray.getString(3);
					if("委托".equals(status)){
						//确认
						String acceptURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Tender/ShipmentTenderAccept";
						listParams = new HashMap<>();
						listParams.put("gids", gids);

						listRes = HttpRequest.post(acceptURL)
								.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
								.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
								.form(listParams).execute().body();

						if(listRes.contains("成功")){
							//确认
							String confirmURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/DeliveryConfirmation";

							listRes = HttpRequest.post(confirmURL)
									.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
									.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
									.form(listParams).execute().body();

							if(listRes.contains("成功")){
								sunshineBackRecord = sunshinePick(sunshineBackRecord,entrust,spNum,gids,actArriDate,actLeaveDate,token);
							}else{
								//保存异常信息
								sunshineBackRecord.setSuccessFlag(1);
								sunshineBackRecord.setBackMemo(listRes);
							}
						}else{
							//保存异常信息
							sunshineBackRecord.setSuccessFlag(1);
							sunshineBackRecord.setBackMemo(listRes);
						}
					}else if("接受".equals(status)){
						//确认
						String confirmURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/DeliveryConfirmation";
						listParams = new HashMap<>();
						listParams.put("gids", gids);

						listRes = HttpRequest.post(confirmURL)
								.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
								.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
								.form(listParams).execute().body();

						if(listRes.contains("成功")){
							sunshineBackRecord = sunshinePick(sunshineBackRecord,entrust,spNum,gids,actArriDate,actLeaveDate,token);
						}else{
							//保存异常信息
							sunshineBackRecord.setSuccessFlag(1);
							sunshineBackRecord.setBackMemo(listRes);
						}
					}else if("确认".equals(status)){
						sunshineBackRecord = sunshinePick(sunshineBackRecord,entrust,spNum,gids,actArriDate,actLeaveDate,token);
					}else{
						sunshineBackRecord.setSuccessFlag(1);
						sunshineBackRecord.setBackMemo("客户单号"+invoice.getCustOrderno()+"，单据状态为："+status+"，无法操作");
					}
				}else{
					//根据车号查询
					listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"eq\",\"data\":\""+invoice.getCustOrderno()+"\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"},{\"field\":\"EQUIPMENT_ID\",\"op\":\"eq\",\"data\":\""+entrust.getCarno()+"\"}]}");
					listRes = HttpRequest.post(listUrl)
							.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
							.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
							.form(listParams).execute().body();
					listJson = JSONObject.parseObject(listRes);
					rows = listJson.getJSONArray("rows");
					if(rows.size() == 1){
						String gids = rows.getJSONObject(0).getString("id");

						JSONArray jsonArray = rows.getJSONObject(0).getJSONArray("cell");
						//SP单号
						String spNum = jsonArray.getString(2);
						//判断状态
						String status =jsonArray.getString(3);
						if("委托".equals(status)){
							//确认
							String acceptURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Tender/ShipmentTenderAccept";
							listParams = new HashMap<>();
							listParams.put("gids", gids);

							listRes = HttpRequest.post(acceptURL)
									.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
									.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
									.form(listParams).execute().body();

							if(listRes.contains("成功")){
								//确认
								String confirmURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/DeliveryConfirmation";

								listRes = HttpRequest.post(confirmURL)
										.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
										.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
										.form(listParams).execute().body();

								if(listRes.contains("成功")){
									sunshineBackRecord = sunshinePick(sunshineBackRecord,entrust,spNum,gids,actArriDate,actLeaveDate,token);
								}else{
									//保存异常信息
									sunshineBackRecord.setSuccessFlag(1);
									sunshineBackRecord.setBackMemo(listRes);
								}
							}else{
								//保存异常信息
								sunshineBackRecord.setSuccessFlag(1);
								sunshineBackRecord.setBackMemo(listRes);
							}
						}else if("接受".equals(status)){
							//确认
							String confirmURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/DeliveryConfirmation";
							listParams = new HashMap<>();
							listParams.put("gids", gids);

							listRes = HttpRequest.post(confirmURL)
									.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
									.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
									.form(listParams).execute().body();

							if(listRes.contains("成功")){
								sunshineBackRecord = sunshinePick(sunshineBackRecord,entrust,spNum,gids,actArriDate,actLeaveDate,token);
							}else{
								//保存异常信息
								sunshineBackRecord.setSuccessFlag(1);
								sunshineBackRecord.setBackMemo(listRes);
							}
						}else if("确认".equals(status)){
							sunshineBackRecord = sunshinePick(sunshineBackRecord,entrust,spNum,gids,actArriDate,actLeaveDate,token);
						}else{
							sunshineBackRecord.setSuccessFlag(1);
							sunshineBackRecord.setBackMemo("客户单号"+invoice.getCustOrderno()+"，单据状态为："+status+"，无法操作");
						}
					}else{
						//保存异常信息
						sunshineBackRecord.setSuccessFlag(1);
						sunshineBackRecord.setBackMemo("客户单号"+invoice.getCustOrderno()+"车牌号"+entrust.getCarno()+"，在阳光系统查询到"+rows.size()+"单");
					}
				}
			}else{
				//保存异常信息
				sunshineBackRecord.setSuccessFlag(1);
				sunshineBackRecord.setBackMemo("登录失败");
			}

		}else{
			//保存异常信息
			sunshineBackRecord.setSuccessFlag(1);
			sunshineBackRecord.setBackMemo("发货单存在拆单情况，无法推送到货信息");
		}
		//保存回调信息
		entrustMapper.insertSunshineBackRecord(sunshineBackRecord);
	}


	@Async
	public void sunshineConfirm(Entrust entrust,Boolean sleepFlag){

		if(sleepFlag){
			try {
				Thread.sleep(5000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		}

		//保存信息
		SunshineBackRecord sunshineBackRecord = new SunshineBackRecord();
		sunshineBackRecord.setId(IdUtil.simpleUUID());
		sunshineBackRecord.setEntrustVbillno(entrust.getVbillno());
		sunshineBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
		sunshineBackRecord.setRegDate(new Date());
		sunshineBackRecord.setType(7);
		sunshineBackRecord.setCustOrderNo(entrust.getCustOrderno());

		//判断是否有拆单的情况
		List<Entrust> entrusts = entrustMapper.selectEntrustListByInvoiceId(entrust.getOrderno());
		if(entrusts != null && entrusts.size() == 1){
			//上传到货信息
			Invoice invoice = invoiceMapper.selectInvoiceById(entrust.getOrderno());
			//根据客户单号查询阳光数据
			String token = getSunshineToken(false);
			if(token != null){
				SimpleDateFormat sdfDay = new SimpleDateFormat("yy/MM/dd");
				Calendar cal = Calendar.getInstance();
				cal.setTime(new Date());
				cal.add(Calendar.MONTH,-3);
				String dayStr = sdfDay.format(cal.getTime());

				//请求列表接口
				String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/GetShipmentInfos?defsort=CREATED_DATE%20DESC";
				Map<String,Object> listParams = new HashMap<>();
				listParams.put("_search","true");
				listParams.put("rows","20");
				listParams.put("page","1");
				listParams.put("sord","asc");
				listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"eq\",\"data\":\""+invoice.getCustOrderno()+"\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"}]}");
				String listRes = HttpRequest.post(listUrl)
						.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
						.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
						.form(listParams).execute().body();
				JSONObject listJson = JSONObject.parseObject(listRes);
				JSONArray rows = listJson.getJSONArray("rows");
				if(rows.size() == 1){
					String gids = rows.getJSONObject(0).getString("id");

					JSONArray jsonArray = rows.getJSONObject(0).getJSONArray("cell");
					//SP单号
					String spNum = jsonArray.getString(2);
					//判断状态
					String status =jsonArray.getString(3);

					 if("接受".equals(status)){
						//确认
						String confirmURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/DeliveryConfirmation";
						listParams = new HashMap<>();
						listParams.put("gids", gids);

						listRes = HttpRequest.post(confirmURL)
								.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
								.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
								.form(listParams).execute().body();

						if(listRes.contains("成功")){
							sunshineBackRecord.setSuccessFlag(0);
							sunshineBackRecord.setBackMemo("单据确认成功");
						}else{
							//保存异常信息
							sunshineBackRecord.setSuccessFlag(1);
							sunshineBackRecord.setBackMemo(listRes);
						}
					}else{
						sunshineBackRecord.setSuccessFlag(1);
						sunshineBackRecord.setBackMemo("客户单号"+invoice.getCustOrderno()+"，单据状态为："+status+"，无法操作");
					}
				}else{
					//根据车号查询
					listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"eq\",\"data\":\""+invoice.getCustOrderno()+"\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"},{\"field\":\"EQUIPMENT_ID\",\"op\":\"eq\",\"data\":\""+entrust.getCarno()+"\"}]}");
					listRes = HttpRequest.post(listUrl)
							.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
							.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
							.form(listParams).execute().body();
					listJson = JSONObject.parseObject(listRes);
					rows = listJson.getJSONArray("rows");
					if(rows.size() == 1){
						String gids = rows.getJSONObject(0).getString("id");

						JSONArray jsonArray = rows.getJSONObject(0).getJSONArray("cell");
						//SP单号
						String spNum = jsonArray.getString(2);
						//判断状态
						String status =jsonArray.getString(3);
						if("接受".equals(status)){
							//确认
							String confirmURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/DeliveryConfirmation";
							listParams = new HashMap<>();
							listParams.put("gids", gids);

							listRes = HttpRequest.post(confirmURL)
									.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
									.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
									.form(listParams).execute().body();

							if(listRes.contains("成功")){
								sunshineBackRecord.setSuccessFlag(0);
								sunshineBackRecord.setBackMemo("单据确认成功");
							}else{
								//保存异常信息
								sunshineBackRecord.setSuccessFlag(1);
								sunshineBackRecord.setBackMemo(listRes);
							}
						}else{
							sunshineBackRecord.setSuccessFlag(1);
							sunshineBackRecord.setBackMemo("客户单号"+invoice.getCustOrderno()+"，单据状态为："+status+"，无法操作");
						}
					}else{
						//保存异常信息
						sunshineBackRecord.setSuccessFlag(1);
						sunshineBackRecord.setBackMemo("客户单号"+invoice.getCustOrderno()+"车牌号"+entrust.getCarno()+"，在阳光系统查询到"+rows.size()+"单");
					}
				}
			}else{
				//保存异常信息
				sunshineBackRecord.setSuccessFlag(1);
				sunshineBackRecord.setBackMemo("登录失败");
			}

		}else{
			//保存异常信息
			sunshineBackRecord.setSuccessFlag(1);
			sunshineBackRecord.setBackMemo("发货单存在拆单情况，无法确认");
		}
		//保存回调信息
		entrustMapper.insertSunshineBackRecord(sunshineBackRecord);
	}


	/**
	 * 派车
	 */
	@Async
	public void sunshineCarDriverUpload(Entrust entrust,Boolean sleepFlag){

		if(sleepFlag){
			try {
				Thread.sleep(5000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		}

		//保存信息
		SunshineBackRecord sunshineBackRecord = new SunshineBackRecord();
		sunshineBackRecord.setId(IdUtil.simpleUUID());
		sunshineBackRecord.setEntrustVbillno(entrust.getVbillno());
		sunshineBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
		sunshineBackRecord.setRegDate(new Date());
		sunshineBackRecord.setType(5);
		sunshineBackRecord.setCustOrderNo(entrust.getCustOrderno());

		//判断是否有拆单的情况
		List<Entrust> entrusts = entrustMapper.selectEntrustListByInvoiceId(entrust.getOrderno());
		if(entrusts != null && entrusts.size() == 1){
			//上传到货信息
			Invoice invoice = invoiceMapper.selectInvoiceById(entrust.getOrderno());
			//根据客户单号查询阳光数据
			String token = getSunshineToken(false);
			if(token != null){
				SimpleDateFormat sdfDay = new SimpleDateFormat("yy/MM/dd");
				Calendar cal = Calendar.getInstance();
				cal.setTime(new Date());
				cal.add(Calendar.MONTH,-3);
				String dayStr = sdfDay.format(cal.getTime());

				//请求列表接口
				String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/GetShipmentInfos?defsort=CREATED_DATE%20DESC";
				Map<String,Object> listParams = new HashMap<>();
				listParams.put("_search","true");
				listParams.put("rows","20");
				listParams.put("page","1");
				listParams.put("sord","asc");
				listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"eq\",\"data\":\""+invoice.getCustOrderno()+"\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"}]}");
				String listRes = HttpRequest.post(listUrl)
						.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
						.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
						.form(listParams).execute().body();
				JSONObject listJson = JSONObject.parseObject(listRes);
				JSONArray rows = listJson.getJSONArray("rows");
				if(rows.size() == 1){
					String gids = rows.getJSONObject(0).getString("id");

					JSONArray jsonArray = rows.getJSONObject(0).getJSONArray("cell");
					//SP单号
					String spNum = jsonArray.getString(2);
					//判断状态
					String status =jsonArray.getString(3);

					//上传车辆行驶证
					sunshineUploadCarPic(entrust,status,gids,spNum,token);

					if("委托".equals(status)){
						//接收
						String acceptURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Tender/ShipmentTenderAccept";
						listParams = new HashMap<>();
						listParams.put("gids", gids);

						listRes = HttpRequest.post(acceptURL)
								.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
								.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
								.form(listParams).execute().body();

						if(listRes.contains("成功")){
						    //上传车辆司机信息
                            sunshineBackRecord = sunshineCarDriver(sunshineBackRecord,entrust,spNum,token);

                            if(sunshineBackRecord.getSuccessFlag() == 0){
								token = getSunshineToken(true);
								//确认
								String confirmURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/DeliveryConfirmation";

								listRes = HttpRequest.post(confirmURL)
										.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
										.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
										.form(listParams).execute().body();

								if(listRes.contains("成功")){
									sunshineBackRecord.setSuccessFlag(0);
									sunshineBackRecord.setBackMemo("车辆司机已上传(系统操作单据接收和确认)");
								}else{
									//保存异常信息
									sunshineBackRecord.setSuccessFlag(1);
									sunshineBackRecord.setBackMemo("接收成功、车辆司机已上传、确认失败："+listRes);
								}
							}
						}else{
							//保存异常信息
							sunshineBackRecord.setSuccessFlag(1);
							sunshineBackRecord.setBackMemo(listRes);
						}
					}else if("接受".equals(status)){
						sunshineBackRecord = sunshineCarDriver(sunshineBackRecord,entrust,spNum,token);
						if(sunshineBackRecord.getSuccessFlag() == 0){
							token = getSunshineToken(true);
							//确认
							String confirmURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/DeliveryConfirmation";
							listParams = new HashMap<>();
							listParams.put("gids", gids);

							listRes = HttpRequest.post(confirmURL)
									.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
									.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
									.form(listParams).execute().body();

							if(listRes.contains("成功")){
								sunshineBackRecord.setSuccessFlag(0);
								sunshineBackRecord.setBackMemo("车辆司机已上传(系统操作单据确认)");
							}else{
								//保存异常信息
								sunshineBackRecord.setSuccessFlag(1);
								sunshineBackRecord.setBackMemo("车辆司机已上传、确认失败："+listRes);
							}
						}
					}else if("确认".equals(status)){
						sunshineBackRecord = sunshineCarDriver(sunshineBackRecord,entrust,spNum,token);
					}else{
						sunshineBackRecord.setSuccessFlag(1);
						sunshineBackRecord.setBackMemo("客户单号"+invoice.getCustOrderno()+"，单据状态为："+status+"，无法操作");
					}
				}else{
					//根据车号查询
					listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"eq\",\"data\":\""+invoice.getCustOrderno()+"\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"},{\"field\":\"EQUIPMENT_ID\",\"op\":\"eq\",\"data\":\""+entrust.getCarno()+"\"}]}");
					listRes = HttpRequest.post(listUrl)
							.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
							.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
							.form(listParams).execute().body();
					listJson = JSONObject.parseObject(listRes);
					rows = listJson.getJSONArray("rows");
					if(rows.size() == 1){
						String gids = rows.getJSONObject(0).getString("id");

						JSONArray jsonArray = rows.getJSONObject(0).getJSONArray("cell");
						//SP单号
						String spNum = jsonArray.getString(2);
						//判断状态
						String status =jsonArray.getString(3);
						if("委托".equals(status)){
							//确认
							String acceptURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Tender/ShipmentTenderAccept";
							listParams = new HashMap<>();
							listParams.put("gids", gids);

							listRes = HttpRequest.post(acceptURL)
									.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
									.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
									.form(listParams).execute().body();

							if(listRes.contains("成功")){
								//车辆司机上传
								sunshineBackRecord = sunshineCarDriver(sunshineBackRecord,entrust,spNum,token);

								if(sunshineBackRecord.getSuccessFlag() == 0){
									token = getSunshineToken(true);
									//确认
									String confirmURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/DeliveryConfirmation";

									listRes = HttpRequest.post(confirmURL)
											.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
											.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
											.form(listParams).execute().body();

									if(listRes.contains("成功")){
										sunshineBackRecord.setSuccessFlag(0);
										sunshineBackRecord.setBackMemo("车辆司机已上传(系统操作单据接收和确认)");
									}else{
										//保存异常信息
										sunshineBackRecord.setSuccessFlag(1);
										sunshineBackRecord.setBackMemo("接收成功、车辆司机已上传、确认失败："+listRes);
									}
								}
							}else{
								//保存异常信息
								sunshineBackRecord.setSuccessFlag(1);
								sunshineBackRecord.setBackMemo(listRes);
							}
						}else if("接受".equals(status)){
							sunshineBackRecord = sunshineCarDriver(sunshineBackRecord,entrust,spNum,token);
							if(sunshineBackRecord.getSuccessFlag() == 0){
								token = getSunshineToken(true);
								//确认
								String confirmURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/DeliveryConfirmation";
								listParams = new HashMap<>();
								listParams.put("gids", gids);

								listRes = HttpRequest.post(confirmURL)
										.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
										.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
										.form(listParams).execute().body();

								if(listRes.contains("成功")){
									sunshineBackRecord.setSuccessFlag(0);
									sunshineBackRecord.setBackMemo("车辆司机已上传(系统操作单据确认)");
								}else{
									//保存异常信息
									sunshineBackRecord.setSuccessFlag(1);
									sunshineBackRecord.setBackMemo("车辆司机已上传、确认失败："+listRes);
								}
							}
						}else if("确认".equals(status)){
							sunshineBackRecord = sunshineCarDriver(sunshineBackRecord,entrust,spNum,token);
						}else{
							sunshineBackRecord.setSuccessFlag(1);
							sunshineBackRecord.setBackMemo("客户单号"+invoice.getCustOrderno()+"，单据状态为："+status+"，无法操作");
						}
					}else{
						//保存异常信息
						sunshineBackRecord.setSuccessFlag(1);
						sunshineBackRecord.setBackMemo("客户单号"+invoice.getCustOrderno()+"车牌号"+entrust.getCarno()+"，在阳光系统查询到"+rows.size()+"单");
					}
				}
			}else{
				//保存异常信息
				sunshineBackRecord.setSuccessFlag(1);
				sunshineBackRecord.setBackMemo("登录失败");
			}

		}else{
			//保存异常信息
			sunshineBackRecord.setSuccessFlag(1);
			sunshineBackRecord.setBackMemo("发货单存在拆单情况，无法推送派车信息");
		}
		//保存回调信息
		entrustMapper.insertSunshineBackRecord(sunshineBackRecord);
	}


	/**
	 * 仅提货操作
	 */
	@Async
	public void sunshinePickOnly(String entrustId,Date actArriDate,Date actLeaveDate){
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);

		//保存信息
		SunshineBackRecord sunshineBackRecord = new SunshineBackRecord();
		sunshineBackRecord.setId(IdUtil.simpleUUID());
		sunshineBackRecord.setEntrustVbillno(entrust.getVbillno());
		sunshineBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
		sunshineBackRecord.setRegDate(new Date());
		sunshineBackRecord.setType(2);
		sunshineBackRecord.setCustOrderNo(entrust.getCustOrderno());

		//判断是否有拆单的情况
		List<Entrust> entrusts = entrustMapper.selectEntrustListByInvoiceId(entrust.getOrderno());
		if(entrusts != null && entrusts.size() == 1){
			//上传到货信息
			Invoice invoice = invoiceMapper.selectInvoiceById(entrust.getOrderno());
			//根据客户单号查询阳光数据
			String token = getSunshineToken(false);
			if(token != null){
				SimpleDateFormat sdfDay = new SimpleDateFormat("yy/MM/dd");
				Calendar cal = Calendar.getInstance();
				cal.setTime(new Date());
				cal.add(Calendar.MONTH,-3);
				String dayStr = sdfDay.format(cal.getTime());

				//请求列表接口
				String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/GetShipmentInfos?defsort=CREATED_DATE%20DESC";
				Map<String,Object> listParams = new HashMap<>();
				listParams.put("_search","true");
				listParams.put("rows","20");
				listParams.put("page","1");
				listParams.put("sord","asc");
				listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"eq\",\"data\":\""+invoice.getCustOrderno()+"\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"}]}");
				String listRes = HttpRequest.post(listUrl)
						.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
						.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
						.form(listParams).execute().body();
				JSONObject listJson = JSONObject.parseObject(listRes);
				JSONArray rows = listJson.getJSONArray("rows");
				if(rows.size() == 1){
					String gids = rows.getJSONObject(0).getString("id");

					JSONArray jsonArray = rows.getJSONObject(0).getJSONArray("cell");
					//SP单号
					String spNum = jsonArray.getString(2);
					//判断状态
					String status =jsonArray.getString(3);
					if("确认".equals(status)){
						//提货
						sdfDay = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						String pickURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/AllPickupOrArrival";
						Map<String, Object> arriParams = new HashMap<>();
						arriParams.put("gids", gids);
						arriParams.put("operation", "pickup");
						arriParams.put("arrivalDate", sdfDay.format(actArriDate));
						arriParams.put("leaveDate", sdfDay.format(actLeaveDate));

						String pickRes = HttpRequest.post(pickURL)
								.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
								.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
								.form(arriParams).execute().body();
						if(pickRes.contains("成功")){
							sunshineBackRecord.setSuccessFlag(0);
						}else{
							sunshineBackRecord.setSuccessFlag(1);
						}
						sunshineBackRecord.setBackMemo(pickRes);
					}else{
						sunshineBackRecord.setSuccessFlag(1);
						sunshineBackRecord.setBackMemo("客户单号"+invoice.getCustOrderno()+"，单据状态为："+status+"，无法操作");
					}
				}else{
					//根据车号查询
					listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"eq\",\"data\":\""+invoice.getCustOrderno()+"\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"},{\"field\":\"EQUIPMENT_ID\",\"op\":\"eq\",\"data\":\""+entrust.getCarno()+"\"}]}");
					listRes = HttpRequest.post(listUrl)
							.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
							.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
							.form(listParams).execute().body();
					listJson = JSONObject.parseObject(listRes);
					rows = listJson.getJSONArray("rows");
					if(rows.size() == 1){
						String gids = rows.getJSONObject(0).getString("id");

						JSONArray jsonArray = rows.getJSONObject(0).getJSONArray("cell");
						//SP单号
						String spNum = jsonArray.getString(2);
						//判断状态
						String status =jsonArray.getString(3);
						if("确认".equals(status)){
							//提货
							sdfDay = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							String pickURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/AllPickupOrArrival";
							Map<String, Object> arriParams = new HashMap<>();
							arriParams.put("gids", gids);
							arriParams.put("operation", "pickup");
							arriParams.put("arrivalDate", sdfDay.format(actArriDate));
							arriParams.put("leaveDate", sdfDay.format(actLeaveDate));

							String pickRes = HttpRequest.post(pickURL)
									.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
									.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
									.form(arriParams).execute().body();
							if(pickRes.contains("成功")){
								sunshineBackRecord.setSuccessFlag(0);
							}else{
								sunshineBackRecord.setSuccessFlag(1);
							}
							sunshineBackRecord.setBackMemo(pickRes);
						}else{
							sunshineBackRecord.setSuccessFlag(1);
							sunshineBackRecord.setBackMemo("客户单号"+invoice.getCustOrderno()+"，单据状态为："+status+"，无法操作");
						}
					}else{
						//保存异常信息
						sunshineBackRecord.setSuccessFlag(1);
						sunshineBackRecord.setBackMemo("客户单号"+invoice.getCustOrderno()+"车牌号"+entrust.getCarno()+"，在阳光系统查询到"+rows.size()+"单");
					}
				}
			}else{
				//保存异常信息
				sunshineBackRecord.setSuccessFlag(1);
				sunshineBackRecord.setBackMemo("登录失败");
			}

		}else{
			//保存异常信息
			sunshineBackRecord.setSuccessFlag(1);
			sunshineBackRecord.setBackMemo("发货单存在拆单情况，无法推送到货信息");
		}
		//保存回调信息
		entrustMapper.insertSunshineBackRecord(sunshineBackRecord);
	}

	//阳光提货
	public SunshineBackRecord sunshinePick(SunshineBackRecord sunshineBackRecord,Entrust entrust,String spNum,String gids,
										   Date actArriDate,Date actLeaveDate,String token){
		//上传车辆司机
		String carNo = entrust.getCarno();
		String carlen = entrust.getCarLen()+"M";
		String driverName = entrust.getDriverName();
		String driverMobile = entrust.getDriverMobile();

		//零担单独设置车辆司机
		if("1".equals(entrust.getTransCode())){
			carNo = "苏F3016X";
			carlen = "自动化设备类型";
			driverName = "客服";
			driverMobile = "15062739966";
		}

		if(StringUtils.isBlank("carNo") || StringUtils.isBlank("carlen") || StringUtils.isBlank("driverName") || StringUtils.isBlank("driverMobile")){
			//保存异常信息
			sunshineBackRecord.setSuccessFlag(1);
			sunshineBackRecord.setBackMemo("车辆或者司机信息为空");
			return sunshineBackRecord;
		}

		String excelPath = "D:/static/sunshineCarDriver.xls";

		//修改excel
		FileInputStream fileInputStream = null;
		HSSFWorkbook workbook = null;
		FileOutputStream fileOutputStream = null;
		try{
			fileInputStream = new FileInputStream(excelPath);
			workbook = new HSSFWorkbook(fileInputStream);
            Sheet sheet = workbook.getSheetAt(0);  // 获取第一个工作表
            Row row = sheet.getRow(1);  // 第二行，因为行索引从 0 开始
            if (row!= null) {
				// 运单ID
				row.getCell(0).setCellValue(spNum);
				row.getCell(1).setCellValue(carNo);
				row.getCell(5).setCellValue(carlen);
				row.getCell(8).setCellValue(driverName);
				row.getCell(20).setCellValue(driverMobile);
            }
            // 保存修改后的工作簿
			fileOutputStream = new FileOutputStream(excelPath);
            workbook.write(fileOutputStream);
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
			try {
				fileInputStream.close();
				fileOutputStream.close();
				workbook.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}


		String uploadExcelURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/SaveImportData?businessAction=ShipmentEquipmentImport&typeName=SCM.TMS7.DTO.Shipment.ShipmentEquipmentImportDTO,SCM.Contract.TMS7";

		Map<String, String> headersPost = new HashMap<>();
		headersPost.put("Accept", "*/*");
		headersPost.put("Accept-Encoding", "gzip, deflate, br, zstd");
		headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
		headersPost.put("Connection", "keep-alive");
		headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryjfPDtu6h3wMxaApO");
		headersPost.put("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw8mwTIzOf6DW/jKZclMUUqI%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXjx9j77poCZ-uRiJPNoxfSsrwMXk2WI3YaoyQi9UlclShGHP610m-Q8-yiYyLUsp6vb84VXFwx0r5cUg8xFcxLspGhleEECbsP0yPKWihJQIgocSctuFXh7zImehtX_-8GlVuGgfZHyQ8sQuUiFwo2znKT6hLiSGmA38OCoRh-0HbTvEuh0MTd0fIPlrSNOe70g; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; Lang=zh-cn; AttachmentWidgetLayout=grid; SSID="+ token +"; PowerTmsCookie=TmsUserID%3DOTQ61QsLxrz5YWMd6MFnhNgdGt6iW%2Bfx%2BkHkHRiqELw%3D");
		headersPost.put("Origin", "https://tms.sungrowpower.com");
		headersPost.put("Referer", "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/Index");
		headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
		headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
		headersPost.put("sec-ch-ua-mobile", "?0");
		headersPost.put("sec-ch-ua-platform", "\"Windows\"");

		//上传回单附件
		File file = new File(excelPath);
		HttpRequest request = HttpRequest.post(uploadExcelURL)
				.headerMap(headersPost, true)
				.form("files", file);
		HttpResponse response = request.execute();
		int statusCode = response.getStatus();
		if (statusCode == 301 || statusCode == 302) {
			String location = response.header("Location");
			if (StrUtil.isNotBlank(location)) {
				//重定向请求
				HttpRequest redirectRequest = HttpRequest.get("https://tms.sungrowpower.com"+location);
				HttpResponse redirectResponse = redirectRequest.execute();
				System.out.println("");
			}
		}

		token = getSunshineToken(true);

		//提货
		SimpleDateFormat sdfDay = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String pickURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/AllPickupOrArrival";
		Map<String, Object> arriParams = new HashMap<>();
		arriParams.put("gids", gids);
		arriParams.put("operation", "pickup");
		arriParams.put("arrivalDate", sdfDay.format(actArriDate));
		arriParams.put("leaveDate", sdfDay.format(actLeaveDate));

		String pickRes = HttpRequest.post(pickURL)
				.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
				.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
				.form(arriParams).execute().body();
		if(pickRes.contains("成功")){
			sunshineBackRecord.setSuccessFlag(0);
		}else{
			sunshineBackRecord.setSuccessFlag(1);
		}
		sunshineBackRecord.setBackMemo(pickRes);



		return sunshineBackRecord;
	}




	//阳光提货
	public SunshineBackRecord sunshineCarDriver(SunshineBackRecord sunshineBackRecord,Entrust entrust,String spNum,String token){
		//上传车辆司机
		String carNo = entrust.getCarno();
		String carlen = entrust.getCarLen()+"M";
		String driverName = entrust.getDriverName();
		String driverMobile = entrust.getDriverMobile();

		//零担单独设置车辆司机
		if("1".equals(entrust.getTransCode())){
			carNo = "苏F3016X";
			carlen = "自动化设备类型";
			driverName = "客服";
			driverMobile = "15062739966";
		}

		if(StringUtils.isBlank("carNo") || StringUtils.isBlank("carlen") || StringUtils.isBlank("driverName") || StringUtils.isBlank("driverMobile")){
			//保存异常信息
			sunshineBackRecord.setSuccessFlag(1);
			sunshineBackRecord.setBackMemo("车辆或者司机信息为空");
			return sunshineBackRecord;
		}

		String excelPath = "D:/static/sunshineCarDriver.xls";

		//修改excel
		FileInputStream fileInputStream = null;
		HSSFWorkbook workbook = null;
		FileOutputStream fileOutputStream = null;
		try{
			fileInputStream = new FileInputStream(excelPath);
			workbook = new HSSFWorkbook(fileInputStream);
			Sheet sheet = workbook.getSheetAt(0);  // 获取第一个工作表
			Row row = sheet.getRow(1);  // 第二行，因为行索引从 0 开始
			if (row!= null) {
				// 运单ID
				row.getCell(0).setCellValue(spNum);
				row.getCell(1).setCellValue(carNo);
				row.getCell(5).setCellValue(carlen);
				row.getCell(8).setCellValue(driverName);
				row.getCell(20).setCellValue(driverMobile);
			}
			// 保存修改后的工作簿
			fileOutputStream = new FileOutputStream(excelPath);
			workbook.write(fileOutputStream);
		} catch (IOException e) {
			e.printStackTrace();
		}finally {
			try {
				fileInputStream.close();
				fileOutputStream.close();
				workbook.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

		token = getSunshineToken(true);

		String uploadExcelURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/SaveImportData?businessAction=ShipmentEquipmentImport&typeName=SCM.TMS7.DTO.Shipment.ShipmentEquipmentImportDTO,SCM.Contract.TMS7";

		Map<String, String> headersPost = new HashMap<>();
		headersPost.put("Accept", "*/*");
		headersPost.put("Accept-Encoding", "gzip, deflate, br, zstd");
		headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
		headersPost.put("Connection", "keep-alive");
		headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryjfPDtu6h3wMxaApO");
		headersPost.put("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw8mwTIzOf6DW/jKZclMUUqI%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXjx9j77poCZ-uRiJPNoxfSsrwMXk2WI3YaoyQi9UlclShGHP610m-Q8-yiYyLUsp6vb84VXFwx0r5cUg8xFcxLspGhleEECbsP0yPKWihJQIgocSctuFXh7zImehtX_-8GlVuGgfZHyQ8sQuUiFwo2znKT6hLiSGmA38OCoRh-0HbTvEuh0MTd0fIPlrSNOe70g; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; Lang=zh-cn; AttachmentWidgetLayout=grid; SSID="+ token +"; PowerTmsCookie=TmsUserID%3DOTQ61QsLxrz5YWMd6MFnhNgdGt6iW%2Bfx%2BkHkHRiqELw%3D");
		headersPost.put("Origin", "https://tms.sungrowpower.com");
		headersPost.put("Referer", "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/Index");
		headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
		headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
		headersPost.put("sec-ch-ua-mobile", "?0");
		headersPost.put("sec-ch-ua-platform", "\"Windows\"");

		//上传回单附件
		File file = new File(excelPath);
		HttpRequest request = HttpRequest.post(uploadExcelURL)
				.headerMap(headersPost, true)
				.form("files", file);
		HttpResponse response = request.execute();
		int statusCode = response.getStatus();
		if (statusCode == 301 || statusCode == 302) {
			String location = response.header("Location");
			if (StrUtil.isNotBlank(location)) {
				//重定向请求
				HttpRequest redirectRequest = HttpRequest.get("https://tms.sungrowpower.com"+location);
				HttpResponse redirectResponse = redirectRequest.execute();
				sunshineBackRecord.setSuccessFlag(0);
				sunshineBackRecord.setBackMemo("车辆司机已上传");
			}else{
				sunshineBackRecord.setSuccessFlag(1);
				sunshineBackRecord.setBackMemo("车辆司机上传失败");
			}
		}else{
			sunshineBackRecord.setSuccessFlag(1);
			sunshineBackRecord.setBackMemo("车辆司机上传失败");
		}

		return sunshineBackRecord;
	}

	/**
	 *
	 * 阳光电源，到货、回单系统自动推送规则
	 * 1、不能通过阳光系统内置ID关联，会存在线下改单问题，改为根据客户单号关联！
	 * 2、TMS客户单号与阳光客户单号一致，且1：1，正常推送！
	 * 3、TMS客户单号与阳光客户单号一致，N：N，暂不推送，记录分析，后续完善！
	 * 4、TMS到货操作，推送阳光系统到货，到货时间与TMS系统一致！
	 * 5、TMS回单确认操作，推送阳光系统回单上传、并签收，签收日期与TMS到货时间一致；
	 * @param entrustId
	 */
	@Async
	public void sunshineArrival(String entrustId,Date actArriDate,Date actLeaveDate){
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);
		//保存信息
		SunshineBackRecord sunshineBackRecord = new SunshineBackRecord();
		sunshineBackRecord.setId(IdUtil.simpleUUID());
		sunshineBackRecord.setEntrustVbillno(entrust.getVbillno());
		sunshineBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
		sunshineBackRecord.setRegDate(new Date());
		sunshineBackRecord.setType(0);
		sunshineBackRecord.setCustOrderNo(entrust.getCustOrderno());

		//判断是否有拆单的情况
		List<Entrust> entrusts = entrustMapper.selectEntrustListByInvoiceId(entrust.getOrderno());
		if(entrusts != null && entrusts.size() == 1){
			//上传到货信息
			Invoice invoice = invoiceMapper.selectInvoiceById(entrust.getOrderno());
			//根据客户单号查询阳光数据
			String token = getSunshineToken(false);
			if(token != null){
				SimpleDateFormat sdfDay = new SimpleDateFormat("yy/MM/dd");
				Calendar cal = Calendar.getInstance();
				cal.setTime(new Date());
				cal.add(Calendar.MONTH,-3);
				String dayStr = sdfDay.format(cal.getTime());

				//请求列表接口
				String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/GetShipmentInfos?defsort=CREATED_DATE%20DESC";
				Map<String,Object> listParams = new HashMap<>();
				listParams.put("_search","true");
				listParams.put("rows","20");
				listParams.put("page","1");
				listParams.put("sord","asc");
				listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"eq\",\"data\":\""+invoice.getCustOrderno()+"\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"}]}");
				String listRes = HttpRequest.post(listUrl)
						.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
						.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
						.form(listParams).execute().body();
				JSONObject listJson = JSONObject.parseObject(listRes);
				JSONArray rows = listJson.getJSONArray("rows");
				if(rows.size() == 1){
					sdfDay = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

					String gids = rows.getJSONObject(0).getString("id");
					//请求到货
					String arriURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/AllPickupOrArrival";
					Map<String, Object> arriParams = new HashMap<>();
					arriParams.put("gids", gids);
					arriParams.put("operation", "arrival");
					arriParams.put("arrivalDate", sdfDay.format(actArriDate));
					arriParams.put("leaveDate", sdfDay.format(actLeaveDate));

					String arriRes = HttpRequest.post(arriURL)
							.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
							.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
							.form(arriParams).execute().body();
					if(arriRes.contains("成功")){
						sunshineBackRecord.setSuccessFlag(0);
					}else{
						sunshineBackRecord.setSuccessFlag(1);
					}
					sunshineBackRecord.setBackMemo(arriRes);
				}else{
					//根据车号查询
					listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"eq\",\"data\":\""+invoice.getCustOrderno()+"\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"},{\"field\":\"EQUIPMENT_ID\",\"op\":\"eq\",\"data\":\""+entrust.getCarno()+"\"}]}");
					listRes = HttpRequest.post(listUrl)
							.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
							.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
							.form(listParams).execute().body();
					listJson = JSONObject.parseObject(listRes);
					rows = listJson.getJSONArray("rows");
					if(rows.size() == 1){
						sdfDay = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

						String gids = rows.getJSONObject(0).getString("id");
						//请求到货
						String arriURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/AllPickupOrArrival";
						Map<String, Object> arriParams = new HashMap<>();
						arriParams.put("gids", gids);
						arriParams.put("operation", "arrival");
						arriParams.put("arrivalDate", sdfDay.format(actArriDate));
						arriParams.put("leaveDate", sdfDay.format(actLeaveDate));

						String arriRes = HttpRequest.post(arriURL)
								.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
								.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
								.form(arriParams).execute().body();
						if(arriRes.contains("成功")){
							sunshineBackRecord.setSuccessFlag(0);
						}else{
							sunshineBackRecord.setSuccessFlag(1);
						}
						sunshineBackRecord.setBackMemo(arriRes);
					}else{
						//保存异常信息
						sunshineBackRecord.setSuccessFlag(1);
						sunshineBackRecord.setBackMemo("客户单号"+invoice.getCustOrderno()+"车牌号"+entrust.getCarno()+"，在阳光系统查询到"+rows.size()+"单");
					}
				}
			}else{
				//保存异常信息
				sunshineBackRecord.setSuccessFlag(1);
				sunshineBackRecord.setBackMemo("登录失败");
			}
		}else{
			//保存异常信息
			sunshineBackRecord.setSuccessFlag(1);
			sunshineBackRecord.setBackMemo("发货单存在拆单情况，无法推送到货信息");
		}
		//保存回调信息
		entrustMapper.insertSunshineBackRecord(sunshineBackRecord);
	}

	/**
	 * 阳光电源回单
	 * @param entrustId
	 */
	@Async
	public void ygdyReceipt(String entrustId,Boolean sleepFlag) {
		if(sleepFlag){
			try {
				Thread.sleep(10000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		}
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);
		//保存信息
		SunshineBackRecord sunshineBackRecord = new SunshineBackRecord();
		sunshineBackRecord.setId(IdUtil.simpleUUID());
		sunshineBackRecord.setEntrustVbillno(entrust.getVbillno());
		sunshineBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
		sunshineBackRecord.setRegDate(new Date());
		sunshineBackRecord.setType(1);
		sunshineBackRecord.setCustOrderNo(entrust.getCustOrderno());

		//判断是否有拆单的情况
		List<Entrust> entrusts = entrustMapper.selectEntrustListByInvoiceId(entrust.getOrderno());
		if(entrusts != null && entrusts.size() == 1){
			//上传到货信息
			Invoice invoice = invoiceMapper.selectInvoiceById(entrust.getOrderno());
			//根据客户单号查询阳光数据
			String token = getSunshineToken(false);
			if(token != null){
				//查询回单ID
				SimpleDateFormat sdfDay = new SimpleDateFormat("yy/MM/dd");
				Calendar cal = Calendar.getInstance();
				cal.setTime(new Date());
				cal.add(Calendar.MONTH, -3);
				String dayStr = sdfDay.format(cal.getTime());
				//请求列表接口
				String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/GetPods?defsort=CREATED_DATE%20DESC";
				Map<String, Object> listParams = new HashMap<>();
				listParams.put("_search", "true");
				listParams.put("rows", "20");
				listParams.put("page", "1");
				listParams.put("sord", "asc");
				listParams.put("filters", "{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"eq\",\"data\":\""+invoice.getCustOrderno()+"\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\"" + dayStr + " 00:00~*\"}]}");
				String listRes = HttpRequest.post(listUrl)
						.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
						.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
						.form(listParams).execute().body();
				JSONObject listJson = JSONObject.parseObject(listRes);
				JSONArray rows = listJson.getJSONArray("rows");
				if(rows.size() == 1){
					String relatedkey = rows.getJSONObject(0).getString("id");
					String relatedID = rows.getJSONObject(0).getJSONArray("cell").getString(1);

					String picUploadURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/PartialUploadAttachment?relatedkey=" + relatedkey + "&relatedID=" + relatedID + "&relatedtype=40&domainName=SALES";

					Map<String, String> headersPost = new HashMap<>();
					headersPost.put("Accept", "*/*");
					headersPost.put("Accept-Encoding", "gzip, deflate, br, zstd");
					headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
					headersPost.put("Connection", "keep-alive");
					headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryjfPDtu6h3wMxaApO");
					headersPost.put("Cookie", "cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; Lang=zh-cn; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DFTvM9f4X5v89fLttSlA3OQ%3D%3D");
					headersPost.put("Origin", "https://tms.sungrowpower.com");
					headersPost.put("Referer", "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/Edit?guid=SALES.POD060308");
					headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
					headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
					headersPost.put("sec-ch-ua-mobile", "?0");
					headersPost.put("sec-ch-ua-platform", "\"Windows\"");

					//上传回单附件
					List<SysUploadFile> sysUploadFiles = entrustWorkMapper.selectReceiptPicByEntrustId(entrust.getEntrustId());
					for(SysUploadFile sysUploadFile : sysUploadFiles) {
						String filePath = "D:/" + sysUploadFile.getFilePath();

						HttpRequest request = HttpRequest.post(picUploadURL)
								.headerMap(headersPost, true)
								.form("files", new File(filePath));

						cn.hutool.http.HttpResponse execute = request.execute();
						String uploadRes = execute.body();
						logger.debug("阳光电源回单上传结果：{}",uploadRes);
					}

					//签收单据
					sdfDay = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

					String receiptUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/PodListSigned";
					listParams = new HashMap<>();
					listParams.put("signed_by","南通吉华物流有限公司");
					if(entrust.getActArriDate() == null){
						listParams.put("signed_date",sdfDay.format(new Date()));
					}else{
						listParams.put("signed_date",sdfDay.format(entrust.getActArriDate()));
					}

					listParams.put("signed_status",10);
					listParams.put("guids",relatedkey);

					listRes = HttpRequest.post(receiptUrl)
							.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
							.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
							.form(listParams).execute().body();
					logger.debug("阳光电源签收结果：{}",listRes);
					if(listRes.contains("成功")){
						sunshineBackRecord.setSuccessFlag(0);
					}else{
						sunshineBackRecord.setSuccessFlag(1);
					}
					sunshineBackRecord.setBackMemo(listRes);

				}else{
					//根据车号查询
					listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"eq\",\"data\":\""+invoice.getCustOrderno()+"\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"},{\"field\":\"EQUIPMENT_ID\",\"op\":\"eq\",\"data\":\""+entrust.getCarno()+"\"}]}");
					listRes = HttpRequest.post(listUrl)
							.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
							.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
							.form(listParams).execute().body();
					listJson = JSONObject.parseObject(listRes);
					rows = listJson.getJSONArray("rows");
					if(rows.size() == 1){
						String relatedkey = rows.getJSONObject(0).getString("id");
						String relatedID = rows.getJSONObject(0).getJSONArray("cell").getString(1);

						String picUploadURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/PartialUploadAttachment?relatedkey=" + relatedkey + "&relatedID=" + relatedID + "&relatedtype=40&domainName=SALES";

						Map<String, String> headersPost = new HashMap<>();
						headersPost.put("Accept", "*/*");
						headersPost.put("Accept-Encoding", "gzip, deflate, br, zstd");
						headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
						headersPost.put("Connection", "keep-alive");
						headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryjfPDtu6h3wMxaApO");
						headersPost.put("Cookie", "cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; Lang=zh-cn; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DFTvM9f4X5v89fLttSlA3OQ%3D%3D");
						headersPost.put("Origin", "https://tms.sungrowpower.com");
						headersPost.put("Referer", "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/Edit?guid=SALES.POD060308");
						headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
						headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
						headersPost.put("sec-ch-ua-mobile", "?0");
						headersPost.put("sec-ch-ua-platform", "\"Windows\"");

						//上传回单附件
						List<SysUploadFile> sysUploadFiles = entrustWorkMapper.selectReceiptPicByEntrustId(entrust.getEntrustId());
						for(SysUploadFile sysUploadFile : sysUploadFiles) {
							String filePath = "D:/" + sysUploadFile.getFilePath();

							HttpRequest request = HttpRequest.post(picUploadURL)
									.headerMap(headersPost, true)
									.form("files", new File(filePath));

							cn.hutool.http.HttpResponse execute = request.execute();
							String uploadRes = execute.body();
							logger.debug("阳光电源回单上传结果：{}",uploadRes);
						}

						//签收单据
						sdfDay = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

						String receiptUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/PodListSigned";
						listParams = new HashMap<>();
						listParams.put("signed_by","南通吉华物流有限公司");
						if(entrust.getActArriDate() == null){
							listParams.put("signed_date",sdfDay.format(new Date()));
						}else{
							listParams.put("signed_date",sdfDay.format(entrust.getActArriDate()));
						}

						listParams.put("signed_status",10);
						listParams.put("guids",relatedkey);

						listRes = HttpRequest.post(receiptUrl)
								.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
								.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
								.form(listParams).execute().body();
						logger.debug("阳光电源签收结果：{}",listRes);
						if(listRes.contains("成功")){
							sunshineBackRecord.setSuccessFlag(0);
						}else{
							sunshineBackRecord.setSuccessFlag(1);
						}
						sunshineBackRecord.setBackMemo(listRes);

					}else{
						//保存异常信息
						sunshineBackRecord.setSuccessFlag(1);
						sunshineBackRecord.setBackMemo("客户单号"+invoice.getCustOrderno()+"车牌号"+entrust.getCarno()+"，在阳光系统查询到"+rows.size()+"单");
					}
				}
			}else{
				//保存异常信息
				sunshineBackRecord.setSuccessFlag(1);
				sunshineBackRecord.setBackMemo("登录失败");
			}
		}else{
			//保存异常信息
			sunshineBackRecord.setSuccessFlag(1);
			sunshineBackRecord.setBackMemo("发货单存在拆单情况，无法推送到货信息");
		}
		//保存回调信息
		entrustMapper.insertSunshineBackRecord(sunshineBackRecord);
	}

	@Async
	public void grabPdfAndQuote(String token){
		try{
			SimpleDateFormat sdfDay = new SimpleDateFormat("yy/MM/dd");
			Calendar cal = Calendar.getInstance();
			cal.setTime(new Date());
			cal.add(Calendar.MONTH,-3);
			String dayStr = sdfDay.format(cal.getTime());

			String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/GetShipmentInfos?udfDisplayForqueryGuid=8200007456ygdySCM.TMS7.Model.Models.shipment_SCM.TMS7.Model&defsort=CREATED_DATE%20DESC";
			Map<String,Object> listParams = new HashMap<>();
			listParams.put("_search","true");
			listParams.put("rows","200");
			listParams.put("page","1");
			listParams.put("sord","asc");
			listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"}],\"groups\":[{\"groupOp\":\"OR\",\"groups\":[],\"rules\":[{\"data\":\"30\",\"op\":\"eq\",\"field\":\"STATUS\"},{\"data\":\"40\",\"op\":\"eq\",\"field\":\"STATUS\"},{\"data\":\"50\",\"op\":\"eq\",\"field\":\"STATUS\"}]}]}");
			String listRes = HttpRequest.post(listUrl)
					.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
					.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
					.form(listParams).execute().body();
			JSONObject listJson = JSONObject.parseObject(listRes);
			JSONArray rows = listJson.getJSONArray("rows");

			//每次限制发五条，企业微信推送存在并发限制
			Integer successCnt = 0;

			for(int i = 0 ; i < rows.size(); i++) {
				JSONObject row = rows.getJSONObject(i);
				String gid = row.getString("id");
				JSONArray cells = row.getJSONArray("cell");

				//运单ID
				String originNo = cells.get(2).toString();
				//客户订单号
				String custOrderNo = cells.get(1).toString();

				SunshinePdfRecord sunshinePdfRecord = new SunshinePdfRecord();
				sunshinePdfRecord.setId(IdUtil.simpleUUID());
				sunshinePdfRecord.setCustOrderNo(custOrderNo);
				sunshinePdfRecord.setOriginNo(originNo);

				//判断是否保存过
				Integer cnt = invoiceMapper.selectSunshinePdfRecordByOriginNo(originNo);
				if (cnt == 0) {
					if(successCnt >= 5){
						break;
					}
					//获取保价金额
					String url = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/Edit";
					listParams = new HashMap<>();
					listParams.put("gid", gid);
					listRes = HttpRequest.post(url)
							.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
							.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token+ "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
							.form(listParams).execute().body();
					Document doc  = Jsoup.parse(listRes);
					Element inputElement = doc.select("input[name=INSURED_AMOUNT]").first();
					if (inputElement!= null) {
						String value = inputElement.attr("value");
						sunshinePdfRecord.setQuoteAmount(value);
					} else {
						sunshinePdfRecord.setQuoteAmount("0");
					}

					//获取PDF
					String pdfUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/QtUdf/CallJob";
					listParams = new HashMap<>();
					listParams.put("id", gid);
					listParams.put("jobName", "SCM.TMS7.Customization.Common.UdfPrint,SCM.TMS7.Customization");
					listParams.put("buttonId", "接货单");

					listRes = HttpRequest.post(pdfUrl)
							.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
							.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
							.form(listParams).execute().body();
					//保存返回信息
					sunshinePdfRecord.setPdfMsg(listRes);
					if(listRes.contains("true")){
						sunshinePdfRecord.setPdfFlag(0);
						JSONObject object = JSONObject.parseObject(listRes);
						//下载地址
						String downloadUrl = "https://tms.sungrowpower.com" + object.getString("Msg");
						//保存到指定路径
						String fileName = cells.getString(32).replace("市","")+"-"+cells.getString(6)+"+"+originNo+".pdf";
						fileName = fileName.replaceAll("[\\\\/:*?\"<>|]", "");
						String relatePath = "/static/upload/sunshinepdf/"+fileName;
						String filePath = "D:"+relatePath;
						//保存文件
						downloadPDF(downloadUrl, filePath, token);
						sunshinePdfRecord.setFileName(fileName);
						sunshinePdfRecord.setFilePath(filePath);
						sunshinePdfRecord.setRelatePath(relatePath);
						sunshinePdfRecord.setDownloadUrl(downloadUrl);

						//小蜜推送
						String key = "394b6d97-9664-4e41-8d7b-544d96212a17";
						String body = HttpRequest.post("https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?key="+key+"&type=file")
								.header("Content-Type", "multipart/form-data")
								.form("media", new File(filePath))
								.form("filename", fileName).execute().body();
						JSONObject wechatPicBack = JSONObject.parseObject(body);
						sunshinePdfRecord.setWxPicMsg(body);
						if(wechatPicBack.getInteger("errcode") == 0){
							String mediaId = wechatPicBack.getString("media_id");
							String postUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key="+key;
							Map params = new LinkedHashMap<String,String>();
							params.put("msgtype","file");

							Map textParam = new LinkedHashMap<String,String>();
							textParam.put("media_id",mediaId);
							params.put("file",textParam);

							String s = JSONUtil.toJsonStr(params);
							String sendRes = HttpUtil.post(postUrl, s);
							if(JSONObject.parseObject(sendRes).getInteger("errcode") != 0){
								sunshinePdfRecord.setPdfFlag(1);
								WechatMessageUtils.sendTxtMessage("2f88fa6e-275b-4df4-9ca4-e363067a0f69","阳光PDF异常:小蜜发送PDF失败,单号"+originNo);
							}

							//发送文本消息
							//String message = "保价"+sunshinePdfRecord.getQuoteAmount().replace(".00","")+"元+"+cells.getString(14).replace("陆运","")+cells.getString(15);
							//WechatMessageUtils.sendTxtMessage(key,message);

							BigDecimal quoteAmount = new BigDecimal(sunshinePdfRecord.getQuoteAmount());
							quoteAmount = quoteAmount.divide(new BigDecimal("10000"),0,BigDecimal.ROUND_UP);

							//发送订单信息
							PdfReader reader = new PdfReader(filePath);
							int pages = reader.getNumberOfPages();
							StringBuffer buffer = new StringBuffer();
							for (int j = 1; j <= pages; j++) {
								String str = PdfTextExtractor.getTextFromPage(reader, j);
								buffer.append(str);
								break;
							}

							List<String> stringList = new ArrayList<>(Arrays.asList(buffer.toString().split("\\n")));

							String startDate = "";
							String startAddress = "";
							String endAddress = "";


							for(int j = 0; j < stringList.size(); j++) {
								String str = stringList.get(j);
								if(str.contains("发货时间")){
									str = str.replace("发货时间：","").replaceAll("\\s", "");
									String[] dateArr = str.split("/");
									if(dateArr.length == 3){
										startDate = dateArr[0]+"年"+dateArr[1]+"月"+dateArr[2]+"日";
									}
								}
								if(str.contains("发货单位")){
									if(!stringList.get(j-1).contains("单据编号")){
										startAddress = stringList.get(j-1);
									}else if(!stringList.get(j+1).contains("发货时间")){
										startAddress = stringList.get(j+1);
									}else{
										startAddress = str.replace("发货单位：","").replaceAll("\\s", "");
									}
								}
								if(str.contains("交货地址")){
									endAddress = str.replace("交货地址：","").replaceAll("\\s", "");
									if(StringUtils.isBlank(endAddress)){
										if(!stringList.get(j-1).contains("接货单位")){
											endAddress = stringList.get(j-1);
										}else if(!stringList.get(j+1).contains("收货人及联系方式")){
											endAddress = stringList.get(j+1);
										}
									}
								}
							}

							//获取货物信息
							Map<String,Object> goodsParams = new HashMap<>();
							goodsParams.put("_search","false");
							goodsParams.put("rows","200");
							goodsParams.put("page","1");
							goodsParams.put("sord","asc");
							String goodsUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/GetShipmentPackageItems?gid="+gid+"&defsort=LINE_ID%20Asc,OM_LINE_ID%20Asc";
							String goodsRes = HttpRequest.post(goodsUrl)
									.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
									.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
									.form(goodsParams).execute().body();
							JSONObject goodsResObj = JSONObject.parseObject(goodsRes);
							JSONArray goodsRows = goodsResObj.getJSONArray("rows");
							String goodsInfo = "";
							for(int j = 0 ; j < goodsRows.size(); j++) {
								JSONArray goodsCell = goodsRows.getJSONObject(j).getJSONArray("cell");
								goodsInfo = goodsInfo + goodsCell.getString(2) + " " + goodsCell.getString(3).replace(".00","") + "台\n";
							}


							StringBuffer res = new StringBuffer();
							res.append("运输公司名称：南通吉华物流有限公司\n");
							res.append("起运时间："+startDate+"\n");
							res.append("货物信息："+goodsInfo);
							if(quoteAmount.compareTo(new BigDecimal("800")) > 0){
								res.append("保险金额："+quoteAmount.toString()+"万(需拆单)\n");
							}else{
								res.append("保险金额："+quoteAmount.toString()+"万\n");
							}
							res.append("车牌号："+"\n");
							res.append("行驶路线："+startAddress+"→"+endAddress+"\n");
							res.append("提货号："+custOrderNo+"\n");
							res.append("车型："+cells.getString(11).replace("陆运","")+cells.getString(12)+"\n");

							sendRes = WechatMessageUtils.sendTxtMessage(key,res.toString());
							if(JSONObject.parseObject(sendRes).getInteger("errcode") != 0){
								sunshinePdfRecord.setPdfFlag(1);
								WechatMessageUtils.sendTxtMessage("2f88fa6e-275b-4df4-9ca4-e363067a0f69","阳光PDF异常:小蜜发送文本失败,单号"+originNo);
							}

						}else{
							sunshinePdfRecord.setPdfFlag(1);
							WechatMessageUtils.sendTxtMessage("2f88fa6e-275b-4df4-9ca4-e363067a0f69","阳光PDF异常:小蜜上传PDF失败,单号"+originNo);
						}
					}else{
						sunshinePdfRecord.setPdfFlag(1);
						WechatMessageUtils.sendTxtMessage("2f88fa6e-275b-4df4-9ca4-e363067a0f69","阳光PDF异常:未获取到PDF,单号"+originNo);
					}

					//保存记录
					invoiceMapper.insertSunshinePdfRecord(sunshinePdfRecord);

					successCnt++;
				}
			}
		}catch (Exception e){
			//小蜜提醒
			WechatMessageUtils.sendTxtMessage("2f88fa6e-275b-4df4-9ca4-e363067a0f69","阳光PDF异常:"+e.getMessage());
			logger.error(e.getMessage());
			logger.error("阳光PDF异常:",e);
		}
	}

	public void downloadPDF(String urlString, String fileName,String token) {
		try {
			URL url = new URL(urlString);
			URLConnection connection = url.openConnection();
			connection.setRequestProperty("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D");

			InputStream inputStream = connection.getInputStream();

			FileOutputStream outputStream = new FileOutputStream(fileName);

			byte[] buffer = new byte[4096];
			int bytesRead;
			while ((bytesRead = inputStream.read(buffer))!= -1) {
				outputStream.write(buffer, 0, bytesRead);
			}

			outputStream.close();
			inputStream.close();

			System.out.println("PDF 下载成功！");
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	@Async
	public void sunshineFeeAmount(EntrustCostMain entrustCostMain,List<EntrustCost> list){
		Entrust entrust = entrustMapper.selectEntrustById(entrustCostMain.getEntrustId());

		//保存信息
		SunshineBackRecord sunshineBackRecord = new SunshineBackRecord();
		sunshineBackRecord.setId(IdUtil.simpleUUID());
		sunshineBackRecord.setEntrustVbillno(entrust.getVbillno());
		sunshineBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
		sunshineBackRecord.setRegDate(new Date());
		sunshineBackRecord.setType(3);
		sunshineBackRecord.setCustOrderNo(entrust.getCustOrderno());

		//查询发货单
		Invoice invoice = invoiceMapper.selectInvoiceById(entrustCostMain.getInvoiceId());
		//阳光电源判断
		if(invoice.getCustomerId().equals("8dbbca4cca2b4ee7978781bca8465cac") || invoice.getCustomerId().equals("1d9ada8bb08a4286b9654cdcb81bcd4c") || invoice.getCustomerId().equals("2a011a0da1964c999441c2622ecd582f")){
			String token = getSunshineToken(false);
			if(StringUtils.isNotBlank(token)){
				//查询应付结账单
				SimpleDateFormat sdfDay = new SimpleDateFormat("yy/MM/dd");
				Calendar cal = Calendar.getInstance();
				cal.setTime(new Date());
				cal.add(Calendar.MONTH,-3);
				String dayStr = sdfDay.format(cal.getTime());
				//请求列表接口
				String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/ShipmentInvoice/GetShipmentInvoices?defsort=CREATED_DATE%20DESC";
				Map<String,Object> listParams = new HashMap<>();
				listParams.put("_search","true");
				listParams.put("rows","20");
				listParams.put("page","1");
				listParams.put("sord","asc");
				listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"eq\",\"data\":\""+invoice.getCustOrderno()+"\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"}]}");
				String listRes = HttpRequest.post(listUrl)
						.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
						.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
						.form(listParams).execute().body();
				JSONObject listJson = JSONObject.parseObject(listRes);
				JSONArray rows = listJson.getJSONArray("rows");
				if(rows.size() == 1) {
					String gids = rows.getJSONObject(0).getString("id");

					JSONArray jsonArray = rows.getJSONObject(0).getJSONArray("cell");
					//PM单号
					String pmNum = jsonArray.getString(3);

					//上传附件
					if(StringUtils.isNotBlank(entrustCostMain.getAppendixId())){

						String picUploadURL = "https://tms.sungrowpower.com/SCM.Configration.WebUI/AttachmentInfo/Upload";

						Map<String, String> headersPost = new HashMap<>();
						headersPost.put("Accept", "*/*");
						headersPost.put("Accept-Encoding","gzip, deflate, br, zstd");
						headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
						headersPost.put("Connection", "keep-alive");
						headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryjfPDtu6h3wMxaApO");
						headersPost.put("Cookie", "cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; Lang=zh-cn; SSID="+token+"; PowerTmsCookie=TmsUserID%3DFTvM9f4X5v89fLttSlA3OQ%3D%3D");
						headersPost.put("Origin", "https://tms.sungrowpower.com");
						headersPost.put("Referer", "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/Edit?guid=SALES.POD060308");
						headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
						headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
						headersPost.put("sec-ch-ua-mobile", "?0");
						headersPost.put("sec-ch-ua-platform", "\"Windows\"");

						Map<String,Object> sendParams = new HashMap<String,Object>();
						sendParams.put("Sys","TMS");
						sendParams.put("Module","ShipmentInvoice");
						sendParams.put("Id",pmNum);
						sendParams.put("AttachType","附加费证明");
						sendParams.put("RelatedKey",gids);
						sendParams.put("PrimaryId",gids);

						List<SysUploadFile> sysUploadFiles = sysUploadFileMapper.selectSysUploadFileByTid(entrustCostMain.getAppendixId());
						for(SysUploadFile sysUploadFile : sysUploadFiles) {
							String filePath = "D:/" + sysUploadFile.getFilePath();
							sendParams.put("files",new File(filePath));

							HttpRequest request = HttpRequest.post(picUploadURL)
									.headerMap(headersPost, true)
									.form(sendParams);
							String uploadRes = request.execute().body();
							logger.debug("阳光电源附加费附件上传结果：{}",uploadRes);
						}
					}


					for(EntrustCost entrustCost : list){
						if(invoice.getCustomerId().equals("2a011a0da1964c999441c2622ecd582f") && !"5".equals(entrustCost.getCostType())){
							continue;
						}
						if (entrustCost.getIsLotFee() == 0 && entrustCost.getPushFlag() == 1) {
							//2:收取客户费用  4:赔偿客户费用
							if ("2".equals(entrustCost.getBudgetType()) || "4".equals(entrustCost.getBudgetType())) {
								//提交费用
								String feeUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/ShipmentInvoice/SaveShipmentInvoiceLine?gid="+gids;
								Map<String, Object> feeParam = new HashMap<>();
								if(invoice.getCustomerId().equals("2a011a0da1964c999441c2622ecd582f") && "5".equals(entrustCost.getCostType())){
									feeParam.put("CHARGE_TYPE_PSC", "190");
									feeParam.put("CHARGE_TYPE_PSC_NAME", "进门费");
								}else{
									feeParam.put("CHARGE_TYPE_PSC", "S50");
									if(StringUtils.isNotBlank(entrustCost.getMemo())){
										feeParam.put("REMARK", entrustCost.getMemo());
									}else{
										feeParam.put("REMARK", "异常费用");
									}
								}

								feeParam.put("CHARGE", entrustCost.getCost());
								feeParam.put("IS_TAX_INCLUDED", "Y");



								listRes = HttpRequest.post(feeUrl)
										.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
										.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
										.form(feeParam).execute().body();
								if(listRes.contains("成功")){
									sunshineBackRecord.setSuccessFlag(0);
								}else{
									sunshineBackRecord.setSuccessFlag(1);
								}
								sunshineBackRecord.setBackMemo(listRes);
							}
						}
					}




				}else{
					//保存异常信息
					sunshineBackRecord.setSuccessFlag(1);
					sunshineBackRecord.setBackMemo("客户单号"+invoice.getCustOrderno()+"，在阳光系统查询到"+rows.size()+"单");
				}
			}else{
				//保存异常信息
				sunshineBackRecord.setSuccessFlag(1);
				sunshineBackRecord.setBackMemo("登录失败");
			}
			//保存回调信息
			entrustMapper.insertSunshineBackRecord(sunshineBackRecord);
		}
	}


	@Async
	public void sunshineFeeAmountAdjust(ReceiveDetailVO receiveDetailVO , AdjustRecord adjustRecord){
		//查询发货单
		Invoice invoice = invoiceMapper.selectInvoiceById(receiveDetailVO.getInvoiceId());
		//保存信息
		SunshineBackRecord sunshineBackRecord = new SunshineBackRecord();
		sunshineBackRecord.setId(IdUtil.simpleUUID());
		sunshineBackRecord.setEntrustVbillno(invoice.getVbillno());
		sunshineBackRecord.setInvoiceVbillno(invoice.getVbillno());
		sunshineBackRecord.setRegDate(new Date());
		sunshineBackRecord.setType(3);
		sunshineBackRecord.setCustOrderNo(invoice.getCustOrderno());

		//阳光电源判断
		if(invoice.getCustomerId().equals("8dbbca4cca2b4ee7978781bca8465cac") || invoice.getCustomerId().equals("1d9ada8bb08a4286b9654cdcb81bcd4c") || invoice.getCustomerId().equals("2a011a0da1964c999441c2622ecd582f")){
			if(receiveDetailVO.getPushFlag() == 1){
				String token = getSunshineToken(false);
				if(StringUtils.isNotBlank(token)){
					//查询应付结账单
					SimpleDateFormat sdfDay = new SimpleDateFormat("yy/MM/dd");
					Calendar cal = Calendar.getInstance();
					cal.setTime(new Date());
					cal.add(Calendar.MONTH,-3);
					String dayStr = sdfDay.format(cal.getTime());
					//请求列表接口
					String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/ShipmentInvoice/GetShipmentInvoices?defsort=CREATED_DATE%20DESC";
					Map<String,Object> listParams = new HashMap<>();
					listParams.put("_search","true");
					listParams.put("rows","20");
					listParams.put("page","1");
					listParams.put("sord","asc");
					listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"eq\",\"data\":\""+invoice.getCustOrderno()+"\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"}]}");
					String listRes = HttpRequest.post(listUrl)
							.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
							.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
							.form(listParams).execute().body();
					JSONObject listJson = JSONObject.parseObject(listRes);
					JSONArray rows = listJson.getJSONArray("rows");
					if(rows.size() == 1) {
						String gids = rows.getJSONObject(0).getString("id");

						JSONArray jsonArray = rows.getJSONObject(0).getJSONArray("cell");
						//PM单号
						String pmNum = jsonArray.getString(3);

						//上传附件
						if(StringUtils.isNotBlank(adjustRecord.getTid())){

							String picUploadURL = "https://tms.sungrowpower.com/SCM.Configration.WebUI/AttachmentInfo/Upload";

							Map<String, String> headersPost = new HashMap<>();
							headersPost.put("Accept", "*/*");
							headersPost.put("Accept-Encoding","gzip, deflate, br, zstd");
							headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
							headersPost.put("Connection", "keep-alive");
							headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryjfPDtu6h3wMxaApO");
							headersPost.put("Cookie", "cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; Lang=zh-cn; SSID="+token+"; PowerTmsCookie=TmsUserID%3DFTvM9f4X5v89fLttSlA3OQ%3D%3D");
							headersPost.put("Origin", "https://tms.sungrowpower.com");
							headersPost.put("Referer", "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/Edit?guid=SALES.POD060308");
							headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
							headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
							headersPost.put("sec-ch-ua-mobile", "?0");
							headersPost.put("sec-ch-ua-platform", "\"Windows\"");

							Map<String,Object> sendParams = new HashMap<String,Object>();
							sendParams.put("Sys","TMS");
							sendParams.put("Module","ShipmentInvoice");
							sendParams.put("Id",pmNum);
							sendParams.put("AttachType","附加费证明");
							sendParams.put("RelatedKey",gids);
							sendParams.put("PrimaryId",gids);

							List<SysUploadFile> sysUploadFiles = sysUploadFileMapper.selectSysUploadFileByTid(adjustRecord.getTid());
							for(SysUploadFile sysUploadFile : sysUploadFiles) {
								String filePath = "D:/" + sysUploadFile.getFilePath();
								sendParams.put("files",new File(filePath));

								HttpRequest request = HttpRequest.post(picUploadURL)
										.headerMap(headersPost, true)
										.form(sendParams);
								String uploadRes = request.execute().body();
								logger.debug("阳光电源附加费附件上传结果：{}",uploadRes);
							}
						}

						//提交费用
						String feeUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/ShipmentInvoice/SaveShipmentInvoiceLine?gid="+gids;
						Map<String, Object> feeParam = new HashMap<>();
						feeParam.put("CHARGE_TYPE_PSC", "S50");
						feeParam.put("CHARGE", receiveDetailVO.getTransFeeCount());
						feeParam.put("IS_TAX_INCLUDED", "Y");
						if(StringUtils.isNotBlank(adjustRecord.getMemo())){
							feeParam.put("REMARK", adjustRecord.getMemo());
						}else{
							feeParam.put("REMARK", "异常费用");
						}


						listRes = HttpRequest.post(feeUrl)
								.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
								.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
								.form(feeParam).execute().body();
						if(listRes.contains("成功")){
							sunshineBackRecord.setSuccessFlag(0);
						}else{
							sunshineBackRecord.setSuccessFlag(1);
						}
						sunshineBackRecord.setBackMemo(listRes);

					}else{
						//保存异常信息
						sunshineBackRecord.setSuccessFlag(1);
						sunshineBackRecord.setBackMemo("客户单号"+invoice.getCustOrderno()+"，在阳光系统查询到"+rows.size()+"单");
					}
				}else{
					//保存异常信息
					sunshineBackRecord.setSuccessFlag(1);
					sunshineBackRecord.setBackMemo("登录失败");
				}
				//保存回调信息
				entrustMapper.insertSunshineBackRecord(sunshineBackRecord);
			}
		}
	}

	public String getSunshineToken(boolean refushFlag){
		// 连接到本地的 Redis 服务器，默认端口是 6379
		try (Jedis jedis = new Jedis(redisHost, Integer.parseInt(redisPort))) {
			if(refushFlag){
				jedis.del("sunshineToken");
			}
			// 可以在这里进行 Redis 操作，例如设置键值对
			String value = jedis.get("sunshineToken");
			if(StringUtils.isBlank(value)){
				String password = sysConfigService.selectConfigByKey("sunshine_password");
				//登录验证
				String loginUrl = "https://tms.sungrowpower.com/scm.cloud.web/Login/Login";
				Map<String,Object> loginParams = new HashMap<>();
				loginParams.put("userid","8200007456");
				loginParams.put("password",password);
				loginParams.put("duration","0");
				String body = HttpRequest.post(loginUrl)
						.form(loginParams)
						.execute().body();
				JSONObject jsonObject = JSONObject.parseObject(body);
				if(StringUtils.isNotBlank(jsonObject.getString("ssid"))) {
					jedis.set("sunshineToken", jsonObject.getString("ssid"));
					// 设置该键在 2 小时后过期（7200 秒）
					jedis.expire("sunshineToken", 7200);
					return jsonObject.getString("ssid");
				}else {
					return null;
				}
			}else{
				return value;
			}
 		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		return null;
	}

	public String getZLLGToken(boolean refushFlag){
		// 连接到本地的 Redis 服务器，默认端口是 6379
		try (Jedis jedis = new Jedis(redisHost, Integer.parseInt(redisPort))) {
			if(refushFlag){
				jedis.del("zllgToken");
			}
			// 可以在这里进行 Redis 操作，例如设置键值对
			String value = jedis.get("zllgToken");
			if(StringUtils.isBlank(value)){
				Map<String, Object> params = new HashMap<String, Object>();
				params.put("customerkey","");
				params.put("rememberMe",true);
				params.put("userCode","ntjhwl");
				params.put("userPassword","f4af811c4789fa736385ecf51a680340");
				params.put("verificationCode","");
				HttpResponse execute = HttpRequest.post("https://lgtms.cofco.com/login").body(JSON.toJSONString(params)).execute();
				String token = execute.getCookie("RequestToken").getValue();
				if(StringUtils.isNotBlank(token)) {
					jedis.set("zllgToken", token);
					// 设置该键在 2 小时后过期（7200 秒）
					jedis.expire("zllgToken", 3600);
					return token;
				}else {
					return null;
				}
			}else{
				return value;
			}
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		return null;
	}

	/**
	 * 中粮面业 派车
	 */
	@Async
	public void zllgPick(String entrustId,String pickCarId,String pickDriverId,Date actArriDate,Date actLeaveDate) throws ParseException {
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);

		//判断发货单被抓取过
		HYInvoiceRecord hyInvoiceRecord = invoiceMapper.selectSuccessHyInvoiceByInvoiceId(entrust.getOrderno());
		if(hyInvoiceRecord == null){
			hyInvoiceRecord = getHYInvoiceRecordByOrderNo(entrust);
		}
		if(hyInvoiceRecord != null && StringUtils.isNotBlank(hyInvoiceRecord.getOriginId())){

			String token = getZLLGToken(false);

			//保存操作信息
			ZLLGBackRecord zllgBackRecord = new ZLLGBackRecord();
			zllgBackRecord.setId(IdUtil.simpleUUID());
			zllgBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
			zllgBackRecord.setEntrustVbillno(entrust.getVbillno());
			zllgBackRecord.setCustOrderNo(hyInvoiceRecord.getCustOrderno());
			zllgBackRecord.setOriginNo(hyInvoiceRecord.getOriginNo());
			zllgBackRecord.setRegDate(new Date());
			//派车
			zllgBackRecord.setType(0);
			if(StringUtils.isBlank(pickCarId) || StringUtils.isBlank(pickDriverId)){
				zllgBackRecord.setSuccessFlag(1);
				zllgBackRecord.setBackMemo("提货没有车辆或司机选择");
			}else{
				Car car = carMapper.selectCarById(pickCarId);
				//查询车辆是否存在
				String carId = getZLCarIdByCarNo(car.getCarno());
				if(StringUtils.isEmpty(carId)){
					//新增车辆
					carId = insertZlCar(entrust.getCarno());
					if(StringUtils.isEmpty(carId)){
						//再次判断，如果还为空就是新增失败
						zllgBackRecord.setSuccessFlag(1);
						zllgBackRecord.setBackMemo("新增车辆失败："+entrust.getCarno());
					}
				}

				Driver driver = driverMapper.selectDriverById(pickDriverId);
				String driverId = getDriverIdByCardId(driver.getCardId());
				if(StringUtils.isEmpty(driverId)){
					//新增车辆
					driverId = insertZLDriver(driver.getDriverName(),driver.getPhone(),driver.getCardId());
					if(StringUtils.isEmpty(driverId)){
						//再次判断，如果还为空就是新增失败
						zllgBackRecord.setSuccessFlag(1);
						zllgBackRecord.setBackMemo("新增司机失败："+driver.getDriverName());
					}
				}

				if(StringUtils.isNotBlank(driverId) && StringUtils.isNotBlank(carId)){
					//提货获取version信息
					String listRes = HttpRequest.get("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybill/getWaybillInfoForAssignVehicle?waybillId="+hyInvoiceRecord.getOriginId())
							.header("Content-Type", "application/json;charset=UTF-8")
							.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
							.execute().body();
					System.out.println(listRes);
					JSONObject object = JSONObject.parseObject(listRes);
					if(object.getInteger("code") == 1){
						String version = object.getJSONObject("result").getString("version");
						//2024-10-11 17:50:26
						String sendCarTime = object.getJSONObject("result").getString("sendCarTime");
						SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						Date sendCarTimeDate = sdf.parse(sendCarTime);

						String assignRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybill/assignVehicle")
								.header("Content-Type", "application/json;charset=UTF-8")
								.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
								.body("{\n" +
										"  \"vehicleId\": "+carId+",\n" +
										"  \"version\": "+version+",\n" +
										"  \"transportMode\": 0,\n" +
										"  \"sendCarTime\": "+sendCarTimeDate.getTime()+",\n" +
										"  \"mainDriverId\": "+driverId+",\n" +
										"  \"deliveryIdList\": [],\n" +
										"  \"coDriver1Id\": \"\",\n" +
										"  \"coDriver2Id\": \"\",\n" +
										"  \"assignCarRemark\": \"\",\n" +
										"  \"terminalList\": [],\n" +
										"  \"extendMap\": {},\n" +
										"  \"id\": \""+hyInvoiceRecord.getOriginId()+"\"\n" +
										"}")
								.execute().body();
						JSONObject assignObj = JSONObject.parseObject(assignRes);
						if(assignObj.getInteger("code") == 1){
							//派车成功
							zllgBackRecord.setSuccessFlag(0);
						}else{
							//派车失败
							zllgBackRecord.setSuccessFlag(1);
						}
						zllgBackRecord.setBackMemo(assignRes);
					}else{
						zllgBackRecord.setSuccessFlag(1);
						zllgBackRecord.setBackMemo("获取提货信息失败："+listRes);
					}
				}
			}
			//保存操作信息
			entrustMapper.insertZLLGBackRecord(zllgBackRecord);

			//节点号
			String wayBillNodeId = null;
			//如果派车成功开始提货操作
			if(zllgBackRecord.getSuccessFlag() == 0){
				//提货进场
				zllgBackRecord.setType(1);
				//获取提货节点
				String listRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/getWaybillNodeList")
						.header("Content-Type", "application/json;charset=UTF-8")
						.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
						.body("{\n" +
								"  \"corpId\": 193729,\n" +
								"  \"id\": \""+hyInvoiceRecord.getOriginId()+"\",\n" +
								"  \"plantForm\": 1,\n" +
								"  \"version\": 1729821652511\n" +
								"}")
						.execute().body();
				JSONObject parseObject = JSONObject.parseObject(listRes);
				if(parseObject.getInteger("code") == 1){
					JSONArray result = parseObject.getJSONArray("result");
					//获取节点
					for(int i = 0 ; i < result.size() ; i++){
						if(result.getJSONObject(i).getBoolean("selectFlag")) {
							wayBillNodeId = result.getJSONObject(i).getString("id");
						}
					}
					if(StringUtils.isBlank(wayBillNodeId)){
						//未找到提货节点
						zllgBackRecord.setSuccessFlag(1);
						zllgBackRecord.setBackMemo("未找到提货节点");
					}else{
						String version = getVersion(hyInvoiceRecord.getOriginNo());
						//提货
						String pickRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/arriveWaybillNode")
								.header("Content-Type", "application/json;charset=UTF-8")
								.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
								.body("{\n" +
										"  \"corpId\": 193729,\n" +
										"  \"id\": \""+hyInvoiceRecord.getOriginId()+"\",\n" +
										"  \"plantForm\": 1,\n" +
										"  \"version\": "+version+",\n" +
										"  \"time\": \""+DateFormatUtils.format(actArriDate,"yyyy-MM-dd HH:mm")+"\",\n" +
										"  \"waybillNodeId\": "+wayBillNodeId+"\n" +
										"}")
								.execute().body();
						JSONObject object = JSONObject.parseObject(pickRes);
						if(object.getInteger("code") == 1){
							//成功
							zllgBackRecord.setSuccessFlag(0);
						}else{
							//提货到场失败
							zllgBackRecord.setSuccessFlag(1);
						}
						zllgBackRecord.setBackMemo(pickRes);
					}
				}else{
					zllgBackRecord.setSuccessFlag(1);
					zllgBackRecord.setBackMemo("获取提货节点失败："+listRes);
				}
				//保存操作信息
				zllgBackRecord.setId(IdUtil.simpleUUID());
				entrustMapper.insertZLLGBackRecord(zllgBackRecord);
			}


			//继续提货离场
			if(zllgBackRecord.getSuccessFlag() == 0){
				//提货离场
				zllgBackRecord.setType(2);
				String version = getVersion(hyInvoiceRecord.getOriginNo());
				String arriRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/leaveWaybillNode")
						.header("Content-Type", "application/json;charset=UTF-8")
						.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
						.body("{\n" +
								"  \"time\": \""+DateFormatUtils.format(actLeaveDate,"yyyy-MM-dd HH:mm")+"\",\n" +
								"  \"waybillNodeId\": "+wayBillNodeId+",\n" +
								"  \"remark\": \"\",\n" +
								"  \"urlList\": [],\n" +
								"  \"corpId\": 193729,\n" +
								"  \"id\": \""+hyInvoiceRecord.getOriginId()+"\",\n" +
								"  \"plantForm\": 1,\n" +
								"  \"version\": "+version+"\n" +
								"}")
						.execute().body();
				JSONObject object = JSONObject.parseObject(arriRes);
				if(object.getInteger("code") == 1) {
					//成功
					zllgBackRecord.setSuccessFlag(0);
				}else{
					//失败
					zllgBackRecord.setSuccessFlag(1);
				}
				zllgBackRecord.setBackMemo(arriRes);
				//保存操作信息
				zllgBackRecord.setId(IdUtil.simpleUUID());
				entrustMapper.insertZLLGBackRecord(zllgBackRecord);
			}

			//继续提货确认
			if(zllgBackRecord.getSuccessFlag() == 0){
				//提货确认
				zllgBackRecord.setType(3);
				//确认
				String commitRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybill/commitDeliveryCheck")
						.header("Content-Type", "application/json;charset=UTF-8")
						.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
						.body("{\n" +
								"  \"waybillId\": \""+hyInvoiceRecord.getOriginId()+"\",\n" +
								"  \"waybillNodeId\": "+wayBillNodeId+",\n" +
								"  \"deleteOrderIdList\": [],\n" +
								"  \"insertOrderIdList\": []\n" +
								"}")
						.execute().body();
				JSONObject object = JSONObject.parseObject(commitRes);
				if(object.getInteger("code") == 1) {
					//成功
					zllgBackRecord.setSuccessFlag(0);
				}else{
					//失败
					zllgBackRecord.setSuccessFlag(1);
				}
				zllgBackRecord.setBackMemo(commitRes);
				//保存操作信息
				zllgBackRecord.setId(IdUtil.simpleUUID());
				entrustMapper.insertZLLGBackRecord(zllgBackRecord);
			}
		}
	}


	/**
	 * 中粮面业 派车
	 */
	@Async
	public void zllgPc(String entrustId,String pickCarId,String pickDriverId) throws ParseException {
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);

		//判断发货单被抓取过
		HYInvoiceRecord hyInvoiceRecord = invoiceMapper.selectSuccessHyInvoiceByInvoiceId(entrust.getOrderno());
		if(hyInvoiceRecord == null){
			hyInvoiceRecord = getHYInvoiceRecordByOrderNo(entrust);
		}
		if(hyInvoiceRecord != null && StringUtils.isNotBlank(hyInvoiceRecord.getOriginId())){

			String token = getZLLGToken(false);

			//保存操作信息
			ZLLGBackRecord zllgBackRecord = new ZLLGBackRecord();
			zllgBackRecord.setId(IdUtil.simpleUUID());
			zllgBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
			zllgBackRecord.setEntrustVbillno(entrust.getVbillno());
			zllgBackRecord.setCustOrderNo(hyInvoiceRecord.getCustOrderno());
			zllgBackRecord.setOriginNo(hyInvoiceRecord.getOriginNo());
			zllgBackRecord.setRegDate(new Date());
			//派车
			zllgBackRecord.setType(0);

			//判断是否操作过
			Integer cnt = entrustMapper.selectZllgRecordSuccessCount(entrust.getInvoiceVbillno(), 0);
			if(cnt == 0){
				if(StringUtils.isBlank(pickCarId) || StringUtils.isBlank(pickDriverId)){
					zllgBackRecord.setSuccessFlag(1);
					zllgBackRecord.setBackMemo("提货没有车辆或司机选择");
				}else{
					Car car = carMapper.selectCarById(pickCarId);
					//查询车辆是否存在
					String carId = getZLCarIdByCarNo(car.getCarno());
					if(StringUtils.isEmpty(carId)){
						//新增车辆
						carId = insertZlCar(entrust.getCarno());
						if(StringUtils.isEmpty(carId)){
							//再次判断，如果还为空就是新增失败
							zllgBackRecord.setSuccessFlag(1);
							zllgBackRecord.setBackMemo("新增车辆失败："+entrust.getCarno());
						}
					}

					Driver driver = driverMapper.selectDriverById(pickDriverId);
					String driverId = getDriverIdByCardId(driver.getCardId());
					if(StringUtils.isEmpty(driverId)){
						//新增车辆
						driverId = insertZLDriver(driver.getDriverName(),driver.getPhone(),driver.getCardId());
						if(StringUtils.isEmpty(driverId)){
							//再次判断，如果还为空就是新增失败
							zllgBackRecord.setSuccessFlag(1);
							zllgBackRecord.setBackMemo("新增司机失败："+driver.getDriverName());
						}
					}

					if(StringUtils.isNotBlank(driverId) && StringUtils.isNotBlank(carId)){
						//提货获取version信息
						String listRes = HttpRequest.get("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybill/getWaybillInfoForAssignVehicle?waybillId="+hyInvoiceRecord.getOriginId())
								.header("Content-Type", "application/json;charset=UTF-8")
								.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
								.execute().body();
						System.out.println(listRes);
						JSONObject object = JSONObject.parseObject(listRes);
						if(object.getInteger("code") == 1){
							String version = object.getJSONObject("result").getString("version");
							//2024-10-11 17:50:26
							String sendCarTime = object.getJSONObject("result").getString("sendCarTime");
							SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							Date sendCarTimeDate = sdf.parse(sendCarTime);

							String assignRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybill/assignVehicle")
									.header("Content-Type", "application/json;charset=UTF-8")
									.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
									.body("{\n" +
											"  \"vehicleId\": "+carId+",\n" +
											"  \"version\": "+version+",\n" +
											"  \"transportMode\": 0,\n" +
											"  \"sendCarTime\": "+sendCarTimeDate.getTime()+",\n" +
											"  \"mainDriverId\": "+driverId+",\n" +
											"  \"deliveryIdList\": [],\n" +
											"  \"coDriver1Id\": \"\",\n" +
											"  \"coDriver2Id\": \"\",\n" +
											"  \"assignCarRemark\": \"\",\n" +
											"  \"terminalList\": [],\n" +
											"  \"extendMap\": {},\n" +
											"  \"id\": \""+hyInvoiceRecord.getOriginId()+"\"\n" +
											"}")
									.execute().body();
							JSONObject assignObj = JSONObject.parseObject(assignRes);
							if(assignObj.getInteger("code") == 1){
								//派车成功
								zllgBackRecord.setSuccessFlag(0);
							}else{
								//派车失败
								zllgBackRecord.setSuccessFlag(1);
							}
							zllgBackRecord.setBackMemo(assignRes);
						}else{
							zllgBackRecord.setSuccessFlag(1);
							zllgBackRecord.setBackMemo("获取提货信息失败："+listRes);
						}
					}
				}
				//保存操作信息
				entrustMapper.insertZLLGBackRecord(zllgBackRecord);
			}
		}
	}

	/**
	 * 中粮面业 派车
	 */
	@Async
	public void zllgPickJC(String entrustId,Date actArriDate) throws ParseException {
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);

		//判断发货单被抓取过
		HYInvoiceRecord hyInvoiceRecord = invoiceMapper.selectSuccessHyInvoiceByInvoiceId(entrust.getOrderno());
		if(hyInvoiceRecord == null){
			hyInvoiceRecord = getHYInvoiceRecordByOrderNo(entrust);
		}
		if(hyInvoiceRecord != null && StringUtils.isNotBlank(hyInvoiceRecord.getOriginId())){

			String token = getZLLGToken(false);

			//保存操作信息
			ZLLGBackRecord zllgBackRecord = new ZLLGBackRecord();
			zllgBackRecord.setId(IdUtil.simpleUUID());
			zllgBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
			zllgBackRecord.setEntrustVbillno(entrust.getVbillno());
			zllgBackRecord.setCustOrderNo(hyInvoiceRecord.getCustOrderno());
			zllgBackRecord.setOriginNo(hyInvoiceRecord.getOriginNo());
			zllgBackRecord.setRegDate(new Date());

			Integer cnt = entrustMapper.selectZllgRecordSuccessCount(entrust.getInvoiceVbillno(), 1);
			if(cnt == 0){
				//节点号
				String wayBillNodeId = null;
				//如果派车成功开始提货操作

				//提货进场
				zllgBackRecord.setType(1);
				//获取提货节点
				String listRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/getWaybillNodeList")
						.header("Content-Type", "application/json;charset=UTF-8")
						.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
						.body("{\n" +
								"  \"corpId\": 193729,\n" +
								"  \"id\": \""+hyInvoiceRecord.getOriginId()+"\",\n" +
								"  \"plantForm\": 1,\n" +
								"  \"version\": 1729821652511\n" +
								"}")
						.execute().body();
				JSONObject parseObject = JSONObject.parseObject(listRes);
				if(parseObject.getInteger("code") == 1){
					JSONArray result = parseObject.getJSONArray("result");
					//获取节点
					for(int i = 0 ; i < result.size() ; i++){
						if(result.getJSONObject(i).getBoolean("selectFlag")) {
							wayBillNodeId = result.getJSONObject(i).getString("id");
						}
					}
					if(StringUtils.isBlank(wayBillNodeId)){
						//未找到提货节点
						zllgBackRecord.setSuccessFlag(1);
						zllgBackRecord.setBackMemo("未找到提货节点");
					}else{
						String version = getVersion(hyInvoiceRecord.getOriginNo());
						//提货
						String pickRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/arriveWaybillNode")
								.header("Content-Type", "application/json;charset=UTF-8")
								.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
								.body("{\n" +
										"  \"corpId\": 193729,\n" +
										"  \"id\": \""+hyInvoiceRecord.getOriginId()+"\",\n" +
										"  \"plantForm\": 1,\n" +
										"  \"version\": "+version+",\n" +
										"  \"time\": \""+DateFormatUtils.format(actArriDate,"yyyy-MM-dd HH:mm")+"\",\n" +
										"  \"waybillNodeId\": "+wayBillNodeId+"\n" +
										"}")
								.execute().body();
						JSONObject object = JSONObject.parseObject(pickRes);
						if(object.getInteger("code") == 1){
							//成功
							zllgBackRecord.setSuccessFlag(0);
						}else{
							//提货到场失败
							zllgBackRecord.setSuccessFlag(1);
						}
						zllgBackRecord.setBackMemo(pickRes);
					}
				}else{
					zllgBackRecord.setSuccessFlag(1);
					zllgBackRecord.setBackMemo("获取提货节点失败："+listRes);
				}
				//保存操作信息
				zllgBackRecord.setId(IdUtil.simpleUUID());
				entrustMapper.insertZLLGBackRecord(zllgBackRecord);
			}
		}
	}

	/***
	 * 激活离场
	 * @param entrustId
	 * @param actLeaveDate
	 * @throws ParseException
	 */
	@Async
	public void zllgPickLC(String entrustId,Date actLeaveDate) throws ParseException {
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);

		//判断发货单被抓取过
		HYInvoiceRecord hyInvoiceRecord = invoiceMapper.selectSuccessHyInvoiceByInvoiceId(entrust.getOrderno());
		if(hyInvoiceRecord == null){
			hyInvoiceRecord = getHYInvoiceRecordByOrderNo(entrust);
		}
		if(hyInvoiceRecord != null && StringUtils.isNotBlank(hyInvoiceRecord.getOriginId())){

			String token = getZLLGToken(false);

			//保存操作信息
			ZLLGBackRecord zllgBackRecord = new ZLLGBackRecord();
			zllgBackRecord.setId(IdUtil.simpleUUID());
			zllgBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
			zllgBackRecord.setEntrustVbillno(entrust.getVbillno());
			zllgBackRecord.setCustOrderNo(hyInvoiceRecord.getCustOrderno());
			zllgBackRecord.setOriginNo(hyInvoiceRecord.getOriginNo());
			zllgBackRecord.setRegDate(new Date());

			Integer cnt = entrustMapper.selectZllgRecordSuccessCount(entrust.getInvoiceVbillno(), 2);
			if(cnt == 0){
				//节点号
				String wayBillNodeId = null;
				//如果派车成功开始提货操作

				//提货离场
				zllgBackRecord.setType(2);
				//获取提货节点
				String listRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/getWaybillNodeList")
						.header("Content-Type", "application/json;charset=UTF-8")
						.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
						.body("{\n" +
								"  \"corpId\": 193729,\n" +
								"  \"id\": \""+hyInvoiceRecord.getOriginId()+"\",\n" +
								"  \"plantForm\": 1,\n" +
								"  \"version\": 1729821652511\n" +
								"}")
						.execute().body();
				JSONObject parseObject = JSONObject.parseObject(listRes);
				if(parseObject.getInteger("code") == 1){
					JSONArray result = parseObject.getJSONArray("result");
					//获取节点
					for(int i = 0 ; i < result.size() ; i++){
						if(result.getJSONObject(i).getBoolean("selectFlag")) {
							wayBillNodeId = result.getJSONObject(i).getString("id");
						}
					}
					if(StringUtils.isBlank(wayBillNodeId)){
						//未找到提货节点
						zllgBackRecord.setSuccessFlag(1);
						zllgBackRecord.setBackMemo("未找到提货节点");
					}else{
						//提货离场
						String version = getVersion(hyInvoiceRecord.getOriginNo());
						String arriRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/leaveWaybillNode")
								.header("Content-Type", "application/json;charset=UTF-8")
								.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
								.body("{\n" +
										"  \"time\": \""+DateFormatUtils.format(actLeaveDate,"yyyy-MM-dd HH:mm")+"\",\n" +
										"  \"waybillNodeId\": "+wayBillNodeId+",\n" +
										"  \"remark\": \"\",\n" +
										"  \"urlList\": [],\n" +
										"  \"corpId\": 193729,\n" +
										"  \"id\": \""+hyInvoiceRecord.getOriginId()+"\",\n" +
										"  \"plantForm\": 1,\n" +
										"  \"version\": "+version+"\n" +
										"}")
								.execute().body();
						JSONObject object = JSONObject.parseObject(arriRes);
						if(object.getInteger("code") == 1) {
							//成功
							zllgBackRecord.setSuccessFlag(0);
						}else{
							//失败
							zllgBackRecord.setSuccessFlag(1);
						}
						zllgBackRecord.setBackMemo(arriRes);
						//保存操作信息
						zllgBackRecord.setId(IdUtil.simpleUUID());
						entrustMapper.insertZLLGBackRecord(zllgBackRecord);
					}
				}else{
					zllgBackRecord.setSuccessFlag(1);
					zllgBackRecord.setBackMemo("获取提货节点失败："+listRes);
				}
				//保存操作信息
				zllgBackRecord.setId(IdUtil.simpleUUID());
				entrustMapper.insertZLLGBackRecord(zllgBackRecord);
			}
		}
	}



	/**
	 * 中粮面业 派车
	 */
	@Async
	public void zllgArri(String entrustId,Date actArriDate,Date actLeaveDate) throws ParseException {
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);

		//判断发货单被抓取过
		HYInvoiceRecord hyInvoiceRecord = invoiceMapper.selectSuccessHyInvoiceByInvoiceId(entrust.getOrderno());
		if(hyInvoiceRecord == null){
			hyInvoiceRecord = getHYInvoiceRecordByOrderNo(entrust);
		}
		if(hyInvoiceRecord != null && StringUtils.isNotBlank(hyInvoiceRecord.getOriginId())){

			//判断是否提货确认成功
			Integer cnt = entrustMapper.selectZllgRecordSuccessCount(entrust.getInvoiceVbillno(), 3);
			if(cnt > 0){
				String token = getZLLGToken(false);

				//保存操作信息
				ZLLGBackRecord zllgBackRecord = new ZLLGBackRecord();
				zllgBackRecord.setId(IdUtil.simpleUUID());
				zllgBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
				zllgBackRecord.setEntrustVbillno(entrust.getVbillno());
				zllgBackRecord.setCustOrderNo(hyInvoiceRecord.getCustOrderno());
				zllgBackRecord.setOriginNo(hyInvoiceRecord.getOriginNo());
				zllgBackRecord.setRegDate(new Date());
				//到货进场
				zllgBackRecord.setType(4);

				String wayBillNodeId = null;
				//获取到货节点
				String listRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/getWaybillNodeList")
						.header("Content-Type", "application/json;charset=UTF-8")
						.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
						.body("{\n" +
								"  \"corpId\": 193729,\n" +
								"  \"id\": \""+hyInvoiceRecord.getOriginId()+"\",\n" +
								"  \"plantForm\": 1,\n" +
								"  \"version\": 1729821652511\n" +
								"}")
						.execute().body();
				JSONObject parseObject = JSONObject.parseObject(listRes);
				if(parseObject.getInteger("code") == 1){
					JSONArray result = parseObject.getJSONArray("result");
					//获取节点
					for(int i = 0 ; i < result.size() ; i++){
						if(result.getJSONObject(i).getBoolean("selectFlag")) {
							wayBillNodeId = result.getJSONObject(i).getString("id");
						}
					}
					if(StringUtils.isBlank(wayBillNodeId)){
						//未找到提货节点
						zllgBackRecord.setSuccessFlag(1);
						zllgBackRecord.setBackMemo("未找到到货节点");
					}else{
						String version = getVersion(hyInvoiceRecord.getOriginNo());
						//提货
						String pickRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/arriveWaybillNode")
								.header("Content-Type", "application/json;charset=UTF-8")
								.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
								.body("{\n" +
										"  \"corpId\": 193729,\n" +
										"  \"id\": \""+hyInvoiceRecord.getOriginId()+"\",\n" +
										"  \"plantForm\": 1,\n" +
										"  \"version\": "+version+",\n" +
										"  \"time\": \""+DateFormatUtils.format(actArriDate,"yyyy-MM-dd HH:mm")+"\",\n" +
										"  \"waybillNodeId\": "+wayBillNodeId+"\n" +
										"}")
								.execute().body();
						JSONObject object = JSONObject.parseObject(pickRes);
						if(object.getInteger("code") == 1){
							//成功
							zllgBackRecord.setSuccessFlag(0);
							zllgArrivalQR(entrustId,actLeaveDate,true);
						}else{
							//到货到场失败
							zllgBackRecord.setSuccessFlag(1);
						}
						zllgBackRecord.setBackMemo(pickRes);
					}
				}else{
					zllgBackRecord.setSuccessFlag(1);
					zllgBackRecord.setBackMemo("获取到货节点失败："+listRes);
				}
				//保存操作信息
				entrustMapper.insertZLLGBackRecord(zllgBackRecord);
			}
		}
	}


	/***
	 * 激活离场
	 * @param entrustId
	 * @throws ParseException
	 */
	@Async
	public void zllgPickQR(String entrustId) throws ParseException {
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);

		//判断发货单被抓取过
		HYInvoiceRecord hyInvoiceRecord = invoiceMapper.selectSuccessHyInvoiceByInvoiceId(entrust.getOrderno());
		if(hyInvoiceRecord == null){
			hyInvoiceRecord = getHYInvoiceRecordByOrderNo(entrust);
		}
		if(hyInvoiceRecord != null && StringUtils.isNotBlank(hyInvoiceRecord.getOriginId())){

			String token = getZLLGToken(false);

			//保存操作信息
			ZLLGBackRecord zllgBackRecord = new ZLLGBackRecord();
			zllgBackRecord.setId(IdUtil.simpleUUID());
			zllgBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
			zllgBackRecord.setEntrustVbillno(entrust.getVbillno());
			zllgBackRecord.setCustOrderNo(hyInvoiceRecord.getCustOrderno());
			zllgBackRecord.setOriginNo(hyInvoiceRecord.getOriginNo());
			zllgBackRecord.setRegDate(new Date());

			//提货确认
			zllgBackRecord.setType(3);


			Integer cnt = entrustMapper.selectZllgRecordSuccessCount(entrust.getInvoiceVbillno(), 3);
			if(cnt == 0){
				//节点号
				String wayBillNodeId = null;
				//如果派车成功开始提货操作


				//获取提货节点
				String getDeliveryCheckRes = HttpRequest.get("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybill/getDeliveryCheck?waybillId="+hyInvoiceRecord.getOriginId())
						.header("Content-Type", "application/json;charset=UTF-8")
						.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
						.execute().body();
				JSONObject parseObject = JSONObject.parseObject(getDeliveryCheckRes);
				if(parseObject.getInteger("code") == 1){
					wayBillNodeId = parseObject.getJSONObject("result").getString("waybillNodeId");
					if(StringUtils.isBlank(wayBillNodeId)){
						//未找到提货节点
						zllgBackRecord.setSuccessFlag(1);
						zllgBackRecord.setBackMemo("未找到确认节点");
					}else{

						//确认
						String commitRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybill/commitDeliveryCheck")
								.header("Content-Type", "application/json;charset=UTF-8")
								.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
								.body("{\n" +
										"  \"waybillId\": \""+hyInvoiceRecord.getOriginId()+"\",\n" +
										"  \"waybillNodeId\": "+wayBillNodeId+",\n" +
										"  \"deleteOrderIdList\": [],\n" +
										"  \"insertOrderIdList\": []\n" +
										"}")
								.execute().body();
						JSONObject object = JSONObject.parseObject(commitRes);
						if(object.getInteger("code") == 1) {
							//成功
							zllgBackRecord.setSuccessFlag(0);
						}else{
							//失败
							zllgBackRecord.setSuccessFlag(1);
						}
						zllgBackRecord.setBackMemo(commitRes);
						//保存操作信息
						zllgBackRecord.setId(IdUtil.simpleUUID());
					}
				}else{
					zllgBackRecord.setSuccessFlag(1);
					zllgBackRecord.setBackMemo("获取确认节点失败："+getDeliveryCheckRes);
				}
				//保存操作信息
				zllgBackRecord.setId(IdUtil.simpleUUID());
				entrustMapper.insertZLLGBackRecord(zllgBackRecord);

				if(zllgBackRecord.getSuccessFlag() == 0){
					//到货进场
					EntrustWork entrustWorkSel = new EntrustWork();
					entrustWorkSel.setEntrustId(entrust.getEntrustId());
					entrustWorkSel.setWorkType("2");
					List<EntrustWork> entrustWorks = entrustWorkMapper.selectEntrustWorkList(entrustWorkSel);
					if(entrustWorks != null && entrustWorks.size() > 0){
						EntrustWork entrustWork = entrustWorks.get(0);
						zllgArrivalJC(entrust.getEntrustId(),entrustWork.getActArriDate());
					}
				}

			}

		}
	}

	public void zllgArrivalJC(String entrustId,Date actArriDate) throws ParseException {
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);

		//判断发货单被抓取过
		HYInvoiceRecord hyInvoiceRecord = invoiceMapper.selectSuccessHyInvoiceByInvoiceId(entrust.getOrderno());
		if(hyInvoiceRecord == null){
			hyInvoiceRecord = getHYInvoiceRecordByOrderNo(entrust);
		}
		if(hyInvoiceRecord != null && StringUtils.isNotBlank(hyInvoiceRecord.getOriginId())){

			String token = getZLLGToken(false);

			//保存操作信息
			ZLLGBackRecord zllgBackRecord = new ZLLGBackRecord();
			zllgBackRecord.setId(IdUtil.simpleUUID());
			zllgBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
			zllgBackRecord.setEntrustVbillno(entrust.getVbillno());
			zllgBackRecord.setCustOrderNo(hyInvoiceRecord.getCustOrderno());
			zllgBackRecord.setOriginNo(hyInvoiceRecord.getOriginNo());
			zllgBackRecord.setRegDate(new Date());
			//到货进场
			zllgBackRecord.setType(4);

			Integer cnt = entrustMapper.selectZllgRecordSuccessCount(entrust.getInvoiceVbillno(), 4);
			if(cnt == 0){
				String wayBillNodeId = null;
				//获取到货节点
				String listRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/getWaybillNodeList")
						.header("Content-Type", "application/json;charset=UTF-8")
						.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
						.body("{\n" +
								"  \"corpId\": 193729,\n" +
								"  \"id\": \""+hyInvoiceRecord.getOriginId()+"\",\n" +
								"  \"plantForm\": 1,\n" +
								"  \"version\": 1729821652511\n" +
								"}")
						.execute().body();
				JSONObject parseObject = JSONObject.parseObject(listRes);
				if(parseObject.getInteger("code") == 1){
					JSONArray result = parseObject.getJSONArray("result");
					//获取节点
					for(int i = 0 ; i < result.size() ; i++){
						if(result.getJSONObject(i).getBoolean("selectFlag")) {
							wayBillNodeId = result.getJSONObject(i).getString("id");
						}
					}
					if(StringUtils.isBlank(wayBillNodeId)){
						//未找到提货节点
						zllgBackRecord.setSuccessFlag(1);
						zllgBackRecord.setBackMemo("未找到到货节点");
					}else{
						String version = getVersion(hyInvoiceRecord.getOriginNo());
						//提货
						String pickRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/arriveWaybillNode")
								.header("Content-Type", "application/json;charset=UTF-8")
								.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
								.body("{\n" +
										"  \"corpId\": 193729,\n" +
										"  \"id\": \""+hyInvoiceRecord.getOriginId()+"\",\n" +
										"  \"plantForm\": 1,\n" +
										"  \"version\": "+version+",\n" +
										"  \"time\": \""+DateFormatUtils.format(actArriDate,"yyyy-MM-dd HH:mm")+"\",\n" +
										"  \"waybillNodeId\": "+wayBillNodeId+"\n" +
										"}")
								.execute().body();
						JSONObject object = JSONObject.parseObject(pickRes);
						if(object.getInteger("code") == 1){
							//成功
							zllgBackRecord.setSuccessFlag(0);
						}else{
							//到货到场失败
							zllgBackRecord.setSuccessFlag(1);
						}
						zllgBackRecord.setBackMemo(pickRes);
					}
				}else{
					zllgBackRecord.setSuccessFlag(1);
					zllgBackRecord.setBackMemo("获取到货节点失败："+listRes);
				}
				//保存操作信息
				entrustMapper.insertZLLGBackRecord(zllgBackRecord);

				if(zllgBackRecord.getSuccessFlag() == 0){
					//到货确认
					EntrustWork entrustWorkSel = new EntrustWork();
					entrustWorkSel.setEntrustId(entrust.getEntrustId());
					entrustWorkSel.setWorkType("2");
					List<EntrustWork> entrustWorks = entrustWorkMapper.selectEntrustWorkList(entrustWorkSel);
					if(entrustWorks != null && entrustWorks.size() > 0){
						EntrustWork entrustWork = entrustWorks.get(0);
						zllgArrivalQR(entrust.getEntrustId(),entrustWork.getActLeaveDate(),true);
					}
				}

			}
		}
	}

	public void zllgLeave(String entrustId,Date actLeaveDate){
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);

		//判断发货单被抓取过
		HYInvoiceRecord hyInvoiceRecord = invoiceMapper.selectSuccessHyInvoiceByInvoiceId(entrust.getOrderno());
		if(hyInvoiceRecord == null){
			hyInvoiceRecord = getHYInvoiceRecordByOrderNo(entrust);
		}
		if(hyInvoiceRecord != null && StringUtils.isNotBlank(hyInvoiceRecord.getOriginId())){

			String token = getZLLGToken(false);

			//保存操作信息
			ZLLGBackRecord zllgBackRecord = new ZLLGBackRecord();
			zllgBackRecord.setId(IdUtil.simpleUUID());
			zllgBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
			zllgBackRecord.setEntrustVbillno(entrust.getVbillno());
			zllgBackRecord.setCustOrderNo(hyInvoiceRecord.getCustOrderno());
			zllgBackRecord.setOriginNo(hyInvoiceRecord.getOriginNo());
			zllgBackRecord.setRegDate(new Date());
			//到货进场
			zllgBackRecord.setType(7);

			//Integer cnt = entrustMapper.selectZllgRecordSuccessCount(entrust.getInvoiceVbillno(), 7);
			//if(cnt == 0){

				String listRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/getLeaveNodeInfoList")
						.header("Content-Type", "application/json;charset=UTF-8")
						.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
						.body("{\n" +
								"  \"corpId\": 193729,\n" +
								"  \"id\": \""+hyInvoiceRecord.getOriginId()+"\",\n" +
								"  \"plantForm\": 1,\n" +
								"  \"version\": 1730883438589\n" +
								"}")
						.execute().body();
				JSONObject object = JSONObject.parseObject(listRes);
				if(object.getInteger("code") == 1){
					String version = getVersion(hyInvoiceRecord.getOriginNo());
					String waybillNodeId = object.getJSONArray("result").getJSONObject(0).getString("id");
					String leaveRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/leaveWaybillNode")
							.header("Content-Type", "application/json;charset=UTF-8")
							.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
							.body("{\n" +
									"  \"time\": \""+DateFormatUtils.format(actLeaveDate,"yyyy-MM-dd HH:mm")+"\",\n" +
									"  \"waybillNodeId\": "+waybillNodeId+",\n" +
									"  \"remark\": \"\",\n" +
									"  \"urlList\": [],\n" +
									"  \"corpId\": 193729,\n" +
									"  \"id\": \""+hyInvoiceRecord.getOriginId()+"\",\n" +
									"  \"plantForm\": 1,\n" +
									"  \"version\": "+version+"\n" +
									"}")
							.execute().body();
					JSONObject leaveJson = JSONObject.parseObject(leaveRes);
					if(leaveJson.getInteger("code") == 1){
						zllgBackRecord.setSuccessFlag(0);
					}else{
						zllgBackRecord.setSuccessFlag(1);
					}
					zllgBackRecord.setBackMemo(leaveRes);
				}else{
					zllgBackRecord.setSuccessFlag(1);
					zllgBackRecord.setBackMemo("获取离开节点失败："+listRes);
				}
				//保存操作信息
				entrustMapper.insertZLLGBackRecord(zllgBackRecord);
			//}
		}
	}

	public void zllgArrivalQR(String entrustId,Date actLeaveDate,Boolean ifLeave) throws ParseException {
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);

		//判断发货单被抓取过
		HYInvoiceRecord hyInvoiceRecord = invoiceMapper.selectSuccessHyInvoiceByInvoiceId(entrust.getOrderno());
		if(hyInvoiceRecord == null){
			hyInvoiceRecord = getHYInvoiceRecordByOrderNo(entrust);
		}
		if(hyInvoiceRecord != null && StringUtils.isNotBlank(hyInvoiceRecord.getOriginId())){

			String token = getZLLGToken(false);

			//保存操作信息
			ZLLGBackRecord zllgBackRecord = new ZLLGBackRecord();
			zllgBackRecord.setId(IdUtil.simpleUUID());
			zllgBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
			zllgBackRecord.setEntrustVbillno(entrust.getVbillno());
			zllgBackRecord.setCustOrderNo(hyInvoiceRecord.getCustOrderno());
			zllgBackRecord.setOriginNo(hyInvoiceRecord.getOriginNo());
			zllgBackRecord.setRegDate(new Date());
			//到货进场
			zllgBackRecord.setType(5);

			Integer cnt = entrustMapper.selectZllgRecordSuccessCount(entrust.getInvoiceVbillno(), 5);
			if(cnt == 0){
				//获取节点
				String waybillRes = HttpRequest.get("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/getNodeListByAction?waybillId="+hyInvoiceRecord.getOriginId()+"&action=delivery_confirm")
						.header("Content-Type", "application/json;charset=UTF-8")
						.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
						.execute().body();
				JSONObject wayBillObject = JSONObject.parseObject(waybillRes);
				if(wayBillObject.getInteger("code") == 1 && wayBillObject.getJSONArray("result").size() != 0){
					String waybillNodeId = wayBillObject.getJSONArray("result").getJSONObject(0).getString("id");

					String commitRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/getGoodsInfoBeforeDelivery")
							.header("Content-Type", "application/json;charset=UTF-8")
							.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
							.body("{\n" +
									"  \"corpId\": 193729,\n" +
									"  \"id\": \""+hyInvoiceRecord.getOriginId()+"\",\n" +
									"  \"plantForm\": 1,\n" +
									"  \"version\": 1730278535898,\n" +
									"  \"waybillNodeId\": "+waybillNodeId+"\n" +
									"}")
							.execute().body();
					JSONObject object = JSONObject.parseObject(commitRes);
					if(object.getInteger("code") == 1){
						JSONObject result = object.getJSONObject("result");
						JSONArray orderLoadList = result.getJSONArray("orderLoadList");
						for(int i = 0; i < orderLoadList.size(); i++){
							//更新值
							JSONObject jsonObject = orderLoadList.getJSONObject(i);
							JSONArray goodsLoadVOS = jsonObject.getJSONArray("goodsLoadVOS");
							for(int j = 0 ; j < goodsLoadVOS.size(); j++){
								JSONObject goodVo = goodsLoadVOS.getJSONObject(j);
								goodVo.put("deliveryTotalAmount",goodVo.getBigDecimal("actualTotalAmount"));
								goodVo.put("deliveryTotalWeight",goodVo.getBigDecimal("actualTotalWeight"));
							}
						}
						String version = getVersion(hyInvoiceRecord.getOriginNo());

						result.put("plantForm",1);
						result.put("temp",-999);
						result.put("time",actLeaveDate.getTime());
						result.put("urlType",2);
						result.put("version",version);

						String comfirmRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/confirmDelivery")
								.header("Content-Type", "application/json;charset=UTF-8")
								.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
								.body(result.toJSONString())
								.execute().body();
						object = JSONObject.parseObject(commitRes);
						if(object.getInteger("code") == 1){
							zllgBackRecord.setSuccessFlag(0);
							if(ifLeave){
								zllgReceipt(entrustId);
								zllgLeave(entrustId,actLeaveDate);
							}
						}else{
							zllgBackRecord.setSuccessFlag(1);
						}
						zllgBackRecord.setBackMemo(comfirmRes);
					}else{
						zllgBackRecord.setSuccessFlag(1);
						zllgBackRecord.setBackMemo("获取确认货品信息失败："+commitRes);
					}
				}else{
					zllgBackRecord.setSuccessFlag(1);
					zllgBackRecord.setBackMemo("获取确认货节点失败："+waybillRes);
				}
				//保存操作信息
				entrustMapper.insertZLLGBackRecord(zllgBackRecord);
			}
		}
	}


	public String getVersion(String waybillNo){
		String token = getZLLGToken(false);

		Date date = new Date();
		long time1 = date.getTime();
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.DAY_OF_MONTH,-15);
		long time0 = cal.getTimeInMillis();

		String listRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybill/pageQueryForTrace")
				.header("Content-Type", "application/json;charset=UTF-8")
				.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
				.body("{\n" +
						"  \"waybillNo\": \""+waybillNo+"\",\n" +
						"  \"transportPlanNo\": \"\",\n" +
						"  \"mainStatusList\": [],\n" +
						"  \"coStatusList\": [],\n" +
						"  \"createTimeList\": [\n" +
						"    "+time0+",\n" +
						"    "+time1+"\n" +
						"  ],\n" +
						"  \"waybillName\": \"\",\n" +
						"  \"vehicleIdList\": [],\n" +
						"  \"mainDriverIdList\": [],\n" +
						"  \"mainDriverPhone\": \"\",\n" +
						"  \"zch\": \"\",\n" +
						"  \"departureStation\": \"\",\n" +
						"  \"arrivalStation\": \"\",\n" +
						"  \"customAreaIdList\": [],\n" +
						"  \"courierNo\": \"\",\n" +
						"  \"salesNo\": \"\",\n" +
						"  \"purchaseOrderNo\": \"\",\n" +
						"  \"transferOrderNo\": \"\",\n" +
						"  \"railwayWaybillNo\": \"\",\n" +
						"  \"freightBillNo\": \"\",\n" +
						"  \"waybillCustomerName\": \"\",\n" +
						"  \"carrierName\": \"\",\n" +
						"  \"orderNo\": \"\",\n" +
						"  \"dispatchTimeList\": [],\n" +
						"  \"assignCarTimeList\": [],\n" +
						"  \"sendCarTimeList\": [],\n" +
						"  \"lineCode\": \"\",\n" +
						"  \"transportMode\": \"\",\n" +
						"  \"carrierIdList\": [],\n" +
						"  \"waybillDepartmentIdList\": [],\n" +
						"  \"additionalRequirementList\": [],\n" +
						"  \"customerIdList\": [],\n" +
						"  \"goodsTypeList\": [],\n" +
						"  \"terminalIdList\": [],\n" +
						"  \"actualDoneTimeList\": [],\n" +
						"  \"lineIdList\": [],\n" +
						"  \"deliveryIdList\": [],\n" +
						"  \"delegateStatus\": \"\",\n" +
						"  \"vehicleOrigin\": [],\n" +
						"  \"dispatchMode\": [],\n" +
						"  \"leaveFactoryTime\": [],\n" +
						"  \"extendMap\": {},\n" +
						"  \"pageParamNewVO\": {\n" +
						"    \"curPage\": 1,\n" +
						"    \"pageSize\": 20,\n" +
						"    \"sortDir\": \"\",\n" +
						"    \"sortIndx\": \"\"\n" +
						"  },\n" +
						"  \"operateStatusList\": [\n" +
						"    \"\"\n" +
						"  ]\n" +
						"}")
				.execute().body();
		logger.error(listRes);
		JSONObject parseObject = JSONObject.parseObject(listRes);
		if(parseObject.getInteger("code") == 104){
			getZLLGToken(true);
			return null;
		}else{
			return parseObject.getJSONObject("result").getJSONArray("data").getJSONObject(0).getString("version");
		}

	}


	public String getZLCarIdByCarNo(String carNo){
		String token = getZLLGToken(false);
		String listRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybill/pageQueryVehicleInfo")
				.header("Content-Type", "application/json;charset=UTF-8")
				.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
				.body("{\n" +
						"  \"vehicleNo\": \""+carNo+"\",\n" +
						"  \"checkTask\": 1,\n" +
						"  \"pageParamNewVO\": {\n" +
						"    \"curPage\": 1,\n" +
						"    \"pageSize\": 200\n" +
						"  }\n" +
						"}")
				.execute().body();
		System.out.println(listRes);
		JSONArray carArray = JSONObject.parseObject(listRes).getJSONObject("result").getJSONArray("data");
		if(carArray.size() > 0){
			return carArray.getJSONObject(0).getString("id");
		}
		return null;
	}

	public String insertZlCar(String carNo){
		String token = getZLLGToken(false);
		String listRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BASE-WEB/vehicleInfo/insertInfo")
				.header("Content-Type", "application/json;charset=UTF-8")
				.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
				.body("{\n" +
						"  \"customName\": \""+carNo+"\",\n" +
						"  \"vehicleID\": \"\",\n" +
						"  \"velProperties\": 2,\n" +
						"  \"vehicleTypeGb\": \"\",\n" +
						"  \"vehicleBrand\": \"\",\n" +
						"  \"vehicleType\": \"\",\n" +
						"  \"departmentId\": [\n" +
						"    -163343,\n" +
						"    null\n" +
						"  ],\n" +
						"  \"engineNo\": \"\",\n" +
						"  \"vln\": \"\",\n" +
						"  \"vlEffectiveTime\": \"\",\n" +
						"  \"overallLengthDouble\": \"\",\n" +
						"  \"overallWidthDouble\": \"\",\n" +
						"  \"overallHeightDouble\": \"\",\n" +
						"  \"approvedLoadDouble\": \"\",\n" +
						"  \"driverList\": [],\n" +
						"  \"carrierList\": [\n" +
						"    {\n" +
						"      \"carrierId\": \"993167545450409984\",\n" +
						"      \"businessCarrier\": 1\n" +
						"    }\n" +
						"  ],\n" +
						"  \"selectedCarrier\": [\n" +
						"    \"993167545450409984\"\n" +
						"  ],\n" +
						"  \"orgCarrierList\": [],\n" +
						"  \"grossMassDouble\": \"\",\n" +
						"  \"licenseFaceUrl\": \"\",\n" +
						"  \"licenseBackUrl\": \"\",\n" +
						"  \"roadTransport\": \"\",\n" +
						"  \"vehicleColor\": \"\",\n" +
						"  \"possessor\": \"\",\n" +
						"  \"property\": \"\",\n" +
						"  \"identifyCode\": \"\",\n" +
						"  \"buyTime\": \"\",\n" +
						"  \"issueDate\": \"\",\n" +
						"  \"issueAuthority\": \"\",\n" +
						"  \"energyType\": \"\",\n" +
						"  \"rtpn\": \"\",\n" +
						"  \"insuranceCode\": \"\",\n" +
						"  \"insuranceCompany\": \"\",\n" +
						"  \"owner\": \"\",\n" +
						"  \"ownerTel\": \"\",\n" +
						"  \"deliveryIds\": [],\n" +
						"  \"otherPicList\": [],\n" +
						"  \"carLengthId\": \"\",\n" +
						"  \"carStatusId\": 1,\n" +
						"  \"extendMap\": {\n" +
						"    \"ext_str1\": \"\",\n" +
						"    \"ext_str2\": [],\n" +
						"    \"ext_str3\": \"\",\n" +
						"    \"ext_str4\": \"\",\n" +
						"    \"ext_str5\": \"\",\n" +
						"    \"ext_str6\": \"\",\n" +
						"    \"ext_str7\": [],\n" +
						"    \"ext_str8\": [],\n" +
						"    \"ext_str9\": \"\",\n" +
						"    \"ext_str10\": 0\n" +
						"  },\n" +
						"  \"carrierId\": \"993167545450409984\"\n" +
						"}")
				.execute().body();
		logger.error(listRes);
		JSONObject object = JSONObject.parseObject(listRes);
		if(object.getInteger("code") == 1){
			return  object.getJSONObject("result").getString("id");
		}
		return null;
	}

	public String getDriverIdByCardId(String cardId){
		String token = getZLLGToken(false);
		String listRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BASE-WEB/drivers/listDriversByPage")
				.header("Content-Type", "application/json;charset=UTF-8")
				.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
				.body("{\n" +
						"  \"name\": \"\",\n" +
						"  \"phone\": \"\",\n" +
						"  \"idCard\": \""+cardId+"\",\n" +
						"  \"sex\": \"\",\n" +
						"  \"departmentIds\": [],\n" +
						"  \"carrierIds\": [],\n" +
						"  \"loginStatus\": [],\n" +
						"  \"relyOnObjList\": [],\n" +
						"  \"pageParamNewVO\": {\n" +
						"    \"curPage\": 1,\n" +
						"    \"pageSize\": 20\n" +
						"  }\n" +
						"}")
				.execute().body();
		JSONObject object = JSONObject.parseObject(listRes);
		if(object.getInteger("code") == 1){
			JSONArray carArray = object.getJSONObject("result").getJSONArray("data");
			if(carArray.size() > 0){
				return  carArray.getJSONObject(0).getString("id");
			}
		}
		return null;
	}

	public String insertZLDriver(String name,String phone,String idCard){
		String token = getZLLGToken(false);
		String listRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BASE-WEB/drivers/saveDriver")
				.header("Content-Type", "application/json;charset=UTF-8")
				.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
				.body("{\n" +
						"  \"driverId\": \"\",\n" +
						"  \"version\": \"\",\n" +
						"  \"corpId\": \"\",\n" +
						"  \"departmentIds\": [],\n" +
						"  \"selectedCarrier\": [\n" +
						"    \"993167545450409984\"\n" +
						"  ],\n" +
						"  \"carrierId\": [\n" +
						"    \"993167545450409984\"\n" +
						"  ],\n" +
						"  \"name\": \""+name+"\",\n" +
						"  \"phone\": \""+phone+"\",\n" +
						"  \"idCard\": \""+idCard+"\",\n" +
						"  \"sex\": 1,\n" +
						"  \"age\": \"\",\n" +
						"  \"driverAge\": \"\",\n" +
						"  \"licenseLevelInt\": \"\",\n" +
						"  \"dqcn\": \"\",\n" +
						"  \"idCardPic\": \"\",\n" +
						"  \"idCardPic2\": \"\",\n" +
						"  \"licensePic\": \"\",\n" +
						"  \"licensePic2\": \"\",\n" +
						"  \"licenseBTime\": \"\",\n" +
						"  \"licenseETime\": \"\",\n" +
						"  \"accountNum\": \"\",\n" +
						"  \"bankTypeId\": \"\",\n" +
						"  \"openBankName\": \"\",\n" +
						"  \"nvqPic\": \"\",\n" +
						"  \"relyOnObjInt\": 2,\n" +
						"  \"lssuingAuthority\": \"\",\n" +
						"  \"accountName\": \"\",\n" +
						"  \"otherPicList\": [],\n" +
						"  \"extendMap\": {}\n" +
						"}")
				.execute().body();
		System.out.println(listRes);
		JSONObject object = JSONObject.parseObject(listRes);
		if(object.getInteger("code") == 1){
			//司机ID
			return object.getString("result");
		}
		return null;
	}

	//中粮回单
	@Async
	public void zllgReceipt(String entrustId) {
		Entrust entrust = entrustMapper.selectEntrustById(entrustId);

		//判断发货单被抓取过
		HYInvoiceRecord hyInvoiceRecord = invoiceMapper.selectSuccessHyInvoiceByInvoiceId(entrust.getOrderno());
		if(hyInvoiceRecord == null){
			hyInvoiceRecord = getHYInvoiceRecordByOrderNo(entrust);
		}
		if (hyInvoiceRecord != null && StringUtils.isNotBlank(hyInvoiceRecord.getOriginId())) {

			String token = getZLLGToken(false);

			//保存操作信息
			ZLLGBackRecord zllgBackRecord = new ZLLGBackRecord();
			zllgBackRecord.setId(IdUtil.simpleUUID());
			zllgBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
			zllgBackRecord.setEntrustVbillno(entrust.getVbillno());
			zllgBackRecord.setCustOrderNo(hyInvoiceRecord.getCustOrderno());
			zllgBackRecord.setOriginNo(hyInvoiceRecord.getOriginNo());
			zllgBackRecord.setRegDate(new Date());
			//到货进场
			zllgBackRecord.setType(6);

			Integer cnt = entrustMapper.selectZllgRecordSuccessCount(entrust.getInvoiceVbillno(), 6);
			if(cnt == 0){
				if (hyInvoiceRecord.getCustOrderno().contains(",")) {
					zllgBackRecord.setSuccessFlag(1);
					zllgBackRecord.setBackMemo("存在多个客户单号无法上传回单");
				} else {
					//判断是否需要回单
					Date date = new Date();
					long time1 = date.getTime();
					Calendar cal = Calendar.getInstance();
					cal.setTime(date);
					cal.add(Calendar.DAY_OF_MONTH, -15);
					long time0 = cal.getTimeInMillis();
					String listRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybill/pageQueryForTrace")
							.header("Content-Type", "application/json;charset=UTF-8")
							.header("Cookie", "JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken=" + token + "; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
							.body("{\n" +
									"  \"waybillNo\": \"" + hyInvoiceRecord.getOriginNo() + "\",\n" +

									"  \"transportPlanNo\": \"\",\n" +
									"  \"mainStatusList\": [],\n" +
									"  \"coStatusList\": [],\n" +
									"  \"createTimeList\": [\n" +
									"    " + time0 + ",\n" +
									"    " + time1 + "\n" +
									"  ],\n" +
									"  \"waybillName\": \"\",\n" +
									"  \"vehicleIdList\": [],\n" +
									"  \"mainDriverIdList\": [],\n" +
									"  \"mainDriverPhone\": \"\",\n" +
									"  \"zch\": \"\",\n" +
									"  \"departureStation\": \"\",\n" +
									"  \"arrivalStation\": \"\",\n" +
									"  \"customAreaIdList\": [],\n" +
									"  \"courierNo\": \"\",\n" +
									"  \"salesNo\": \"\",\n" +
									"  \"purchaseOrderNo\": \"\",\n" +
									"  \"transferOrderNo\": \"\",\n" +
									"  \"railwayWaybillNo\": \"\",\n" +
									"  \"freightBillNo\": \"\",\n" +
									"  \"waybillCustomerName\": \"\",\n" +
									"  \"carrierName\": \"\",\n" +
									"  \"orderNo\": \"\",\n" +
									"  \"dispatchTimeList\": [],\n" +
									"  \"assignCarTimeList\": [],\n" +
									"  \"sendCarTimeList\": [],\n" +
									"  \"lineCode\": \"\",\n" +
									"  \"transportMode\": \"\",\n" +
									"  \"carrierIdList\": [],\n" +
									"  \"waybillDepartmentIdList\": [],\n" +
									"  \"additionalRequirementList\": [],\n" +
									"  \"customerIdList\": [],\n" +
									"  \"goodsTypeList\": [],\n" +
									"  \"terminalIdList\": [],\n" +
									"  \"actualDoneTimeList\": [],\n" +
									"  \"lineIdList\": [],\n" +
									"  \"deliveryIdList\": [],\n" +
									"  \"delegateStatus\": \"\",\n" +
									"  \"vehicleOrigin\": [],\n" +
									"  \"dispatchMode\": [],\n" +
									"  \"leaveFactoryTime\": [],\n" +
									"  \"extendMap\": {},\n" +
									"  \"pageParamNewVO\": {\n" +
									"    \"curPage\": 1,\n" +
									"    \"pageSize\": 20,\n" +
									"    \"sortDir\": \"\",\n" +
									"    \"sortIndx\": \"\"\n" +
									"  },\n" +
									"  \"operateStatusList\": [\n" +
									"    \"10002\"\n" +
									"  ]\n" +
									"}")
							.execute().body();
					JSONObject parseObject = JSONObject.parseObject(listRes);
					if (parseObject.getInteger("code") == 1) {
						if(parseObject.getJSONObject("result").getJSONArray("data").size() == 0){
							//重新查已完成的
							listRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybill/pageQueryForTrace")
									.header("Content-Type", "application/json;charset=UTF-8")
									.header("Cookie", "JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken=" + token + "; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
									.body("{\n" +
											"  \"waybillNo\": \"" + hyInvoiceRecord.getOriginNo() + "\",\n" +

											"  \"transportPlanNo\": \"\",\n" +
											"  \"mainStatusList\": [],\n" +
											"  \"coStatusList\": [],\n" +
											"  \"createTimeList\": [\n" +
											"    " + time0 + ",\n" +
											"    " + time1 + "\n" +
											"  ],\n" +
											"  \"waybillName\": \"\",\n" +
											"  \"vehicleIdList\": [],\n" +
											"  \"mainDriverIdList\": [],\n" +
											"  \"mainDriverPhone\": \"\",\n" +
											"  \"zch\": \"\",\n" +
											"  \"departureStation\": \"\",\n" +
											"  \"arrivalStation\": \"\",\n" +
											"  \"customAreaIdList\": [],\n" +
											"  \"courierNo\": \"\",\n" +
											"  \"salesNo\": \"\",\n" +
											"  \"purchaseOrderNo\": \"\",\n" +
											"  \"transferOrderNo\": \"\",\n" +
											"  \"railwayWaybillNo\": \"\",\n" +
											"  \"freightBillNo\": \"\",\n" +
											"  \"waybillCustomerName\": \"\",\n" +
											"  \"carrierName\": \"\",\n" +
											"  \"orderNo\": \"\",\n" +
											"  \"dispatchTimeList\": [],\n" +
											"  \"assignCarTimeList\": [],\n" +
											"  \"sendCarTimeList\": [],\n" +
											"  \"lineCode\": \"\",\n" +
											"  \"transportMode\": \"\",\n" +
											"  \"carrierIdList\": [],\n" +
											"  \"waybillDepartmentIdList\": [],\n" +
											"  \"additionalRequirementList\": [],\n" +
											"  \"customerIdList\": [],\n" +
											"  \"goodsTypeList\": [],\n" +
											"  \"terminalIdList\": [],\n" +
											"  \"actualDoneTimeList\": [],\n" +
											"  \"lineIdList\": [],\n" +
											"  \"deliveryIdList\": [],\n" +
											"  \"delegateStatus\": \"\",\n" +
											"  \"vehicleOrigin\": [],\n" +
											"  \"dispatchMode\": [],\n" +
											"  \"leaveFactoryTime\": [],\n" +
											"  \"extendMap\": {},\n" +
											"  \"pageParamNewVO\": {\n" +
											"    \"curPage\": 1,\n" +
											"    \"pageSize\": 20,\n" +
											"    \"sortDir\": \"\",\n" +
											"    \"sortIndx\": \"\"\n" +
											"  },\n" +
											"  \"operateStatusList\": [\n" +
											"    \"500\"\n" +
											"  ]\n" +
											"}")
									.execute().body();
							parseObject = JSONObject.parseObject(listRes);
						}
						if (parseObject.getJSONObject("result").getJSONArray("data").size() != 0) {
							//获取版本信息
							String beforeReceiptRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/getOrderBeforeReceipt")
									.header("Content-Type", "application/json;charset=UTF-8")
									.header("Cookie","JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
									.body("{\n" +
											"  \"corpId\": 193729,\n" +
											"  \"id\": \""+hyInvoiceRecord.getOriginId()+"\",\n" +
											"  \"plantForm\": 1,\n" +
											"  \"version\": 1730883438589\n" +
											"}")
									.execute().body();
							JSONObject beforeReceiptResJson = JSONObject.parseObject(beforeReceiptRes);
							if(beforeReceiptResJson.getInteger("code") ==1){
								JSONObject receiptObject = beforeReceiptResJson.getJSONObject("result");

								Map<String, String> headersPost = new HashMap<>();
								headersPost.put("Accept", "*/*");
								headersPost.put("accept-encoding", "gzip, deflate, br, zstd");
								headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
								headersPost.put("Connection", "keep-alive");
								headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryZjbmiGmr8NpKby2v");
								headersPost.put("Cookie", "lang=zh-CN; PWD_EXPIRED_INFO=; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; RequestToken=" + token + "; WebgisCertValue=nvh8fxIiIbXrZb+WrKoT+1du6gGtBgIhq7s9YYSeO6S8nv7+t1bwn8fXuqPGpgXsVz8zq1HnDbmI/AB9Hy8toBVcEd9Y2t+6; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw19UIUgZYwbob/EPWFiBJeA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj__dQTf5irqNSWlRQB4O0FfZYBxr73ORIF0QBKzsp-ZJ0B-_cBQ79eEJ20UQYvdjjb9l_OHNzSxrwzqTTR3z7ZvlgVFtoeLCoVrhHPn3vytAFxHuP31XpnSmmhA1TFdQrbR7hauTf7q9p2qyttDUAy7YbW6gq16p3C4WsGgdmJ1tsPUhyswY9FunZcQO3gxbRQ");
								headersPost.put("Host", "lgtms.cofco.com");
								headersPost.put("Origin", "https://lgtms.cofco.com");
								headersPost.put("referer", "https://lgtms.cofco.com/");
								headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36");
								headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
								headersPost.put("sec-ch-ua-mobile", "?0");
								headersPost.put("sec-ch-ua-platform", "\"Windows\"");
								headersPost.put("token", token);

								//图片数组
								JSONArray urlArray = receiptObject.getJSONArray("orderList").getJSONObject(0).getJSONArray("urlList");

								String picUploadURL = "https://lgtms.cofco.com/api3/E6-MS-TMS-SUPPORT-WEB/api/upload/fildUploadPublicByInputStream?customerId=e6yun3.0&businessTypeKey=waybillPlatform";
								//上传回单附件
								List<SysUploadFile> sysUploadFiles = entrustWorkMapper.selectReceiptPicByEntrustId(entrust.getEntrustId());

								for (SysUploadFile sysUploadFile : sysUploadFiles) {
									String filePath = "D:/" + sysUploadFile.getFilePath();

									HttpRequest request = HttpRequest.post(picUploadURL)
											.headerMap(headersPost, true)
											.form("files", new File(filePath));

									JSONObject fileUploadRes = JSONObject.parseObject(request.execute().body());
									if (fileUploadRes.getInteger("code") == 1) {
										String fileUrl = fileUploadRes.getJSONArray("result").getJSONObject(0).getString("fileUrl");
										urlArray.add(fileUrl);
									} else {
										zllgBackRecord.setSuccessFlag(1);
										zllgBackRecord.setBackMemo("回单上传失败：" + request.execute().body());
									}
								}

								receiptObject.put("corpId", "193729");
								receiptObject.put("plantForm", "1");


								String receiptRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybillAction/receipt")
										.header("Content-Type", "application/json;charset=UTF-8")
										.header("Cookie", "JSESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken=" + token + "; WebgisCertValue=xOgy+8l5lS3Mh7mSOZPr2jZusS8UIBkDE8bYaLfAbDP8og0usQDJ814va9sBGb6aY1jUWpeMrgdzYNOGnVBw5uo4af57m8ab; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw6EkX6FX7nYqsgHoCuzmRIk%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj9vM_JVtAan0E9ueZ5wHZ5Q0Xs8gMDQl_lFbcQV-VC4PLPaiZLtKKlf1KfzA91cVDMHo2MInAMSVfteD59RPPSVo99PG0W972jHgmyze6ala673jJLZcA1tOShD69XM2NYbUDcA4VCRGN826pFGQuxAQdoq3LpuMlAb1yb0dEMzR")
										.body(receiptObject.toJSONString())
										.execute().body();
								JSONObject object = JSONObject.parseObject(receiptRes);
								if (object.getInteger("code") == 1) {
									zllgBackRecord.setSuccessFlag(0);
								} else {
									zllgBackRecord.setSuccessFlag(1);
								}
								zllgBackRecord.setBackMemo(receiptRes);

							}else{
								zllgBackRecord.setSuccessFlag(1);
								zllgBackRecord.setBackMemo("未获取到回单信息："+beforeReceiptRes);
							}
						} else {
							zllgBackRecord.setSuccessFlag(1);
							zllgBackRecord.setBackMemo("单据未进行到可回单状态");
						}
					}
				}
				//保存操作信息
				entrustMapper.insertZLLGBackRecord(zllgBackRecord);
			}
		}
	}

	public HYInvoiceRecord getHYInvoiceRecordByOrderNo(Entrust entrust){
		Invoice invoice = invoiceMapper.selectInvoiceById(entrust.getOrderno());

		HYInvoiceRecord hyInvoiceRecord = new HYInvoiceRecord();
		if(StringUtils.isBlank(invoice.getCustOrderno())){
			return hyInvoiceRecord;
		}
		//获取Token
		String token = getZLLGToken(false);

		//请求列表
		Map<String, Object> listParams = new HashMap<String, Object>();
		Long[] createTimeList = new Long[2];
		Date date = new Date();
		createTimeList[1] = date.getTime();
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.DAY_OF_MONTH,-30);
		createTimeList[0] = cal.getTimeInMillis();
		listParams.put("createTimeList",createTimeList);

		Map<String,Object> pageParamNewVO = new HashMap<>();
		pageParamNewVO.put("curPage",1);
		pageParamNewVO.put("pageSize",20);
		listParams.put("pageParamNewVO",pageParamNewVO);
		listParams.put("waybillType",1);
		listParams.put("orderNo",invoice.getCustOrderno());
		String listRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybill/pageQueryForManage")
				.header("Content-Type", "application/json;charset=UTF-8")
				.header("Cookie","SESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=2njHggGIDHtKg2+fQflEPwl9ZpP4mYhbwHxY0/qD5qvPgV0mU8t/QxfgMsiAaOYHznXFA51FPKnZo6ydk173bryrpE7DLy38; SECKEY_ABVK=mzuODm9MRee89OdNtrQWwxJFc/6zdtFVW+6o4efgDCQ%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj1-9TGqJLbfcywSbdUQrSHPU_AGZhwDYOX1Fc4hvbU93zmfNDR6ByM20tBuVhKcPvPij4MysSn8U9yebQsjSDCFqeMrvJXjz15TBmzLXaSEcC9ZB2rYr5PLFkFswlRw3qemMCdgYKK4PvezfYTyN706stSESWyKKlMDV8n2eSbUpvwbsYFTAVPbRkFCt88xTIA")
				.body(JSON.toJSONString(listParams))
				.execute().body();
		JSONArray listArr = JSONObject.parseObject(listRes).getJSONObject("result").getJSONArray("data");
		if(listArr.size() != 0){
			JSONObject para = listArr.getJSONObject(0);
			hyInvoiceRecord.setOriginId(para.getString("id"));
			hyInvoiceRecord.setOriginNo(para.getString("waybillNo"));
			hyInvoiceRecord.setCustOrderno(para.getString("orderNos"));
		}
		return hyInvoiceRecord;
	}



	public void sunshineUploadCarPic(Entrust entrust,String status,String gids,String spNum,String token){
		//保存信息
		SunshineBackRecord sunshineBackRecord = new SunshineBackRecord();
		sunshineBackRecord.setId(IdUtil.simpleUUID());
		sunshineBackRecord.setEntrustVbillno(entrust.getVbillno());
		sunshineBackRecord.setInvoiceVbillno(entrust.getInvoiceVbillno());
		sunshineBackRecord.setRegDate(new Date());
		sunshineBackRecord.setType(6);
		sunshineBackRecord.setCustOrderNo(entrust.getCustOrderno());

		if("委托".equals(status) || "接受".equals(status)){
			//查询车辆
			if(StringUtils.isBlank(entrust.getCarnoId())){
				sunshineBackRecord.setSuccessFlag(1);
				sunshineBackRecord.setBackMemo("单据车辆不存在");
			}else{
				//行驶证
				List<CarPicVO> carPicVOS = carMapper.selectCarPicByCarIdAndPicType(entrust.getCarnoId(),"1");
				if(carPicVOS == null || carPicVOS.size() == 0){
					sunshineBackRecord.setSuccessFlag(1);
					sunshineBackRecord.setBackMemo("车辆行驶证不存在");
				}else{
					String picUploadURL = "https://tms.sungrowpower.com/SCM.Configration.WebUI/AttachmentInfo/Upload";

					Map<String, String> headersPost = new HashMap<>();
					headersPost.put("Accept", "*/*");
					headersPost.put("Accept-Encoding","gzip, deflate, br, zstd");
					headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
					headersPost.put("Connection", "keep-alive");
					headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryjfPDtu6h3wMxaApO");
					headersPost.put("Cookie", "cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; Lang=zh-cn; SSID="+token+"; PowerTmsCookie=TmsUserID%3DFTvM9f4X5v89fLttSlA3OQ%3D%3D");
					headersPost.put("Origin", "https://tms.sungrowpower.com");
					headersPost.put("Referer", "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/Edit?guid=SALES.POD060308");
					headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
					headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
					headersPost.put("sec-ch-ua-mobile", "?0");
					headersPost.put("sec-ch-ua-platform", "\"Windows\"");

					Map<String,Object> sendParams = new HashMap<String,Object>();
					sendParams.put("Sys","TMS");
					sendParams.put("Module","Shipment");
					sendParams.put("Id",spNum);
					sendParams.put("AttachType","行驶证");
					sendParams.put("Relatedtype","20");
					sendParams.put("RelatedKey",gids);
					sendParams.put("PrimaryId",gids);

					CarPicVO sysUploadFile = carPicVOS.get(0);
					String filePath = "D:/" + sysUploadFile.getFilePath();
					File file = new File(filePath);
					String fileExtension = filePath.substring(filePath.lastIndexOf("."));
					String newFileName = entrust.getCarno()+fileExtension;

					HttpRequest request = HttpRequest.post(picUploadURL)
							.headerMap(headersPost, true)
							.form(sendParams)
							.form("files",file,newFileName);
					String uploadRes = request.execute().body();
					logger.debug(entrust.getCarno()+"车辆行驶证上传结果：{}",uploadRes);

					JSONObject uploadJson = JSONObject.parseObject(uploadRes);

					//上传成功调用
					if(uploadRes.contains("true")){
						String afterUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Attachment/AfterPartialUpdateData";
						sendParams = new HashMap<>();
						sendParams.put("Sys","TMS");
						sendParams.put("Module","Shipment");
						sendParams.put("Id",spNum);
						sendParams.put("AttachType","行驶证");
						sendParams.put("Relatedtype","20");
						sendParams.put("RelatedKey",gids);
						sendParams.put("PrimaryId",gids);
						sendParams.put("AttachmenIds",uploadJson.getJSONArray("Msg"));


						uploadRes = HttpRequest.post(afterUrl)
								.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
								.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
								.form(sendParams).execute().body();

						if(uploadRes.contains("true")){
							sunshineBackRecord.setSuccessFlag(0);
							sunshineBackRecord.setBackMemo(uploadRes);
						}else{
							sunshineBackRecord.setSuccessFlag(1);
							sunshineBackRecord.setBackMemo("上传车辆行驶证失败(二次回调)");
						}
					}else{
						sunshineBackRecord.setSuccessFlag(1);
						sunshineBackRecord.setBackMemo("上传车辆行驶证失败");
					}
				}
			}
		}else{
			sunshineBackRecord.setSuccessFlag(1);
			sunshineBackRecord.setBackMemo("单据状态为："+status+"，无法上传车辆行驶证");
		}

		//保存回调信息
		entrustMapper.insertSunshineBackRecord(sunshineBackRecord);
	}

	/**
	 * 上传接收状态车辆照片信息
	 */
	public AjaxResult sunshineAcceptCarPicUpload(){
		//保存信息
		SunshineBackRecord sunshineBackRecord = new SunshineBackRecord();
		sunshineBackRecord.setRegDate(new Date());
		sunshineBackRecord.setType(6);


		StringBuffer sb = new StringBuffer();

		String token = getSunshineToken(true);

		SimpleDateFormat sdfDay = new SimpleDateFormat("yy/MM/dd");
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		cal.add(Calendar.MONTH,-3);
		String dayStr = sdfDay.format(cal.getTime());

		String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/GetShipmentInfos?defsort=CREATED_DATE%20DESC";
		Map<String,Object> listParams = new HashMap<>();
		listParams.put("_search","true");
		listParams.put("rows","200");
		listParams.put("page","1");
		listParams.put("sord","asc");
		listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"STATUS\",\"op\":\"eq\",\"data\":\"40\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"}]}");
		String listRes = HttpRequest.post(listUrl)
				.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
				.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
				.form(listParams).execute().body();
		JSONObject listJson = JSONObject.parseObject(listRes);
		JSONArray tableRows = listJson.getJSONArray("rows");
		for(int j = 0 ; j < tableRows.size() ; j++){
			String gid = tableRows.getJSONObject(j).getString("id");
			String custOrderNo = tableRows.getJSONObject(j).getJSONArray("cell").getString(1);
			String spNum = tableRows.getJSONObject(j).getJSONArray("cell").getString(2);

			sunshineBackRecord.setEntrustVbillno(spNum);
			sunshineBackRecord.setInvoiceVbillno(spNum);
			sunshineBackRecord.setCustOrderNo(custOrderNo);

			Map<String,Object> goodsParams = new HashMap<>();
			goodsParams.put("_search","false");
			goodsParams.put("rows","200");
			goodsParams.put("page","1");
			goodsParams.put("sord","asc");
			String goodsUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/GetShipmentEquipments?gid="+gid+"&type=&defsort=LINE_ID%20Asc";
			String goodsRes = HttpRequest.post(goodsUrl)
					.header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
					.header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
					.form(goodsParams).execute().body();
			JSONObject goodsResObj = JSONObject.parseObject(goodsRes);
			JSONArray rows = goodsResObj.getJSONArray("rows");
			if(rows.size() == 0){
				sb.append(spNum+":阳光无车辆信息"+"</br>");
				sunshineBackRecord.setSuccessFlag(1);
				sunshineBackRecord.setBackMemo("阳光无车辆信息");
				//保存回调信息
				sunshineBackRecord.setId(IdUtil.simpleUUID());
				entrustMapper.insertSunshineBackRecord(sunshineBackRecord);
			}
			for(int i = 0 ; i <rows.size() ; i++){
				String carNo = rows.getJSONObject(i).getJSONArray("cell").getString(1);
				if(StringUtils.isBlank(carNo)){
					sb.append(spNum+":阳光无车辆信息"+"</br>");
					sunshineBackRecord.setSuccessFlag(1);
					sunshineBackRecord.setBackMemo("阳光无车辆信息");
				}else{
					Car car = carMapper.selectCarByCarno(carNo);
					if(car == null){
						sb.append(spNum+":"+carNo+"未查询到车辆</br>");
						sunshineBackRecord.setSuccessFlag(1);
						sunshineBackRecord.setBackMemo(carNo+"未查询到车辆");
					}else{
						//行驶证
						List<CarPicVO> carPicVOS = carMapper.selectCarPicByCarIdAndPicType(car.getCarId(),"1");
						if(carPicVOS == null || carPicVOS.size() == 0){
							sb.append(spNum+":"+carNo+":车辆行驶证不存在</br>");
							sunshineBackRecord.setSuccessFlag(1);
							sunshineBackRecord.setBackMemo(carNo+"车辆行驶证不存在");
						}else{
							String picUploadURL = "https://tms.sungrowpower.com/SCM.Configration.WebUI/AttachmentInfo/Upload";

							Map<String, String> headersPost = new HashMap<>();
							headersPost.put("Accept", "*/*");
							headersPost.put("Accept-Encoding","gzip, deflate, br, zstd");
							headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
							headersPost.put("Connection", "keep-alive");
							headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryjfPDtu6h3wMxaApO");
							headersPost.put("Cookie", "cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; Lang=zh-cn; SSID="+token+"; PowerTmsCookie=TmsUserID%3DFTvM9f4X5v89fLttSlA3OQ%3D%3D");
							headersPost.put("Origin", "https://tms.sungrowpower.com");
							headersPost.put("Referer", "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/Edit?guid=SALES.POD060308");
							headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
							headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
							headersPost.put("sec-ch-ua-mobile", "?0");
							headersPost.put("sec-ch-ua-platform", "\"Windows\"");

							Map<String,Object> sendParams = new HashMap<String,Object>();
							sendParams.put("Sys","TMS");
							sendParams.put("Module","Shipment");
							sendParams.put("Id",spNum);
							sendParams.put("AttachType","行驶证");
							sendParams.put("Relatedtype","20");
							sendParams.put("RelatedKey",gid);
							sendParams.put("PrimaryId",gid);

							CarPicVO sysUploadFile = carPicVOS.get(0);
							String filePath = "D:/" + sysUploadFile.getFilePath();
							File file = new File(filePath);
							String fileExtension = filePath.substring(filePath.lastIndexOf("."));
							String newFileName = carNo+fileExtension;

							HttpRequest request = HttpRequest.post(picUploadURL)
									.headerMap(headersPost, true)
									.form(sendParams)
									.form("files",file,newFileName);
							String uploadRes = request.execute().body();
							logger.info(carNo+"车辆行驶证上传结果：{}",uploadRes);

							JSONObject uploadJson = JSONObject.parseObject(uploadRes);

							//上传成功调用
							if(uploadRes.contains("true")){
								String afterUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Attachment/AfterPartialUpdateData";
								sendParams = new HashMap<>();
								sendParams.put("Sys","TMS");
								sendParams.put("Module","Shipment");
								sendParams.put("Id",spNum);
								sendParams.put("AttachType","行驶证");
								sendParams.put("Relatedtype","20");
								sendParams.put("RelatedKey",gid);
								sendParams.put("PrimaryId",gid);
								sendParams.put("AttachmenIds",uploadJson.getJSONArray("Msg"));


								uploadRes = HttpRequest.post(afterUrl)
										.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
										.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
										.form(sendParams).execute().body();
								if(uploadRes.contains("true")){
									sb.append(spNum+":"+carNo+":车辆行驶证上传成功</br>");
									sunshineBackRecord.setSuccessFlag(0);
									sunshineBackRecord.setBackMemo(carNo+"车辆行驶证上传成功");
								}else{
									sb.append(spNum+":"+carNo+":上传车辆行驶证失败(二次回调)</br>");
									sunshineBackRecord.setSuccessFlag(1);
									sunshineBackRecord.setBackMemo(carNo+"上传车辆行驶证失败(二次回调)");
								}
							}else{
								sb.append(spNum+":"+carNo+":上传车辆行驶证失败</br>");
								sunshineBackRecord.setSuccessFlag(1);
								sunshineBackRecord.setBackMemo(carNo+"上传车辆行驶证失败");
							}
						}
					}
				}
				sunshineBackRecord.setId(IdUtil.simpleUUID());
				//保存回调信息
				entrustMapper.insertSunshineBackRecord(sunshineBackRecord);
			}

			if(sunshineBackRecord.getSuccessFlag() == 0){
				sunshineBackRecord.setType(7);
				//确认
				String confirmURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/DeliveryConfirmation";
				listParams = new HashMap<>();
				listParams.put("gids", gid);

				listRes = HttpRequest.post(confirmURL)
						.header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
						.header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
						.form(listParams).execute().body();

				if(listRes.contains("成功")){
					sb.append(spNum+":单据确认成功</br>");
					sunshineBackRecord.setSuccessFlag(0);
					sunshineBackRecord.setBackMemo("单据确认成功");
				}else{
					//保存异常信息
					sb.append(spNum+":单据确认失败"+listRes+"</br>");
					sunshineBackRecord.setSuccessFlag(1);
					sunshineBackRecord.setBackMemo(listRes);
				}
				sunshineBackRecord.setId(IdUtil.simpleUUID());
				//保存回调信息
				entrustMapper.insertSunshineBackRecord(sunshineBackRecord);
			}

		}
		logger.info("阳光车辆行驶证上传结果：{}",sb.toString());
		return AjaxResult.success(sb.toString());
	}


}
