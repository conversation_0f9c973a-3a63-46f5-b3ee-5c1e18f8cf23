package com.ruoyi.tms.service.carrier.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.tms.constant.finance.PayDetailStatusEnum;
import com.ruoyi.tms.constant.finance.ReceiveDetailStatusEnum;
import com.ruoyi.tms.domain.basic.Carrier;
import com.ruoyi.tms.domain.basic.LockCarrierHistory;
import com.ruoyi.tms.domain.carrier.Entrust;
import com.ruoyi.tms.domain.carrier.EntrustExp;
import com.ruoyi.tms.domain.carrier.EntrustExpLossRecord;
import com.ruoyi.tms.domain.carrier.EntrustLot;
import com.ruoyi.tms.domain.entrustException.*;
import com.ruoyi.tms.domain.finance.OtherFee;
import com.ruoyi.tms.domain.finance.PayDetail;
import com.ruoyi.tms.domain.finance.ReceiveDetail;
import com.ruoyi.tms.domain.invoice.Invoice;
import com.ruoyi.tms.domain.trace.Allocation;
import com.ruoyi.tms.mapper.basic.CarrierMapper;
import com.ruoyi.tms.mapper.carrier.EntrustExpMapper;
import com.ruoyi.tms.mapper.carrier.EntrustLotMapper;
import com.ruoyi.tms.mapper.carrier.EntrustMapper;
import com.ruoyi.tms.mapper.entrustException.*;
import com.ruoyi.tms.mapper.finance.OtherFeeMapper;
import com.ruoyi.tms.mapper.finance.PayDetailMapper;
import com.ruoyi.tms.mapper.finance.ReceiveDetailMapper;
import com.ruoyi.tms.mapper.invoice.InvoiceMapper;
import com.ruoyi.tms.mapper.trace.AllocationMapper;
import com.ruoyi.tms.mapper.trace.TEntrustExpMapper;
import com.ruoyi.tms.service.carrier.IEntrustExpService;
import com.ruoyi.tms.service.finance.IPayDetailService;
import com.ruoyi.tms.service.finance.IReceiveDetailService;
import com.ruoyi.tms.service.trace.IAllocationService;
import com.ruoyi.tms.vo.finance.ReceiveDetailVO;
import com.ruoyi.tms.vo.trace.AjaxParamsVO;
import com.ruoyi.tms.vo.trace.EntrustExpVo;
import com.ruoyi.util.ShiroUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.text.Convert;
import org.springframework.transaction.annotation.Transactional;

/**
 * 在途跟踪（包括异常） 服务层实现
 *
 * <AUTHOR>
 * @date 2019-12-19
 */
@Service
public class EntrustExpServiceImpl implements IEntrustExpService
{
	@Autowired
	private ShiroUtils shiroUtils;
	@Autowired
	private EntrustExpMapper entrustExpMapper;
	@Autowired
	private TEntrustExpMapper tEntrustExpMapper;
	@Autowired
	private TExpEntrustMoreMapper tExpEntrustMoreMapper;
	@Autowired
	private TInsuranceMapper tInsuranceMapper;
	@Autowired
	private TExceptionTrackMapper tExceptionTrackMapper;
	@Autowired
	private EntrustLotMapper entrustLotMapper;
	@Autowired
	private EntrustMapper entrustMapper;
	@Autowired
	private TExceptionDictMapper exceptionDictMapper;
	@Autowired
	private ReceiveDetailMapper receiveDetailMapper;
	@Autowired
	@Lazy
	private IReceiveDetailService receiveDetailService;
	@Autowired
	private PayDetailMapper payDetailMapper;
	@Lazy
	@Autowired
	private IPayDetailService payDetailService;
	@Autowired
	private InvoiceMapper invoiceMapper;
	@Autowired
	private TExpInvoiceMapper expInvoiceMapper;
	@Autowired
	private MExceptionCorrectionMapper exceptionCorrectionMapper;
	@Autowired
	private MAppraisersMapper appraisersMapper;
	@Autowired
	private ResidualValueTrackingMapper residualValueTrackingMapper;
	@Autowired
	private CarrierMapper carrierMapper;
	@Autowired
	private IAllocationService allocationService;
	@Autowired
	private AllocationMapper allocationMapper;
	@Autowired
	private OtherFeeMapper otherFeeMapper;
	@Autowired
	private InsuranceRecordMapper insuranceRecordMapper;

	/**
	 * 查询在途跟踪（包括异常）信息
	 *
	 * @param entrustExpId 在途跟踪（包括异常）ID
	 * @return 在途跟踪（包括异常）信息
	 */
	@Override
	public EntrustExp selectEntrustExpById(String entrustExpId)
	{
		return entrustExpMapper.selectEntrustExpById(entrustExpId);
	}

	/**
	 * 新增在途跟踪（包括异常）
	 *
	 * @param entrustExp 在途跟踪（包括异常）信息
	 * @return 结果
	 */
	@Override
	public int insertEntrustExp(EntrustExp entrustExp)
	{
		return entrustExpMapper.insertEntrustExp(entrustExp);
	}

	/**
	 * 修改在途跟踪（包括异常）
	 *
	 * @param entrustExp 在途跟踪（包括异常）信息
	 * @return 结果
	 */
	@Override
	public int updateEntrustExp(EntrustExp entrustExp)
	{
		return entrustExpMapper.updateEntrustExp(entrustExp);
	}

	/**
	 * 根据运单Id查询委托单异常
	 * @param lotId
	 * @return
	 */
	@Override
	public List<EntrustExp> selectEntrustExpByLotId(String lotId) {
		return entrustExpMapper.selectEntrustExpByLotId(lotId);
	}

	@Override
	public List<EntrustExp> selectNoInspectcarEntrustExpByLotId(String lotId) {
		return entrustExpMapper.selectNoInspectcarEntrustExpByLotId(lotId);
	}

	@Override
	public List<EntrustExpVo> queryEntrustExpByEntrustId(EntrustExpVo entrustExpVo) {
		List<EntrustExpVo> entrustExpVos = entrustExpMapper.selectEntrustExpByEntrustId(entrustExpVo);
		return entrustExpVos;
	}


	@Override
	public List<EntrustExpVo> queryEntrustExpByEntrustIdPhone(EntrustExpVo entrustExpVo) {
		List<EntrustExpVo> entrustExpVos = entrustExpMapper.selectEntrustExpByEntrustIdPhone(entrustExpVo);
		return entrustExpVos;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult removeLotById(String lotId,String entrustExpId) {
		List<PayDetail> payDetailList = payDetailMapper.selectPayDetailListByLotId(lotId);
		long count = payDetailList.stream().filter(x -> x.getIncomeRemark() != 0).count();
		if (count > 0) {
			return AjaxResult.error("存在赔款或扣款的数据，无法解除关联。");
		}

		TExpEntrustMoreExample tExpEntrustMoreExample = new TExpEntrustMoreExample();
		tExpEntrustMoreExample.createCriteria().andDelFlagEqualTo((short) 0).andLotIdEqualTo(lotId)
				.andExpIdEqualTo(entrustExpId);
		List<TExpEntrustMore> tExpEntrustMores = tExpEntrustMoreMapper.selectByExample(tExpEntrustMoreExample);
		if (tExpEntrustMores.size() < 1) {
			return AjaxResult.error("未查询到绑定数据，无法解除关联，请刷新后重试。");
		}

		for (TExpEntrustMore tExpEntrustMore : tExpEntrustMores) {
			TExpEntrustMore moreExampleUpdate = new TExpEntrustMore();
			moreExampleUpdate.setDelFlag((short) 1);
			moreExampleUpdate.setDelDate(new Date());
			moreExampleUpdate.setId(tExpEntrustMore.getId());
			tExpEntrustMoreMapper.updateByPrimaryKeySelective(moreExampleUpdate);
		}

		return AjaxResult.success();
	}

	/**
	 * 删除异常信息
	 * @param ids
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeEntrustExpByIds(String ids) {
		String userId = shiroUtils.getUserId().toString();//删除操作人
		String[] entrustExpIds = Convert.toStrArray(ids);//主异常id数组
		//TODO  主异常删除  主异常关联的字典表(主异常id)  放开锁定的运单  放开锁定的承运商

		List<String> expIdList = Arrays.asList(entrustExpIds);

		TEntrustExp tEntrustExp = new TEntrustExp();
		tEntrustExp.setDelFlag((short)1);
		tEntrustExp.setDelDate(new Date());
		tEntrustExp.setDelUserId(userId);
		TEntrustExpExample tEntrustExpExample = new TEntrustExpExample();
		tEntrustExpExample.createCriteria().andEntrustExpIdIn(expIdList).andDelFlagEqualTo((short) 0);
		int i = tEntrustExpMapper.updateByExampleSelective(tEntrustExp, tEntrustExpExample);

		TExceptionDict tExceptionDict = new TExceptionDict();
		tExceptionDict.setDelFlag((short) 1);
		tExceptionDict.setDelDate(new Date());
		TExceptionDictExample tExceptionDictExample = new TExceptionDictExample();
		tExceptionDictExample.createCriteria().andEntrustExpIdIn(expIdList).andDelFlagEqualTo((short) 0);
		int i1 = exceptionDictMapper.updateByExampleSelective(tExceptionDict, tExceptionDictExample);

		//TODO 锁定应付  锁定承运商
		TEntrustExpExample exceptionExample = new TEntrustExpExample();
		exceptionExample.createCriteria().andEntrustExpIdIn(expIdList).andDelFlagEqualTo((short) 0);
		List<TEntrustExp> tEntrustExps = tEntrustExpMapper.selectByExample(exceptionExample);
		for (int j = 0; j < tEntrustExps.size(); j++) {
			TEntrustExp exp = tEntrustExps.get(j);
			if(exp.getLockPay() != null) {
				//委托单找运单
				EntrustLot entrustLot = entrustLotMapper.selectEntrustLotById("lotid");
				if(exp.getLockPay() == 1) {
					entrustLot.setLockPay("1");
					entrustLot.setCorDate(new Date());
					entrustLot.setEntrustLotId(entrustLot.getEntrustLotId());
					//entrustLot.setCorScrId(pageId);
					entrustLot.setCorUserId(shiroUtils.getUserId().toString());
					entrustLotMapper.updateEntrustLot(entrustLot);
				}
				//如果勾选了锁定承运商
				if(exp.getLockPay() == 2) {
					Carrier carrier = new Carrier();
					carrier.setLockPay(1);
					carrier.setCarrierId(entrustLot.getCarrierId());
					carrier.setCorDate(new Date());
					//carrier.setCorScrId(pageId);
					carrierMapper.updateCarrier(carrier);
				}
			}
		}

		//删除损失和收入(主异常id)   删除跟踪记录(主异常id)
		TInsurance insurance = new TInsurance();
		insurance.setDelFlag((short) 1);
		insurance.setDelDate(new Date());
		TInsuranceExample tInsuranceExample = new TInsuranceExample();
		tInsuranceExample.createCriteria().andExpIdIn(expIdList);
		int i2 = tInsuranceMapper.updateByExampleSelective(insurance, tInsuranceExample);

		//删除出险记录
		for (String exeId : expIdList) {
			InsuranceRecord update = new InsuranceRecord();
			update.setDelDate(new Date());
			update.setDelFlag(1);
			update.setDelUserId(userId);
			insuranceRecordMapper.updateByEntrustExpId(update, exeId);
		}

		TExceptionTrack exceptionTrack = new TExceptionTrack();
		exceptionTrack.setDelFlag((short) 1);
		exceptionTrack.setDelDate(new Date());
		TExceptionTrackExample exceptionTrackExample = new TExceptionTrackExample();
		exceptionTrackExample.createCriteria().andExpIdIn(expIdList);
		int i3 = tExceptionTrackMapper.updateByExampleSelective(exceptionTrack, exceptionTrackExample);
		//发货单关联主异常(主异常id)  运单关联主异常(主异常id)
		TExpInvoice expInvoice = new TExpInvoice();
		expInvoice.setDelFlag((short) 1);
		expInvoice.setDelUserId(userId);
		TExpInvoiceExample expInvoiceExample = new TExpInvoiceExample();
		expInvoiceExample.createCriteria().andExpIdIn(expIdList);
		int i4 = expInvoiceMapper.updateByExampleSelective(expInvoice, expInvoiceExample);

		TExpEntrustMore expEntrustMore = new TExpEntrustMore();
		expEntrustMore.setDelFlag((short) 1);
		expEntrustMore.setDelDate(new Date());
		TExpEntrustMoreExample expEntrustMoreExample = new TExpEntrustMoreExample();
		expEntrustMoreExample.createCriteria().andExpIdIn(expIdList);
		int i5 = tExpEntrustMoreMapper.updateByExampleSelective(expEntrustMore, expEntrustMoreExample);
		//残值跟踪表   异常整改表(主异常id)   整改表对应的考核人员表(整改表id)
		ResidualValueTracking residualValueTracking = new ResidualValueTracking();
		residualValueTracking.setDelFlag((short) 1);
		residualValueTracking.setDelUserId(userId);
		ResidualValueTrackingExample residualValueTrackingExample = new ResidualValueTrackingExample();
		residualValueTrackingExample.createCriteria().andExpIdIn(expIdList);
		int i6 = residualValueTrackingMapper.updateByExampleSelective(residualValueTracking, residualValueTrackingExample);

		MExceptionCorrection exceptionCorrection = new MExceptionCorrection();
		exceptionCorrection.setDelFlag((short) 1);
		exceptionCorrection.setDelUserId(userId);
		MExceptionCorrectionExample exceptionCorrectionExample = new MExceptionCorrectionExample();
		exceptionCorrectionExample.createCriteria().andExpIdIn(expIdList).andDelFlagEqualTo((short) 0);
		List<MExceptionCorrection> mExceptionCorrections = exceptionCorrectionMapper.selectByExample(exceptionCorrectionExample);

		int i7 = exceptionCorrectionMapper.updateByExampleSelective(exceptionCorrection, exceptionCorrectionExample);

		List<String> exceptionCorrcetionIdList = exceptionCorrcetionIdList = mExceptionCorrections.stream().map(MExceptionCorrection::getId).collect(Collectors.toList());
		if(ObjectUtil.isNotEmpty(exceptionCorrcetionIdList)) {
			MAppraisers appraisers = new MAppraisers();
			appraisers.setDelFlag((short) 1);
			appraisers.setDelUserId(userId);
			MAppraisersExample appraisersExample = new MAppraisersExample();
			appraisersExample.createCriteria().andExceptionCorrectionIdIn(exceptionCorrcetionIdList);
			int i8 = appraisersMapper.updateByExampleSelective(appraisers, appraisersExample);
		}
		/*int a = 0;
		for (int i = 0; i < entrustExpIds.length; i++) {
			String entrustExpId = entrustExpIds[i];
			//删除主异常
			TEntrustExp tEntrustExp = new TEntrustExp();
			tEntrustExp.setDelFlag((short)1);
			TEntrustExpExample tEntrustExpExample = new TEntrustExpExample();
			tEntrustExpExample.createCriteria().andEntrustExpIdEqualTo(entrustExpId);
			a = tEntrustExpMapper.updateByExampleSelective(tEntrustExp,tEntrustExpExample);

			//放开运单的锁定状态
			TExpEntrustMoreExample moreExampleore= new TExpEntrustMoreExample();
			moreExampleore.createCriteria().andExpIdEqualTo(entrustExpId);
			List<TExpEntrustMore> tExpEntrusts = tExpEntrustMoreMapper.selectByExample(moreExampleore);
			for (int j = 0; j < tExpEntrusts.size(); j++) {
				String lotId = tExpEntrusts.get(j).getLotId();
				EntrustLot entrustLot = new EntrustLot();
				entrustLot.setEntrustLotId(lotId);
				entrustLot.setLockPay("2");
				entrustLotMapper.updateEntrustLot(entrustLot);
			}


			//删除关联表
			TExpEntrustMoreExample tExpEntrustMoreExample = new TExpEntrustMoreExample();
			tExpEntrustMoreExample.createCriteria().andExpIdEqualTo(entrustExpId);
			TExpEntrustMore tExpEntrustMore = new TExpEntrustMore();
			tExpEntrustMore.setDelFlag((short)1);
			tExpEntrustMore.setDelDate(new Date());
			tExpEntrustMore.setCorUserId(userId);
			tExpEntrustMoreMapper.updateByExampleSelective(tExpEntrustMore,tExpEntrustMoreExample);


			//删除损失和收入
			TInsurance tInsurance = new TInsurance();
			tInsurance.setDelFlag((short)1);
			TInsuranceExample tInsuranceExample = new TInsuranceExample();
			tInsuranceExample.createCriteria().andExpIdEqualTo(entrustExpId);
			tInsuranceMapper.updateByExampleSelective(tInsurance,tInsuranceExample);
			//删除跟踪记录
			TExceptionTrack tExceptionTrack = new TExceptionTrack();
			tExceptionTrack.setDelFlag((short)1);
			TExceptionTrackExample tExceptionTrackExample = new TExceptionTrackExample();
			tExceptionTrackExample.createCriteria().andExpIdEqualTo(entrustExpId);
			tExceptionTrackMapper.updateByExampleSelective(tExceptionTrack,tExceptionTrackExample);
		}
		return a > 0;*/
		return true;
	}

	/**
	 * 修改异常
	 * @param ajaxParamsVO
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean editEntrustException(AjaxParamsVO ajaxParamsVO) {
		String pageId = ajaxParamsVO.getPageId();
		if(StringUtils.isBlank(pageId)){
			pageId = "abnormalEdit_temp";
		}
		if(ajaxParamsVO.getEntrustExp().getIsCustomerException() == 1) {
			//先删除主异常 关联表 和损失、收入表
			//保存主异常表   EntrustExp
			TEntrustExp tEntrustExp = new TEntrustExp();

			//
			

			EntrustExp entrustExp = ajaxParamsVO.getEntrustExp();
			//历史信息
			EntrustExp entrustExpHistory = entrustExpMapper.selectEntrustExpById(entrustExp.getEntrustExpId());

			//BeanUtils.copyProperties 导致Integer 0 丢失
			Integer lockPay = entrustExp.getLockPay();
			Integer isInsurance = entrustExp.getIsInsurance();
			Integer isCompensationRequired = entrustExp.getIsCompensationRequired();
			Integer abnormalLink = entrustExp.getAbnormalLink();
			//BeanUtils.copyProperties("转换前的类", "转换后的类");
			BeanUtils.copyProperties(entrustExp,tEntrustExp);
			tEntrustExp.setLockPay(Short.parseShort(String.valueOf(lockPay)));
			tEntrustExp.setIsInsurance(isInsurance);
			tEntrustExp.setIsCompensationRequired(Short.parseShort(String.valueOf(isCompensationRequired)));
			tEntrustExp.setAbnormalLink(abnormalLink);
			//保险
			TInsuranceExample tInsuranceExample = new TInsuranceExample();
			tInsuranceExample.createCriteria().andExpIdEqualTo(entrustExp.getEntrustExpId());
			int i3 = tInsuranceMapper.deleteByExample(tInsuranceExample);
			//出险记录
			InsuranceRecord update = new InsuranceRecord();
			update.setDelDate(new Date());
			update.setDelFlag(1);
			update.setDelUserId(String.valueOf(shiroUtils.getUserId()));
			insuranceRecordMapper.updateByEntrustExpId(update, entrustExp.getEntrustExpId());
			//异常追踪
			TExceptionTrackExample tExceptionTrackExample = new TExceptionTrackExample();
			tExceptionTrackExample.createCriteria().andExpIdEqualTo(entrustExp.getEntrustExpId());
			int i7 = tExceptionTrackMapper.deleteByExample(tExceptionTrackExample);
			//残值跟踪
			ResidualValueTrackingExample residualValueTrackingExample = new ResidualValueTrackingExample();
			residualValueTrackingExample.createCriteria().andExpIdEqualTo(entrustExp.getEntrustExpId());
			int i6 = residualValueTrackingMapper.deleteByExample(residualValueTrackingExample);
			//主异常关联异常字典表
			TExceptionDictExample exceptionDictExample = new TExceptionDictExample();
			exceptionDictExample.createCriteria().andEntrustExpIdEqualTo(entrustExp.getEntrustExpId());
			int i1 = exceptionDictMapper.deleteByExample(exceptionDictExample);
			//删除发货单和异常的关联
			TExpInvoiceExample expInvoiceExample = new TExpInvoiceExample();
			expInvoiceExample.createCriteria().andExpIdEqualTo(entrustExp.getEntrustExpId());
			expInvoiceMapper.deleteByExample(expInvoiceExample);
			//删除主异常和运单的关联关系
			//int i1 = tEntrustExpMapper.deleteByPrimaryKey(entrustExp.getEntrustExpId());
			TExpEntrustMoreExample tExpEntrustMoreExample = new TExpEntrustMoreExample();
			tExpEntrustMoreExample.createCriteria().andExpIdEqualTo(entrustExp.getEntrustExpId());
			int i2 = tExpEntrustMoreMapper.deleteByExample(tExpEntrustMoreExample);

			//主异常信息//主异常还是修改
			entrustExp.setCorDate(new Date());
			entrustExp.setCorScrId(pageId);
			entrustExp.setCorUserName(shiroUtils.getSysUser().getUserName());
			entrustExp.setCorUserId(shiroUtils.getUserId().toString());
			int i = tEntrustExpMapper.updateByPrimaryKeySelective(tEntrustExp);
			if(i == 0) {
				throw new BusinessException("修改异常失败!");
			}

			if (entrustExp.getLossAmount() == null) {
				entrustExp.setLossAmount(BigDecimal.ZERO);
			}

			BigDecimal la = BigDecimal.ZERO;
			if (entrustExpHistory.getLossAmount() != null) {
				la = entrustExpHistory.getLossAmount();
			}

			if(entrustExp.getLossAmount().compareTo(la) != 0){
				EntrustExpLossRecord entrustExpLossRecord = new EntrustExpLossRecord();
				entrustExpLossRecord.setId(IdUtil.simpleUUID());
				entrustExpLossRecord.setEntrustExpId(entrustExp.getEntrustExpId());
				entrustExpLossRecord.setRegUserName(shiroUtils.getSysUser().getUserName());
				entrustExpLossRecord.setLossAmount(entrustExp.getLossAmount().subtract(entrustExpHistory.getLossAmount()));
				entrustExpLossRecord.setLossMemo(entrustExp.getLossMemo());
				entrustExpMapper.insertEntrustExpLossRecord(entrustExpLossRecord);
			}
			//Entrust entrust = entrustMapper.selectEntrustById(entrustExp.getEntrustId());
			//再新增
			//保存异常主表关联字典
			String expType = entrustExp.getExpType();
			String[] split_expType = expType.split(",");
			for (String s : split_expType) {
				TExceptionDict exceptionDict = new TExceptionDict();
				exceptionDict.setId(IdUtil.simpleUUID());
				exceptionDict.setEntrustExpId(entrustExp.getEntrustExpId());
				exceptionDict.setDictCode((Short.parseShort(s)));
				exceptionDictMapper.insertSelective(exceptionDict);
			}

			List<String> invoiceIds = ajaxParamsVO.getInvoiceIds();
			//应付费用不做删除 做差额  根据invoiceId(发货单id)和PAYOUT_MARK(赔款标志)删选出所有的应收明细,算出所有应付明细总额,和页面传过来的赔款金额对比,取差值插入应收明细
		/*List<String> invoiceIdList = new ArrayList<>();
		if(invoiceIds.size() > 0) {
			for (String invoiceId : invoiceIds) {
				//通过发货单号查找发货单
				Invoice invoice = invoiceMapper.selectInvoiceById(invoiceId.split("-")[0]);
				invoiceIdList.add(invoice.getInvoiceId());
			}
		}
		ReceiveDetail receive = new ReceiveDetail();
		receive.setDelFlag(1);
		receive.setDelDate(new Date());
		int i5 = receiveDetailMapper.deleteReceiveDetailBatchByLotId(invoiceIdList, receive);*/
			//保存主异常和发货单之间的关联关系;  应收明细表中存在发货单和应收明细的关联关系
			if(invoiceIds.size() > 0) {
				for (String invoiceId : invoiceIds) {
					String invId = invoiceId.split("-")[0];
					String adjustAmount = invoiceId.split("-")[1];
					String adjustType = invoiceId.split("-")[2];    //add:增加 deduct:扣减
//                    String isProfitIncluded = invoiceId.split("-")[3];   //0:不纳入利润计算 1:纳入利润计算

//					ReceiveDetailVO receiveDetailVO = new ReceiveDetailVO();
//					receiveDetailVO.setInvoiceId(invId);
//					receiveDetailVO.setPayoutMark(1);
//					receiveDetailVO.setDelFlag(0);
//					List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveList(receiveDetailVO);
//					//receiveDetailVOS.stream().mapToDouble(ReceiveDetailVO::getTransFeeCount).sum();
//					BigDecimal reduce = receiveDetailVOS.stream()
//							//将user对象的age取出来map为Bigdecimal
//							.map(ReceiveDetailVO::getTransFeeCount)
//							// 使用reduce()聚合函数,实现累加器
//							.reduce(BigDecimal.ZERO, BigDecimal::add);
//					String s = invoiceId.split("-")[1];
//					BigDecimal subtract = new BigDecimal(s).negate().subtract(reduce);

					TExpInvoice expInvoice = new TExpInvoice();
					expInvoice.setId(IdUtil.simpleUUID());
					expInvoice.setExpId(entrustExp.getEntrustExpId());
					expInvoice.setInvoiceId(invId);
					expInvoiceMapper.insertSelective(expInvoice);
					//查找发货单信息
					Invoice invoice = invoiceMapper.selectInvoiceById(invId);
					/*
					 * 锁定三方
					 */
					OtherFee otherFeeUpdate = new OtherFee();
					otherFeeUpdate.setLockOtherFee(entrustExp.getLockOtherFee());
					otherFeeMapper.updateByLotId(otherFeeUpdate, invoice.getInvoiceId());

					//新建应收明细
					if(StringUtils.isNotEmpty(adjustAmount) && !adjustAmount.equals("0")) {
//						if(subtract.compareTo(BigDecimal.ZERO) != 0) {
							BigDecimal transFeeCount;
							if ("add".equals(adjustType)) {
								transFeeCount = new BigDecimal(adjustAmount);
							}else {
								transFeeCount = new BigDecimal(adjustAmount).negate();  //扣款存负数
							}
							int payoutMark = 1; //0:不是赔款 1:是赔款
	//                            if ("1".equals(isProfitIncluded)) {
	//                                payoutMark = 0;
	//                            }else {
	//                                payoutMark = 1;
	//                            }


						//应付费用完善 发货单信息完善
							ReceiveDetailVO receiveDetail = new ReceiveDetailVO();
							receiveDetail.setReceiveDetailId(IdUtil.simpleUUID());
							receiveDetail.setVbillno(receiveDetailService.createReceiveDetailVbillno("1".equals(invoice.getIsFleetData()) ? 1 : 0));
							receiveDetail.setFreeType("1");//费用类型  0:在途 1:运费
							//异常赔款
//							receiveDetail.setCostTypeOnWay("27");
							//费用使用差额
							//receiveDetail.setTransFeeCount(new BigDecimal(invoiceId.split("-")[1]).negate());//扣款存负数
							receiveDetail.setTransFeeCount(transFeeCount);
							receiveDetail.setUngotAmount(transFeeCount);
							receiveDetail.setInvoiceId(invoice.getInvoiceId());
							receiveDetail.setInvoiceVbillno(invoice.getVbillno());
							receiveDetail.setCustomerId(invoice.getCustomerId());
							receiveDetail.setCustCode(invoice.getCustCode());
							receiveDetail.setCustName(invoice.getCustName());
							receiveDetail.setSalesDept(invoice.getSalesDept());
							receiveDetail.setBalaDept(invoice.getBalaDept());
							receiveDetail.setBalaCustomer(invoice.getBalaCustomerId());
							receiveDetail.setBalatype(invoice.getBalaType());
							receiveDetail.setBalaCode(invoice.getBalaCode());
							receiveDetail.setBalaName(invoice.getBalaName());
							receiveDetail.setBalaCorp(invoice.getBalaCorpId());//字典bala_corp
							receiveDetail.setNumCount(invoice.getNumCount());
							receiveDetail.setVolumeCount(invoice.getVolumeCount());
							receiveDetail.setFeeWeightCount(invoice.getWeightCount());
							receiveDetail.setReqDeliDate(invoice.getReqDeliDate());
							receiveDetail.setReqArriDate(invoice.getReqArriDate());
							receiveDetail.setVbillstatus(ReceiveDetailStatusEnum.AFFIRM.getValue()); //应收单据状态 默认已确认
							receiveDetail.setPayoutMark(payoutMark);//异常应收扣款标志
							receiveDetail.setRegScrId(pageId);
							receiveDetail.setCorScrId(pageId);
							receiveDetail.setIsFleetData(invoice.getIsFleetData());
							receiveDetail.setIsFleetAssign(invoice.getIsFleetAssign());
							receiveDetailService.insertReceiveDetailAndAdjustRecord(receiveDetail,false);
							//receiveDetailService.insertReceiveDetail(receiveDetail);
//						}
					}
				}
			}

			List<String> lotIdTransFeeList = ajaxParamsVO.getLotIdTransFeeList();
			//删除赔付应付明细
		/*List<String> lotIds = new ArrayList<>();
		for (int j = 0; j < lotIdTransFeeList.size(); j++) {
			//根据运单号查询对应的承运商信息  以及该运单对应的车辆和司机信息
			String[] split = lotIdTransFeeList.get(j).split(",");
			for (int k = 0; k < split.length; k++) {
				String lot_vbillno = split[k];
				//EntrustLot entrustLot = entrustLotMapper.selectEntrustLotByLotNo(lot_vbillno.split("-")[0]);
				lotIds.add(lot_vbillno.split("-")[0]);
			}
		}
		PayDetail pay = new PayDetail();
		pay.setDelFlag(1);
		pay.setDelDate(new Date());
		int i4 = payDetailMapper.deletePayDetailBatchByLotId(lotIds, pay);*/
			//根据payDetailId删除成本分摊信息
			//allocationMapper.deleteAllocationByPayDetail(new String[]{}, shiroUtils.getSysUser().getUserId().toString(), pageId);
			//保存运单
		/*String carrierId = "";
		for (int j = 0; j < lotnolist.size(); j++) {  //一个运单保存一条异常记录
			//根据运单号查询对应的承运商信息  以及该运单对应的车辆和司机信息
			String[] split = lotnolist.get(j).split(",");
			for (int k = 0; k < split.length; k++) {
				String lot_vbillno = split[k];
				EntrustLot entrustLot = entrustLotMapper.selectEntrustLotByLotNo(split[k]);
				//如果勾选了锁定应付  那么运单中的lock_pay字段也锁定

				entrustLot.setLockPay(String.valueOf(entrustExp.getLockPay()));
				entrustLotMapper.updateEntrustLot(entrustLot);
//				if(entrustExp.getLockPay() == 1) {
//					entrustLot.setLockPay("1");
//					entrustLotMapper.updateEntrustLot(entrustLot);
//				}
				//carrierId = entrustLot.getCarrierId();
				TExpEntrustMore expEntrustMore = new TExpEntrustMore();
				expEntrustMore.setRegDate(new Date()); //
				expEntrustMore.setRegUserId(shiroUtils.getUserId().toString());
				expEntrustMore.setRegUserName(shiroUtils.getSysUser().getUserName());
				expEntrustMore.setRegScrId("abnormalEdit1"); //新增画面id
				expEntrustMore.setCorDate(new Date());
				expEntrustMore.setCorUserName(shiroUtils.getSysUser().getUserName());
				expEntrustMore.setCorUserId(shiroUtils.getUserId().toString());

				expEntrustMore.setId(IdUtil.simpleUUID());
				entrustLot.setEntrustLotId(entrustLot.getEntrustLotId());
				expEntrustMore.setExpId(entrustExp.getEntrustExpId());//关联 主异常表id
				expEntrustMore.setCarrierId(entrustLot.getCarrierId());
				expEntrustMore.setCarrierName(entrustLot.getCarrierName());
				expEntrustMore.setCarnoId(entrustLot.getCarnoId());
				expEntrustMore.setCarno(entrustLot.getCarNo());
				expEntrustMore.setDriverName(entrustLot.getDriverName());
				expEntrustMore.setLotId(entrustLot.getEntrustLotId());
				expEntrustMore.setLotNo(entrustLot.getLot());
				tExpEntrustMoreMapper.insertSelective(expEntrustMore);

				//TODO  如果扣款那么需要添加一条运单对应的应付明细   承运商扣款标志 INCOME_REMARK 1:赔款
				//直接新建一条和运单相关联的应付明细记录
				if(StringUtils.isNotEmpty(lot_vbillno.split("-")[1])) {
					PayDetail payDetail = new PayDetail();
					payDetail.setPayDetailId(IdUtil.simpleUUID());
					payDetail.setVbillno(payDetailService.createPayDetailVbillno("1".equals(entrustLot.getIsFleetData()) ? 1 : 0));
					payDetail.setTransFeeCount(new BigDecimal(lot_vbillno.split("-")[1]));
					payDetailMapper.insertPayDetail(payDetail);
				}
			}
		}*/
			List<AjaxParamsVO.LockCarrier> lockCarrierList = ajaxParamsVO.getLockCarrierList();
			for (AjaxParamsVO.LockCarrier lockCarrier : lockCarrierList) {
				Carrier carrier = carrierMapper.selectCarrierById(lockCarrier.getCarrierId());
				if (lockCarrier.getLockPay() != null && !lockCarrier.getLockPay().equals(carrier.getLockPay())) {
					//保存承运商锁定信息
					LockCarrierHistory lockCarrierHistory = new LockCarrierHistory();
					lockCarrierHistory.setId(IdUtil.simpleUUID());
					lockCarrierHistory.setCarrierId(lockCarrier.getCarrierId());
					lockCarrierHistory.setCarrierName(carrier.getCarrName());
					lockCarrierHistory.setLockUserName(shiroUtils.getSysUser().getUserName());
					lockCarrierHistory.setLockTime(new Date());
					lockCarrierHistory.setLockType(lockCarrier.getLockPay());
					lockCarrierHistory.setLockReason("修改异常：" + entrustExp.getEntrustId() + "/" + lockCarrier.getLockPay());
					carrierMapper.insertLockCarrierHistory(lockCarrierHistory);

					//设置整合信息
					String lockPayReason = DateFormatUtils.format(new Date(), "yyyy-MM-dd")+"/"+shiroUtils.getSysUser().getUserName()+"/"+lockCarrierHistory.getLockReason();

					Carrier carrierUpdate = new Carrier();
					carrierUpdate.setLockPayReason(lockCarrier.getLockPayReason());
					carrierUpdate.setLockPayReasonUnion(lockPayReason);
					carrierUpdate.setLockPay(lockCarrier.getLockPay());
					carrierUpdate.setCarrierId(lockCarrier.getCarrierId());
					carrierUpdate.setCorDate(new Date());
					carrierUpdate.setCorScrId(pageId);
					carrierMapper.updateCarrier(carrierUpdate);
				}
			}

			if(ObjectUtil.isNotEmpty(lotIdTransFeeList)) {
				for (String lotId : lotIdTransFeeList) {  //一个运单保存一条异常记录  lotnolist:["BAT202211245122-1500.50","BAT202211165086-1200.00","BAT202211155082-0","BAT202208204802-0"]
					EntrustLot entrustLot = entrustLotMapper.selectEntrustLotById(lotId.split("-")[0]);
					//查询运单对应的所有委托单
//					List<Entrust> entrusts = entrustMapper.selectEntrustByLotId(entrustLot.getEntrustLotId());
//
//					PayDetail pay = new PayDetail();
//					pay.setLotId(lotId.split("-")[0]);
//					pay.setIncomeRemark(1);
//					List<PayDetail> payDetails = payDetailMapper.selectPayDetailList(pay);
//
//					BigDecimal reduce = payDetails.stream()
//							//将payDetails对象的TransFeeCount取出来map为Bigdecimal
//							.map(PayDetail::getTransFeeCount)
//							// 使用reduce()聚合函数,实现累加器
//							.reduce(BigDecimal.ZERO, BigDecimal::add);
//					String s = lotId.split("-")[1];
//					BigDecimal subtract = new BigDecimal(s).negate().subtract(reduce);

					//如果勾选了锁定应付  那么所有运单中的lock_pay字段也锁定
					if(entrustExp.getLockPay() == 1) {
						entrustLot.setLockPay("1");
						entrustLot.setCorDate(new Date());
						entrustLot.setCorScrId(pageId);
						entrustLot.setCorUserId(shiroUtils.getUserId().toString());
						entrustLotMapper.updateEntrustLot(entrustLot);

						/*List<EntrustExp> entrustExps = entrustExpMapper.getEntrustExpByLotId(entrustLot.getEntrustLotId());
						long count = entrustExps.stream().filter(x -> x.getLockPay() == 2).count();

						if (count == 0) {
							Carrier carrier = new Carrier();
							carrier.setLockPay(0);  //0否 1:是
							carrier.setCarrierId(entrustLot.getCarrierId());
							carrier.setCorDate(new Date());
							carrier.setCorScrId(pageId);
							carrierMapper.updateCarrier(carrier);
						}*/
					}
					//如果勾选了锁定承运商
				/*	if(entrustExp.getLockPay() == 2) {
						//保存承运商锁定信息
						LockCarrierHistory lockCarrierHistory = new LockCarrierHistory();
						lockCarrierHistory.setId(IdUtil.simpleUUID());
						lockCarrierHistory.setCarrierId(entrustLot.getCarrierId());
						lockCarrierHistory.setCarrierName(entrustLot.getCarrierName());
						lockCarrierHistory.setLockUserName(shiroUtils.getSysUser().getUserName());
						lockCarrierHistory.setLockTime(new Date());
						lockCarrierHistory.setLockReason("异常跟踪/"+entrustLot.getLot()+"/"+entrustExp.getHandleNote());
						carrierMapper.insertLockCarrierHistory(lockCarrierHistory);

						//设置整合信息
						String lockPayReason = DateFormatUtils.format(new Date(), "yyyy-MM-dd")+"/"+shiroUtils.getSysUser().getUserName()+"/"+lockCarrierHistory.getLockReason();

						Carrier carrier = new Carrier();
						carrier.setLockPayReason(lockCarrierHistory.getLockReason());
						carrier.setLockPayReasonUnion(lockPayReason);
						carrier.setLockPay(1);
						carrier.setCarrierId(entrustLot.getCarrierId());
						carrier.setCorDate(new Date());
						carrier.setCorScrId(pageId);
						carrierMapper.updateCarrier(carrier);

						entrustLot.setLockPay("2");
						entrustLot.setCorDate(new Date());
						entrustLot.setCorScrId(pageId);
						entrustLot.setCorUserId(shiroUtils.getUserId().toString());
						entrustLotMapper.updateEntrustLot(entrustLot);

					}*/
					//取消了锁定
					if(entrustExp.getLockPay() == 0) {
						entrustLot.setLockPay("2");
						entrustLot.setCorDate(new Date());
						entrustLot.setCorScrId(pageId);
						entrustLot.setCorUserId(shiroUtils.getUserId().toString());
						entrustLot.setSingleLock("2");

						entrustLotMapper.updateEntrustLot(entrustLot);

			/*			List<EntrustExp> entrustExps = entrustExpMapper.getEntrustExpByLotId(entrustLot.getEntrustLotId());
						long count = entrustExps.stream().filter(x -> x.getLockPay() == 2).count();

						if (count == 0) {
							Carrier carrier = new Carrier();
							carrier.setLockPay(0);  //0否 1:是
							carrier.setCarrierId(entrustLot.getCarrierId());
							carrier.setCorDate(new Date());
							carrier.setCorScrId(pageId);
							carrierMapper.updateCarrier(carrier);
						}*/

//						Carrier carrier = new Carrier();
//						carrier.setLockPay(0);  //0否 1:是
//						carrier.setCarrierId(entrustLot.getCarrierId());
//						carrier.setCorDate(new Date());
//						carrier.setCorScrId(pageId);
//						carrierMapper.updateCarrier(carrier);
					}
					TExpEntrustMore expEntrustMore = new TExpEntrustMore();
					expEntrustMore.setRegDate(new Date());
					expEntrustMore.setRegUserId(shiroUtils.getUserId().toString());
					expEntrustMore.setRegUserName(shiroUtils.getSysUser().getUserName());
					expEntrustMore.setRegScrId(pageId); //新增画面id
					expEntrustMore.setCorScrId(pageId); //新增画面id
					expEntrustMore.setCorDate(new Date());
					expEntrustMore.setCorUserName(shiroUtils.getSysUser().getUserName());
					expEntrustMore.setCorUserId(shiroUtils.getUserId().toString());
					expEntrustMore.setId(IdUtil.simpleUUID());
					entrustLot.setEntrustLotId(entrustLot.getEntrustLotId());
					expEntrustMore.setExpId(entrustExp.getEntrustExpId());//关联 主异常表id
					expEntrustMore.setCarrierId(entrustLot.getCarrierId());
					expEntrustMore.setCarrierName(entrustLot.getCarrierName());
					expEntrustMore.setCarnoId(entrustLot.getCarnoId());
					expEntrustMore.setCarno(entrustLot.getCarNo());
					expEntrustMore.setDriverName(entrustLot.getDriverName());
					expEntrustMore.setLotId(entrustLot.getEntrustLotId());
					expEntrustMore.setLotNo(entrustLot.getLot());
					tExpEntrustMoreMapper.insertSelective(expEntrustMore);

					List<PayDetail> writeOffList = new ArrayList<>();

					//如果扣款那么需要添加一条运单对应的应付明细   承运商扣款标志 INCOME_REMARK 1:赔款
					//直接新建一条和运单相关联的应付明细记录
					if(StringUtils.isNotEmpty(lotId.split("-")[1])) {
						if(!lotId.split("-")[1].equals("0")) {
							//异常扣款金额
							BigDecimal abnormalFee = new BigDecimal(lotId.split("-")[1]);

							List<PayDetail> payDetailList = payDetailService.selectPayDetailListByLotId(entrustLot.getEntrustLotId());

							//将已经存在的金额减去
//							BigDecimal bigDecimal = payDetailList.stream()
//									.filter(x -> x.getIncomeRemark() != null && x.getIncomeRemark() == 1)
//									.map(PayDetail::getTransFeeCount)
//									.filter(Objects::nonNull)
//									.reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
//							abnormalFee = NumberUtil.sub(abnormalFee, bigDecimal);

							payDetailList = payDetailList.stream()
									.filter(x -> PayDetailStatusEnum.NEW.getValue() == x.getVbillstatus()
											|| PayDetailStatusEnum.AFFIRM.getValue() == x.getVbillstatus())
									.sorted(Comparator.comparingInt(x -> {
										String costTypeFreight = x.getCostTypeFreight();
										// 如果costTypeFreight是1、3、5 油卡，返回1，否则返回0
										return ("1".equals(costTypeFreight) || "3".equals(costTypeFreight)
												|| "5".equals(costTypeFreight)) ? 1 : 0;
									}))
									.collect(Collectors.toList());

							if (abnormalFee.compareTo(BigDecimal.ZERO) != 0 && payDetailList.size() == 0) {
								throw new BusinessException("不存“新建”或“已确认”的应付单据，无法异常扣款应付。");
							}

							//“新建”或“已确认”的应付合计
							BigDecimal feeCountAll = payDetailList.stream()
									.map(PayDetail::getTransFeeCount)
									.reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

							if (abnormalFee.compareTo(feeCountAll) > 0) {
								throw new BusinessException("异常扣款应付金额大于“新建”或“已确认”的应付单据金额合计，无法扣款。");
							}

							for (PayDetail payDetail : payDetailList) {
								if (abnormalFee.compareTo(BigDecimal.ZERO) != 0) {
									BigDecimal transFeeCount = payDetail.getTransFeeCount();

									if (abnormalFee.compareTo(transFeeCount) > 0) {
										//异常扣款金额
										abnormalFee = NumberUtil.sub(abnormalFee, transFeeCount);

										writeOffList.add(payDetail);
									} else if (abnormalFee.compareTo(transFeeCount) == 0) {

										abnormalFee = BigDecimal.ZERO;

										writeOffList.add(payDetail);
									} else if (abnormalFee.compareTo(transFeeCount) < 0) {
										PayDetail payDetailInsert = payDetailService.splitPayDetail(payDetail, abnormalFee
												, "EntrustExceptionMoreService.addEntrustException");

										writeOffList.add(payDetailInsert);
										abnormalFee = BigDecimal.ZERO;
									}
								}
							}
							int isProfitIncluded = cn.hutool.core.convert.Convert.toInt(lotId.split("-")[2], 0);

							for (PayDetail payDetail : writeOffList) {
								//核销应付
								payDetailService.writeOffAbnormalPayDetail(payDetail
										, "EntrustExceptionMoreService.addEntrustException", isProfitIncluded);
							}


							//异常扣款金额
//							BigDecimal abnormalFee = new BigDecimal(lot_vbillno.split("-")[1]);
//
//							List<PayDetail> payDetailList = payDetailService.selectPayDetailListByLotId(entrustLot.getEntrustLotId());
//							payDetailList = payDetailList.stream()
//									.filter(x -> PayDetailStatusEnum.NEW.getValue() == x.getVbillstatus()
//											|| PayDetailStatusEnum.AFFIRM.getValue() == x.getVbillstatus())
//									.collect(Collectors.toList());
//
//							if (abnormalFee.compareTo(BigDecimal.ZERO) != 0 && payDetailList.size() == 0) {
//								throw new BusinessException("不存“新建”或“已确认”的应付单据，无法异常扣款应付。");
//							}
//
//							//“新建”或“已确认”的应付合计
//							BigDecimal feeCountAll = payDetailList.stream()
//									.map(PayDetail::getTransFeeCount)
//									.reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
//
//							if (abnormalFee.compareTo(feeCountAll) > 0) {
//								throw new BusinessException("异常扣款应付金额大于“新建”或“已确认”的应付单据金额合计，无法扣款。");
//							}
//
//							for (PayDetail payDetail : payDetailList) {
//								if (abnormalFee.compareTo(BigDecimal.ZERO) != 0) {
//									BigDecimal transFeeCount = payDetail.getTransFeeCount();
//
//									if (abnormalFee.compareTo(transFeeCount) > 0) {
//										//异常扣款金额
//										abnormalFee = NumberUtil.sub(abnormalFee, transFeeCount);
//
//										writeOffList.add(payDetail);
//									} else if (abnormalFee.compareTo(transFeeCount) == 0) {
//
//										abnormalFee = BigDecimal.ZERO;
//
//										writeOffList.add(payDetail);
//									} else if (abnormalFee.compareTo(transFeeCount) < 0) {
//										PayDetail payDetailInsert = payDetailService.splitPayDetail(payDetail, abnormalFee
//												, "EntrustExceptionMoreService.addEntrustException");
//
//										writeOffList.add(payDetailInsert);
//										abnormalFee = BigDecimal.ZERO;
//									}
//								}
//							}
//
//							for (PayDetail payDetail : writeOffList) {
//								//核销应付
//								payDetailService.writeOffAbnormalPayDetail(payDetail
//										, "EntrustExceptionMoreService.addEntrustException");
//							}
//



//							List<PayDetail> payDetailList = new ArrayList<>();
//							//List<Entrust> entrustList = new ArrayList<>();
//							PayDetail payDetail = new PayDetail();
//							payDetail.setPayDetailId(IdUtil.simpleUUID());
//							payDetail.setVbillno(payDetailService.createPayDetailVbillno("1".equals(entrustLot.getIsFleetData()) ? 1 : 0));
//							payDetail.setVbillstatus(0);//应付单状态 新建状态
//							payDetail.setFreeType("1");//在途费
//							payDetail.setCostTypeOnWay("18"); //在途费用类型 字典获取  异常扣款	18
//							//payDetail.setTransFeeCount(new BigDecimal(lot_vbillno.split("-")[1]).negate());//取负数  前台为正数
//							payDetail.setTransFeeCount(subtract);
//							payDetail.setLotId(entrustLot.getEntrustLotId());
//							payDetail.setLotno(entrustLot.getLot());
//							payDetail.setCarrierId(entrustLot.getCarrierId());
//							payDetail.setCarrName(entrustLot.getCarrierName());
//							payDetail.setBalaCorp(entrustLot.getBalaCorp());
//							payDetail.setBalatype(entrustLot.getBalatype());
//							payDetail.setDriverMobile(entrustLot.getDriverMobile());
//							payDetail.setDriverName(entrustLot.getDriverName());
//							payDetail.setCarno(entrustLot.getCarNo());
//							payDetail.setRegScrId(pageId);
//							payDetail.setIncomeRemark(1);
//
//							//TODO 业务口的分配车队只能是1对1
//							if(entrusts.size() == 1) {
//								Entrust entrust1 = entrusts.get(0);
//								if ("0".equals(entrust1.getIsFleetData()) && !"0".equals(entrust1.getIsFleetAssign())) {//is_Fleet_Data = 0 and  is_Fleet_Assign != 0
//									//找出对应的分配车队发货单
//									Invoice invoiceAll = invoiceMapper.selectInvoiceByBizEntrustId(entrust1.getEntrustId());
//									if (invoiceAll != null && !invoiceAll.getVbillstatus().equals("0")) {
//										//不是新建状态需要插入应付
//										ReceiveDetailVO receiveDetail = new ReceiveDetailVO();
//
//										//拷贝发货单信息
//										BeanUtils.copyBeanProp(receiveDetail, invoiceAll);
//										receiveDetail.setReceiveDetailId(IdUtil.simpleUUID());
//										//结算客户id
//										receiveDetail.setBalaCustomer(invoiceAll.getBalaCustomerId());
//										receiveDetail.setVbillno(receiveDetailService.createReceiveDetailVbillno(1));
//										//客户名称存客户简称
//										receiveDetail.setCustName(invoiceAll.getCustAbbr());
//										//结算方式
//										receiveDetail.setBalatype(invoiceAll.getBalaType());
//										//应收单据状态
//										receiveDetail.setVbillstatus(payDetail.getVbillstatus());
//										receiveDetail.setInvoiceId(invoiceAll.getInvoiceId());
//										//发货单号
//										receiveDetail.setInvoiceVbillno(invoiceAll.getVbillno());
//										//总重量
//										receiveDetail.setFeeWeightCount(invoiceAll.getWeightCount());
//										//是否原始单据 默认原始单据
//										receiveDetail.setVbillType("1");
//										receiveDetail.setDelFlag(0);
//										//结算公司id
//										receiveDetail.setBalaCorp(invoiceAll.getBalaCorpId());
//										receiveDetail.setFreeType(payDetail.getFreeType());
//										//
//										receiveDetail.setTransFeeCount(payDetail.getTransFeeCount());
//										receiveDetail.setRegScrId(pageId);
//										receiveDetail.setCorScrId(pageId);
//
//										receiveDetail.setIsFleetData("1");
//										receiveDetail.setIsFleetAssign("1");
//										receiveDetail.setFleetPayDetailId(payDetail.getPayDetailId());
//										receiveDetail.setPayoutMark(1); //异常标识
//
//										receiveDetailService.insertReceiveDetailAndAdjustRecord(receiveDetail, false);
//										//等分配车队的应付数据保存完成  还要回填原来车队应收中对应的应付表的id
//										payDetail.setIsFleetAssign("1");//分配车队的应付保存其状态为1
//										payDetail.setFleetReceiveDetailId(receiveDetail.getReceiveDetailId());
//										/*
//										 * 调整车队的发货单是否加入对账包状态
//										 */
//										List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveByInvoiceId(invoiceAll.getInvoiceId());
//
//										//已对账的数量
//										long reconciledCount = receiveDetailVOS.stream().filter(x -> ReceiveDetailStatusEnum.RECONCILED.getValue() == x.getVbillstatus()).count();
//
//										Invoice invoiceFleetUp = new Invoice();
//										invoiceFleetUp.setInvoiceId(invoiceAll.getInvoiceId());
//										if (reconciledCount == receiveDetailVOS.size()) {
//											//已全部加入对账包
//											invoiceFleetUp.setIsAddReceCheck(2);
//											invoiceMapper.updateInvoice(invoiceFleetUp);
//										} else if (reconciledCount < receiveDetailVOS.size() && reconciledCount > 0) {
//											//部分加入对账包
//											invoiceFleetUp.setIsAddReceCheck(1);
//											invoiceMapper.updateInvoice(invoiceFleetUp);
//										} else {
//											invoiceFleetUp.setIsAddReceCheck(0);
//											invoiceMapper.updateInvoice(invoiceFleetUp);
//										}
//									} else {
//										//分配车队的应付保存其状态为2 该生成，但对应的车队发货单未确定，所以还未生成应收
//										payDetail.setIsFleetAssign("2");
//									}
//								} else {
//									payDetail.setIsFleetAssign("0");//不是分配车队的数据
//								}
//							}
//
//							payDetailService.insertPayDetailAndAdjustRecord(payDetail, null, false);
//							//payDetailMapper.insertPayDetail(payDetail);
//							//成本分摊
//							payDetailList.add(payDetail);
//							//entrustList.add(entrust);
//							List<Allocation> allocationByEntrustList = allocationService.getAllocationByEntrustList(payDetailList, entrusts, pageId);
//							for (Allocation allocation : allocationByEntrustList) {
//								allocation.setCostAllocationId(IdUtil.simpleUUID());
//								allocation.setRegDate(new Date());
//								allocation.setCorDate(new Date());
//								allocation.setRegScrId(pageId);
//								allocation.setCorScrId(pageId);
//								allocation.setRegUserId(shiroUtils.getUserId().toString());
//								allocationMapper.insertAllocation(allocation);
//							}
						}
					}
				}
			}

			//残值跟踪
			List<ResidualValueTracking> residualValueTrackingList = ajaxParamsVO.getResidualValueTrackingList();
			if(ObjectUtil.isNotEmpty(residualValueTrackingList)) {
				for (ResidualValueTracking residualValueTracking:residualValueTrackingList) {
					residualValueTracking.setExpId(entrustExp.getEntrustExpId());
					residualValueTrackingMapper.insertSelective(residualValueTracking);
				}
			}


			List<AjaxParamsVO.LotIdDedTransFeeVO> lotIdDedTransFeeList = ajaxParamsVO.getLotIdDedTransFeeList();
			for (AjaxParamsVO.LotIdDedTransFeeVO lotIdDedTransFeeVO : lotIdDedTransFeeList) {
				//运单id
				String lotId = lotIdDedTransFeeVO.getLotId();
				//扣款金额
				BigDecimal fee = lotIdDedTransFeeVO.getFee();
				//备注
				String memo = lotIdDedTransFeeVO.getMemo();

				EntrustLot entrustLot = entrustLotMapper.selectEntrustLotById(lotId);
				//查询运单对应的所有委托单
				List<Entrust> entrusts = entrustMapper.selectEntrustByLotId(entrustLot.getEntrustLotId());
				Carrier carrier = carrierMapper.selectCarrierById(entrustLot.getCarrierId());
				if (fee != null && BigDecimal.ZERO.compareTo(fee) != 0) {
					List<PayDetail> payDetailList = new ArrayList<>();
					PayDetail payDetail = new PayDetail();
					payDetail.setPayDetailId(IdUtil.simpleUUID());
					payDetail.setVbillno(payDetailService.createPayDetailVbillno("1".equals(entrustLot.getIsFleetData()) ? 1 : 0));
					payDetail.setVbillstatus(1);//应付单状态  默认已确认
					payDetail.setFreeType("1");//在途费
					payDetail.setCostTypeOnWay("27"); //在途费用类型 字典获取  异常赔款	27
					payDetail.setTransFeeCount(fee.negate());//取负数  前台为正数
					payDetail.setUngotAmount(fee.negate());//取负数  前台为正数
					payDetail.setLotId(entrustLot.getEntrustLotId());
					payDetail.setLotno(entrustLot.getLot());
					payDetail.setCarrierId(entrustLot.getCarrierId());
					payDetail.setCarrName(entrustLot.getCarrierName());
					payDetail.setCarrCode(carrier.getCarrCode());
					payDetail.setBalaCorp(entrustLot.getBalaCorp());
					payDetail.setBalatype(entrustLot.getBalatype());
					payDetail.setDriverMobile(entrustLot.getDriverMobile());
					payDetail.setDriverName(entrustLot.getDriverName());
					payDetail.setCarno(entrustLot.getCarNo());
					//payDetail.setReqDeliDate(entrustLot.getReqDeliDate());  //取实际提货时间
					//payDetail.setReqArriDate(entrustLot.getReqArriDate());  //去实际到货时间
					payDetail.setRegScrId(pageId);
					//赔款
					payDetail.setIncomeRemark(2);
					payDetail.setIsFleetData(entrustLot.getIsFleetData());
					//备注
					payDetail.setMemo(memo);
					//TODO 业务口的分配车队只能是1对1
					if (entrusts.size() == 1) {
						Entrust entrust = entrusts.get(0);
						if ("0".equals(entrust.getIsFleetData()) && !"0".equals(entrust.getIsFleetAssign())) {//is_Fleet_Data = 0 and  is_Fleet_Assign != 0
							//找出对应的分配车队发货单
							Invoice invoiceAll = invoiceMapper.selectInvoiceByBizEntrustId(entrust.getEntrustId());
							if (invoiceAll != null && !invoiceAll.getVbillstatus().equals("0")) {
								//不是新建状态需要插入应付
								ReceiveDetailVO receiveDetail = new ReceiveDetailVO();

								//拷贝发货单信息
								BeanUtils.copyBeanProp(receiveDetail, invoiceAll);
								receiveDetail.setReceiveDetailId(IdUtil.simpleUUID());
								//结算客户id
								receiveDetail.setBalaCustomer(invoiceAll.getBalaCustomerId());
								receiveDetail.setVbillno(receiveDetailService.createReceiveDetailVbillno(1));
								//客户名称存客户简称
								receiveDetail.setCustName(invoiceAll.getCustAbbr());
								//结算方式
								receiveDetail.setBalatype(invoiceAll.getBalaType());
								//应收单据状态
								receiveDetail.setVbillstatus(payDetail.getVbillstatus());
								receiveDetail.setInvoiceId(invoiceAll.getInvoiceId());
								//发货单号
								receiveDetail.setInvoiceVbillno(invoiceAll.getVbillno());
								//总重量
								receiveDetail.setFeeWeightCount(invoiceAll.getWeightCount());
								//是否原始单据 默认原始单据
								receiveDetail.setVbillType("1");
								receiveDetail.setDelFlag(0);
								//结算公司id
								receiveDetail.setBalaCorp(invoiceAll.getBalaCorpId());
								receiveDetail.setFreeType(payDetail.getFreeType());
								//
								receiveDetail.setTransFeeCount(payDetail.getTransFeeCount());
								receiveDetail.setRegScrId(pageId);
								receiveDetail.setCorScrId(pageId);

								receiveDetail.setIsFleetData("1");
								receiveDetail.setIsFleetAssign("1");
								receiveDetail.setFleetPayDetailId(payDetail.getPayDetailId());
								receiveDetail.setPayoutMark(1); //异常标识

								receiveDetailService.insertReceiveDetailAndAdjustRecord(receiveDetail, false);
								//等分配车队的应付数据保存完成  还要回填原来车队应收中对应的应付表的id
								payDetail.setIsFleetAssign("1");//分配车队的应付保存其状态为1
								payDetail.setFleetReceiveDetailId(receiveDetail.getReceiveDetailId());
								/*
								 * 调整车队的发货单是否加入对账包状态
								 */
								List<ReceiveDetailVO> receiveDetailVOS = receiveDetailMapper.selectReceiveByInvoiceId(invoiceAll.getInvoiceId());

								//已对账的数量
								long reconciledCount = receiveDetailVOS.stream().filter(x -> ReceiveDetailStatusEnum.RECONCILED.getValue() == x.getVbillstatus()).count();

								Invoice invoiceFleetUp = new Invoice();
								invoiceFleetUp.setInvoiceId(invoiceAll.getInvoiceId());
								if (reconciledCount == receiveDetailVOS.size()) {
									//已全部加入对账包
									invoiceFleetUp.setIsAddReceCheck(2);
									invoiceMapper.updateInvoice(invoiceFleetUp);
								} else if (reconciledCount < receiveDetailVOS.size() && reconciledCount > 0) {
									//部分加入对账包
									invoiceFleetUp.setIsAddReceCheck(1);
									invoiceMapper.updateInvoice(invoiceFleetUp);
								} else {
									invoiceFleetUp.setIsAddReceCheck(0);
									invoiceMapper.updateInvoice(invoiceFleetUp);
								}
							} else {
								//分配车队的应付保存其状态为2 该生成，但对应的车队发货单未确定，所以还未生成应收
								payDetail.setIsFleetAssign("2");
							}
						} else {
							payDetail.setIsFleetAssign("0");//不是分配车队的数据
						}
					}

					payDetailService.insertPayDetailAndAdjustRecord(payDetail, null, false);
					//payDetailMapper.insertPayDetail(payDetail);
					//成本分摊
					payDetailList.add(payDetail);
					//entrustList.add(entrust);
					List<Allocation> allocationByEntrustList = allocationService.getAllocationByEntrustList(payDetailList, entrusts, pageId);
					for (Allocation allocation : allocationByEntrustList) {
						allocation.setCostAllocationId(IdUtil.simpleUUID());
						allocation.setRegDate(new Date());
						allocation.setRegUserId(shiroUtils.getUserId().toString());
						allocation.setRegScrId(pageId);
						allocationMapper.insertAllocation(allocation);
					}
				}
			}

			InsuranceRecord insuranceRecord = new InsuranceRecord();

			//保存保险&损失
			List<TInsurance> insuranceList = ajaxParamsVO.getInsuranceList();
			for (int j = 0; j < insuranceList.size(); j++) {
				insuranceList.get(j).setRegDate(new Date()); //
				insuranceList.get(j).setRegUserId(shiroUtils.getUserId().toString());
				insuranceList.get(j).setRegUserName(shiroUtils.getSysUser().getUserName());
				insuranceList.get(j).setRegScrId("abnormal1"); //新增画面id
				insuranceList.get(j).setCorDate(new Date());
				insuranceList.get(j).setCorUserName(shiroUtils.getSysUser().getUserName());
				insuranceList.get(j).setCorUserId(shiroUtils.getUserId().toString());

				insuranceList.get(j).setId(IdUtil.simpleUUID());
				insuranceList.get(j).setExpId(entrustExp.getEntrustExpId());
				tInsuranceMapper.insertSelective(insuranceList.get(j));


				BigDecimal amount = insuranceList.get(j).getLossAmount();
				if (amount == null) {
					amount = BigDecimal.ZERO;
				}

				switch (insuranceList.get(j).getLossItem()) {
					case "货损":
						insuranceRecord.setAccidentAmount(
								insuranceRecord.getAccidentAmount() == null
										? amount
										: insuranceRecord.getAccidentAmount().add(amount)
						);
						break;
					case "二次运输费":
						insuranceRecord.setSecondaryTransportationFee(
								insuranceRecord.getSecondaryTransportationFee() == null
										? amount
										: insuranceRecord.getSecondaryTransportationFee().add(amount)
						);
						break;
					case "驳货费":
						insuranceRecord.setTransshipmentFee(
								insuranceRecord.getTransshipmentFee() == null
										? amount
										: insuranceRecord.getTransshipmentFee().add(amount)
						);
						break;
					case "律师费":
						insuranceRecord.setLawyerFee(
								insuranceRecord.getLawyerFee() == null
										? amount
										: insuranceRecord.getLawyerFee().add(amount)
						);
						break;
					case "律师差旅费":
						insuranceRecord.setLawyerTravelFee(
								insuranceRecord.getLawyerTravelFee() == null
										? amount
										: insuranceRecord.getLawyerTravelFee().add(amount)
						);
						break;
					case "诉讼费":
						insuranceRecord.setLitigationFee(
								insuranceRecord.getLitigationFee() == null
										? amount
										: insuranceRecord.getLitigationFee().add(amount)
						);
						break;
					case "其他费":
						insuranceRecord.setOtherFees(
								insuranceRecord.getOtherFees() == null
										? amount
										: insuranceRecord.getOtherFees().add(amount)
						);
						break;
					case "保险赔付":
						insuranceRecord.setInsuranceCompensationAmount(
								insuranceRecord.getInsuranceCompensationAmount() == null
										? amount
										: insuranceRecord.getInsuranceCompensationAmount().add(amount)
						);
						break;
					case "残值":
						insuranceRecord.setResidualValue(
								insuranceRecord.getResidualValue() == null
										? amount
										: insuranceRecord.getResidualValue().add(amount)
						);
						break;
					case "承运商赔付":
						insuranceRecord.setCarrierCompensationAmount(
								insuranceRecord.getCarrierCompensationAmount() == null
										? amount
										: insuranceRecord.getCarrierCompensationAmount().add(amount)
						);
						break;
					case "其他赔付":
						insuranceRecord.setOtherPayout(
								insuranceRecord.getOtherPayout() == null
										? amount
										: insuranceRecord.getOtherPayout().add(amount)
						);
						break;
					default:
						break;
				}
			}

			//不为空则需要插入T_INSURANCE_RECORD
			if (StringUtils.isNotEmpty(entrustExp.getInsurancePolicyId())) {
				insuranceRecord.setId(IdUtil.simpleUUID());
				insuranceRecord.setInsurancePolicyId(entrustExp.getInsurancePolicyId());
				insuranceRecord.setEntrustExpId(entrustExp.getEntrustExpId());
				insuranceRecord.setInsuranceDate(entrustExp.getAccidentTime());
				insuranceRecord.setClaimNumber(entrustExp.getReportNo());
				insuranceRecord.setRegScrId(pageId);

				//保险赔付金额+承运商赔付金额+其他赔付+残值
				BigDecimal add = NumberUtil.add(insuranceRecord.getInsuranceCompensationAmount(),
						insuranceRecord.getCarrierCompensationAmount(),
						insuranceRecord.getOtherPayout(), insuranceRecord.getResidualValue());
				//货损+二次运输费用+驳货费+律师费+律师差旅费+诉讼费+其他费
				BigDecimal add1 = NumberUtil.add(insuranceRecord.getAccidentAmount(),
						insuranceRecord.getSecondaryTransportationFee(),
						insuranceRecord.getTransshipmentFee(),
						insuranceRecord.getLawyerFee(),
						insuranceRecord.getLawyerTravelFee(),
						insuranceRecord.getLitigationFee(),
						insuranceRecord.getOtherFees());

				insuranceRecord.setCompanyProfitLoss(NumberUtil.sub(add, add1));
				insuranceRecordMapper.insertSelective(insuranceRecord);
			}

			//保存异常跟踪
			List<TExceptionTrack> exceptionTrackList = ajaxParamsVO.getExceptionTrackList();
			for (int j = 0; j < exceptionTrackList.size(); j++) {
				exceptionTrackList.get(j).setRegDate(new Date()); //
				exceptionTrackList.get(j).setRegUserId(shiroUtils.getUserId().toString());
				exceptionTrackList.get(j).setRegUserName(shiroUtils.getSysUser().getUserName());
				exceptionTrackList.get(j).setRegScrId("abnormal1"); //新增画面id
				exceptionTrackList.get(j).setCorDate(new Date());
				exceptionTrackList.get(j).setCorUserName(shiroUtils.getSysUser().getUserName());
				exceptionTrackList.get(j).setCorUserId(shiroUtils.getUserId().toString());

				exceptionTrackList.get(j).setId(IdUtil.simpleUUID());
				exceptionTrackList.get(j).setExpId(entrustExp.getEntrustExpId());
				tExceptionTrackMapper.insertSelective(exceptionTrackList.get(j));
			}
			return i > 0;
		}else {
			TEntrustExp tEntrustExp = new TEntrustExp();
			EntrustExp entrustExp = ajaxParamsVO.getEntrustExp();
			BeanUtils.copyProperties(entrustExp,tEntrustExp);

			//主异常关联异常字典表删除
			TExceptionDictExample exceptionDictExample = new TExceptionDictExample();
			exceptionDictExample.createCriteria().andEntrustExpIdEqualTo(entrustExp.getEntrustExpId());
			int i1 = exceptionDictMapper.deleteByExample(exceptionDictExample);

			//异常追踪删除
			TExceptionTrackExample tExceptionTrackExample = new TExceptionTrackExample();
			tExceptionTrackExample.createCriteria().andExpIdEqualTo(entrustExp.getEntrustExpId());
			int i7 = tExceptionTrackMapper.deleteByExample(tExceptionTrackExample);

			//主异常信息//主异常还是修改
			entrustExp.setCorDate(new Date());
			entrustExp.setCorScrId(pageId);
			entrustExp.setCorUserName(shiroUtils.getSysUser().getUserName());
			entrustExp.setCorUserId(shiroUtils.getUserId().toString());
			int i = tEntrustExpMapper.updateByPrimaryKeySelective(tEntrustExp);

			//保存异常主表关联字典
			String expType = entrustExp.getExpType();
			String[] split_expType = expType.split(",");
			for (String s : split_expType) {
				TExceptionDict exceptionDict = new TExceptionDict();
				exceptionDict.setId(IdUtil.simpleUUID());
				exceptionDict.setEntrustExpId(entrustExp.getEntrustExpId());
				exceptionDict.setDictCode((Short.parseShort(s)));
				exceptionDictMapper.insertSelective(exceptionDict);
			}

			//保存异常跟踪
			List<TExceptionTrack> exceptionTrackList = ajaxParamsVO.getExceptionTrackList();
			for (int j = 0; j < exceptionTrackList.size(); j++) {
				exceptionTrackList.get(j).setRegDate(new Date()); //
				exceptionTrackList.get(j).setRegUserId(shiroUtils.getUserId().toString());
				exceptionTrackList.get(j).setRegUserName(shiroUtils.getSysUser().getUserName());
				exceptionTrackList.get(j).setRegScrId("abnormal1"); //新增画面id
				exceptionTrackList.get(j).setCorDate(new Date());
				exceptionTrackList.get(j).setCorUserName(shiroUtils.getSysUser().getUserName());
				exceptionTrackList.get(j).setCorUserId(shiroUtils.getUserId().toString());

				exceptionTrackList.get(j).setId(IdUtil.simpleUUID());
				exceptionTrackList.get(j).setExpId(entrustExp.getEntrustExpId());
				tExceptionTrackMapper.insertSelective(exceptionTrackList.get(j));
			}
			return i > 0;
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean dataRecover() {
		TEntrustExpExample tEntrustExpExample = new TEntrustExpExample();
		tEntrustExpExample.createCriteria().andDelFlagEqualTo((short) 0);
		List<TEntrustExp> tEntrustExps = tEntrustExpMapper.selectByExample(tEntrustExpExample);

		int i3 = 0;
		for (int i = 0; i < tEntrustExps.size(); i++) {
			TEntrustExp tEntrustExp = tEntrustExps.get(i);
			tEntrustExp.setCaseStatus("3");
			tEntrustExp.setAccidentTime(tEntrustExp.getExpOccTime());
			tEntrustExpMapper.updateByPrimaryKeySelective(tEntrustExp);
			//T_EXP_ENTRUST_MORE
			TExpEntrustMore tExpEntrustMore = new TExpEntrustMore();
			tExpEntrustMore.setId(IdUtil.simpleUUID());
			tExpEntrustMore.setExpId(tEntrustExp.getEntrustExpId());
			tExpEntrustMore.setCarnoId(tEntrustExp.getCarnoId());
			tExpEntrustMore.setCarno(tEntrustExp.getCarno());
			tExpEntrustMore.setDriverId("");
			tExpEntrustMore.setCarrierId(tEntrustExp.getCarrierId());
			//TODO 根据承运商id查询承运商名字

			tExpEntrustMore.setDriverName(tEntrustExp.getDriverName());
			tExpEntrustMore.setRegUserId(tEntrustExp.getRegUserId());
			tExpEntrustMore.setRegUserName(tEntrustExp.getRegUserName());
			tExpEntrustMore.setRegDate(tEntrustExp.getRegDate());
			tExpEntrustMore.setRegScrId(tEntrustExp.getRegScrId());
			tExpEntrustMore.setCorUserId(tEntrustExp.getCorUserId());
			tExpEntrustMore.setCorUserName(tEntrustExp.getCorUserName());
			tExpEntrustMore.setCorDate(tEntrustExp.getCorDate());
			tExpEntrustMore.setDelFlag((short) 0);
			//通过委托单查询运单
			String entrustId = tEntrustExp.getEntrustId();
			tExpEntrustMore.setEntrustId(entrustId);
			Entrust entrust = entrustMapper.selectEntrustById(entrustId);
			if(ObjectUtil.isNotEmpty(entrust)) {
				tExpEntrustMore.setLotId(entrust.getLotId());
				tExpEntrustMore.setLotNo(entrust.getLot());
			}
			int i1 = tExpEntrustMoreMapper.insertSelective(tExpEntrustMore);

			//T_INSURANCE
			TInsurance tInsurance = new TInsurance();
			tInsurance.setId(IdUtil.simpleUUID());
			tInsurance.setEntrustId(tEntrustExp.getEntrustId());
			tInsurance.setLossItem(""); //损失明细
			tInsurance.setVbillno(entrust.getLot());  //单号
			tInsurance.setLossAmount(tEntrustExp.getActualLoss());//损失
			tInsurance.setRemark(tEntrustExp.getNote());
			tInsurance.setInsuranceType("1");
			tInsurance.setExpId(tEntrustExp.getEntrustExpId());
			tInsurance.setRegUserId(tEntrustExp.getRegUserId());
			tInsurance.setRegUserName(tEntrustExp.getRegUserName());
			tInsurance.setRegDate(tEntrustExp.getRegDate());
			tInsurance.setDelFlag((short) 0);
			int i2 = tInsuranceMapper.insertSelective(tInsurance);
			//t_entrust_lot 运单锁定需要将lock_pay状态置为1 不需要

			//t_exception_track
			TExceptionTrack tExceptionTrack = new TExceptionTrack();
			tExceptionTrack.setId(IdUtil.simpleUUID());
			tExceptionTrack.setDelFlag((short) 0);
			tExceptionTrack.setExpId(tEntrustExp.getEntrustExpId());
			tExceptionTrack.setTrackingDate(tEntrustExp.getHandleTime());
			tExceptionTrack.setTrackingContent(tEntrustExp.getHandleNote());
			tExceptionTrack.setRegUserId(tEntrustExp.getRegUserId());
			tExceptionTrack.setRegDate(tEntrustExp.getRegDate());
			i3 = tExceptionTrackMapper.insertSelective(tExceptionTrack);
		}
		return i3 > 0;
	}
}
