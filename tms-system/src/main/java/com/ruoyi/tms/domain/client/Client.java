package com.ruoyi.tms.domain.client;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.MybatisAutoFill;
import com.ruoyi.common.handler.MyDataHandler;
import com.ruoyi.system.domain.SysDept;
import com.ruoyi.system.domain.SysUser;
import com.ruoyi.tms.vo.client.CustomerServiceVO;
import lombok.Data;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-08-29
 */
@Data
public class Client extends BaseEntity {
	private static final long serialVersionUID = 1L;
	
	/** 客户ID */
	private String customerId;
	/** 删除标志 */
	private Integer delFlag;
	/** 删除时间 */
	private Date delDate;
	/** 客户编码 */
	@Excel(name = "客户编码",sort = 4,type= Excel.Type.EXPORT,width = 20)
	private String custCode;
	/** 客户名称 */
	@Excel(name = "客户名称",sort = 5,width = 32)
	@NotNull(message = "客户名称不能为空")
	@Size(min = 0, max = 60, message = "客户名称长度不能超过60个字符")
	private String custName;
	/** 客户简称 */
	@Excel(name = "客户简称",sort = 6)
	@NotNull(message = "客户简称不能为空")
	@Size(min = 0, max = 15, message = "客户简称长度不能超过15个字符")
	private String custAbbr;

	/** 客户类型 字典表 */
	private String custType;
	/** 是否认证 （0:未认证  1: 已认证） */
	private String ifAuthentication;
	/** 运营部(运营组) */
	@NotNull(message = "运营组不能为空")
	@Excel(name = "运营组",sort = 2, type = Excel.Type.IMPORT)
	private String salesDept;
	/** 运营部(运营组)  若依目前不支持一对多的导入,只支持一对多导出 */
	@Excel(name = "运营组",sort = 2, type = Excel.Type.EXPORT, targetAttr = "deptName")
	private SysDept sysDept;

	/**
	 * 运营部
	 */
	private String opsDeptId;
	@Excel(name = "运营部",sort = 1)
	private String opsDeptName;

	/**
	 * 管理部
	 */
	private String mgmtDeptId;
	@Excel(name = "管理部",sort = 0)
	private String mgmtDeptName;

	/** 结算组 */
	@Excel(name = "结算组",sort = 8,dict = "balaDept")
	private String balaDept;
	/** 驻场组 */
	@Excel(name = "驻场组",sort = 9, type = Excel.Type.IMPORT)
	private String stationDept;

	@Excel(name = "驻场组",sort = 10, type = Excel.Type.EXPORT, targetAttr = "deptName")
	private SysDept stationDeptName;

	/** 运营公司 */
	@NotNull(message = "运营公司不能为空")
	@Excel(name = "运营公司",sort = 11,readConverterExp = "YK=亿鼎科技,TG=江苏泰港,QJ=南通群健,JY=上海吉源,KH=浙江康皓,YC=南通远程,YS=江苏亿尚,XYD=江苏欣亿鼎,SB=山西顺邦,QX=南通琦欣,YD=亿鼎物流,MD=南通明达,JH=南通吉华,MY=江苏铭源,HK=南通皓凯,DW=南通鼎旺,DH=南通鼎辉")
	private String balaCorp;

	/** 结算公司 */
	@NotNull(message = "结算公司不能为空")
	@Excel(name = "结算公司",sort = 12,readConverterExp = "YK=亿鼎科技,TG=江苏泰港,QJ=南通群健,JY=上海吉源,KH=浙江康皓,YC=南通远程,YS=江苏亿尚,XYD=江苏欣亿鼎,SB=山西顺邦,QX=南通琦欣,YD=亿鼎物流,MD=南通明达,JH=南通吉华,MY=江苏铭源,HK=南通皓凯,DW=南通鼎旺,DH=南通鼎辉")
	private String operateCorp;

	/** 公司 */
	private String corpId;
	/** 业务员 是否不需要了? */
	//@Excel(name = "业务员")
	private String psndoc;
	/** 业务员联系方式 */
	private String psncontact;
	/** 客户地址（省）ID */
	private String provinceId;
	/** 客户地址（市）ID */
	private String cityId;
	/** 客户地址（区）ID */
	private String areaId;
	/** 客户地址（详细地址） */
	@Excel(name = "客户详细地址",sort = 13, width = 32)
	private String address;
	/** 联系人 */
//	@Excel(name = "联系人")
	@NotNull(message = "联系人不能为空")
	private String contact;
	/** 联系人职位 */
	//@Excel(name = "联系人职位")
	private String contactPost;
	/** 联系人电话 */
//	@Excel(name = "联系人电话", handler = MyDataHandler.class)
	@NotNull(message = "联系人电话不能为空")
    @Size(max = 11, message = "手机号码长度不能超过11个字符")
	private String phone;
	/** 联系人手机 */
	private String mobile;
	/** 联系人邮箱 */
	private String email;
	/** 联系人传真 */
	private String fax;
	/** 联系人邮政编码 */
	private String zipcode;
	/** 开票公司 */
	@Excel(name = "开票公司",sort = 14, dict = "balaCorp")
	@NotNull(message = "开票公司不能为空")
	private String billingCorp;
	/** 开票日期 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date billingDate;
	/** 纳税识别号 */
	private String taxIdentify;
	/** 结算方式 数据字典表 */
	private String balaType;
	/** 开票类型 数据字典表 */
	private String billingType;
	/** 发票抬头 */
	@NotNull(message = "发票抬头不能为空")
	private String billingPayable;
	/** 开户银行 */
	private String bank;
	/** 折扣率 */
	private Integer discountRate;
	/** 账期 */
	private Integer accountPeriod;
	/** 创建人 */
	@MybatisAutoFill(createBy = true)
	private String regUserId;
	/** 账期提前量 */
	private Integer accPeriodAhead;
	/** 开票提前量 */
	private Integer billingAhead;
	/** 信用额度 */
	private Integer creditAmount;
	/** 账号名称 */
	private String accountName;
	/** 开户账号 */
	@NotNull(message = "开户账号不能为空")
	private String bankAccount;
	/** 注册地址 */
	private String registerAddr;
	/** 计费规则 */
	private Integer billingRule;
	/** 法人代表 */
	private String legalRepresent;
	/** 注册资金 */
	private Integer registerCapital;
	/** 收件人 */
	private String addressee;
	/** 收件人联系方式 */
	private String addresseeContact;
	/** 收件人联系地址 */
	private String addresseeAddr;
	/** 网址 */
	private String website;
	/** 是否失效  Y：失效 N：有效 */
	private String lockedFlag;
	/** 备注 */
	private String memo;
	/** 客户活跃程度 */
	private Integer custActivity;

	/** 修改人 */
	@MybatisAutoFill(updateBy = true)
	private String corUserId;
	/** 修改时间 */
	@MybatisAutoFill(updateTime = true)
	private Date corDate;

	private String regScrId;

	private String corScrId;
	/** 营业执照号 */
	//@Excel(name = "营业执照号(证件号)", width = 20)
	private String businesslicense;
    /** 法人身份证*/
	private String legalCard;
    /** 用户性质*/
    private String customerType;
	/**
	 * 省名称
	 */
	private String provinceName;
	/**
	 *市名称
	 */
	private String cityName;
	/**
	 * 区名称
	 */
	private String areaName;
	/**
	 * 删除人ID
	 */
	private String delUserId;
	/**
	 * 业务员名称
	 */
	private String psndocName;
	/**
	 * 开户银行ID
	 */
	private String bankId;
	/**
	 * 客户集团
	 */
	@Excel(name = "客户集团",sort = 3, type = Excel.Type.EXPORT, targetAttr = "groupName", width = 30)
	private Group group;

	/**
	 * APP提货联系人
	 */
	@Excel(name = "APP联系人",sort = 16)
	private String appDeliContact;
	/**
	 * APP联系人手机
	 */
	@Excel(name = "APP联系人手机",sort = 17, handler = MyDataHandler.class)
	@Size(max = 11, message = "手机号码长度不能超过11个字符")
	private String appDeliMobile;
	/**
	 * 审核状态
	 */
	private Integer checkStatus;
	/**
	 * 审核人
	 */
	private String checkMan;
	/**
	 * 审核时间
	 */
	private Date checkDate;

	/** 是否设置指导价*/
	@Excel(name = "是否设置指导价", sort = 18,readConverterExp = "0=否,1=是")
	private Integer crtGuidePrice;

	/** 税率*/
	private BigDecimal tariff;

	/**
	 * 是否需要回单 0否 1是
	 */
	@Excel(name = "是否需要回单",sort = 19, readConverterExp = "0=否,1=是")
	private Integer isNeedReceipt;

	/** 是否跟踪*/
	@Excel(name = "是否跟踪",sort = 20, readConverterExp = "0=否,1=是")
	private Integer isNeedTrace;

	/** 结算客户*/
	private List<CustBala> custBalaList;

	/**
	 * 结算客户 原来一对多 现在一对一
	 */
	@Excel(name = "关联结算客户",sort = 21, width = 30)
	private String relatedCustName;

	/** 客户收发货地址*/
	private List<CustAddress> custAddressList;
	/** 客户货品信息*/
	private List<CustGoods> custGoodsList;
	/** 客户线路*/
	private List<CustTransLine> custTransLineList;
	/**开票信息*/
	private List<CustBilling> custBillingList;
	/**客户级别*/
	@Excel(name="客户级别", sort = 22,type = Excel.Type.EXPORT, readConverterExp = "1=A,2=B,3=C,4=D,5=S")
	private Integer customerLevel;
	/**客户集团id*/
	private String groupId;
	/** 账期*/
	@Excel(name="对账期(天)",sort = 23)
	private String paymentDays;
	/**开票期*/
	@Excel(name = "申请开票期(天)",sort = 24)
	private Integer invoiceDays;
	/**收款期*/
	@NotNull(message = "合同账期不能为空")
	@Excel(name = "合同账期(天)",sort = 25)
	private Integer collectionDays;

	/** 合同到期时间 */
	@Excel(name = "合同到期日",sort = 25)
	private String invalidDate;

	/**是否锁定第三方费用：0否，1是*/
//	@Excel(name = "是否锁定第三方费用", readConverterExp = "0=否,1=是")
	private Integer isLockOtherFee;
	/**调整额*/
	@Excel(name = "调整配置额",sort = 26)
	private BigDecimal adjustment;
	/**
	 * 特殊日期
	 */
	@Excel(name = "特殊日期(天)",sort = 27)
	//@TableField(strategy = FieldStrategy.IGNORED)
	private Integer specialDate;
	/**
	 * 企业性质
	 */
	@Excel(name = "企业性质",sort = 28,dict = "enterpriseNature")
	private Integer enterpriseNature;
	/**
	 * 客户来源
	 */
	@Excel(name = "客户来源",sort = 29, type = Excel.Type.EXPORT, dict = "customerSource")
	private Integer customerSource;

	/**
	 * 开票信息  一对多
	 */
	@Excel(name = "开票明细",sort = 30, type = Excel.Type.EXPORT,  width = 30, wrapText = true)
	private String billingDetail;

	@Excel(name = "创建人",sort = 31, type = Excel.Type.EXPORT, targetAttr = "userName")
	private SysUser user;

	/** 创建时间 */
	@MybatisAutoFill(createTime = true)
	@Excel(name="创建时间",sort = 32,dateFormat="yyyy-MM-dd HH:mm:ss",width=18)
	private Date regDate;

	@Excel(name = "是否启用",sort = 33, type = Excel.Type.EXPORT, readConverterExp ="0=启用,1=停用")
	private Integer isEnabled;

	//禁用时间


	/**
	 * 装卸费单价
	 */
	@Excel(name = "装卸单价",sort = 34,width = 32)
	private BigDecimal handlingCharges;

	/**
	 * 计费方式
	 */
	@Excel(name = "装卸计价",sort = 35, readConverterExp = "0=按件,1=按方,2=按吨")
	private String handlingChargesType;

	/**
	 * 是否启用合同价计费
	 */
	private Integer enableContractPrice;

	/**
	 * 删选临期
	 */
	private Integer temporary;

	/**
	 * 客户对应的客户 可能有多个
	 */
//	@Excel(name = "客户联系人", type = Excel.Type.EXPORT)
//	private List<SysUserImportDTO> customerServiceUser;

	/**
	 * 客户对应的客户 可能有多个
	 */
	private List<CustomerService> customerServiceList;
	private List<CustomerServiceVO> customerServiceVOList;

	@Excel(name = "客户对应客服",sort = 36, type = Excel.Type.EXPORT,width = 30, wrapText = true)
	private String customerContacts;

	@Excel(name = "客户对应业务员",sort = 37, type = Excel.Type.EXPORT,width = 30, wrapText = true)
	private String customerClerks;

	/**
	 * 平台费率
	 */
	private BigDecimal platRate;

	/**
	 * 平台税点
	 */
	private BigDecimal platTax;
	/**
	 * 运营部id
	 */
	private String salesId;
//	@Excel(name = "运营部")
	private String salesName;


	/** 合同价精确到市或区  0区 1市*/
	private Integer accurateRegion;
	/** 合同价精确到到货详细地址 0否  1是*/
	private Integer accurateArriDetail;

	/** 合同价精确到货品 0否  1是*/
	private Integer accurateGoods;
	/** 是否开启价格浮动  0否  1是*/
	private Integer contractPriceFloat;

	/** 合同价浮动价开始日期*/
	private Date floatDateStart;
	/** 合同价浮动价结束日期*/
	private Date floatDateEnd;

	private Integer isExistContract;

	/**
	 * 指导价浮动比率
	 */
	private BigDecimal referenceRate;

	/**
	 * 是否议价 0否1是
	 */
	@Excel(name = "是否议价",sort = 38, readConverterExp = "0=否,1=是")
	private Integer ifBargain;

	/**
	 * 是否特殊指导价 0否1是
	 */
	private Integer isSpecialReferencePrice;

	/**
	 * 合同价审核  0未审核  1已审核
	 */
	private Integer contractPriceReview;
	/**
	 * 审核人
	 */
	private String contractPriceReviewUserId;
	/**
	 * 审核人
	 */
	private String contractPriceReviewUserName;
	/**
	 * 审核时间
	 */
	private Date contractPriceReviewDate;
	/**
	 * 是否自动调度  0否 1是
	 */
	private Integer isAutoDispatch;
	/** 自动调度配置类型 0线路+应收  1背靠背 2区间*/
	private Integer autoDispatchType;

	/**
	 * 自动调度  中转站地址id
	 */
	private String autoSubsectionAddrId;

	/**
	 * 是否特殊流转 0否1是  “是”流转赵羽桐审核用费用
	 */
	private Integer isSpecialTransfer;

	/** 自动调度时 是否可以录入送货费 0不可以 1可以*/
	private Integer autoDispatchDeliFee;
	/** 自动调度时 是否可以录入提货费 0不可以 1可以*/
	private Integer autoDispatchPickUpFee;

	/** 发货单运营组*/
	@Excel(name = "单据运营组",sort = 39)
	private String invoiceSalesDeptName;

	/**
	 * 客户类别
	 */
	private String clientType;
	/**
	 * 客户类别 0业务客户  1车队客户  2园区客户
	 */
	private Integer sourceType;

	/**
	 * 下单时合同价类型 0仅合同价  1可溢价
	 */
	private Integer contractPriceType;

	/**
	 * 允许无合同下单 0否  1是
	 */
	@Excel(name = "允许无合同下单",sort = 38, readConverterExp = "0=否,1=是")
	private Integer contractNeedType;

	/**
	 * 注意事项附件id
	 */
	private String noticeFileId;

	/**
	 * 引荐人
	 */
	@Excel(name = "引荐人",sort = 40)
	private String referrer;

	/**
	 * 标的金额
	 */
	@Excel(name = "标的金额",sort = 41)
	private BigDecimal subjectAmount;

	private String saleDeptName;
	private String saleDeptParentName;

	@Excel(name = "禁用时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Excel.Type.EXPORT)
	private Date disabledTime;

	/**
	 * 财务结算审核  0无需审核  1需要审核
	 */
	private Integer settlementCheck;

	/**
	 * 发货单确认时是否校验下单联系人及电话  0不  1需要
	 */
	private Integer invContactCheck;

}
