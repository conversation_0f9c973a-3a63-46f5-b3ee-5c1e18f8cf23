package com.ruoyi.tms.domain.client;

import com.ruoyi.common.annotation.MybatisAutoFill;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 自动调度配置表
 *
 * <AUTHOR> zjx
 * @version : v1.0.0
 * @date : 2023-07-24 10:25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AutoDispatchConfig extends BaseEntity {
    /**
     * ID
     */
    private String id;

    /**
     * 自动调度配置类型 0线路+应收  1背靠背  2区间
     */
    private Integer autoDispatchType;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 客户简称
     */
    private String custAbbr;

    /**
     * 承运商主键ID
     */
    private String carrierId;

    /**
     * 承运商名称
     */
    private String carrName;

    /**
     * 提货(省)
     */
    private String deliProvinceId;

    /**
     * 提货（市）
     */
    private String deliCityId;

    /**
     * 提货(区)
     */
    private String deliAreaId;

    /**
     * 提货省名称
     */
    private String deliProName;

    /**
     * 提货市名称
     */
    private String deliCityName;

    /**
     * 提货区名称
     */
    private String deliAreaName;

    /**
     * 到货地址（省）
     */
    private String arriProvinceId;

    /**
     * 到货地址（市）
     */
    private String arriCityId;

    /**
     * 到货地址（区）
     */
    private String arriAreaId;

    /**
     * 到货省名称
     */
    private String arriProName;

    /**
     * 到货市名称
     */
    private String arriCityName;

    /**
     * 到货区名称
     */
    private String arriAreaName;

    /**
     * 应付扣减的金额或比率
     */
    private BigDecimal deductionAmount;

    /**
     * 应付扣减类型
     * 0金额  应付单价=应收单价-输入金额
     * 1比率  应付单价=应收单价 *（1-输入比率）
     * 2固定（总价）  应付总价=输入金额
     * 3固定（单价）  应付单价=输入金额
     * 4区间价格
     */
    private Integer deductionType;

    /**
     * 应付扣减类型为扣减应收时， 0仅运费  1运费+在途
     */
    private Integer deductionFeeType;

    /**
     * 创建人ID
     */
    @MybatisAutoFill(createBy = true)
    private String regUserId;

    /**
     * 创建人名称
     */
    @MybatisAutoFill(createByName = true)
    private String regUserName;

    /**
     * 新增画面ID
     */
    private String regScrId;

    /**
     * 新增日期
     */
    @MybatisAutoFill(createTime = true)
    private Date regDate;

    /**
     * 修改人ID
     */
    @MybatisAutoFill(updateBy = true)
    private String corUserId;

    /**
     * 修改人名称
     */
    @MybatisAutoFill(updateByName = true)
    private String corUserName;

    /**
     * 修改时间
     */
    @MybatisAutoFill(updateTime = true)
    private Date corDate;

    /**
     * 修改画面ID
     */
    private String corScrId;

    /**
     * 删除标志1:删除
     */
    private Integer delFlag;

    /**
     * 删除人
     */
    private String delUserid;

    /**
     * 删除时间
     */
    private Date delDate;

    /**
     * 计价方式
     */
    private String billingMethod;
    /**
     * 车长ID
     */
    private String carLen;
    /**
     * 车长名称
     */
    private String carLenName;
    /**
     * 车型ID
     */
    private String carType;
    /**
     * 车型名称
     */
    private String carTypeName;
    /**
     * 相同数据id   用于确定相同的数据
     */
    private String sameId;

    /**
     * 开票类型
     */
    private String billingType;

    /**
     * 油卡比例
     */
    private Double oilRatio;

    /**
     * 到货地址名称
     */
    private String arriAddrName;
    /**
     * 到货地址类型  0省市区  1地址名称
     */
    private Integer arriType;

    /**
     * 油卡比例类型  0百分比  1固定金额
     */
    private Integer oilType;

    /** 油卡类型 1预付油卡  3到付油卡  5回付油卡 */
    private String oilCostType;

    /** 货品id*/
    private String goodsId;
    /** 货品名称*/
    private String goodsName;

    /**
     * 版本id
     */
    private String versionId;

    /**
     * 货品特性  0普通品  1危险品
     */
    private Integer goodsCharacter;

    /**
     * 是否往返订单   0否  1是
     */
    private Integer isRoundTrip;


    private String corDateStr;
}