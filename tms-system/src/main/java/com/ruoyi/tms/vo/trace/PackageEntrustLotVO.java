package com.ruoyi.tms.vo.trace;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.tms.domain.carrier.EntrustExp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * PackageEntrustLotVO
 *
 * <AUTHOR> zjx
 * @version : v1.0.0
 * @date : 2022-11-25 13:12
 */
@Data
public class PackageEntrustLotVO {

    /**
     * 运单id
     */
    private String lotId;

    /**
     * 应付单状态
     */
    private Integer vbillstatus;

    /**
     * 应付对账id
     */
    private String payCheckSheetId;

    /**
     * 总金额
     */
    private BigDecimal transFeeCount;
    /** 现金总金额*/
    private BigDecimal transFeeCash;
    /** 油卡总金额*/
    private BigDecimal transFeeOil;

    /**
     * 已付金额
     */
    private BigDecimal gotAmount;
    /**
     * 未付金额
     */
    private BigDecimal ungotAmount;
    /**
     * 对账单号
     */
    private String sheetVbillno;

    private Integer lotG7Syn;
    private Integer lotG7Start;
    private Integer lotG7End;
    private String lotG7Msg;

    private BigDecimal oilRatio;
    /**
     * 运单号
     */
    private String lotno;
    /**
     * 创建时间
     */
    private Date regDate;

    private String payWay;

    /**
     * 提货地址
     */
    private String deliAddr;
    /**
     * 收货地址
     */
    private String arriAddr;

    /**
     * 车牌号
     */
    private String carno;
    /**
     * 承运商ID
     */
    private String carrierId;
    /**
     * 承运商名称
     */
    private String carrName;

    private String payDetailIdList;

    /** 委托单要求提货日期 */
    private String entrustReqDeliDate;
    /**
     * 创建人
     */
    private String regUserId;


    private String status;
    /**
     * 发货单号
     */
    private String invoiceVbillno;
    /**
     * 司机名称
     */
    private String driverName;
    /** 运营组名称 */
    private String salesDeptName;
    /**
     * 客户简称
     */
    private String custAbbr;

    private Date reqDeliDateStart;
    private Date reqDeliDateEnd;
    private String regUserName;

    private String balaCorp;

    private String goodsName;

    /**
     * 委托单数量
     */
    private Integer entrustCt;

    /** 总件数 */
    private Double numCount;
    /** 总重量 */
    private Double weightCount;
    /** 总体积 */
    private Double volumeCount;

    /** '0未确认、1部分确认、2已确认 */
    private Integer ifAllConfirm;
    /** 正本回单 0未上传、1部分上传、2已上传 */
    private Integer ifAllReceiptUpload;
    /** 正本回单 0未上传、1部分上传、2已上传 */
    private Integer ifAllReceipt;

    private String billingTypeLabel;
    private BigDecimal freightFeeRate;
    private BigDecimal oilCardRate;

    /** 提货地省 */
    private String deliProvince;
    /** 提货地市 */
    private String deliCity;
    /** 提货地区 */
    private String deliArea;
    /** 到货省 */
    private String arriProvince;
    /** 到货市 */
    private String arriCity;
    /** 到货区 */
    private String arriArea;

    /**运单状态*/
    private Integer lotVbillstatus;

    /**
     *  是否到货段  0不是  1是
     *
     */
    private Integer isArrivalSection;

    /**
     * 能否支付 0否 1是
     */
    private Integer couldPay;

    /**
     * 是否锁定运单应付(异常锁定) 1是 2否
     */
    private String lockPay;

    /**
     * 锁定付款 0否 1是
     */
    private Integer lockPayCarrier;
    /**
     * 锁定原因展示
     */
    private String lockPayReasonUnion;

    /**
     * 未核销的司机代收数量
     */
    private Long driverCollectionCt;

    private String singleLock;
    private String lockMemo;

    /** 异常扣款*/
    private BigDecimal abnormalDeduction;

    /** 零担运段类型 0提货段  1干线段  2送货段 */
    private Integer ltlType;

    /** 是否在审核中  0不在  1在*/
    private Integer isCheck;

    /** 异常信息*/
    private List<EntrustExp> entrustExpList;

    private String g7Corp;
    private String g7LotQst;
    private String g7DriverExt;
    private String g7CarExt;

    /** 是否自动调度生成  0不是  1是*/
    private Integer isAutoDis;
    /** 运输方式 */
    private String transType;

    private String invoiceMemo;
    private String lotMemo;
    private Integer spStatus;
    private String spNo;
}
