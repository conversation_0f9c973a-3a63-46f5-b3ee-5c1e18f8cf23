package com.ruoyi.tms.vo.client;

import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.tms.domain.client.AutoDispatchSection;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * autoDispatchVO
 *
 * <AUTHOR> zjx
 * @version : v1.0.0
 * @date : 2023-07-24 16:23
 */
@Data
public class AutoDispatchVO {
    /** 客户id*/
    private String customerId;
    /** 是否自动调度  0否 1是*/
    private Integer isAutoDispatch;
    /** 自动调度配置类型 0线路+应收  1背靠背 2区间*/
    private Integer autoDispatchType;
    /** 自动调度时 是否可以录入送货费 0不可以 1可以*/
    private Integer autoDispatchDeliFee;
    /** 自动调度时 是否可以录入提货费 0不可以 1可以*/
    private Integer autoDispatchPickUpFee;

    /** 自动调度  中转站地址id */
    private String autoSubsectionAddrId;

    private List<AutoDispatchConfigVO> autoDispatchConfigList;

    /** 分页信息*/
    private TableDataInfo tableDataInfo;


    @Data
    public static class AutoDispatchConfigVO {
        /**
         * ID
         */
        private String id;

        /**
         * 版本id
         */
        private String versionId;

        /**
         * 省市区
         */
        private String deliProvinceId;
        private String deliCityId;
        private String deliAreaId;
        private String deliProName;
        private String deliCityName;
        private String deliAreaName;

        private String arriProvinceId;
        private String arriCityId;
        private String arriAreaId;
        private String arriProName;
        private String arriCityName;
        private String arriAreaName;

        /** 到货区id集合*/
        private String arriAreaIds;
        private String arriAreaNames;

        /** 到货地址名称 */
        private String arriAddrName;
        /** 到货地址类型  0省市区  1地址名称 */
        private Integer arriType;


        /**
         * 应付扣减类型  0金额  1比率 2固定（总价） 3固定（单价） 4区间价格
         */
        private Integer deductionType;
        /**
         * 应付扣减金额
         */
        private BigDecimal deductionAmount;

        /**
         * 应付扣减类型为扣减应收时， 0仅运费  1运费+在途
         */
        private Integer deductionFeeType;

        /**
         * 承运商id
         */
        private String carrierId;
        /**
         * 承运商名称
         */
        private String carrName;

        /**
         * 计价方式
         */
        private String billingMethod;

        /**
         * 货品特性  0普通品  1危险品
         */
        private Integer goodsCharacter;
        /**
         * 是否往返订单   0否  1是
         */
        private Integer isRoundTrip;

        /**
         * 车长ID
         */
        private String carLen;
        /**
         * 车长名称
         */
        private String carLenName;
        /**
         * 车型ID
         */
        private String carType;
        /**
         * 车型名称
         */
        private String carTypeName;

        /**
         * 相同数据id   用于确定相同的数据
         */
        private String sameId;

        /**
         * 开票类型
         */
        private String billingType;

        /** 油卡比例*/
        private Double oilRatio;

        /** 油卡比例类型  0百分比  1固定金额 */
        private Integer oilType;

        /** 油卡类型 1预付油卡  3到付油卡  5回付油卡 */
        private String oilCostType;

        /** 货品id*/
        private String goodsId;
        /** 货品名称*/
        private String goodsName;
        
        /**
         * 序号  导入时用
         */
        private String number;

        private String corDateStr;

        private List<AutoDispatchSectionVO> autoDispatchSectionList;

        @Override
        public boolean equals(Object o) {
            if (o == this) {
                return true;
            }
            if (!(o instanceof AutoDispatchConfigVO)) {
                return false;
            }
            AutoDispatchConfigVO other = (AutoDispatchConfigVO) o;

            if (this.arriType == 1) {
                return Objects.equals(deliAreaId, other.deliAreaId) &&
                        Objects.equals(arriAddrName, other.arriAddrName) &&
                        Objects.equals(carrierId, other.carrierId) &&
                        Objects.equals(carLen, other.carLen) &&
                        Objects.equals(carType, other.carType) &&
                        Objects.equals(billingMethod, other.billingMethod);
            }else {
                return Objects.equals(deliAreaId, other.deliAreaId) &&
                        Objects.equals(arriAreaId, other.arriAreaId) &&
                        Objects.equals(carrierId, other.carrierId) &&
                        Objects.equals(carLen, other.carLen) &&
                        Objects.equals(carType, other.carType) &&
                        Objects.equals(billingMethod, other.billingMethod);

            }
        }

        @Override
        public int hashCode() {
            if (this.arriType == 1) {
                return Objects.hash(deliAreaId, arriAddrName, carrierId, carLen, carType, billingMethod);
            } else {
                return Objects.hash(deliAreaId, arriAreaId, carrierId, carLen, carType, billingMethod);
            }
        }

    }

    @Data
    public static class AutoDispatchSectionVO {
        /**
         * 合同价区间ID
         */
        private String id;

        /**
         * 区间开始
         */
        private BigDecimal startSection;

        /**
         * 区间结束
         */
        private BigDecimal endSection;

        /**
         * 区间开始运算符  0:大于 1: 大于等于
         */
        private Integer startOperator;

        /**
         * 区间结束运算符  2:小于 3 小于等于
         */
        private Integer endOperator;

        /**
         * 价格(可能是单价 可能是总价)
         */
        private BigDecimal price;

        /**
         * 是否是固定价格（0否 1是）
         */
        private Integer isFixedPrice;

        /**
         * 相同数据id   用于确定相同的数据
         */
        private String sameId;

    }


    /**
     * 合并相同配置
     *
     * @param dataList
     * @return
     */
    public static List<AutoDispatchVO.AutoDispatchConfigVO> mergeData(List<AutoDispatchVO.AutoDispatchConfigVO> dataList) {
        // 使用一个Map来存储相同数据的合并结果
        Map<String, AutoDispatchConfigVO> mergedDataMap = dataList.stream()
                .collect(Collectors.toMap(
                        AutoDispatchVO::generateKey,
                        data -> data,
                        AutoDispatchVO::mergeData
                ));

        // 将Map中的值转换回List
        return new ArrayList<>(mergedDataMap.values());
    }

    /**
     * 生成用于Map键的唯一标识  去重的依据
     * @param data
     * @return
     */
    private static String generateKey(AutoDispatchVO.AutoDispatchConfigVO data) {
        String key = data.getDeliAreaId() + "-" +
                data.getDeductionAmount() + "-" +
                data.getCarrierId() + "-" +
                data.getBillingMethod() + "-" +
                data.getCarLen() + "-" +
                data.getCarType() + "-" +
                generateSectionsKey(data.getAutoDispatchSectionList());

        if (data.getArriType() == 1) {
            key += "-" + data.getArriAddrName();
        } else {
            key += "-" + data.getArriCityId();
        }

        return key;
    }

    /**
     * 明细去重的依据
     * @param sectionList
     * @return
     */
    private static String generateSectionsKey(List<AutoDispatchVO.AutoDispatchSectionVO> sectionList) {
        // 将AutoDispatchSectionList的内容连接为一个字符串，以确保每个配置中的AutoDispatchSectionList都相同
        StringBuilder keyBuilder = new StringBuilder();
        for (AutoDispatchVO.AutoDispatchSectionVO section : sectionList) {
            keyBuilder.append(section.getStartSection())
                    .append(section.getEndSection())
                    .append(section.getStartOperator())
                    .append(section.getEndOperator())
                    .append(section.getPrice())
                    .append(section.getIsFixedPrice());
        }
        return keyBuilder.toString();
    }


    /**
     * 合并数据  将到货区id 和 name合并
     * *
     * @param existing
     * @param replacement
     * @return
     */
    private static AutoDispatchVO.AutoDispatchConfigVO mergeData( AutoDispatchVO.AutoDispatchConfigVO existing
            , AutoDispatchVO.AutoDispatchConfigVO replacement) {
        if (existing.getArriType() == 0) {
            // 在这里合并需要合并的字段，例如arriAreaId
            String mergedArriAreaIds = existing.getArriAreaId() + "," + replacement.getArriAreaId();
            String mergedArriAreaNames = existing.getArriAreaName() + "," + replacement.getArriAreaName();
            existing.setArriAreaId(mergedArriAreaIds);
            existing.setArriAreaName(mergedArriAreaNames);
        } else if (existing.getArriType() == 1) {
            // arriType为1时不进行合并，只去重，选择保留现有的
            existing = existing;
        }

        return existing;
    }

}
