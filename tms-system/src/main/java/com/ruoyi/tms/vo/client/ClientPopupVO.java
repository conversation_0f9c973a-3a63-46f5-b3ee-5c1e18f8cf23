package com.ruoyi.tms.vo.client;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/10/18 13:56
 */
@Data
@ApiModel("客户信息")
public class ClientPopupVO {
	/**
	 * 客户ID
	 */
	@ApiModelProperty("客户ID")
	private String customerId = "";
	/**
	 * 删除标志
	 */
	@ApiModelProperty(hidden = true)
	private Integer delFlag;
	/**
	 * 删除时间
	 */
	@ApiModelProperty(hidden = true)
	private Date delDate;
	/**
	 * 客户编码
	 */
	@ApiModelProperty("客户编码")
	private String custCode = "";
	/**
	 * 客户名称
	 */
	@ApiModelProperty("客户名称")
	private String custName = "";
	/**
	 * 客户简称
	 */
	@ApiModelProperty(hidden = true)
	private String custAbbr;
	/**
	 * 客户类型 字典表
	 */
	@ApiModelProperty(hidden = true)
	private Integer custType;
	/**
	 * 是否认证 （0:未认证  1: 已认证）
	 */
	@ApiModelProperty(hidden = true)
	private String ifAuthentication;
	/**
	 * 运营部
	 */
	@ApiModelProperty(hidden = true)
	private String salesDept;
	/**
	 * 结算组
	 */
	@ApiModelProperty("结算组")
	private String balaDept = "";
	/**
	 * 驻场组
	 */
	@ApiModelProperty(hidden = true)
	private String stationDept;
	/**
	 * 运营公司
	 */
	@ApiModelProperty("结算公司id")
	private String balaCorp = "";

	/** 结算公司 */
	private String operateCorp;

	/**
	 * 公司
	 */
	@ApiModelProperty(hidden = true)
	private String corpId;
	/**
	 * 业务员
	 */
	@ApiModelProperty("业务员")
	private String psndoc = "";
	/**
	 * 业务员联系方式
	 */
	@ApiModelProperty(hidden = true)
	private String psncontact;
	/**
	 * 客户地址（省）ID
	 */
	@ApiModelProperty(hidden = true)
	private String provinceId;
	/**
	 * 客户地址（市）ID
	 */
	@ApiModelProperty(hidden = true)
	private String cityId;
	/**
	 * 客户地址（区）ID
	 */
	@ApiModelProperty(hidden = true)
	private String areaId;
	/**
	 * 客户地址（详细地址）
	 */
	@ApiModelProperty(hidden = true)
	private String address;
	/**
	 * 联系人
	 */
	@ApiModelProperty(hidden = true)
	private String contact;
	/**
	 * 联系人职位
	 */
	@ApiModelProperty(hidden = true)
	private String contactPost;
	/**
	 * 联系人电话
	 */
	@ApiModelProperty(hidden = true)
	private String phone;
	/**
	 * 联系人手机
	 */
	@ApiModelProperty(hidden = true)
	private String mobile;
	/**
	 * 联系人邮箱
	 */
	@ApiModelProperty(hidden = true)
	private String email;
	/**
	 * 联系人传真
	 */
	@ApiModelProperty(hidden = true)
	private String fax;
	/**
	 * 联系人邮政编码
	 */
	@ApiModelProperty(hidden = true)
	private String zipcode;
	/**
	 * 开票公司
	 */
	@ApiModelProperty("开票公司")
	private String billingCorp = "";
	/**
	 * 开票日期
	 */
	@ApiModelProperty(hidden = true)
	private Date billingDate;
	/**
	 * 纳税识别号
	 */
	@ApiModelProperty(hidden = true)
	private String taxIdentify;
	/**
	 * 结算方式 数据字典表
	 */
	@ApiModelProperty(hidden = true)
	private String balaType;
	/**
	 * 开票类型 数据字典表
	 */
	@ApiModelProperty(hidden = true)
	private String billingType;
	/**
	 * 发票抬头
	 */
	@ApiModelProperty(hidden = true)
	private String billingPayable;
	/**
	 * 开户银行
	 */
	@ApiModelProperty(hidden = true)
	private String bank;
	/**
	 * 折扣率
	 */
	@ApiModelProperty(hidden = true)
	private Integer discountRate;
	/**
	 * 账期
	 */
	@ApiModelProperty(hidden = true)
	private Integer accountPeriod;
	/**
	 * 账期提前量
	 */
	@ApiModelProperty(hidden = true)
	private Integer accPeriodAhead;
	/**
	 * 开票提前量
	 */
	@ApiModelProperty(hidden = true)
	private Integer billingAhead;
	/**
	 * 信用额度
	 */
	@ApiModelProperty(hidden = true)
	private Integer creditAmount;
	/**
	 * 账号名称
	 */
	@ApiModelProperty(hidden = true)
	private String accountName;
	/**
	 * 开户账号
	 */
	@ApiModelProperty(hidden = true)
	private String bankAccount;
	/**
	 * 注册地址
	 */
	@ApiModelProperty(hidden = true)
	private String registerAddr;
	/**
	 * 计费规则
	 */
	@ApiModelProperty(hidden = true)
	private Integer billingRule;
	/**
	 * 法人代表
	 */
	@ApiModelProperty(hidden = true)
	private String legalRepresent;
	/**
	 * 注册资金
	 */
	@ApiModelProperty(hidden = true)
	private Integer registerCapital;
	/**
	 * 收件人
	 */
	@ApiModelProperty(hidden = true)
	private String addressee;
	/**
	 * 收件人联系方式
	 */
	@ApiModelProperty(hidden = true)
	private String addresseeContact;
	/**
	 * 收件人联系地址
	 */
	@ApiModelProperty(hidden = true)
	private String addresseeAddr;
	/**
	 * 网址
	 */
	@ApiModelProperty(hidden = true)
	private String website;
	/**
	 * 是否失效  Y：失效 N：有效
	 */
	@ApiModelProperty(hidden = true)
	private String lockedFlag;
	/**
	 * 备注
	 */
	@ApiModelProperty(hidden = true)
	private String memo;
	/**
	 * 客户活跃程度
	 */
	@ApiModelProperty(hidden = true)
	private Integer custActivity;
	/**
	 * 创建人
	 */
	@ApiModelProperty(hidden = true)
	private String regUserId;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(hidden = true)
	private Date regDate;
	/**
	 * 修改人
	 */
	@ApiModelProperty(hidden = true)
	private String corUserId;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(hidden = true)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date corDate;
	/**
	 * 创建画面ID
	 */
	@ApiModelProperty(hidden = true)
	private String regScrId;
	/**
	 * 修改画面ID
	 */
	@ApiModelProperty(hidden = true)
	private String corScrId;
	/**
	 * 营业执照号码
	 */
	@ApiModelProperty(hidden = true)
	private String businesslicense;
	/**
	 * 法人身份证
	 */
	@ApiModelProperty(hidden = true)
	private String legalCard;
	/**
	 * 用户性质
	 */
	@ApiModelProperty(hidden = true)
	private String customerType;
	/**
	 * 账期
	 */
	@ApiModelProperty(hidden = true)
	private String paymentDays;
	/**
	 * 开户银行ID
	 */
	@ApiModelProperty(hidden = true)
	private String bankId;
	/**
	 * 业务员名称
	 */
	@ApiModelProperty(hidden = true)
	private String psndocName;
	/**
	 * 驻场组名称
	 */
	@ApiModelProperty(hidden = true)
	private String stationDeptName;
	/**
	 * 结算组名称
	 */
	@ApiModelProperty(hidden = true)
	private String balaDeptName;
	/**
	 * 运营部名称
	 */
	@ApiModelProperty("运营部")
	private String salesDeptName = "";
	/**
	 * 省名称
	 */
	@ApiModelProperty(hidden = true)
	private String provinceName;
	/**
	 * 市名称
	 */
	@ApiModelProperty(hidden = true)
	private String cityName;
	/**
	 * 区名称
	 */
	@ApiModelProperty(hidden = true)
	private String areaName;

	/**
	 * APP提货联系人
	 */
	@ApiModelProperty("app联系人")
	private String appDeliContact = "";
	/**
	 * APP联系人手机
	 */
	@ApiModelProperty("APP联系人手机")
	private String appDeliMobile = "";
	/**
	 * 审核状态
	 */
	@ApiModelProperty(hidden = true)
	private Integer checkStatus;
	/**
	 * 审核人
	 */
	@ApiModelProperty(hidden = true)
	private String checkMan;
	/**
	 * 审核时间
	 */
	@ApiModelProperty(hidden = true)
	private Date checkDate;

	/**
	 * 是否设置指导价
	 */
	@ApiModelProperty(hidden = true)
	private Integer crtGuidePrice;
	/**
	 * 客户级别
	 */
	@ApiModelProperty(hidden = true)
	private Integer customerLevel;
	/**
	 * 税率
	 */
	@ApiModelProperty(hidden = true)
	private BigDecimal tariff;

	/**
	 * 是否需要回单
	 */
	private Integer isNeedReceipt;
	/**
	 * 是否需要跟踪
	 */
	private Integer isNeedTrace;
	/**开票期*/
	private Integer invoiceDays;
	/**收款期*/
	private Integer collectionDays;
	/**是否锁定第三方费用：0否，1是*/
	private Integer isLockOtherFee;
	/** 调整金额需要审核比例*/
	private BigDecimal adjustment;

	/** 是否默认*/
	@ApiModelProperty("是否默认  1是 0否")
	private String isDefault;

	/**特殊日期*/
	private Integer specialDate;

	/**
	 * 企业性质
	 */
	private Integer enterpriseNature;
	/**
	 * 客户来源
	 */
	private Integer customerSource;
	/**
	 * 是否启用
	 */
	private Short isEnabled;
	/**
	 * 装卸费单价
	 */
	private BigDecimal handlingCharges;
	/**
	 * 计费方式
	 */
	private String handlingChargesType;

	/**
	 * 是否启用合同价计费
	 */
	private Integer enableContractPrice;

	/**
	 * 合同到期日
	 */
	private String invalidDate;
	/**
	 * 平台费率
	 */
	private BigDecimal platRate;
	/**
	 * 平台税点
	 */
	private BigDecimal platTax;

	/**
	 * 运营部名称
	 */
	private String salesName;
	/** 合同价精确到市或区  0区 1市 2详细地址*/
	private Integer accurateRegion;
	/** 合同价精确到到货详细地址 0否  1是*/
	private Integer accurateArriDetail;

	/** 合同价精确到货品 0否  1是*/
	private Integer accurateGoods;

	/** 是否开启价格浮动  0否  1是*/
	private Integer contractPriceFloat;
	/** 合同价浮动价开始日期*/
	private Date floatDateStart;
	/** 合同价浮动价结束日期*/
	private Date floatDateEnd;

	private String salesId;

	private Integer customerExpCount;

	private Integer isExistContract;
	/**
	 * 指导价比率
	 */
	private BigDecimal referenceRate;

	/**
	 * 是否议价 0否1是
	 */
	private Integer ifBargain;

	/**
	 * 是否特殊指导价 0否1是
	 */
	private Integer isSpecialReferencePrice;
	/**
	 * 合同价审核  0未审核  1已审核
	 */
	private Integer contractPriceReview;
	/**
	 * 审核人
	 */
	private String contractPriceReviewUserId;
	/**
	 * 审核人
	 */
	private String contractPriceReviewUserName;
	/**
	 * 审核时间
	 */
	private Date contractPriceReviewDate;

	/**
	 * 是否自动调度  0否 1是
	 */
	private Integer isAutoDispatch;
	/** 自动调度配置类型 0线路+应收  1背靠背  2区间*/
	private Integer autoDispatchType;

	/**
	 * 自动调度  中转站地址id
	 */
	private String autoSubsectionAddrId;

	/**
	 * 是否特殊流转 0否1是  “是”流转赵羽桐审核用费用
	 */
	private Integer isSpecialTransfer;

	/** 自动调度时 是否可以录入送货费 0不可以 1可以*/
	private Integer autoDispatchDeliFee;
	/** 自动调度时 是否可以录入提货费 0不可以 1可以*/
	private Integer autoDispatchPickUpFee;

	/** 发货单运营组*/
	private String invoiceSalesDeptName;

	/**
	 * 发货单下单确认的时候 是否检查地址经纬度  0否 1是
	 */
	private Integer isCheckLonlat;

	/**
	 * 下单时合同价类型 0仅合同价  1可溢价
	 */
	private Integer contractPriceType;

	/**
	 * 注意事项附件id
	 */
	private String noticeFileId;
	private String noticeFileUrl;
	private String noticeFileName;

	/**
	 * 引荐人
	 */
	private String referrer;

	/** 运营部*/
	private String opsDeptId;
	private String opsDeptName;

	/** 管理部*/
	private String mgmtDeptId;
	private String mgmtDeptName;

	private Date disabledTime;
	/**
	 * 客户类别 0业务客户  1车队客户  2园区客户
	 */
	private Integer sourceType;

	/**
	 * 标的金额
	 */
	private BigDecimal subjectAmount;

	private String groupName;
	/**
	 * 允许无合同下单 0否  1是
	 */
	private Integer contractNeedType;

	/**
	 * 财务结算审核  0无需审核  1需要审核
	 */
	private Integer settlementCheck;
	/**
	 * 发货单确认时是否校验下单联系人及电话  0不  1需要
	 */
	private Integer invContactCheck;

}
