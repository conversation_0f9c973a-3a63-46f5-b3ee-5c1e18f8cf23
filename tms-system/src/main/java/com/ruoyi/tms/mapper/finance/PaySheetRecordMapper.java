package com.ruoyi.tms.mapper.finance;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.tms.domain.finance.OfflinePay;
import com.ruoyi.tms.domain.finance.PaySheetRecord;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 应付对账的付款申请 数据层
 * 
 * <AUTHOR>
 * @date 2019-09-26
 */
public interface PaySheetRecordMapper {
	/**
     * 查询应付对账的付款申请信息
     * 
     * @param paySheetRecordId 应付对账的付款申请ID
     * @return 应付对账的付款申请信息
     */
	PaySheetRecord selectPaySheetRecordById(String paySheetRecordId);
	
	/**
     * 查询应付对账的付款申请列表
     * 
     * @param paySheetRecord 应付对账的付款申请信息
     * @return 应付对账的付款申请集合
     */
	List<PaySheetRecord> selectPaySheetRecordList(PaySheetRecord paySheetRecord);

	/**
     * 新增应付对账的付款申请
     * 
     * @param paySheetRecord 应付对账的付款申请信息
     * @return 结果
     */
	int insertPaySheetRecord(PaySheetRecord paySheetRecord);
	
	/**
     * 修改应付对账的付款申请
     * 
     * @param paySheetRecord 应付对账的付款申请信息
     * @return 结果
     */
	int updatePaySheetRecord(PaySheetRecord paySheetRecord);

	/**
	 * 修改应付对账状态
	 *
	 * @param paySheetRecord 应付对账的付款申请信息
	 * @return 结果
	 */
	int updatePaySheetRecordStatus(PaySheetRecord paySheetRecord);

	/**
     * 删除应付对账的付款申请
     * 
     * @param paySheetRecordId 应付对账的付款申请ID
     * @return 结果
     */
	int deletePaySheetRecordById(String paySheetRecordId);
	
	/**
     * 批量删除应付对账的付款申请
     * 
     * @param paySheetRecordIds 需要删除的数据ID
     * @return 结果
     */
	int deletePaySheetRecordByIds(String[] paySheetRecordIds);

	/**
	 * 审核
	 * @param paySheetRecord
	 * @return
	 */
	int checkPaySheetRecord(PaySheetRecord paySheetRecord);

	/**
	 * 根据应付对账申请ID，查询应付明细ID
	 *
	 * @param strings
	 * @return
	 */
	List<String> selectPayDetailIdsByPaySheetRecordIds(List<String> strings);

	/**
	 *  根据申请id 获取应付id
	 * @param vbillno
	 * @return
	 */
	List<String> selectPayDetailIdsByVbillno(String vbillno);

	/**
	 * 根据应付对账申请IDS，查询应付对账申请状态
	 *
	 * @param strings
	 * @return
	 */
	List<PaySheetRecord> selectStatusByPaySheetRecordIds(List<String> strings);

	/**
	 * 修改付款申请 判断状态是否一致
	 *
	 * @param paySheetRecord 应付对账的付款申请信息
	 * @return 结果
	 */
	int updatePaySheetRecordJudgmentStatus(PaySheetRecord paySheetRecord);

	/**
	 *
	 * @param paySheetRecord
	 * @return
	 */
    List<PaySheetRecord> getPaySheetRecordList(PaySheetRecord paySheetRecord);

	/**
	 * 获取所有数据的金额合计
	 * @param paySheetRecord
	 * @return
	 */
	Map<String, Object> selectPaySheetRecordAmountCount(PaySheetRecord paySheetRecord);

	/**
	 * 按对账单号对G7待支付进行列表操作
	 *
	 * @param corp
	 * @return
	 */
    List<Map<String, Object>> listG7WaitingPay(@Param("corp") String corp, @Param("vbillno") String vbillno);

	List<String> listWaitingPayLotId(@Param("corp") String corp, @Param("paySheetRecordIds") Collection<String> paySheetRecordIds);

	void setPaySheetRecordStatusByLotIds(@Param("lotIds") List<String> lotIds);

    List<OfflinePay> offlineList(PaySheetRecord paySheetRecord);

    int selectPaySheetRecordCountPermission(PaySheetRecord paySheetRecord);

    List<Map<String, Object>> listCarrBankAndG7PayeeSyn(@Param("carrBankIdList") List<String> g7CarrBankIdList);
}