package com.ruoyi.tms.mapper.client;

import com.ruoyi.tms.domain.client.AutoDispatchConfig;
import com.ruoyi.tms.vo.client.AutoDispatchAnalyzeVO;
import com.ruoyi.tms.vo.client.AutoDispatchMatchVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 自动调度配置表
 *
 * <AUTHOR> zjx
 * @version : v1.0.0
 * @date : 2023-07-24 10:25
 */
public interface AutoDispatchConfigMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(String id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(AutoDispatchConfig record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(AutoDispatchConfig record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    AutoDispatchConfig selectByPrimaryKey(String id);

    List<AutoDispatchConfig> selectByAll(AutoDispatchConfig autoDispatchConfig);

    List<AutoDispatchConfig> selectAllByCarrierId(@Param("carrierId") String carrierId);

    /**
     * 根据客户id取配置
     * @param customerId    客户id
     * @param isLatest      是否取最新版本  null为取所有  不为null则取最新
     * @return
     */
    List<AutoDispatchConfig> selectAllByCustomerId(@Param("customerId") String customerId
            , @Param("isLatest") Integer isLatest);

    List<AutoDispatchConfig> selectAllBySameId(@Param("sameId") String sameId);
    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(AutoDispatchConfig record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(AutoDispatchConfig record);

    int updateBatch(List<AutoDispatchConfig> list);

    int updateByCustomerId(@Param("updated") AutoDispatchConfig updated, @Param("customerId") String customerId);

    int updateBySameId(@Param("updated") AutoDispatchConfig updated, @Param("sameId") String sameId);

    /**
     * 根据到货地址  批量插入
     * @param configInsert
     * @return
     */
    int addBatchByArriAddr(AutoDispatchConfig configInsert);

    /**
     * 查询配置信息，将相同sameid的数据合并
     * @param configSele
     * @return
     */
    List<AutoDispatchConfig> selectBySame(AutoDispatchConfig configSele);
    List<AutoDispatchConfig> selectBySame1(AutoDispatchConfig configSele);

    List<AutoDispatchConfig> getPriceList(AutoDispatchMatchVO autoDispatchConfig);

    /**
     * 查询自动调度分析列表
     * @param autoDispatchAnalyzeVO
     * @return
     */
    List<AutoDispatchAnalyzeVO> selectAnalyzeList(AutoDispatchAnalyzeVO autoDispatchAnalyzeVO);

}