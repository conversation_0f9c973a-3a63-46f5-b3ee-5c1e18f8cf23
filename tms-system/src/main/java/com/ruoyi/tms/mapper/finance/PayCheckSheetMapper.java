package com.ruoyi.tms.mapper.finance;

import com.ruoyi.system.domain.SysUploadFile;
import com.ruoyi.tms.domain.carrier.EntrustLot;
import com.ruoyi.tms.domain.finance.PayCheckSheet;
import com.ruoyi.tms.domain.finance.PayDetail;
import com.ruoyi.tms.vo.carrier.EntrustLotExportVO;
import com.ruoyi.tms.vo.finance.LotPayDetailVO;
import com.ruoyi.tms.vo.finance.PayDetailExportVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 应付对账 数据层
 *
 * <AUTHOR>
 * @date 2019-09-26
 */
public interface PayCheckSheetMapper {

	/**
	 * 删除明细和对账单关联
	 *
	 * @param payDetailId
	 * @return
	 */
	Integer deleteRelation(String payDetailId);

	/**
	 * 根据应付明细ID获取对应的对账单ID
	 *
	 * @param payDetailId
	 * @return
	 */
	String selectPayCheckSheetId(String payDetailId);

	/**
	 * 查询应付对账信息
	 *
	 * @param payCheckSheetId 应付对账ID
	 * @return 应付对账信息
	 */
	PayCheckSheet selectPayCheckSheetById(String payCheckSheetId);

	/**
	 * 查询应付对账列表
	 *
	 * @param payCheckSheet 应付对账信息
	 * @return 应付对账集合
	 */
	List<PayCheckSheet> selectPayCheckSheetList(PayCheckSheet payCheckSheet);

	/**
	 * 查询应付对账列表 根据承运商求和
	 *
	 * @param payCheckSheet 应付对账信息
	 * @return 应付对账集合
	 */
	List<PayCheckSheet> selectPayCheckSheetListGroupByCarrier(PayCheckSheet payCheckSheet);

	/**
	 * 修改应付对账单
	 *
	 * @param payCheckSheet 应付对账实体类
	 * @return
	 */
    int updatePayCheck(PayCheckSheet payCheckSheet);

	/**
	 * 修改应付对账 - 减金额
	 *
	 * @param payCheckSheet
	 * @return
	 */
	int updatePayCheckSubtractAmount(PayCheckSheet payCheckSheet);

	/**
	 * 核销应付对账
	 *
	 * @param status           状态
	 * @param payCheckSheetIds 应付对账Ids
	 * @return
	 */
	int verification(@Param("status") int status, @Param("payCheckSheetIds") String[] payCheckSheetIds);


	/**
	 * 生成序列
	 *
	 * @return String
	 */
	String getSeq();

	/**
	 * 通过应付对账单展示所有应付明细列表
	 *
	 * @param map 参数
	 * @return 结果
	 */
    List<PayDetail> selectPayDetailBySheetId(Map<String, String> map);

    List<Map<String, Object>> paySumByBalaCorp(@Param("payCheckSheetId") String payCheckSheetId);

    List<Map<String, Object>> paySumByBalaCorpPlus(@Param("payCheckSheetId") String payCheckSheetId);

	/**
	 * 查询成本分摊合计金额
	 *
	 * @param map
	 * @return
	 */
	BigDecimal selectPaySumDetailBySheetId(Map<String, String> map);

	/**
	 * 通过应付对账单展示所有应付明细列表（导出）
	 *
	 * @param map
	 * @return
	 */
	List<PayDetailExportVO> exportPayDetailBySheetId(Map<String, String> map);

	/**
	 * 在途跟踪 承运商对账 应付明细
	 *
	 * @param map
	 */
	Map<String, Object> selectPayDetailBySheetIdAmountCount(Map<String, String> map);


	/**
	 * 根据承运商Id查询应付对账单
	 *
	 * @param carrierId 承运商ID
	 * @return 结果
	 */
	List<PayCheckSheet> selectPayCheckSheetByCarrierId(String carrierId);

	/**
	 * 确认对账
	 *
	 * @param payCheckSheet
	 */
    void affirm(PayCheckSheet payCheckSheet);

	/**
	 * 反确认对账
	 *
	 * @param payCheckSheet
	 */
	void reverse(PayCheckSheet payCheckSheet);

	/**
	 * 查询承运商对账信息
	 *
	 * @param payCheckSheet
	 * @return
	 */
	List<PayCheckSheet> selectPayCheckSheetListByCarrierId(PayCheckSheet payCheckSheet);

	/**
	 * 根据应付明细ID查询所在对账单
	 *
	 * @param payDetailId
	 * @return
	 */
	PayCheckSheet selectPayCheckSheetByPayDetailId(@Param("payDetailId") String payDetailId);

	/**
	 * 查询对账单明细
	 *
	 * @param payCheckSheetId 对账单ID
	 * @return
	 */
	List<PayDetail> selectStatementDetailById(String payCheckSheetId);

	/**
	 * 根据对账单id查询所属的所有应收明细
	 *
	 * @param payCheckSheetId
	 * @return
	 */
    List<PayDetail> selectPayDetailBypayCheckSheetId(String payCheckSheetId);


	/**
	 * 查询对账单下应付运单信息
	 * @param payCheckSheetId
	 * @return
	 */
	List<EntrustLot> selectLotBypayCheckSheetId(String payCheckSheetId);

	/**
	 * 统计承运商对账 查询列表金额
	 *
	 * @param payCheckSheet
	 * @return
	 */
	Map<String, Object> selectPayCheckSheetAmountCount(PayCheckSheet payCheckSheet);
	Map<String, Object> selectPayCheckSheetAmountSingleCount(PayCheckSheet payCheckSheet);

	/**
	 * 更新对账单现金金额
	 *
	 * @param payCheckSheet
	 * @return
	 */
    int updatePayCheckSheetAmount(PayCheckSheet payCheckSheet);

	/**
	 * 更新油卡申请金额
	 *
	 * @param payCheckSheet
	 * @return
	 */
	int updatePayCheckSheetAmountOil(PayCheckSheet payCheckSheet);

	/**
	 * 插入税额记录
	 *
	 * @param payCheckSheet
	 * @return
	 */
	int insertTaxRecord(PayCheckSheet payCheckSheet);

	/**
	 * 重置税额信息
	 *
	 * @param payCheckSheet
	 * @return
	 */
	int updatePayCheckTaxRevoke(PayCheckSheet payCheckSheet);

	int updatePayCheckTaxRevokeOil(PayCheckSheet payCheckSheet);

	int updatePayCheckTaxRevokeAll(PayCheckSheet payCheckSheet);

	/**
	 * 插入审核记录
	 *
	 * @param payCheckSheet
	 * @return
	 */
	int insertPayCheckSheetCheck(PayCheckSheet payCheckSheet);

	PayCheckSheet selectReceCheckSheetByAdjustRecordId(@Param("adjustRecordId") String adjustRecordId);

	/**
	 * 对账单调整记录
	 * @param checkSheetId
	 * @return
	 */
	List<PayCheckSheet> selectReceCheckSheetCheckByCheckSheetId(@Param("checkSheetId") String checkSheetId);

	/**
	 * 删除调整记录
	 *
	 * @return
	 */
	int deletePayCheckSheetCheck(@Param("payCheckSheetId") String payCheckSheetId, @Param("adjustType") String adjustType);

	/**
	 * 验证删除记录创建日期是否关账
	 *
	 * @param payCheckSheetId
	 * @param adjustType
	 * @return
	 */
	int checkDeletePayCheckSheetCheck(@Param("payCheckSheetId") String payCheckSheetId, @Param("adjustType") String adjustType);

	BigDecimal sumG7PayAbleAmount(String payCheckSheetId);

	/**
	 * @param payCheckSheetId
	 * @return [{PAY_DETAIL_ID, DRIVER_NAME, DRIVER_MOBILE, DRIVER_IDCARD, G7_SYN, G7_PAY, DRIVER_CONTRACT_SIGNED, LOT_ID, VBILLNO, FREE_TYPE, COST_TYPE_FREIGHT, BILLING_CORP, CARR_BANK_ID, LOT}]
	 */
    List<Map<String, Object>> listPayDetailPayInfo(String payCheckSheetId);

	/**
	 * 根据运单id  查询园区应付申请数据
	 *
	 * @param lotId
	 * @return
	 */
    List<Map<String, Object>> selectPayCheckPartDataByLotId(@Param("lotId") String lotId);

    List<Map<String, Object>> listLockedInfo(@Param("payCheckSheetId") String payCheckSheetId);

	int saveLotReceipt(@Param("payCheckSheetId") String payCheckSheetId, @Param("tidReceipt") String tidReceipt,
					   @Param("scrId") String scrId, @Param("userId") String userId, @Param("regDate") Date date, @Param("paySheetRecordId") String paySheetRecordId);

	List<SysUploadFile> listCheckSheetReceipt(@Param("payCheckSheetId") String payCheckSheetId, @Param("paySheetRecordId") String paySheetRecordId);

    List<Map<String, Object>> listDetailTax(@Param("payCheckSheetId") String payCheckSheetId);

	int clearPayDetailTax(@Param("payCheckSheetId") String payCheckSheetId);

	int bindPayCheckSheetAndTax(@Param("payCheckSheetId") String payCheckSheetId);

	int initPayDetailTax(@Param("payCheckSheetId") String payCheckSheetId, @Param("userId") String usrId, @Param("regDate") Date date, @Param("scrId") String srcId);

	//List<Map<String, Object>> findNoneTaxTxtPayDetail(@Param("payCheckSheetId") String payCheckSheetId);

	int addDeductTax(@Param("payDetailId") String payDetailId, @Param("billingType") String billingType,
					 @Param("amount") BigDecimal amount, @Param("effectDate") Date effectDate,
					 @Param("regDate") Date regDate, @Param("userId") String userId, @Param("scrId") String scrId, @Param("flag") Integer flag,
					 @Param("payCheckSheetId") String payCheckSheetId);

	String getCheckSheetSumTaxTxtByLot(String payCheckSheetId);

	String getCheckSheetSumTaxTxt(String payCheckSheetId);

	/**
	 * 委托单
	 * @param entrustLot
	 * @return
	 */
	List<EntrustLot> selectPayCheckSheetSureList(EntrustLot entrustLot);
	List<EntrustLotExportVO> exportPayCheckSheetSureList(EntrustLot entrustLot);

    void deleteTidReceipt(@Param("payCheckSheetId") String payCheckSheetId, @Param("fileId") String fileId, @Param("tid") String tid);

    List<String> listDriverIdcards(@Param("payCheckSheetId") String payCheckSheetId);

	String getCarrBankIdcard(@Param("carrBankId") String carrBankId);

	/**
	 * 查询对账包中的应付金额  按运单分组
	 * @param lotPayDetailVO
	 * @return
	 */
	List<LotPayDetailVO> selectPayDetailBySheetIdGroupByLot(LotPayDetailVO lotPayDetailVO);

	List<PayDetail> getAbnormalDeductionBySheetId(String payCheckSheetId);

    List<Map<String, Object>> listG7Ext(String payCheckSheetId);

    Integer countPayCheckSheet(@Param("carrierId") String carrierId, @Param("year") Integer year, @Param("month") Integer month);

	List<Map<String, Object>> listLotAndCarrBank(Map<String, String> param);

	Map<String, Object> sumTotalAmountByCarrier(@Param("carrierId") String carrierId);

	List<Map<String, Object>> checkPaySheetRecordFuelcard(@Param("carrierId") String carrierId);
}