<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tms.mapper.client.AutoDispatchConfigMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.tms.domain.client.AutoDispatchConfig">
    <!--@mbg.generated-->
    <!--@Table M_AUTO_DISPATCH_CONFIG-->
    <id column="ID" property="id" />
    <result column="CUSTOMER_ID" property="customerId" />
    <result column="CUST_NAME" property="custName" />
    <result column="CUST_ABBR" property="custAbbr" />
    <result column="CARRIER_ID" property="carrierId" />
    <result column="CARR_NAME" property="carrName" />
    <result column="DELI_PROVINCE_ID" property="deliProvinceId" />
    <result column="DELI_CITY_ID" property="deliCityId" />
    <result column="DELI_AREA_ID" property="deliAreaId" />
    <result column="DELI_PRO_NAME" property="deliProName" />
    <result column="DELI_CITY_NAME" property="deliCityName" />
    <result column="DELI_AREA_NAME" property="deliAreaName" />
    <result column="ARRI_PROVINCE_ID" property="arriProvinceId" />
    <result column="ARRI_CITY_ID" property="arriCityId" />
    <result column="ARRI_AREA_ID" property="arriAreaId" />
    <result column="ARRI_PRO_NAME" property="arriProName" />
    <result column="ARRI_CITY_NAME" property="arriCityName" />
    <result column="ARRI_AREA_NAME" property="arriAreaName" />
    <result column="DEDUCTION_AMOUNT" property="deductionAmount" />
    <result column="DEDUCTION_TYPE" property="deductionType" />
    <result column="REG_USER_ID" property="regUserId" />
    <result column="REG_USER_NAME" property="regUserName" />
    <result column="REG_SCR_ID" property="regScrId" />
    <result column="REG_DATE" property="regDate" />
    <result column="COR_USER_ID" property="corUserId" />
    <result column="COR_USER_NAME" property="corUserName" />
    <result column="COR_DATE" property="corDate" />
    <result column="COR_DATE_STR" property="corDateStr" />
    <result column="COR_SCR_ID" property="corScrId" />
    <result column="DEL_FLAG" property="delFlag" />
    <result column="DEL_USERID" property="delUserid" />
    <result column="DEL_DATE" property="delDate" />
    <result column="AUTO_DISPATCH_TYPE" property="autoDispatchType" />
    <result column="BILLING_METHOD" property="billingMethod" />
    <result column="CAR_LEN" property="carLen" />
    <result column="CAR_LEN_NAME" property="carLenName" />
    <result column="CAR_TYPE" property="carType" />
    <result column="CAR_TYPE_NAME" property="carTypeName" />
    <result column="same_id" property="sameId" />
    <result column="billing_type" property="billingType" />
    <result column="oil_ratio" property="oilRatio" />
    <result column="arri_addr_name" property="arriAddrName" />
    <result column="arri_type" property="arriType" />
    <result column="oil_type" property="oilType" />
    <result column="OIL_COST_TYPE" property="oilCostType" />
    <result column="goods_id" property="goodsId" />
    <result column="goods_name" property="goodsName" />
    <result column="VERSION_ID" property="versionId" />
    <result column="deduction_fee_type" property="deductionFeeType" />
    <result column="goods_character" property="goodsCharacter" />
    <result column="is_round_trip" property="isRoundTrip" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CUSTOMER_ID, CUST_NAME, CUST_ABBR, CARRIER_ID, CARR_NAME, DELI_PROVINCE_ID, DELI_CITY_ID, 
    DELI_AREA_ID, DELI_PRO_NAME, DELI_CITY_NAME, DELI_AREA_NAME, ARRI_PROVINCE_ID, ARRI_CITY_ID, 
    ARRI_AREA_ID, ARRI_PRO_NAME, ARRI_CITY_NAME, ARRI_AREA_NAME, DEDUCTION_AMOUNT, DEDUCTION_TYPE, 
    REG_USER_ID, REG_USER_NAME, REG_SCR_ID, REG_DATE, COR_USER_ID, COR_USER_NAME, COR_DATE, 
    COR_SCR_ID, DEL_FLAG, DEL_USERID, DEL_DATE, AUTO_DISPATCH_TYPE, BILLING_METHOD, CAR_LEN, 
    CAR_LEN_NAME, CAR_TYPE, CAR_TYPE_NAME, same_id, billing_type, oil_ratio, arri_addr_name,
    arri_type, oil_type, OIL_COST_TYPE, goods_id, goods_name, VERSION_ID, deduction_fee_type,
    goods_character, is_round_trip
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from M_AUTO_DISPATCH_CONFIG
    where ID = #{id}
  </select>

  <select id="selectByAll" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from M_AUTO_DISPATCH_CONFIG
    <where>
      <if test="id != null">
        and ID=#{id,jdbcType=VARCHAR}
      </if>
      <if test="customerId != null">
        and CUSTOMER_ID=#{customerId,jdbcType=VARCHAR}
      </if>
      <if test="custName != null">
        and CUST_NAME=#{custName,jdbcType=VARCHAR}
      </if>
      <if test="custAbbr != null">
        and CUST_ABBR=#{custAbbr,jdbcType=VARCHAR}
      </if>
      <if test="carrierId != null">
        and CARRIER_ID=#{carrierId,jdbcType=VARCHAR}
      </if>
      <if test="carrName != null">
        and CARR_NAME=#{carrName,jdbcType=VARCHAR}
      </if>
      <if test="deliProvinceId != null">
        and DELI_PROVINCE_ID=#{deliProvinceId,jdbcType=VARCHAR}
      </if>
      <if test="deliCityId != null">
        and DELI_CITY_ID=#{deliCityId,jdbcType=VARCHAR}
      </if>
      <if test="deliAreaId != null">
        and DELI_AREA_ID=#{deliAreaId,jdbcType=VARCHAR}
      </if>
      <if test="deliProName != null">
        and DELI_PRO_NAME=#{deliProName,jdbcType=VARCHAR}
      </if>
      <if test="deliCityName != null">
        and DELI_CITY_NAME=#{deliCityName,jdbcType=VARCHAR}
      </if>
      <if test="deliAreaName != null">
        and DELI_AREA_NAME=#{deliAreaName,jdbcType=VARCHAR}
      </if>
      <if test="arriProvinceId != null">
        and ARRI_PROVINCE_ID=#{arriProvinceId,jdbcType=VARCHAR}
      </if>
      <if test="arriCityId != null">
        and ARRI_CITY_ID=#{arriCityId,jdbcType=VARCHAR}
      </if>
      <if test="arriAreaId != null">
        and ARRI_AREA_ID=#{arriAreaId,jdbcType=VARCHAR}
      </if>
      <if test="arriProName != null">
        and ARRI_PRO_NAME=#{arriProName,jdbcType=VARCHAR}
      </if>
      <if test="arriCityName != null">
        and ARRI_CITY_NAME=#{arriCityName,jdbcType=VARCHAR}
      </if>
      <if test="arriAreaName != null">
        and ARRI_AREA_NAME=#{arriAreaName,jdbcType=VARCHAR}
      </if>
      <if test="deductionAmount != null">
        and DEDUCTION_AMOUNT=#{deductionAmount,jdbcType=DECIMAL}
      </if>
      <if test="deductionType != null">
        and DEDUCTION_TYPE=#{deductionType,jdbcType=DECIMAL}
      </if>
      <if test="regUserId != null">
        and REG_USER_ID=#{regUserId,jdbcType=VARCHAR}
      </if>
      <if test="regUserName != null">
        and REG_USER_NAME=#{regUserName,jdbcType=VARCHAR}
      </if>
      <if test="regScrId != null">
        and REG_SCR_ID=#{regScrId,jdbcType=VARCHAR}
      </if>
      <if test="regDate != null">
        and REG_DATE=#{regDate,jdbcType=TIMESTAMP}
      </if>
      <if test="corUserId != null">
        and COR_USER_ID=#{corUserId,jdbcType=VARCHAR}
      </if>
      <if test="corUserName != null">
        and COR_USER_NAME=#{corUserName,jdbcType=VARCHAR}
      </if>
      <if test="corDate != null">
        and COR_DATE=#{corDate,jdbcType=TIMESTAMP}
      </if>
      <if test="corScrId != null">
        and COR_SCR_ID=#{corScrId,jdbcType=VARCHAR}
      </if>
      <if test="delFlag != null">
        and DEL_FLAG=#{delFlag,jdbcType=DECIMAL}
      </if>
      <if test="delUserid != null">
        and DEL_USERID=#{delUserid,jdbcType=VARCHAR}
      </if>
      <if test="delDate != null">
        and DEL_DATE=#{delDate,jdbcType=TIMESTAMP}
      </if>
      <if test="autoDispatchType != null">
        and auto_dispatch_type=#{autoDispatchType,jdbcType=DECIMAL}
      </if>
      <if test="billingMethod != null">
        and billing_method=#{billingMethod,jdbcType=VARCHAR}
      </if>
      <if test="carLen != null">
          and CAR_LEN=#{carLen,jdbcType=VARCHAR}
      </if>
      <if test="carLenName != null">
          and CAR_LEN_NAME=#{carLenName,jdbcType=VARCHAR}
      </if>
      <if test="carType != null">
          and CAR_TYPE=#{carType,jdbcType=VARCHAR}
      </if>
      <if test="carTypeName != null">
          and CAR_TYPE_NAME=#{carTypeName,jdbcType=VARCHAR}
      </if>
      <if test="sameId != null">
          and same_id=#{sameId,jdbcType=VARCHAR}
      </if>
      <if test="billingType != null">
          and billing_type=#{billingType,jdbcType=VARCHAR}
      </if>
      <if test="oilRatio != null">
          and oil_ratio=#{oilRatio,jdbcType=DECIMAL}
      </if>
      <if test="arriAddrName != null">
          and arri_addr_name=#{arriAddrName,jdbcType=VARCHAR}
      </if>
      <if test="arriType != null">
          and arri_type=#{arriType,jdbcType=INTEGER}
      </if>
      <if test="oilType != null">
          and oil_type=#{oilType,jdbcType=INTEGER}
      </if>
      <if test="oilCostType != null">
          and OIL_COST_TYPE=#{oilCostType,jdbcType=VARCHAR}
      </if>
      <if test="versionId != null">
          and VERSION_ID=#{versionId,jdbcType=VARCHAR}
      </if>
      <if test="deductionFeeType != null">
          and deduction_fee_type=#{deductionFeeType,jdbcType=DECIMAL}
      </if>
      <if test="goodsId != null">
          and goods_id=#{goodsId,jdbcType=VARCHAR}
      </if>
      <if test="goodsName != null">
          and goods_name=#{goodsName,jdbcType=VARCHAR}
      </if>
      <if test="goodsCharacter != null">
          and goods_character=#{goodsCharacter,jdbcType=INTEGER}
      </if>
      <if test="isRoundTrip != null">
          and is_round_trip=#{isRoundTrip,jdbcType=INTEGER}
      </if>

    </where>
  </select>

  <select id="selectAllByCarrierId" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from M_AUTO_DISPATCH_CONFIG
    where CARRIER_ID=#{carrierId,jdbcType=VARCHAR}
     and DEL_FLAG = 0
  </select>

  <select id="selectAllByCustomerId" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from M_AUTO_DISPATCH_CONFIG
    where CUSTOMER_ID=#{customerId,jdbcType=VARCHAR}
    and DEL_FLAG = 0
    <if test="isLatest != null">
      and version_Id = (
      select id
      from M_AUTO_DISPATCH_VERSION
      where M_AUTO_DISPATCH_VERSION.id = M_AUTO_DISPATCH_CONFIG.version_Id
      and IS_LATEST = 1 and M_AUTO_DISPATCH_VERSION.DEL_FLAG = 0
      fetch first 1 row only
      )
    </if>
  </select>

  <select id="selectAllBySameId" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from M_AUTO_DISPATCH_CONFIG
    where SAME_ID=#{sameId,jdbcType=VARCHAR}
    and DEL_FLAG = 0
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from M_AUTO_DISPATCH_CONFIG
    where ID = #{id}
  </delete>
  <insert id="insert" parameterType="com.ruoyi.tms.domain.client.AutoDispatchConfig">
    <!--@mbg.generated-->
    insert into M_AUTO_DISPATCH_CONFIG (ID, CUSTOMER_ID, CUST_NAME, CUST_ABBR, CARRIER_ID, CARR_NAME, 
      DELI_PROVINCE_ID, DELI_CITY_ID, DELI_AREA_ID, DELI_PRO_NAME, DELI_CITY_NAME, 
      DELI_AREA_NAME, ARRI_PROVINCE_ID, ARRI_CITY_ID, ARRI_AREA_ID, ARRI_PRO_NAME, 
      ARRI_CITY_NAME, ARRI_AREA_NAME, DEDUCTION_AMOUNT, DEDUCTION_TYPE, REG_USER_ID, 
      REG_USER_NAME, REG_SCR_ID, REG_DATE, COR_USER_ID, COR_USER_NAME, COR_DATE, 
      COR_SCR_ID, DEL_FLAG, DEL_USERID, DEL_DATE, AUTO_DISPATCH_TYPE, BILLING_METHOD, 
      CAR_LEN, CAR_LEN_NAME, CAR_TYPE, CAR_TYPE_NAME, same_id,billing_type,oil_ratio,
      arri_addr_name ,arri_type, oil_type, OIL_COST_TYPE, VERSION_ID, deduction_fee_type,
      goods_id,goods_name,goods_character,is_round_trip )
    values (#{id}, #{customerId}, #{custName}, #{custAbbr}, #{carrierId}, #{carrName}, 
      #{deliProvinceId}, #{deliCityId}, #{deliAreaId}, #{deliProName}, #{deliCityName}, 
      #{deliAreaName}, #{arriProvinceId}, #{arriCityId}, #{arriAreaId}, #{arriProName}, 
      #{arriCityName}, #{arriAreaName}, #{deductionAmount}, #{deductionType}, #{regUserId}, 
      #{regUserName}, #{regScrId}, #{regDate}, #{corUserId}, #{corUserName}, #{corDate}, 
      #{corScrId}, #{delFlag}, #{delUserid}, #{delDate}, #{autoDispatchType}, #{billingMethod}, 
      #{carLen}, #{carLenName}, #{carType}, #{carTypeName}, #{sameId}, #{billingType}, #{oilRatio},
      #{arriAddrName}, #{arriType}, #{oilType}, #{oilCostType}, #{versionId}, #{deductionFeeType},
      #{goodsId}, #{goodsName}, #{goodsCharacter}, #{isRoundTrip})
  </insert>
  <insert id="insertSelective" parameterType="com.ruoyi.tms.domain.client.AutoDispatchConfig">
    <!--@mbg.generated-->
    insert into M_AUTO_DISPATCH_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="customerId != null">
        CUSTOMER_ID,
      </if>
      <if test="custName != null">
        CUST_NAME,
      </if>
      <if test="custAbbr != null">
        CUST_ABBR,
      </if>
      <if test="carrierId != null">
        CARRIER_ID,
      </if>
      <if test="carrName != null">
        CARR_NAME,
      </if>
      <if test="deliProvinceId != null">
        DELI_PROVINCE_ID,
      </if>
      <if test="deliCityId != null">
        DELI_CITY_ID,
      </if>
      <if test="deliAreaId != null">
        DELI_AREA_ID,
      </if>
      <if test="deliProName != null">
        DELI_PRO_NAME,
      </if>
      <if test="deliCityName != null">
        DELI_CITY_NAME,
      </if>
      <if test="deliAreaName != null">
        DELI_AREA_NAME,
      </if>
      <if test="arriProvinceId != null">
        ARRI_PROVINCE_ID,
      </if>
      <if test="arriCityId != null">
        ARRI_CITY_ID,
      </if>
      <if test="arriAreaId != null">
        ARRI_AREA_ID,
      </if>
      <if test="arriProName != null">
        ARRI_PRO_NAME,
      </if>
      <if test="arriCityName != null">
        ARRI_CITY_NAME,
      </if>
      <if test="arriAreaName != null">
        ARRI_AREA_NAME,
      </if>
      <if test="deductionAmount != null">
        DEDUCTION_AMOUNT,
      </if>
      <if test="deductionType != null">
        DEDUCTION_TYPE,
      </if>
      <if test="regUserId != null">
        REG_USER_ID,
      </if>
      <if test="regUserName != null">
        REG_USER_NAME,
      </if>
      <if test="regScrId != null">
        REG_SCR_ID,
      </if>
      <if test="regDate != null">
        REG_DATE,
      </if>
      <if test="corUserId != null">
        COR_USER_ID,
      </if>
      <if test="corUserName != null">
        COR_USER_NAME,
      </if>
      <if test="corDate != null">
        COR_DATE,
      </if>
      <if test="corScrId != null">
        COR_SCR_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
      <if test="delUserid != null">
        DEL_USERID,
      </if>
      <if test="delDate != null">
        DEL_DATE,
      </if>
      <if test="autoDispatchType != null">
        AUTO_DISPATCH_TYPE,
      </if>
      <if test="billingMethod != null">
        BILLING_METHOD,
      </if>
      <if test="carLen != null">
        CAR_LEN,
      </if>
      <if test="carLenName != null">
        CAR_LEN_NAME,
      </if>
      <if test="carType != null">
        CAR_TYPE,
      </if>
      <if test="carTypeName != null">
        CAR_TYPE_NAME,
      </if>
      <if test="sameId != null">
        same_id,
      </if>
      <if test="billingType != null">
        billing_type,
      </if>
      <if test="oilRatio != null">
        oil_ratio,
      </if>
      <if test="arriAddrName != null">
        arri_addr_name,
      </if>
      <if test="arriType != null">
        arri_type,
      </if>
      <if test="oilType != null">
        oil_type,
      </if>
      <if test="oilCostType != null">
        OIL_COST_TYPE,
      </if>
      <if test="goodsId != null">
        goods_id,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="versionId != null">
        VERSION_ID,
      </if>
        <if test="deductionFeeType != null">
            deduction_fee_type,
        </if>
        <if test="goodsCharacter != null">
            goods_character,
        </if>
        <if test="isRoundTrip != null">
            is_round_trip,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="customerId != null">
        #{customerId},
      </if>
      <if test="custName != null">
        #{custName},
      </if>
      <if test="custAbbr != null">
        #{custAbbr},
      </if>
      <if test="carrierId != null">
        #{carrierId},
      </if>
      <if test="carrName != null">
        #{carrName},
      </if>
      <if test="deliProvinceId != null">
        #{deliProvinceId},
      </if>
      <if test="deliCityId != null">
        #{deliCityId},
      </if>
      <if test="deliAreaId != null">
        #{deliAreaId},
      </if>
      <if test="deliProName != null">
        #{deliProName},
      </if>
      <if test="deliCityName != null">
        #{deliCityName},
      </if>
      <if test="deliAreaName != null">
        #{deliAreaName},
      </if>
      <if test="arriProvinceId != null">
        #{arriProvinceId},
      </if>
      <if test="arriCityId != null">
        #{arriCityId},
      </if>
      <if test="arriAreaId != null">
        #{arriAreaId},
      </if>
      <if test="arriProName != null">
        #{arriProName},
      </if>
      <if test="arriCityName != null">
        #{arriCityName},
      </if>
      <if test="arriAreaName != null">
        #{arriAreaName},
      </if>
      <if test="deductionAmount != null">
        #{deductionAmount},
      </if>
      <if test="deductionType != null">
        #{deductionType},
      </if>
      <if test="regUserId != null">
        #{regUserId},
      </if>
      <if test="regUserName != null">
        #{regUserName},
      </if>
      <if test="regScrId != null">
        #{regScrId},
      </if>
      <if test="regDate != null">
        #{regDate},
      </if>
      <if test="corUserId != null">
        #{corUserId},
      </if>
      <if test="corUserName != null">
        #{corUserName},
      </if>
      <if test="corDate != null">
        #{corDate},
      </if>
      <if test="corScrId != null">
        #{corScrId},
      </if>
      <if test="delFlag != null">
        #{delFlag},
      </if>
      <if test="delUserid != null">
        #{delUserid},
      </if>
      <if test="delDate != null">
        #{delDate},
      </if>
      <if test="autoDispatchType != null">
        #{autoDispatchType},
      </if>
      <if test="billingMethod != null">
        #{billingMethod},
      </if>
      <if test="carLen != null">
        #{carLen},
      </if>
      <if test="carLenName != null">
        #{carLenName},
      </if>
      <if test="carType != null">
        #{carType},
      </if>
      <if test="carTypeName != null">
        #{carTypeName},
      </if>
      <if test="sameId != null">
        #{sameId},
      </if>
      <if test="billingType != null">
        #{billingType},
      </if>
      <if test="oilRatio != null">
        #{oilRatio},
      </if>
      <if test="arriAddrName != null">
        #{arriAddrName},
      </if>
      <if test="arriType != null">
        #{arriType},
      </if>
      <if test="oilType != null">
        #{oilType},
      </if>
      <if test="oilCostType != null">
        #{oilCostType},
      </if>
      <if test="goodsId != null">
        #{goodsId},
      </if>
      <if test="goodsName != null">
        #{goodsName},
      </if>
      <if test="versionId != null">
        #{versionId},
      </if>
        <if test="deductionFeeType != null">
            #{deductionFeeType},
        </if>
        <if test="goodsCharacter != null">
            #{goodsCharacter},
        </if>
        <if test="isRoundTrip != null">
            #{isRoundTrip},
        </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.tms.domain.client.AutoDispatchConfig">
    <!--@mbg.generated-->
    update M_AUTO_DISPATCH_CONFIG
    <set>
      <if test="customerId != null">
        CUSTOMER_ID = #{customerId},
      </if>
      <if test="custName != null">
        CUST_NAME = #{custName},
      </if>
      <if test="custAbbr != null">
        CUST_ABBR = #{custAbbr},
      </if>
      <if test="carrierId != null">
        CARRIER_ID = #{carrierId},
      </if>
      <if test="carrName != null">
        CARR_NAME = #{carrName},
      </if>
      <if test="deliProvinceId != null">
        DELI_PROVINCE_ID = #{deliProvinceId},
      </if>
      <if test="deliCityId != null">
        DELI_CITY_ID = #{deliCityId},
      </if>
      <if test="deliAreaId != null">
        DELI_AREA_ID = #{deliAreaId},
      </if>
      <if test="deliProName != null">
        DELI_PRO_NAME = #{deliProName},
      </if>
      <if test="deliCityName != null">
        DELI_CITY_NAME = #{deliCityName},
      </if>
      <if test="deliAreaName != null">
        DELI_AREA_NAME = #{deliAreaName},
      </if>
      <if test="arriProvinceId != null">
        ARRI_PROVINCE_ID = #{arriProvinceId},
      </if>
      <if test="arriCityId != null">
        ARRI_CITY_ID = #{arriCityId},
      </if>
      <if test="arriAreaId != null">
        ARRI_AREA_ID = #{arriAreaId},
      </if>
      <if test="arriProName != null">
        ARRI_PRO_NAME = #{arriProName},
      </if>
      <if test="arriCityName != null">
        ARRI_CITY_NAME = #{arriCityName},
      </if>
      <if test="arriAreaName != null">
        ARRI_AREA_NAME = #{arriAreaName},
      </if>
      <if test="deductionAmount != null">
        DEDUCTION_AMOUNT = #{deductionAmount},
      </if>
      <if test="deductionType != null">
        DEDUCTION_TYPE = #{deductionType},
      </if>
      <if test="regUserId != null">
        REG_USER_ID = #{regUserId},
      </if>
      <if test="regUserName != null">
        REG_USER_NAME = #{regUserName},
      </if>
      <if test="regScrId != null">
        REG_SCR_ID = #{regScrId},
      </if>
      <if test="regDate != null">
        REG_DATE = #{regDate},
      </if>
      <if test="corUserId != null">
        COR_USER_ID = #{corUserId},
      </if>
      <if test="corUserName != null">
        COR_USER_NAME = #{corUserName},
      </if>
      <if test="corDate != null">
        COR_DATE = #{corDate},
      </if>
      <if test="corScrId != null">
        COR_SCR_ID = #{corScrId},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag},
      </if>
      <if test="delUserid != null">
        DEL_USERID = #{delUserid},
      </if>
      <if test="delDate != null">
        DEL_DATE = #{delDate},
      </if>
      <if test="autoDispatchType != null">
        AUTO_DISPATCH_TYPE = #{autoDispatchType},
      </if>
      <if test="billingMethod != null">
        BILLING_METHOD = #{billingMethod},
      </if>
      <if test="carLen != null">
        CAR_LEN = #{carLen},
      </if>
      <if test="carLenName != null">
        CAR_LEN_NAME = #{carLenName},
      </if>
      <if test="carType != null">
        CAR_TYPE = #{carType},
      </if>
      <if test="carTypeName != null">
        CAR_TYPE_NAME = #{carTypeName},
      </if>
      <if test="sameId != null">
        same_id = #{sameId},
      </if>
      <if test="billingType != null">
        billing_type = #{billingType},
      </if>
      <if test="oilRatio != null">
        oil_ratio = #{oilRatio},
      </if>
      <if test="arriAddrName != null">
        arri_addr_name = #{arriAddrName},
      </if>
      <if test="arriType != null">
        arri_type = #{arriType},
      </if>
      <if test="oilType != null">
        oil_type = #{oilType},
      </if>
      <if test="oilCostType != null">
        OIL_COST_TYPE = #{oilCostType},
      </if>
      <if test="versionId != null">
        VERSION_ID = #{versionId},
      </if>
        <if test="deductionFeeType != null">
            deduction_fee_type = #{deductionFeeType},
        </if>
        <if test="goodsCharacter != null">
            goods_character = #{goodsCharacter},
        </if>
        <if test="isRoundTrip != null">
            is_round_trip = #{isRoundTrip},
        </if>
    </set>
    where ID = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.tms.domain.client.AutoDispatchConfig">
    <!--@mbg.generated-->
    update M_AUTO_DISPATCH_CONFIG
    set CUSTOMER_ID = #{customerId},
      CUST_NAME = #{custName},
      CUST_ABBR = #{custAbbr},
      CARRIER_ID = #{carrierId},
      CARR_NAME = #{carrName},
      DELI_PROVINCE_ID = #{deliProvinceId},
      DELI_CITY_ID = #{deliCityId},
      DELI_AREA_ID = #{deliAreaId},
      DELI_PRO_NAME = #{deliProName},
      DELI_CITY_NAME = #{deliCityName},
      DELI_AREA_NAME = #{deliAreaName},
      ARRI_PROVINCE_ID = #{arriProvinceId},
      ARRI_CITY_ID = #{arriCityId},
      ARRI_AREA_ID = #{arriAreaId},
      ARRI_PRO_NAME = #{arriProName},
      ARRI_CITY_NAME = #{arriCityName},
      ARRI_AREA_NAME = #{arriAreaName},
      DEDUCTION_AMOUNT = #{deductionAmount},
      DEDUCTION_TYPE = #{deductionType},
      REG_USER_ID = #{regUserId},
      REG_USER_NAME = #{regUserName},
      REG_SCR_ID = #{regScrId},
      REG_DATE = #{regDate},
      COR_USER_ID = #{corUserId},
      COR_USER_NAME = #{corUserName},
      COR_DATE = #{corDate},
      COR_SCR_ID = #{corScrId},
      DEL_FLAG = #{delFlag},
      DEL_USERID = #{delUserid},
      DEL_DATE = #{delDate},
      AUTO_DISPATCH_TYPE = #{autoDispatchType},
      BILLING_METHOD = #{billingMethod},
      CAR_LEN = #{carLen},
      CAR_LEN_NAME = #{carLenName},
      CAR_TYPE = #{carType},
      CAR_TYPE_NAME = #{carTypeName},
      same_id = #{sameId},
      billing_type = #{billingType},
      oil_ratio = #{oilRatio},
      arri_addr_name = #{arriAddrName},
      arri_type = #{arriType},
      oil_type = #{oilType},
      OIL_COST_TYPE = #{oilCostType},
      VERSION_ID = #{versionId},
      DEDUCTION_FEE_TYPE = #{deductionFeeType},
      goods_character = #{goodsCharacter},
      is_round_trip = #{isRoundTrip}
    where ID = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update M_AUTO_DISPATCH_CONFIG
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="CUSTOMER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.customerId}
        </foreach>
      </trim>
      <trim prefix="CUST_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.custName}
        </foreach>
      </trim>
      <trim prefix="CUST_ABBR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.custAbbr}
        </foreach>
      </trim>
      <trim prefix="CARRIER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.carrierId}
        </foreach>
      </trim>
      <trim prefix="CARR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.carrName}
        </foreach>
      </trim>
      <trim prefix="DELI_PROVINCE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.deliProvinceId}
        </foreach>
      </trim>
      <trim prefix="DELI_CITY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.deliCityId}
        </foreach>
      </trim>
      <trim prefix="DELI_AREA_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.deliAreaId}
        </foreach>
      </trim>
      <trim prefix="DELI_PRO_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.deliProName}
        </foreach>
      </trim>
      <trim prefix="DELI_CITY_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.deliCityName}
        </foreach>
      </trim>
      <trim prefix="DELI_AREA_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.deliAreaName}
        </foreach>
      </trim>
      <trim prefix="ARRI_PROVINCE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.arriProvinceId}
        </foreach>
      </trim>
      <trim prefix="ARRI_CITY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.arriCityId}
        </foreach>
      </trim>
      <trim prefix="ARRI_AREA_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.arriAreaId}
        </foreach>
      </trim>
      <trim prefix="ARRI_PRO_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.arriProName}
        </foreach>
      </trim>
      <trim prefix="ARRI_CITY_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.arriCityName}
        </foreach>
      </trim>
      <trim prefix="ARRI_AREA_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.arriAreaName}
        </foreach>
      </trim>
      <trim prefix="DEDUCTION_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.deductionAmount}
        </foreach>
      </trim>
      <trim prefix="DEDUCTION_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.deductionType}
        </foreach>
      </trim>
      <trim prefix="REG_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.regUserId}
        </foreach>
      </trim>
      <trim prefix="REG_USER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.regUserName}
        </foreach>
      </trim>
      <trim prefix="REG_SCR_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.regScrId}
        </foreach>
      </trim>
      <trim prefix="REG_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.regDate}
        </foreach>
      </trim>
      <trim prefix="COR_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.corUserId}
        </foreach>
      </trim>
      <trim prefix="COR_USER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.corUserName}
        </foreach>
      </trim>
      <trim prefix="COR_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.corDate}
        </foreach>
      </trim>
      <trim prefix="COR_SCR_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.corScrId}
        </foreach>
      </trim>
      <trim prefix="DEL_FLAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.delFlag}
        </foreach>
      </trim>
      <trim prefix="DEL_USERID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.delUserid}
        </foreach>
      </trim>
      <trim prefix="DEL_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.delDate}
        </foreach>
      </trim>
      <trim prefix="AUTO_DISPATCH_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.autoDispatchType}
        </foreach>
      </trim>
      <trim prefix="BILLING_METHOD = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.billingMethod}
        </foreach>
      </trim>
      <trim prefix="CAR_LEN = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.carLen}
        </foreach>
      </trim>
      <trim prefix="CAR_LEN_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.carLenName}
        </foreach>
      </trim>
      <trim prefix="CAR_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.carType}
        </foreach>
      </trim>
      <trim prefix="CAR_TYPE_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.carTypeName}
        </foreach>
      </trim>
      <trim prefix="same_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.sameId}
        </foreach>
      </trim>
      <trim prefix="billing_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.billingType}
        </foreach>
      </trim>
      <trim prefix="oil_ratio = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.oilRatio}
        </foreach>
      </trim>
      <trim prefix="arri_addr_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.arriAddrName}
        </foreach>
      </trim>
      <trim prefix="arri_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.arriType}
        </foreach>
      </trim>
      <trim prefix="oil_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.oilType}
        </foreach>
      </trim>
      <trim prefix="OIL_COST_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id} then #{item.oilCostType}
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>

  <update id="updateByCustomerId">
    <!--@mbg.generated-->
    update M_AUTO_DISPATCH_CONFIG
    <set>
      <if test="updated.id != null">
        ID = #{updated.id,jdbcType=VARCHAR},
      </if>
      <if test="updated.customerId != null">
        CUSTOMER_ID = #{updated.customerId,jdbcType=VARCHAR},
      </if>
      <if test="updated.custName != null">
        CUST_NAME = #{updated.custName,jdbcType=VARCHAR},
      </if>
      <if test="updated.custAbbr != null">
        CUST_ABBR = #{updated.custAbbr,jdbcType=VARCHAR},
      </if>
      <if test="updated.carrierId != null">
        CARRIER_ID = #{updated.carrierId,jdbcType=VARCHAR},
      </if>
      <if test="updated.carrName != null">
        CARR_NAME = #{updated.carrName,jdbcType=VARCHAR},
      </if>
      <if test="updated.deliProvinceId != null">
        DELI_PROVINCE_ID = #{updated.deliProvinceId,jdbcType=VARCHAR},
      </if>
      <if test="updated.deliCityId != null">
        DELI_CITY_ID = #{updated.deliCityId,jdbcType=VARCHAR},
      </if>
      <if test="updated.deliAreaId != null">
        DELI_AREA_ID = #{updated.deliAreaId,jdbcType=VARCHAR},
      </if>
      <if test="updated.deliProName != null">
        DELI_PRO_NAME = #{updated.deliProName,jdbcType=VARCHAR},
      </if>
      <if test="updated.deliCityName != null">
        DELI_CITY_NAME = #{updated.deliCityName,jdbcType=VARCHAR},
      </if>
      <if test="updated.deliAreaName != null">
        DELI_AREA_NAME = #{updated.deliAreaName,jdbcType=VARCHAR},
      </if>
      <if test="updated.arriProvinceId != null">
        ARRI_PROVINCE_ID = #{updated.arriProvinceId,jdbcType=VARCHAR},
      </if>
      <if test="updated.arriCityId != null">
        ARRI_CITY_ID = #{updated.arriCityId,jdbcType=VARCHAR},
      </if>
      <if test="updated.arriAreaId != null">
        ARRI_AREA_ID = #{updated.arriAreaId,jdbcType=VARCHAR},
      </if>
      <if test="updated.arriProName != null">
        ARRI_PRO_NAME = #{updated.arriProName,jdbcType=VARCHAR},
      </if>
      <if test="updated.arriCityName != null">
        ARRI_CITY_NAME = #{updated.arriCityName,jdbcType=VARCHAR},
      </if>
      <if test="updated.arriAreaName != null">
        ARRI_AREA_NAME = #{updated.arriAreaName,jdbcType=VARCHAR},
      </if>
      <if test="updated.deductionAmount != null">
        DEDUCTION_AMOUNT = #{updated.deductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="updated.deductionType != null">
        DEDUCTION_TYPE = #{updated.deductionType,jdbcType=DECIMAL},
      </if>
      <if test="updated.regUserId != null">
        REG_USER_ID = #{updated.regUserId,jdbcType=VARCHAR},
      </if>
      <if test="updated.regUserName != null">
        REG_USER_NAME = #{updated.regUserName,jdbcType=VARCHAR},
      </if>
      <if test="updated.regScrId != null">
        REG_SCR_ID = #{updated.regScrId,jdbcType=VARCHAR},
      </if>
      <if test="updated.regDate != null">
        REG_DATE = #{updated.regDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.corUserId != null">
        COR_USER_ID = #{updated.corUserId,jdbcType=VARCHAR},
      </if>
      <if test="updated.corUserName != null">
        COR_USER_NAME = #{updated.corUserName,jdbcType=VARCHAR},
      </if>
      <if test="updated.corDate != null">
        COR_DATE = #{updated.corDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.corScrId != null">
        COR_SCR_ID = #{updated.corScrId,jdbcType=VARCHAR},
      </if>
      <if test="updated.delFlag != null">
        DEL_FLAG = #{updated.delFlag,jdbcType=DECIMAL},
      </if>
      <if test="updated.delUserid != null">
        DEL_USERID = #{updated.delUserid,jdbcType=VARCHAR},
      </if>
      <if test="updated.delDate != null">
        DEL_DATE = #{updated.delDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.autoDispatchType != null">
        AUTO_DISPATCH_TYPE = #{updated.autoDispatchType,jdbcType=DECIMAL},
      </if>
      <if test="updated.billingMethod != null">
        BILLING_METHOD = #{updated.billingMethod,jdbcType=VARCHAR},
      </if>
      <if test="updated.carLen != null">
          CAR_LEN = #{updated.carLen,jdbcType=VARCHAR},
      </if>
      <if test="updated.carLenName != null">
          CAR_LEN_NAME = #{updated.carLenName,jdbcType=VARCHAR},
      </if>
      <if test="updated.carType != null">
          CAR_TYPE = #{updated.carType,jdbcType=VARCHAR},
      </if>
      <if test="updated.carTypeName != null">
          CAR_TYPE_NAME = #{updated.carTypeName,jdbcType=VARCHAR},
      </if>
      <if test="updated.sameId != null">
        same_id = #{updated.sameId,jdbcType=VARCHAR},
      </if>
      <if test="updated.billingType != null">
        billing_type = #{updated.billingType,jdbcType=VARCHAR},
      </if>
      <if test="updated.oilRatio != null">
        oil_ratio = #{updated.oilRatio,jdbcType=DECIMAL},
      </if>
      <if test="updated.arriAddrName != null">
        arri_addr_name = #{updated.arriAddrName,jdbcType=VARCHAR},
      </if>
      <if test="updated.arriType != null">
        arri_type = #{updated.arriType,jdbcType=VARCHAR},
      </if>
      <if test="updated.oilType != null">
        oil_type = #{updated.oilType,jdbcType=INTEGER},
      </if>
      <if test="updated.oilCostType != null">
        OIL_COST_TYPE = #{updated.oilCostType,jdbcType=VARCHAR},
      </if>
      <if test="updated.deductionFeeType != null">
        DEDUCTION_FEE_TYPE = #{updated.deductionFeeType,jdbcType=DECIMAL},
      </if>
      <if test="updated.goodsCharacter != null">
        goods_character = #{updated.goodsCharacter,jdbcType=INTEGER},
      </if>
      <if test="updated.isRoundTrip != null">
        is_round_trip = #{updated.isRoundTrip,jdbcType=INTEGER},
      </if>
    </set>
    where CUSTOMER_ID=#{customerId,jdbcType=VARCHAR} and DEL_FLAG = 0
  </update>

  <update id="updateBySameId">
    <!--@mbg.generated-->
    update M_AUTO_DISPATCH_CONFIG
    <set>
      <if test="updated.id != null">
        ID = #{updated.id,jdbcType=VARCHAR},
      </if>
      <if test="updated.customerId != null">
        CUSTOMER_ID = #{updated.customerId,jdbcType=VARCHAR},
      </if>
      <if test="updated.custName != null">
        CUST_NAME = #{updated.custName,jdbcType=VARCHAR},
      </if>
      <if test="updated.custAbbr != null">
        CUST_ABBR = #{updated.custAbbr,jdbcType=VARCHAR},
      </if>
      <if test="updated.carrierId != null">
        CARRIER_ID = #{updated.carrierId,jdbcType=VARCHAR},
      </if>
      <if test="updated.carrName != null">
        CARR_NAME = #{updated.carrName,jdbcType=VARCHAR},
      </if>
      <if test="updated.deliProvinceId != null">
        DELI_PROVINCE_ID = #{updated.deliProvinceId,jdbcType=VARCHAR},
      </if>
      <if test="updated.deliCityId != null">
        DELI_CITY_ID = #{updated.deliCityId,jdbcType=VARCHAR},
      </if>
      <if test="updated.deliAreaId != null">
        DELI_AREA_ID = #{updated.deliAreaId,jdbcType=VARCHAR},
      </if>
      <if test="updated.deliProName != null">
        DELI_PRO_NAME = #{updated.deliProName,jdbcType=VARCHAR},
      </if>
      <if test="updated.deliCityName != null">
        DELI_CITY_NAME = #{updated.deliCityName,jdbcType=VARCHAR},
      </if>
      <if test="updated.deliAreaName != null">
        DELI_AREA_NAME = #{updated.deliAreaName,jdbcType=VARCHAR},
      </if>
      <if test="updated.arriProvinceId != null">
        ARRI_PROVINCE_ID = #{updated.arriProvinceId,jdbcType=VARCHAR},
      </if>
      <if test="updated.arriCityId != null">
        ARRI_CITY_ID = #{updated.arriCityId,jdbcType=VARCHAR},
      </if>
      <if test="updated.arriAreaId != null">
        ARRI_AREA_ID = #{updated.arriAreaId,jdbcType=VARCHAR},
      </if>
      <if test="updated.arriProName != null">
        ARRI_PRO_NAME = #{updated.arriProName,jdbcType=VARCHAR},
      </if>
      <if test="updated.arriCityName != null">
        ARRI_CITY_NAME = #{updated.arriCityName,jdbcType=VARCHAR},
      </if>
      <if test="updated.arriAreaName != null">
        ARRI_AREA_NAME = #{updated.arriAreaName,jdbcType=VARCHAR},
      </if>
      <if test="updated.deductionAmount != null">
        DEDUCTION_AMOUNT = #{updated.deductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="updated.deductionType != null">
        DEDUCTION_TYPE = #{updated.deductionType,jdbcType=DECIMAL},
      </if>
      <if test="updated.regUserId != null">
        REG_USER_ID = #{updated.regUserId,jdbcType=VARCHAR},
      </if>
      <if test="updated.regUserName != null">
        REG_USER_NAME = #{updated.regUserName,jdbcType=VARCHAR},
      </if>
      <if test="updated.regScrId != null">
        REG_SCR_ID = #{updated.regScrId,jdbcType=VARCHAR},
      </if>
      <if test="updated.regDate != null">
        REG_DATE = #{updated.regDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.corUserId != null">
        COR_USER_ID = #{updated.corUserId,jdbcType=VARCHAR},
      </if>
      <if test="updated.corUserName != null">
        COR_USER_NAME = #{updated.corUserName,jdbcType=VARCHAR},
      </if>
      <if test="updated.corDate != null">
        COR_DATE = #{updated.corDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.corScrId != null">
        COR_SCR_ID = #{updated.corScrId,jdbcType=VARCHAR},
      </if>
      <if test="updated.delFlag != null">
        DEL_FLAG = #{updated.delFlag,jdbcType=DECIMAL},
      </if>
      <if test="updated.delUserid != null">
        DEL_USERID = #{updated.delUserid,jdbcType=VARCHAR},
      </if>
      <if test="updated.delDate != null">
        DEL_DATE = #{updated.delDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.autoDispatchType != null">
        AUTO_DISPATCH_TYPE = #{updated.autoDispatchType,jdbcType=DECIMAL},
      </if>
      <if test="updated.billingMethod != null">
        BILLING_METHOD = #{updated.billingMethod,jdbcType=VARCHAR},
      </if>
      <if test="updated.carLen != null">
        CAR_LEN = #{updated.carLen,jdbcType=VARCHAR},
      </if>
      <if test="updated.carLenName != null">
        CAR_LEN_NAME = #{updated.carLenName,jdbcType=VARCHAR},
      </if>
      <if test="updated.carType != null">
        CAR_TYPE = #{updated.carType,jdbcType=VARCHAR},
      </if>
      <if test="updated.carTypeName != null">
        CAR_TYPE_NAME = #{updated.carTypeName,jdbcType=VARCHAR},
      </if>
      <if test="updated.sameId != null">
        SAME_ID = #{updated.sameId,jdbcType=VARCHAR},
      </if>
      <if test="updated.billingType != null">
        billing_type = #{updated.billingType,jdbcType=VARCHAR},
      </if>
      <if test="updated.oilRatio != null">
        oil_ratio = #{updated.oilRatio,jdbcType=DECIMAL},
      </if>
      <if test="updated.arriAddrName != null">
        arri_addr_name = #{updated.arriAddrName,jdbcType=VARCHAR},
      </if>
      <if test="updated.arriType != null">
        arri_type = #{updated.arriType,jdbcType=INTEGER},
      </if>
      <if test="updated.oilType != null">
        oil_type = #{updated.oilType,jdbcType=INTEGER},
      </if>
      <if test="updated.oilCostType != null">
        OIL_COST_TYPE = #{updated.oilCostType,jdbcType=VARCHAR},
      </if>
      <if test="updated.versionId != null">
        VERSION_ID = #{updated.versionId,jdbcType=VARCHAR},
      </if>
        <if test="updated.deductionFeeType != null">
            DEDUCTION_FEE_TYPE = #{updated.deductionFeeType,jdbcType=DECIMAL},
        </if>
        <if test="updated.goodsCharacter != null">
            goods_character = #{updated.goodsCharacter,jdbcType=INTEGER},
        </if>
        <if test="updated.isRoundTrip != null">
            is_round_trip = #{updated.isRoundTrip,jdbcType=INTEGER},
        </if>

    </set>
    where SAME_ID=#{sameId,jdbcType=VARCHAR}
  </update>

  <insert id="addBatchByArriAddr" parameterType="com.ruoyi.tms.domain.client.AutoDispatchConfig">
      insert into M_AUTO_DISPATCH_CONFIG
      <trim prefix="(" suffix=")" suffixOverrides=",">
        ID,
        same_id,
        CUSTOMER_ID,
        CUST_NAME,
        CUST_ABBR,
        CARRIER_ID,
        CARR_NAME,
        DELI_PROVINCE_ID,
        DELI_CITY_ID,
        DELI_AREA_ID,
        DELI_PRO_NAME,
        DELI_CITY_NAME,
        DELI_AREA_NAME,
        ARRI_PROVINCE_ID,
        ARRI_CITY_ID,
        ARRI_AREA_ID,
        ARRI_PRO_NAME,
        ARRI_CITY_NAME,
        ARRI_AREA_NAME,
      <if test="deductionAmount != null">
        DEDUCTION_AMOUNT,
      </if>
      <if test="deductionType != null">
        DEDUCTION_TYPE,
      </if>
        REG_USER_ID,
        REG_USER_NAME,
        REG_SCR_ID,
        REG_DATE,
      <if test="corUserId != null">
        COR_USER_ID,
      </if>
      <if test="corUserName != null">
        COR_USER_NAME,
      </if>
      <if test="corDate != null">
        COR_DATE,
      </if>
      <if test="corScrId != null">
        COR_SCR_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
      <if test="delUserid != null">
        DEL_USERID,
      </if>
      <if test="delDate != null">
        DEL_DATE,
      </if>
        AUTO_DISPATCH_TYPE,
      <if test="billingMethod != null">
        BILLING_METHOD,
      </if>
      <if test="carLen != null">
        CAR_LEN,
      </if>
      <if test="carLenName != null">
        CAR_LEN_NAME,
      </if>
      <if test="carType != null">
        CAR_TYPE,
      </if>
      <if test="carTypeName != null">
        CAR_TYPE_NAME,
      </if>
      <if test="billingType != null">
        billing_type,
      </if>
      <if test="oilRatio != null">
        oil_ratio,
      </if>
      <if test="oilType != null">
        oil_type,
      </if>
      <if test="oilCostType != null">
        OIL_COST_TYPE,
      </if>
      <if test="goodsId != null">
        goods_id,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
        <if test="goodsCharacter != null">
            goods_character,
        </if>
        <if test="isRoundTrip != null">
            is_round_trip,
        </if>
        arri_type,
        version_id,
        deduction_fee_type

    </trim>
      select
      <trim suffixOverrides=",">
             REGEXP_SUBSTR(#{id,jdbcType=VARCHAR}, '[^,]+', 1, t3.rn),
             #{sameId},
             #{customerId},
             #{custName},
             #{custAbbr},
             #{carrierId},
             #{carrName},
             #{deliProvinceId},
             #{deliCityId},
             #{deliAreaId},
             #{deliProName},
             #{deliCityName},
             #{deliAreaName},
        t3.province_code,
        t3.city_code,
        t3.area_code,
        t3.province_name,
        t3.city_name,
        t3.area_name,
             <if test="deductionAmount != null">
               #{deductionAmount},
             </if>
             <if test="deductionType != null">
               #{deductionType},
             </if>
             #{regUserId},
             #{regUserName},
             #{regScrId},
             #{regDate},
             <if test="corUserId != null">
                #{corUserId},
             </if><if test="corUserName != null">
                #{corUserName},
            </if>
            <if test="corDate != null">
                #{corDate},
            </if>
            <if test="corScrId != null">
                #{corScrId},
            </if>
            <if test="delFlag != null">
                #{delFlag},
            </if><if test="delUserid != null">
                #{delUserid},
            </if>
            <if test="delDate != null">
                #{delDate},
            </if>
            #{autoDispatchType},
            <if test="billingMethod != null">
                #{billingMethod},
            </if>
            <if test="carLen != null">
                #{carLen},
            </if>
            <if test="carLenName != null">
                #{carLenName},
            </if>
            <if test="carType != null">
                #{carType},
            </if>
            <if test="carTypeName != null">
              #{carTypeName},
            </if>
            <if test="billingType != null">
              #{billingType},
            </if>
            <if test="oilRatio != null">
              #{oilRatio},
            </if>
            <if test="oilType != null">
              #{oilType},
            </if>
            <if test="oilCostType != null">
              #{oilCostType},
            </if>
            <if test="goodsId != null">
              #{goodsId},
            </if>
            <if test="goodsName != null">
              #{goodsName},
            </if>
            <if test="goodsCharacter != null">
              #{goodsCharacter},
            </if>
            <if test="isRoundTrip != null">
              #{isRoundTrip},
            </if>
              #{arriType},
              #{versionId},
              #{deductionFeeType}

      </trim>
    from
      (select t.province_code,
             t.province_name,
             t1.city_code,
             t1.city_name,
             t2.area_code,
             t2.area_name,
             row_number() over(order by dbms_random.value) rn
      from m_province t
             left join m_city t1
                       on t.province_id = t1.province_id
             left join m_area t2
                       on t1.city_id = t2.city_id
        where
      <if test="arriAreaId != null  and arriAreaId != '' and arriAreaId.indexOf(',') != -1">
         t2.area_id in
        <foreach item="item" index="index" collection="arriAreaId.split(',')" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="arriAreaId != null  and arriAreaId != '' and arriAreaId.indexOf(',') == -1">
         t2.area_id = #{arriAreaId}
      </if>
        ) t3
  </insert>

  <select id="selectBySame" resultMap="BaseResultMap">
     select t.same_id,
            LISTAGG(t.id, ',') WITHIN GROUP(ORDER BY t.id) AS id,
            t.customer_id,
            t.cust_name,
            t.cust_abbr,
            t.carrier_id,
            t.carr_name,
            t.deli_province_id,
            t.deli_city_id,
            t.deli_area_id,
            t.deli_pro_name,
            t.deli_city_name,
            t.deli_area_name,
            t.arri_province_id,
            t.arri_city_id,
            LISTAGG(t.arri_area_id, ',') WITHIN GROUP(ORDER BY t.id) AS arri_area_id,
            t.arri_pro_name,
            t.arri_city_name,
            LISTAGG(t.arri_area_name, ',') WITHIN GROUP(ORDER BY t.id) AS arri_area_name,
            t.deduction_amount,
            t.deduction_type,
            t.auto_dispatch_type,
            t.billing_method,
            t.car_len,
            t.car_len_name,
            t.car_type,
            t.car_type_name,
            t.billing_type,
            t.oil_ratio,
            t.arri_addr_name,
            t.arri_type,
            t.oil_type,
            t.OIL_COST_TYPE,
            t.goods_id,
            t.goods_name,
            t.VERSION_ID,
            t.DEDUCTION_FEE_TYPE,
            t.goods_character,
            t.is_round_trip
    from M_AUTO_DISPATCH_CONFIG t
    left join M_AUTO_DISPATCH_VERSION t1 on t.VERSION_ID=t1.ID
    where t.DEL_FLAG = 0
    <if test="id != null">
      and t.ID=#{id,jdbcType=VARCHAR}
    </if>
    <if test="customerId != null">
      and t.CUSTOMER_ID=#{customerId,jdbcType=VARCHAR}
    </if>
    <if test="custName != null">
      and t.CUST_NAME=#{custName,jdbcType=VARCHAR}
    </if>
    <if test="custAbbr != null">
      and t.CUST_ABBR=#{custAbbr,jdbcType=VARCHAR}
    </if>
    <if test="carrierId != null">
      and t.CARRIER_ID=#{carrierId,jdbcType=VARCHAR}
    </if>
    <if test="carrName != null">
      <bind name="carrName" value="carrName+'%'"></bind>
      and t.CARR_NAME like #{carrName}
    </if>
    <if test="deliProvinceId != null">
      and t.DELI_PROVINCE_ID=#{deliProvinceId,jdbcType=VARCHAR}
    </if>
    <if test="deliCityId != null">
      and t.DELI_CITY_ID=#{deliCityId,jdbcType=VARCHAR}
    </if>
    <if test="deliAreaId != null">
      and t.DELI_AREA_ID=#{deliAreaId,jdbcType=VARCHAR}
    </if>
    <if test="deliProName != null">
      and t.DELI_PRO_NAME=#{deliProName,jdbcType=VARCHAR}
    </if>
    <if test="deliCityName != null">
      and t.DELI_CITY_NAME=#{deliCityName,jdbcType=VARCHAR}
    </if>
    <if test="deliAreaName != null">
      and t.DELI_AREA_NAME=#{deliAreaName,jdbcType=VARCHAR}
    </if>
    <if test="arriProvinceId != null">
      and t.ARRI_PROVINCE_ID=#{arriProvinceId,jdbcType=VARCHAR}
    </if>
    <if test="arriCityId != null">
      and t.ARRI_CITY_ID=#{arriCityId,jdbcType=VARCHAR}
    </if>
    <if test="arriAreaId != null">
      and t.ARRI_AREA_ID=#{arriAreaId,jdbcType=VARCHAR}
    </if>
    <if test="arriProName != null">
      and t.ARRI_PRO_NAME=#{arriProName,jdbcType=VARCHAR}
    </if>
    <if test="arriCityName != null">
      and t.ARRI_CITY_NAME=#{arriCityName,jdbcType=VARCHAR}
    </if>
    <if test="arriAreaName != null">
      and t.ARRI_AREA_NAME=#{arriAreaName,jdbcType=VARCHAR}
    </if>
    <if test="deductionAmount != null">
      and t.DEDUCTION_AMOUNT=#{deductionAmount,jdbcType=DECIMAL}
    </if>
    <if test="deductionType != null">
      and t.DEDUCTION_TYPE=#{deductionType,jdbcType=DECIMAL}
    </if>
    <if test="autoDispatchType != null">
      and t.auto_dispatch_type=#{autoDispatchType,jdbcType=DECIMAL}
    </if>
    <if test="billingMethod != null">
      and t.billing_method=#{billingMethod,jdbcType=VARCHAR}
    </if>
    <if test="carLen != null">
      and t.CAR_LEN=#{carLen,jdbcType=VARCHAR}
    </if>
    <if test="carLenName != null">
      and t.CAR_LEN_NAME=#{carLenName,jdbcType=VARCHAR}
    </if>
    <if test="carType != null">
      and t.CAR_TYPE=#{carType,jdbcType=VARCHAR}
    </if>
    <if test="carTypeName != null">
      and t.CAR_TYPE_NAME=#{carTypeName,jdbcType=VARCHAR}
    </if>
    <if test="sameId != null">
      and t.same_id=#{sameId,jdbcType=VARCHAR}
    </if>
    <if test="versionId != null">
      and t.VERSION_ID=#{versionId,jdbcType=VARCHAR}
    </if>
    <if test="versionId == null">
      and t1.IS_LATEST = 1
    </if>
    group by t.same_id,
        t.customer_id,
        t.cust_name,
        t.cust_abbr,
        t.carrier_id,
        t.carr_name,
        t.deli_province_id,
        t.deli_city_id,
        t.deli_area_id,
        t.deli_pro_name,
        t.deli_city_name,
        t.deli_area_name,
        t.arri_province_id,
        t.arri_city_id,
        t.arri_pro_name,
        t.arri_city_name,
        t.deduction_amount,
        t.deduction_type,
        t.auto_dispatch_type,
        t.billing_method,
        t.car_len,
        t.car_len_name,
        t.car_type,
        t.car_type_name,
        t.billing_type,
        t.oil_ratio,
        t.arri_addr_name,
        t.arri_type,
        t.oil_type,
        t.OIL_COST_TYPE,
        t.goods_id,
        t.goods_name,
        t.VERSION_ID,
        t.DEDUCTION_FEE_TYPE,
        t.goods_character,
        t.is_round_trip
    order by t.deli_pro_name desc,t.deli_city_name desc,t.deli_area_name desc,t.arri_pro_name desc,t.arri_city_name desc
  </select>

  <select id="selectBySame1" resultMap="BaseResultMap">
    select t.same_id,
    LISTAGG(t.id, ',') WITHIN GROUP(ORDER BY t.id) AS id,
    t.customer_id,
    t.cust_name,
    t.cust_abbr,
    t.carrier_id,
    t.carr_name,
    t.deli_province_id,
    t.deli_city_id,
    t.deli_area_id,
    t.deli_pro_name,
    t.deli_city_name,
    t.deli_area_name,
    t.arri_province_id,
    t.arri_city_id,
    LISTAGG(t.arri_area_id, ',') WITHIN GROUP(ORDER BY t.id) AS arri_area_id,
    t.arri_pro_name,
    t.arri_city_name,
    LISTAGG(t.arri_area_name, ',') WITHIN GROUP(ORDER BY t.id) AS arri_area_name,
    t.deduction_amount,
    t.deduction_type,
    t.auto_dispatch_type,
    t.billing_method,
    t.car_len,
    t.car_len_name,
    t.car_type,
    t.car_type_name,
    t.billing_type,
    t.oil_ratio,
    t.arri_addr_name,
    t.arri_type,
    t.oil_type,
    t.OIL_COST_TYPE,
    t.goods_id,
    t.goods_name,
    t.VERSION_ID,
    t.DEDUCTION_FEE_TYPE,
    t.goods_character,
    t.is_round_trip,
    to_char(t.cor_date,'yyyy-MM-dd') cor_date_str
    from M_AUTO_DISPATCH_CONFIG t
    left join M_AUTO_DISPATCH_VERSION t1 on t.VERSION_ID=t1.ID
    where t.DEL_FLAG = 0
    <if test="id != null">
      and t.ID=#{id,jdbcType=VARCHAR}
    </if>
    <if test="customerId != null">
      and t.CUSTOMER_ID=#{customerId,jdbcType=VARCHAR}
    </if>
    <if test="custName != null">
      and t.CUST_NAME=#{custName,jdbcType=VARCHAR}
    </if>
    <if test="custAbbr != null">
      and t.CUST_ABBR=#{custAbbr,jdbcType=VARCHAR}
    </if>
    <if test="carrierId != null">
      and t.CARRIER_ID=#{carrierId,jdbcType=VARCHAR}
    </if>
    <if test="carrName != null">
      <bind name="carrName" value="carrName+'%'"></bind>
      and t.CARR_NAME like #{carrName}
    </if>
    <if test="deliProvinceId != null">
      and t.DELI_PROVINCE_ID=#{deliProvinceId,jdbcType=VARCHAR}
    </if>
    <if test="deliCityId != null">
      and t.DELI_CITY_ID=#{deliCityId,jdbcType=VARCHAR}
    </if>
    <if test="deliAreaId != null">
      and t.DELI_AREA_ID=#{deliAreaId,jdbcType=VARCHAR}
    </if>
    <if test="deliProName != null">
      and t.DELI_PRO_NAME=#{deliProName,jdbcType=VARCHAR}
    </if>
    <if test="deliCityName != null">
      and t.DELI_CITY_NAME=#{deliCityName,jdbcType=VARCHAR}
    </if>
    <if test="deliAreaName != null">
      and t.DELI_AREA_NAME=#{deliAreaName,jdbcType=VARCHAR}
    </if>
    <if test="arriProvinceId != null">
      and t.ARRI_PROVINCE_ID=#{arriProvinceId,jdbcType=VARCHAR}
    </if>
    <if test="arriCityId != null">
      and t.ARRI_CITY_ID=#{arriCityId,jdbcType=VARCHAR}
    </if>
    <if test="arriAreaId != null">
      and t.ARRI_AREA_ID=#{arriAreaId,jdbcType=VARCHAR}
    </if>
    <if test="arriProName != null">
      and t.ARRI_PRO_NAME=#{arriProName,jdbcType=VARCHAR}
    </if>
    <if test="arriCityName != null">
      and t.ARRI_CITY_NAME=#{arriCityName,jdbcType=VARCHAR}
    </if>
    <if test="arriAreaName != null">
      and t.ARRI_AREA_NAME=#{arriAreaName,jdbcType=VARCHAR}
    </if>
    <if test="deductionAmount != null">
      and t.DEDUCTION_AMOUNT=#{deductionAmount,jdbcType=DECIMAL}
    </if>
    <if test="deductionType != null">
      and t.DEDUCTION_TYPE=#{deductionType,jdbcType=DECIMAL}
    </if>
    <if test="autoDispatchType != null">
      and t.auto_dispatch_type=#{autoDispatchType,jdbcType=DECIMAL}
    </if>
    <if test="billingMethod != null">
      and t.billing_method=#{billingMethod,jdbcType=VARCHAR}
    </if>
    <if test="carLen != null">
      and t.CAR_LEN=#{carLen,jdbcType=VARCHAR}
    </if>
    <if test="carLenName != null">
      and t.CAR_LEN_NAME=#{carLenName,jdbcType=VARCHAR}
    </if>
    <if test="carType != null">
      and t.CAR_TYPE=#{carType,jdbcType=VARCHAR}
    </if>
    <if test="carTypeName != null">
      and t.CAR_TYPE_NAME=#{carTypeName,jdbcType=VARCHAR}
    </if>
    <if test="sameId != null">
      and t.same_id=#{sameId,jdbcType=VARCHAR}
    </if>
    <if test="versionId != null">
      and t.VERSION_ID=#{versionId,jdbcType=VARCHAR}
    </if>
    <if test="versionId == null">
      and t1.IS_LATEST = 1
    </if>
    group by t.same_id,
    t.customer_id,
    t.cust_name,
    t.cust_abbr,
    t.carrier_id,
    t.carr_name,
    t.deli_province_id,
    t.deli_city_id,
    t.deli_area_id,
    t.deli_pro_name,
    t.deli_city_name,
    t.deli_area_name,
    t.arri_province_id,
    t.arri_city_id,
    t.arri_pro_name,
    t.arri_city_name,
    t.deduction_amount,
    t.deduction_type,
    t.auto_dispatch_type,
    t.billing_method,
    t.car_len,
    t.car_len_name,
    t.car_type,
    t.car_type_name,
    t.billing_type,
    t.oil_ratio,
    t.arri_addr_name,
    t.arri_type,
    t.oil_type,
    t.OIL_COST_TYPE,
    t.goods_id,
    t.goods_name,
    t.VERSION_ID,
    t.DEDUCTION_FEE_TYPE,
    t.goods_character,
    t.is_round_trip,
    to_char(t.cor_date,'yyyy-MM-dd')
    order by to_char(t.cor_date,'yyyy-MM-dd') desc,t.deli_pro_name desc,t.deli_city_name desc,t.deli_area_name desc,t.arri_pro_name desc,t.arri_city_name desc
  </select>

  <select id="getPriceList" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from M_AUTO_DISPATCH_CONFIG
    <where>
          DEL_FLAG = 0
      and auto_dispatch_type=2
      and version_id=#{versionId,jdbcType=VARCHAR}
      and CUSTOMER_ID=#{customerId,jdbcType=VARCHAR}

<!--      and DELI_AREA_ID=#{deliAreaId,jdbcType=VARCHAR}-->
<!--      and (ARRI_AREA_ID=#{arriAreaId,jdbcType=VARCHAR}-->
<!--            or arri_addr_name=#{arriAddrName,jdbcType=VARCHAR})-->

      <if test="deliAreaIdList != null and deliAreaIdList.size() != 0">
        and DELI_AREA_ID in
        <foreach item="id" collection="deliAreaIdList" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
      <if test="deliAreaIdList == null or deliAreaIdList.size() == 0">
        and DELI_AREA_ID is null
      </if>


      <if test="arriAreaIdList != null and arriAreaIdList.size() != 0">
        and (ARRI_AREA_ID in
        <foreach item="id" collection="arriAreaIdList" open="(" separator="," close=")">
          #{id}
        </foreach>

        <if test="arriAddrNameList != null and arriAddrNameList.size() != 0">
          or arri_addr_name in
          <foreach item="id" collection="arriAddrNameList" open="(" separator="," close=")">
            #{id}
          </foreach>
        </if>

        )
      </if>
      <if test="arriAreaIdList == null or arriAreaIdList.size() == 0">
        and ARRI_AREA_ID is null
      </if>

      <if test="id != null">
        and id=#{id,jdbcType=VARCHAR}
      </if>
      <if test="carrierId != null">
        and CARRIER_ID=#{carrierId,jdbcType=VARCHAR}
      </if>
      <if test="billingMethod != null">
        and billing_method=#{billingMethod,jdbcType=VARCHAR}
      </if>
      <if test="carLen != null">
        and CAR_LEN=#{carLen,jdbcType=VARCHAR}
      </if>
      <if test="carType != null">
        and CAR_TYPE=#{carType,jdbcType=VARCHAR}
      </if>
    </where>


  </select>

  <select id="selectAnalyzeList" resultType="com.ruoyi.tms.vo.client.AutoDispatchAnalyzeVO">
    select tt.*,tt1.invalid_date invalidDate from (
      select t.carrier_id                                 carrierId,
            t.carr_name                                   carrName,
            listagg(distinct t.customer_id, ',')          customerId,
            listagg(distinct t.cust_abbr, ',')            custAbbr,
            t.deli_pro_name                               deliProName,
            t.deli_city_name                              deliCityName,
            listagg(distinct t.deli_area_name, ',')       deliAreaName,
            t.arri_type                                   arriType,
            listagg(distinct t.arri_addr_name, ',')       arriAddrName,
            t.arri_pro_name                               arriProName,
            t.arri_city_name                              arriCityName,
            listagg(distinct t.arri_area_name, ',')       arriAreaName,
            t.car_len_name                                carLenName,
            t.car_type_name                               carTypeName,
            t.billing_method                              billingMethod,
            t.billing_type                                billingType,
            t.oil_cost_type                               oilCostType,
            t.oil_type                                    oilType,
            t.oil_ratio                                   oilRatio,
            t.deduction_type                              deductionType,
            t.deduction_fee_type                          deductionFeeType,
            t.deduction_amount                            deductionAmount,
            t2.section_amount                             sectionAmount
      from M_AUTO_DISPATCH_CONFIG t
      left join M_AUTO_DISPATCH_VERSION t1 on t.version_id = t1.id
      left join (SELECT a.auto_dispatch_config_id,
                        LISTAGG(
                          CASE a.start_operator
                              WHEN 0 THEN TO_CHAR(a.start_section) || <![CDATA[ '<x' ]]>
                              WHEN 1 THEN TO_CHAR(a.start_section) || <![CDATA[ '<=x' ]]>
                              ELSE '' END
                          || CASE a.end_operator
                                  WHEN 2 THEN <![CDATA[ '<' ]]> || TO_CHAR(a.end_section)
                                  WHEN 3 THEN <![CDATA[ '<=' ]]> || TO_CHAR(a.end_section)
                                  ELSE '' END
                          || ' ¥' || TO_CHAR(a.price, 'FM9999999990.00')
                          || CASE a.is_fixed_price WHEN 1 THEN ' 固' ELSE '' END, ', '
                          ) WITHIN GROUP (ORDER BY a.start_section) AS section_amount
                  FROM M_AUTO_DISPATCH_SECTION a
                  WHERE a.del_flag = 0
                  GROUP BY a.auto_dispatch_config_id) t2 on t.id=t2.auto_dispatch_config_id
      where t.del_flag = 0
          and t1.is_latest = 1
        <if test="carrName != null and carrName != ''">
          <bind name="carrName" value="carrName+'%'"></bind>
          and t.CARR_NAME like #{carrName}
        </if>
        <if test="custAbbr != null and custAbbr != ''">
          <bind name="custAbbr" value="custAbbr+'%'"></bind>
          and t.cust_abbr like #{custAbbr}
        </if>
      group  by t.carrier_id,
                t.carr_name,
           --     t.customer_id,
           --     t.cust_abbr,
                t.deli_pro_name,
                t.deli_city_name,
                t.arri_type,
                t.arri_pro_name,
                t.arri_city_name,
                t.car_len_name,
                t.car_type_name,
                t.billing_method,
                t.billing_type,
                t.oil_cost_type,
                t.oil_type,
                t.oil_ratio,
                t.deduction_type,
                t.deduction_fee_type,
                t.deduction_amount,
                t2.section_amount
      order by t.carrier_id,
                t.carr_name,
           --     t.customer_id,
           --     t.cust_abbr,
                t.deli_pro_name,
                t.deli_city_name,
                t.arri_type,
                t.arri_pro_name,
                t.arri_city_name,
                t.car_len_name,
                t.car_type_name,
                t.billing_method,
                t.billing_type,
                t.oil_cost_type,
                t.deduction_type,
                t.deduction_fee_type
    )tt left join (select a.party_b_id,
                        case
                            when max(a.LONG_TERM_EFFECTIVE) = '1' then '长期有效'
                            else
                                case
                                    when max(a.end_date) is null and max(a.EXTENSION_DATE) is null then null
                                    when max(a.end_date) is null then to_char(max(a.EXTENSION_DATE), 'yyyy-mm-dd')
                                    when max(a.EXTENSION_DATE) is null then to_char(max(a.end_date), 'yyyy-mm-dd')
                                    else to_char(greatest(max(a.end_date), max(a.EXTENSION_DATE)),'yyyy-mm-dd')
                                    end
                            end invalid_date
                    from M_CONTRACT_CARRIER a
                    where a.del_flag = 0
                group by a.party_b_id
    ) tt1 on tt.carrierId=tt1.party_b_id
  </select>
</mapper>