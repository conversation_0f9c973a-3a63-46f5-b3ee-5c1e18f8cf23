<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tms.mapper.client.ClientMapper">
    <resultMap type="com.ruoyi.tms.domain.client.Client" id="ClientResult">
        <result property="customerId"    column="customer_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="delDate"    column="del_date"    />
        <result property="custCode"    column="cust_code"    />
        <result property="custName"    column="cust_name"    />
        <result property="custAbbr"    column="cust_abbr"    />
        <result property="custType"    column="cust_type"    />
        <result property="ifAuthentication"    column="if_authentication"    />
        <result property="salesDept"    column="sales_dept"    />
        <result property="balaDept"    column="bala_dept"    />
        <result property="stationDept"    column="station_dept"    />
        <result property="balaCorp"    column="bala_corp"    />
        <result property="psndoc"    column="psndoc"    />
        <result property="psncontact"    column="psncontact"    />
        <result property="provinceId"    column="province_id"    />
        <result property="cityId"    column="city_id"    />
        <result property="areaId"    column="area_id"    />
        <result property="address"    column="address"    />
        <result property="contact"    column="contact"    />
        <result property="contactPost"    column="contact_post"    />
        <result property="phone"    column="phone"    />
        <result property="mobile"    column="mobile"    />
        <result property="email"    column="email"    />
        <result property="fax"    column="fax"    />
        <result property="zipcode"    column="zipcode"    />
        <result property="billingCorp"    column="billing_corp"    />
        <result property="billingDate"    column="billing_date"    />
        <result property="taxIdentify"    column="tax_identify"    />
        <result property="balaType"    column="bala_type"    />
        <result property="billingType"    column="billing_type"    />
        <result property="billingPayable"    column="billing_payable"    />
        <result property="bank"    column="bank"    />
        <result property="discountRate"    column="discount_rate"    />
        <result property="accPeriodAhead"    column="acc_period_ahead"    />
        <result property="billingAhead"    column="billing_ahead"    />
        <result property="creditAmount"    column="credit_amount"    />
        <result property="accountName"    column="account_name"    />
        <result property="bankAccount"    column="bank_account"    />
        <result property="registerAddr"    column="register_addr"    />
        <result property="billingRule"    column="billing_rule"    />
        <result property="legalRepresent"    column="legal_represent"    />
        <result property="registerCapital"    column="register_capital"    />
        <result property="addressee"    column="addressee"    />
        <result property="addresseeContact"    column="addressee_contact"    />
        <result property="addresseeAddr"    column="addressee_addr"    />
        <result property="website"    column="website"    />
        <result property="memo"    column="memo"    />
        <result property="custActivity"    column="cust_activity"    />
        <result property="regUserId"    column="reg_user_id"    />
        <result property="regDate"    column="reg_date"    />
        <result property="corUserId"    column="cor_user_id"    />
        <result property="corDate"    column="cor_date"    />
        <result property="regScrId"    column="reg_scr_id"    />
        <result property="corScrId"    column="cor_scr_id"    />
        <result property="businesslicense"    column="businesslicense"    />
        <result property="psndocName"    column="psndoc_name"    />
        <result property="legalCard"    column="legal_card"    />
        <result property="customerType"    column="customer_type"    />
        <result property="paymentDays"    column="payment_days"    />
        <result property="provinceName"    column="province_name"    />
        <result property="cityName"    column="city_name"    />
        <result property="areaName"    column="area_name"    />
        <result property="delUserId"    column="del_user_id"    />
        <result property="bankId"    column="bank_id"    />
        <result property="checkStatus"    column="check_status"    />
        <result property="checkMan"    column="check_man"    />
        <result property="checkDate"    column="check_date"    />
        <result property="crtGuidePrice"    column="crt_guide_price"    />
        <result property="customerLevel"    column="customer_level"    />
        <result property="tariff"    column="tariff"    />
        <result property="isNeedReceipt"    column="is_need_receipt"    />
        <result property="isNeedTrace"    column="is_need_trace"    />
        <result property="invoiceDays"    column="invoice_days"    />
        <result property="collectionDays"    column="collection_days"    />
        <result property="customerSource"    column="customer_source"    />
        <result property="enterpriseNature"    column="enterprise_nature"    />
        <result property="appDeliContact"    column="app_deli_contact"    />
        <result property="appDeliMobile"    column="app_deli_mobile"    />
        <result property="relatedCustName" column="related_cust_name"/>
        <result property="isEnabled" column="is_enabled"/>
        <result property="salesName" column="SALES_NAME"/>
        <result property="enableContractPrice" column="ENABLE_CONTRACT_PRICE"/>
        <result property="isSpecialReferencePrice" column="is_special_reference_price"/>
        <result property="ifBargain" column="if_bargain"/>
        <result property="operateCorp" column="operate_corp"/>
        <result property="isLockOtherFee" column="is_lock_other_fee"/>
        <result property="adjustment" column="adjustment"/>
        <result property="handlingCharges" column="handling_charges"/>
        <result property="handlingChargesType" column="handling_charges_type"/>
        <result property="salesId" column="SALES_ID"/>
        <result property="referrer" column="referrer"/>
        <result property="contractNeedType" column="contract_Need_Type"/>
        <result property="invoiceSalesDeptName" column="invoiceSalesDeptName"/>
        <result property="disabledTime" column="disabled_time"/>
        <result property="mgmtDeptId" column="mgmt_dept_id"/>
        <result property="mgmtDeptName" column="mgmt_dept_name"/>
        <result property="opsDeptId" column="ops_dept_id"/>
        <result property="opsDeptName" column="ops_dept_name"/>

        <result property="subjectAmount" column="subject_amount"/>
        <result property="invalidDate" column="invalid_date"/>
        <result property="saleDeptName" column="sales_dept_name"/>
        <result property="saleDeptParentName" column="sales_dept_parent_name"/>

        <!-- 运营组(部门) -->
        <association property="sysDept" javaType="com.ruoyi.system.domain.SysDept">
            <id column="saleId" property="deptId"/>
            <result column="saleDeptName" property="deptName"/>
        </association>
        <!-- 驻场组(部门) -->
        <association property="stationDeptName" javaType="com.ruoyi.system.domain.SysDept">
            <id column="stationId" property="deptId"/>
            <result column="stationDeptName" property="deptName" />
        </association>
        <!-- 人员 -->
        <association property="user" javaType="com.ruoyi.system.domain.SysUser">
            <id column="userId" property="userId"/>
            <result column="user_name" property="userName" />
        </association>
        <!-- 集团公司  多对多 -->
        <collection property="group" ofType="com.ruoyi.tms.domain.client.Group" resultMap="GroupResult"/>
        <!-- 客户绑定的客服人员 -->
<!--        <collection property="customerServiceUser" ofType="com.ruoyi.system.domain.SysUser" resultMap="UserResult"/>-->
    </resultMap>
    <!-- 集团公司 -->
    <resultMap type="com.ruoyi.tms.domain.client.Group" id="GroupResult">
        <id property="groupId"    column="group_id"/>
        <result property="delFlag"    column="del_flag"/>
        <result property="delDate"    column="del_date"/>
        <result property="groupCode"    column="group_code"/>
        <result property="groupName"    column="group_name"/>
        <result property="regUserId"    column="reg_user_id"/>
        <result property="regDate"    column="reg_date"/>
        <result property="corUserId"    column="cor_user_id"/>
        <result property="corDate"    column="cor_date"/>
        <result property="groupCustId"    column="GROUP_CUST_ID"/>
    </resultMap>
    <!-- 用户 -->
<!--    <resultMap type="com.ruoyi.tms.dto.SysUserImportDTO" id="UserResult">-->
<!--        <id property="userId"    column="serviceUserId"/>-->
<!--        <result property="userName"    column="serviceUserName"/>-->
<!--        <result property="phonenumber"    column="PHONENUMBER"/>-->
<!--    </resultMap>-->

    <resultMap type="CustPic" id="CustPicResult">
        <result property="custPicId"    column="cust_pic_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="delDate"    column="del_date"    />
        <result property="picType"    column="pic_type"    />
        <result property="appendixId"    column="appendix_id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileId"    column="file_id"    />
    </resultMap>
    <resultMap type="CustBala" id="CustBalaResult">
        <result property="custBalaId"    column="cust_bala_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="delDate"    column="del_date"    />
        <result property="customerId"    column="customer_id"    />
        <result property="relatedCustId"    column="related_cust_id"    />
        <result property="isDefault"    column="is_default"    />
        <result property="custName"    column="cust_name"    />
        <result property="lockedFlag"    column="locked_flag"    />
    </resultMap>
    <resultMap type="CustGoods" id="CustGoodsResult">
        <result property="custGoodsId"    column="cust_goods_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="delDate"    column="del_date"    />
        <result property="customerId"    column="customer_id"    />
        <result property="goodsId"    column="goods_id"    />
        <result property="goodsCode"    column="goods_code"    />
        <result property="goodsName"    column="goods_name"    />
        <result property="goodsType"    column="goods_type"    />
        <result property="goodsTypeName"    column="goods_type_name"    />
        <result property="packId"    column="pack_id"    />
        <result property="unitWeight"    column="unit_weight"    />
        <result property="unitVolume"    column="unit_volume"    />
        <result property="length"    column="length"    />
        <result property="width"    column="width"    />
        <result property="height"    column="height"    />
        <result property="transNote"    column="trans_note"    />
        <result property="regUserId"    column="reg_user_id"    />
        <result property="regDate"    column="reg_date"    />
        <result property="corUserId"    column="cor_user_id"    />
        <result property="corDate"    column="cor_date"    />
    </resultMap>
    <resultMap type="CustTransLine" id="CustTransLineResult">
        <result property="custTranslineId"    column="cust_transline_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="delDate"    column="del_date"    />
        <result property="customerId"    column="customer_id"    />
        <result property="transLineId"    column="trans_line_id"    />
        <result property="lineCode"    column="line_code"    />
        <result property="lineName"    column="line_name"    />
        <result property="startProvince"    column="start_province"    />
        <result property="startCity"    column="start_city"    />
        <result property="startArea"    column="start_area"    />
        <result property="endProvince"    column="end_province"    />
        <result property="endCity"    column="end_city"    />
        <result property="endArea"    column="end_area"    />
        <result property="receiptIntervalDay"    column="receipt_interval_day"    />
        <result property="regUserId"    column="reg_user_id"    />
        <result property="regDate"    column="reg_date"    />
        <result property="corUserId"    column="cor_user_id"    />
        <result property="corDate"    column="cor_date"    />
        <result property="startDetailAddr"    column="startDetailAddr"    />
        <result property="endDetailAddr"    column="endDetailAddr"    />
        <result property="startProvinceName" column="start_province_name"/>
        <result property="startCityName" column="start_city_name"/>
        <result property="startAreaName" column="start_area_name"/>
        <result property="endProvinceName" column="end_province_name"/>
        <result property="endCityName" column="end_city_name"/>
        <result property="endAreaName" column="end_area_name"/>
    </resultMap>
    <resultMap type="Contract" id="ContractResult">
        <result property="contractId"    column="contract_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="delDate"    column="del_date"    />
        <result property="contractno"    column="contractno"    />
        <result property="name"    column="name"    />
        <result property="contractType"    column="contract_type"    />
        <result property="contractStatus"    column="contract_status"    />
        <result property="currency"    column="currency"    />
        <result property="effectiveDate"    column="effective_date"    />
        <result property="invalidDate"    column="invalid_date"    />
        <result property="amount"    column="amount"    />
        <result property="memo"    column="memo"    />
        <result property="appendixId"    column="appendix_id"    />
        <result property="signUserid"    column="sign_userid"    />
        <result property="signDate"    column="sign_date"    />
        <result property="partyaId"    column="partya_id"    />
        <result property="partybId"    column="partyb_id"    />
        <result property="warningDate"    column="warning_date"    />
        <result property="regUserId"    column="reg_user_id"    />
        <result property="regDate"    column="reg_date"    />
        <result property="corUserId"    column="cor_user_id"    />
        <result property="corDate"    column="cor_date"    />
        <result property="tid"    column="tid"    />
        <result property="fileName"    column="file_name"    />
        <result property="filePath"    column="file_path"    />
    </resultMap>
    <resultMap type="com.ruoyi.tms.vo.client.ClientPopupVO" id="ClientPopupVOResult">
        <result property="customerId"    column="customer_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="delDate"    column="del_date"    />
        <result property="custCode"    column="cust_code"    />
        <result property="custName"    column="cust_name"    />
        <result property="custAbbr"    column="cust_abbr"    />
        <result property="custType"    column="cust_type"    />
        <result property="ifAuthentication"    column="if_authentication"    />
        <result property="salesDept"    column="sales_dept"    />
        <result property="balaDept"    column="bala_dept"    />
        <result property="stationDept"    column="station_dept"    />
        <result property="balaCorp"    column="bala_corp"    />
        <result property="psndoc"    column="psndoc"    />
        <result property="psncontact"    column="psncontact"    />
        <result property="provinceId"    column="province_id"    />
        <result property="cityId"    column="city_id"    />
        <result property="areaId"    column="area_id"    />
        <result property="address"    column="address"    />
        <result property="contact"    column="contact"    />
        <result property="contactPost"    column="contact_post"    />
        <result property="phone"    column="phone"    />
        <result property="mobile"    column="mobile"    />
        <result property="email"    column="email"    />
        <result property="fax"    column="fax"    />
        <result property="zipcode"    column="zipcode"    />
        <result property="billingCorp"    column="billing_corp"    />
        <result property="billingDate"    column="billing_date"    />
        <result property="taxIdentify"    column="tax_identify"    />
        <result property="balaType"    column="bala_type"    />
        <result property="billingType"    column="billing_type"    />
        <result property="billingPayable"    column="billing_payable"    />
        <result property="bank"    column="bank"    />
        <result property="discountRate"    column="discount_rate"    />
        <result property="accPeriodAhead"    column="acc_period_ahead"    />
        <result property="billingAhead"    column="billing_ahead"    />
        <result property="creditAmount"    column="credit_amount"    />
        <result property="accountName"    column="account_name"    />
        <result property="bankAccount"    column="bank_account"    />
        <result property="registerAddr"    column="register_addr"    />
        <result property="billingRule"    column="billing_rule"    />
        <result property="legalRepresent"    column="legal_represent"    />
        <result property="registerCapital"    column="register_capital"    />
        <result property="addressee"    column="addressee"    />
        <result property="addresseeContact"    column="addressee_contact"    />
        <result property="addresseeAddr"    column="addressee_addr"    />
        <result property="website"    column="website"    />
        <result property="memo"    column="memo"    />
        <result property="custActivity"    column="cust_activity"    />
        <result property="regUserId"    column="reg_user_id"    />
        <result property="regDate"    column="reg_date"    />
        <result property="corUserId"    column="cor_user_id"    />
        <result property="corDate"    column="cor_date"    />
        <result property="regScrId"    column="reg_scr_id"    />
        <result property="corScrId"    column="cor_scr_id"    />
        <result property="businesslicense"    column="businesslicense"    />
        <result property="stationDeptName"    column="station_dept_name"    />
        <result property="balaDeptName"    column="bala_dept_name"    />
        <result property="salesDeptName"    column="sales_dept_name"    />
        <result property="psndocName"    column="psndoc_name"    />
        <result property="legalCard"    column="legal_card"    />
        <result property="customerType"    column="customer_type"    />
        <result property="paymentDays"    column="payment_days"    />
        <result property="provinceName"    column="province_name"    />
        <result property="cityName"    column="city_name"    />
        <result property="areaName"    column="area_name"    />
        <result property="bankId"    column="bank_id"    />
        <result property="appDeliContact"    column="app_deli_contact"    />
        <result property="appDeliMobile"    column="app_deli_mobile"    />
        <result property="checkStatus"    column="check_status"    />
        <result property="checkMan"    column="check_man"    />
        <result property="checkDate"    column="check_date"    />
        <result property="crtGuidePrice"    column="crt_guide_price"    />
        <result property="customerLevel"    column="customer_level"    />
        <result property="isNeedReceipt"    column="is_need_receipt"    />
        <result property="isNeedTrace"    column="is_need_trace"    />
        <result property="invoiceDays" column="INVOICE_DAYS"/>
        <result property="collectionDays" column="COLLECTION_DAYS"/>
        <result property="isLockOtherFee" column="IS_LOCK_OTHER_FEE"/>
        <result property="adjustment" column="adjustment"/>
        <result property="specialDate" column="special_date"/>
        <result property="customerSource" column="customer_source"/>
        <result property="enterpriseNature" column="enterprise_nature"/>
        <result property="isEnabled" column="IS_ENABLED"/>
        <result property="handlingCharges" column="handling_charges"/>
        <result property="handlingChargesType" column="handling_charges_type"/>
        <result property="enableContractPrice" column="ENABLE_CONTRACT_PRICE"/>
        <result property="platRate" column="PLAT_RATE"/>
        <result property="platTax" column="PLAT_TAX"/>
        <result property="disabledTime" column="disabled_time"/>

        <result property="invalidDate" column="invalid_date"/>
        <result property="salesName" column="sales_name"/>
        <result property="accurateRegion" column="accurate_region"/>
        <result property="accurateArriDetail" column="accurate_arri_detail"/>
        <result property="accurateGoods" column="ACCURATE_GOODS"/>
        <result property="contractPriceFloat" column="contract_price_float"/>
        <result property="floatDateStart" column="float_date_start"/>
        <result property="floatDateEnd" column="float_date_end"/>
        <result property="salesId" column="SALES_ID"/>

        <result property="customerExpCount" column="customer_exp_count"/>
        <result property="referenceRate" column="reference_rate"/>
        <result property="ifBargain" column="if_bargain"/>

        <result property="isSpecialReferencePrice" column="is_special_reference_price"/>

        <result property="contractPriceReview" column="CONTRACT_PRICE_REVIEW"/>
        <result property="contractPriceReviewUserId" column="CONTRACT_PRICE_REVIEW_USER_ID"/>
        <result property="contractPriceReviewUserName" column="CONTRACT_PRICE_REVIEW_USER_NAME"/>
        <result property="contractPriceReviewDate" column="CONTRACT_PRICE_REVIEW_DATE"/>
        <result property="isAutoDispatch" column="IS_AUTO_DISPATCH"/>
        <result property="autoDispatchType" column="auto_dispatch_type"/>
        <result property="autoSubsectionAddrId" column="auto_subsection_addr_id"/>
        <result property="operateCorp" column="operate_corp"/>
        <result property="isSpecialTransfer" column="IS_SPECIAL_TRANSFER"/>
        <result property="autoDispatchDeliFee" column="AUTO_DISPATCH_DELI_FEE"/>
        <result property="autoDispatchPickUpFee" column="AUTO_DISPATCH_PICK_UP_FEE"/>

        <result property="invoiceSalesDeptName" column="invoiceSalesDeptName"/>
        <result property="isCheckLonlat" column="is_check_lonlat"/>
        <result property="contractPriceType" column="CONTRACT_PRICE_TYPE"/>
        <result property="noticeFileId" column="notice_file_id"/>
        <result property="referrer" column="referrer"/>
        <result property="mgmtDeptId" column="mgmt_dept_id"/>
        <result property="mgmtDeptName" column="mgmt_dept_name"/>
        <result property="opsDeptId" column="ops_dept_id"/>
        <result property="opsDeptName" column="ops_dept_name"/>
        <result property="sourceType" column="source_type"/>
        <result property="subjectAmount" column="subject_amount"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="contractNeedType" column="contract_Need_Type"/>

        <result property="settlementCheck" column="settlement_check"/>
        <result property="invContactCheck" column="inv_contact_check"/>
    </resultMap>
    <sql id="selectCustomerVo">
        SELECT
            customer_id,
            del_flag,
            del_date,
            cust_code,
            cust_name,
            cust_abbr,
            cust_type,
            if_authentication,
            sales_dept,
            bala_dept,
            station_dept,
            bala_corp,
            psndoc,
            psncontact,
            province_id,
            city_id,
            area_id,
            address,
            contact,
            contact_post,
            phone,
            mobile,
            email,
            fax,
            zipcode,
            billing_corp,
            billing_date,
            tax_identify,
            bala_type,
            billing_type,
            billing_payable,
            bank,
            discount_rate,
            acc_period_ahead,
            billing_ahead,
            credit_amount,
            account_name,
            bank_account,
            register_addr,
            billing_rule,
            legal_represent,
            register_capital,
            addressee,
            addressee_contact,
            addressee_addr,
            website,
            memo,
            cust_activity,
            reg_user_id,
            reg_date,
            cor_user_id,
            cor_date,
            reg_scr_id,
            cor_scr_id,
            businesslicense,
            psndoc_name,
            payment_days,
            bank_id,
            crt_guide_price,
            customer_level,
            tariff,
            is_need_receipt,
            is_need_trace,
            INVOICE_DAYS,
            COLLECTION_DAYS,
            IS_LOCK_OTHER_FEE,
            ADJUSTMENT,
            SPECIAL_DATE,
            ENTERPRISE_NATURE,
            CUSTOMER_SOURCE,
            APP_DELI_CONTACT,
            APP_DELI_MOBILE,
            operate_corp,
               IS_ENABLED,
            SALES_ID
        FROM
            m_customer
        </sql>

    <sql id="selectClientPopupVo">
        SELECT T.CUSTOMER_ID,
               T.CUST_CODE,
               T.CUST_NAME,
               T.CUST_ABBR,
               T.CUST_TYPE,
               T.SALES_DEPT,
               T.BALA_DEPT,
               T.STATION_DEPT,
               T.BALA_CORP,
               T.PSNDOC,
               T.PSNCONTACT,
               T.PROVINCE_ID,
               T.CITY_ID,
               T.AREA_ID,
               T.ADDRESS,
               T.CONTACT,
               T.CONTACT_POST,
               T.PHONE,
               T.MOBILE,
               T.EMAIL,
               T.FAX,
               T.ZIPCODE,
               T.BILLING_CORP,
               T.BILLING_DATE,
               T.TAX_IDENTIFY,
               T.BALA_TYPE,
               T.BILLING_TYPE,
               T.BILLING_PAYABLE,
               T.BANK,
               T.DISCOUNT_RATE,
               T.ACC_PERIOD_AHEAD,
               T.BILLING_AHEAD,
               T.CREDIT_AMOUNT,
               T.ACCOUNT_NAME,
               T.BANK_ACCOUNT,
               T.REGISTER_ADDR,
               T.BILLING_RULE,
               T.LEGAL_REPRESENT,
               T.REGISTER_CAPITAL,
               T.ADDRESSEE,
               T.ADDRESSEE_CONTACT,
               T.ADDRESSEE_ADDR,
               T.WEBSITE,
               T.MEMO,
               T.REG_DATE,
               T.COR_DATE,
               T.BUSINESSLICENSE,
               T.PSNDOC_NAME,
               T.LEGAL_CARD,
               T.CUSTOMER_TYPE,
               T.PAYMENT_DAYS,
               T.PROVINCE_NAME,
               T.CITY_NAME,
               T.AREA_NAME,
               T.BANK_ID,
               T.APP_DELI_CONTACT,
               T.APP_DELI_MOBILE,
               T.CHECK_STATUS,
               T.CHECK_DATE,
               T.CHECK_MAN,
               t.crt_guide_price,
               t.customer_level,
               t.tariff,
               t.is_need_receipt,
               t.is_need_trace,
               t.adjustment,
               T.IS_ENABLED,
               t.disabled_time,
               T1.DEPT_NAME sales_dept_name,
               T2.DEPT_NAME bala_dept_name,
               T3.DEPT_NAME station_dept_name,
               sys_user.user_name cor_user_id,
               user2.user_name reg_user_id,
               t.INVOICE_DAYS,
               t.COLLECTION_DAYS,
               t.IS_LOCK_OTHER_FEE,
               t.special_date,
               t.ENTERPRISE_NATURE,
               t.CUSTOMER_SOURCE,
               t.handling_charges,
                t.handling_charges_type,
                t.ENABLE_CONTRACT_PRICE,
                t.PLAT_RATE,
                t.PLAT_TAX,
                t.accurate_region,
                t.accurate_arri_detail,
                t.ACCURATE_GOODS,
                t.contract_price_float,
                t.FLOAT_DATE_START,
                t.FLOAT_DATE_END,
                t.SALES_ID,
                t.reference_rate,
                t.if_bargain,
                t.is_special_reference_price,
                t.CONTRACT_PRICE_REVIEW,
                t.CONTRACT_PRICE_REVIEW_USER_ID,
                t.CONTRACT_PRICE_REVIEW_USER_NAME,
                t.CONTRACT_PRICE_REVIEW_DATE,
                t.IS_AUTO_DISPATCH,
                t.auto_dispatch_type,
                t.auto_subsection_addr_id,
               t.operate_corp,
                t.IS_SPECIAL_TRANSFER,
                t.AUTO_DISPATCH_DELI_FEE,
                t.AUTO_DISPATCH_PICK_UP_FEE,
               t.is_check_lonlat,
                t.CONTRACT_PRICE_TYPE,
                t.notice_file_id,
                t.referrer,
                t.source_type,
                t.subject_amount,
               t.contract_Need_Type,
               t.settlement_check,
                t.inv_contact_check
          FROM M_CUSTOMER T
          LEFT JOIN SYS_DEPT T1
            ON T.SALES_DEPT = T1.DEPT_ID and  T1.DEL_FLAG = 0
          LEFT JOIN SYS_DEPT T2
            ON T.BALA_DEPT = T2.DEPT_ID and T2.DEL_FLAG = 0
               LEFT JOIN SYS_DEPT T3
            ON T.STATION_DEPT = T3.DEPT_ID and T3.DEL_FLAG = 0
          left join sys_user on sys_user.USER_ID = t.cor_user_id and sys_user.DEL_FLAG = 0
          left join sys_user user2 on user2.USER_ID = t.reg_user_id and user2.del_flag = 0
    </sql>

    <!--插入一条合同信息-->
    <insert id="insertContract" >
        insert into m_contract
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contractId != null  and contractId != ''  ">contract_id,</if>
            <if test="delFlag != null  ">del_flag,</if>
            <if test="delDate != null  ">del_date,</if>
            <if test="contractno != null  and contractno != ''  ">contractno,</if>
            <if test="name != null  and name != ''  ">name,</if>
            <if test="contractType != null  ">contract_type,</if>
            <if test="contractStatus != null  ">contract_status,</if>
            <if test="currency != null  and currency != ''  ">currency,</if>
            <if test="effectiveDate != null  ">effective_date,</if>
            <if test="invalidDate != null  ">invalid_date,</if>
            <if test="amount != null  ">amount,</if>
            <if test="memo != null  and memo != ''  ">memo,</if>
            <if test="appendixId != null  and appendixId != ''  ">appendix_id,</if>
            <if test="signUserid != null  and signUserid != ''  ">sign_userid,</if>
            <if test="signDate != null  ">sign_date,</if>
            <if test="partyaId != null  and partyaId != ''  ">partya_id,</if>
            <if test="partybId != null  and partybId != ''  ">partyb_id,</if>
            <if test="lockedFlag != null  and lockedFlag != ''  ">locked_flag,</if>
            <if test="warningDate != null  ">warning_date,</if>
            <if test="regUserId != null  and regUserId != ''  ">reg_user_id,</if>
            <if test="regDate != null  ">reg_date,</if>
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id,</if>
            <if test="corDate != null  ">cor_date,</if>
            <if test="delUserId != null  and delUserId != ''  ">del_user_id,</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id,</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contractId != null  and contractId != ''  ">#{contractId},</if>
            <if test="delFlag != null  ">#{delFlag},</if>
            <if test="delDate != null  ">#{delDate},</if>
            <if test="contractno != null  and contractno != ''  ">#{contractno},</if>
            <if test="name != null  and name != ''  ">#{name},</if>
            <if test="contractType != null  ">#{contractType},</if>
            <if test="contractStatus != null  ">#{contractStatus},</if>
            <if test="currency != null  and currency != ''  ">#{currency},</if>
            <if test="effectiveDate != null  ">#{effectiveDate},</if>
            <if test="invalidDate != null  ">#{invalidDate},</if>
            <if test="amount != null  ">#{amount},</if>
            <if test="memo != null  and memo != ''  ">#{memo},</if>
            <if test="appendixId != null  and appendixId != ''  ">#{appendixId},</if>
            <if test="signUserid != null  and signUserid != ''  ">#{signUserid},</if>
            <if test="signDate != null  ">#{signDate},</if>
            <if test="partyaId != null  and partyaId != ''  ">#{partyaId},</if>
            <if test="partybId != null  and partybId != ''  ">#{partybId},</if>
            <if test="lockedFlag != null  and lockedFlag != ''  ">#{lockedFlag},</if>
            <if test="warningDate != null  ">#{warningDate},</if>
            <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="corUserId != null  and corUserId != ''  ">#{corUserId},</if>
            <if test="corDate != null  ">#{corDate},</if>
            <if test="delUserId != null  and delUserId != ''  ">#{delUserId},</if>
            <if test="regScrId != null  and regScrId != ''  ">#{regScrId},</if>
            <if test="corScrId != null  and corScrId != ''  ">#{corScrId},</if>
        </trim>

    </insert>

    <!--插入客户基本信息-->
    <insert id="insertClient">
        insert into m_customer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null  and customerId != ''  ">customer_id,</if>
            <if test="delFlag != null  ">del_flag,</if>
            <if test="delDate != null  ">del_date,</if>
            <if test="custCode != null  and custCode != ''  ">cust_code,</if>
            <if test="custName != null  and custName != ''  ">cust_name,</if>
            <if test="custAbbr != null  and custAbbr != ''  ">cust_abbr,</if>
            <if test="custType != null  ">cust_type,</if>
            <if test="ifAuthentication != null  and ifAuthentication != ''  ">if_authentication,</if>
            <if test="salesDept != null  and salesDept != ''  ">sales_dept,</if>
            <if test="balaDept != null  and balaDept != ''  ">bala_dept,</if>
            <if test="stationDept != null  and stationDept != ''  ">station_dept,</if>
            <if test="balaCorp != null  and balaCorp != ''  ">bala_corp,</if>
            <if test="psndoc != null  and psndoc != ''  ">psndoc,</if>
            <if test="psncontact != null  and psncontact != ''  ">psncontact,</if>
            <if test="provinceId != null  and provinceId != ''  ">province_id,</if>
            <if test="cityId != null  and cityId != ''  ">city_id,</if>
            <if test="areaId != null  and areaId != ''  ">area_id,</if>
            <if test="address != null  and address != ''  ">address,</if>
            <if test="contact != null  and contact != ''  ">contact,</if>
            <if test="contactPost != null  and contactPost != ''  ">contact_post,</if>
            <if test="phone != null  and phone != ''  ">phone,</if>
            <if test="mobile != null  and mobile != ''  ">mobile,</if>
            <if test="email != null  and email != ''  ">email,</if>
            <if test="fax != null  and fax != ''  ">fax,</if>
            <if test="zipcode != null  and zipcode != ''  ">zipcode,</if>
            <if test="billingCorp != null  and billingCorp != ''  ">billing_corp,</if>
            <if test="billingDate != null  ">billing_date,</if>
            <if test="taxIdentify != null  and taxIdentify != ''  ">tax_identify,</if>
            <if test="balaType != null  and balaType != ''  ">bala_type,</if>
            <if test="billingType != null  and billingType != ''  ">billing_type,</if>
            <if test="billingPayable != null  and billingPayable != ''  ">billing_payable,</if>
            <if test="bank != null  and bank != ''  ">bank,</if>
            <if test="discountRate != null  ">discount_rate,</if>
            <if test="accPeriodAhead != null  ">acc_period_ahead,</if>
            <if test="billingAhead != null  ">billing_ahead,</if>
            <if test="creditAmount != null  ">credit_amount,</if>
            <if test="accountName != null  and accountName != ''  ">account_name,</if>
            <if test="bankAccount != null  and bankAccount != ''  ">bank_account,</if>
            <if test="registerAddr != null  and registerAddr != ''  ">register_addr,</if>
            <if test="billingRule != null  ">billing_rule,</if>
            <if test="legalRepresent != null  and legalRepresent != ''  ">legal_represent,</if>
            <if test="registerCapital != null  ">register_capital,</if>
            <if test="addressee != null  and addressee != ''  ">addressee,</if>
            <if test="addresseeContact != null  and addresseeContact != ''  ">addressee_contact,</if>
            <if test="addresseeAddr != null  and addresseeAddr != ''  ">addressee_addr,</if>
            <if test="website != null  and website != ''  ">website,</if>
            <if test="memo != null  and memo != ''  ">memo,</if>
            <if test="custActivity != null  ">cust_activity,</if>
            <if test="regUserId != null  and regUserId != ''  ">reg_user_id,</if>
            <if test="regDate != null  ">reg_date,</if>
            <if test="regUserId != null  and regUserId != ''  ">cor_user_id,</if>
            <if test="regDate != null  ">cor_date,</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id,</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id,</if>
            <if test="businesslicense != null  and businesslicense != ''  ">businesslicense,</if>
            <if test="psndocName != null  and psndocName != ''  ">psndoc_name,</if>
            <if test="legalCard != null  and legalCard != ''  ">legal_card,</if>
            <if test="customerType != null  and customerType != ''  ">customer_type,</if>
            <if test="paymentDays != null  and paymentDays != ''  ">payment_days,</if>
            <if test="provinceName != null  and provinceName != ''  ">province_name,</if>
            <if test="cityName != null  and cityName != ''  ">city_name,</if>
            <if test="areaName != null  and areaName != ''  ">area_name,</if>
            <if test="delUserId != null  and delUserId != ''  ">del_user_id,</if>
            <if test="bankId != null ">bank_id,</if>
            <if test="appDeliContact != null  and appDeliContact != ''  ">app_deli_contact,</if>
            <if test="appDeliMobile != null  and appDeliMobile != ''  ">app_deli_mobile,</if>
            <if test="crtGuidePrice != null">crt_guide_price,</if>
            <if test="customerLevel != null ">customer_level,</if>
            <if test="tariff != null ">tariff,</if>
            <if test="isNeedReceipt != null ">IS_NEED_RECEIPT,</if>
            <if test="isNeedTrace != null ">IS_NEED_TRACE,</if>
            <if test="invoiceDays != null ">INVOICE_DAYS,</if>
            <if test="collectionDays != null ">COLLECTION_DAYS,</if>
            <if test="adjustment != null ">adjustment,</if>
            <if test="checkStatus != null ">check_status,</if>
            <if test="specialDate != null and specialDate != ''">special_date,</if>
            <if test="enterpriseNature != null and enterpriseNature != ''">ENTERPRISE_NATURE,</if>
            <if test="customerSource != null and customerSource != ''">CUSTOMER_SOURCE,</if>
            <if test="isEnabled != null and isEnabled != '' or isEnabled== 0">IS_ENABLED,</if>
            <if test="handlingCharges != null">handling_charges,</if>
            <if test="handlingChargesType != null and handlingChargesType != ''">handling_charges_type,</if>
            <if test="enableContractPrice != null and enableContractPrice != ''">ENABLE_CONTRACT_PRICE,</if>
            <if test="salesId != null and salesId != ''">SALES_ID,</if>
            <if test="referenceRate != null">reference_rate,</if>
            <if test="operateCorp != null and operateCorp != ''">operate_corp,</if>
            <if test="sourceType != null ">source_type,</if>
            <if test="contractPriceType != null ">contract_price_type,</if>
            <if test="noticeFileId != null ">notice_file_id,</if>
            <if test="referrer != null and referrer != ''">referrer,</if>
            <if test="ifBargain != null and ifBargain != ''">if_bargain,</if>
            <if test="subjectAmount != null">subject_amount,</if>
            <if test="contractNeedType != null">contract_Need_Type,</if>
            <if test="settlementCheck != null">settlement_check,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null  and customerId != ''  ">#{customerId},</if>
            <if test="delFlag != null  ">#{delFlag},</if>
            <if test="delDate != null  ">#{delDate},</if>
            <if test="custCode != null  and custCode != ''  ">#{custCode},</if>
            <if test="custName != null  and custName != ''  ">#{custName},</if>
            <if test="custAbbr != null  and custAbbr != ''  ">#{custAbbr},</if>
            <if test="custType != null  ">#{custType},</if>
            <if test="ifAuthentication != null  and ifAuthentication != ''  ">#{ifAuthentication},</if>
            <if test="salesDept != null  and salesDept != ''  ">#{salesDept},</if>
            <if test="balaDept != null  and balaDept != ''  ">#{balaDept},</if>
            <if test="stationDept != null  and stationDept != ''  ">#{stationDept},</if>
            <if test="balaCorp != null  and balaCorp != ''  ">#{balaCorp},</if>
            <if test="corpId != null  and corpId != ''  ">#{corpId},</if>
            <if test="psndoc != null  and psndoc != ''  ">#{psndoc},</if>
            <if test="psncontact != null  and psncontact != ''  ">#{psncontact},</if>
            <if test="provinceId != null  and provinceId != ''  ">#{provinceId},</if>
            <if test="cityId != null  and cityId != ''  ">#{cityId},</if>
            <if test="areaId != null  and areaId != ''  ">#{areaId},</if>
            <if test="address != null  and address != ''  ">#{address},</if>
            <if test="contact != null  and contact != ''  ">#{contact},</if>
            <if test="contactPost != null  and contactPost != ''  ">#{contactPost},</if>
            <if test="phone != null  and phone != ''  ">#{phone},</if>
            <if test="mobile != null  and mobile != ''  ">#{mobile},</if>
            <if test="email != null  and email != ''  ">#{email},</if>
            <if test="fax != null  and fax != ''  ">#{fax},</if>
            <if test="zipcode != null  and zipcode != ''  ">#{zipcode},</if>
            <if test="billingCorp != null  and billingCorp != ''  ">#{billingCorp},</if>
            <if test="billingDate != null  ">#{billingDate},</if>
            <if test="taxIdentify != null  and taxIdentify != ''  ">#{taxIdentify},</if>
            <if test="balaType != null  and balaType != ''  ">#{balaType},</if>
            <if test="billingType != null  and billingType != ''  ">#{billingType},</if>
            <if test="billingPayable != null  and billingPayable != ''  ">#{billingPayable},</if>
            <if test="bank != null  and bank != ''  ">#{bank},</if>
            <if test="discountRate != null  ">#{discountRate},</if>
            <if test="accPeriodAhead != null  ">#{accPeriodAhead},</if>
            <if test="billingAhead != null  ">#{billingAhead},</if>
            <if test="creditAmount != null  ">#{creditAmount},</if>
            <if test="accountName != null  and accountName != ''  ">#{accountName},</if>
            <if test="bankAccount != null  and bankAccount != ''  ">#{bankAccount},</if>
            <if test="registerAddr != null  and registerAddr != ''  ">#{registerAddr},</if>
            <if test="billingRule != null  ">#{billingRule},</if>
            <if test="legalRepresent != null  and legalRepresent != ''  ">#{legalRepresent},</if>
            <if test="registerCapital != null  ">#{registerCapital},</if>
            <if test="addressee != null  and addressee != ''  ">#{addressee},</if>
            <if test="addresseeContact != null  and addresseeContact != ''  ">#{addresseeContact},</if>
            <if test="addresseeAddr != null  and addresseeAddr != ''  ">#{addresseeAddr},</if>
            <if test="website != null  and website != ''  ">#{website},</if>
            <if test="lockedFlag != null  and lockedFlag != ''  ">#{lockedFlag},</if>
            <if test="memo != null  and memo != ''  ">#{memo},</if>
            <if test="custActivity != null  ">#{custActivity},</if>
            <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="regScrId != null  and regScrId != ''  ">#{regScrId},</if>
            <if test="corScrId != null  and corScrId != ''  ">#{corScrId},</if>
            <if test="businesslicense != null  and businesslicense != ''  ">#{businesslicense},</if>
            <if test="psndocName != null  and psndocName != ''  ">#{psndocName},</if>
            <if test="legalCard != null  and legalCard != ''  ">#{legalCard},</if>
            <if test="customerType != null  and customerType != ''  ">#{customerType},</if>
            <if test="paymentDays != null  and paymentDays != ''  ">#{paymentDays},</if>
            <if test="provinceName != null  and provinceName != ''  ">#{provinceName},</if>
            <if test="cityName != null  and cityName != ''  ">#{cityName},</if>
            <if test="areaName != null  and areaName != ''  ">#{areaName},</if>
            <if test="delUserId != null  and delUserId != ''  ">#{delUserId},</if>
            <if test="bankId != null ">#{bankId},</if>
            <if test="appDeliContact != null  and appDeliContact != ''  ">#{appDeliContact},</if>
            <if test="appDeliMobile != null  and appDeliMobile != ''  ">#{appDeliMobile},</if>
            <if test="crtGuidePrice != null ">#{crtGuidePrice},</if>
            <if test="customerLevel != null ">#{customerLevel},</if>
            <if test="tariff != null ">#{tariff},</if>
            <if test="isNeedReceipt != null ">#{isNeedReceipt},</if>
            <if test="isNeedTrace != null ">#{isNeedTrace},</if>
            <if test="invoiceDays != null ">#{invoiceDays},</if>
            <if test="collectionDays != null ">#{collectionDays},</if>
            <if test="adjustment != null ">#{adjustment},</if>
            <if test="checkStatus != null ">#{checkStatus},</if>
            <if test="specialDate != null and specialDate != ''">#{specialDate},</if>
            <if test="enterpriseNature != null and enterpriseNature != ''">#{enterpriseNature},</if>
            <if test="customerSource != null and customerSource != ''">#{customerSource},</if>
            <if test="isEnabled != null and isEnabled != '' or isEnabled == 0">#{isEnabled},</if>
            <if test="handlingCharges != null">#{handlingCharges},</if>
            <if test="handlingChargesType != null and handlingChargesType != ''">#{handlingChargesType},</if>
            <if test="enableContractPrice != null and enableContractPrice != ''">#{enableContractPrice},</if>
            <if test="salesId != null and salesId != ''">#{salesId},</if>
            <if test="referenceRate != null">#{referenceRate},</if>
            <if test="operateCorp != null and operateCorp != ''">#{operateCorp},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="contractPriceType != null">#{contractPriceType},</if>
            <if test="noticeFileId != null">#{noticeFileId},</if>
            <if test="referrer != null and referrer != ''">#{referrer},</if>
            <if test="ifBargain != null and ifBargain != ''">#{ifBargain},</if>
            <if test="subjectAmount != null">#{subjectAmount},</if>
            <if test="contractNeedType != null">#{contractNeedType},</if>
            <if test="settlementCheck != null">#{settlementCheck},</if>

        </trim>
    </insert>

    <!--导出查询客户列表  -->
    <select id="selectClientList" parameterType="map" resultMap="ClientResult">
        SELECT
        t.*,
        t3.group_id AS gid,
        t3.group_name,
        t4.DEPT_ID AS saleId,
        t4.DEPT_NAME AS saleDeptName,
        t5.DEPT_ID AS stationId,
        t5.DEPT_NAME AS stationDeptName,
        t6.USER_ID AS userId,
        t6.USER_NAME,
        t8.CUSTOMER_ID AS related_cust_id,
        t8.CUST_NAME AS related_cust_name,

        temp.serviceUserId,
        temp.serviceUserName,
        temp.PHONENUMBER,
<!--        t11.SALES_NAME,-->
        t12.invoiceSalesDeptName,

        t13.DEPT_ID      ops_dept_id,
        t13.DEPT_NAME    ops_dept_name,
        t14.DEPT_ID      mgmt_dept_id,
        t14.DEPT_NAME    mgmt_dept_name,
        (
        select

        case
        when count(1) = 0 then null
        when max(bus.LONG_TERM_EFFECTIVE) = '1' then '长期有效'
        else
        case
        when max(bus.end_date) is null and max(bus.EXTENSION_DATE) is null and max(bus.SYSTEM_EXTENSION_DATE) is null then '有合同无有效期'
        else to_char(
        greatest(
        COALESCE(max(bus.end_date), DATE '0001-01-01'),
        COALESCE(max(bus.EXTENSION_DATE), DATE '0001-01-01'),
        COALESCE(max(bus.SYSTEM_EXTENSION_DATE), DATE '0001-01-01')
        ),
        'yyyy-mm-dd'
        )
        end
        end invalid_date
        from M_CONTRACT_BUSINESS bus
        left join M_CONTRACT_BUSINESS_CUST busCust on bus.ID=busCust.CONTRACT_BUSINESS_ID and busCust.DEL_FLAG=0
        where bus.del_flag = 0 and busCust.CUSTOMER_ID = t.customer_id
        ) invalid_date
        -- t10.user_id AS serviceUserId,
        -- t10.user_name AS serviceUserName,
        -- t10.PHONENUMBER
        FROM
        M_CUSTOMER t
        LEFT JOIN M_GROUP_CUST t2 ON t2.CUSTOMER_ID = t.CUSTOMER_ID
        LEFT JOIN M_GROUP t3 ON t3.GROUP_ID = t2.GROUP_ID
        LEFT JOIN SYS_DEPT t4 ON t4.DEPT_ID = t.SALES_DEPT AND t4.DEL_FLAG = 0
        LEFT JOIN SYS_DEPT t5 ON t5.DEPT_ID = t.STATION_DEPT AND t5.DEL_FLAG = 0
        LEFT JOIN SYS_USER t6 ON t6.user_id = t.REG_USER_ID AND t6.DEL_FLAG = 0
        LEFT JOIN M_CUST_BALA t7 ON t7.CUSTOMER_ID = t.CUSTOMER_ID AND t7.DEL_FLAG = 0
        LEFT JOIN M_CUSTOMER t8 ON t8.CUSTOMER_ID = t7.RELATED_CUST_ID AND t8.DEL_FLAG = 0
<!--        left join M_SALES_GROUP t11 on  t.SALES_ID = t11.id and  t11.del_flag = 0-->

        LEFT JOIN SYS_DEPT T13  ON T4.PARENT_ID = T13.DEPT_ID     <!--运营部-->
        LEFT JOIN SYS_DEPT T14 ON T13.PARENT_ID = T14.DEPT_ID      <!--管理部-->

        LEFT JOIN
        (
            SELECT
                t9.CUSTOMER_ID,
                t10.user_id AS serviceUserId,
                t10.user_name AS serviceUserName,
                t10.PHONENUMBER
            FROM M_CUSTOMER_SERVICE t9
            LEFT JOIN SYS_USER t10 ON t9.SERVICE_ID = t10.user_id
            WHERE t9.del_flag = 0  AND t10.del_flag = 0
        )  temp ON temp.CUSTOMER_ID = t.CUSTOMER_ID AND temp.serviceUserId IS NOT NULL
        left join (
               select invoice.customer_id customer_id,
                      listagg(distinct dept.DEPT_NAME, ',') WITHIN GROUP(ORDER BY dept.DEPT_NAME) invoiceSalesDeptName
                 from T_RECEIVE_DETAIL receive
            left join t_invoice invoice on invoice.invoice_id = receive.invoice_id
            left join sys_dept dept on dept.DEPT_ID = invoice.SALES_DEPT
                where receive.DEL_FLAG = 0
                  and receive.IS_NTOCC = 0
                  and receive.VBILLSTATUS != 4
                  and receive.UNGOT_AMOUNT != 0
                group by invoice.customer_id
        ) t12 on t12.customer_id = t.customer_id

        where
        t.DEL_FLAG = 0
        <if test="customerId != null  and customerId != '' "> and t.customer_id = #{customerId}</if>
        <if test="delDate != null "> and t.del_date = #{delDate}</if>
        <if test="custCode != null  and custCode != '' "> and t.cust_code = #{custCode}</if>
        <if test="custName != null and custName.trim() != ''">
            <bind name="custName" value="custName + '%'"/>
            and (t.CUST_NAME like #{custName} or t.cust_abbr like #{custName})
        </if>
        <if test="psndocName != null and psndocName.trim() != ''">
            <bind name="psndocName" value="psndocName + '%'"/>
            and t.PSNDOC_NAME like #{psndocName}
        </if>
        <if test="custAbbr != null  and custAbbr != '' ">
            <bind name="custAbbr" value="custAbbr + '%'"/>
            and t.cust_abbr like #{custAbbr}
        </if>
        <if test="custType != null "> and t.cust_type = #{custType}</if>
        <if test="ifAuthentication != null  and ifAuthentication != '' "> and t.if_authentication = #{ifAuthentication}</if>
        <if test="salesDept != null  and salesDept != '' "> and t.sales_dept = #{salesDept}</if>
        <if test="balaDept != null  and balaDept != '' "> and t.bala_dept = #{balaDept}</if>
        <if test="stationDept != null  and stationDept != '' "> and t.station_dept = #{stationDept}</if>
        <if test="balaCorp != null  and balaCorp != '' "> and t.bala_corp = #{balaCorp}</if>
        <if test="psndoc != null  and psndoc != '' ">
            <bind name="psndoc" value="psndoc + '%'"/>
            and t.psndoc like #{psndoc}
        </if>
        <if test="psncontact != null  and psncontact != '' "> and t.psncontact = #{psncontact}</if>
        <if test="provinceId != null  and provinceId != '' "> and t.province_id = #{provinceId}</if>
        <if test="cityId != null  and cityId != '' "> and t.city_id = #{cityId}</if>
        <if test="areaId != null  and areaId != '' "> and t.area_id = #{areaId}</if>
        <if test="address != null  and address != '' ">
            <bind name="address" value="address + '%'"/>
            and t.address like #{address}
        </if>
        <if test="contact != null  and contact != '' ">
            <bind name="contact" value="contact + '%'"/>
            and t.contact like #{contact}
        </if>
        <if test="contactPost != null  and contactPost != '' "> and t.contact_post = #{contactPost}</if>
        <if test="phone != null  and phone != '' ">
            <bind name="phone" value="phone + '%'"/>
            and t.phone like #{phone}
        </if>
        <if test="mobile != null  and mobile != '' "> and t.mobile = #{mobile}</if>
        <if test="email != null  and email != '' "> and t.email = #{email}</if>
        <if test="fax != null  and fax != '' "> and t.fax = #{fax}</if>
        <if test="zipcode != null  and zipcode != '' "> and t.zipcode = #{zipcode}</if>
        <if test="billingCorp != null  and billingCorp != '' "> and t.billing_corp = #{billingCorp}</if>
        <if test="billingDate != null "> and t.billing_date = #{billingDate}</if>
        <if test="taxIdentify != null  and taxIdentify != '' "> and t.tax_identify = #{taxIdentify}</if>
        <if test="balaType != null  and balaType != '' "> and t.bala_type = #{balaType}</if>
        <if test="billingType != null  and billingType != '' "> and t.billing_type = #{billingType}</if>
        <if test="billingPayable != null  and billingPayable != '' "> and t.billing_payable = #{billingPayable}</if>
        <if test="bank != null  and bank != '' "> and t.bank = #{bank}</if>
        <if test="discountRate != null "> and t.discount_rate = #{discountRate}</if>
        <if test="accPeriodAhead != null "> and t.acc_period_ahead = #{accPeriodAhead}</if>
        <if test="billingAhead != null "> and t.billing_ahead = #{billingAhead}</if>
        <if test="creditAmount != null "> and t.credit_amount = #{creditAmount}</if>
        <if test="accountName != null  and accountName != '' "> and t.account_name = #{accountName}</if>
        <if test="bankAccount != null  and bankAccount != '' "> and t.bank_account = #{bankAccount}</if>
        <if test="registerAddr != null  and registerAddr != '' "> and t.register_addr = #{registerAddr}</if>
        <if test="billingRule != null "> and t.billing_rule = #{billingRule}</if>
        <if test="legalRepresent != null  and legalRepresent != '' "> and t.legal_represent = #{legalRepresent}</if>
        <if test="registerCapital != null "> and t.register_capital = #{registerCapital}</if>
        <if test="addressee != null  and addressee != '' "> and t.addressee = #{addressee}</if>
        <if test="addresseeContact != null  and addresseeContact != '' "> and t.addressee_contact = #{addresseeContact}</if>
        <if test="addresseeAddr != null  and addresseeAddr != '' "> and t.addressee_addr = #{addresseeAddr}</if>
        <if test="website != null  and website != '' "> and t.website = #{website}</if>
        <if test="memo != null  and memo != '' "> and t.memo = #{memo}</if>
        <if test="custActivity != null "> and t.cust_activity = #{custActivity}</if>
        <if test="regUserId != null  and regUserId != '' "> and t.reg_user_id = #{regUserId}</if>
        <if test="regScrId != null  and regScrId != '' "> and t.reg_scr_id = #{regScrId}</if>
        <if test="corScrId != null  and corScrId != '' "> and t.cor_scr_id = #{corScrId}</if>
        <if test="businesslicense != null  and businesslicense != '' "> and t.businesslicense = #{businesslicense}</if>
        <if test="ifBargain != null "> and t.if_Bargain = #{ifBargain}</if>
        <if test="regDate != null">
            AND t.REG_DATE <![CDATA[   >=  ]]> #{regDate}
        </if>
        <if test="corDate != null">
            AND t.REG_DATE <![CDATA[   <=  ]]> #{corDate}
        </if>
        <!--查询修改人-->
        <if test="corUserId != null and corUserId.trim() != ''">
            <bind name="corUserId" value="corUserId + '%'"/>
            and user2.user_name like #{corUserId}
        </if>
        <if test="checkStatus != null "> and t.check_status = #{checkStatus}</if>
        <!--客户级别-->
        <if test="customerLevel != null "> and t.customer_level = #{customerLevel}</if>
        <!-- 客户来源 -->
        <if test="customerSource != null and  customerSource != ''"> and t.customer_source = #{customerSource}</if>

        <if test="sourceType != null"> and t.source_type = #{sourceType}</if>

<!--        <if test="clientType != null and clientType =='all' ">-->
<!--            and t.REG_SCR_ID != 'importAllData' and NVL(t.SALES_ID, ' ') != '2a828327651f4e1cbbc798d8c56ec99c'-->
<!--        </if>-->
<!--        <if test="clientType != null and clientType =='fleet' ">-->
<!--            and t.SALES_ID = '2a828327651f4e1cbbc798d8c56ec99c'-->
<!--        </if>-->
<!--        <if test="clientType != null and clientType =='park' ">-->
<!--            and t.REG_SCR_ID = 'importAllData'-->
<!--        </if>-->

        <if test="isEnabled != null and isEnabled != '' or isEnabled == 0">and t.is_enabled = #{isEnabled}</if>

        <if test="mgmtDeptId != null and mgmtDeptId != ''">
            and exists (
            select 1 from sys_dept a
            where a.del_flag = 0
            and (a.dept_id = #{mgmtDeptId} or ',' || a.ancestors || ',' LIKE '%,' || #{mgmtDeptId} || ',%')
            and a.dept_id = t.sales_dept
            )
        </if>
        <if test="opsDeptId != null and opsDeptId != ''">
            and exists (
            select 1 from sys_dept a
            where a.del_flag = 0
            and (a.dept_id = #{opsDeptId} or ',' || a.ancestors || ',' LIKE '%,' || #{opsDeptId} || ',%')
            and a.dept_id = t.sales_dept
            )
        </if>
        <if test="isExistContract  == 0"> and exists (
            select
            bus.ID
            from
            M_CONTRACT_BUSINESS bus
            left join M_CONTRACT_BUSINESS_CUST busCust on bus.ID=busCust.CONTRACT_BUSINESS_ID and busCust.DEL_FLAG=0
            where bus.del_flag = 0
            and busCust.CUSTOMER_ID = t.customer_id
            )</if>
        <if test="isExistContract  == 1"> and not exists (
            select
            bus.ID
            from
            M_CONTRACT_BUSINESS bus
            left join M_CONTRACT_BUSINESS_CUST busCust on bus.ID=busCust.CONTRACT_BUSINESS_ID and busCust.DEL_FLAG=0
            where bus.del_flag = 0
            and busCust.CUSTOMER_ID = t.customer_id
            )</if>


        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by t.COR_DATE desc, t.REG_DATE desc, t.CUST_NAME DESC
    </select>

    <select id="selectClientByCustAbbr" resultMap="ClientResult">
        select
        customer_id,
        cust_abbr
        from m_customer
        where del_flag = 0
        <if test="custAbbr != null  and custAbbr != '' ">
            <bind name="custAbbr" value="custAbbr + '%'"/>
            and cust_abbr like #{custAbbr}
        </if>
    </select>

    <!--保存客户照片信息-->
    <insert id="insertClientPic" parameterType="map">
        insert into M_CUST_PIC(
            CUST_PIC_ID,
        <if test="fileType != null and fileType.trim() != '' ">PIC_TYPE,</if>
            APPENDIX_ID,
            CUSTOMER_ID
        )values (
            #{picId},
        <if test="fileType != null and fileType.trim() != '' ">#{fileType},</if>
            #{tid},
            #{clientId}
        )
    </insert>

    <!--保存客户集团信息-->
    <insert id="insertClientGroup" >
        insert into M_GROUP_CUST(
          GROUP_CUST_ID,
          GROUP_ID,
          CUSTOMER_ID,
          DEL_FLAG
        ) values (
           #{groupCustId},
           #{groupId},
           #{customerId},
           #{delFlag}
        )
    </insert>

    <!--修改客户集团信息-->
    <update id="updateClientGroup" >
       update M_GROUP_CUST set
           GROUP_ID = #{groupId}
        where
            CUSTOMER_ID = #{customerId}
    </update>

    <!--保存客户合同信息-->
    <insert id="insertClientContract" >
        insert into M_CUST_CONTRACT(
        CUST_CONTRACT_ID,
        CONTRACT_ID,
        BALA_CUST_ID,
        DEL_FLAG
        )VALUES (
        #{custContractId},
        #{contractId},
        #{balaCustId},
        #{delFlag}
        )
    </insert>

    <!--根据客户Id查询客户信息-->
    <select id="selectClientById" resultMap="ClientPopupVOResult">
        <include refid="selectClientPopupVo"/>

	      where t.CUSTOMER_ID = #{clientId}
    </select>

    <!--根据客户Id查询结算客户-->
    <select id="selectRelatedClientById" resultMap="CustBalaResult">
          SELECT
            m.CUST_NAME,
            mcb.RELATED_CUST_ID,
            mcb.CUST_BALA_ID,
            mcb.IS_DEFAULT,
            mcb.LOCKED_FLAG
          FROM
            M_CUSTOMER m
              LEFT JOIN (
              SELECT
                RELATED_CUST_ID,
                CUST_BALA_ID,
                CUSTOMER_ID,
                DEL_FLAG,
                IS_DEFAULT,
                LOCKED_FLAG
              FROM
                M_CUST_BALA
              WHERE
                  CUSTOMER_ID = #{custBalaId}
            ) mcb ON m.CUSTOMER_ID = mcb.RELATED_CUST_ID
          WHERE
              m.CUSTOMER_ID = mcb.RELATED_CUST_ID
              and mcb.DEL_FLAG = 0
    </select>

    <!--根据客户Id查询客户货品-->
    <select id="selectClientGoodsById" resultMap="CustGoodsResult">
                SELECT
                	m.GOODS_NAME,
                	m.GOODS_ID,
                	m.GOODS_TYPE_NAME,
                	m.GOODS_TYPE,
                	mcg.CUST_GOODS_ID
                FROM
                	M_GOODS m
                LEFT JOIN (
                	SELECT
                		GOODS_ID,
                		CUST_GOODS_ID,
                		DEL_FLAG
                	FROM
                		M_CUST_GOODS
                	WHERE
                		CUSTOMER_ID = #{clientId}
                ) mcg ON m.GOODS_ID = mcg.GOODS_ID
                WHERE
                	m.GOODS_ID = mcg.GOODS_ID
                AND mcg.DEL_FLAG = 0
    </select>

    <!--修改客户基本信息-->
    <update id="updateClient">
        update m_customer
        <trim prefix="SET" suffixOverrides=",">
            <if test="delFlag != null  ">del_flag = #{delFlag},</if>
            <if test="delDate != null  ">del_date = #{delDate},</if>
            <if test="custCode != null  and custCode != ''  ">cust_code = #{custCode},</if>
            <if test="custName != null  and custName != ''  ">cust_name = #{custName},</if>
            <if test="custAbbr != null  and custAbbr != ''  ">cust_abbr = #{custAbbr},</if>
            <if test="custType != null  ">cust_type = #{custType},</if>
            <if test="ifAuthentication != null  and ifAuthentication != ''  ">if_authentication = #{ifAuthentication},</if>
            <if test="salesDept != null  and salesDept != ''  ">sales_dept = #{salesDept},</if>
            <if test="balaDept != null  and balaDept != ''  ">bala_dept = #{balaDept},</if>
            <if test="stationDept != null  and stationDept != ''  ">station_dept = #{stationDept},</if>
            <if test="balaCorp != null  and balaCorp != ''  ">bala_corp = #{balaCorp},</if>
            <if test="psndoc != null  and psndoc != ''  ">psndoc = #{psndoc},</if>
            <if test="psncontact != null  and psncontact != ''  ">psncontact = #{psncontact},</if>
            <if test="provinceId != null  and provinceId != ''  ">province_id = #{provinceId},</if>
            <if test="cityId != null  and cityId != ''  ">city_id = #{cityId},</if>
            <if test="areaId != null  and areaId != ''  ">area_id = #{areaId},</if>
            <if test="address != null  and address != ''  ">address = #{address},</if>
            <if test="contact != null  and contact != ''  ">contact = #{contact},</if>
            <if test="contactPost != null  and contactPost != ''  ">contact_post = #{contactPost},</if>
            <if test="phone != null  and phone != ''  ">phone = #{phone},</if>
            <if test="mobile != null  and mobile != ''  ">mobile = #{mobile},</if>
            <if test="email != null  and email != ''  ">email = #{email},</if>
            <if test="fax != null  and fax != ''  ">fax = #{fax},</if>
            <if test="zipcode != null  and zipcode != ''  ">zipcode = #{zipcode},</if>
            <if test="billingCorp != null  and billingCorp != ''  ">billing_corp = #{billingCorp},</if>
            <if test="billingDate != null  ">billing_date = #{billingDate},</if>
            <if test="taxIdentify != null  and taxIdentify != ''  ">tax_identify = #{taxIdentify},</if>
            <if test="balaType != null  and balaType != ''  ">bala_type = #{balaType},</if>
            <if test="billingType != null  and billingType != ''  ">billing_type = #{billingType},</if>
            <if test="billingPayable != null  and billingPayable != ''  ">billing_payable = #{billingPayable},</if>
            <if test="bank != null  and bank != ''  ">bank = #{bank},</if>
            <if test="discountRate != null  ">discount_rate = #{discountRate},</if>
            <if test="accPeriodAhead != null  ">acc_period_ahead = #{accPeriodAhead},</if>
            <if test="billingAhead != null  ">billing_ahead = #{billingAhead},</if>
            <if test="creditAmount != null  ">credit_amount = #{creditAmount},</if>
            <if test="accountName != null  and accountName != ''  ">account_name = #{accountName},</if>
            <if test="bankAccount != null  and bankAccount != ''  ">bank_account = #{bankAccount},</if>
            <if test="registerAddr != null  and registerAddr != ''  ">register_addr = #{registerAddr},</if>
            <if test="billingRule != null  ">billing_rule = #{billingRule},</if>
            <if test="legalRepresent != null  and legalRepresent != ''  ">legal_represent = #{legalRepresent},</if>
            <if test="registerCapital != null  ">register_capital = #{registerCapital},</if>
            <if test="addressee != null  and addressee != ''  ">addressee = #{addressee},</if>
            <if test="addresseeContact != null  and addresseeContact != ''  ">addressee_contact = #{addresseeContact},</if>
            <if test="addresseeAddr != null  and addresseeAddr != ''  ">addressee_addr = #{addresseeAddr},</if>
            <if test="website != null  and website != ''  ">website = #{website},</if>
            <if test="memo != null  and memo != ''  ">memo = #{memo},</if>
            <if test="custActivity != null  ">cust_activity = #{custActivity},</if>
            <if test="regUserId != null  and regUserId != ''  ">reg_user_id = #{regUserId},</if>
            <if test="regDate != null  ">reg_date = #{regDate},</if>
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id = #{corUserId},</if>
            <if test="corDate != null  ">cor_date = #{corDate},</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id = #{regScrId},</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id = #{corScrId},</if>
            <if test="businesslicense != null  and businesslicense != ''  ">businesslicense = #{businesslicense},</if>
            <if test="psndocName != null  and psndocName != ''  ">psndoc_name = #{psndocName},</if>
            <if test="legalCard != null  and legalCard != ''  ">legal_card = #{legalCard},</if>
            <if test="customerType != null  and customerType != ''  ">customer_type = #{customerType},</if>
            <if test="paymentDays != null  and paymentDays != ''  ">payment_days = #{paymentDays},</if>
            <if test="provinceName != null  and provinceName != ''  ">province_name =  #{provinceName},</if>
            <if test="cityName != null  and cityName != ''  ">city_name = #{cityName},</if>
            <if test="areaName != null  and areaName != ''  ">area_name = #{areaName},</if>
            <if test="delUserId != null  and delUserId != ''  ">del_user_id = #{delUserId},</if>
            <if test="bankId != null ">bank_id = #{bankId},</if>
            <if test="crtGuidePrice != null ">crt_guide_price = #{crtGuidePrice},</if>
            <if test="customerLevel != null ">customer_level = #{customerLevel},</if>
            <if test="tariff != null ">tariff = #{tariff},</if>
            <if test="isLockOtherFee != null ">is_lock_other_fee = #{isLockOtherFee},</if>
            <if test="adjustment != null ">adjustment = #{adjustment},</if>
            <if test="invoiceDays != null and invoiceDays !=''">invoice_days = #{invoiceDays},</if>
            <if test="collectionDays != null and collectionDays != ''">collection_days = #{collectionDays},</if>
            <if test="isEnabled != null and isEnabled != '' or isEnabled == 0">IS_ENABLED = #{isEnabled},</if>
            <if test="isNeedReceipt != null and isNeedReceipt != '' or isNeedReceipt == 0">IS_NEED_RECEIPT = #{isNeedReceipt},</if>
            <if test="enableContractPrice != null and enableContractPrice != '' ">ENABLE_CONTRACT_PRICE = #{enableContractPrice},</if>
            <if test="platRate != null and platRate != '' or platRate == 0">PLAT_RATE = #{platRate},</if>
            <if test="platTax != null and platTax != '' or platTax == 0">PLAT_TAX = #{platTax},</if>

            <if test="specialDate != null and specialDate != ''">special_date = #{specialDate},</if>
            <if test="accurateRegion != null and accurateRegion != ''">ACCURATE_REGION = #{accurateRegion},</if>
            <if test="accurateArriDetail != null and accurateArriDetail != ''">accurate_arri_detail = #{accurateArriDetail},</if>
            <if test="accurateGoods != null ">ACCURATE_GOODS = #{accurateGoods},</if>
            <if test="contractPriceFloat != null">contract_price_float = #{contractPriceFloat},</if>
            <if test="floatDateStart != null ">float_date_start = #{floatDateStart},</if>
            <if test="floatDateEnd != null ">float_date_end = #{floatDateEnd},</if>
            <if test="appDeliContact != null  and appDeliContact != ''  ">app_deli_contact = #{appDeliContact},</if>
            <if test="appDeliMobile != null  and appDeliMobile != ''  ">app_deli_mobile = #{appDeliMobile},</if>
            <if test="contractPriceReview != null   ">contract_price_review = #{contractPriceReview},</if>
            <if test="contractPriceReviewUserId != null  and contractPriceReviewUserId != ''  ">contract_price_review_user_id = #{contractPriceReviewUserId},</if>
            <if test="contractPriceReviewUserName != null  and contractPriceReviewUserName != ''  ">contract_price_review_user_name = #{contractPriceReviewUserName},</if>
            <if test="contractPriceReviewDate != null ">contract_price_review_date = #{contractPriceReviewDate},</if>
            <if test="isAutoDispatch != null ">is_auto_dispatch = #{isAutoDispatch},</if>
            <if test="autoDispatchType != null ">auto_dispatch_type = #{autoDispatchType},</if>
            <if test="autoSubsectionAddrId != null ">auto_subsection_addr_id = #{autoSubsectionAddrId},</if>
            <if test="autoDispatchDeliFee != null ">auto_dispatch_deli_fee = #{autoDispatchDeliFee},</if>
            <if test="autoDispatchPickUpFee != null ">auto_dispatch_pick_up_fee = #{autoDispatchPickUpFee},</if>
        </trim>
        where customer_id = #{customerId}
    </update>

    <update id="updateClientByAdministrator">
        update m_customer
        <trim prefix="SET" suffixOverrides=",">
            <if test="corDate != null  ">cor_date = #{corDate},</if>
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id = #{corUserId},</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id = #{corScrId},</if>
            <if test="referenceRate != null">reference_rate = #{referenceRate},</if>
            <if test="referenceRate == null">reference_rate = '',</if>
            <if test="disabledTime != null">disabled_Time = #{disabledTime},</if>

            payment_days = #{paymentDays,jdbcType=VARCHAR},
            invoice_days = #{invoiceDays,jdbcType=INTEGER},
            collection_days = #{collectionDays,jdbcType=INTEGER},
            special_date = #{specialDate,jdbcType=INTEGER},

            adjustment = #{adjustment,jdbcType=DOUBLE},
            PLAT_RATE = #{platRate,jdbcType=DOUBLE},
            PLAT_TAX = #{platTax,jdbcType=DOUBLE},

            is_lock_other_fee = #{isLockOtherFee,jdbcType=INTEGER},

            ENABLE_CONTRACT_PRICE = #{enableContractPrice,jdbcType=INTEGER},
            crt_guide_price = #{crtGuidePrice,jdbcType=INTEGER},
            IS_NEED_RECEIPT = #{isNeedReceipt,jdbcType=INTEGER},
            IS_ENABLED = #{isEnabled},
            if_Bargain = #{ifBargain},
            is_special_reference_price = #{isSpecialReferencePrice,jdbcType=INTEGER},
            is_special_transfer = #{isSpecialTransfer,jdbcType=INTEGER},
            contract_Need_Type = #{contractNeedType,jdbcType=INTEGER},
            contract_price_type = #{contractPriceType,jdbcType=INTEGER},
            settlement_check = #{settlementCheck,jdbcType=INTEGER},
            inv_contact_check = #{invContactCheck,jdbcType=INTEGER}
        </trim>
        where customer_id = #{customerId}
    </update>

    <!-- Excel修复三个日期   -->
    <update id="updateClientForDaysByCustomerId">
        update m_customer
        <trim prefix="SET" suffixOverrides=",">
            <if test="paymentDays != null  and paymentDays != ''  ">payment_days = #{paymentDays},</if>
            <if test="invoiceDays != null and invoiceDays !=''">invoice_days = #{invoiceDays},</if>
            <if test="collectionDays != null and collectionDays != ''">collection_days = #{collectionDays},</if>
        </trim>
        where cust_abbr = #{custAbbr}
    </update>

    <!--根据客户Id查询客户线路信息-->
    <select id="selectClientLine" resultMap="CustTransLineResult">
            SELECT
            	m.TRANS_LINE_ID,
            	m.LINE_NAME,
                m.start_province_name||m.start_city_name||m.start_city_name as startDetailAddr,
                m.end_province_name||m.end_city_name||m.end_area_name as endDetailAddr,
            	mtl.CUST_TRANSLINE_ID,
            	mtl.RECEIPT_INTERVAL_DAY
            FROM
            	M_TRANS_LINE m
            LEFT JOIN (
            	SELECT
            		CUST_TRANSLINE_ID,
            		TRANS_LINE_ID,
            		RECEIPT_INTERVAL_DAY,
            		DEL_FLAG
            	FROM
            		M_CUST_TRANS_LINE
            	WHERE
            		CUSTOMER_ID = #{clientId}
            ) mtl ON m.TRANS_LINE_ID = mtl.TRANS_LINE_ID
            WHERE
            	m.TRANS_LINE_ID = mtl.TRANS_LINE_ID
            AND mtl.DEL_FLAG = 0
    </select>

    <!--根据Id删除结算客户-->
    <update id="deleteRelatedClient" >
        update M_CUST_BALA set
              DEL_FLAG = #{delFlag},
              DEL_DATE = #{delDate}
             where
              CUSTOMER_ID = #{customerId}
    </update>

    <!--根据Id删除客户货品信息-->
    <update id="deleteClientGoods" >
         update M_CUST_GOODS set
                DEL_FLAG = #{delFlag},
                DEL_DATE = #{delDate}
             where
                 CUSTOMER_ID = #{customerId}
    </update>

    <!--根据Id删除客户线路信息-->
    <update id="deleteClientLine" >
         update M_CUST_TRANS_LINE set
                DEL_FLAG = #{delFlag},
                DEL_DATE = #{delDate}
             where
                 CUSTOMER_ID = #{customerId}
    </update>

    <!--根据客户Id删除合同信息-->
    <update id="deleteCustContract">
        update M_CUST_CONTRACT set
              DEL_FLAG = #{delFlag},
              DEL_DATE = #{delDate}
        where
            BALA_CUST_ID = #{balaCustId}
    </update>

    <update id="deleteContractList">
        update M_CONTRACT
          set
             DEL_FLAG = #{contract.delFlag},
             DEL_DATE = #{contract.delDate},
             DEL_USER_ID = #{contract.delUserId}
        where
              CONTRACT_ID in
        <foreach item="ids" collection="list" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </update>

    <!--根据客户Id删除合同价-->
    <update id="deleteContractPc">
        update M_CUST_CONTRACTPC set
              DEL_FLAG = #{delFlag},
              DEL_DATE = #{delDate},
              DEL_USER_ID = #{delUserId}
        where
            CUSTOMER_ID = #{customerId}
    </update>

    <!--根据客户Id查询客户照片-->
    <select id="selectClientPic" resultType="com.ruoyi.system.domain.SysUploadFile">
              SELECT
              	TID as tid,
              	FILE_NAME as fileName,
              	FILE_PATH as filePath
              FROM
              	SYS_UPLOAD_FILE suf
              LEFT JOIN (
              	SELECT
              		APPENDIX_ID
              	FROM
              		M_CUST_PIC
              	WHERE
              		CUSTOMER_ID = #{clientId}
              ) mct ON suf.TID = mct.APPENDIX_ID
              WHERE
              	suf.TID = mct.APPENDIX_ID
    </select>

    <!--根据客户Id查询客户下的所有结算客户-->
    <select id="selectRelatedListByClientId" parameterType="map" resultMap="ClientPopupVOResult">
        <include refid="selectClientPopupVo"/>
                LEFT JOIN (
                SELECT
                    RELATED_CUST_ID,
                    CUST_BALA_ID,
                    CUSTOMER_ID,
                    DEL_FLAG,
                    LOCKED_FLAG
                FROM
                    M_CUST_BALA
                WHERE
                    CUSTOMER_ID = #{customerId}
            ) mcb ON t.CUSTOMER_ID = mcb.RELATED_CUST_ID
        WHERE
            t.CUSTOMER_ID = mcb.RELATED_CUST_ID
          AND mcb.DEL_FLAG = '0'
        <if test="custName != null  and custName != '' ">
            <bind name="custName" value=" custName + '%' "/>
            and t.cust_name like  #{custName}
        </if>
        <if test="checkStatus != null  and checkStatus != '' ">
            and t.check_status = #{checkStatus}
        </if>
        <if test="checkStatus != null  and checkStatus != '' ">
            and t.check_status = #{checkStatus}
        </if>
        <if test="sourceType != null ">
            and t.source_type = #{sourceType}
        </if>
        order by COR_DATE desc,CUST_NAME DESC
    </select>

    <!--插入区间-->
    <insert id="insertSection">
        insert into M_CONTRACTPC_SECTION(
            CONTRACTPC_SECTION_ID,
            CUST_CONTRACTPC_ID,
            CUSTOMER_ID,
            START_SECTION,
            END_SECTION,
            GUIDING_PRICE,
            REG_USER_ID,
            REG_DATE,
            COR_USER_ID,
            COR_DATE,
            START_OPERATOR,
            END_OPERATOR,
            DEL_FLAG,
            reg_scr_id,
            cor_scr_id,
            PROFIT,
            special_price,
            is_fixed_price,
            cost_price,
            delivery_fee
        )values (
            #{contractpcSectionId},
            #{custContractpcId},
            #{customerId},
            #{startSection,jdbcType=VARCHAR},
            #{endSection,jdbcType=VARCHAR},
            #{guidingPrice},
            #{regUserId},
            #{regDate},
            #{regUserId},
            #{regDate},
            #{startOperator},
            #{endOperator},
            #{delFlag},
            #{regScrId},
            #{corScrId},
            #{profit,jdbcType=DECIMAL},
            #{specialPrice,jdbcType=DECIMAL},
            #{isFixedPrice},
            #{costPrice,jdbcType=DECIMAL},
            #{deliveryFee,jdbcType=DECIMAL}
            )
    </insert>

    <!--插入合同价-->
    <insert id="insertContractPrice">
        insert into M_CUST_CONTRACTPC
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="custContractpcId != null  and custContractpcId != ''  ">CUST_CONTRACTPC_ID,</if>
            <if test="customerId != null  and customerId != ''  ">CUSTOMER_ID,</if>
            <if test="contractId != null  and contractId != ''  ">contract_id,</if>
            <if test="transLineId != null  and transLineId != ''  ">TRANS_LINE_ID,</if>
            <if test="lineName != null  and lineName != ''  ">LINE_NAME,</if>
            <if test="lineCode != null  and lineCode != ''  ">LINE_CODE,</if>
            <if test="deliProName != null  and deliProName != ''  ">deli_pro_name,</if>
            <if test="deliProvinceId != null  and deliProvinceId != ''  ">deli_province_id,</if>
            <if test="deliCityName != null  and deliCityName != ''  ">deli_city_name,</if>
            <if test="deliCityId != null  and deliCityId != ''  ">deli_city_id,</if>
            <if test="deliAreaName != null  and deliAreaName != ''  ">deli_area_name,</if>
            <if test="deliAreaId != null  and deliAreaId != ''  ">deli_area_id,</if>
            <if test="arriProName != null  and arriProName != ''  ">arri_pro_name,</if>
            <if test="arriProvinceId != null  and arriProvinceId != ''  ">arri_province_id,</if>
            <if test="arriCityName != null  and arriCityName != ''  ">arri_city_name,</if>
            <if test="arriCityId != null  and arriCityId != ''  ">arri_city_id,</if>
            <if test="arriAreaName != null  and arriAreaName != ''  ">arri_area_name,</if>
            <if test="arriAreaId != null  and arriAreaId != ''  ">arri_area_id,</if>
            <if test="carLen != null  and carLen != ''  ">CAR_LEN,</if>
            <if test="carType != null  and carType != ''  ">CAR_TYPE,</if>
            <if test="billingMethod != null  ">BILLING_METHOD,</if>
            <if test="mileage != null  ">MILEAGE,</if>
            <if test="guidingPrice != null  ">GUIDING_PRICE,</if>
            <if test="ifSection != null  and ifSection != ''  ">IF_SECTION,</if>
            <if test="goodsCharacter != null">GOODS_CHARACTER,</if>
            <if test="regUserId != null  and regUserId != ''  ">REG_USER_ID,</if>
            <if test="regDate != null  ">REG_DATE,</if>
            <if test="corUserId != null  and corUserId != ''  ">COR_USER_ID,</if>
            <if test="corDate != null ">COR_DATE,</if>
            <if test="delFlag != null ">DEL_FLAG,</if>
            <if test="regScrId != null  and regScrId != ''  ">REG_SCR_ID,</if>
            <if test="corScrId != null  and corScrId != ''  ">COR_SCR_ID,</if>
            <if test="profit != null  ">PROFIT,</if>
            <if test="goodsId != null  ">goods_id,</if>
            <if test="goodsName != null  ">goods_name,</if>
            <if test="arrivalId != null  ">arrival_id,</if>
            <if test="arriAddrName != null  ">arri_addr_name,</if>
            <if test="arriDetailAddr != null  ">arri_detail_addr,</if>
            <if test="specialPrice != null  ">SPECIAL_PRICE,</if>
            <if test="isIncludeTax != null  ">is_include_tax,</if>
            <if test="costPrice != null  ">cost_price,</if>
            <if test="priceReview != null  ">price_review,</if>
            <if test="priceReviewUserId != null  ">price_review_user_id,</if>
            <if test="priceReviewUserName != null  ">price_review_user_name,</if>
            <if test="priceReviewDate != null  ">price_review_date,</if>
            <if test="isOversize != null  ">IS_OVERSIZE,</if>
            <if test="versionId != null  ">version_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="custContractpcId != null  and custContractpcId != ''  ">#{custContractpcId},</if>
            <if test="customerId != null  and customerId != ''  ">#{customerId},</if>
            <if test="contractId != null  and contractId != ''  ">#{contractId},</if>
            <if test="transLineId != null  and transLineId != ''  ">#{transLineId},</if>
            <if test="lineName != null  and lineName != ''  ">#{lineName},</if>
            <if test="lineCode != null  and lineCode != ''  ">#{lineCode},</if>
            <if test="deliProName != null  and deliProName != ''  ">#{deliProName},</if>
            <if test="deliProvinceId != null  and deliProvinceId != ''  ">#{deliProvinceId},</if>
            <if test="deliCityName != null  and deliCityName != ''  ">#{deliCityName},</if>
            <if test="deliCityId != null  and deliCityId != ''  ">#{deliCityId},</if>
            <if test="deliAreaName != null  and deliAreaName != ''  ">#{deliAreaName},</if>
            <if test="deliAreaId != null  and deliAreaId != ''  ">#{deliAreaId},</if>
            <if test="arriProName != null  and arriProName != ''  ">#{arriProName},</if>
            <if test="arriProvinceId != null  and arriProvinceId != ''  ">#{arriProvinceId},</if>
            <if test="arriCityName != null  and arriCityName != ''  ">#{arriCityName},</if>
            <if test="arriCityId != null  and arriCityId != ''  ">#{arriCityId},</if>
            <if test="arriAreaName != null  and arriAreaName != ''  ">#{arriAreaName},</if>
            <if test="arriAreaId != null  and arriAreaId != ''  ">#{arriAreaId},</if>
            <if test="carLen != null  and carLen != ''  ">#{carLen},</if>
            <if test="carType != null  and carType != ''  ">#{carType},</if>
            <if test="billingMethod != null  ">#{billingMethod},</if>
            <if test="mileage != null  ">#{mileage,jdbcType=VARCHAR},</if>
            <if test="guidingPrice != null   ">#{guidingPrice,jdbcType=VARCHAR},</if>
            <if test="ifSection != null  and ifSection != ''  ">#{ifSection},</if>
            <if test="goodsCharacter != null">#{goodsCharacter},</if>
            <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="delFlag != null ">#{delFlag},</if>
            <if test="regScrId != null  and regScrId != ''  ">#{regScrId},</if>
            <if test="corScrId != null  and corScrId != ''  ">#{corScrId},</if>
            <if test="profit != null   ">#{profit},</if>
            <if test="goodsId != null   ">#{goodsId},</if>
            <if test="goodsName != null   ">#{goodsName},</if>
            <if test="arrivalId != null   ">#{arrivalId},</if>
            <if test="arriAddrName != null   ">#{arriAddrName},</if>
            <if test="arriDetailAddr != null   ">#{arriDetailAddr},</if>
            <if test="specialPrice != null   ">#{specialPrice},</if>
            <if test="isIncludeTax != null   ">#{isIncludeTax},</if>
            <if test="costPrice != null   ">#{costPrice},</if>
            <if test="priceReview != null   ">#{priceReview},</if>
            <if test="priceReviewUserId != null   ">#{priceReviewUserId},</if>
            <if test="priceReviewUserName != null   ">#{priceReviewUserName},</if>
            <if test="priceReviewDate != null   ">#{priceReviewDate},</if>
            <if test="isOversize != null   ">#{isOversize},</if>
            <if test="versionId != null   ">#{versionId},</if>
        </trim>
    </insert>

    <!--根据客户ID查询合同信息-->
    <select id="selectContractByClientId" resultMap="ContractResult">
        SELECT
            suf.TID,
            suf.FILE_NAME,
            suf.FILE_PATH,
            mc.NAME,
            mc.EFFECTIVE_DATE,
            mc.INVALID_DATE,
            mc.WARNING_DATE,
            mc.CONTRACT_ID
        FROM
            M_CONTRACT mc
                LEFT JOIN  SYS_UPLOAD_FILE suf ON suf.TID = mc.appendix_id
                LEFT JOIN  M_CUST_CONTRACT mcc ON mcc.CONTRACT_ID = mc.CONTRACT_ID
        WHERE
            mcc.DEL_FLAG = 0 and mc.DEL_FLAG = 0
          AND mcc.BALA_CUST_ID = #{id}
</select>

    <!--根据客户Id查询客户集团-->
    <select id="selectGroupByClientId" resultMap="GroupResult">
        SELECT
            m.GROUP_ID,
            m.GROUP_NAME,
            mgc.GROUP_CUST_ID
        FROM
            M_GROUP m
                LEFT JOIN (
                SELECT
                    GROUP_ID,
                    GROUP_CUST_ID,
                    DEL_FLAG
                FROM
                    M_GROUP_CUST
                WHERE
                    CUSTOMER_ID = #{clientId}
            ) mgc ON mgc.GROUP_ID = m.GROUP_ID
        WHERE
            mgc.GROUP_ID = m.GROUP_ID
            and mgc.DEL_FLAG = 0
    </select>

    <!--插入图片信息-->
    <insert id="insertPic" parameterType="Custpic">
        insert into M_CUST_PIC
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="custPicId != null  and custPicId != ''  ">cust_pic_id,</if>
            <if test="delFlag != null  ">del_flag,</if>
            <if test="delDate != null  ">del_date,</if>
            <if test="picType != null  ">pic_type,</if>
            <if test="appendixId != null  and appendixId != ''  ">appendix_id,</if>
            <if test="customerId != null  and customerId != ''  ">customer_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="custPicId != null  and custPicId != ''  ">#{custPicId},</if>
            <if test="delFlag != null  ">#{delFlag},</if>
            <if test="delDate != null  ">#{delDate},</if>
            <if test="picType != null  ">#{picType},</if>
            <if test="appendixId != null  and appendixId != ''  ">#{appendixId},</if>
            <if test="customerId != null  and customerId != ''  ">#{customerId},</if>
            <if test="lockedFlag != null  and lockedFlag != ''  ">#{lockedFlag},</if>
        </trim>
    </insert>

    <!--查询附件信息-->
    <select id="selectPicList" parameterType="Custpic" resultMap="CustPicResult">
        SELECT
        cust_pic_id,
        del_flag,
        del_date,
        pic_type,
        appendix_id,
        customer_id
        FROM
        M_CUST_PIC
        <where>
            del_flag != 1
            <if test="picType != null "> and pic_type = #{picType}</if>
            <if test="appendixId != null  and appendixId != '' "> and appendix_id = #{appendixId}</if>
            <if test="customerId != null  and customerId != '' "> and customer_id = #{customerId}</if>


        </where>
    </select>

    <!--查询附件类型-->
    <select id="selectPicImgList" parameterType="Custpic" resultMap="CustPicResult">
        SELECT
            pic_type,
            appendix_id,
            file_name,
            file_path,
            file_id
        FROM
            M_CUST_PIC pic
                LEFT JOIN sys_upload_file suf ON suf.tid = pic.appendix_id
        WHERE
            pic.CUSTOMER_ID = #{clientId} and suf.del_flag = 0 and pic.del_flag != 1
    </select>

    <!--修改附件信息-->
    <update id="updatePic" parameterType="Custpic">
        update M_CUST_PIC
        <trim prefix="SET" suffixOverrides=",">
            <if test="appendixId != null  and appendixId != ''  ">appendix_id = #{appendixId},</if>
        </trim>
        where customer_id = #{customerId} and pic_type = #{picType}
    </update>

    <!-- 获取 Sequence  -->
    <select id="getSeq" resultType="java.lang.String">
        select SEQ_CUSTOMER.nextval from dual
    </select>

    <!--新增结算客户-->
    <insert id="insertCustBala">
        insert into M_CUST_BALA(
            CUST_BALA_ID,
            RELATED_CUST_ID,
            CUSTOMER_ID,
            IS_DEFAULT,
            LOCKED_FLAG,
            DEL_FLAG
        ) values
            (
                #{custBalaId,jdbcType=VARCHAR},
                #{relatedCustId,jdbcType=VARCHAR},
                #{customerId,jdbcType=VARCHAR},
                #{isDefault,jdbcType=VARCHAR},
                #{lockedFlag,jdbcType=VARCHAR},
                #{delFlag,jdbcType=VARCHAR}
            )
    </insert>

    <!--新增客户货品信息-->
    <insert id="insertCustGoods">
        INSERT INTO M_CUST_GOODS (
            CUST_GOODS_ID,
            CUSTOMER_ID,
            GOODS_ID,
            GOODS_CODE,
            GOODS_NAME,
            GOODS_TYPE,
            GOODS_TYPE_NAME,
            LENGTH,
            WIDTH,
            HEIGHT,
            TRANS_NOTE,
            REG_USER_ID,
            REG_DATE,
            COR_USER_ID,
            COR_DATE,
            DEL_FLAG
        )(
            SELECT
                #{custGoodsId},
                #{customerId},
                #{goodsId},
                GOODS_CODE,
                GOODS_NAME,
                GOODS_TYPE,
                GOODS_TYPE_NAME,
                LENGTH,
                WIDTH,
                HEIGHT,
                TRANS_NOTE,
                #{regUserId},
                #{regDate},
                #{regUserId},
                #{regDate},
                #{delFlag}
            FROM
                M_GOODS
            WHERE
                GOODS_ID = #{goodsId}
        )
    </insert>

    <!--新增客户线路信息-->
    <insert id="insertCustTransLine">
        insert into  M_CUST_TRANS_LINE
          (
              CUST_TRANSLINE_ID,
              CUSTOMER_ID,
              TRANS_LINE_ID,
              LINE_CODE,
              LINE_NAME,
              START_PROVINCE,
              START_CITY,
              START_AREA,
              START_PROVINCE_NAME,
              START_CITY_NAME,
              START_AREA_NAME,
              END_PROVINCE,
              END_CITY,
              END_AREA,
              END_PROVINCE_NAME,
              END_CITY_NAME,
              END_AREA_NAME,
              RECEIPT_INTERVAL_DAY,
              REG_USER_ID,
              REG_DATE,
              COR_USER_ID,
              COR_DATE,
              DEL_FLAG
          )  (
            select
                #{custTranslineId},
                #{customerId},
                #{transLineId},
                M_TRANS_LINE.LINE_CODE,
                M_TRANS_LINE.LINE_NAME,
                M_TRANS_LINE.START_PROVINCE_ID,
                M_TRANS_LINE.START_CITY_ID,
                M_TRANS_LINE.START_AREA_ID,
                M_TRANS_LINE.START_PROVINCE_NAME,
                M_TRANS_LINE.START_CITY_NAME,
                M_TRANS_LINE.START_AREA_NAME,
                M_TRANS_LINE.END_PROVINCE_ID,
                M_TRANS_LINE.END_CITY_ID,
                M_TRANS_LINE.END_AREA_ID,
                M_TRANS_LINE.END_PROVINCE_NAME,
                M_TRANS_LINE.END_CITY_NAME,
                M_TRANS_LINE.END_AREA_NAME,
                #{receiptIntervalDay,jdbcType = VARCHAR},
                #{regUserId},
                #{regDate},
                #{regUserId},
                #{regDate},
                #{delFlag}
            from M_TRANS_LINE where TRANS_LINE_ID = #{transLineId}
        )

    </insert>

    <!--删除客户-->
    <update id="deleteClientByIds">
        update  M_CUSTOMER set
        del_flag = #{client.delFlag},
        del_date =  #{client.delDate},
        del_user_id =  #{client.delUserId}
        where
        CUSTOMER_ID in
        <foreach item="ids" collection="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </update>

    <!-- 获取 ClientPopupVO 添加数据权限-->
    <select id="selectCustomerListByPermissionSales" parameterType="Client" resultMap="ClientPopupVOResult">
        <include refid="selectClientPopupVo"/>
        where
            t.DEL_FLAG = 0
            <if test="customerId != null  and customerId != '' "> and t.customer_id = #{customerId}</if>
            <if test="delDate != null "> and t.del_date = #{delDate}</if>
            <if test="custCode != null  and custCode != '' "> and t.cust_code = #{custCode}</if>
            <if test="custName != null  and custName != '' ">
                <bind name="custName" value="'%' + custName + '%' "/>
                and t.cust_name like #{custName}
            </if>
            <if test="custAbbr != null  and custAbbr != '' ">
                <bind name="custAbbr" value="'%' + custAbbr + '%' "/>
                and t.cust_abbr like #{custAbbr}
            </if>
            <if test="custType != null "> and t.cust_type = #{custType}</if>
            <if test="ifAuthentication != null  and ifAuthentication != '' "> and t.if_authentication = #{ifAuthentication}</if>
            <if test="salesDept != null  and salesDept != '' "> and t.sales_dept = #{salesDept}</if>
            <if test="balaDept != null  and balaDept != '' "> and t.bala_dept = #{balaDept}</if>
            <if test="stationDept != null  and stationDept != '' "> and t.station_dept = #{stationDept}</if>
            <if test="balaCorp != null  and balaCorp != '' "> and t.bala_corp = #{balaCorp}</if>
            <if test="psndoc != null  and psndoc != '' "> and t.psndoc = #{psndoc}</if>
            <if test="psncontact != null  and psncontact != '' "> and t.psncontact = #{psncontact}</if>
            <if test="provinceId != null  and provinceId != '' "> and t.province_id = #{provinceId}</if>
            <if test="cityId != null  and cityId != '' "> and t.city_id = #{cityId}</if>
            <if test="areaId != null  and areaId != '' "> and t.area_id = #{areaId}</if>
            <if test="address != null  and address != '' "> and t.address = #{address}</if>
            <if test="contact != null  and contact != '' "> and t.contact = #{contact}</if>
            <if test="contactPost != null  and contactPost != '' "> and t.contact_post = #{contactPost}</if>
            <if test="phone != null  and phone != '' "> and t.phone = #{phone}</if>
            <if test="mobile != null  and mobile != '' "> and t.mobile = #{mobile}</if>
            <if test="email != null  and email != '' "> and t.email = #{email}</if>
            <if test="fax != null  and fax != '' "> and t.fax = #{fax}</if>
            <if test="zipcode != null  and zipcode != '' "> and t.zipcode = #{zipcode}</if>
            <if test="billingCorp != null  and billingCorp != '' "> and t.billing_corp = #{billingCorp}</if>
            <if test="billingDate != null "> and t.billing_date = #{billingDate}</if>
            <if test="taxIdentify != null  and taxIdentify != '' "> and t.tax_identify = #{taxIdentify}</if>
            <if test="balaType != null  and balaType != '' "> and t.bala_type = #{balaType}</if>
            <if test="billingType != null  and billingType != '' "> and t.billing_type = #{billingType}</if>
            <if test="billingPayable != null  and billingPayable != '' "> and t.billing_payable = #{billingPayable}</if>
            <if test="bank != null  and bank != '' "> and t.bank = #{bank}</if>
            <if test="discountRate != null "> and t.discount_rate = #{discountRate}</if>
            <if test="accPeriodAhead != null "> and t.acc_period_ahead = #{accPeriodAhead}</if>
            <if test="billingAhead != null "> and t.billing_ahead = #{billingAhead}</if>
            <if test="creditAmount != null "> and t.credit_amount = #{creditAmount}</if>
            <if test="accountName != null  and accountName != '' "> and t.account_name = #{accountName}</if>
            <if test="bankAccount != null  and bankAccount != '' "> and t.bank_account = #{bankAccount}</if>
            <if test="registerAddr != null  and registerAddr != '' "> and t.register_addr = #{registerAddr}</if>
            <if test="billingRule != null "> and t.billing_rule = #{billingRule}</if>
            <if test="legalRepresent != null  and legalRepresent != '' "> and t.legal_represent = #{legalRepresent}</if>
            <if test="registerCapital != null "> and t.register_capital = #{registerCapital}</if>
            <if test="addressee != null  and addressee != '' "> and t.addressee = #{addressee}</if>
            <if test="addresseeContact != null  and addresseeContact != '' "> and t.addressee_contact = #{addresseeContact}</if>
            <if test="addresseeAddr != null  and addresseeAddr != '' "> and t.addressee_addr = #{addresseeAddr}</if>
            <if test="website != null  and website != '' "> and t.website = #{website}</if>
            <if test="memo != null  and memo != '' "> and t.memo = #{memo}</if>
            <if test="custActivity != null "> and t.cust_activity = #{custActivity}</if>
            <if test="regUserId != null  and regUserId != '' "> and t.reg_user_id = #{regUserId}</if>
            <if test="regDate != null "> and t.reg_date = #{regDate}</if>
            <if test="corUserId != null  and corUserId != '' "> and t.cor_user_id = #{corUserId}</if>
            <if test="corDate != null "> and t.cor_date = #{corDate}</if>
            <if test="regScrId != null  and regScrId != '' "> and t.reg_scr_id = #{regScrId}</if>
            <if test="corScrId != null  and corScrId != '' "> and t.cor_scr_id = #{corScrId}</if>
            <if test="businesslicense != null  and businesslicense != '' "> and t.businesslicense = #{businesslicense}</if>
            <if test="checkStatus != null "> and t.check_status = #{checkStatus}</if>
            <if test="isEnabled != null "> and t.is_enabled = #{isEnabled}</if>
            <if test="customerSource != null">and t.customer_source = #{customerSource}</if>
            <if test="sourceType != null">and t.source_type = #{sourceType}</if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by COR_DATE desc,CUST_NAME DESC
    </select>

    <!-- 查询ClientPopupVo对象-->
    <select id="selectClientPopupVoList" parameterType="Client" resultMap="ClientPopupVOResult">
        <if test="temporary == 1">select * from (</if>
        SELECT
        T.CUSTOMER_ID,
        T.CUST_CODE,
        T.CUST_NAME,
        T.CUST_ABBR,
        T.CUST_TYPE,
        T.SALES_DEPT,
        T.BALA_DEPT,
        T.STATION_DEPT,
        T.BALA_CORP,
        T.PSNDOC,
        T.PSNCONTACT,
        T.PROVINCE_ID,
        T.CITY_ID,
        T.AREA_ID,
        T.ADDRESS,
        T.CONTACT,
        T.CONTACT_POST,
        T.PHONE,
        T.MOBILE,
        T.EMAIL,
        T.FAX,
        T.ZIPCODE,
        T.BILLING_CORP,
        T.BILLING_DATE,
        T.TAX_IDENTIFY,
        T.BALA_TYPE,
        T.BILLING_TYPE,
<!--        T.BILLING_PAYABLE,-->
        T.BANK,
        T.DISCOUNT_RATE,
        T.ACC_PERIOD_AHEAD,
        T.BILLING_AHEAD,
        T.CREDIT_AMOUNT,
        T.ACCOUNT_NAME,
        T.BANK_ACCOUNT,
        T.REGISTER_ADDR,
        T.BILLING_RULE,
        T.LEGAL_REPRESENT,
        T.REGISTER_CAPITAL,
        T.ADDRESSEE,
        T.ADDRESSEE_CONTACT,
        T.ADDRESSEE_ADDR,
        T.WEBSITE,
        T.MEMO,
        T.REG_DATE,
        T.COR_DATE,
        T.BUSINESSLICENSE,
        T.PSNDOC_NAME,
        T.LEGAL_CARD,
        T.CUSTOMER_TYPE,
        T.PAYMENT_DAYS,
        T.PROVINCE_NAME,
        T.CITY_NAME,
        T.AREA_NAME,
        T.BANK_ID,
        T.APP_DELI_CONTACT,
        T.APP_DELI_MOBILE,
        T.CHECK_STATUS,
        T.CHECK_DATE,
        T.CHECK_MAN,
        t.crt_guide_price,
        t.customer_level,
        t.tariff,
        t.is_need_receipt,
        t.is_need_trace,
        t.adjustment,
        T.IS_ENABLED,

        t.INVOICE_DAYS,
        t.COLLECTION_DAYS,
        t.IS_LOCK_OTHER_FEE,
        t.special_date,
        t.ENTERPRISE_NATURE,
        t.CUSTOMER_SOURCE,
        t.handling_charges,
        t.handling_charges_type,
        t.ENABLE_CONTRACT_PRICE,
        t.if_Bargain,
        t.reference_rate,
        t.contract_price_type,

        T1.DEPT_NAME sales_dept_name,
        T2.DEPT_NAME bala_dept_name,
        T3.DEPT_NAME station_dept_name,
        sys_user.user_name cor_user_id,
        user2.user_name reg_user_id,

        (
        select

        case
        when count(1) = 0 then null
        when max(bus.LONG_TERM_EFFECTIVE) = '1' then '长期有效'
        else
        case
        when max(bus.end_date) is null and max(bus.EXTENSION_DATE) is null and max(bus.SYSTEM_EXTENSION_DATE) is null then '有合同无有效期'
        else to_char(
        greatest(
        COALESCE(max(bus.end_date), DATE '0001-01-01'),
        COALESCE(max(bus.EXTENSION_DATE), DATE '0001-01-01'),
        COALESCE(max(bus.SYSTEM_EXTENSION_DATE), DATE '0001-01-01')
        ),
        'yyyy-mm-dd'
        )
        end
        end invalid_date
        from M_CONTRACT_BUSINESS bus
        left join M_CONTRACT_BUSINESS_CUST busCust on bus.ID=busCust.CONTRACT_BUSINESS_ID and busCust.DEL_FLAG=0
        where bus.del_flag = 0 and busCust.CUSTOMER_ID = t.customer_id
        ) invalid_date,
<!--        t8.SALES_NAME,-->

        (
            select count(*) from T_ENTRUST_EXP t9
            <!--left join T_ENTRUST t10 on  t9.ENTRUST_ID = t10.ENTRUST_ID and t10.del_flag = 0-->
            where t9.DEL_FLAG = 0  AND t9.REG_DATE >= TO_DATE('2021-12-07 00:00:00','yyyy-mm-dd hh24:mi:ss')
            <!--AND (t.CUSTOMER_ID = t10.CUSTOMER_ID or t9.CUSTOMER_ID = t.CUSTOMER_ID)-->
            and t9.CUSTOMER_ID = t.CUSTOMER_ID
        ) as customer_exp_count,
        t9.invoiceSalesDeptName,

        (select listagg(a.BILLING_PAYABLE,',') WITHIN GROUP(order by a.BILLING_PAYABLE)
                from m_cust_billing a where a.DEL_FLAG=0 and a.CUSTOMER_ID=t.CUSTOMER_ID) BILLING_PAYABLE,
        t.notice_file_id,
        t5.DEPT_ID      ops_dept_id,
        t5.DEPT_NAME    ops_dept_name,
        t6.DEPT_ID      mgmt_dept_id,
        t6.DEPT_NAME    mgmt_dept_name,
        t.disabled_time,
        t10.GROUP_NAME GROUP_NAME,
                                            t.CONTRACT_NEED_TYPE
        FROM M_CUSTOMER T
        LEFT JOIN SYS_DEPT T1 ON T.SALES_DEPT = T1.DEPT_ID and  T1.DEL_FLAG = 0
        LEFT JOIN SYS_DEPT T2  ON T.BALA_DEPT = T2.DEPT_ID and T2.DEL_FLAG = 0
        LEFT JOIN SYS_DEPT T3 ON T.STATION_DEPT = T3.DEPT_ID and T3.DEL_FLAG = 0
        left join sys_user on sys_user.USER_ID = t.cor_user_id and sys_user.DEL_FLAG = 0
        left join sys_user user2 on user2.USER_ID = t.reg_user_id and user2.del_flag = 0

        LEFT JOIN SYS_DEPT T5  ON T1.PARENT_ID = T5.DEPT_ID     <!--运营部-->
        LEFT JOIN SYS_DEPT T6 ON T5.PARENT_ID = T6.DEPT_ID      <!--管理部-->

        <!--        left join m_sales_group t8 on T.sales_id = t8.id-->

        left join (
            select invoice.customer_id customer_id,
                   listagg(distinct dept.DEPT_NAME, ',') WITHIN GROUP(ORDER BY dept.DEPT_NAME) invoiceSalesDeptName
              from T_RECEIVE_DETAIL receive
         left join t_invoice invoice on invoice.invoice_id = receive.invoice_id
         left join sys_dept dept on dept.DEPT_ID = invoice.SALES_DEPT
             where receive.DEL_FLAG = 0
               and receive.IS_NTOCC = 0
               and receive.VBILLSTATUS != 4
               and receive.UNGOT_AMOUNT != 0
            group by invoice.customer_id
        ) t9 on t9.customer_id = t.customer_id
        left join (select a.customer_id,a1.group_id,a1.group_name
                     from m_group_cust a
                left join m_group a1 on a.group_id = a1.group_id
                    where a.del_flag=0 and a1.del_flag=0) t10 on t10.customer_id = t.customer_id
        where t.DEL_FLAG = 0
        <if test="customerId != null  and customerId != '' "> and t.customer_id = #{customerId}</if>
        <if test="delDate != null "> and t.del_date = #{delDate}</if>
        <if test="custCode != null  and custCode != '' "> and t.cust_code = #{custCode}</if>
        <if test="custName != null and custName.trim() != ''">
            <bind name="custName" value="custName + '%'"/>
            and (t.CUST_NAME like #{custName} or t.cust_abbr like #{custName} or t10.GROUP_NAME like #{custName})
        </if>
        <if test="psndocName != null and psndocName.trim() != ''">
            <bind name="psndocName" value="psndocName + '%'"/>
            and t.PSNDOC_NAME like #{psndocName}
        </if>
        <if test="custAbbr != null  and custAbbr != '' ">
            <bind name="custAbbr" value="custAbbr + '%'"/>
            and t.cust_abbr like #{custAbbr}
        </if>
        <if test="custType != null "> and t.cust_type = #{custType}</if>
        <if test="ifAuthentication != null  and ifAuthentication != '' "> and t.if_authentication = #{ifAuthentication}</if>
        <if test="salesDept != null  and salesDept != '' "> and t.sales_dept = #{salesDept}</if>
        <if test="balaDept != null  and balaDept != '' "> and t.bala_dept = #{balaDept}</if>
        <if test="stationDept != null  and stationDept != '' "> and t.station_dept = #{stationDept}</if>
        <if test="balaCorp != null  and balaCorp != '' "> and t.bala_corp = #{balaCorp}</if>
        <if test="operateCorp != null  and operateCorp != '' "> and t.operate_corp = #{operateCorp}</if>
        <if test="psndoc != null  and psndoc != '' ">
            <bind name="psndoc" value="psndoc + '%'"/>
            and t.psndoc like #{psndoc}
        </if>
        <if test="psncontact != null  and psncontact != '' "> and t.psncontact = #{psncontact}</if>
        <if test="provinceId != null  and provinceId != '' "> and t.province_id = #{provinceId}</if>
        <if test="cityId != null  and cityId != '' "> and t.city_id = #{cityId}</if>
        <if test="areaId != null  and areaId != '' "> and t.area_id = #{areaId}</if>
        <if test="address != null  and address != '' ">
            <bind name="address" value="address + '%'"/>
            and t.address like #{address}
        </if>
        <if test="contact != null  and contact != '' ">
            <bind name="contact" value="contact + '%'"/>
            and (t.contact like #{contact} or t.phone like #{contact})
        </if>
        <if test="contactPost != null  and contactPost != '' "> and t.contact_post = #{contactPost}</if>
        <if test="phone != null  and phone != '' ">
            <bind name="phone" value="phone + '%'"/>
            and t.phone like #{phone}
        </if>
        <if test="mobile != null  and mobile != '' "> and t.mobile = #{mobile}</if>
        <if test="email != null  and email != '' "> and t.email = #{email}</if>
        <if test="fax != null  and fax != '' "> and t.fax = #{fax}</if>
        <if test="zipcode != null  and zipcode != '' "> and t.zipcode = #{zipcode}</if>
        <if test="billingCorp != null  and billingCorp != '' "> and t.billing_corp = #{billingCorp}</if>
        <if test="billingDate != null "> and t.billing_date = #{billingDate}</if>
        <if test="taxIdentify != null  and taxIdentify != '' "> and t.tax_identify = #{taxIdentify}</if>
        <if test="balaType != null  and balaType != '' "> and t.bala_type = #{balaType}</if>
        <if test="billingType != null  and billingType != '' "> and t.billing_type = #{billingType}</if>
        <if test="bank != null  and bank != '' "> and t.bank = #{bank}</if>
        <if test="discountRate != null "> and t.discount_rate = #{discountRate}</if>
        <if test="accPeriodAhead != null "> and t.acc_period_ahead = #{accPeriodAhead}</if>
        <if test="billingAhead != null "> and t.billing_ahead = #{billingAhead}</if>
        <if test="creditAmount != null "> and t.credit_amount = #{creditAmount}</if>
        <if test="accountName != null  and accountName != '' "> and t.account_name = #{accountName}</if>
        <if test="bankAccount != null  and bankAccount != '' "> and t.bank_account = #{bankAccount}</if>
        <if test="registerAddr != null  and registerAddr != '' "> and t.register_addr = #{registerAddr}</if>
        <if test="billingRule != null "> and t.billing_rule = #{billingRule}</if>
        <if test="legalRepresent != null  and legalRepresent != '' "> and t.legal_represent = #{legalRepresent}</if>
        <if test="registerCapital != null "> and t.register_capital = #{registerCapital}</if>
        <if test="addressee != null  and addressee != '' "> and t.addressee = #{addressee}</if>
        <if test="addresseeContact != null  and addresseeContact != '' "> and t.addressee_contact = #{addresseeContact}</if>
        <if test="addresseeAddr != null  and addresseeAddr != '' "> and t.addressee_addr = #{addresseeAddr}</if>
        <if test="website != null  and website != '' "> and t.website = #{website}</if>
        <if test="memo != null  and memo != '' "> and t.memo = #{memo}</if>
        <if test="custActivity != null "> and t.cust_activity = #{custActivity}</if>
        <if test="regUserId != null  and regUserId != '' "> and t.reg_user_id = #{regUserId}</if>
        <if test="regScrId != null  and regScrId != '' "> and t.reg_scr_id = #{regScrId}</if>
        <if test="corScrId != null  and corScrId != '' "> and t.cor_scr_id = #{corScrId}</if>
        <if test="businesslicense != null  and businesslicense != '' "> and t.businesslicense = #{businesslicense}</if>

        <if test="regDate != null">
            AND t.REG_DATE <![CDATA[   >=  ]]> #{regDate}
        </if>
        <if test="corDate != null">
            AND t.REG_DATE <![CDATA[   <=  ]]> #{corDate}
        </if>
        <!--查询修改人-->
        <if test="corUserId != null and corUserId.trim() != ''">
            <bind name="corUserId" value="corUserId + '%'"/>
            and user2.user_name like #{corUserId}
        </if>
        <if test="checkStatus != null "> and t.check_status = #{checkStatus}</if>
        <if test="ifBargain != null "> and t.if_Bargain = #{ifBargain}</if>
        <!--客户级别-->
        <if test="customerLevel != null "> and t.customer_level = #{customerLevel}</if>
        <!-- 客户来源 -->
        <if test="customerSource != null and  customerSource != ''"> and t.customer_source = #{customerSource}</if>
        <!-- 没有一个月的概念了  直接使用预警时间筛选 -->

        <!-- 启用/停用 -->
        <if test="isEnabled != null and isEnabled != '' or isEnabled == 0">and t.is_enabled = #{isEnabled}</if>

        <if test="isExistContract  == 0"> and exists (
            select
            bus.ID
            from
            M_CONTRACT_BUSINESS bus
            left join M_CONTRACT_BUSINESS_CUST busCust on bus.ID=busCust.CONTRACT_BUSINESS_ID and busCust.DEL_FLAG=0
            where bus.del_flag = 0
            and busCust.CUSTOMER_ID = t.customer_id
            )</if>
        <if test="isExistContract  == 1"> and not exists (
            select
            bus.ID
            from
            M_CONTRACT_BUSINESS bus
            left join M_CONTRACT_BUSINESS_CUST busCust on bus.ID=busCust.CONTRACT_BUSINESS_ID and busCust.DEL_FLAG=0
            where bus.del_flag = 0
            and busCust.CUSTOMER_ID = t.customer_id
            )</if>

        <!-- 筛选临期  一个月 -->
<!--        <if test="temporary == 1">-->
<!--            and t3.INVALID_DATE  <![CDATA[ <= ]]> add_months ( SYSDATE, 1)-->
<!--            and t3.INVALID_DATE  <![CDATA[ > ]]> SYSDATE-->
<!--        </if>-->
        <!-- 筛选到期 -->
<!--        <if test="driver.temporary == 2">-->
<!--            and t1.validperiod_to <![CDATA[ < ]]> NOW()-->
<!--        </if>-->

        <if test="salesId != null and salesId !='' "> and t.sales_id = #{salesId}</if>

        <if test="sourceType != null"> and t.source_type = #{sourceType}</if>

<!--        <if test="params.type != null and params.type =='all' ">-->
<!--            and t.REG_SCR_ID != 'importAllData' and NVL(t.SALES_ID, ' ') != '2a828327651f4e1cbbc798d8c56ec99c'-->
<!--        </if>-->
<!--        <if test="params.type != null and params.type =='fleet' ">-->
<!--            and t.SALES_ID = '2a828327651f4e1cbbc798d8c56ec99c'-->
<!--        </if>-->
<!--        <if test="params.type != null and params.type =='park' ">-->
<!--            and t.REG_SCR_ID = 'importAllData'-->
<!--        </if>-->
        <if test="billingPayable != null  and billingPayable != '' ">
            <bind name="billingPayable" value="billingPayable + '%'"/>
            and exists(select * from m_cust_billing a
                        where a.DEL_FLAG=0
                            and a.CUSTOMER_ID=t.CUSTOMER_ID
                            and a.BILLING_PAYABLE like #{billingPayable})
        </if>

        <if test="mgmtDeptId != null and mgmtDeptId != ''">
            and exists (
                select 1 from sys_dept a
                where a.del_flag = 0
                  and (a.dept_id = #{mgmtDeptId} or ',' || a.ancestors || ',' LIKE '%,' || #{mgmtDeptId} || ',%')
                  and a.dept_id = t.sales_dept
            )
        </if>
        <if test="opsDeptId != null and opsDeptId != ''">
            and exists (
                select 1 from sys_dept a
                where a.del_flag = 0
                  and (a.dept_id = #{opsDeptId} or ',' || a.ancestors || ',' LIKE '%,' || #{opsDeptId} || ',%')
                  and a.dept_id = t.sales_dept
            )
        </if>

        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by t.COR_DATE desc,t.CUST_NAME DESC
        <if test="temporary == 1">)
            where if_Bargain = 0 and contract_Need_Type = 0
            and ( (INVALID_DATE <![CDATA[ <= ]]> to_char(add_months ( SYSDATE, 1),'yyyy-MM-dd')  and INVALID_DATE != '长期有效' ) or INVALID_DATE is null or INVALID_DATE = '有合同无有效期' )
        </if>
    </select>

    <select id="selectTemporaryCount" parameterType="Client"  resultType="Integer">
      select count(1) from (
        SELECT
        T.CUSTOMER_ID,
        T.CUST_CODE,
        T.CUST_NAME,
        T.CUST_ABBR,
        T.CUST_TYPE,
        T.SALES_DEPT,
        T.BALA_DEPT,
        T.STATION_DEPT,
        T.BALA_CORP,
        T.PSNDOC,
        T.PSNCONTACT,
        T.PROVINCE_ID,
        T.CITY_ID,
        T.AREA_ID,
        T.ADDRESS,
        T.CONTACT,
        T.CONTACT_POST,
        T.PHONE,
        T.MOBILE,
        T.EMAIL,
        T.FAX,
        T.ZIPCODE,
        T.BILLING_CORP,
        T.BILLING_DATE,
        T.TAX_IDENTIFY,
        T.BALA_TYPE,
        T.BILLING_TYPE,
        <!--        T.BILLING_PAYABLE,-->
        T.BANK,
        T.DISCOUNT_RATE,
        T.ACC_PERIOD_AHEAD,
        T.BILLING_AHEAD,
        T.CREDIT_AMOUNT,
        T.ACCOUNT_NAME,
        T.BANK_ACCOUNT,
        T.REGISTER_ADDR,
        T.BILLING_RULE,
        T.LEGAL_REPRESENT,
        T.REGISTER_CAPITAL,
        T.ADDRESSEE,
        T.ADDRESSEE_CONTACT,
        T.ADDRESSEE_ADDR,
        T.WEBSITE,
        T.MEMO,
        T.REG_DATE,
        T.COR_DATE,
        T.BUSINESSLICENSE,
        T.PSNDOC_NAME,
        T.LEGAL_CARD,
        T.CUSTOMER_TYPE,
        T.PAYMENT_DAYS,
        T.PROVINCE_NAME,
        T.CITY_NAME,
        T.AREA_NAME,
        T.BANK_ID,
        T.APP_DELI_CONTACT,
        T.APP_DELI_MOBILE,
        T.CHECK_STATUS,
        T.CHECK_DATE,
        T.CHECK_MAN,
        t.crt_guide_price,
        t.customer_level,
        t.tariff,
        t.is_need_receipt,
        t.is_need_trace,
        t.adjustment,
        T.IS_ENABLED,

        t.INVOICE_DAYS,
        t.COLLECTION_DAYS,
        t.IS_LOCK_OTHER_FEE,
        t.special_date,
        t.ENTERPRISE_NATURE,
        t.CUSTOMER_SOURCE,
        t.handling_charges,
        t.handling_charges_type,
        t.ENABLE_CONTRACT_PRICE,
        t.if_Bargain,
        t.reference_rate,
        t.contract_price_type,

        T1.DEPT_NAME sales_dept_name,
        T2.DEPT_NAME bala_dept_name,
        T3.DEPT_NAME station_dept_name,
        sys_user.user_name cor_user_id,
        user2.user_name reg_user_id,

        (
        select

        case
        when count(1) = 0 then null
        when max(bus.LONG_TERM_EFFECTIVE) = '1' then '长期有效'
        else
        case
        when max(bus.end_date) is null and max(bus.EXTENSION_DATE) is null and max(bus.SYSTEM_EXTENSION_DATE) is null then '有合同无有效期'
        else to_char(
        greatest(
        COALESCE(max(bus.end_date), DATE '0001-01-01'),
        COALESCE(max(bus.EXTENSION_DATE), DATE '0001-01-01'),
        COALESCE(max(bus.SYSTEM_EXTENSION_DATE), DATE '0001-01-01')
        ),
        'yyyy-mm-dd'
        )
        end
        end invalid_date
        from M_CONTRACT_BUSINESS bus
        left join M_CONTRACT_BUSINESS_CUST busCust on bus.ID=busCust.CONTRACT_BUSINESS_ID and busCust.DEL_FLAG=0
        where bus.del_flag = 0 and busCust.CUSTOMER_ID = t.customer_id
        ) invalid_date,
        t9.invoiceSalesDeptName,
        t.notice_file_id,
        t5.DEPT_ID      ops_dept_id,
        t5.DEPT_NAME    ops_dept_name,
        t6.DEPT_ID      mgmt_dept_id,
        t6.DEPT_NAME    mgmt_dept_name,
        t.disabled_time,
        t10.GROUP_NAME GROUP_NAME,
        t.CONTRACT_NEED_TYPE
        FROM M_CUSTOMER T
        LEFT JOIN SYS_DEPT T1 ON T.SALES_DEPT = T1.DEPT_ID and  T1.DEL_FLAG = 0
        LEFT JOIN SYS_DEPT T2  ON T.BALA_DEPT = T2.DEPT_ID and T2.DEL_FLAG = 0
        LEFT JOIN SYS_DEPT T3 ON T.STATION_DEPT = T3.DEPT_ID and T3.DEL_FLAG = 0
        left join sys_user on sys_user.USER_ID = t.cor_user_id and sys_user.DEL_FLAG = 0
        left join sys_user user2 on user2.USER_ID = t.reg_user_id and user2.del_flag = 0

        LEFT JOIN SYS_DEPT T5  ON T1.PARENT_ID = T5.DEPT_ID     <!--运营部-->
        LEFT JOIN SYS_DEPT T6 ON T5.PARENT_ID = T6.DEPT_ID      <!--管理部-->
        left join (
        select invoice.customer_id customer_id,
        listagg(distinct dept.DEPT_NAME, ',') WITHIN GROUP(ORDER BY dept.DEPT_NAME) invoiceSalesDeptName
        from T_RECEIVE_DETAIL receive
        left join t_invoice invoice on invoice.invoice_id = receive.invoice_id
        left join sys_dept dept on dept.DEPT_ID = invoice.SALES_DEPT
        where receive.DEL_FLAG = 0
        and receive.IS_NTOCC = 0
        and receive.VBILLSTATUS != 4
        and receive.UNGOT_AMOUNT != 0
        group by invoice.customer_id
        ) t9 on t9.customer_id = t.customer_id
        left join (select a.customer_id,a1.group_id,a1.group_name
        from m_group_cust a
        left join m_group a1 on a.group_id = a1.group_id
        where a.del_flag=0 and a1.del_flag=0) t10 on t10.customer_id = t.customer_id
        where t.DEL_FLAG = 0
        <if test="customerId != null  and customerId != '' "> and t.customer_id = #{customerId}</if>
        <if test="delDate != null "> and t.del_date = #{delDate}</if>
        <if test="custCode != null  and custCode != '' "> and t.cust_code = #{custCode}</if>
        <if test="custName != null and custName.trim() != ''">
            <bind name="custName" value="custName + '%'"/>
            and (t.CUST_NAME like #{custName} or t.cust_abbr like #{custName} or t10.GROUP_NAME like #{custName})
        </if>
        <if test="psndocName != null and psndocName.trim() != ''">
            <bind name="psndocName" value="psndocName + '%'"/>
            and t.PSNDOC_NAME like #{psndocName}
        </if>
        <if test="custAbbr != null  and custAbbr != '' ">
            <bind name="custAbbr" value="custAbbr + '%'"/>
            and t.cust_abbr like #{custAbbr}
        </if>
        <if test="custType != null "> and t.cust_type = #{custType}</if>
        <if test="ifAuthentication != null  and ifAuthentication != '' "> and t.if_authentication = #{ifAuthentication}</if>
        <if test="salesDept != null  and salesDept != '' "> and t.sales_dept = #{salesDept}</if>
        <if test="balaDept != null  and balaDept != '' "> and t.bala_dept = #{balaDept}</if>
        <if test="stationDept != null  and stationDept != '' "> and t.station_dept = #{stationDept}</if>
        <if test="balaCorp != null  and balaCorp != '' "> and t.bala_corp = #{balaCorp}</if>
        <if test="operateCorp != null  and operateCorp != '' "> and t.operate_corp = #{operateCorp}</if>
        <if test="psndoc != null  and psndoc != '' ">
            <bind name="psndoc" value="psndoc + '%'"/>
            and t.psndoc like #{psndoc}
        </if>
        <if test="psncontact != null  and psncontact != '' "> and t.psncontact = #{psncontact}</if>
        <if test="provinceId != null  and provinceId != '' "> and t.province_id = #{provinceId}</if>
        <if test="cityId != null  and cityId != '' "> and t.city_id = #{cityId}</if>
        <if test="areaId != null  and areaId != '' "> and t.area_id = #{areaId}</if>
        <if test="address != null  and address != '' ">
            <bind name="address" value="address + '%'"/>
            and t.address like #{address}
        </if>
        <if test="contact != null  and contact != '' ">
            <bind name="contact" value="contact + '%'"/>
            and (t.contact like #{contact} or t.phone like #{contact})
        </if>
        <if test="contactPost != null  and contactPost != '' "> and t.contact_post = #{contactPost}</if>
        <if test="phone != null  and phone != '' ">
            <bind name="phone" value="phone + '%'"/>
            and t.phone like #{phone}
        </if>
        <if test="mobile != null  and mobile != '' "> and t.mobile = #{mobile}</if>
        <if test="email != null  and email != '' "> and t.email = #{email}</if>
        <if test="fax != null  and fax != '' "> and t.fax = #{fax}</if>
        <if test="zipcode != null  and zipcode != '' "> and t.zipcode = #{zipcode}</if>
        <if test="billingCorp != null  and billingCorp != '' "> and t.billing_corp = #{billingCorp}</if>
        <if test="billingDate != null "> and t.billing_date = #{billingDate}</if>
        <if test="taxIdentify != null  and taxIdentify != '' "> and t.tax_identify = #{taxIdentify}</if>
        <if test="balaType != null  and balaType != '' "> and t.bala_type = #{balaType}</if>
        <if test="billingType != null  and billingType != '' "> and t.billing_type = #{billingType}</if>
        <if test="bank != null  and bank != '' "> and t.bank = #{bank}</if>
        <if test="discountRate != null "> and t.discount_rate = #{discountRate}</if>
        <if test="accPeriodAhead != null "> and t.acc_period_ahead = #{accPeriodAhead}</if>
        <if test="billingAhead != null "> and t.billing_ahead = #{billingAhead}</if>
        <if test="creditAmount != null "> and t.credit_amount = #{creditAmount}</if>
        <if test="accountName != null  and accountName != '' "> and t.account_name = #{accountName}</if>
        <if test="bankAccount != null  and bankAccount != '' "> and t.bank_account = #{bankAccount}</if>
        <if test="registerAddr != null  and registerAddr != '' "> and t.register_addr = #{registerAddr}</if>
        <if test="billingRule != null "> and t.billing_rule = #{billingRule}</if>
        <if test="legalRepresent != null  and legalRepresent != '' "> and t.legal_represent = #{legalRepresent}</if>
        <if test="registerCapital != null "> and t.register_capital = #{registerCapital}</if>
        <if test="addressee != null  and addressee != '' "> and t.addressee = #{addressee}</if>
        <if test="addresseeContact != null  and addresseeContact != '' "> and t.addressee_contact = #{addresseeContact}</if>
        <if test="addresseeAddr != null  and addresseeAddr != '' "> and t.addressee_addr = #{addresseeAddr}</if>
        <if test="website != null  and website != '' "> and t.website = #{website}</if>
        <if test="memo != null  and memo != '' "> and t.memo = #{memo}</if>
        <if test="custActivity != null "> and t.cust_activity = #{custActivity}</if>
        <if test="regUserId != null  and regUserId != '' "> and t.reg_user_id = #{regUserId}</if>
        <if test="regScrId != null  and regScrId != '' "> and t.reg_scr_id = #{regScrId}</if>
        <if test="corScrId != null  and corScrId != '' "> and t.cor_scr_id = #{corScrId}</if>
        <if test="businesslicense != null  and businesslicense != '' "> and t.businesslicense = #{businesslicense}</if>

        <if test="regDate != null">
            AND t.REG_DATE <![CDATA[   >=  ]]> #{regDate}
        </if>
        <if test="corDate != null">
            AND t.REG_DATE <![CDATA[   <=  ]]> #{corDate}
        </if>
        <!--查询修改人-->
        <if test="corUserId != null and corUserId.trim() != ''">
            <bind name="corUserId" value="corUserId + '%'"/>
            and user2.user_name like #{corUserId}
        </if>
        <if test="checkStatus != null "> and t.check_status = #{checkStatus}</if>
        <if test="ifBargain != null "> and t.if_Bargain = #{ifBargain}</if>
        <!--客户级别-->
        <if test="customerLevel != null "> and t.customer_level = #{customerLevel}</if>
        <!-- 客户来源 -->
        <if test="customerSource != null and  customerSource != ''"> and t.customer_source = #{customerSource}</if>
        <!-- 没有一个月的概念了  直接使用预警时间筛选 -->

        <!-- 启用/停用 -->
        <if test="isEnabled != null and isEnabled != '' or isEnabled == 0">and t.is_enabled = #{isEnabled}</if>

        <if test="isExistContract  == 0"> and exists (
            select
            bus.ID
            from
            M_CONTRACT_BUSINESS bus
            left join M_CONTRACT_BUSINESS_CUST busCust on bus.ID=busCust.CONTRACT_BUSINESS_ID and busCust.DEL_FLAG=0
            where bus.del_flag = 0
            and busCust.CUSTOMER_ID = t.customer_id
            )</if>
        <if test="isExistContract  == 1"> and not exists (
            select
            bus.ID
            from
            M_CONTRACT_BUSINESS bus
            left join M_CONTRACT_BUSINESS_CUST busCust on bus.ID=busCust.CONTRACT_BUSINESS_ID and busCust.DEL_FLAG=0
            where bus.del_flag = 0
            and busCust.CUSTOMER_ID = t.customer_id
            )</if>


        <if test="salesId != null and salesId !='' "> and t.sales_id = #{salesId}</if>

        <if test="sourceType != null"> and t.source_type = #{sourceType}</if>


        <if test="billingPayable != null  and billingPayable != '' ">
            <bind name="billingPayable" value="billingPayable + '%'"/>
            and exists(select * from m_cust_billing a
            where a.DEL_FLAG=0
            and a.CUSTOMER_ID=t.CUSTOMER_ID
            and a.BILLING_PAYABLE like #{billingPayable})
        </if>

        <if test="mgmtDeptId != null and mgmtDeptId != ''">
            and exists (
            select 1 from sys_dept a
            where a.del_flag = 0
            and (a.dept_id = #{mgmtDeptId} or ',' || a.ancestors || ',' LIKE '%,' || #{mgmtDeptId} || ',%')
            and a.dept_id = t.sales_dept
            )
        </if>
        <if test="opsDeptId != null and opsDeptId != ''">
            and exists (
            select 1 from sys_dept a
            where a.del_flag = 0
            and (a.dept_id = #{opsDeptId} or ',' || a.ancestors || ',' LIKE '%,' || #{opsDeptId} || ',%')
            and a.dept_id = t.sales_dept
            )
        </if>

        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by t.COR_DATE desc,t.CUST_NAME DESC
        )
            where if_Bargain = 0 and contract_Need_Type = 0
              and ( (INVALID_DATE <![CDATA[ <= ]]> to_char(add_months ( SYSDATE, 1),'yyyy-MM-dd')  and INVALID_DATE != '长期有效' ) or INVALID_DATE is null or INVALID_DATE = '有合同无有效期')

    </select>

    <!-- 查询ClientPopupVo对象-->
    <select id="selectClientByGroupIdList" parameterType="Client" resultMap="ClientPopupVOResult">
        SELECT
            T.CUSTOMER_ID,
            T.CUST_CODE,
            T.CUST_NAME,
            T.CUST_ABBR,
            T.CUST_TYPE,
            T.SALES_DEPT,
            T.BALA_DEPT,
            T.STATION_DEPT,
            T.BALA_CORP,
            T.PSNDOC,
            T.PSNCONTACT,
            T.PROVINCE_ID,
            T.CITY_ID,
            T.AREA_ID,
            T.ADDRESS,
            T.CONTACT,
            T.CONTACT_POST,
            T.PHONE,
            T.MOBILE,
            T.EMAIL,
            T.FAX,
            T.ZIPCODE,
            T.BILLING_CORP,
            T.BILLING_DATE,
            T.TAX_IDENTIFY,
            T.BALA_TYPE,
            T.BILLING_TYPE,
            T.BILLING_PAYABLE,
            T.BANK,
            T.DISCOUNT_RATE,
            T.ACC_PERIOD_AHEAD,
            T.BILLING_AHEAD,
            T.CREDIT_AMOUNT,
            T.ACCOUNT_NAME,
            T.BANK_ACCOUNT,
            T.REGISTER_ADDR,
            T.BILLING_RULE,
            T.LEGAL_REPRESENT,
            T.REGISTER_CAPITAL,
            T.ADDRESSEE,
            T.ADDRESSEE_CONTACT,
            T.ADDRESSEE_ADDR,
            T.WEBSITE,
            T.MEMO,
            T.REG_DATE,
            T.COR_DATE,
            T.BUSINESSLICENSE,
            T.PSNDOC_NAME,
            T.LEGAL_CARD,
            T.CUSTOMER_TYPE,
            T.PAYMENT_DAYS,
            T.PROVINCE_NAME,
            T.CITY_NAME,
            T.AREA_NAME,
            T.BANK_ID,
            T.APP_DELI_CONTACT,
            T.APP_DELI_MOBILE,
            T.CHECK_STATUS,
            T.CHECK_DATE,
            T.CHECK_MAN,
            t.crt_guide_price,
            t.customer_level,
            t.tariff,
            T1.DEPT_NAME sales_dept_name,
            T2.DEPT_NAME bala_dept_name,
            T3.DEPT_NAME station_dept_name,
            sys_user.user_name cor_user_id,
            user2.user_name reg_user_id
        FROM M_CUSTOMER T
        LEFT JOIN SYS_DEPT T1
          ON T.SALES_DEPT = T1.DEPT_ID and  T1.DEL_FLAG = 0
        LEFT JOIN SYS_DEPT T2
          ON T.BALA_DEPT = T2.DEPT_ID and T2.DEL_FLAG = 0
        LEFT JOIN SYS_DEPT T3
          ON T.STATION_DEPT = T3.DEPT_ID and T3.DEL_FLAG = 0
        left join sys_user on sys_user.USER_ID = t.cor_user_id and sys_user.DEL_FLAG = 0
        left join sys_user user2 on user2.USER_ID = t.reg_user_id and user2.del_flag = 0
        left join M_GROUP_CUST gcust
          on gcust.customer_id = t.customer_id and gcust.del_flag = 0
        where
          gcust.GROUP_ID = #{groupId}
        and t.DEL_FLAG = 0
        <if test="customerId != null  and customerId != '' "> and t.customer_id = #{customerId}</if>
        <if test="delDate != null "> and t.del_date = #{delDate}</if>
        <if test="custCode != null  and custCode != '' "> and t.cust_code = #{custCode}</if>
        <if test="custName != null and custName.trim() != ''">
            <bind name="custName" value="custName + '%'"/>
            and t.CUST_NAME like #{custName}
        </if>
        <if test="psndocName != null and psndocName.trim() != ''">
            <bind name="psndocName" value="psndocName + '%'"/>
            and t.PSNDOC_NAME like #{psndocName}
        </if>
        <if test="custAbbr != null  and custAbbr != '' "> and t.cust_abbr = #{custAbbr}</if>
        <if test="custType != null "> and t.cust_type = #{custType}</if>
        <if test="ifAuthentication != null  and ifAuthentication != '' "> and t.if_authentication = #{ifAuthentication}</if>
        <if test="salesDept != null  and salesDept != '' "> and t.sales_dept = #{salesDept}</if>
        <if test="balaDept != null  and balaDept != '' "> and t.bala_dept = #{balaDept}</if>
        <if test="stationDept != null  and stationDept != '' "> and t.station_dept = #{stationDept}</if>
        <if test="balaCorp != null  and balaCorp != '' "> and t.bala_corp = #{balaCorp}</if>
        <if test="psndoc != null  and psndoc != '' ">
            <bind name="psndoc" value="psndoc + '%'"/>
            and t.psndoc like #{psndoc}
        </if>
        <if test="psncontact != null  and psncontact != '' "> and t.psncontact = #{psncontact}</if>
        <if test="provinceId != null  and provinceId != '' "> and t.province_id = #{provinceId}</if>
        <if test="cityId != null  and cityId != '' "> and t.city_id = #{cityId}</if>
        <if test="areaId != null  and areaId != '' "> and t.area_id = #{areaId}</if>
        <if test="address != null  and address != '' ">
            <bind name="address" value="address + '%'"/>
            and t.address like #{address}
        </if>
        <if test="contact != null  and contact != '' ">
            <bind name="contact" value="contact + '%'"/>
            and t.contact like #{contact}
        </if>
        <if test="contactPost != null  and contactPost != '' "> and t.contact_post = #{contactPost}</if>
        <if test="phone != null  and phone != '' ">
            <bind name="phone" value="phone + '%'"/>
            and t.phone like #{phone}
        </if>
        <if test="mobile != null  and mobile != '' "> and t.mobile = #{mobile}</if>
        <if test="email != null  and email != '' "> and t.email = #{email}</if>
        <if test="fax != null  and fax != '' "> and t.fax = #{fax}</if>
        <if test="zipcode != null  and zipcode != '' "> and t.zipcode = #{zipcode}</if>
        <if test="billingCorp != null  and billingCorp != '' "> and t.billing_corp = #{billingCorp}</if>
        <if test="billingDate != null "> and t.billing_date = #{billingDate}</if>
        <if test="taxIdentify != null  and taxIdentify != '' "> and t.tax_identify = #{taxIdentify}</if>
        <if test="balaType != null  and balaType != '' "> and t.bala_type = #{balaType}</if>
        <if test="billingType != null  and billingType != '' "> and t.billing_type = #{billingType}</if>
        <if test="billingPayable != null  and billingPayable != '' "> and t.billing_payable = #{billingPayable}</if>
        <if test="bank != null  and bank != '' "> and t.bank = #{bank}</if>
        <if test="discountRate != null "> and t.discount_rate = #{discountRate}</if>
        <if test="accPeriodAhead != null "> and t.acc_period_ahead = #{accPeriodAhead}</if>
        <if test="billingAhead != null "> and t.billing_ahead = #{billingAhead}</if>
        <if test="creditAmount != null "> and t.credit_amount = #{creditAmount}</if>
        <if test="accountName != null  and accountName != '' "> and t.account_name = #{accountName}</if>
        <if test="bankAccount != null  and bankAccount != '' "> and t.bank_account = #{bankAccount}</if>
        <if test="registerAddr != null  and registerAddr != '' "> and t.register_addr = #{registerAddr}</if>
        <if test="billingRule != null "> and t.billing_rule = #{billingRule}</if>
        <if test="legalRepresent != null  and legalRepresent != '' "> and t.legal_represent = #{legalRepresent}</if>
        <if test="registerCapital != null "> and t.register_capital = #{registerCapital}</if>
        <if test="addressee != null  and addressee != '' "> and t.addressee = #{addressee}</if>
        <if test="addresseeContact != null  and addresseeContact != '' "> and t.addressee_contact = #{addresseeContact}</if>
        <if test="addresseeAddr != null  and addresseeAddr != '' "> and t.addressee_addr = #{addresseeAddr}</if>
        <if test="website != null  and website != '' "> and t.website = #{website}</if>
        <if test="memo != null  and memo != '' "> and t.memo = #{memo}</if>
        <if test="custActivity != null "> and t.cust_activity = #{custActivity}</if>
        <if test="regUserId != null  and regUserId != '' "> and t.reg_user_id = #{regUserId}</if>
        <if test="regScrId != null  and regScrId != '' "> and t.reg_scr_id = #{regScrId}</if>
        <if test="corScrId != null  and corScrId != '' "> and t.cor_scr_id = #{corScrId}</if>
        <if test="businesslicense != null  and businesslicense != '' "> and t.businesslicense = #{businesslicense}</if>

        <if test="regDate != null">
            AND t.REG_DATE <![CDATA[   >=  ]]> #{regDate}
        </if>
        <if test="corDate != null">
            AND t.REG_DATE <![CDATA[   <=  ]]> #{corDate}
        </if>
        <!--查询修改人-->
        <if test="corUserId != null and corUserId.trim() != ''">
            <bind name="corUserId" value="corUserId + '%'"/>
            and user2.user_name like #{corUserId}
        </if>
        <if test="checkStatus != null "> and t.check_status = #{checkStatus}</if>
        <!--客户级别-->
        <if test="customerLevel != null "> and t.customer_level = #{customerLevel}</if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by COR_DATE desc,CUST_NAME DESC
    </select>

    <!--获取客户list-->
    <select id="selectClientListByIdList" parameterType="Client" resultMap="ClientResult">
        SELECT
            m.CUSTOMER_ID,
            m.CUST_CODE,
            m.CUST_NAME,
            m.CUST_ABBR,
            m.CUST_TYPE,
            m.IF_AUTHENTICATION,
            deptA.DEPT_NAME AS SALES_DEPT,
            deptB.DEPT_NAME AS BALA_DEPT,
            deptC.DEPT_NAME AS STATION_DEPT,
            userA.LOGIN_NAME AS PSNDOC,
            m.BALA_CORP,
            m.PSNDOC,
            m.PSNCONTACT,
            m.PROVINCE_ID,
            m.CITY_ID,
            m.AREA_ID,
            m.ADDRESS,
            m.CONTACT,
            m.CONTACT_POST,
            m.PHONE,
            m.MOBILE,
            m.EMAIL,
            m.FAX,
            m.ZIPCODE,
            m.BILLING_CORP,
            m.BILLING_DATE,
            m.TAX_IDENTIFY,
            m.BALA_TYPE,
            m.BILLING_TYPE,
            m.BILLING_PAYABLE,
            m.BANK,
            m.DISCOUNT_RATE,
            m.ACC_PERIOD_AHEAD,
            m.BILLING_AHEAD,
            m.CREDIT_AMOUNT,
            m.ACCOUNT_NAME,
            m.BANK_ACCOUNT,
            m.REGISTER_ADDR,
            m.BILLING_RULE,
            m.LEGAL_REPRESENT,
            m.REGISTER_CAPITAL,
            m.ADDRESSEE,
            m.ADDRESSEE_CONTACT,
            m.ADDRESSEE_ADDR,
            m.WEBSITE,
            m.MEMO,
            m.CUST_ACTIVITY,
            m.businessLicense,
            m.payment_days,
            m.customer_level,
            m.is_need_receipt,
            m.INVOICE_DAYS,
            m.COLLECTION_DAYS,
            m.CRT_GUIDE_PRICE,
            m.customer_source,
            m.enterprise_nature,
            m.APP_DELI_CONTACT,
            m.APP_DELI_MOBILE,
            userB.user_name reg_user_Id,
            m.is_special_reference_price
        FROM
            M_CUSTOMER m
                LEFT JOIN sys_dept deptA ON m.SALES_DEPT = deptA.dept_id
                LEFT JOIN sys_dept deptB ON m.BALA_DEPT = deptB.dept_id
                LEFT JOIN sys_dept deptC ON m.STATION_DEPT = deptC.dept_id
                LEFT JOIN sys_user userA ON m.PSNDOC = userA.user_id
                left join sys_user userB on m.reg_user_id = userB.USER_ID
        where
              m.DEL_FLAG = 0
            and m.CUSTOMER_ID in
        <foreach item="ids" collection="list" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </select>

    <!--获取客户照片路径-->
    <select id="getCustPicPath" resultMap="CustPicResult">
        select
            pic_type,
            appendix_id,
            file_name,
            file_path,
            FILE_ID,
            PIC_TYPE
        from M_CUST_PIC
                 join sys_upload_file
                      on M_CUST_PIC.appendix_id = sys_upload_file.tid
        where sys_upload_file.del_flag=0
          and M_CUST_PIC.CUSTOMER_ID = #{clientId} and M_CUST_PIC.DEL_FLAG != 1
    </select>

    <!--根据客户ID删除照片-->
    <update id="deleteCustPic">
        UPDATE M_CUST_PIC
        SET DEL_FLAG = 1,
            DEL_DATE = sysdate
        WHERE
            CUSTOMER_ID = #{customerId}
    </update>

    <!--根据客户ID及照片类型删除 对应客户照片-->
    <update id="deleteCustPicByType">
        UPDATE M_CUST_PIC
        SET DEL_FLAG = 1,
            DEL_DATE = sysdate
        WHERE
            CUSTOMER_ID = #{customerId}
          and PIC_TYPE = #{picType}
    </update>

    <!--根据客户id获取 客户的默认结算客户-->
    <select id="getDefaultCustBalaByCustomerId" resultMap="ClientResult">
        <include refid="selectCustomerVo"/>
        WHERE CUSTOMER_ID = (SELECT RELATED_CUST_ID
                               FROM M_CUST_BALA
                              WHERE CUSTOMER_ID = #{customerId}
                                AND IS_DEFAULT = '1'
                                AND DEL_FLAG = 0)
    </select>

    <update id="updateClientAll">
        update m_customer
        <trim prefix="SET" suffixOverrides=",">
            <if test="custName != null  ">cust_name = #{custName},</if>
            <if test="custAbbr != null   ">cust_abbr = #{custAbbr},</if>
            <if test="custType != null  ">cust_type = #{custType},</if>
            <if test="ifAuthentication != null  ">if_authentication = #{ifAuthentication},</if>
            <if test="salesDept != null ">sales_dept = #{salesDept},</if>
            <if test="balaDept != null ">bala_dept = #{balaDept},</if>
            <if test="stationDept != null    ">station_dept = #{stationDept},</if>
            <if test="balaCorp != null   ">bala_corp = #{balaCorp},</if>
            <if test="psndoc != null   ">psndoc = #{psndoc},</if>
            <if test="psncontact != null  ">psncontact = #{psncontact},</if>
            <if test="provinceId != null  ">province_id = #{provinceId},</if>
            <if test="cityId != null   ">city_id = #{cityId},</if>
            <if test="areaId != null   ">area_id = #{areaId},</if>
            <if test="address != null   ">address = #{address},</if>
            <if test="contact != null   ">contact = #{contact},</if>
            <if test="contactPost != null   ">contact_post = #{contactPost},</if>
            <if test="phone != null    ">phone = #{phone},</if>
            <if test="mobile != null   ">mobile = #{mobile},</if>
            <if test="email != null   ">email = #{email},</if>
            <if test="fax != null   ">fax = #{fax},</if>
            <if test="zipcode != null   ">zipcode = #{zipcode},</if>
            <if test="billingCorp != null  ">billing_corp = #{billingCorp},</if>
            billing_date = #{billingDate,jdbcType=VARCHAR},
            <if test="taxIdentify != null   ">tax_identify = #{taxIdentify},</if>
            <if test="balaType != null  ">bala_type = #{balaType},</if>
            <if test="billingType != null  ">billing_type = #{billingType},</if>
            <if test="billingPayable != null   ">billing_payable = #{billingPayable},</if>
            <if test="bank != null ">bank = #{bank},</if>
            <if test="discountRate != null  ">discount_rate = #{discountRate},</if>
            <if test="accPeriodAhead != null  ">acc_period_ahead = #{accPeriodAhead},</if>
            <if test="billingAhead != null  ">billing_ahead = #{billingAhead},</if>
            <if test="creditAmount != null  ">credit_amount = #{creditAmount},</if>
            <if test="accountName != null  ">account_name = #{accountName},</if>
            <if test="bankAccount != null  ">bank_account = #{bankAccount},</if>
            <if test="registerAddr != null  ">register_addr = #{registerAddr},</if>
            <if test="billingRule != null  ">billing_rule = #{billingRule},</if>
            <if test="legalRepresent != null ">legal_represent = #{legalRepresent},</if>
           register_capital = #{registerCapital,jdbcType=VARCHAR},
            <if test="addressee != null  ">addressee = #{addressee},</if>
            <if test="addresseeContact != null  ">addressee_contact = #{addresseeContact},</if>
            <if test="addresseeAddr != null   ">addressee_addr = #{addresseeAddr},</if>
            <if test="website != null ">website = #{website},</if>
            <if test="memo != null  ">memo = #{memo},</if>
            <if test="custActivity != null  ">cust_activity = #{custActivity},</if>
            <if test="corUserId != null  ">cor_user_id = #{corUserId},</if>
            <if test="corDate != null  ">cor_date = #{corDate},</if>
            <if test="corScrId != null  ">cor_scr_id = #{corScrId},</if>
            <if test="businesslicense != null  ">businesslicense = #{businesslicense},</if>
            <if test="psndocName != null   ">psndoc_name = #{psndocName},</if>
            <if test="legalCard != null  ">legal_card = #{legalCard},</if>
            <if test="customerType != null  ">customer_type = #{customerType},</if>
            payment_days = #{paymentDays,jdbcType=VARCHAR},
            <if test="provinceName != null  ">province_name =  #{provinceName},</if>
            <if test="cityName != null ">city_name = #{cityName},</if>
            <if test="areaName != null ">area_name = #{areaName},</if>
            <if test="bankId != null ">bank_id = #{bankId},</if>
            <if test="appDeliContact != null  and appDeliContact != ''  ">app_deli_contact = #{appDeliContact},</if>
            <if test="appDeliMobile != null  and appDeliMobile != ''  ">app_deli_mobile = #{appDeliMobile},</if>
            <if test="crtGuidePrice != null ">crt_guide_price = #{crtGuidePrice},</if>
            <if test="checkStatus != null ">check_status = #{checkStatus},</if>
            <if test="customerLevel != null ">customer_level = #{customerLevel},</if>
            <if test="isNeedReceipt != null ">IS_NEED_RECEIPT = #{isNeedReceipt},</if>
            <if test="isNeedTrace != null ">IS_NEED_TRACE = #{isNeedTrace},</if>
            <if test="invoiceDays != null ">INVOICE_DAYS = #{invoiceDays},</if>
            <if test="collectionDays != null ">COLLECTION_DAYS = #{collectionDays},</if>
            <if test="adjustment != null ">adjustment = #{adjustment},</if>
            <if test="enterpriseNature != null ">ENTERPRISE_NATURE = #{enterpriseNature},</if>
            <if test="customerSource != null ">CUSTOMER_SOURCE = #{customerSource},</if>

            <if test="isEnabled != null and isEnabled != '' or isEnabled == 0">is_enabled = #{isEnabled},</if>
            <if test="handlingCharges != null ">handling_charges = #{handlingCharges},</if>
            <if test="handlingCharges == null ">handling_charges = null,</if>
            handling_charges_type = #{handlingChargesType},
            <if test="enableContractPrice != null and enableContractPrice!='' ">ENABLE_CONTRACT_PRICE = #{enableContractPrice},</if>
            <if test="specialDate != null and specialDate != ''">special_date = #{specialDate},</if>
            <if test="salesId != null">sales_id = #{salesId},</if>
            <if test="referenceRate != null">reference_rate = #{referenceRate},</if>
            <if test="operateCorp != null">operate_corp = #{operateCorp},</if>
            <if test="noticeFileId != null">notice_file_id = #{noticeFileId},</if>
            <if test="referrer != null">referrer = #{referrer},</if>
            subject_amount = #{subjectAmount,jdbcType=DECIMAL},
        </trim>
        where customer_id = #{customerId}
    </update>

    <update id="checkClientStatusById" parameterType="Client">
        update m_customer set
        check_status = #{checkStatus},
        check_date = #{checkDate},
        check_man = #{checkMan},
        cor_scr_id = #{corScrId}
        where customer_id = #{customerId} and check_status in (0,2)
    </update>

    <!-- 查询ClientPopupVo对象-->
    <select id="exportClientList" parameterType="Client" resultMap="ClientResult">
        SELECT T.CUSTOMER_ID,
        T.CUST_CODE,
        T.CUST_NAME,
        T.CUST_ABBR,
        T.CUST_TYPE,

        T.STATION_DEPT,
        T.BALA_CORP,
        T.PSNDOC,
        T.PSNCONTACT,
        T.PROVINCE_ID,
        T.CITY_ID,
        T.AREA_ID,
        T.ADDRESS,
        T.CONTACT,
        T.CONTACT_POST,
        T.PHONE,
        T.MOBILE,
        T.EMAIL,
        T.FAX,
        T.ZIPCODE,
        T.BILLING_CORP,
        T.BILLING_DATE,
        T.TAX_IDENTIFY,
        T.BALA_TYPE,
        T.BILLING_TYPE,
        T.BILLING_PAYABLE,
        T.BANK,
        T.DISCOUNT_RATE,
        T.ACC_PERIOD_AHEAD,
        T.BILLING_AHEAD,
        T.CREDIT_AMOUNT,
        T.ACCOUNT_NAME,
        T.BANK_ACCOUNT,
        T.REGISTER_ADDR,
        T.BILLING_RULE,
        T.LEGAL_REPRESENT,
        T.REGISTER_CAPITAL,
        T.ADDRESSEE,
        T.ADDRESSEE_CONTACT,
        T.ADDRESSEE_ADDR,
        T.WEBSITE,
        T.MEMO,
        T.REG_DATE,
        T.COR_DATE,
        T.BUSINESSLICENSE,
        T.PSNDOC_NAME,
        T.LEGAL_CARD,
        T.CUSTOMER_TYPE,
        T.PAYMENT_DAYS,
        T.PROVINCE_NAME,
        T.CITY_NAME,
        T.AREA_NAME,
        T.BANK_ID,
        T.APP_DELI_CONTACT,
        T.APP_DELI_MOBILE,
        T.CHECK_STATUS,
        T.CHECK_DATE,
        T.CHECK_MAN,
        T.CUSTOMER_LEVEL,
        T.is_need_receipt,
        t.INVOICE_DAYS,
        t.COLLECTION_DAYS,
        t.customer_source,
        t.enterprise_nature,

        T1.DEPT_NAME sales_dept,
        T2.DEPT_NAME bala_dept,
        T3.DEPT_NAME station_dept,
        sys_user.user_name cor_user_id,
        user2.user_name reg_user_id
        FROM M_CUSTOMER T
        LEFT JOIN SYS_DEPT T1
        ON T.SALES_DEPT = T1.DEPT_ID and  T1.DEL_FLAG = 0
        LEFT JOIN SYS_DEPT T2
        ON T.BALA_DEPT = T2.DEPT_ID and T2.DEL_FLAG = 0
        LEFT JOIN SYS_DEPT T3
        ON T.STATION_DEPT = T3.DEPT_ID and T3.DEL_FLAG = 0
        left join sys_user on sys_user.USER_ID = t.cor_user_id and sys_user.DEL_FLAG = 0
        left join sys_user user2 on user2.USER_ID = t.reg_user_id and user2.del_flag = 0
        where
        t.DEL_FLAG = 0
        order by COR_DATE desc,CUST_NAME DESC
    </select>
    <select id="selectClientByPhone" resultMap="ClientResult">
        <include refid="selectCustomerVo"/>
        where phone = #{phone} and del_flag = 0
    </select>
    <select id="listClientsAndDeleteThisMonth" resultMap="ClientResult">
       select customer_id,cust_name,cust_code,sales_dept from m_customer where del_flag = 0
       union all
       select customer_id,cust_name,cust_code,sales_dept from m_customer where del_flag = 1 and to_char(del_date,'yyyy-MM') = #{delDate}
    </select>
    <select id="selectCustomerByCustCode" resultType="com.ruoyi.tms.domain.client.Client">
        select
            customer_id customerId,
            cust_code custCode,
            cust_name custName,
            cust_abbr custAbbr,
            bala_dept balaDept,
            bala_corp balaCorp,
            operate_corp operateCorp
        from M_CUSTOMER
        where CUST_CODE = #{custCode} and rownum = 1
    </select>
    <select id="selectCustomerByCustName" resultMap="ClientResult">
        <include refid="selectCustomerVo"/>
        where cust_name = #{custName} and del_flag = 0 and rownum = 1
    </select>
    <select id="selectCustomerByCustAbbr" resultMap="ClientResult">
        <include refid="selectCustomerVo"/>
        where cust_abbr = #{custAbbr} and del_flag = 0 and rownum = 1 and customer_Source = 1 and is_enabled = 0
    </select>

    <select id="selectCustomerByCustAbbrNotCD" resultMap="ClientResult">
        SELECT
            cust.customer_id,
            cust.del_flag,
            cust.del_date,
            cust.cust_code,
            cust.cust_name,
            cust.cust_abbr,
            cust.cust_type,
            cust.if_authentication,
            cust.sales_dept,
            cust.bala_dept,
            cust.station_dept,
            cust.bala_corp,
            cust.psndoc,
            cust.psncontact,
            cust.province_id,
            cust.city_id,
            cust.area_id,
            cust.address,
            cust.contact,
            cust.contact_post,
            cust.phone,
            cust.mobile,
            cust.email,
            cust.fax,
            cust.zipcode,
            cust.billing_corp,
            cust.billing_date,
            cust.tax_identify,
            cust.bala_type,
            cust.billing_type,
            cust.billing_payable,
            cust.bank,
            cust.discount_rate,
            cust.acc_period_ahead,
            cust.billing_ahead,
            cust.credit_amount,
            cust.account_name,
            cust.bank_account,
            cust.register_addr,
            cust.billing_rule,
            cust.legal_represent,
            cust.register_capital,
            cust.addressee,
            cust.addressee_contact,
            cust.addressee_addr,
            cust.website,
            cust.memo,
            cust.cust_activity,
            cust.reg_user_id,
            cust.reg_date,
            cust.cor_user_id,
            cust.cor_date,
            cust.reg_scr_id,
            cust.cor_scr_id,
            cust.businesslicense,
            cust.psndoc_name,
            cust.payment_days,
            cust.bank_id,
            cust.crt_guide_price,
            cust.customer_level,
            cust.tariff,
            cust.is_need_receipt,
            cust.is_need_trace,
            cust.INVOICE_DAYS,
            cust.COLLECTION_DAYS,
            cust.IS_LOCK_OTHER_FEE,
            cust.ADJUSTMENT,
            cust.SPECIAL_DATE,
            cust.ENTERPRISE_NATURE,
            cust.CUSTOMER_SOURCE,
            cust.APP_DELI_CONTACT,
            cust.APP_DELI_MOBILE,
            cust.operate_corp,
            cust.IS_ENABLED,
            cust.SALES_ID
        FROM
            m_customer cust
                left join sys_dept dept on dept.dept_id = cust.sales_dept
                left join sys_dept dept_p on dept_p.dept_id = dept.parent_id
        where cust.cust_abbr = #{custAbbr} and cust.del_flag = 0 and rownum = 1 and cust.customer_Source = 1 and cust.is_enabled = 0
          and dept_p.parent_id != '501'
    </select>

    <select id="selectClientByCustNameAndSalesDept" resultMap="ClientResult">
        <include refid="selectCustomerVo"/>
        where cust_name = #{custName} and sales_dept = #{deptId} and del_flag = 0
    </select>

    <select id="selectCustBala" resultType="com.ruoyi.tms.domain.client.CustBala">
        select t.cust_bala_id       custBalaId,
               t.del_flag           delFlag,
               t.del_date           delDate,
               t.customer_id        customerId,
               t.related_cust_id    relatedCustId,
               t.is_default         isDefault,
               t.locked_flag        lockedFlag
        from m_cust_bala t
        <where>
            <if test="custBalaId != null  and custBalaId != '' "> and t.cust_bala_id = #{custBalaId}</if>
            <if test="delFlag != null  and delFlag != '' "> and t.del_flag = #{delFlag}</if>
            <if test="customerId != null  and customerId != '' "> and t.customer_id = #{customerId}</if>
            <if test="relatedCustId != null  and relatedCustId != '' "> and t.related_cust_id = #{relatedCustId}</if>
            <if test="isDefault != null  and isDefault != '' "> and t.is_default = #{isDefault}</if>
            <if test="lockedFlag != null  and lockedFlag != '' "> and t.locked_flag = #{lockedFlag}</if>
        </where>
    </select>

    <select id="selectClientByCustAbbrAndSalesDept" resultMap="ClientResult">
        <include refid="selectCustomerVo"/>
        where cust_abbr = #{custAbbr} and sales_dept = #{deptId} and del_flag = 0
    </select>

    <select id="selectclientCountPermission" resultType="int">
        select count(*) FROM M_CUSTOMER T
        where
        t.DEL_FLAG = 0
        <if test="checkStatus != null "> and t.check_status = #{checkStatus}</if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <resultMap id="rm2507" type="java.util.Map">
        <result property="customerId" column="customer_id" />
        <result property="custAbbr" column="cust_abbr" />
        <result property="salesDeptName" column="sales_dept_name" />
        <result property="salesDeptLeader" column="sales_dept_leader" />
        <result property="salesGroupName" column="sales_group_name" />
        <result property="yybfzr" column="yybfzr" />
    </resultMap>

    <select id="selectSalesGroup" resultMap="rm2507">
        select t.customer_id,t.cust_abbr,
            t1.dept_name sales_dept_name,
            t1.leader sales_dept_leader,
            t2.dept_name sales_group_name,
            t2.LEADER yybfzr
        from M_CUSTOMER T
                 LEFT JOIN SYS_DEPT T1 ON T1.DEPT_ID = to_number(T.SALES_DEPT)
                 LEFT JOIN SYS_DEPT T2 on T2.DEPT_ID = T1.PARENT_ID
        where t.customer_id in (<foreach collection="customerIds" item="customerId" separator=",">
            #{customerId}
        </foreach>)
    </select>

    <resultMap id="rm2560" type="java.util.Map">
        <result column="cust_abbr" property="custAbbr"/>
        <result column="customer_id" property="customerId"/>
        <!--<result column="sales_id" property="salesId"/>-->
        <result column="sales_name" property="salesName"/>
        <result column="sales_team_leader" property="salesTeamLeader"/>
        <result column="user_id" property="userId" javaType="Long"/>
        <result column="login_name" property="loginName"/>
    </resultMap>

    <select id="getSalesGroupOwner" resultMap="rm2560">
        select t.customer_id,t.cust_abbr,t2.dept_name sales_name,t2.leader sales_team_leader,t3.user_id,t3.login_name
        from M_CUSTOMER t
            left join sys_dept t4 on t4.dept_id = to_number(t.SALES_DEPT)
        left join sys_dept t2 on t2.dept_id = t4.parent_id
        left join sys_user t3 on t3.user_name = t2.leader and t3.user_type = '00' and t3.del_flag = 0 and t3.status = '0'
        where t.customer_id = #{customerId}
    </select>

    <select id="countNotSpecialTransfer" resultType="java.lang.Integer">
        select count(1) from m_customer t
        where t.customer_id in (<foreach collection="customerIds" separator="," item="customerId">#{customerId}</foreach>)
        and (t.is_special_transfer is null or t.is_special_transfer = 0)
    </select>

    <select id="selectBzjList" resultType="com.ruoyi.tms.domain.client.ReportZJCB">
        select zjcb.id,
               zjcb.customer_id customerId,
               zjcb.zjcb,
               zjcb.year_month yearMonth,
               zjcb.type,
               zjcb.sales_dept salesDept,
               zjcb.sales_id salesId,
               cust.CUST_ABBR custAbbr,
               dept.dept_name deptName,
               salesGroup.sales_name salesName,
               t_target.cnt
        from T_REPORT_ZJCB zjcb
                 left join sys_dept dept on dept.dept_id = zjcb.sales_dept
                 left join m_sales_group salesGroup on salesGroup.id = zjcb.sales_id
                 left join M_CUSTOMER cust on ZJCB.CUSTOMER_ID = cust.CUSTOMER_ID
                 left join (select count(1) cnt,customer_id,YEAR_MONTH from M_CUST_SALES_TARGET where DEL_FLAG  = 0
                 and target_amount != 0
                 group by  customer_id, YEAR_MONTH) t_target
                           on t_target.customer_id = zjcb.CUSTOMER_ID and t_target.YEAR_MONTH = zjcb.YEAR_MONTH
        <where>
        <if test="custAbbr != null  and custAbbr != '' ">
            <bind name="custAbbr" value="custAbbr + '%'"/>
            and cust.cust_abbr like #{custAbbr}
        </if>
        <if test="yearMonth != null  and yearMonth != '' ">
            and zjcb.year_month =  #{yearMonth}
        </if>
            <if test="type != null  ">
                and zjcb.type =  #{type}
            </if>
        </where>
        order by zjcb.year_month desc
    </select>

    <select id="selectBlongList" resultType="com.ruoyi.tms.domain.basic.CustFeeMonthBlong">
        select id,
               customer_id customerId,
               cust_abbr custAbbr,
               month,
               sales_dept salesDept,
               sales_dept_name salesDeptName,
               sales_id salesId,
               sales_name  salesName,
               MGMT_DEPT_ID mgmtDeptId,
               MGMT_DEPT_NAME mgmtDeptName
        from T_CUST_FEE_MONTH_BLONG
        <where>
            and sales_dept != '502' and sales_id != '501' and sales_id != '461'
            <if test="custAbbr != null  and custAbbr != '' ">
                <bind name="custAbbr" value="custAbbr + '%'"/>
                and cust_abbr like #{custAbbr}
            </if>
            <if test="startMonth != null  and startMonth != '' ">
                and month &gt;=  #{startMonth}
            </if>
            <if test="endMonth != null  and endMonth != '' ">
                and month &lt;=  #{endMonth}
            </if>
            <if test="month != null  and month != '' ">
                and month =  #{month}
            </if>
            <if test="salesDept != null  and salesDept != '' and salesDept.indexOf(',') != -1">
                and sales_dept in
                <foreach item="item" index="index" collection="salesDept.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="salesDept != null  and salesDept != '' and salesDept.indexOf(',') == -1">
                and sales_dept = #{salesDept}
            </if>

            <if test="salesId != null  and salesId != '' and salesId.indexOf(',') != -1">
                and sales_id in
                <foreach item="item" index="index" collection="salesId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="salesId != null  and salesId != '' and salesId.indexOf(',') == -1">
                and sales_id = #{salesId}
            </if>
        </where>
        order by month desc
    </select>

    <select id="selectBlongById" resultType="com.ruoyi.tms.domain.basic.CustFeeMonthBlong">
        select id,
               customer_id customerId,
               cust_abbr custAbbr,
            month,
            sales_dept salesDept,
            sales_dept_name salesDeptName,
            sales_id salesId,
            sales_name  salesName
        from T_CUST_FEE_MONTH_BLONG
        where id = #{id}
    </select>
    <select id="selectBzjById"  resultType="com.ruoyi.tms.domain.client.ReportZJCB">
        select zjcb.id,
               zjcb.customer_id customerId,
               zjcb.zjcb,
               zjcb.year_month yearMonth,
               zjcb.type,
               zjcb.sales_dept salesDept,
               zjcb.sales_id salesId,
               cust.CUST_ABBR custAbbr,
               dept.dept_name deptName,
               salesGroup.sales_name salesName,
               t_target.cnt
        from T_REPORT_ZJCB zjcb
                 left join sys_dept dept on dept.dept_id = zjcb.sales_dept
                 left join m_sales_group salesGroup on salesGroup.id = zjcb.sales_id
                 left join M_CUSTOMER cust on ZJCB.CUSTOMER_ID = cust.CUSTOMER_ID
                 left join (select count(1) cnt,customer_id,YEAR_MONTH from M_CUST_SALES_TARGET where DEL_FLAG  = 0 group by  customer_id, YEAR_MONTH) t_target
                           on t_target.customer_id = zjcb.CUSTOMER_ID and t_target.YEAR_MONTH = zjcb.YEAR_MONTH
        where  zjcb.id = #{id}
    </select>

    <update id="saveEditGroup1">
        update t_report_zjcb
        set sales_dept = #{salesDept,jdbcType=VARCHAR},
            sales_id = #{salesId,jdbcType=VARCHAR}
        where id=#{id}
    </update>


    <update id="saveEditGroup2">
        update T_CUST_FEE_MONTH_BLONG
        set sales_dept = #{salesDept,jdbcType=VARCHAR},
            sales_dept_name = (select dept_name from sys_dept where dept_id = #{salesDept,jdbcType=VARCHAR} ),
            sales_id = #{salesId,jdbcType=VARCHAR},
            sales_name = (select dept_name from sys_dept where dept_id =  #{salesId,jdbcType=VARCHAR}),
            mgmt_dept_id = #{mgmtDeptId,jdbcType=VARCHAR},
            mgmt_dept_name = (select dept_name from sys_dept where dept_id =  #{mgmtDeptId,jdbcType=VARCHAR})
        where id=#{id}
    </update>

    <update id="updateContractPriceReview">
        MERGE INTO M_CUSTOMER t
        USING (
            SELECT a.customer_id,
                   CASE
                       WHEN COUNT(CASE WHEN a.price_review = 0 THEN 1 END) = 0 THEN 1  -- 全部已审核
                       WHEN COUNT(CASE WHEN a.price_review = 1 THEN 1 END) <![CDATA[ > ]]> 0 AND
                            COUNT(CASE WHEN a.price_review = 0 THEN 1 END) <![CDATA[ > ]]> 0 THEN 2  -- 部分已审核
                       ELSE 0  -- 全部未审核
                       END AS contract_price_review
            FROM M_CUST_CONTRACTPC a
            WHERE a.del_flag = 0
            GROUP BY a.customer_id
        ) subquery
        ON (t.customer_id = subquery.customer_id)
        WHEN MATCHED THEN
            UPDATE SET
                       t.contract_price_review = subquery.contract_price_review
                        <if test="priceReviewUserName != null">
                            , t.CONTRACT_PRICE_REVIEW_USER_NAME = #{priceReviewUserName}
                        </if>
                        <if test="priceReviewUserId != null">
                            , t.contract_price_review_user_id = #{priceReviewUserId}
                        </if>
                        <if test="priceReviewDate != null">
                            , t.contract_price_review_date = #{priceReviewDate}
                        </if>
            WHERE t.del_flag = 0
              AND t.customer_id = #{customerId}
    </update>

    <select id="selectByServiceId" resultMap="ClientResult">
        select t.customer_id,
               t.del_flag,
               t.del_date,
               t.cust_code,
               t.cust_name,
               t.cust_abbr,
               t.cust_type,
               t.if_authentication,
               t.sales_dept,
               t.bala_dept,
               t.station_dept,
               t.bala_corp,
               t.corp_id,
               t.psndoc,
               t.psncontact,
               t.province_id,
               t.city_id,
               t.area_id,
               t.address,
               t.contact,
               t.contact_post,
               t.phone,
               t.mobile,
               t.email,
               t.fax,
               t.zipcode,
               t.billing_corp,
               t.billing_date,
               t.tax_identify,
               t.bala_type,
               t.billing_type,
               t.billing_payable,
               t.bank,
               t.discount_rate,
               t.account_period,
               t.acc_period_ahead,
               t.billing_ahead,
               t.credit_amount,
               t.account_name,
               t.bank_account,
               t.register_addr,
               t.billing_rule,
               t.legal_represent,
               t.register_capital,
               t.addressee,
               t.addressee_contact,
               t.addressee_addr,
               t.website,
               t.locked_flag,
               t.memo,
               t.cust_activity,
               t.reg_user_id,
               t.reg_date,
               t.cor_user_id,
               t.cor_date,
               t.reg_scr_id,
               t.cor_scr_id,
               t.businesslicense,
               t.psndoc_name,
               t.legal_card,
               t.customer_type,
               t.payment_days,
               t.province_name,
               t.city_name,
               t.area_name,
               t.del_user_id,
               t.bank_id,
               t.app_deli_contact,
               t.app_deli_mobile,
               t.check_status,
               t.check_man,
               t.check_date,
               t.crt_guide_price,
               t.customer_level,
               t.tariff,
               t.is_need_receipt,
               t.is_need_trace,
               t.invoice_days,
               t.collection_days,
               t.is_lock_other_fee,
               t.adjustment,
               t.special_date,
               t.enterprise_nature,
               t.customer_source,
               t.is_enabled,
               t.handling_charges,
               t.handling_charges_type,
               t.enable_contract_price,
               t.plat_rate,
               t.plat_tax,
               t.sales_id,
               t.accurate_region,
               t.accurate_goods,
               t.accurate_arri_detail,
               t.contract_price_float,
               t.float_date_start,
               t.float_date_end,
               t.reference_rate,
               t.if_bargain,
               t.is_special_reference_price,
               t.contract_price_review,
               t.contract_price_review_user_id,
               t.contract_price_review_user_name,
               t.contract_price_review_date,
               t.is_auto_dispatch,
               t.auto_dispatch_type,
               t.auto_subsection_addr_id,
               t.operate_corp,
               t.is_special_transfer,
               t.auto_dispatch_deli_fee,
               t.is_check_lonlat,
               t.auto_dispatch_pick_up_fee,
               t.source_type,
               t.contract_price_type,
               dept.dept_name as sales_dept_name,
               dept_p.dept_name as sales_dept_parent_name
        from M_CUSTOMER t
        left join sys_dept dept on dept.dept_id= t.sales_dept
        left join sys_dept dept_p on dept.parent_id= dept_p.dept_id
        left join M_CUSTOMER_SERVICE t1 on t.customer_id = t1.CUSTOMER_ID
        where t.DEL_FLAG=0 and t1.DEL_FLAG=0
          and t1.SERVICE_ID = #{userId,jdbcType=VARCHAR}
    </select>

    <resultMap id="yyInfo" type="java.util.Map">
        <result column="customer_id" property="customerId" />
        <result column="cust_abbr" property="custAbbr" />
        <result column="yyz_id" property="yyzId" javaType="Long"/>
        <result column="yyz_name" property="yyzName"/>
        <result column="yyz_leader" property="yyzLeader" />
        <result column="yyb_id" property="yybId" javaType="Long"/>
        <result column="yyb_name" property="yybName" />
        <result column="yyb_leader" property="yybLeader" />
        <result column="glb_id" property="glbId" javaType="Long"/>
        <result column="glb_name" property="glbName" />
        <result column="glb_leader" property="glbLeader" />
        <result column="jsz_id" property="jszId" javaType="Long"/>
        <result column="jsz_name" property="jszName" />
        <result column="jsz_leader" property="jszLeader" />
    </resultMap>

    <select id="selectYyDeptInfo" resultMap="yyInfo">
        select t.customer_id,t.cust_abbr,
               a.dept_id yyz_id,a.dept_name yyz_name,a.LEADER yyz_leader,
               b.dept_id yyb_id,b.dept_name yyb_name,b.LEADER yyb_leader,
               c.dept_id glb_id,c.dept_name glb_name,c.LEADER glb_leader,
               d.dept_id jsz_id,d.dept_name jsz_name,d.leader jsz_leader
        from m_customer t
        left join sys_dept a on a.dept_id = to_number(t.sales_dept)
        left join sys_dept b on b.dept_id = a.PARENT_ID
        left join sys_dept c on c.dept_id = b.PARENT_ID
        left join sys_dept d on d.dept_id = to_number(t.bala_dept)
        where t.customer_id = #{customerId}
    </select>

    <select id="selectYyDeptInfoList" resultMap="yyInfo">
        select t.customer_id,t.cust_abbr,
               a.dept_id yyz_id,a.dept_name yyz_name,a.LEADER yyz_leader,
               b.dept_id yyb_id,b.dept_name yyb_name,b.LEADER yyb_leader,
               c.dept_id glb_id,c.dept_name glb_name,c.LEADER glb_leader,
               d.dept_id jsz_id,d.dept_name jsz_name,d.leader jsz_leader
        from m_customer t
        left join sys_dept a on a.dept_id = to_number(t.sales_dept)
        left join sys_dept b on b.dept_id = a.PARENT_ID
        left join sys_dept c on c.dept_id = b.PARENT_ID
        left join sys_dept d on d.dept_id = to_number(t.bala_dept)
        where t.customer_id in (<foreach collection="customerIds" item="customerId" separator=",">#{customerId}</foreach>)
    </select>

    <select id="selectByAppDeliContact" resultMap="ClientResult">
        select t.customer_id,
               t.del_flag,
               t.del_date,
               t.cust_code,
               t.cust_name,
               t.cust_abbr,
               t.cust_type,
               t.if_authentication,
               t.sales_dept,
               t.bala_dept,
               t.station_dept,
               t.bala_corp,
               t.corp_id,
               t.psndoc,
               t.psncontact,
               t.province_id,
               t.city_id,
               t.area_id,
               t.address,
               t.contact,
               t.contact_post,
               t.phone,
               t.mobile,
               t.email,
               t.fax,
               t.zipcode,
               t.billing_corp,
               t.billing_date,
               t.tax_identify,
               t.bala_type,
               t.billing_type,
               t.billing_payable,
               t.bank,
               t.discount_rate,
               t.account_period,
               t.acc_period_ahead,
               t.billing_ahead,
               t.credit_amount,
               t.account_name,
               t.bank_account,
               t.register_addr,
               t.billing_rule,
               t.legal_represent,
               t.register_capital,
               t.addressee,
               t.addressee_contact,
               t.addressee_addr,
               t.website,
               t.locked_flag,
               t.memo,
               t.cust_activity,
               t.reg_user_id,
               t.reg_date,
               t.cor_user_id,
               t.cor_date,
               t.reg_scr_id,
               t.cor_scr_id,
               t.businesslicense,
               t.psndoc_name,
               t.legal_card,
               t.customer_type,
               t.payment_days,
               t.province_name,
               t.city_name,
               t.area_name,
               t.del_user_id,
               t.bank_id,
               t.app_deli_contact,
               t.app_deli_mobile,
               t.check_status,
               t.check_man,
               t.check_date,
               t.crt_guide_price,
               t.customer_level,
               t.tariff,
               t.is_need_receipt,
               t.is_need_trace,
               t.invoice_days,
               t.collection_days,
               t.is_lock_other_fee,
               t.adjustment,
               t.special_date,
               t.enterprise_nature,
               t.customer_source,
               t.is_enabled,
               t.handling_charges,
               t.handling_charges_type,
               t.enable_contract_price,
               t.plat_rate,
               t.plat_tax,
               t.sales_id,
               t.accurate_region,
               t.accurate_goods,
               t.accurate_arri_detail,
               t.contract_price_float,
               t.float_date_start,
               t.float_date_end,
               t.reference_rate,
               t.if_bargain,
               t.is_special_reference_price,
               t.contract_price_review,
               t.contract_price_review_user_id,
               t.contract_price_review_user_name,
               t.contract_price_review_date,
               t.is_auto_dispatch,
               t.auto_dispatch_type,
               t.auto_subsection_addr_id,
               t.operate_corp,
               t.is_special_transfer,
               t.auto_dispatch_deli_fee,
               t.is_check_lonlat,
               t.auto_dispatch_pick_up_fee,
               t.source_type,
               t.contract_price_type,
               dept.dept_name as sales_dept_name,
               dept_p.dept_name as sales_dept_parent_name
        from M_CUSTOMER t
                 left join sys_dept dept on dept.dept_id= t.sales_dept
                 left join sys_dept dept_p on dept.parent_id= dept_p.dept_id
        where t.DEL_FLAG=0
          and t.APP_DELI_CONTACT = #{userName,jdbcType=VARCHAR}

    </select>

    <update id="updateAppDeliContact">
        update M_CUSTOMER set app_deli_contact = #{appDeliContact,jdbcType=VARCHAR},
        app_deli_mobile = #{appDeliMobile,jdbcType=VARCHAR}
        where customer_id = #{customerId}
    </update>

    <select id="selectClientByGroupId" resultMap="ClientResult">
        SELECT t.customer_id,
               t.del_flag,
               t.del_date,
               t.cust_code,
               t.cust_name,
               t.cust_abbr,
               t.cust_type,
               t.if_authentication,
               t.sales_dept,
               t.bala_dept,
               t.station_dept,
               t.bala_corp,
               t.corp_id,
               t.psndoc,
               t.psncontact,
               t.province_id,
               t.city_id,
               t.area_id,
               t.address,
               t.contact,
               t.contact_post,
               t.phone,
               t.mobile,
               t.email,
               t.fax,
               t.zipcode,
               t.billing_corp,
               t.billing_date,
               t.tax_identify,
               t.bala_type,
               t.billing_type,
               t.billing_payable,
               t.bank,
               t.discount_rate,
               t.account_period,
               t.acc_period_ahead,
               t.billing_ahead,
               t.credit_amount,
               t.account_name,
               t.bank_account,
               t.register_addr,
               t.billing_rule,
               t.legal_represent,
               t.register_capital,
               t.addressee,
               t.addressee_contact,
               t.addressee_addr,
               t.website,
               t.locked_flag,
               t.memo,
               t.cust_activity,
               t.reg_user_id,
               t.reg_date,
               t.cor_user_id,
               t.cor_date,
               t.reg_scr_id,
               t.cor_scr_id,
               t.businesslicense,
               t.psndoc_name,
               t.legal_card,
               t.customer_type,
               t.payment_days,
               t.province_name,
               t.city_name,
               t.area_name,
               t.del_user_id,
               t.bank_id,
               t.app_deli_contact,
               t.app_deli_mobile,
               t.check_status,
               t.check_man,
               t.check_date,
               t.crt_guide_price,
               t.customer_level,
               t.tariff,
               t.is_need_receipt,
               t.is_need_trace,
               t.invoice_days,
               t.collection_days,
               t.is_lock_other_fee,
               t.adjustment,
               t.special_date,
               t.enterprise_nature,
               t.customer_source,
               t.is_enabled,
               t.handling_charges,
               t.handling_charges_type,
               t.enable_contract_price,
               t.plat_rate,
               t.plat_tax,
               t.sales_id,
               t.accurate_region,
               t.accurate_goods,
               t.accurate_arri_detail,
               t.contract_price_float,
               t.float_date_start,
               t.float_date_end,
               t.reference_rate,
               t.if_bargain,
               t.is_special_reference_price,
               t.contract_price_review,
               t.contract_price_review_user_id,
               t.contract_price_review_user_name,
               t.contract_price_review_date,
               t.is_auto_dispatch,
               t.auto_dispatch_type,
               t.auto_subsection_addr_id,
               t.operate_corp,
               t.is_special_transfer,
               t.auto_dispatch_deli_fee,
               t.is_check_lonlat,
               t.auto_dispatch_pick_up_fee,
               t.source_type,
               t.contract_price_type,
               t.notice_file_id,
               t.referrer,
               t.disabled_time,
               t.subject_amount
        FROM m_customer t
                 LEFT JOIN M_GROUP_CUST t1 ON t.customer_id = t1.customer_id
        WHERE t1.group_id = #{groupId,jdbcType=VARCHAR}
          AND t.del_flag = 0
    </select>
</mapper>
