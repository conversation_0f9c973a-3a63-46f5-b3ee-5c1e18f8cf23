<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tms.mapper.finance.PayDetailMapper">
    
    <resultMap type="PayDetail" id="PayDetailResult">
        <result property="payDetailId"    column="pay_detail_id"    />
        <result property="vbillno"    column="vbillno"    />
        <result property="vbillstatus"    column="vbillstatus"    />
        <result property="freeType"    column="free_type"    />
        <result property="costTypeOnWay"    column="cost_type_on_way"    />
        <result property="costTypeFreight"    column="cost_type_freight"    />
        <result property="lotId"    column="lot_id"    />
        <result property="lotno"    column="lotno"    />
        <result property="carrierId"    column="carrier_id"    />
        <result property="carrCode"    column="carr_code"    />
        <result property="carrName"    column="carr_name"    />
        <result property="balaCorp"    column="bala_corp"    />
        <result property="balatype"    column="balatype"    />
        <result property="transFeeCount"    column="trans_fee_count"    />
        <result property="gotAmount"    column="got_amount"    />
        <result property="ungotAmount"    column="ungot_amount"    />
        <result property="oilCardNumber"    column="oil_card_number"    />
        <result property="checkNo"    column="check_no"    />
        <result property="checkHead"    column="check_head"    />
        <result property="unconfirmType"    column="unconfirm_type"    />
        <result property="unconfirmMemo"    column="unconfirm_memo"    />
        <result property="confirmTime"    column="confirm_time"    />
        <result property="confirmUser"    column="confirm_user"    />
        <result property="driverMobile"    column="driver_mobile"    />
        <result property="driverName"    column="driver_name"    />
        <result property="carno"    column="carno"    />
        <result property="reqDeliDate"    column="req_deli_date"    />
        <result property="reqArriDate"    column="req_arri_date"    />
        <result property="isClose"    column="is_close"    />
        <result property="isAdjust"    column="is_adjust"    />
        <result property="adjustMemo"    column="adjust_memo"    />
        <result property="memo"    column="memo"    />
        <result property="regUserId"    column="reg_user_id"    />
        <result property="regDate"    column="reg_date"    />
        <result property="regScrId"    column="reg_scr_id"    />
        <result property="corUserId"    column="cor_user_id"    />
        <result property="corDate"    column="cor_date"    />
        <result property="corScrId"    column="cor_scr_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="delDate"    column="del_date"    />
        <result property="delUserId"    column="del_user_id"    />
        <result property="recCardNo"    column="rec_card_no"    />
        <result property="recAccount"    column="rec_account"    />
        <result property="recBank"    column="rec_bank"    />
        <result property="isNtocc"    column="is_ntocc"    />
        <result property="size"    column="sizes"    />
        <result property="deliAddr"    column="deli_addr"    />
        <result property="arriAddr"    column="arri_addr"    />
        <result property="carrBankId"    column="carr_bank_id"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="applyUser"    column="apply_user"    />
        <result property="balaMethod"    column="bala_type"    />
        <result property="reqPayDate"    column="req_pay_date"    />
        <result property="invoiceVbillno"    column="INVOICE_VBILLNO"    />
        <result property="custAbbr"    column="cust_abbr"    />
        <result property="carrBalaType"    column="carr_bala_type"    />
        <result property="sheetVbillno"    column="sheet_vbillno"    />
        <result property="params.oilRatio"    column="oil_ratio"    />
        <result property="tid"    column="tid"    />
        <result property="taxAmount"    column="tax_amount"    />
        <result property="checkUserName"    column="check_user_name"    />
        <result property="checkDate"    column="check_date"    />
        <result property="checkMemo"    column="check_memo"    />
        <result property="checkUserId"    column="check_user_id"    />
        <result property="checkStatus"    column="check_status"    />
        <result property="reviewStatus"    column="review_status"    />
        <result property="params.transLineName"    column="TRANS_LINE_NAME"    />
        <result property="applyMemo"    column="apply_memo"    />
        <result property="isOilDeposit"    column="is_oil_deposit"    />
        <result property="legalCard"    column="LEGAL_CARD"    />
        <result property="salesDeptName"    column="sales_dept_name"    />
        <result property="entrustReqDeliDate"    column="entrust_req_deli_date"    />
        <result property="bankBackFlag"    column="bank_back_flag"    />
        <result property="isFleetData"    column="is_fleet_data"    />
        <result property="isFleetAssign"    column="is_fleet_assign"    />
        <result property="fleetReceiveDetailId"    column="fleet_receive_detail_id"    />
        <result property="entrustLotIsFleetAssign"    column="entrust_lot_is_fleet_assign"    />
        <result property="writeOffTime"    column="write_off_time"    />

        <result property="g7Syn" column="g7_syn" />
        <result property="lotG7Syn" column="lot_g7_syn" />
        <result property="lotG7Start" column="lot_g7_start" />
        <result property="lotG7End" column="lot_g7_end" />
        <result property="g7Pay" column="g7_pay" />
        <result property="lotG7Msg" column="lot_g7_msg"/>
        <result property="g7PayErr" column="g7_pay_err" />
        <result property="accountType" column="account_type" />
        <result property="writeFuelcardId" column="write_fuelcard_id" />
        <result property="lotReqDeliDate" column="lot_req_deli_date" />
        <result property="payDetailIdList" column="pay_detail_id_list" />

        <result property="isMine" column="is_mine" />
        <result property="oilCardName" column="FUELCARD_NAME" />
        <result property="payWay" column="pay_way" />
        <result property="consumbleBack" column="CONSUMBLE_BACK"/>
        <result property="carrType" column="carr_type"/>
        <result property="payCheckSheetId" column="pay_check_sheet_id" />
        <result property="incomeRemark" column="INCOME_REMARK" />

        <result property="spNo" column="sp_no" />
        <result property="lotSpLock" column="lot_sp_lock" />
        <result property="writeOffTo" column="write_off_to" />

        <result property="lotLockPay" column="lotLockPay" />
        <result property="batchNo" column="batch_No" />
        <result property="entrustCostFileId" column="entrust_cost_file_id" />
        <result property="entrustCostId" column="entrust_cost_id" />
    </resultMap>

    <resultMap type="PayDetailVO" id="PayDetailVOResult">
        <result property="payDetailId"    column="pay_detail_id"    />
        <result property="transFeeCount"    column="trans_fee_count"    />
        <result property="memo"    column="memo"    />
        <result property="recCardNo"    column="rec_card_no"    />
        <result property="recAccount"    column="rec_account"    />
        <result property="recBank"    column="rec_bank"    />
        <result property="oilCardNumber"    column="oil_card_number"    />
        <result property="payType"    column="pay_type"    />
        <result property="balatype"    column="balatype"    />
    </resultMap>

    <resultMap type="Entrust" id="EntrustResult">
        <result property="entrustId"    column="entrust_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="delDate"    column="del_date"    />
        <result property="vbillno"    column="vbillno"    />
        <result property="vbillstatus"    column="vbillstatus"    />
        <result property="invoiceVbillno"    column="invoice_vbillno"    />
        <result property="custOrderno"    column="cust_orderno"    />
        <result property="orderno"    column="orderno"    />
        <result property="customerId"    column="customer_id"    />
        <result property="carrierId"    column="carrier_id"    />
        <result property="carnoId"    column="carno_id"    />
        <result property="carLenId"    column="car_len_id"    />
        <result property="carTypeId"    column="car_type_id"    />
        <result property="trailerId"    column="trailer_id"    />
        <result property="transTypeId"    column="trans_type_id"    />
        <result property="transLineId"    column="trans_line_id"    />
        <result property="transLine"    column="trans_line"    />
        <result property="reqDeliDate"    column="req_deli_date"    />
        <result property="reqArriDate"    column="req_arri_date"    />
        <result property="memo"    column="memo"    />
        <result property="deliveryId"    column="delivery_id"    />
        <result property="deliProvinceId"    column="deli_province_id"    />
        <result property="deliCityId"    column="deli_city_id"    />
        <result property="deliAreaId"    column="deli_area_id"    />
        <result property="deliDetailAddr"    column="deli_detail_addr"    />
        <result property="deliContact"    column="deli_contact"    />
        <result property="deliMobile"    column="deli_mobile"    />
        <result property="deliPhone"    column="deli_phone"    />
        <result property="deliEmail"    column="deli_email"    />
        <result property="arrivalId"    column="arrival_id"    />
        <result property="arriProvinceId"    column="arri_province_id"    />
        <result property="arriCityId"    column="arri_city_id"    />
        <result property="arriAreaId"    column="arri_area_id"    />
        <result property="arriDetailAddr"    column="arri_detail_addr"    />
        <result property="arriContact"    column="arri_contact"    />
        <result property="arriMobile"    column="arri_mobile"    />
        <result property="arriPhone"    column="arri_phone"    />
        <result property="arriEmail"    column="arri_email"    />
        <result property="numCount"    column="num_count"    />
        <result property="weightCount"    column="weight_count"    />
        <result property="volumeCount"    column="volume_count"    />
        <result property="feeWeightCount"    column="fee_weight_count"    />
        <result property="costAmount"    column="cost_amount"    />
        <result property="regUserId"    column="reg_user_id"    />
        <result property="regDate"    column="reg_date"    />
        <result property="corpId"    column="corp_id"    />
        <result property="balatype"    column="balatype"    />
        <result property="volumeWeightCount"    column="volume_weight_count"    />
        <result property="actDeliDate"    column="act_deli_date"    />
        <result property="actArriDate"    column="act_arri_date"    />
        <result property="trackingStatus"    column="tracking_status"    />
        <result property="trackingMemo"    column="tracking_memo"    />
        <result property="trackingTime"    column="tracking_time"    />
        <result property="expFlag"    column="exp_flag"    />
        <result property="segmentVbillno"    column="segment_vbillno"    />
        <result property="lot"    column="lot"    />
        <result property="lotId"    column="lot_id"    />
        <result property="packNumCount"    column="pack_num_count"    />
        <result property="dbilldate"    column="dbilldate"    />
        <result property="urgentLevel"    column="urgent_level"    />
        <result property="corUserId"    column="cor_user_id"    />
        <result property="corDate"    column="cor_date"    />
        <result property="unconfirmType"    column="unconfirm_type"    />
        <result property="unconfirmMemo"    column="unconfirm_memo"    />
        <result property="confirmUserid"    column="confirm_userid"    />
        <result property="confirmDate"    column="confirm_date"    />
        <result property="unconfirmUserid"    column="unconfirm_userid"    />
        <result property="unconfirmDate"    column="unconfirm_date"    />
        <result property="carno"    column="carno"    />
        <result property="driverName"    column="driver_name"    />
        <result property="driverMobile"    column="driver_mobile"    />
        <result property="actArriMan"    column="act_arri_man"    />
        <result property="actArriMemo"    column="act_arri_memo"    />
        <result property="actDeliMan"    column="act_deli_man"    />
        <result property="actDeliMemo"    column="act_deli_memo"    />
        <result property="isAppend"    column="is_append"    />
        <result property="ifReceipt"    column="if_receipt"    />
        <result property="receiptNum"    column="receipt_num"    />
        <result property="receiptMan"    column="receipt_man"    />
        <result property="receiptDate"    column="receipt_date"    />
        <result property="receiptMemo"    column="receipt_memo"    />
        <result property="receiptBookMan"    column="receipt_book_man"    />
        <result property="receiptBookTime"    column="receipt_book_time"    />
        <result property="receiptAppendixId"    column="receipt_appendix_id"    />
        <result property="receiptFlag"    column="receipt_flag"    />
        <result property="checkMan"    column="check_man"    />
        <result property="checkNo"    column="check_no"    />
        <result property="checkTime"    column="check_time"    />
        <result property="serialno"    column="serialno"    />
        <result property="ifInsurance"    column="if_insurance"    />
        <result property="deliMethod"    column="deli_method"    />
        <result property="billingCorp"    column="billing_corp"    />
        <result property="segmentType"    column="segment_type"    />
        <result property="printBy"    column="print_by"    />
        <result property="printTime"    column="print_time"    />
        <result property="printCount"    column="print_count"    />
        <result property="custCode"    column="cust_code"    />
        <result property="custName"    column="cust_name"    />
        <result property="transCode"    column="trans_code"    />
        <result property="transName"    column="trans_name"    />
        <result property="deliAddrCode"    column="deli_addr_code"    />
        <result property="deliAddrName"    column="deli_addr_name"    />
        <result property="deliProName"    column="deli_pro_name"    />
        <result property="deliCityName"    column="deli_city_name"    />
        <result property="arriAddrCode"    column="arri_addr_code"    />
        <result property="arriAddrName"    column="arri_addr_name"    />
        <result property="arriProName"    column="arri_pro_name"    />
        <result property="arriCityName"    column="arri_city_name"    />
        <result property="carTypeCode"    column="car_type_code"    />
        <result property="carTypeName"    column="car_type_name"    />
        <result property="carrCode"    column="carr_code"    />
        <result property="carrName"    column="carr_name"    />
        <result property="corpCode"    column="corp_code"    />
        <result property="corpName"    column="corp_name"    />
        <result property="regUserName"    column="reg_user_name"    />
        <result property="corUserName"    column="cor_user_name"    />
        <result property="isNtocc"    column="is_ntocc"    />
        <result property="isFleetData"    column="is_fleet_data"    />
        <result property="isFleetAssign"    column="is_fleet_assign"    />
        <result property="bizInvoiceId"    column="biz_invoice_id"    />
        <result property="bizInvoiceVbillno"    column="biz_invoice_vbillno"    />
        <result property="bizSegmentId"    column="biz_segment_id"    />
        <result property="bizSegmentVbillno"    column="biz_segment_vbillno"    />
        <result property="bizEntrustId"    column="biz_entrust_id"    />
        <result property="bizEntrustVbillno"    column="biz_entrust_vbillno"    />
        <result property="bizEntrustLotId"    column="biz_entrust_lot_id"    />
        <result property="bizEntrustLotVbillno"    column="biz_entrust_lot_vbillno"    />

    </resultMap>
    <resultMap type="Invoice" id="InvoiceResult">
        <result property="invoiceId"    column="invoice_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="delUserid"    column="del_userid"    />
        <result property="delDate"    column="del_date"    />
        <result property="vbillno"    column="vbillno"    />
        <result property="custOrderno"    column="cust_orderno"    />
        <result property="vbillstatus"    column="vbillstatus"    />
        <result property="groupId"    column="group_id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="balaCustomerId"    column="bala_customer_id"    />
        <result property="balaCorpId"    column="bala_corp_id"    />
        <result property="balaDept"    column="bala_dept"    />
        <result property="stationDept"    column="station_dept"    />
        <result property="residentsId"    column="residents_id"    />
        <result property="transLineId"    column="trans_line_id"    />
        <result property="dispatcherId"    column="dispatcher_id"    />
        <result property="balaType"    column="bala_type"    />
        <result property="urgentLevel"    column="urgent_level"    />
        <result property="ifBilling"    column="if_billing"    />
        <result property="reqDeliDate"    column="req_deli_date"    />
        <result property="reqArriDate"    column="req_arri_date"    />
        <result property="orderDate"    column="order_date"    />
        <result property="psndoc"    column="psndoc"    />
        <result property="salesDept"    column="sales_dept"    />
        <result property="memo"    column="memo"    />
        <result property="deliveryId"    column="delivery_id"    />
        <result property="deliProvinceId"    column="deli_province_id"    />
        <result property="deliCityId"    column="deli_city_id"    />
        <result property="deliAreaId"    column="deli_area_id"    />
        <result property="deliDetailAddr"    column="deli_detail_addr"    />
        <result property="deliContact"    column="deli_contact"    />
        <result property="deliMobile"    column="deli_mobile"    />
        <result property="deliEmail"    column="deli_email"    />
        <result property="arrivalId"    column="arrival_id"    />
        <result property="arriProvinceId"    column="arri_province_id"    />
        <result property="arriCityId"    column="arri_city_id"    />
        <result property="arriAreaId"    column="arri_area_id"    />
        <result property="arriDetailAddr"    column="arri_detail_addr"    />
        <result property="arriContact"    column="arri_contact"    />
        <result property="arriMobile"    column="arri_mobile"    />
        <result property="arriEmail"    column="arri_email"    />
        <result property="ifBackbill"    column="if_backbill"    />
        <result property="backbillNum"    column="backbill_num"    />
        <result property="ifInsReceipt"    column="if_ins_receipt"    />
        <result property="receiptAmount"    column="receipt_amount"    />
        <result property="receiptMemo"    column="receipt_memo"    />
        <result property="insuranceAppendixId"    column="insurance_appendix_id"    />
        <result property="insuranceNo"    column="insurance_no"    />
        <result property="insuranceCompany"    column="insurance_company"    />
        <result property="numCount"    column="num_count"    />
        <result property="weightCount"    column="weight_count"    />
        <result property="volumeCount"    column="volume_count"    />
        <result property="costAmount"    column="cost_amount"    />
        <result property="carLen"    column="car_len"    />
        <result property="carType"    column="car_type"    />
        <result property="unconfirmType"    column="unconfirm_type"    />
        <result property="unconfirmMemo"    column="unconfirm_memo"    />
        <result property="billingCorp"    column="billing_corp"    />
        <result property="deliProName"    column="deli_pro_name"    />
        <result property="deliCityName"    column="deli_city_name"    />
        <result property="deliAreaName"    column="deli_area_name"    />
        <result property="deliAddrCode"    column="deli_addr_code"    />
        <result property="deliAddrName"    column="deli_addr_name"    />
        <result property="arriProName"    column="arri_pro_name"    />
        <result property="arriCityName"    column="arri_city_name"    />
        <result property="arriAreaName"    column="arri_area_name"    />
        <result property="arriAddrCode"    column="arri_addr_code"    />
        <result property="arriAddrName"    column="arri_addr_name"    />
        <result property="custCode"    column="cust_code"    />
        <result property="custName"    column="cust_name"    />
        <result property="balaCode"    column="bala_code"    />
        <result property="balaName"    column="bala_name"    />
        <result property="transCode"    column="trans_code"    />
        <result property="transName"    column="trans_name"    />
        <result property="urgentLevelName"    column="urgent_level_name"    />
        <result property="carLenName"    column="car_len_name"    />
        <result property="carTypeName"    column="car_type_name"    />
        <result property="confirmUserid"    column="confirm_userid"    />
        <result property="confirmDate"    column="confirm_date"    />
        <result property="unconfirmUserid"    column="unconfirm_userid"    />
        <result property="unconfirmDate"    column="unconfirm_date"    />
        <result property="closeNote"    column="close_note"    />
        <result property="regUserId"    column="reg_user_id"    />
        <result property="regUserName"    column="reg_user_name"    />
        <result property="regDate"    column="reg_date"    />
        <result property="corUserId"    column="cor_user_id"    />
        <result property="corUserName"    column="cor_user_name"    />
        <result property="corDate"    column="cor_date"    />
        <result property="regScrId"    column="reg_scr_id"    />
        <result property="corScrId"    column="cor_scr_id"    />
        <result property="transLineName"    column="trans_line_name"    />
        <result property="groupName"    column="group_name"    />
        <result property="dispatcherName"    column="dispatcher_name"    />
        <result property="custAbbr"    column="cust_abbr"    />
        <result property="appDeliContact"    column="app_deli_contact"    />
        <result property="appDeliMobile"    column="app_deli_mobile"    />
        <result property="srcType"    column="src_type"    />
        <result property="isClose"    column="is_close"    />
        <result property="isRating"    column="is_rating"    />
        <result property="isNtocc"    column="is_ntocc"    />
        <result property="businesstypename"    column="businesstypename"    />
        <result property="synchronizationtime"    column="synchronizationtime"    />
        <result property="isexception"    column="isexception"    />
    </resultMap>
	<sql id="selectTPayDetailVo">
        select
               pay_detail_id,
               vbillno,
               vbillstatus,
               free_type,
               cost_type_on_way,
               cost_type_freight,
               lot_id,
               lotno,
               carrier_id,
               carr_code,
               carr_name,
               bala_corp,
               balatype,
               trans_fee_count,
               got_amount,
               ungot_amount,
               oil_card_number,
               check_no,
               check_head,
               unconfirm_type,
               unconfirm_memo,
               confirm_time,
               confirm_user,
               driver_mobile,
               driver_name,
               carno,
               req_deli_date,
               req_arri_date,
               is_close,
               is_adjust,
               adjust_memo,
               memo,
               reg_user_id,
               reg_date,
               reg_scr_id,
               cor_user_id,
               cor_date,
               cor_scr_id,
               del_flag,
               del_date,
               del_user_id,
               rec_card_no,
               rec_account,
               rec_bank,
               is_ntocc,
               apply_user,
               apply_time,
               is_fleet_data,
               is_fleet_assign,
               fleet_receive_detail_id,
               carr_bank_id
        from t_pay_detail
    </sql>
    <sql id="selectEntrustLotVo">
        select entrust_lot_id, del_flag, del_date, lot, vbillstatus, deli_province, deli_city, deli_area,
            arri_province, arri_cicust_abbrty, arri_area, trans_type,  carrier_id, carno_id,  car_len, car_type, memo,
            reg_user_id, reg_date, cor_user_id, cor_date,  num_count, weight_count, volume_count, cost_amount,
            trailer_id, balatype
        from t_entrust_lot
    </sql>

    <!--获取应付明细列表-->
    <select id="selectPayDetailList" resultMap="PayDetailResult">
        SELECT
            t.pay_detail_id,
            t.free_type,
            t.vbillno,
            t.vbillstatus,
            t.lotno,
            t.carrier_id,
            t.balatype,
            t.trans_fee_count,
            t.got_amount,
            t.ungot_amount,
            t.memo,
            t.check_no,
            t.check_head,
            t.unconfirm_type,
            t.unconfirm_memo,
            t.confirm_time,
            t.confirm_user,
            t.driver_mobile,
            t.driver_name,
            t.carno,
            t.req_deli_date,
            t.req_arri_date,
            t.carr_code,
            t.carr_name,
            t.reg_date,
            t.cor_user_id,
            t.cor_date,
            t.del_flag,
            t.del_date,
            t.del_user_id,
            t.oil_card_number,
            t.bala_corp,
            t.reg_scr_id,
            t.cor_scr_id,
            t.is_close,
            t.adjust_memo,
            t.is_adjust,
            t.cost_type_on_way,
            t.cost_type_freight,
            t.rec_card_no,
            t.rec_account,
            t.rec_bank,
            t.is_ntocc,
            t.LOT_ID,
            t.carr_bank_id,
            t.apply_user,
            t.apply_time,
            t.check_user_name,
            t.check_date,
            t.check_memo,
            t.check_status review_status,
            t.CONSUMBLE_BACK,
            t.INCOME_REMARK,
            sysUser.user_name as reg_user_id,
            lot.DELI_PROVINCE_NAME || lot.DELI_CITY_NAME || lot.DELI_AREA_NAME as deli_addr,
            lot.ARRI_PROVINCE_NAME || lot.ARRI_CITY_NAME || lot.ARRI_AREA_NAME as arri_addr,
            carr.bala_type as bala_type,
            carr.carr_type as carr_type,
            t.tid,
            lot.oil_ratio,
            nvl(t.tax_amount,0) tax_amount,
            lot.g7_syn lot_g7_syn,
            lot.g7_start lot_g7_start,
            lot.g7_end  lot_g7_end,
            lot.pay_way,
            t.g7_pay,
            lot.g7_msg lot_g7_msg,
            lot.req_deli_date lot_req_deli_date,
               e.pay_check_sheet_id,
               t.is_fleet_data,
               t.is_Fleet_Assign,
            t.sp_no,
               t.lot_sp_lock,
               driver.g7_ext g7DriverExt,
               car.g7_ext g7CarExt,
               case when lot.g7_syn is not null then (select billing_corp from t_entrust e where e.lot_id = t.lot_id and e.del_flag = 0 and rownum = 1) end g7Corp,
            lot.g7_qst g7LotQst,
           t.entrust_cost_file_id,
        t.ENTRUST_COST_ID
        FROM
            t_pay_detail t
            left join sys_user sysUser on t.reg_user_id = sysUser.USER_ID and sysUser.del_flag = 0
            left join T_ENTRUST_LOT lot on t.LOT_ID = lot.ENTRUST_LOT_ID
            left join m_carrier carr on carr.carrier_id = t.carrier_id and carr.del_flag = 0
            left join t_pay_check_sheet_b e on e.pay_detail_id = t.pay_detail_id and e.del_falg = 0
            left join m_driver driver on driver.driver_id = lot.driver_id
            left join m_car car on car.car_id = lot.carno_id
        <where>
            t.del_flag = 0
            and lot.DEL_FLAG = 0
            <if test="lotno != null and lotno.trim() != ''">
                <bind name="lotno" value="lotno + '%'"/>
                and t.lotno like #{lotno}
            </if>
            <if test="vbillno != null and vbillno.trim() != ''">
                <bind name="vbillno" value="vbillno + '%'"/>
                and t.vbillno like #{vbillno}
            </if>
            <if test="applyUser != null and applyUser.trim() != ''">
                <bind name="applyUser" value="applyUser + '%'"/>
                and t.apply_user like #{applyUser}
            </if>

            <if test="vbillstatus != null">
                and t.vbillstatus = #{vbillstatus}
            </if>
            <if test="status != null  and status != '' and status.indexOf(',') != -1">
                and t.vbillstatus in
                <foreach item="item" index="index" collection="status.split(',')" open="(" separator="," close=")">
                    ${@Integer@parseInt(item)}
                </foreach>
            </if>
            <if test="status != null and status.trim() != '' and status.indexOf(',') == -1">
                and t.vbillstatus = ${@Integer@parseInt(status)}
            </if>


            <if test="carrName != null and carrName.trim() != ''">
                <bind name="carrName" value="carrName + '%'"/>
                 and t.carr_name like #{carrName}
            </if>
            <if test="balatype != null and balatype.trim() != ''">
                and t.balatype = #{balatype}
            </if>
            <if test="driverName != null and driverName.trim() != ''">
                <bind name="driverName" value="driverName + '%'"/>
                and t.driver_name like #{driverName}
            </if>

            <if test="startDate != null ">
                and t.REG_DATE <![CDATA[   >=  ]]> #{startDate}
            </if>
            <if test="endtDate != null ">
                and t.REG_DATE <![CDATA[   <=  ]]> #{endtDate}
            </if>
            <if test="regUserName != null and regUserName != ''">
                <bind name="regUserName" value="regUserName + '%'"/>
                and sysUser.user_name like  #{regUserName}
            </if>
            <if test="carrierId != null and carrierId.trim() != ''">
                and t.carrier_id = #{carrierId}
            </if>
            <if test="lotId != null and lotId != ''">
                and t.lot_id = #{lotId}
            </if>
            <if test="incomeRemark != null and incomeRemark != ''">
                and t.INCOME_REMARK = #{incomeRemark}
            </if>
            <if test="costTypeFreight != null  and costTypeFreight != ''">
                and t.cost_type_freight in
                <foreach item="item" index="index" collection="costTypeFreight.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="oilCardNumber != null and oilCardNumber != ''">
                <bind name="oilCardNumber" value="oilCardNumber + '%'"/>
                and t.oil_card_number like  #{oilCardNumber}
            </if>
            <if test="payCheckSheetId != null and payCheckSheetId != ''">
                and e.pay_Check_Sheet_Id = #{payCheckSheetId}
            </if>
            <if test="batchNo != null and batchNo !=''">
                and t.batch_no = #{batchNo}
            </if>
        </where>

        <if test="params.unionDeposit == true">
            union all
            select t.id pay_detail_id,
                   '3' free_type,
            t.vbillno,
            t.vbillstatus,
            t.lotno,
            t.carrier_id,
            null balatype,
            t.amount trans_fee_count,
            t.got_amount,
            t.ungot_amount,
            null memo,
            null check_no,
            null check_head,
            null unconfirm_type,
            null unconfirm_memo,
            null confirm_time,
            null confirm_user,
            null driver_mobile,
            lot.driver_name,
            lot.car_no carno,
            null req_deli_date,
            null req_arri_date,
            carr.carr_code,
            carr.carr_name,
            t.reg_date,
            t.cor_user_id,
            t.cor_date,
            null del_flag,
            null del_date,
            null del_user_id,
            null oil_card_number,
            null bala_corp,
            t.reg_scr_id,
            t.cor_scr_id,
            null is_close,
            null adjust_memo,
            0 is_adjust,
            null cost_type_on_way,
            null cost_type_freight,
            t.rec_card_no,
            t.rec_account,
            t.rec_bank,
            0 is_ntocc,
            t.LOT_ID,
            t.carr_bank_id,
            null apply_user,
            null apply_time,
            null check_user_name,
            null check_date,
            null check_memo,
            null review_status,
            null CONSUMBLE_BACK,
            null INCOME_REMARK,
            sysUser.user_name as reg_user_id,
            lot.DELI_PROVINCE_NAME || lot.DELI_CITY_NAME || lot.DELI_AREA_NAME as deli_addr,
            lot.ARRI_PROVINCE_NAME || lot.ARRI_CITY_NAME || lot.ARRI_AREA_NAME as arri_addr,
            carr.bala_type as bala_type,
            carr.carr_type as carr_type,
            null tid,
            lot.oil_ratio,
            0 tax_amount,
            lot.g7_syn lot_g7_syn,
            lot.g7_start lot_g7_start,
            lot.g7_end  lot_g7_end,
            null pay_way,
            null g7_pay,
            lot.g7_msg lot_g7_msg,
            lot.req_deli_date lot_req_deli_date,
            null pay_check_sheet_id,
            null is_fleet_data,
            null is_Fleet_Assign,
            null sp_no,
            t.lot_sp_lock,
            null g7DriverExt,
            null g7CarExt,
            null g7Corp,
            null g7LotQst,
            null entrust_cost_file_id,
            null ENTRUST_COST_ID
            from t_entrust_lot_deposit t
            left join t_entrust_lot lot on lot.ENTRUST_LOT_ID = t.lot_id
            left join m_carrier carr on carr.CARRIER_ID = t.carrier_id
            left join sys_user sysUser on t.reg_user_id = sysUser.USER_ID
            where t.del_flag = 0
            <if test="lotId != null">
                and t.lot_id = #{lotId}
            </if>
        </if>

        order by REG_DATE  desc
    </select>

    <select id="selectPayDetailListGroupByCostTypeFreight" resultMap="PayDetailResult">
        select LOT_ID,
               FREE_TYPE,
               COST_TYPE_FREIGHT,
               COST_TYPE_ON_WAY,
               sum(TRANS_FEE_COUNT) TRANS_FEE_COUNT,
               sum(GOT_AMOUNT)      GOT_AMOUNT,
               sum(unGOT_AMOUNT)    unGot_amount
        from T_PAY_DETAIL t
        where del_flag = 0
        <if test="lotId != null and lotId != ''">
            and t.lot_id = #{lotId}
        </if>
        <if test="costTypeFreight != null  and costTypeFreight != ''">
            and t.cost_type_freight in
            <foreach item="item" index="index" collection="costTypeFreight.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="freeType != null and freeType != ''">
            and t.free_type = #{freeType}
        </if>
        group by LOT_ID, FREE_TYPE, COST_TYPE_FREIGHT, COST_TYPE_ON_WAY
    </select>

    <!--获取打包对账应付明细列表-->
<!--    <select id="selectPackagePayDetailList" resultMap="PayDetailResult">-->
<!--        <if test="salesDeptName != null and salesDeptName.trim() != ''">-->
<!--           select * from (-->
<!--        </if>-->
<!--        SELECT-->
<!--        DISTINCT-->
<!--        t.pay_detail_id,-->
<!--        t.free_type,-->
<!--        t.vbillno,-->
<!--        t.vbillstatus,-->
<!--        t.lotno,-->
<!--        t.carrier_id,-->
<!--        t.balatype,-->
<!--        t.trans_fee_count,-->
<!--        t.got_amount,-->
<!--        t.ungot_amount,-->
<!--        t.memo,-->
<!--        t.check_no,-->
<!--        t.check_head,-->
<!--        t.unconfirm_type,-->
<!--        t.unconfirm_memo,-->
<!--        t.confirm_time,-->
<!--        t.confirm_user,-->
<!--        t.driver_mobile,-->
<!--        t.driver_name,-->
<!--        t.carno,-->
<!--        t.req_deli_date,-->
<!--        t.req_arri_date,-->
<!--        t.carr_code,-->
<!--        t.carr_name,-->
<!--        t.reg_date,-->
<!--        t.cor_user_id,-->
<!--        t.cor_date,-->
<!--        t.del_flag,-->
<!--        t.del_date,-->
<!--        t.del_user_id,-->
<!--        t.oil_card_number,-->
<!--        t.reg_scr_id,-->
<!--        t.cor_scr_id,-->
<!--        t.is_close,-->
<!--        t.adjust_memo,-->
<!--        t.is_adjust,-->
<!--        t.cost_type_on_way,-->
<!--        t.cost_type_freight,-->
<!--        t.rec_card_no,-->
<!--        t.rec_account,-->
<!--        t.rec_bank,-->
<!--        t.is_ntocc,-->
<!--        t.LOT_ID,-->
<!--        t.carr_bank_id,-->
<!--        t.apply_user,-->
<!--        t.apply_time,-->
<!--        sysUser.user_name as reg_user_id,-->
<!--        lot.DELI_PROVINCE_NAME || lot.DELI_CITY_NAME || lot.DELI_AREA_NAME as deli_addr,-->
<!--        lot.ARRI_PROVINCE_NAME || lot.ARRI_CITY_NAME || lot.ARRI_AREA_NAME as arri_addr,-->
<!--        carr.bala_type as bala_type,-->
<!--        lot.oil_ratio oil_ratio,-->
<!--        t2.INVOICE_VBILLNO,-->
<!--        t3.cust_abbr cust_abbr,-->
<!--        lot.g7_syn lot_g7_syn,-->
<!--        lot.g7_start lot_g7_start,-->
<!--        lot.g7_end  lot_g7_end,-->
<!--        t.g7_pay,-->
<!--        lot.g7_msg lot_g7_msg,-->
<!--        to_char(entrust.req_deli_date,'yyyy-MM-dd') entrust_req_deli_date,-->
<!--        (select listagg(BALA_CORP,',')  WITHIN GROUP(ORDER BY 1)  from (select distinct-->
<!--        case cust.BALA_CORP when 'MY' THEN '铭源' WHEN 'JH' THEN '吉华' end as BALA_CORP from T_ENTRUST entrust-->
<!--        left join M_CUSTOMER cust on cust.CUSTOMER_ID = entrust.CUSTOMER_ID-->
<!--        where entrust.lot_id = lot.ENTRUST_LOT_ID)) bala_corp ,-->
<!--        (select listagg(dept_name,',')  WITHIN GROUP(ORDER BY 1)  from (select distinct-->
<!--         dept.dept_name from T_ENTRUST entrust-->
<!--        left join M_CUSTOMER cust on cust.CUSTOMER_ID = entrust.CUSTOMER_ID-->
<!--        left join sys_dept dept on cust.sales_dept = dept.dept_id-->
<!--        where entrust.lot_id = lot.ENTRUST_LOT_ID)-->
<!--        ) sales_dept_name-->
<!--        FROM-->
<!--        t_pay_detail t-->
<!--        left join sys_user sysUser on t.reg_user_id = sysUser.USER_ID and sysUser.del_flag = 0-->
<!--        left join T_ENTRUST_LOT lot on t.LOT_ID = lot.ENTRUST_LOT_ID and lot.DEL_FLAG = 0-->
<!--        left join T_ENTRUST entrust on entrust.LOT_ID = lot.ENTRUST_LOT_ID and entrust.DEL_FLAG = 0-->
<!--        left join m_carrier carr on carr.carrier_id = t.carrier_id-->
<!--        left join (select LOT_ID,listagg(INVOICE_VBILLNO,',')  WITHIN GROUP(ORDER BY LOT_ID) INVOICE_VBILLNO from T_ENTRUST group by LOT_ID) t2-->
<!--        on lot.ENTRUST_LOT_ID = t2.LOT_ID-->
<!--        left join (select LOT_ID,listagg(cust_abbr,',')  WITHIN GROUP(ORDER BY LOT_ID) cust_abbr from T_ENTRUST group by LOT_ID) t3-->
<!--        on lot.ENTRUST_LOT_ID = t3.LOT_ID-->

<!--        <where>-->
<!--            t.del_flag = 0-->
<!--            and lot.DEL_FLAG = 0-->
<!--            and carr.BALA_TYPE = 2-->
<!--            and not exists(-->
<!--            select ENTRUST_ID from T_ENTRUST entrust-->
<!--            where  entrust.LOT_ID = lot.ENTRUST_LOT_ID-->
<!--            and (RECEIPT_CONFIRM_FLAG = 0 or RECEIPT_CONFIRM_FLAG is null)-->
<!--            and entrust.DEL_FLAG = 0-->
<!--            )-->
<!--            &lt;!&ndash;回单日期&ndash;&gt;-->
<!--            <if test="receiptDateStart != null ">-->
<!--                and to_date(to_char(entrust.receipt_date,'yyyy-MM-dd'),'yyyy-MM-dd') <![CDATA[   >=  ]]> to_date(to_char(#{receiptDateStart},'yyyy-MM-dd'),'yyyy-MM-dd')-->
<!--            </if>-->
<!--            <if test="receiptDateEnd != null  ">-->
<!--                and to_date(to_char(entrust.receipt_date,'yyyy-MM-dd'),'yyyy-MM-dd') <![CDATA[   <=  ]]> to_date(to_char(#{receiptDateEnd},'yyyy-MM-dd'),'yyyy-MM-dd')-->
<!--            </if>-->
<!--            &lt;!&ndash;要求提货日期&ndash;&gt;-->
<!--            <if test="reqDeliDateStart != null ">-->
<!--                and to_date(to_char(entrust.req_deli_date,'yyyy-MM-dd'),'yyyy-MM-dd') <![CDATA[   >=  ]]> to_date(to_char(#{reqDeliDateStart},'yyyy-MM-dd'),'yyyy-MM-dd')-->
<!--            </if>-->
<!--            <if test="reqDeliDateEnd != null  ">-->
<!--                and to_date(to_char(entrust.req_deli_date,'yyyy-MM-dd'),'yyyy-MM-dd') <![CDATA[   <=  ]]> to_date(to_char(#{reqDeliDateEnd},'yyyy-MM-dd'),'yyyy-MM-dd')-->
<!--            </if>-->
<!--            <if test="lotno != null and lotno.trim() != ''">-->
<!--                <bind name="lotno" value="lotno + '%'"/>-->
<!--                and t.lotno like #{lotno}-->
<!--            </if>-->
<!--            <if test="vbillno != null and vbillno.trim() != ''">-->
<!--                <bind name="vbillno" value="vbillno + '%'"/>-->
<!--                and t.vbillno like #{vbillno}-->
<!--            </if>-->
<!--            <if test="applyUser != null and applyUser.trim() != ''">-->
<!--                <bind name="applyUser" value="applyUser + '%'"/>-->
<!--                and t.apply_user like #{applyUser}-->
<!--            </if>-->

<!--            <if test="vbillstatus != null">-->
<!--                and t.vbillstatus = #{vbillstatus}-->
<!--            </if>-->
<!--            <if test="status != null  and status != '' and status.indexOf(',') != -1">-->
<!--                and t.vbillstatus in-->
<!--                <foreach item="item" index="index" collection="status.split(',')" open="(" separator="," close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="status != null and status.trim() != '' and status.indexOf(',') == -1">-->
<!--                and t.vbillstatus = #{status}-->
<!--            </if>-->
<!--            <if test="isNtocc != null and isNtocc != ''">-->
<!--                and t.is_ntocc = #{isNtocc}-->
<!--            </if>-->

<!--            <if test="carrName != null and carrName.trim() != ''">-->
<!--                <bind name="carrName" value="carrName + '%'"/>-->
<!--                and t.carr_name like #{carrName}-->
<!--            </if>-->

<!--            <if test="balatype != null and balatype.trim() != ''">-->
<!--                and t.balatype = #{balatype}-->
<!--            </if>-->
<!--            <if test="driverName != null and driverName.trim() != ''">-->
<!--                <bind name="driverName" value="driverName + '%'"/>-->
<!--                and t.driver_name like #{driverName}-->
<!--            </if>-->

<!--            <if test="startDate != null ">-->
<!--                and t.REG_DATE <![CDATA[   >=  ]]> #{startDate}-->
<!--            </if>-->
<!--            <if test="endtDate != null ">-->
<!--                and t.REG_DATE <![CDATA[   <=  ]]> #{endtDate}-->
<!--            </if>-->
<!--            <if test="regUserName != null and regUserName != ''">-->
<!--                <bind name="regUserName" value="regUserName + '%'"/>-->
<!--                and sysUser.user_name like  #{regUserName}-->
<!--            </if>-->
<!--            <if test="carrierId != null and carrierId.trim() != ''">-->
<!--                and t.carrier_id = #{carrierId}-->
<!--            </if>-->
<!--            <if test="lotId != null and lotId != ''">-->
<!--                and t.lot_id = #{lotId}-->
<!--            </if>-->
<!--            <if test="costTypeFreight != null  and costTypeFreight != ''">-->
<!--                and t.cost_type_freight in-->
<!--                <foreach item="item" index="index" collection="costTypeFreight.split(',')" open="(" separator="," close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->

<!--            <if test="oilCardNumber != null and oilCardNumber != ''">-->
<!--                <bind name="oilCardNumber" value="oilCardNumber + '%'"/>-->
<!--                and t.oil_card_number like  #{oilCardNumber}-->
<!--            </if>-->
<!--            <if test="invoiceVbillno != null and invoiceVbillno != ''">-->
<!--                <bind name="invoiceVbillno" value="invoiceVbillno + '%'"/>-->
<!--                and t2.INVOICE_VBILLNO like  #{invoiceVbillno}-->
<!--            </if>-->
<!--            &lt;!&ndash;客户简称&ndash;&gt;-->
<!--            <if test="custAbbr != null and custAbbr != ''">-->
<!--                <bind name="custAbbr" value="custAbbr + '%'"/>-->
<!--                and t3.cust_abbr like  #{custAbbr}-->
<!--            </if>-->
<!--        </where>-->
<!--        order by t.REG_DATE  desc-->
<!--        <if test="salesDeptName != null and salesDeptName.trim() != ''">-->
<!--            <bind name="salesDeptName" value="salesDeptName + '%'"/>-->
<!--            )-->
<!--            where sales_dept_name like #{salesDeptName}-->
<!--        </if>-->

<!--    </select>-->

    <select id="selectPackagePayDetailListX" resultType="com.ruoyi.tms.vo.trace.PackageEntrustLotVO">
        select a.entrust_lot_id lotId,
               a.vbillstatus,
               a.pay_check_sheet_id payCheckSheetId,
               a.trans_Fee_Count transFeeCount,
               a.transFeeOil transFeeOil,
               a.transFeeCash transFeeCash,
               a.got_Amount gotAmount,
               a.ungot_Amount ungotAmount,
               a.vbillno sheetVbillno,
               a.freight_fee_rate freightFeeRate,
               nvl((select dict_label from sys_dict_data aa where aa.dict_type = 'billing_type' and aa.dict_value = a.billing_type),'不开票') billingTypeLabel,
               a.oil_card_rate oilCardRate,
               b.vbillstatus lotVbillstatus,
               b.g7_syn lotG7Syn,
               b.g7_start lotG7Start,
               b.g7_end lotG7End,
               b.g7_msg lotG7Msg,
               b.oil_ratio oilRatio,
               b.lot lotno,
               b.is_Arrival_Section isArrivalSection,
               b.reg_date regDate,
               b.pay_way payWay,
               b.DELI_PROVINCE_NAME || b.DELI_CITY_NAME || b.DELI_AREA_NAME as deliAddr,
               b.ARRI_PROVINCE_NAME || b.ARRI_CITY_NAME || b.ARRI_AREA_NAME as arriAddr,
               b.car_no carno,
               a.carrier_id carrierId,
               a.carr_name carrName,
               a.pay_detail_id_list payDetailIdList,
               to_char(b.req_deli_date,'yyyy-MM-dd') entrustReqDeliDate,
               sysUser.user_name as regUserId,
               b.num_count numCount,
               b.weight_count weightCount,
               b.volume_count volumeCount,
               b.if_all_confirm ifAllConfirm,
               b.if_all_receipt_upload  ifAllReceiptUpload,
               b.if_all_receipt ifAllReceipt,
               nvl(b.lock_pay,'2') lockPay,
               a.locK_pay lockPayCarrier,
               nvl(a.LOCK_PAY_reason_union,'运单锁定') lockPayReasonUnion,
               b.single_lock singleLock,
               b.lock_memo lockMemo,
               b.LTL_TYPE ltlType,
               case when b.g7_syn is not null then (select billing_corp from t_entrust e where e.lot_id = b.entrust_lot_id and e.del_flag = 0 and rownum = 1) end g7Corp,
               b.g7_qst g7LotQst,
               driver.g7_ext g7DriverExt,
               car.g7_ext g7CarExt,
               b.IS_AUTO_DIS isAutoDis,
               b.TRANS_TYPE transType,
               b.memo        lotMemo,
               b.SP_STATUS   spStatus,
               b.sp_no       spNo
        from (select lot.entrust_lot_id,
                     lot.billing_type,
                     lot.freight_fee_rate,
                     lot.oil_ratio oil_card_rate,
                     p.vbillstatus,
                     b.pay_check_sheet_id,
<!--                     listagg(p.vbillstatus,',') WITHIN GROUP(ORDER BY p.vbillstatus) vbillstatus,-->
<!--                     listagg(b.pay_check_sheet_id,',') WITHIN GROUP(ORDER BY b.pay_check_sheet_id) pay_check_sheet_id,-->
                     sum(p.trans_Fee_Count) trans_Fee_Count,
                     sum(case  when p.cost_type_freight='1' or p.cost_type_freight='3' or p.cost_type_freight='5' then p.trans_Fee_Count else 0 end) transFeeOil,
                     sum(case  when p.cost_type_freight!='1' and p.cost_type_freight!='3' and p.cost_type_freight!='5' then p.trans_Fee_Count else 0 end) transFeeCash,
                     sum(p.got_Amount) got_Amount,
                     sum(p.ungot_Amount) ungot_Amount,
                     listagg(p.pay_detail_id,',') WITHIN GROUP(ORDER BY p.pay_detail_id) pay_detail_id_list,
                     cs.vbillno,
<!--                     listagg(cs.vbillno,',') WITHIN GROUP(ORDER BY cs.vbillno) vbillno,-->
                     c.carrier_id,
                     c.carr_name,
                     c.lock_pay,
                     c.LOCK_PAY_reason_union
            from m_carrier c
                inner join t_pay_detail p on p.carrier_id = c.carrier_id and p.del_flag = 0 and p.CONSUMBLE_BACK = 1
                <if test="status != null and status != ''">
                    and p.vbillstatus in (
                    <foreach item="item" index="index" collection="status.split(',')" separator=",">
                        ${@Integer@parseInt(item)}
                    </foreach>)
                </if>
                inner join t_entrust_lot lot on lot.entrust_lot_id = p.lot_id and lot.del_flag = 0
<!--                and not exists (select 1 from T_ENTRUST entrust where entrust.LOT_ID = lot.ENTRUST_LOT_ID and RECEIPT_CONFIRM_FLAG = 0 and entrust.DEL_FLAG = 0)-->
                <if test='(driverName != null and driverName.trim() != "") or (salesDeptName != null and salesDeptName !="") or (custAbbr != null and custAbbr != "") or (invoiceVbillno != null and invoiceVbillno != "")'>
                    and exists (select 1 from t_entrust entrust
                    <if test="invoiceVbillno != null and invoiceVbillno != ''">
                        <bind name="invoiceVbillno" value="invoiceVbillno + '%'"/>
                        inner join t_invoice t2 on t2.invoice_id = entrust.orderno and t2.vbillno like #{invoiceVbillno}
                    </if>
                    where entrust.lot_id = lot.entrust_lot_id
                    <if test='driverName != null and driverName.trim() != ""'>
                        <bind name="driverName" value="driverName + '%'"/>
                        and entrust.driver_name like #{driverName}
                    </if>

                    <if test='salesDeptName != null and salesDeptName !=""'>
                        and entrust.sales_dept in (
                        <foreach collection="salesDeptName.split(',')" separator="," item="item">'${@Integer@parseInt(item)}'</foreach>)
                    </if>
                    <if test='custAbbr != null and custAbbr != ""'>
                        <bind name="custAbbr" value="custAbbr + '%'"/>
                        and entrust.cust_abbr like #{custAbbr}
                    </if>
                    and entrust.del_flag=0)
                </if>
                left join T_PAY_CHECK_SHEET_B b on b.pay_detail_id = p.pay_detail_id and b.del_falg = 0
                left join t_pay_check_sheet cs on cs.pay_check_sheet_id = b.pay_check_sheet_id
                where c.bala_type = '2' <!--and c.del_flag = 0-->
        <if test="carrName != null and carrName.trim() != ''">
            <bind name="carrName" value="carrName + '%'"/>
            and c.carr_name like #{carrName}
        </if>
        <if test="lotno != null and lotno !=''">
            <bind name="lotno" value="lotno + '%'"/>
            and lot.lot like #{lotno}
        </if>
        <if test='carno != null and carno != ""'>
            <bind name="carno" value="carno + '%'"/>
            and lot.car_no like #{carno}
        </if>
        <if test="reqDeliDateStart != null ">
            and lot.req_deli_date >= to_date(to_char(#{reqDeliDateStart},'yyyy-MM-dd'),'yyyy-MM-dd')
        </if>
        <if test="reqDeliDateEnd != null  ">
            and lot.req_deli_date &lt; to_date(to_char(#{reqDeliDateEnd},'yyyy-MM-dd'),'yyyy-MM-dd') + 1
        </if>
            group by lot.entrust_lot_id,
                    lot.billing_type,
                    lot.freight_fee_rate,
                    lot.oil_ratio,
                    p.vbillstatus,
                    b.pay_check_sheet_id,
                    cs.vbillno,
                    c.carrier_id,
                    c.carr_name,
                    c.lock_pay,
                c.LOCK_PAY_reason_union
        ) a
        left join t_entrust_lot b on b.entrust_lot_id = a.entrust_lot_id
        left join sys_user sysUser on b.reg_user_id = sysUser.USER_ID
        left join m_driver driver on driver.driver_id = b.driver_id
        left join m_car car on car.car_id = b.carno_id
        <where>
            <if test="regUserName != null and regUserName != ''">
                <bind name="regUserName" value="regUserName + '%'"/>
                and sysUser.user_name like #{regUserName}
            </if>
            <if test="deliProvince != null  and deliProvince != ''  ">and b.deli_province = #{deliProvince}</if>
            <if test="deliCity != null  and deliCity != ''  ">and b.deli_city = #{deliCity}</if>
            <if test="deliArea != null  and deliArea != ''  ">and b.deli_area = #{deliArea}</if>
            <!--到货省市区-->
            <if test="arriProvince != null  and arriProvince != '' "> and b.arri_province = #{arriProvince}</if>
            <if test="arriCity != null  and arriCity != '' "> and b.arri_city = #{arriCity}</if>
            <if test="arriArea != null  and arriArea != '' "> and b.arri_area = #{arriArea}</if>
            <if test="transType != null  and transType != '' "> and b.trans_type = #{transType}</if>

        </where>
        order by b.reg_date desc
    </select>

    <select id="selectPackagePayDetailAmountCountX" resultType="java.util.Map">
        select
               sum(p.trans_Fee_Count) transFeeCount,
               sum(p.got_Amount) gotAmount,
               sum(p.ungot_Amount) ungotAmount
        from m_carrier c
            inner join t_pay_detail p on p.carrier_id = c.carrier_id and p.del_flag = 0
        <if test="status != null and status != ''">
            and p.vbillstatus in (
            <foreach item="item" index="index" collection="status.split(',')" separator=",">
                ${@Integer@parseInt(item)}
            </foreach>)
        </if>
        inner join t_entrust_lot lot on lot.entrust_lot_id = p.lot_id and lot.del_flag = 0
<!--        and not exists (select 1 from T_ENTRUST entrust where entrust.LOT_ID = lot.ENTRUST_LOT_ID and RECEIPT_CONFIRM_FLAG = 0 and entrust.DEL_FLAG = 0)-->
        <if test='(driverName != null and driverName.trim() != "") or (salesDeptName != null and salesDeptName !="") or (custAbbr != null and custAbbr != "") or (invoiceVbillno != null and invoiceVbillno != "")'>
            and exists (select 1 from t_entrust entrust
            <if test="invoiceVbillno != null and invoiceVbillno != ''">
                <bind name="invoiceVbillno" value="invoiceVbillno + '%'"/>
                inner join t_invoice t2 on t2.invoice_id = entrust.orderno and t2.vbillno like #{invoiceVbillno}
            </if>
            where entrust.lot_id = lot.entrust_lot_id
            <if test='driverName != null and driverName.trim() != ""'>
                <bind name="driverName" value="driverName + '%'"/>
                and entrust.driver_name like #{driverName}
            </if>
            <if test='salesDeptName != null and salesDeptName !=""'>
                and entrust.sales_dept in (
                <foreach collection="salesDeptName.split(',')" separator="," item="item">'${@Integer@parseInt(item)}'</foreach>)
            </if>
            <if test='custAbbr != null and custAbbr != ""'>
                <bind name="custAbbr" value="custAbbr + '%'"/>
                and entrust.cust_abbr like #{custAbbr}
            </if>
            and entrust.del_flag=0)
        </if>
        <if test="lotno != null and lotno !=''">
            <bind name="lotno" value="lotno + '%'"/>
            and lot.lot like #{lotno}
        </if>
        <if test="reqDeliDateStart != null ">
            and lot.req_deli_date >= to_date(to_char(#{reqDeliDateStart},'yyyy-MM-dd'),'yyyy-MM-dd')
        </if>
        <if test="reqDeliDateEnd != null  ">
            and lot.req_deli_date &lt; to_date(to_char(#{reqDeliDateEnd},'yyyy-MM-dd'),'yyyy-MM-dd') + 1
        </if>
        <if test="regUserName != null and regUserName != ''">
            <bind name="regUserName" value="regUserName + '%'"/>
            inner join sys_user sysUser on lot.reg_user_id = sysUser.USER_ID and sysUser.user_name like #{regUserName}
        </if>
        left join T_PAY_CHECK_SHEET_B b on b.pay_detail_id = p.pay_detail_id and b.del_falg = 0
        left join t_pay_check_sheet cs on cs.pay_check_sheet_id = b.pay_check_sheet_id
        where c.bala_type = '2' and c.del_flag = 0
        <if test="carrName != null and carrName.trim() != ''">
            <bind name="carrName" value="carrName + '%'"/>
            and c.carr_name like #{carrName}
        </if>
    </select>

    <resultMap id="entrustListAggInfo" type="HashMap">
        <id column="lot_id" property="lotId" />
        <result column="bala_corp" property="balaCorp" />
        <result column="goods_name" property="goodsName" />
        <result column="INVOICE_VBILLNO" property="invoiceVbillno" />
        <result column="driver_name" property="driverName" />
        <result column="driver_mobile" property="driverMobile" />
        <result column="sales_dept_name" property="salesDeptName" />
        <result column="cust_abbr" property="custAbbr" />
    </resultMap>

    <select id="listAggEntrustInfoByLotIds" resultMap="entrustListAggInfo">
        select lot_id,
               regexp_replace(listagg(case a.billing_corp when 'MY' then '铭源' when 'JH' then '吉华' when 'DH' then '鼎辉' when 'DW' then '鼎旺' else a.billing_corp end,',') WITHIN GROUP(ORDER BY a.billing_corp),'([^,]+)(,\1)*(,|$)', '\1\3') bala_corp,
               regexp_replace(listagg(a.goods_name,',') WITHIN GROUP(ORDER BY a.goods_name),'([^,]+)(,\1)*(,|$)', '\1\3') goods_name,
               listagg(b.vbillno,',') WITHIN GROUP(ORDER BY b.vbillno) INVOICE_VBILLNO,
               regexp_replace(listagg(a.driver_name,',') WITHIN GROUP(ORDER BY a.driver_name),'([^,]+)(,\1)*(,|$)', '\1\3') driver_name,
               regexp_replace(listagg(a.driver_mobile,',') WITHIN GROUP(ORDER BY a.driver_name),'([^,]+)(,\1)*(,|$)', '\1\3') driver_mobile,
               regexp_replace(listagg(c.dept_name,',') WITHIN GROUP(ORDER BY c.dept_name),'([^,]+)(,\1)*(,|$)', '\1\3') sales_dept_name,
               regexp_replace(listagg(a.cust_abbr,',') WITHIN GROUP(ORDER BY a.cust_abbr),'([^,]+)(,\1)*(,|$)', '\1\3') cust_abbr,
                listagg(distinct b.MEMO,'\n') WITHIN GROUP(ORDER BY b.MEMO) INVOICE_MEMO

        from t_entrust a
            left join t_invoice b on a.orderno = b.invoice_id
            left join sys_dept c on c.dept_id = to_number(a.sales_dept)
        where a.lot_id in (<foreach collection="lotIds" item="lotId" separator=",">#{lotId}</foreach>) and a.del_flag=0 group by a.lot_id
    </select>

    <!--获取在途跟踪应付明细列表-->
    <select id="selectTraceToPayDetailList" resultMap="PayDetailResult">
        SELECT distinct
            t.pay_detail_id,
        t.free_type,
        t.vbillno,
        t.vbillstatus,
        t.lotno,
        t.carrier_id,
        t.balatype,
        t.trans_fee_count,
        t.got_amount,
        t.ungot_amount,
        t.memo,
        t.check_no,
        t.check_head,
        t.unconfirm_type,
        t.unconfirm_memo,
        t.confirm_time,
        t.confirm_user,
        t.driver_mobile,
        t.driver_name,
        t.carno,
        t.req_deli_date,
        t.req_arri_date,
        t.carr_code,
        t.carr_name,
        t.reg_date,
        t.cor_user_id,
        t.cor_date,
        t.del_flag,
        t.del_date,
        t.del_user_id,
        t.oil_card_number,
        t.bala_corp,
        t.reg_scr_id,
        t.cor_scr_id,
        t.is_close,
        t.adjust_memo,
        t.is_adjust,
        t.cost_type_on_way,
        t.cost_type_freight,
        t.rec_card_no,
        t.rec_account,
        t.rec_bank,
        t.is_ntocc,
        t.LOT_ID,
        t.carr_bank_id,
        t.apply_user,
        t.apply_time,
        t.apply_memo,
        sysUser.user_name as reg_user_id,
        lot.DELI_PROVINCE_NAME || lot.DELI_CITY_NAME || lot.DELI_AREA_NAME as deli_addr,
        lot.ARRI_PROVINCE_NAME || lot.ARRI_CITY_NAME || lot.ARRI_AREA_NAME as arri_addr,
        carrier.bala_type as carr_bala_type,
        sheet.VBILLNO sheet_vbillno,
        t.tax_amount,
        t.is_fleet_data,
        t.is_fleet_assign,
        t.fleet_receive_detail_id,
        t.write_off_time,
        lot.g7_syn lot_g7_syn,
        lot.g7_start lot_g7_start,
        lot.g7_end  lot_g7_end,
        lot.pay_way,
        t.g7_pay,
        lot.g7_msg lot_g7_msg,
        t.account_type,
        lot.req_deli_date lot_req_deli_date
        FROM
        t_pay_detail t
        left join T_ENTRUST_LOT lot on t.LOT_ID = lot.ENTRUST_LOT_ID and lot.DEL_FLAG = 0
        left join T_ENTRUST entrust on lot.ENTRUST_LOT_ID = entrust.LOT_ID and entrust.DEL_FLAG = 0
        left join M_CARRIER carrier on carrier.carrier_id = entrust.carrier_id
        left join sys_user sysUser on t.reg_user_id = sysUser.USER_ID and sysUser.del_flag = 0
        left join t_pay_check_sheet_b sheetb on sheetb.PAY_DETAIL_ID = t.PAY_DETAIL_ID and sheetb.del_falg = 0
        left join T_PAY_CHECK_SHEET sheet on sheet.pay_check_sheet_id = sheetb.PAY_CHECK_SHEET_ID and sheet.DEL_FLAG = 0
        left join m_customer cust on cust.customer_id = entrust.customer_id
        <where>
            t.del_flag = 0
            <if test="isFleetData != null and isFleetData != ''">
                and t.IS_FLEET_DATA = #{isFleetData,jdbcType=VARCHAR}
            </if>
            <if test="lotno != null and lotno.trim() != ''">
                <bind name="lotno" value="lotno + '%'"/>
                and t.lotno like #{lotno}
            </if>
            <if test="vbillno != null and vbillno.trim() != ''">
                <bind name="vbillno" value="vbillno + '%'"/>
                and t.vbillno like #{vbillno}
            </if>
            <if test="applyUser != null and applyUser.trim() != ''">
                <bind name="applyUser" value="applyUser + '%'"/>
                and t.apply_user like #{applyUser}
            </if>

            <if test="vbillstatus != null">
                and t.vbillstatus = #{vbillstatus}
            </if>
            <if test="status != null  and status != '' and status.indexOf(',') != -1">
                and t.vbillstatus in
                <foreach item="item" index="index" collection="status.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status.trim() != '' and status.indexOf(',') == -1">
                and t.vbillstatus = #{status}
            </if>
            <if test="carrName != null and carrName.trim() != ''">
                <bind name="carrName" value="carrName + '%'"/>
                and t.carr_name like #{carrName}
            </if>
            <if test="balatype != null and balatype.trim() != ''">
                and t.balatype = #{balatype}
            </if>
            <if test="isAdjust != null ">
                and t.is_adjust = #{isAdjust}
            </if>
            <if test="driverName != null and driverName.trim() != ''">
                <bind name="driverName" value="driverName + '%'"/>
                and t.driver_name like #{driverName}
            </if>

            <if test="startDate != null ">
                and to_date(to_char(t.REG_DATE,'yyyy-mm-dd'),'yyyy-mm-dd') <![CDATA[   >=  ]]> #{startDate}
            </if>
            <if test="endtDate != null ">
                and to_date(to_char(t.REG_DATE,'yyyy-mm-dd'),'yyyy-mm-dd') <![CDATA[   <=  ]]> #{endtDate}
            </if>
            <if test="lotReqDeliDateStart != null and lotReqDeliDateStart.trim() != '' ">
                and to_char(lot.req_deli_date,'yyyy-mm-dd') <![CDATA[   >=  ]]> #{lotReqDeliDateStart}
            </if>
            <if test="lotReqDeliDateEnd != null  and lotReqDeliDateEnd.trim() != '' ">
                and to_char(lot.req_deli_date,'yyyy-mm-dd') <![CDATA[   <=  ]]> #{lotReqDeliDateEnd}
            </if>

            <if test="applyDateStart != null ">
                and to_date(to_char(t.apply_time,'yyyy-MM-dd'),'yyyy-MM-dd') <![CDATA[   >=  ]]> to_date(to_char(#{applyDateStart},'yyyy-MM-dd'),'yyyy-MM-dd')
            </if>
            <if test="applyDateEnd != null ">
                and to_date(to_char(t.apply_time,'yyyy-MM-dd'),'yyyy-MM-dd') <![CDATA[   <=  ]]> to_date(to_char(#{applyDateEnd},'yyyy-MM-dd'),'yyyy-MM-dd')
            </if>
            <if test="regUserName != null and regUserName != ''">
                <bind name="regUserName" value="regUserName + '%'"/>
                and sysUser.user_name like  #{regUserName}
            </if>
            <if test="carrierId != null and carrierId.trim() != ''">
                and t.carrier_id = #{carrierId}
            </if>
            <if test="lotId != null and lotId != ''">
                and t.lot_id = #{lotId}
            </if>
            <!--费用类型-->
            <if test="freeType != null and freeType != ''">
                and t.free_type = #{freeType}
            </if>
            <if test="costTypeOnWay != null and costTypeOnWay != ''">
                and t.free_type = 1 and t.cost_type_on_way in
                <foreach item="item" index="index" collection="costTypeOnWay.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!--运费费用类型-->
            <if test="costTypeFreight != null  and costTypeFreight != ''">
                and t.free_type = 0 and t.cost_type_freight in
                <foreach item="item" index="index" collection="costTypeFreight.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="isOilDeposit != null and isOilDeposit != ''">
                and t.is_oil_deposit = #{isOilDeposit}
            </if>
        </where>
        ${params.dataScope}
        order by t.REG_DATE  desc
    </select>

    <!--统计在途跟踪应付明细列表金额-->
    <select id="selectTraceToPayDetailAmountCount" resultType="Map">
        select
        sum(nvl(t1.transFeeCount,0)) transFeeCount,
        sum(nvl(t1.gotAmount,0)) gotAmount,
        sum(nvl(t1.ungotAmount,0)) ungotAmount,
        sum(nvl(t1.TAX_AMOUNT,0)) taxAmount
        from (
        SELECT
        distinct
        t.vbillno,
        t.trans_fee_count transFeeCount,
        t.got_amount gotAmount,
        t.ungot_amount ungotAmount,
        t.TAX_AMOUNT
        FROM
        t_entrust entrust
        left join T_ENTRUST_LOT lot on entrust.LOT_ID = lot.ENTRUST_LOT_ID
        left join t_pay_detail t on t.LOT_ID = lot.ENTRUST_LOT_ID
        left join sys_user sysUser on t.reg_user_id = sysUser.USER_ID and sysUser.del_flag = 0
        <where>
            t.del_flag = 0
            <if test="isFleetData != null and isFleetData != ''">
                and  t.IS_FLEET_DATA = #{isFleetData,jdbcType=VARCHAR}
            </if>
            <if test="lotno != null and lotno.trim() != ''">
                <bind name="lotno" value="lotno + '%'"/>
                and t.lotno like #{lotno}
            </if>
            <if test="vbillno != null and vbillno.trim() != ''">
                <bind name="vbillno" value="vbillno + '%'"/>
                and t.vbillno like #{vbillno}
            </if>
            <if test="applyUser != null and applyUser.trim() != ''">
                <bind name="applyUser" value="applyUser + '%'"/>
                and t.apply_user like #{applyUser}
            </if>

            <if test="vbillstatus != null">
                and t.vbillstatus = #{vbillstatus}
            </if>
            <if test="status != null  and status != '' and status.indexOf(',') != -1">
                and t.vbillstatus in
                <foreach item="item" index="index" collection="status.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status.trim() != '' and status.indexOf(',') == -1">
                and t.vbillstatus = #{status}
            </if>


            <if test="carrName != null and carrName.trim() != ''">
                <bind name="carrName" value="carrName + '%'"/>
                and t.carr_name like #{carrName}
            </if>
            <if test="balatype != null and balatype.trim() != ''">
                and t.balatype = #{balatype}
            </if>
            <if test="driverName != null and driverName.trim() != ''">
                <bind name="driverName" value="driverName + '%'"/>
                and t.driver_name like #{driverName}
            </if>

            <if test="startDate != null ">
                and t.REG_DATE <![CDATA[   >=  ]]> #{startDate}
            </if>
            <if test="endtDate != null ">
                and t.REG_DATE <![CDATA[   <=  ]]> #{endtDate}
            </if>
            <if test="regUserName != null and regUserName != ''">
                <bind name="regUserName" value="regUserName + '%'"/>
                and sysUser.user_name like  #{regUserName}
            </if>
            <if test="carrierId != null and carrierId.trim() != ''">
                and t.carrier_id = #{carrierId}
            </if>
            <if test="lotId != null and lotId != ''">
                and t.lot_id = #{lotId}
            </if>
            <if test="costTypeFreight != null  and costTypeFreight != ''">
                and t.cost_type_freight in
                <foreach item="item" index="index" collection="costTypeFreight.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="isOilDeposit != null and isOilDeposit != ''">
                and t.is_oil_deposit = #{isOilDeposit}
            </if>
        </where>
        ${params.dataScope}
        order by t.REG_DATE  desc ) t1
    </select>

    <!--根据运单ID查询委托单-->
    <select id="selectEntrustByLot" resultMap="EntrustResult">
        SELECT
            entrust_id,
            del_flag,
            del_date,
            vbillno,
            vbillstatus,
            invoice_vbillno,
            cust_orderno,
            orderno,
            customer_id,
            carrier_id,
            carno_id,
            car_len_id,
            trailer_id,
            trans_line_id,
            req_deli_date,
            req_arri_date,
            memo,
            delivery_id,
            deli_province_id,
            deli_city_id,
            deli_area_id,
            deli_detail_addr,
            deli_contact,
            deli_mobile,
            deli_email,
            arrival_id,
            arri_province_id,
            arri_city_id,
            arri_area_id,
            arri_detail_addr,
            arri_contact,
            arri_mobile,
            arri_email,
            num_count,
            weight_count,
            volume_count,
            cost_amount,
            reg_user_id,
            reg_date,
            balatype,
            act_deli_date,
            act_arri_date,
            tracking_status,
            tracking_memo,
            exp_flag,
            segment_vbillno,
            lot,
            lot_id,
            urgent_level,
            cor_user_id,
            cor_date,
            unconfirm_type,
            unconfirm_memo,
            confirm_userid,
            confirm_date,
            unconfirm_userid,
            unconfirm_date,
            carno,
            driver_name,
            driver_mobile,
            act_deli_man,
            act_deli_memo,
            if_receipt,
            receipt_num,
            receipt_man,
            receipt_date,
            receipt_memo,
            receipt_book_man,
            receipt_book_time,
            receipt_appendix_id,
            billing_corp,
            segment_type,
            cust_code,
            cust_name,
            trans_code,
            trans_name,
            deli_addr_code,
            deli_addr_name,
            deli_pro_name,
            deli_city_name,
            arri_addr_code,
            arri_addr_name,
            arri_pro_name,
            arri_city_name,
            car_type_code,
            car_type_name,
            carr_code,
            carr_name,
            reg_user_name,
            cor_user_name,
            is_ntocc,
            is_fleet_data,
            is_fleet_assign,
	        biz_invoice_id,
	        biz_invoice_vbillno,
	        biz_segment_id,
	        biz_segment_vbillno,
	        biz_entrust_id,
	        biz_entrust_vbillno,
	        biz_entrust_lot_id,
	        biz_entrust_lot_vbillno
        FROM
            t_entrust
          where LOT_ID  = #{entrustLotId}

    </select>

    <!--新增一条应付明细-->
    <insert id="insertPayDetail" parameterType="PayDetail">
        insert into t_pay_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="payDetailId != null  and payDetailId != ''  ">pay_detail_id,</if>
            <if test="vbillno != null  and vbillno != ''  ">vbillno,</if>
            <if test="vbillstatus != null  ">vbillstatus,</if>
            <if test="freeType != null  and freeType != ''  ">free_type,</if>
            <if test="costTypeOnWay != null  and costTypeOnWay != ''  ">cost_type_on_way,</if>
            <if test="costTypeFreight != null  and costTypeFreight != ''  ">cost_type_freight,</if>
            <if test="lotId != null  and lotId != ''  ">lot_id,</if>
            <if test="lotno != null  and lotno != ''  ">lotno,</if>
            <if test="carrierId != null  and carrierId != ''  ">carrier_id,</if>
            <if test="carrCode != null  and carrCode != ''  ">carr_code,</if>
            <if test="carrName != null  and carrName != ''  ">carr_name,</if>
            <if test="balaCorp != null  and balaCorp != ''  ">bala_corp,</if>
            <if test="balatype != null  and balatype != ''  ">balatype,</if>
            <if test="transFeeCount != null  ">trans_fee_count,</if>
            <if test="gotAmount != null  ">got_amount,</if>
            <if test="ungotAmount != null  ">ungot_amount,</if>
            <if test="oilCardNumber != null  and oilCardNumber != ''  ">oil_card_number,</if>
            <if test="checkNo != null  and checkNo != ''  ">check_no,</if>
            <if test="checkHead != null  and checkHead != ''  ">check_head,</if>
            <if test="unconfirmType != null  ">unconfirm_type,</if>
            <if test="unconfirmMemo != null  and unconfirmMemo != ''  ">unconfirm_memo,</if>
            <if test="confirmTime != null  ">confirm_time,</if>
            <if test="confirmUser != null  and confirmUser != ''  ">confirm_user,</if>
            <if test="driverMobile != null  and driverMobile != ''  ">driver_mobile,</if>
            <if test="driverName != null  and driverName != ''  ">driver_name,</if>
            <if test="carno != null  and carno != ''  ">carno,</if>
            <if test="reqDeliDate != null  ">req_deli_date,</if>
            <if test="reqArriDate != null  ">req_arri_date,</if>
            <if test="isClose != null  ">is_close,</if>
            <if test="isAdjust != null  ">is_adjust,</if>
            <if test="adjustMemo != null  and adjustMemo != ''  ">adjust_memo,</if>
            <if test="memo != null  and memo != ''  ">memo,</if>
            <if test="regUserId != null  and regUserId != ''  ">reg_user_id,</if>
            <if test="regDate != null  ">reg_date,</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id,</if>
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id,</if>
            <if test="corDate != null  ">cor_date,</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id,</if>
            <if test="delFlag != null  ">del_flag,</if>
            <if test="delDate != null  ">del_date,</if>
            <if test="delUserId != null  and delUserId != ''  ">del_user_id,</if>
            <if test="recCardNo != null  and recCardNo != ''  ">rec_card_no,</if>
            <if test="recAccount != null  and recAccount != ''  ">rec_account,</if>
            <if test="recBank != null  and recBank != ''  ">rec_bank,</if>
            <if test="isNtocc != null  ">is_ntocc,</if>
            <if test="isOilDeposit != null  ">IS_OIL_DEPOSIT,</if>
            <if test="splitPayDetailId != null  and splitPayDetailId != ''  ">SPLIT_PAY_DETAIL_ID,</if>
            <if test="isFleetData != null and isFleetData != ''">is_fleet_data,</if>
            <if test="isFleetAssign != null and isFleetAssign != ''">is_fleet_assign,</if>
            <if test="fleetReceiveDetailId != null and fleetReceiveDetailId != ''">fleet_receive_detail_id,</if>
            <if test="carrBankId != null and carrBankId != ''">carr_bank_id,</if>
            <if test="incomeRemark != null and incomeRemark != ''">INCOME_REMARK,</if>
            <if test="createByAdjust != null">CREATE_BY_ADJUST,</if>
            <if test="entrustCostFileId != null">entrust_cost_file_id,</if>
            <if test="entrustCostId != null">entrust_cost_id,</if>
            <if test="sourceType != null">source_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="payDetailId != null  and payDetailId != ''  ">#{payDetailId},</if>
            <if test="vbillno != null  and vbillno != ''  ">#{vbillno},</if>
            <if test="vbillstatus != null  ">#{vbillstatus},</if>
            <if test="freeType != null  and freeType != ''  ">#{freeType},</if>
            <if test="costTypeOnWay != null  and costTypeOnWay != ''  ">#{costTypeOnWay},</if>
            <if test="costTypeFreight != null  and costTypeFreight != ''  ">#{costTypeFreight},</if>
            <if test="lotId != null  and lotId != ''  ">#{lotId},</if>
            <if test="lotno != null  and lotno != ''  ">#{lotno},</if>
            <if test="carrierId != null  and carrierId != ''  ">#{carrierId},</if>
            <if test="carrCode != null  and carrCode != ''  ">#{carrCode},</if>
            <if test="carrName != null  and carrName != ''  ">#{carrName},</if>
            <if test="balaCorp != null  and balaCorp != ''  ">#{balaCorp},</if>
            <if test="balatype != null  and balatype != ''  ">#{balatype},</if>
            <if test="transFeeCount != null  ">#{transFeeCount},</if>
            <if test="gotAmount != null  ">#{gotAmount},</if>
            <if test="ungotAmount != null  ">#{ungotAmount},</if>
            <if test="oilCardNumber != null  and oilCardNumber != ''  ">#{oilCardNumber},</if>
            <if test="checkNo != null  and checkNo != ''  ">#{checkNo},</if>
            <if test="checkHead != null  and checkHead != ''  ">#{checkHead},</if>
            <if test="unconfirmType != null  ">#{unconfirmType},</if>
            <if test="unconfirmMemo != null  and unconfirmMemo != ''  ">#{unconfirmMemo},</if>
            <if test="confirmTime != null  ">#{confirmTime},</if>
            <if test="confirmUser != null  and confirmUser != ''  ">#{confirmUser},</if>
            <if test="driverMobile != null  and driverMobile != ''  ">#{driverMobile},</if>
            <if test="driverName != null  and driverName != ''  ">#{driverName},</if>
            <if test="carno != null  and carno != ''  ">#{carno},</if>
            <if test="reqDeliDate != null  ">#{reqDeliDate},</if>
            <if test="reqArriDate != null  ">#{reqArriDate},</if>
            <if test="isClose != null  ">#{isClose},</if>
            <if test="isAdjust != null  ">#{isAdjust},</if>
            <if test="adjustMemo != null  and adjustMemo != ''  ">#{adjustMemo},</if>
            <if test="memo != null  and memo != ''  ">#{memo},</if>
            <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="regScrId != null  and regScrId != ''  ">#{regScrId},</if>
            <if test="corUserId != null  and corUserId != ''  ">#{corUserId},</if>
            <if test="corDate != null  ">#{corDate},</if>
            <if test="corScrId != null  and corScrId != ''  ">#{corScrId},</if>
            <if test="delFlag != null  ">#{delFlag},</if>
            <if test="delDate != null  ">#{delDate},</if>
            <if test="delUserId != null  and delUserId != ''  ">#{delUserId},</if>
            <if test="recCardNo != null  and recCardNo != ''  ">#{recCardNo},</if>
            <if test="recAccount != null  and recAccount != ''  ">#{recAccount},</if>
            <if test="recBank != null  and recBank != ''  ">#{recBank},</if>
            <if test="isNtocc != null  ">#{isNtocc},</if>
            <if test="isOilDeposit != null  ">#{isOilDeposit},</if>
            <if test="splitPayDetailId != null  and splitPayDetailId != ''  ">#{splitPayDetailId},</if>
            <if test="isFleetData != null and isFleetData != ''" >#{isFleetData},</if>
            <if test="isFleetAssign != null and isFleetAssign != ''">#{isFleetAssign},</if>
            <if test="fleetReceiveDetailId != null and fleetReceiveDetailId != ''">#{fleetReceiveDetailId},</if>
            <if test="carrBankId != null and carrBankId != ''">#{carrBankId},</if>
            <if test="incomeRemark != null and incomeRemark != ''">#{incomeRemark},</if>
            <if test="createByAdjust != null">#{createByAdjust},</if>
            <if test="entrustCostFileId != null">#{entrustCostFileId},</if>
            <if test="entrustCostId != null">#{entrustCostId},</if>
            <if test="sourceType != null">#{sourceType},</if>
        </trim>
    </insert>

    <!--新增一条应付明细-->
    <insert id="insertPayDetailAdjust" parameterType="PayDetailAdjust">
        insert into t_pay_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="payDetailId != null  and payDetailId != ''  ">pay_detail_id,</if>
            <if test="vbillno != null  and vbillno != ''  ">vbillno,</if>
            <if test="vbillstatus != null  ">vbillstatus,</if>
            <if test="freeType != null  and freeType != ''  ">free_type,</if>
            <if test="costTypeOnWay != null  and costTypeOnWay != ''  ">cost_type_on_way,</if>
            <if test="costTypeFreight != null  and costTypeFreight != ''  ">cost_type_freight,</if>
            <if test="lotId != null  and lotId != ''  ">lot_id,</if>
            <if test="lotno != null  and lotno != ''  ">lotno,</if>
            <if test="carrierId != null  and carrierId != ''  ">carrier_id,</if>
            <if test="carrCode != null  and carrCode != ''  ">carr_code,</if>
            <if test="carrName != null  and carrName != ''  ">carr_name,</if>
            <if test="balaCorp != null  and balaCorp != ''  ">bala_corp,</if>
            <if test="balatype != null  and balatype != ''  ">balatype,</if>
            <if test="transFeeCount != null  ">trans_fee_count,</if>
            <if test="gotAmount != null  ">got_amount,</if>
            <if test="ungotAmount != null  ">ungot_amount,</if>
            <if test="oilCardNumber != null  and oilCardNumber != ''  ">oil_card_number,</if>
            <if test="checkNo != null  and checkNo != ''  ">check_no,</if>
            <if test="checkHead != null  and checkHead != ''  ">check_head,</if>
            <if test="unconfirmType != null  ">unconfirm_type,</if>
            <if test="unconfirmMemo != null  and unconfirmMemo != ''  ">unconfirm_memo,</if>
            <if test="confirmTime != null  ">confirm_time,</if>
            <if test="confirmUser != null  and confirmUser != ''  ">confirm_user,</if>
            <if test="driverMobile != null  and driverMobile != ''  ">driver_mobile,</if>
            <if test="driverName != null  and driverName != ''  ">driver_name,</if>
            <if test="carno != null  and carno != ''  ">carno,</if>
            <if test="reqDeliDate != null  ">req_deli_date,</if>
            <if test="reqArriDate != null  ">req_arri_date,</if>
            <if test="isClose != null  ">is_close,</if>
            <if test="isAdjust != null  ">is_adjust,</if>
            <if test="adjustMemo != null  and adjustMemo != ''  ">adjust_memo,</if>
            <if test="memo != null  and memo != ''  ">memo,</if>
            <if test="regUserId != null  and regUserId != ''  ">reg_user_id,</if>
            <if test="regDate != null  ">reg_date,</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id,</if>
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id,</if>
            <if test="corDate != null  ">cor_date,</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id,</if>
            <if test="delFlag != null  ">del_flag,</if>
            <if test="delDate != null  ">del_date,</if>
            <if test="delUserId != null  and delUserId != ''  ">del_user_id,</if>
            <if test="recCardNo != null  and recCardNo != ''  ">rec_card_no,</if>
            <if test="recAccount != null  and recAccount != ''  ">rec_account,</if>
            <if test="recBank != null  and recBank != ''  ">rec_bank,</if>
            <if test="isNtocc != null  ">is_ntocc,</if>
            <if test="isOilDeposit != null  ">IS_OIL_DEPOSIT,</if>
            <if test="splitPayDetailId != null  and splitPayDetailId != ''  ">SPLIT_PAY_DETAIL_ID,</if>

            <if test="isFleetData != null and isFleetData != ''">is_fleet_data,</if>
            <if test="isFleetAssign != null and isFleetAssign != ''">is_fleet_assign,</if>
            <if test="fleetReceiveDetailId != null and fleetReceiveDetailId != ''">fleet_receive_detail_id,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="payDetailId != null  and payDetailId != ''  ">#{payDetailId},</if>
            <if test="vbillno != null  and vbillno != ''  ">#{vbillno},</if>
            <if test="vbillstatus != null  ">#{vbillstatus},</if>
            <if test="freeType != null  and freeType != ''  ">#{freeType},</if>
            <if test="costTypeOnWay != null  and costTypeOnWay != ''  ">#{costTypeOnWay},</if>
            <if test="costTypeFreight != null  and costTypeFreight != ''  ">#{costTypeFreight},</if>
            <if test="lotId != null  and lotId != ''  ">#{lotId},</if>
            <if test="lotno != null  and lotno != ''  ">#{lotno},</if>
            <if test="carrierId != null  and carrierId != ''  ">#{carrierId},</if>
            <if test="carrCode != null  and carrCode != ''  ">#{carrCode},</if>
            <if test="carrName != null  and carrName != ''  ">#{carrName},</if>
            <if test="balaCorp != null  and balaCorp != ''  ">#{balaCorp},</if>
            <if test="balatype != null  and balatype != ''  ">#{balatype},</if>
            <if test="transFeeCount != null  ">#{transFeeCount},</if>
            <if test="gotAmount != null  ">#{gotAmount},</if>
            <if test="ungotAmount != null  ">#{ungotAmount},</if>
            <if test="oilCardNumber != null  and oilCardNumber != ''  ">#{oilCardNumber},</if>
            <if test="checkNo != null  and checkNo != ''  ">#{checkNo},</if>
            <if test="checkHead != null  and checkHead != ''  ">#{checkHead},</if>
            <if test="unconfirmType != null  ">#{unconfirmType},</if>
            <if test="unconfirmMemo != null  and unconfirmMemo != ''  ">#{unconfirmMemo},</if>
            <if test="confirmTime != null  ">#{confirmTime},</if>
            <if test="confirmUser != null  and confirmUser != ''  ">#{confirmUser},</if>
            <if test="driverMobile != null  and driverMobile != ''  ">#{driverMobile},</if>
            <if test="driverName != null  and driverName != ''  ">#{driverName},</if>
            <if test="carno != null  and carno != ''  ">#{carno},</if>
            <if test="reqDeliDate != null  ">#{reqDeliDate},</if>
            <if test="reqArriDate != null  ">#{reqArriDate},</if>
            <if test="isClose != null  ">#{isClose},</if>
            <if test="isAdjust != null  ">#{isAdjust},</if>
            <if test="adjustMemo != null  and adjustMemo != ''  ">#{adjustMemo},</if>
            <if test="memo != null  and memo != ''  ">#{memo},</if>
            <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="regScrId != null  and regScrId != ''  ">#{regScrId},</if>
            <if test="corUserId != null  and corUserId != ''  ">#{corUserId},</if>
            <if test="corDate != null  ">#{corDate},</if>
            <if test="corScrId != null  and corScrId != ''  ">#{corScrId},</if>
            <if test="delFlag != null  ">#{delFlag},</if>
            <if test="delDate != null  ">#{delDate},</if>
            <if test="delUserId != null  and delUserId != ''  ">#{delUserId},</if>
            <if test="recCardNo != null  and recCardNo != ''  ">#{recCardNo},</if>
            <if test="recAccount != null  and recAccount != ''  ">#{recAccount},</if>
            <if test="recBank != null  and recBank != ''  ">#{recBank},</if>
            <if test="isNtocc != null  ">#{isNtocc},</if>
            <if test="isOilDeposit != null  ">#{isOilDeposit},</if>
            <if test="splitPayDetailId != null  and splitPayDetailId != ''  ">#{splitPayDetailId},</if>

            <if test="isFleetData != null and isFleetData != ''" >#{isFleetData},</if>
            <if test="isFleetAssign != null and isFleetAssign != ''">#{isFleetAssign},</if>
            <if test="fleetReceiveDetailId != null and fleetReceiveDetailId != ''">#{fleetReceiveDetailId},</if>

        </trim>
    </insert>

    <!--新增一条应付明细-->
    <insert id="insertPayDetailCheck" parameterType="PayDetailCheck">
        insert into T_PAY_DETAIL_CHECK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="payDetailId != null  and payDetailId != ''  ">PAY_DETAIL_CHECK_ID,</if>
            <if test="vbillno != null  and vbillno != ''  ">vbillno,</if>
            <if test="vbillstatus != null  ">vbillstatus,</if>
            <if test="freeType != null  and freeType != ''  ">free_type,</if>
            <if test="costTypeOnWay != null  and costTypeOnWay != ''  ">cost_type_on_way,</if>
            <if test="costTypeFreight != null  and costTypeFreight != ''  ">cost_type_freight,</if>
            <if test="lotId != null  and lotId != ''  ">lot_id,</if>
            <if test="lotno != null  and lotno != ''  ">lotno,</if>
            <if test="carrierId != null  and carrierId != ''  ">carrier_id,</if>
            <if test="carrCode != null  and carrCode != ''  ">carr_code,</if>
            <if test="carrName != null  and carrName != ''  ">carr_name,</if>
            <if test="balaCorp != null  and balaCorp != ''  ">bala_corp,</if>
            <if test="balatype != null  and balatype != ''  ">balatype,</if>
            <if test="transFeeCount != null  ">trans_fee_count,</if>
            <if test="gotAmount != null  ">got_amount,</if>
            <if test="ungotAmount != null  ">ungot_amount,</if>
            <if test="oilCardNumber != null  and oilCardNumber != ''  ">oil_card_number,</if>
            <if test="checkNo != null  and checkNo != ''  ">check_no,</if>
            <if test="checkHead != null  and checkHead != ''  ">check_head,</if>
            <if test="unconfirmType != null  ">unconfirm_type,</if>
            <if test="unconfirmMemo != null  and unconfirmMemo != ''  ">unconfirm_memo,</if>
            <if test="confirmTime != null  ">confirm_time,</if>
            <if test="confirmUser != null  and confirmUser != ''  ">confirm_user,</if>
            <if test="driverMobile != null  and driverMobile != ''  ">driver_mobile,</if>
            <if test="driverName != null  and driverName != ''  ">driver_name,</if>
            <if test="carno != null  and carno != ''  ">carno,</if>
            <if test="reqDeliDate != null  ">req_deli_date,</if>
            <if test="reqArriDate != null  ">req_arri_date,</if>
            <if test="isClose != null  ">is_close,</if>
            <if test="isAdjust != null  ">is_adjust,</if>
            <if test="adjustMemo != null  and adjustMemo != ''  ">adjust_memo,</if>
            <if test="memo != null  and memo != ''  ">memo,</if>
            <if test="regUserId != null  and regUserId != ''  ">reg_user_id,</if>
            <if test="regDate != null  ">reg_date,</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id,</if>
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id,</if>
            <if test="corDate != null  ">cor_date,</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id,</if>
            <if test="delFlag != null  ">del_flag,</if>
            <if test="delDate != null  ">del_date,</if>
            <if test="delUserId != null  and delUserId != ''  ">del_user_id,</if>
            <if test="recCardNo != null  and recCardNo != ''  ">rec_card_no,</if>
            <if test="recAccount != null  and recAccount != ''  ">rec_account,</if>
            <if test="recBank != null  and recBank != ''  ">rec_bank,</if>
            <if test="isNtocc != null  ">is_ntocc,</if>
            <if test="isOilDeposit != null  ">IS_OIL_DEPOSIT,</if>
            <if test="splitPayDetailId != null  and splitPayDetailId != ''  ">SPLIT_PAY_DETAIL_ID,</if>
            <if test="adjustRecordId != null  and adjustRecordId != ''  ">ADJUST_RECORD_ID,</if>
            <if test="adjustCheckStatus != null  ">ADJUST_CHECK_STATUS,</if>

            <if test="isFleetData != null and isFleetData != ''">is_fleet_data,</if>
            <if test="isFleetAssign != null and isFleetAssign != ''">is_fleet_assign,</if>
            <if test="fleetReceiveDetailId != null and fleetReceiveDetailId != ''">fleet_receive_detail_id,</if>
            <if test="billingType != null and billingType != ''">billing_Type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="payDetailId != null  and payDetailId != ''  ">#{payDetailId},</if>
            <if test="vbillno != null  and vbillno != ''  ">#{vbillno},</if>
            <if test="vbillstatus != null  ">#{vbillstatus},</if>
            <if test="freeType != null  and freeType != ''  ">#{freeType},</if>
            <if test="costTypeOnWay != null  and costTypeOnWay != ''  ">#{costTypeOnWay},</if>
            <if test="costTypeFreight != null  and costTypeFreight != ''  ">#{costTypeFreight},</if>
            <if test="lotId != null  and lotId != ''  ">#{lotId},</if>
            <if test="lotno != null  and lotno != ''  ">#{lotno},</if>
            <if test="carrierId != null  and carrierId != ''  ">#{carrierId},</if>
            <if test="carrCode != null  and carrCode != ''  ">#{carrCode},</if>
            <if test="carrName != null  and carrName != ''  ">#{carrName},</if>
            <if test="balaCorp != null  and balaCorp != ''  ">#{balaCorp},</if>
            <if test="balatype != null  and balatype != ''  ">#{balatype},</if>
            <if test="transFeeCount != null  ">#{transFeeCount},</if>
            <if test="gotAmount != null  ">#{gotAmount},</if>
            <if test="ungotAmount != null  ">#{ungotAmount},</if>
            <if test="oilCardNumber != null  and oilCardNumber != ''  ">#{oilCardNumber},</if>
            <if test="checkNo != null  and checkNo != ''  ">#{checkNo},</if>
            <if test="checkHead != null  and checkHead != ''  ">#{checkHead},</if>
            <if test="unconfirmType != null  ">#{unconfirmType},</if>
            <if test="unconfirmMemo != null  and unconfirmMemo != ''  ">#{unconfirmMemo},</if>
            <if test="confirmTime != null  ">#{confirmTime},</if>
            <if test="confirmUser != null  and confirmUser != ''  ">#{confirmUser},</if>
            <if test="driverMobile != null  and driverMobile != ''  ">#{driverMobile},</if>
            <if test="driverName != null  and driverName != ''  ">#{driverName},</if>
            <if test="carno != null  and carno != ''  ">#{carno},</if>
            <if test="reqDeliDate != null  ">#{reqDeliDate},</if>
            <if test="reqArriDate != null  ">#{reqArriDate},</if>
            <if test="isClose != null  ">#{isClose},</if>
            <if test="isAdjust != null  ">#{isAdjust},</if>
            <if test="adjustMemo != null  and adjustMemo != ''  ">#{adjustMemo},</if>
            <if test="memo != null  and memo != ''  ">#{memo},</if>
            <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="regScrId != null  and regScrId != ''  ">#{regScrId},</if>
            <if test="corUserId != null  and corUserId != ''  ">#{corUserId},</if>
            <if test="corDate != null  ">#{corDate},</if>
            <if test="corScrId != null  and corScrId != ''  ">#{corScrId},</if>
            <if test="delFlag != null  ">#{delFlag},</if>
            <if test="delDate != null  ">#{delDate},</if>
            <if test="delUserId != null  and delUserId != ''  ">#{delUserId},</if>
            <if test="recCardNo != null  and recCardNo != ''  ">#{recCardNo},</if>
            <if test="recAccount != null  and recAccount != ''  ">#{recAccount},</if>
            <if test="recBank != null  and recBank != ''  ">#{recBank},</if>
            <if test="isNtocc != null  ">#{isNtocc},</if>
            <if test="isOilDeposit != null  ">#{isOilDeposit},</if>
            <if test="splitPayDetailId != null  and splitPayDetailId != ''  ">#{splitPayDetailId},</if>
            <if test="adjustRecordId != null  and adjustRecordId != ''  ">#{adjustRecordId},</if>
            <if test="adjustCheckStatus != null  ">#{adjustCheckStatus},</if>

            <if test="isFleetData != null and isFleetData != ''" >#{isFleetData},</if>
            <if test="isFleetAssign != null and isFleetAssign != ''">#{isFleetAssign},</if>
            <if test="fleetReceiveDetailId != null and fleetReceiveDetailId != ''">#{fleetReceiveDetailId},</if>
            <if test="billingType != null and billingType != ''">#{billingType},</if>
        </trim>
    </insert>

    <insert id="insertAdjustRecord" parameterType="adjustRecord">
        insert into T_ADJUST_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="adjustRecordId != null  and adjustRecordId != ''  ">ADJUST_RECORD_ID,</if>
            <if test="adjustRecordType != null  ">ADJUST_RECORD_TYPE,</if>
            <if test="memo != null  and memo != ''  ">memo,</if>
            <if test="regUserId != null  and regUserId != ''  ">reg_user_id,</if>
            <if test="regUserName != null  and regUserName != ''  ">REG_USER_NAME,</if>
            <if test="regDate != null  ">reg_date,</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id,</if>
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id,</if>
            <if test="corDate != null  ">cor_date,</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id,</if>
            <if test="delFlag != null  ">del_flag,</if>
            <if test="delDate != null  ">del_date,</if>
            <if test="isCostAdjust != null  and isCostAdjust != ''  ">IS_COST_ADJUST,</if>
            <if test="costAdjustMemo != null  and costAdjustMemo != ''  ">COST_ADJUST_MEMO,</if>
            <if test="tid != null  and tid != ''  ">tid,</if>
            <if test="adjustCheckStatus != null">adjust_check_status,</if>
            <if test="invoiceId != null  and invoiceId != ''  ">invoice_Id,</if>
            <if test="invoiceVbillno != null  and invoiceVbillno != ''  ">invoice_Vbillno,</if>
            <if test="lotId != null  and lotId != ''  ">lot_Id,</if>
            <if test="lotNo != null  and lotNo != ''  ">lot_No,</if>
            <if test="numAdjust != null ">num_Adjust,</if>
            <if test="weightAdjust != null ">weight_Adjust,</if>
            <if test="volumeAdjust != null ">volume_Adjust,</if>
            <if test="numAdjustLot != null ">num_Adjust_Lot,</if>
            <if test="weightAdjustLot != null ">weight_Adjust_Lot,</if>
            <if test="volumeAdjustLot != null ">volume_Adjust_Lot,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="adjustRecordId != null  and adjustRecordId != ''  ">#{adjustRecordId},</if>
            <if test="adjustRecordType != null ">#{adjustRecordType},</if>
            <if test="memo != null  and memo != ''  ">#{memo},</if>
            <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
            <if test="regUserName != null  and regUserName != ''  ">#{regUserName},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="regScrId != null  and regScrId != ''  ">#{regScrId},</if>
            <if test="corUserId != null  and corUserId != ''  ">#{corUserId},</if>
            <if test="corDate != null  ">#{corDate},</if>
            <if test="corScrId != null  and corScrId != ''  ">#{corScrId},</if>
            <if test="delFlag != null  ">#{delFlag},</if>
            <if test="delDate != null  ">#{delDate},</if>
            <if test="isCostAdjust != null  and isCostAdjust != ''  ">#{isCostAdjust},</if>
            <if test="costAdjustMemo != null  and costAdjustMemo != ''  ">#{costAdjustMemo},</if>
            <if test="tid != null  and tid != ''  ">#{tid},</if>
            <if test="adjustCheckStatus != null">#{adjustCheckStatus},</if>
            <if test="invoiceId != null  and invoiceId != ''  ">#{invoiceId},</if>
            <if test="invoiceVbillno != null  and invoiceVbillno != ''  ">#{invoiceVbillno},</if>
            <if test="lotId != null  and lotId != ''  ">#{lotId},</if>
            <if test="lotNo != null  and lotNo != ''  ">#{lotNo},</if>
            <if test="numAdjust != null ">#{numAdjust},</if>
            <if test="weightAdjust != null ">#{weightAdjust},</if>
            <if test="volumeAdjust != null ">#{volumeAdjust},</if>
            <if test="numAdjustLot != null ">#{numAdjustLot},</if>
            <if test="weightAdjustLot != null ">#{weightAdjustLot},</if>
            <if test="volumeAdjustLot != null ">#{volumeAdjustLot},</if>
        </trim>
    </insert>

    <!--根据Id查询应付明细-->
    <select id="selectPayDetailById" resultMap="PayDetailResult">
        SELECT
            pay_detail_id,
            free_type,
            vbillno,
            vbillstatus,
            lotno,
            carrier_id,
            balatype,
            trans_fee_count,
            got_amount,
            ungot_amount,
            memo,
            check_no,
            check_head,
            unconfirm_type,
            unconfirm_memo,
            confirm_time,
            confirm_user,
            driver_mobile,
            driver_name,
            carno,
            req_deli_date,
            req_arri_date,
            carr_code,
            carr_name,
            oil_card_number,
            bala_corp,
            cost_type_on_way,
            cost_type_freight,
            is_adjust,
            lot_id,
            rec_card_no,
            rec_account,
            rec_bank,
            is_ntocc,
            cor_date,
               reg_date,
               reg_user_id,
            CARR_BANK_ID,
            tid,
            APPLY_USER,
            APPLY_memo,
            CHECK_USER_NAME,
            CHECK_DATE,
            CHECK_MEMO,
            CHECK_STATUS,
            IS_OIL_DEPOSIT,
            TAX_AMOUNT,
            del_flag,
            is_fleet_data,
            is_fleet_assign,
            fleet_receive_detail_id,
            g7_syn,
            g7_pay,
            account_type,
            write_fuelcard_id,
            CONSUMBLE_BACK,
            LOT_SP_LOCK,
            WRITE_OFF_TO,
            batch_no,
            entrust_cost_id
        FROM
            t_pay_detail
        where
            pay_detail_id = #{payDetailId} and del_flag = 0
    </select>

    <!--修改应付明细-->
    <update id="editPayDetail">
    update t_pay_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="vbillno != null  and vbillno != ''  ">vbillno = #{vbillno},</if>
            <if test="vbillstatus != null  ">vbillstatus = #{vbillstatus},</if>
            <if test="freeType != null  and freeType != ''  ">free_type = #{freeType},</if>
            <if test="costTypeOnWay != null  and costTypeOnWay != ''  ">cost_type_on_way = #{costTypeOnWay},</if>
            <if test="costTypeFreight != null  and costTypeFreight != ''  ">cost_type_freight = #{costTypeFreight},</if>
            <if test="lotId != null  and lotId != ''  ">lot_id = #{lotId},</if>
            <if test="lotno != null  and lotno != ''  ">lotno = #{lotno},</if>
            <if test="carrierId != null  and carrierId != ''  ">carrier_id = #{carrierId},</if>
            <if test="carrCode != null  and carrCode != ''  ">carr_code = #{carrCode},</if>
            <if test="carrName != null  and carrName != ''  ">carr_name = #{carrName},</if>
            <if test="balaCorp != null  and balaCorp != ''  ">bala_corp = #{balaCorp},</if>
            <if test="balatype != null  and balatype != ''  ">balatype = #{balatype},</if>
            <if test="transFeeCount != null  ">trans_fee_count = #{transFeeCount},</if>
            <if test="gotAmount != null  ">got_amount = #{gotAmount},</if>
            <if test="ungotAmount != null  ">ungot_amount = #{ungotAmount},</if>
            <if test="oilCardNumber != null">oil_card_number = #{oilCardNumber},</if>
            <if test="checkNo != null  and checkNo != ''  ">check_no = #{checkNo},</if>
            <if test="checkHead != null  and checkHead != ''  ">check_head = #{checkHead},</if>
            <if test="unconfirmType != null  ">unconfirm_type = #{unconfirmType},</if>
            <if test="unconfirmMemo != null  and unconfirmMemo != ''  ">unconfirm_memo = #{unconfirmMemo},</if>
            <if test="confirmTime != null  ">confirm_time = #{confirmTime},</if>
            <if test="confirmUser != null  and confirmUser != ''  ">confirm_user = #{confirmUser},</if>
            <if test="driverMobile != null  and driverMobile != ''  ">driver_mobile = #{driverMobile},</if>
            <if test="driverName != null  and driverName != ''  ">driver_name = #{driverName},</if>
            <if test="carno != null  and carno != ''  ">carno = #{carno},</if>
            <if test="reqDeliDate != null  ">req_deli_date = #{reqDeliDate},</if>
            <if test="reqArriDate != null  ">req_arri_date = #{reqArriDate},</if>
            <if test="isClose != null  ">is_close = #{isClose},</if>
            <if test="isAdjust != null  ">is_adjust = #{isAdjust},</if>
            <if test="adjustMemo != null  and adjustMemo != ''  ">adjust_memo = #{adjustMemo},</if>
            <if test="memo != null">memo = #{memo},</if>
        <!--    <if test="regUserId != null  and regUserId != ''  ">reg_user_id = #{regUserId},</if>
            <if test="regDate != null  ">reg_date = #{regDate},</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id = #{regScrId},</if>-->
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id = #{corUserId},</if>
            <if test="corDate != null  ">cor_date = #{corDate},</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id = #{corScrId},</if>
            <if test="delFlag != null  ">del_flag = #{delFlag},</if>
            <if test="delDate != null  ">del_date = #{delDate},</if>
            <if test="delUserId != null  and delUserId != ''  ">del_user_id = #{delUserId},</if>
            <if test="recCardNo != null  ">rec_card_no = #{recCardNo},</if>
            <if test="recAccount != null  ">rec_account = #{recAccount},</if>
            <if test="recBank != null ">rec_bank = #{recBank},</if>
            <if test="carrBankId != null ">carr_bank_id = #{carrBankId},</if>
            <if test="isFleetData != null  and isFleetData != ''"> is_fleet_data = #{isFleetData},</if>
            <if test="isFleetAssign != null and isFleetAssign != ''"> is_fleet_assign = #{isFleetAssign},</if>
            <if test="fleetReceiveDetailId != null and fleetReceiveDetailId != ''"> fleet_receive_detail_id = #{fleetReceiveDetailId},</if>
            <if test="writeOffTime != null ">  write_off_time = #{writeOffTime},</if>
        </trim>
        where pay_detail_id = #{payDetailId}
</update>

    <!--修改应付明细 限制新建状态-->
    <update id="editPayDetailByNewStatus">
        update t_pay_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="vbillno != null  and vbillno != ''  ">vbillno = #{vbillno},</if>
            <if test="vbillstatus != null  ">vbillstatus = #{vbillstatus},</if>
            <if test="freeType != null  and freeType != ''  ">free_type = #{freeType},</if>
            <if test="costTypeOnWay != null  and costTypeOnWay != ''  ">cost_type_on_way = #{costTypeOnWay},</if>
            <if test="costTypeFreight != null  and costTypeFreight != ''  ">cost_type_freight = #{costTypeFreight},</if>
            <if test="lotId != null  and lotId != ''  ">lot_id = #{lotId},</if>
            <if test="lotno != null  and lotno != ''  ">lotno = #{lotno},</if>
            <if test="carrierId != null  and carrierId != ''  ">carrier_id = #{carrierId},</if>
            <if test="carrCode != null  and carrCode != ''  ">carr_code = #{carrCode},</if>
            <if test="carrName != null  and carrName != ''  ">carr_name = #{carrName},</if>
            <if test="balaCorp != null  and balaCorp != ''  ">bala_corp = #{balaCorp},</if>
            <if test="balatype != null  and balatype != ''  ">balatype = #{balatype},</if>
            <if test="transFeeCount != null  ">trans_fee_count = #{transFeeCount},</if>
            <if test="gotAmount != null  ">got_amount = #{gotAmount},</if>
            <if test="ungotAmount != null  ">ungot_amount = #{ungotAmount},</if>
            <if test="oilCardNumber != null">oil_card_number = #{oilCardNumber},</if>
            <if test="checkNo != null  and checkNo != ''  ">check_no = #{checkNo},</if>
            <if test="checkHead != null  and checkHead != ''  ">check_head = #{checkHead},</if>
            <if test="unconfirmType != null  ">unconfirm_type = #{unconfirmType},</if>
            <if test="unconfirmMemo != null  and unconfirmMemo != ''  ">unconfirm_memo = #{unconfirmMemo},</if>
            <if test="confirmTime != null  ">confirm_time = #{confirmTime},</if>
            <if test="confirmUser != null  and confirmUser != ''  ">confirm_user = #{confirmUser},</if>
            <if test="driverMobile != null  and driverMobile != ''  ">driver_mobile = #{driverMobile},</if>
            <if test="driverName != null  and driverName != ''  ">driver_name = #{driverName},</if>
            <if test="carno != null  and carno != ''  ">carno = #{carno},</if>
            <if test="reqDeliDate != null  ">req_deli_date = #{reqDeliDate},</if>
            <if test="reqArriDate != null  ">req_arri_date = #{reqArriDate},</if>
            <if test="isClose != null  ">is_close = #{isClose},</if>
            <if test="isAdjust != null  ">is_adjust = #{isAdjust},</if>
            <if test="adjustMemo != null  and adjustMemo != ''  ">adjust_memo = #{adjustMemo},</if>
            <if test="memo != null">memo = #{memo},</if>
         <!--   <if test="regUserId != null  and regUserId != ''  ">reg_user_id = #{regUserId},</if>
            <if test="regDate != null  ">reg_date = #{regDate},</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id = #{regScrId},</if>-->
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id = #{corUserId},</if>
            <if test="corDate != null  ">cor_date = #{corDate},</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id = #{corScrId},</if>
            <if test="delFlag != null  ">del_flag = #{delFlag},</if>
            <if test="delDate != null  ">del_date = #{delDate},</if>
            <if test="delUserId != null  and delUserId != ''  ">del_user_id = #{delUserId},</if>
            <if test="recCardNo != null  ">rec_card_no = #{recCardNo},</if>
            <if test="recAccount != null  ">rec_account = #{recAccount},</if>
            <if test="recBank != null ">rec_bank = #{recBank},</if>
            <if test="carrBankId != null ">carr_bank_id = #{carrBankId},</if>

            <if test="isFleetData != null  and isFleetData != ''"> is_fleet_data = #{isFleetData},</if>
            <if test="isFleetAssign != null and isFleetAssign != ''"> is_fleet_assign = #{isFleetAssign},</if>
            <if test="fleetReceiveDetailId != null and fleetReceiveDetailId != ''"> fleet_receive_detail_id = #{fleetReceiveDetailId},</if>

        </trim>
        where pay_detail_id = #{payDetailId} and vbillstatus = 0
    </update>

    <update id="updatePayDetailStatus">
        update t_pay_detail set vbillstatus = #{vbillstatus}
        where pay_detail_id = #{payDetailId}
        and vbillstatus != #{vbillstatus}
    </update>

    <update id="updatePayDetailStatusByCheckStatus">
        update t_pay_detail set vbillstatus = #{vbillstatus}
        <if test="corUserId != null  and corUserId != ''  ">,cor_user_id = #{corUserId}</if>
        <if test="corDate != null  ">,cor_date = #{corDate}</if>
        <if test="corScrId != null  and corScrId != ''  ">,cor_scr_id = #{corScrId}</if>
        where pay_detail_id = #{payDetailId}
        and (vbillstatus = #{checkStatus} or vbillstatus = #{checkStatus1})
    </update>


    <update id="updatePayDetailStatusByList">
        update T_PAY_DETAIL SET
        <if test="payDetail.unconfirmType != null">
            unconfirm_type = #{payDetail.unconfirmType},
        </if>
        <if test="payDetail.unconfirmMemo != null and payDetail.unconfirmMemo != ''">
            unconfirm_memo = #{payDetail.unconfirmMemo},
        </if>
        vbillstatus = #{payDetail.vbillstatus},
        ungot_amount = trans_fee_count - got_amount,
        cor_date = #{payDetail.corDate},
        cor_user_id = #{payDetail.corUserId}
        where vbillstatus in
        <foreach item="status" collection="statusArr" open="(" separator="," close=")">
            #{status}
        </foreach>
        and  PAY_DETAIL_ID in
        <foreach item="ids" collection="list" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </update>



    <!--新增对账单-->
    <insert id="insertPayCheckSheet">
        insert into T_PAY_CHECK_SHEET
        <trim prefix="(" suffix=")" suffixOverrides=",">
            pay_check_sheet_id,
            vbillno,
            vbillstatus,
            carrier_id,
            year,
            month,
            total_amount,
            ungot_amount,
            memo,
            carr_code,
            carr_name,
            hand_verification,
            reg_user_id,
            reg_date,
            cor_date,
            cor_user_id,
            bala_corp,
            reg_scr_id,
            cor_scr_id,
            is_ntocc,
            pay_check_sheet_name,
            oil_amount,
            is_fleet_data,
            is_exist_fleet_data,
            LOT_G7_END,
            <if test="applicationAmount != null  ">application_amount,</if>
            <if test="applicationAmountOil != null  ">application_amount_oil,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{payCheckSheetId},
            #{vbillno,jdbcType=VARCHAR},
            #{vbillstatus,jdbcType=VARCHAR},
            #{carrierId,jdbcType=VARCHAR},
            #{year,jdbcType=VARCHAR},
            #{month,jdbcType=VARCHAR},
            #{totalAmount,jdbcType=VARCHAR},
            #{ungotAmount,jdbcType=VARCHAR},
            #{memo,jdbcType=VARCHAR},
            #{carrCode,jdbcType=VARCHAR},
            #{carrName,jdbcType=VARCHAR},
            #{handVerification,jdbcType=VARCHAR},
            #{regUserId,jdbcType=VARCHAR},
            #{regDate,jdbcType=VARCHAR},
            #{regDate,jdbcType=VARCHAR},
            #{regUserId,jdbcType=VARCHAR},
            #{balaCorp,jdbcType=VARCHAR},
            #{regScrId,jdbcType=VARCHAR},
            #{corScrId,jdbcType=VARCHAR},
            #{isNtocc,jdbcType=VARCHAR},
            #{payCheckSheetName,jdbcType=VARCHAR},
            #{oilAmount,jdbcType=VARCHAR},
            #{isFleetData,jdbcType=VARCHAR},
            #{isExistFleetData,jdbcType=VARCHAR},
            #{lotG7End,jdbcType=INTEGER},
            <if test="applicationAmount != null">#{applicationAmount},</if>
            <if test="applicationAmountOil != null">#{applicationAmountOil},</if>
        </trim>
    </insert>



    <!--插入应付明细/应付对账的中间表-->
    <insert id="insertPaySheetB">
        insert into T_PAY_CHECK_SHEET_B
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="payCheckSheetBId != null  and payCheckSheetBId != ''  ">pay_check_sheet_b_id,</if>
            <if test="payCheckSheetId != null  and payCheckSheetId != ''  ">pay_check_sheet_id,</if>
            <if test="payDetailId != null  and payDetailId != ''  ">pay_detail_id,</if>
            <if test="regUserId != null  and regUserId != ''  ">reg_user_id,</if>
            <if test="regDate != null  ">reg_date,</if>
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id,</if>
            <if test="corDate != null  ">cor_date,</if>
            <if test="delFalg != null  ">del_falg,</if>
            <if test="delUserId != null  and delUserId != ''  ">del_user_id,</if>
            <if test="delDate != null  ">del_date,</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id,</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="payCheckSheetBId != null  and payCheckSheetBId != ''  ">#{payCheckSheetBId},</if>
            <if test="payCheckSheetId != null  and payCheckSheetId != ''  ">#{payCheckSheetId},</if>
            <if test="payDetailId != null  and payDetailId != ''  ">#{payDetailId},</if>
            <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="corUserId != null  and corUserId != ''  ">#{corUserId},</if>
            <if test="corDate != null  ">#{corDate},</if>
            <if test="delFalg != null  ">#{delFalg},</if>
            <if test="delUserId != null  and delUserId != ''  ">#{delUserId},</if>
            <if test="delDate != null  ">#{delDate},</if>
            <if test="regScrId != null  and regScrId != ''  ">#{regScrId},</if>
            <if test="corScrId != null  and corScrId != ''  ">#{corScrId},</if>
        </trim>
    </insert>

    <!--批量删除应付明细-->
    <update id="deletePayDetailByIds" parameterType="int">
        update T_PAY_DETAIL set
        del_flag = #{payDetail.delFlag},
        del_date = #{payDetail.delDate},
        DEL_USER_ID = #{payDetail.delUserId}
        where
        vbillstatus = #{payDetail.vbillstatus} and
        pay_detail_id in
        <foreach item="ids" collection="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </update>

    <!--批量删除应付明细与应付对账的中间表-->
    <update id="deletePayDetailSheetByIds" parameterType="int">
        update t_pay_check_sheet_b set
        DEL_FALG = #{payDetail.delFlag},
        del_date = #{payDetail.delDate}
        where
        PAY_DETAIL_ID in
        <foreach item="ids" collection="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </update>

    <!--以承运商为单位展示的应付明细列表-->
    <select id="selectPayDetailListByCarrier" resultMap="PayDetailResult">
        SELECT pay.CARR_NAME,
        pay.CARR_CODE,
        pay.CARRIER_ID,
        <!--总条数-->
        count(1) as sizes,
        sum(pay.TRANS_FEE_COUNT) AS TRANS_FEE_COUNT,
        sum(pay.GOT_AMOUNT) AS GOT_AMOUNT,
        sum(pay.UNGOT_AMOUNT) AS UNGOT_AMOUNT
        FROM T_PAY_DETAIL pay
        left join T_PAY_CHECK_SHEET_B sheet
        on pay.PAY_DETAIL_ID = sheet.PAY_DETAIL_ID
        <where>
            pay.del_flag = 0
            <!-- 已申请=6 -->
            and pay.vbillstatus = 6
            <!-- 部分核销=3 -->
            or pay.vbillstatus = 3
            <!-- 加入对账单的不展示 -->
            and sheet.PAY_DETAIL_ID is null
            <if test="carrName != null and carrName.trim() != ''">
                <bind name="carrName" value="carrName + '%'"/>
                and pay.CARR_NAME like #{carrName}
            </if>

        </where>
        GROUP BY
        pay.CARRIER_ID,
        pay.CARR_CODE,
        pay.CARR_NAME
    </select>

    <!--根据运单号获取符合无车承运人的发货单-->
    <select id="selectInvoiceByLot" resultMap="InvoiceResult">
        select
            t.invoice_id,
            t.vbillno,
            t.cust_orderno,
            t.vbillstatus,
            t.group_id,
            t.customer_id,
            t.bala_customer_id,
            t.bala_corp_id,
            t.bala_dept,
            t.station_dept,
            t.residents_id,
            t.trans_line_id,
            t.dispatcher_id,
            t.bala_type,
            t.urgent_level,
            t.if_billing,
            t.req_deli_date,
            t.req_arri_date,
            t.order_date,
            t.psndoc,
            t.sales_dept,
            t.memo,
            t.delivery_id,
            t.deli_province_id,
            t.deli_city_id,
            t.deli_area_id,
            t.deli_detail_addr,
            t.deli_contact,
            t.deli_mobile,
            t.deli_email,
            t.arrival_id,
            t.arri_province_id,
            t.arri_city_id,
            t.arri_area_id,
            t.arri_detail_addr,
            t.arri_contact,
            t.arri_mobile,
            t.arri_email,
            t.if_backbill,
            t.backbill_num,
            t.if_ins_receipt,
            t.receipt_amount,
            t.receipt_memo,
            t.insurance_appendix_id,
            t.insurance_no,
            t.insurance_company,
            t.num_count,
            t.weight_count,
            t.volume_count,
            t.cost_amount,
            t.car_len,
            t.car_type,
            t.unconfirm_type,
            t.unconfirm_memo,
            t.billing_corp,
            t.deli_pro_name,
            t.deli_city_name,
            t.deli_area_name,
            t.deli_addr_code,
            t.deli_addr_name,
            t.arri_pro_name,
            t.arri_city_name,
            t.arri_area_name,
            t.arri_addr_code,
            t.arri_addr_name,
            t.cust_code,
            t.cust_name,
            t.bala_code,
            t.bala_name,
            t.urgent_level_name,
            t.car_len_name,
            t.close_note,
            t.trans_line_name,
            t.group_name,
            t.dispatcher_name,
            t.app_deli_contact,
            t.app_deli_mobile,
            t.src_type,
            t.is_rating,
            t.is_ntocc,
            t.businesstypename,
            t.synchronizationtime,
            t.isexception
        FROM
            T_INVOICE t
                left join T_ENTRUST te on te.ORDERNO = t.INVOICE_ID
        WHERE
            te.IS_NTOCC = 1 and
            te.LOT_ID = #{lot}
    </select>

    <update id="updatePayDetailById" parameterType="PayDetail">
        update t_pay_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="vbillno != null  and vbillno != ''  ">vbillno = #{vbillno},</if>
            <if test="vbillstatus != null  ">vbillstatus = #{vbillstatus},</if>
            <if test="freeType != null  and freeType != ''  ">free_type = #{freeType},</if>
            <if test="costTypeOnWay != null  and costTypeOnWay != ''  ">cost_type_on_way = #{costTypeOnWay},</if>
            <if test="costTypeFreight != null  and costTypeFreight != ''  ">cost_type_freight = #{costTypeFreight},</if>
            <if test="lotId != null  and lotId != ''  ">lot_id = #{lotId},</if>
            <if test="lotno != null  and lotno != ''  ">lotno = #{lotno},</if>
            <if test="carrierId != null  and carrierId != ''  ">carrier_id = #{carrierId},</if>
            <if test="carrCode != null  and carrCode != ''  ">carr_code = #{carrCode},</if>
            <if test="carrName != null  and carrName != ''  ">carr_name = #{carrName},</if>
            <if test="balaCorp != null  and balaCorp != ''  ">bala_corp = #{balaCorp},</if>
            <if test="balatype != null  and balatype != ''  ">balatype = #{balatype},</if>
            <if test="transFeeCount != null  ">trans_fee_count = #{transFeeCount},</if>
            <if test="gotAmount != null  ">got_amount = #{gotAmount},</if>
            <if test="ungotAmount != null  ">ungot_amount = #{ungotAmount},</if>
            <if test="oilCardNumber != null  and oilCardNumber != ''  ">oil_card_number = #{oilCardNumber},</if>
            <if test="checkNo != null  and checkNo != ''  ">check_no = #{checkNo},</if>
            <if test="checkHead != null  and checkHead != ''  ">check_head = #{checkHead},</if>
            <if test="unconfirmType != null  ">unconfirm_type = #{unconfirmType},</if>
            <if test="unconfirmMemo != null  and unconfirmMemo != ''  ">unconfirm_memo = #{unconfirmMemo},</if>
            <if test="confirmTime != null  ">confirm_time = #{confirmTime},</if>
            <if test="confirmUser != null  and confirmUser != ''  ">confirm_user = #{confirmUser},</if>
            <if test="driverMobile != null  and driverMobile != ''  ">driver_mobile = #{driverMobile},</if>
            <if test="driverName != null  and driverName != ''  ">driver_name = #{driverName},</if>
            <if test="carno != null  and carno != ''  ">carno = #{carno},</if>
            <if test="reqDeliDate != null  ">req_deli_date = #{reqDeliDate},</if>
            <if test="reqArriDate != null  ">req_arri_date = #{reqArriDate},</if>
            <if test="isClose != null  ">is_close = #{isClose},</if>
            <if test="isAdjust != null  ">is_adjust = #{isAdjust},</if>
            <if test="adjustMemo != null  and adjustMemo != ''  ">adjust_memo = #{adjustMemo},</if>
            <if test="memo != null  and memo != ''  ">memo = #{memo},</if>
         <!--   <if test="regUserId != null  and regUserId != ''  ">reg_user_id = #{regUserId},</if>
            <if test="regDate != null  ">reg_date = #{regDate},</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id = #{regScrId},</if>-->
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id = #{corUserId},</if>
            <if test="corDate != null  ">cor_date = #{corDate},</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id = #{corScrId},</if>
            <if test="delFlag != null  ">del_flag = #{delFlag},</if>
            <if test="delDate != null  ">del_date = #{delDate},</if>
            <if test="delUserId != null  and delUserId != ''  ">del_user_id = #{delUserId},</if>
            <if test="recCardNo != null  and recCardNo != ''  ">rec_card_no = #{recCardNo},</if>
            <if test="recAccount != null  and recAccount != ''  ">rec_account = #{recAccount},</if>
            <if test="recBank != null  and recBank != ''  ">rec_bank = #{recBank},</if>
            <if test="bakcWriteType != null ">  bakc_write_type = #{bakcWriteType},</if>
            <if test="backWriteMemo != null and  backWriteMemo != '' ">  back_write_memo = #{backWriteMemo},</if>
            <if test="backWriteTime != null ">  back_write_time = #{backWriteTime},</if>
            <if test="isFleetData != null  and isFleetData != ''"> is_fleet_data = #{isFleetData},</if>
            <if test="isFleetAssign != null and isFleetAssign != ''"> is_fleet_assign = #{isFleetAssign},</if>
            <if test="fleetReceiveDetailId != null and fleetReceiveDetailId != ''"> fleet_receive_detail_id = #{fleetReceiveDetailId},</if>
            <if test="writeOffTime != null ">  write_off_time = #{writeOffTime},</if>
            <if test="consumbleBack != null and consumbleBack != '' or consumbleBack == 0">  CONSUMBLE_BACK = #{consumbleBack},</if>
            <if test="incomeRemark != null ">  income_remark = #{incomeRemark},</if>
            <if test="lotSpLock != null">lot_sp_lock = #{lotSpLock},</if>
            <if test="writeOffTo != null">write_off_to = #{writeOffTo},</if>
        </trim>
        where pay_detail_id = #{payDetailId}
</update>

    <update id="updatePayDetailByIdRx" parameterType="PayDetail">
        update t_pay_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="payDetail.vbillno != null  and payDetail.vbillno != ''  ">vbillno = #{payDetail.vbillno},</if>
            <if test="payDetail.vbillstatus != null  ">vbillstatus = #{payDetail.vbillstatus},</if>
            <if test="payDetail.freeType != null  and payDetail.freeType != ''  ">free_type = #{payDetail.freeType},</if>
            <if test="payDetail.costTypeOnWay != null  and payDetail.costTypeOnWay != ''  ">cost_type_on_way = #{payDetail.costTypeOnWay},</if>
            <if test="payDetail.costTypeFreight != null  and payDetail.costTypeFreight != ''  ">cost_type_freight = #{payDetail.costTypeFreight},</if>
            <if test="payDetail.lotId != null  and payDetail.lotId != ''  ">lot_id = #{payDetail.lotId},</if>
            <if test="payDetail.lotno != null  and payDetail.lotno != ''  ">lotno = #{payDetail.lotno},</if>
            <if test="payDetail.carrierId != null  and payDetail.carrierId != ''  ">carrier_id = #{payDetail.carrierId},</if>
            <if test="payDetail.carrCode != null  and payDetail.carrCode != ''  ">carr_code = #{payDetail.carrCode},</if>
            <if test="payDetail.carrName != null  and payDetail.carrName != ''  ">carr_name = #{payDetail.carrName},</if>
            <if test="payDetail.balaCorp != null  and payDetail.balaCorp != ''  ">bala_corp = #{payDetail.balaCorp},</if>
            <if test="payDetail.balatype != null  and payDetail.balatype != ''  ">balatype = #{payDetail.balatype},</if>
            <if test="payDetail.transFeeCount != null  ">trans_fee_count = #{payDetail.transFeeCount},</if>
            <if test="payDetail.gotAmount != null  ">got_amount = #{payDetail.gotAmount},</if>
            <if test="payDetail.ungotAmount != null  ">ungot_amount = #{payDetail.ungotAmount},</if>
            <if test="payDetail.oilCardNumber != null  and payDetail.oilCardNumber != ''  ">oil_card_number = #{payDetail.oilCardNumber},</if>
            <if test="payDetail.checkNo != null  and payDetail.checkNo != ''  ">check_no = #{payDetail.checkNo},</if>
            <if test="payDetail.checkHead != null  and payDetail.checkHead != ''  ">check_head = #{payDetail.checkHead},</if>
            <if test="payDetail.unconfirmType != null  ">unconfirm_type = #{payDetail.unconfirmType},</if>
            <if test="payDetail.unconfirmMemo != null  and payDetail.unconfirmMemo != ''  ">unconfirm_memo = #{payDetail.unconfirmMemo},</if>
            <if test="payDetail.confirmTime != null  ">confirm_time = #{payDetail.confirmTime},</if>
            <if test="payDetail.confirmUser != null  and payDetail.confirmUser != ''  ">confirm_user = #{payDetail.confirmUser},</if>
            <if test="payDetail.driverMobile != null  and payDetail.driverMobile != ''  ">driver_mobile = #{payDetail.driverMobile},</if>
            <if test="payDetail.driverName != null  and payDetail.driverName != ''  ">driver_name = #{payDetail.driverName},</if>
            <if test="payDetail.carno != null  and payDetail.carno != ''  ">carno = #{payDetail.carno},</if>
            <if test="payDetail.reqDeliDate != null  ">req_deli_date = #{payDetail.reqDeliDate},</if>
            <if test="payDetail.reqArriDate != null  ">req_arri_date = #{payDetail.reqArriDate},</if>
            <if test="payDetail.isClose != null  ">is_close = #{payDetail.isClose},</if>
            <if test="payDetail.isAdjust != null  ">is_adjust = #{payDetail.isAdjust},</if>
            <if test="payDetail.adjustMemo != null  and payDetail.adjustMemo != ''  ">adjust_memo = #{payDetail.adjustMemo},</if>
            <if test="payDetail.memo != null  and payDetail.memo != ''  ">memo = #{payDetail.memo},</if>
            <!--   <if test="regUserId != null  and regUserId != ''  ">reg_user_id = #{payDetail.regUserId},</if>
               <if test="regDate != null  ">reg_date = #{payDetail.regDate},</if>
               <if test="regScrId != null  and regScrId != ''  ">reg_scr_id = #{payDetail.regScrId},</if>-->
            <if test="payDetail.corUserId != null  and payDetail.corUserId != ''  ">cor_user_id = #{payDetail.corUserId},</if>
            <if test="payDetail.corDate != null  ">cor_date = #{payDetail.corDate},</if>
            <if test="payDetail.corScrId != null  and payDetail.corScrId != ''  ">cor_scr_id = #{payDetail.corScrId},</if>
            <if test="payDetail.delFlag != null  ">del_flag = #{payDetail.delFlag},</if>
            <if test="payDetail.delDate != null  ">del_date = #{payDetail.delDate},</if>
            <if test="payDetail.delUserId != null  and payDetail.delUserId != ''  ">del_user_id = #{payDetail.delUserId},</if>
            <if test="payDetail.recCardNo != null  and payDetail.recCardNo != ''  ">rec_card_no = #{payDetail.recCardNo},</if>
            <if test="payDetail.recAccount != null  and payDetail.recAccount != ''  ">rec_account = #{payDetail.recAccount},</if>
            <if test="payDetail.recBank != null  and payDetail.recBank != ''  ">rec_bank = #{payDetail.recBank},</if>
            <if test="payDetail.bakcWriteType != null ">  bakc_write_type = #{payDetail.bakcWriteType},</if>
            <if test="payDetail.backWriteMemo != null and  payDetail.backWriteMemo != '' ">  back_write_memo = #{payDetail.backWriteMemo},</if>
            <if test="payDetail.backWriteTime != null ">  back_write_time = #{payDetail.backWriteTime},</if>
            <if test="payDetail.isFleetData != null  and payDetail.isFleetData != ''"> is_fleet_data = #{payDetail.isFleetData},</if>
            <if test="payDetail.isFleetAssign != null and payDetail.isFleetAssign != ''"> is_fleet_assign = #{payDetail.isFleetAssign},</if>
            <if test="payDetail.fleetReceiveDetailId != null and payDetail.fleetReceiveDetailId != ''"> fleet_receive_detail_id = #{payDetail.fleetReceiveDetailId},</if>
            <if test="payDetail.writeOffTime != null ">  write_off_time = #{payDetail.writeOffTime},</if>
            <if test="payDetail.consumbleBack != null and payDetail.consumbleBack != '' or payDetail.consumbleBack == 0">  CONSUMBLE_BACK = #{payDetail.consumbleBack},</if>
        </trim>
        where pay_detail_id = #{payDetail.payDetailId}
          and vbillstatus = #{status}
    </update>

    <select id="selectPayDetailByIdsForSheet" resultMap="PayDetailResult">
        select
        t.pay_detail_id,
        t.vbillno,
        t.vbillstatus,
        t.free_type,
        t.cost_type_on_way,
        t.cost_type_freight,
        t.lot_id,
        t.lotno,
        t.carrier_id,
        t.carr_code,
        t.carr_name,
        t.bala_corp,
        t.balatype,
        t.trans_fee_count,
        t.got_amount,
        t.ungot_amount,
        t.oil_card_number,
        t.check_no,
        t.check_head,
        t.unconfirm_type,
        t.unconfirm_memo,
        t.confirm_time,
        t.confirm_user,
        t.driver_mobile,
        t.driver_name,
        t.carno,
        <!--t.req_deli_date,-->
        t.req_arri_date,
        t.is_close,
        t.is_adjust,
        t.adjust_memo,
        t.memo,
        t.reg_user_id,
        t.reg_date,
        t.reg_scr_id,
        t.cor_user_id,
        t.cor_date,
        t.cor_scr_id,
        t.del_flag,
        t.del_date,
        t.del_user_id,
        t.rec_card_no,
        t.rec_account,
        t.rec_bank,
        t.is_ntocc,
        t.apply_user,
        t.apply_time,
        t.is_fleet_data,
        t.is_fleet_assign,
        t.fleet_receive_detail_id,
        t.CONSUMBLE_BACK,
        t2.req_deli_date,
        t2.deli_province_name || t2.deli_city_name || t2.deli_area_name deli_addr,
        t2.arri_province_name || t2.arri_city_name || t2.arri_area_name arri_addr,
               t2.g7_syn lot_g7_syn,
               t2.g7_msg lot_g7_msg,
               t2.g7_start lot_g7_start,
               t2.g7_end lot_g7_end
        from t_pay_detail t
        left join t_entrust_lot t2 on t2.entrust_lot_id = t.lot_id
        where
        t.pay_detail_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectPayDetailByIds" resultMap="PayDetailResult">
        <include refid="selectTPayDetailVo"/>
        where
            pay_detail_id in
        <foreach item="ids" collection="list" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </select>
    <!-- 获取 Sequence  -->
    <select id="getSeq" resultType="java.lang.String" useCache="false" flushCache="true">
        select SEQ_PAY_DETAIL.nextval from dual
    </select>


    <!--获取应付明细列表-->
    <select id="selectPayDetailListByLotOutId" resultMap="PayDetailResult">
        SELECT
        t_pay_detail.pay_detail_id,
        t_pay_detail.trans_fee_count,
        t_pay_detail.oil_card_number,
        t_pay_detail.memo,
        t_pay_detail.balatype,
        t_pay_detail.free_type,
        t_pay_detail.cost_type_on_way,
        t_pay_detail.COST_TYPE_FREIGHT,
        t_pay_detail.rec_card_no,
        t_pay_detail.rec_bank,
        t_pay_detail.rec_account
        FROM
        t_pay_detail
        left join t_entrust on t_entrust.lot = t_pay_detail.lotno
        left join t_segment on t_segment.SEGMENT_ID=t_entrust.SEGMENT_ID
        left join t_lot_out on t_lot_out.segment_id=t_segment.segment_id
        where
           t_pay_detail.del_flag != 1
           and t_lot_out.lot_out_id=#{lotOutId}
           and t_pay_detail.free_type ='0'
    </select>


    <select id="selectInvoiceByEntrustId" resultMap="InvoiceResult">
        select  distinct
            t.invoice_id,
            t.del_flag,
            t.del_userid,
            t.del_date,
            t.vbillno,
            t.cust_orderno,
            t.vbillstatus,
            t.group_id,
            t.customer_id,
            t.bala_customer_id,
            t.bala_corp_id,
            t.bala_dept,
            t.station_dept,
            t.residents_id,
            t.trans_line_id,
            t.dispatcher_id,
            t.dispatcher_name,
            t.bala_type,
            t.urgent_level,
            t.if_billing,
            t.req_deli_date,
            t.req_arri_date,
            t.order_date,
            t.psndoc,
            t.sales_dept,
            t.memo,
            t.delivery_id,
            t.deli_province_id,
            t.deli_city_id,
            t.deli_area_id,
            t.deli_detail_addr,
            t.deli_contact,
            t.deli_mobile,
            t.deli_email,
            t.arrival_id,
            t.arri_province_id,
            t.arri_city_id,
            t.arri_area_id,
            t.arri_detail_addr,
            t.arri_contact,
            t.arri_mobile,
            t.arri_email,
            t.if_backbill,
            t.backbill_num,
            t.if_ins_receipt,
            t.receipt_amount,
            t.receipt_memo,
            t.insurance_no,
            t.num_count,
            t.weight_count,
            t.volume_count,
            t.cost_amount,
            t.car_len,
            t.car_type,
            t.unconfirm_type,
            t.unconfirm_memo,
            t.billing_corp,
            t.deli_pro_name,
            t.deli_city_name,
            t.deli_area_name,
            t.deli_addr_code,
            t.deli_addr_name,
            t.arri_pro_name,
            t.arri_city_name,
            t.arri_area_name,
            t.arri_addr_code,
            t.arri_addr_name,
            t.cust_code,
            t.cust_name,
            t.bala_code,
            t.bala_name,
            t.trans_code,
            t.trans_name,
            t.urgent_level_name,
            t.car_len_name,
            t.car_type_name,
            t.confirm_userid,
            t.confirm_date,
            t.unconfirm_userid,
            t.unconfirm_date,
            t.close_note,
            t.reg_user_id,
            t.reg_user_name,
            t.reg_date,
            t.cor_user_id,
            t.cor_user_name,
            t.cor_date,
            t.reg_scr_id,
            t.cor_scr_id,
            t.trans_line_name,
            t.group_name
        FROM
            T_INVOICE t
                left join T_ENTRUST te on te.ORDERNO = t.INVOICE_ID
        WHERE
            te.ENTRUST_ID = #{entrustId}

    </select>
    <select id="selectPaySheetRecord" resultType="com.ruoyi.tms.domain.finance.PaySheetRecord">
        select
            sheetrecord.PAY_AMOUNT payAmount,
            sheetrecord.GOT_AMOUNT gotAmount,
            sheetrecord.UNGOT_AMOUNT ungotAmount
        from T_PAY_SHEET_RECORD sheetrecord
            left join T_PAY_CHECK_SHEET_B b on sheetrecord.PAY_CHECK_SHEET_ID = b.PAY_CHECK_SHEET_ID
            left join T_PAY_DETAIL detail on detail.PAY_DETAIL_ID = b.PAY_DETAIL_ID
        where
            detail.LOT_ID = #{entrustLotId} and sheetrecord.DEL_FALG = 0 and detail.DEL_FLAG = 0 and b.DEL_FALG = 0 and rownum = 1
    </select>

    <!--申请付款-->
    <update id="applyPay">
        <!--申请付款 改变状态 6=已申请 1=已确认-->
        update t_pay_detail
        <trim prefix="SET" suffixOverrides=",">
            vbillstatus = 6,
            cor_date = #{corDate},
            cor_scr_id = #{corScrId},
            cor_user_id = #{corUserId},
            apply_time = #{applyTime},
            apply_user = #{applyUser},
            req_pay_date = #{reqPayDate},
            tid = #{tid,jdbcType=VARCHAR},
            <if test="accountType != null and accountType != ''">account_type = #{accountType},</if>
            <if test="writeFuelcardId != null and writeFuelcardId != ''">write_fuelcard_id = #{writeFuelcardId},</if>
            <if test="applyMemo != null and applyMemo != ''">apply_memo = #{applyMemo},</if>
            <if test="oilCardNumber != null and oilCardNumber != ''">oil_card_number = #{oilCardNumber},</if>
            <if test="recCardNo != null and recCardNo != '' ">rec_card_no = #{recCardNo},</if>
            <if test="recAccount != null and recAccount != '' ">rec_account = #{recAccount},</if>
            <if test="recBank != null and recBank != ''">rec_bank = #{recBank},</if>
            <if test="carrBankId != null and carrBankId != ''">carr_bank_id = #{carrBankId},</if>
            <if test="batchNo != null and batchNo != ''">batch_No = #{batchNo},</if>
        </trim>
        where pay_detail_id = #{payDetailId} and vbillstatus = 1
    </update>

    <!--申请付款审核-->
    <update id="saveApplyCheck">
        <!--申请付款 改变状态 6=已申请 1=已确认 8=审核中 -->
        update t_pay_detail
        <trim prefix="SET" suffixOverrides=",">
            vbillstatus = 8,
            cor_date = #{corDate},
            cor_scr_id = #{corScrId},
            cor_user_id = #{corUserId},
            apply_time = #{applyTime},
            apply_user = #{applyUser},
            req_pay_date = #{reqPayDate},
            tid = #{tid,jdbcType=VARCHAR},
            <if test="accountType != null and accountType != ''">account_type = #{accountType},</if>
            <if test="writeFuelcardId != null and writeFuelcardId != ''">write_fuelcard_id = #{writeFuelcardId},</if>
            <if test="applyMemo != null and applyMemo != ''">apply_memo = #{applyMemo},</if>
            <if test="oilCardNumber != null and oilCardNumber != ''">oil_card_number = #{oilCardNumber},</if>
            <if test="recCardNo != null and recCardNo != '' ">rec_card_no = #{recCardNo},</if>
            <if test="recAccount != null and recAccount != '' ">rec_account = #{recAccount},</if>
            <if test="recBank != null and recBank != ''">rec_bank = #{recBank},</if>
            <if test="carrBankId != null and carrBankId != ''">carr_bank_id = #{carrBankId},</if>
        </trim>
        where pay_detail_id = #{payDetailId} and vbillstatus = 1
    </update>

    <!--申请付款审核-->
    <update id="updatePayDetailStatusApplyPay">
        <!--申请付款 改变状态 6=已申请 1=已确认 8=审核中 -->
        update t_pay_detail
        set vbillstatus = #{vbillstatus},
        cor_date = #{corDate},
        cor_scr_id = #{corScrId},
        cor_user_id = #{corUserId}
        where pay_detail_id = #{payDetailId} and vbillstatus = 8
    </update>

    <resultMap id="OfflinePayMap" type="OfflinePay">
        <result property="vbillno" column="vbillno" />
        <result property="recCardNo" column="rec_card_no" />
        <result property="recAccount" column="rec_account" />
        <result property="ungotAmount" column="ungot_amount" />
    </resultMap>

    <select id="offlineList" resultMap="OfflinePayMap">
        select
               t.vbillno,t.rec_card_no,t.rec_account,t.ungot_amount
        from t_pay_detail t
        left join T_PAY_CHECK_SHEET_B sheet on t.PAY_DETAIL_ID = sheet.PAY_DETAIL_ID
        left join T_ENTRUST_LOT lot on t.LOT_ID = lot.ENTRUST_LOT_ID
        left join m_carrier carr on t.CARRIER_ID = carr.CARRIER_ID
        left join m_car car on car.car_id = lot.carno_id
        <include refid="condition1"></include>
        order by t.req_pay_date ,t.vbillno desc
    </select>

    <sql id="condition1">
        <where>
            <!--排除对账单的应付明细-->
            t.del_flag != 1 and sheet.PAY_DETAIL_ID is null
            <!--排除园区数据-->
<!--            and t.reg_scr_id != 'importAllData' and t.reg_scr_id != 'partDataPayDetailApplication'-->
            <if test="lotno != null and lotno.trim() != ''">
                <bind name="lotno" value="lotno + '%'"/>
                and t.lotno like #{lotno}
            </if>
            <if test="vbillno != null and vbillno.trim() != ''">
                <bind name="vbillno" value="vbillno + '%'"/>
                and t.vbillno like #{vbillno}
            </if>

            <if test="vbillstatus != null">
                and t.vbillstatus = #{vbillstatus}
            </if>
            <if test="status != null  and status != '' and status.indexOf(',') != -1">
                and t.vbillstatus in
                <foreach item="item" index="index" collection="status.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status.trim() != '' and status.indexOf(',') == -1">
                and t.vbillstatus = #{status}
            </if>
            <if test="carrName != null and carrName.trim() != ''">
                <bind name="carrName" value="carrName + '%'"/>
                and t.carr_name like #{carrName}
            </if>
            <if test="recAccount != null and recAccount.trim() != ''">
                <bind name="recAccount" value="recAccount + '%'"/>
                and t.rec_account like #{recAccount}
            </if>
            <if test="balatype != null and balatype.trim() != ''">
                and t.balatype = #{balatype}
            </if>
            <if test="driverName != null and driverName.trim() != ''">
                <bind name="driverName" value="driverName + '%'"/>
                and t.driver_name like #{driverName}
            </if>

            <if test="startDate != null ">
                and t.REG_DATE <![CDATA[   >=  ]]> #{startDate}
            </if>
            <if test="endtDate != null ">
                and t.REG_DATE <![CDATA[   <=  ]]> #{endtDate}
            </if>
            <!--申请时间-->
            <if test="params.applyDateStart != null and params.applyDateStart != ''">
                and t.apply_time <![CDATA[   >=  ]]> to_date(#{params.applyDateStart},'yyyy-MM-dd')
            </if>
            <if test="params.applyDateEnd != null and params.applyDateEnd != ''">
                and t.apply_time <![CDATA[   <=  ]]> to_date(#{params.applyDateEnd},'yyyy-MM-dd')
            </if>
            <if test="regUserName != null and regUserName != ''">
                <bind name="regUserName" value="regUserName + '%'"/>
                and sysUser.user_name like  #{regUserName}
            </if>
            <if test="applyUser != null and applyUser != ''">
                <bind name="applyUser" value="applyUser + '%'"/>
                and t.apply_user like  #{applyUser}
            </if>
            <if test="carrierId != null and carrierId.trim() != ''">
                and t.carrier_id = #{carrierId}
            </if>
            <if test="lotId != null and lotId != ''">
                and t.lot_id = #{lotId}
            </if>
            <if test="costTypeFreight != null  and costTypeFreight != ''">
                and t.cost_type_freight in
                <foreach item="item" index="index" collection="costTypeFreight.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>

            </if>
            <if test="costTypeOnWay != null and costTypeOnWay != ''">
                and t.cost_type_on_way = #{costTypeOnWay}
            </if>

            <!--调度组-->
            <if test="params.transLineId != null  and params.transLineId != '' and params.transLineId.indexOf(',') != -1">
                and lot.trans_line_id in
                <foreach item="item" index="index" collection="params.transLineId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.transLineId != null and params.transLineId.trim() != '' and params.transLineId.indexOf(',') == -1">
                and lot.TRANS_LINE_ID = #{params.transLineId}
            </if>
            <if test="bankBackFlag != null ">
                and t.bank_back_flag = #{bankBackFlag}
            </if>
            <if test="balaCorp != null and balaCorp!=''">
                and t.bala_corp = #{balaCorp}
            </if>
            <choose>
                <when test='payWay == "Y"'>
                    and lot.pay_way = 'Y'
                </when>
                <when test="payWay == 'yl'">
                    and (
                        <!-- 非g7数据 or 推送过g7审验不过的数据 or 到货失败的数据 -->
                        (lot.g7_syn is null or (lot.g7_syn = 1 and lot.g7_syn_time is not null) or lot.g7_end = 1)
                        and
                        (car.carrierid is null or car.carrierid not in ('21228416176d4dbca8d3f1707a218e15','0e3181c8acdc46c194b3308a79b1ca77'))
                    ) and lot.pay_way is null
                </when>
                <when test="payWay == 'g7'">
                    and lot.g7_syn = 2 and lot.g7_end = 2
                </when>
                <when test="payWay == 'qx'">
                    and car.carrierid in ('21228416176d4dbca8d3f1707a218e15','0e3181c8acdc46c194b3308a79b1ca77')
                </when>
            </choose>
        </where>
    </sql>

    <!--获取应付明细列表 排除加入对账单的应付明细-->
    <select id="selectPayDetailListNotStatements" resultMap="PayDetailResult">
            SELECT
            t.pay_detail_id,
            t.free_type,
            t.vbillno,
            t.vbillstatus,
            t.lotno,
            t.carrier_id,
            t.balatype,
            t.trans_fee_count,
            t.got_amount,
            t.ungot_amount,
            t.memo,
            t.check_no,
            t.check_head,
            t.unconfirm_type,
            t.unconfirm_memo,
            t.confirm_time,
            t.confirm_user,
            t.driver_mobile,
            t.driver_name,
            t.carno,
            t.req_deli_date,
            t.req_arri_date,
            t.carr_code,
            t.carr_name,
            t.reg_date,
            t.cor_user_id,
            t.cor_date,
            t.del_flag,
            t.del_date,
            t.del_user_id,
            t.oil_card_number,
        <if test='params.showOilCardName == "1"'>fcard.FUELCARD_NAME,</if>
            t.bala_corp,
            t.reg_scr_id,
            t.cor_scr_id,
            t.is_close,
            t.adjust_memo,
            t.is_adjust,
            t.cost_type_on_way,
            t.cost_type_freight,
            t.rec_card_no,
            t.rec_account,
            t.rec_bank,
            t.is_ntocc,
            t.LOT_ID,
            t.apply_time,
            t.apply_user,
            t.apply_memo,
            t.req_pay_date,
            sysUser.user_name as reg_user_id,
            lot.DELI_PROVINCE_NAME || lot.DELI_CITY_NAME || lot.DELI_AREA_NAME as deli_addr,
            lot.ARRI_PROVINCE_NAME || lot.ARRI_CITY_NAME || lot.ARRI_AREA_NAME as arri_addr,
            lot.TRANS_LINE_NAME,
            carr.LEGAL_CARD,
            t.BANK_BACK_FLAG,
            t.g7_syn g7_syn,
            lot.g7_syn lot_g7_syn,
            lot.g7_start lot_g7_start,
            lot.g7_end  lot_g7_end,
            t.g7_pay,
            lot.g7_msg lot_g7_msg,
            t.G7_PAY_ERR,
            t.account_type,
            case when car.carrierid in ('21228416176d4dbca8d3f1707a218e15','0e3181c8acdc46c194b3308a79b1ca77') then 1 else 0 end is_mine,
            lot.pay_way,
            lot.LOCK_PAY lotLockPay,
            carr.LOCK_PAY carrLockPay,
            car.g7_ext g7CarExt,
            lot.g7_qst g7LotQst,
            t.batch_no
            FROM
            t_pay_detail t
            left join sys_user sysUser on t.reg_user_id = sysUser.USER_ID and sysUser.del_flag = 0
            left join T_PAY_CHECK_SHEET_B sheet on t.PAY_DETAIL_ID = sheet.PAY_DETAIL_ID
            left join T_ENTRUST_LOT lot on t.LOT_ID = lot.ENTRUST_LOT_ID
            left join m_carrier carr on t.CARRIER_ID = carr.CARRIER_ID
            left join m_car car on car.car_id = lot.carno_id
            <if test='params.showOilCardName == "1"'>
            left join m_fuelcard fcard on fcard.fuelcard_no = t.oil_card_number and fcard.del_flag = 0
            </if>
        <include refid="condition1"></include>
        order by t.req_pay_date desc ,t.vbillno desc
    </select>

    <resultMap id="sumPayDetailNotStatements" type="java.util.HashMap">
        <result column="trans_fee_count" property="transFeeCount" javaType="BigDecimal"/>
        <result column="got_amount" property="gotAmount" javaType="BigDecimal"/>
        <result column="ungot_amount" property="ungotAmount" javaType="BigDecimal"/>
    </resultMap>

    <select id="sumPayDetailListNotStatements" resultMap="sumPayDetailNotStatements">
        select nvl(sum(x.trans_fee_count),0) trans_fee_count,
        nvl(sum(x.got_amount),0) got_amount,
        nvl(sum(x.ungot_amount),0) ungot_amount from (
            SELECT
                t.trans_fee_count, t.got_amount, t.ungot_amount
            FROM t_pay_detail t
            left join sys_user sysUser on t.reg_user_id = sysUser.USER_ID and sysUser.del_flag = 0
            <!--left join T_PAY_CHECK_SHEET_B sheet on t.PAY_DETAIL_ID = sheet.PAY_DETAIL_ID-->
            left join T_ENTRUST_LOT lot on t.LOT_ID = lot.ENTRUST_LOT_ID
            <if test="payWay == 'yl' or payWay == 'qx'">left join m_car car on car.car_id = lot.carno_id</if>
            <where>
                <!--排除对账单的应付明细-->
                t.del_flag = 0 <!--and sheet.PAY_DETAIL_ID is null-->
                <if test="lotno != null and lotno.trim() != ''">
                    <bind name="lotno" value="lotno + '%'"/>
                    and t.lotno like #{lotno}
                </if>
                <if test="vbillno != null and vbillno.trim() != ''">
                    <bind name="vbillno" value="vbillno + '%'"/>
                    and t.vbillno like #{vbillno}
                </if>
                <if test="vbillstatus != null">
                    and t.vbillstatus = #{vbillstatus}
                </if>
                <if test="status != null  and status != '' and status.indexOf(',') != -1">
                    and t.vbillstatus in
                    <foreach item="item" index="index" collection="status.split(',')" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="status != null and status.trim() != '' and status.indexOf(',') == -1">
                    and t.vbillstatus = #{status}
                </if>
                <if test="carrName != null and carrName.trim() != ''">
                    <bind name="carrName" value="carrName + '%'"/>
                    and t.carr_name like #{carrName}
                </if>
                <if test="recAccount != null and recAccount.trim() != ''">
                    <bind name="recAccount" value="recAccount + '%'"/>
                    and t.rec_account like #{recAccount}
                </if>
                <if test="balatype != null and balatype.trim() != ''">
                    and t.balatype = #{balatype}
                </if>
                <if test="driverName != null and driverName.trim() != ''">
                    <bind name="driverName" value="driverName + '%'"/>
                    and t.driver_name like #{driverName}
                </if>

                <if test="startDate != null ">
                    and t.REG_DATE <![CDATA[   >=  ]]> #{startDate}
                </if>
                <if test="endtDate != null ">
                    and t.REG_DATE <![CDATA[   <=  ]]> #{endtDate}
                </if>
                <!--申请时间-->
                <if test="params.applyDateStart != null and params.applyDateStart != ''">
                    and t.apply_time <![CDATA[   >=  ]]> to_date(#{params.applyDateStart},'yyyy-MM-dd')
                </if>
                <if test="params.applyDateEnd != null and params.applyDateEnd != ''">
                    and t.apply_time <![CDATA[   <=  ]]> to_date(#{params.applyDateEnd},'yyyy-MM-dd')
                </if>
                <if test="regUserName != null and regUserName != ''">
                    <bind name="regUserName" value="regUserName + '%'"/>
                    and sysUser.user_name like #{ regUserName}
                </if>
                <if test="applyUser != null and applyUser != ''">
                    <bind name="applyUser" value="applyUser + '%'"/>
                    and t.apply_user like #{ applyUser}
                </if>
                <if test="carrierId != null and carrierId.trim() != ''">
                    and t.carrier_id = #{carrierId}
                </if>
                <if test="lotId != null and lotId != ''">
                    and t.lot_id = #{lotId}
                </if>
                <if test="costTypeFreight != null  and costTypeFreight != ''">
                    and t.cost_type_freight in
                    <foreach item="item" index="index" collection="costTypeFreight.split(',')" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    and t.free_type=0
                </if>
                <!--调度组-->
                <if test="params.transLineId != null  and params.transLineId != '' and params.transLineId.indexOf(',') != -1">
                    and lot.trans_line_id in
                    <foreach item="item" index="index" collection="params.transLineId.split(',')" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="params.transLineId != null and params.transLineId.trim() != '' and params.transLineId.indexOf(',') == -1">
                    and lot.TRANS_LINE_ID = #{params.transLineId}
                </if>
                <if test="bankBackFlag != null ">
                    and t.bank_back_flag = #{bankBackFlag}
                </if>
                <choose>
                    <when test='payWay == "Y"'>
                        and lot.pay_way = 'Y'
                    </when>
                    <when test="payWay == 'yl'">
                        and (
                        <!-- 非g7数据 or 推送过g7审验不过的数据 or 到货失败的数据 -->
                        (lot.g7_syn is null or (lot.g7_syn = 1 and lot.g7_syn_time is not null) or lot.g7_end = 1)
                        and
                        (car.carrierid is null or car.carrierid not in (
                        '21228416176d4dbca8d3f1707a218e15','0e3181c8acdc46c194b3308a79b1ca77'))
                        ) and lot.pay_way is null
                    </when>
                    <when test="payWay == 'g7'">
                        and lot.g7_syn = 2 and lot.g7_end = 2
                    </when>
                    <when test="payWay == 'qx'">
                        and car.carrierid in ('21228416176d4dbca8d3f1707a218e15','0e3181c8acdc46c194b3308a79b1ca77')
                    </when>
                </choose>
            </where>
            <if test="params.unionDeposit == true">
            union all
                select t.amount, t.got_amount, t.ungot_amount
                from t_entrust_lot_deposit t
                where t.del_flag = 0
                <if test="lotId != null and lotId != ''">
                    and t.lot_id = #{lotId}
                </if>
            </if>
        ) x
    </select>

    <select id="listPayDetailReview" resultMap="PayDetailResult">
        select t.* from (
        SELECT
        t.pay_detail_id,
        t.free_type,
        t.vbillno,
        t.vbillstatus,
        t.lotno,
        t.carrier_id,
        t.balatype,
        t.trans_fee_count,
        t.got_amount,
        t.ungot_amount,
        t.memo,
        t.check_no,
        t.check_head,
        t.unconfirm_type,
        t.unconfirm_memo,
        t.confirm_time,
        t.confirm_user,
        t.driver_mobile,
        t.driver_name,
        t.carno,
        t.req_deli_date,
        t.req_arri_date,
        t.carr_code,
        t.carr_name,
        t.reg_date,
        t.cor_user_id,
        t.cor_date,
        t.del_flag,
        t.del_date,
        t.del_user_id,
        t.oil_card_number,
        t.bala_corp,
        t.reg_scr_id,
        t.cor_scr_id,
        t.is_close,
        t.adjust_memo,
        t.is_adjust,
        t.cost_type_on_way,
        t.cost_type_freight,
        t.rec_card_no,
        t.rec_account,
        t.rec_bank,
        t.is_ntocc,
        t.LOT_ID,
        t.apply_time,
        t.apply_user,
        t.apply_memo,
        t.req_pay_date,
        sysUser.user_name as reg_user_id,
        lot.DELI_PROVINCE_NAME || lot.DELI_CITY_NAME || lot.DELI_AREA_NAME as deli_addr,
        lot.ARRI_PROVINCE_NAME || lot.ARRI_CITY_NAME || lot.ARRI_AREA_NAME as arri_addr,
        lot.TRANS_LINE_NAME,
        carr.LEGAL_CARD,
        <!--运营组拼接 去重-->
        (
        select
        listagg(t.dept_name, ',') within group(order by t.dept_name)
        from (
        select
        distinct c.dept_name
        from T_COST_ALLOCATION a
        left join t_invoice b on a.INVOICE_ID = b.invoice_id
        left join sys_dept c on b.SALES_DEPT = c.DEPT_ID
        where a.PAY_DETAIL_ID = t.pay_detail_id and a.del_flag = 0)t
        ) sales_dept_name
        FROM
        t_pay_detail t
        left join sys_user sysUser on t.reg_user_id = sysUser.USER_ID and sysUser.del_flag = 0
        left join T_PAY_CHECK_SHEET_B sheet on t.PAY_DETAIL_ID = sheet.PAY_DETAIL_ID
        left join T_ENTRUST_LOT lot on t.LOT_ID = lot.ENTRUST_LOT_ID
        left join m_carrier carr on t.CARRIER_ID = carr.CARRIER_ID
        <where>
            <!--排除对账单的应付明细-->
            t.del_flag != 1 and t.vbillstatus = 6 and sheet.PAY_DETAIL_ID is null
            <if test="vbillno != null and vbillno.trim() != ''">
                <bind name="vbillno" value="vbillno + '%'"/>
                and t.vbillno like #{vbillno}
            </if>
            <if test="isFleetData != null and isFleetData != ''">
                and t.is_fleet_data = #{isFleetData,jdbcType=VARCHAR}
            </if>
        </where>
        order by t.apply_time desc) t
        <where>
            <!--运营组查询-->
            <if test="salesDeptName != null  and salesDeptName != '' and salesDeptName.indexOf(',') == -1">
                <bind name="salesDeptName" value="'%'+ salesDeptName + '%'"/>
                t.sales_dept_name like #{salesDeptName}
            </if>
            <if test="salesDeptName != null  and salesDeptName != '' and salesDeptName.indexOf(',') != -1">
                <foreach item="item"  index="index"  open="(" separator="or"  close=")" collection="salesDeptName.split(',')">
                    <bind name="item" value="'%'+ item + '%'"/>
                    t.sales_dept_name like #{item}
                </foreach>
            </if>
        </where>

    </select>

    <update id="updatePickAndArrivalTimeByLotId">
        update t_pay_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="vbillno != null  and vbillno != ''  ">vbillno = #{vbillno},</if>
            <if test="vbillstatus != null  ">vbillstatus = #{vbillstatus},</if>
            <if test="freeType != null  and freeType != ''  ">free_type = #{freeType},</if>
            <if test="costTypeOnWay != null  and costTypeOnWay != ''  ">cost_type_on_way = #{costTypeOnWay},</if>
            <if test="costTypeFreight != null  and costTypeFreight != ''  ">cost_type_freight = #{costTypeFreight},</if>
            <if test="lotId != null  and lotId != ''  ">lot_id = #{lotId},</if>
            <if test="lotno != null  and lotno != ''  ">lotno = #{lotno},</if>
            <if test="carrierId != null  and carrierId != ''  ">carrier_id = #{carrierId},</if>
            <if test="carrCode != null  and carrCode != ''  ">carr_code = #{carrCode},</if>
            <if test="carrName != null  and carrName != ''  ">carr_name = #{carrName},</if>
            <if test="balaCorp != null  and balaCorp != ''  ">bala_corp = #{balaCorp},</if>
            <if test="balatype != null  and balatype != ''  ">balatype = #{balatype},</if>
            <if test="transFeeCount != null  ">trans_fee_count = #{transFeeCount},</if>
            <if test="gotAmount != null  ">got_amount = #{gotAmount},</if>
            <if test="ungotAmount != null  ">ungot_amount = #{ungotAmount},</if>
            <if test="oilCardNumber != null">oil_card_number = #{oilCardNumber},</if>
            <if test="checkNo != null  and checkNo != ''  ">check_no = #{checkNo},</if>
            <if test="checkHead != null  and checkHead != ''  ">check_head = #{checkHead},</if>
            <if test="unconfirmType != null  ">unconfirm_type = #{unconfirmType},</if>
            <if test="unconfirmMemo != null  and unconfirmMemo != ''  ">unconfirm_memo = #{unconfirmMemo},</if>
            <if test="confirmTime != null  ">confirm_time = #{confirmTime},</if>
            <if test="confirmUser != null  and confirmUser != ''  ">confirm_user = #{confirmUser},</if>
            <if test="driverMobile != null ">driver_mobile = #{driverMobile},</if>
            <if test="driverName != null  and driverName != ''  ">driver_name = #{driverName},</if>
            <if test="carno != null  and carno != ''  ">carno = #{carno},</if>
            <if test="reqDeliDate != null  ">req_deli_date = #{reqDeliDate},</if>
            <if test="reqArriDate != null  ">req_arri_date = #{reqArriDate},</if>
            <if test="isClose != null  ">is_close = #{isClose},</if>
            <if test="isAdjust != null  ">is_adjust = #{isAdjust},</if>
            <if test="adjustMemo != null  and adjustMemo != ''  ">adjust_memo = #{adjustMemo},</if>
            <if test="memo != null">memo = #{memo},</if>
            <!--<if test="regUserId != null  and regUserId != ''  ">reg_user_id = #{regUserId},</if>
            <if test="regDate != null  ">reg_date = #{regDate},</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id = #{regScrId},</if>-->
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id = #{corUserId},</if>
            <if test="corDate != null  ">cor_date = #{corDate},</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id = #{corScrId},</if>
            <if test="delFlag != null  ">del_flag = #{delFlag},</if>
            <if test="delDate != null  ">del_date = #{delDate},</if>
            <if test="delUserId != null  and delUserId != ''  ">del_user_id = #{delUserId},</if>
            <if test="recCardNo != null  ">rec_card_no = #{recCardNo},</if>
            <if test="recAccount != null  ">rec_account = #{recAccount},</if>
            <if test="recBank != null ">rec_bank = #{recBank},</if>

            <if test="isFleetData != null  and isFleetData != ''"> is_fleet_data = #{isFleetData},</if>
            <if test="isFleetAssign != null and isFleetAssign != ''"> is_fleet_assign = #{isFleetAssign},</if>
            <if test="fleetReceiveDetailId != null and fleetReceiveDetailId != ''"> fleet_receive_detail_id = #{fleetReceiveDetailId},</if>

        </trim>
        where LOT_ID = #{lotId}
    </update>

    <select id="selectMainToPayDetailList" resultMap="PayDetailResult">
        SELECT
        t.pay_detail_id,
        t.free_type,
        t.vbillno,
        t.vbillstatus,
        t.lotno,
        t.carrier_id,
        t.balatype,
        t.trans_fee_count,
        t.got_amount,
        t.ungot_amount,
        t.memo,
        t.check_no,
        t.check_head,
        t.unconfirm_type,
        t.unconfirm_memo,
        t.confirm_time,
        t.confirm_user,
        t.driver_mobile,
        t.driver_name,
        t.carno,
        t.req_deli_date,
        t.req_arri_date,
        t.carr_code,
        t.carr_name,
        t.reg_date,
        t.cor_user_id,
        t.cor_date,
        t.del_flag,
        t.del_date,
        t.del_user_id,
        t.oil_card_number,
        t.bala_corp,
        t.reg_scr_id,
        t.cor_scr_id,
        t.is_close,
        t.adjust_memo,
        t.is_adjust,
        t.cost_type_on_way,
        t.cost_type_freight,
        t.rec_card_no,
        t.rec_account,
        t.rec_bank,
        t.is_ntocc,
        t.LOT_ID,
        t.carr_bank_id,
        t.is_fleet_assign,
        carr.bala_type as bala_type,
        sysUser.user_name as reg_user_id,
        lot.DELI_PROVINCE_NAME || lot.DELI_CITY_NAME || lot.DELI_AREA_NAME as deli_addr,
        lot.ARRI_PROVINCE_NAME || lot.ARRI_CITY_NAME || lot.ARRI_AREA_NAME as arri_addr,
        lot.g7_syn lot_g7_syn,
        lot.g7_start lot_g7_start,
        lot.g7_end  lot_g7_end,
        t.g7_pay,
        lot.g7_msg lot_g7_msg
        FROM
        t_pay_detail t
        left join sys_user sysUser on t.reg_user_id = sysUser.USER_ID and sysUser.del_flag = 0
        left join T_ENTRUST_LOT lot on t.LOT_ID = lot.ENTRUST_LOT_ID
        left join m_carrier carr on carr.carrier_id = t.carrier_id and carr.del_flag = 0
        <where>
            t.del_flag = 0
            AND lot.DEL_FLAG = 0
            <if test="lotno != null and lotno.trim() != ''">
                <bind name="lotno" value="lotno + '%'"/>
                and t.lotno like #{lotno}
            </if>
            <if test="vbillno != null and vbillno.trim() != ''">
                <bind name="vbillno" value="vbillno + '%'"/>
                and t.vbillno like #{vbillno}
            </if>

            <if test="vbillstatus != null">
                and t.vbillstatus = #{vbillstatus}
            </if>
            <if test="status != null  and status != '' and status.indexOf(',') != -1">
                and t.vbillstatus in
                <foreach item="item" index="index" collection="status.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status.trim() != '' and status.indexOf(',') == -1">
                and t.vbillstatus = #{status}
            </if>
            <if test="carno != null  and carno != ''  ">
                AND t.carno like concat(#{carno},'%')
            </if>
            <if test="carrName != null and carrName.trim() != ''">
                <bind name="carrName" value="carrName + '%'"/>
                and t.carr_name like #{carrName}
            </if>
            <if test="balatype != null and balatype.trim() != ''">
                and t.balatype = #{balatype}
            </if>
            <if test="driverName != null and driverName.trim() != ''">
                <bind name="driverName" value="driverName + '%'"/>
                and t.driver_name like #{driverName}
            </if>

            <if test="startDate != null ">
                and t.REG_DATE <![CDATA[   >=  ]]> #{startDate}
            </if>
            <if test="endtDate != null ">
                and t.REG_DATE <![CDATA[   <=  ]]> #{endtDate}
            </if>
            <if test="regUserName != null and regUserName != ''">
                <bind name="regUserName" value="regUserName + '%'"/>
                and sysUser.user_name like  #{regUserName}
            </if>
            <if test="carrierId != null and carrierId.trim() != ''">
                and t.carrier_id = #{carrierId}
            </if>
            <if test="lotId != null and lotId != ''">
                and t.lot_id = #{lotId}
            </if>
            <if test="costTypeFreight != null  and costTypeFreight != ''">
                and t.cost_type_freight in
                <foreach item="item" index="index" collection="costTypeFreight.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ${params.dataScope}
        order by t.REG_DATE  desc
    </select>
    <!--撤销申请 回到已确认状态-->
    <update id="cancelApply">
        update t_pay_detail
          set vbillstatus = 1,
          BANK_BACK_FLAG = 0 ,
          cor_scr_id = 'payDetail',
          cor_date = #{corDate},
          cor_user_id = #{corUserId},
        batch_no = null
        where
        pay_detail_id in
        <foreach item="payDetailIds" collection="payDetailIds" open="(" separator="," close=")">
            #{payDetailIds}
        </foreach>
        and del_flag = 0
        and vbillstatus = 6

    </update>
    <update id="payDetailReview">
        update t_pay_detail
          set check_status = #{checkStatus},
              vbillstatus = #{vbillstatus},
              check_user_id = #{checkUserId},
              check_user_name = #{checkUserName},
              check_date = #{checkDate},
              cor_scr_id = #{corScrId},
              check_memo = #{checkMemo,jdbcType=VARCHAR}
            <if test="checkStatus == 2">,batch_no = null</if> <!--不通过时，清空批次号-->
        where pay_detail_id = #{payDetailId}
        and vbillstatus = 6 <!--6：已申请-->
    </update>

    <!--根据运单id   查询应付单-->
    <select id="selectPayDetailListByLotId" resultType="PayDetail">
        select
            t.pay_detail_id     payDetailId,
            t.vbillno,
            t.vbillstatus,
            t.carno             carno,
            t.carr_name         carrName,
            t.driver_name       driverName,
            t.driver_mobile     driverMobile,
            t.trans_fee_count   transFeeCount,
            t.got_amount        gotAmount,
            t.ungot_amount      ungotAmount,
            t.FREE_TYPE         freeType,
            t.COST_TYPE_ON_WAY  costTypeOnWay,
            t.req_deli_date     reqDeliDate,
            t.req_arri_date     reqArriDate,
            t.oil_card_number   oilCardNumber,
            t.lotno,
            t.is_adjust         isAdjust,
            t.adjust_memo       adjustMemo,
            t.rec_account       recAccount,
            t.rec_card_no       recCardNo,
            t.rec_bank          recBank,
            t.apply_time        applyTime,
            t.apply_user        applyUser,
            t.memo,
            t.is_ntocc          isNtocc,
            t.lot_id            lotId,
            t.CARRIER_ID        carrierId,
            t.balatype          balatype,
            t.check_no          checkNo,
            t.check_head        checkHead,
            t.unconfirm_type    unconfirmType,
            t.unconfirm_memo    unconfirmMemo,
            t.confirm_time      confirmTime,
            t.confirm_user      confirmUser,
            t.carr_code         carrCode,
            t.bala_corp         balaCorp,
            t.cost_type_freight costTypeFreight,
            t.is_close          isClose,
            t.carr_bank_id      carrBankId,
            t.bakc_write_type         bakcWriteType,
            t.back_write_memo         backWriteMemo,
            t.back_write_time         backWriteTime,
            t.req_pay_date            reqPayDate,
            t.Tid                     tid,
            t.check_status            checkStatus,
            t.check_user_name         checkUserName,
            t.check_user_id           checkUserId,
            t.check_memo              checkMemo,
            t.check_date              checkDate,
            t.apply_memo              applyMemo,
            t.is_oil_deposit          isOilDeposit,
            t.split_pay_detail_id     splitPayDetailId,
            t.bank_back_flag          bankBackFlag,
            t.is_fleet_data           isFleetData,
            t.is_fleet_assign         isFleetAssign,
            t.fleet_receive_detail_id fleetReceiveDetailId,
            t.write_off_time          writeOffTime,
            t.reg_date                regDate,
            t.tax_amount taxAmount,
            t.income_remark     incomeRemark,
            t1.user_name regUserId,
            lot.g7_syn lotG7Syn,
            lot.g7_start lotG7Start,
            lot.g7_end  lotG7End,
            t.g7_pay,
            lot.g7_msg lotG7Msg,
               t.lot_Sp_Lock lotSpLock,
            sheet.vbillno sheetVbillno
        from t_pay_detail t
          left join sys_user t1
         on t.reg_user_id = t1.user_id
            left join t_pay_check_sheet_b sheetb on sheetb.pay_detail_id = t.pay_detail_id
            left join t_pay_check_sheet sheet on sheet.pay_check_sheet_id = sheetb.pay_check_sheet_id
  left join T_ENTRUST_LOT lot on t.LOT_ID = lot.ENTRUST_LOT_ID
        where t.lot_id = #{lotId} and t.del_flag = 0
        ORDER BY T.REG_DATE
    </select>

    <!--根据运单id   查询应付单-->
    <select id="selectPayDetailListByBatchNo" resultType="PayDetail">
        select
            t.pay_detail_id     payDetailId,
            t.vbillno,
            t.vbillstatus,
            t.carno             carno,
            t.carr_name         carrName,
            t.driver_name       driverName,
            t.driver_mobile     driverMobile,
            t.trans_fee_count   transFeeCount,
            t.got_amount        gotAmount,
            t.ungot_amount      ungotAmount,
            t.FREE_TYPE         freeType,
            t.COST_TYPE_ON_WAY  costTypeOnWay,
            t.req_deli_date     reqDeliDate,
            t.req_arri_date     reqArriDate,
            t.oil_card_number   oilCardNumber,
            t.lotno,
            t.is_adjust         isAdjust,
            t.adjust_memo       adjustMemo,
            t.rec_account       recAccount,
            t.rec_card_no       recCardNo,
            t.rec_bank          recBank,
            t.apply_time        applyTime,
            t.apply_user        applyUser,
            t.memo,
            t.is_ntocc          isNtocc,
            t.lot_id            lotId,
            t.CARRIER_ID        carrierId,
            t.balatype          balatype,
            t.check_no          checkNo,
            t.check_head        checkHead,
            t.unconfirm_type    unconfirmType,
            t.unconfirm_memo    unconfirmMemo,
            t.confirm_time      confirmTime,
            t.confirm_user      confirmUser,
            t.carr_code         carrCode,
            t.bala_corp         balaCorp,
            t.cost_type_freight costTypeFreight,
            t.is_close          isClose,
            t.carr_bank_id      carrBankId,
            t.bakc_write_type         bakcWriteType,
            t.back_write_memo         backWriteMemo,
            t.back_write_time         backWriteTime,
            t.req_pay_date            reqPayDate,
            t.Tid                     tid,
            t.check_status            checkStatus,
            t.check_user_name         checkUserName,
            t.check_user_id           checkUserId,
            t.check_memo              checkMemo,
            t.check_date              checkDate,
            t.apply_memo              applyMemo,
            t.is_oil_deposit          isOilDeposit,
            t.split_pay_detail_id     splitPayDetailId,
            t.bank_back_flag          bankBackFlag,
            t.is_fleet_data           isFleetData,
            t.is_fleet_assign         isFleetAssign,
            t.fleet_receive_detail_id fleetReceiveDetailId,
            t.write_off_time          writeOffTime,
            t.reg_date                regDate,
            t.tax_amount taxAmount,
            t.income_remark     incomeRemark,
            t1.user_name regUserId,
            lot.g7_syn lotG7Syn,
            lot.g7_start lotG7Start,
            lot.g7_end  lotG7End,
            t.g7_pay,
            lot.g7_msg lotG7Msg,
            t.lot_Sp_Lock lotSpLock
        from t_pay_detail t
                 left join sys_user t1
                           on t.reg_user_id = t1.user_id
                 left join T_ENTRUST_LOT lot on t.LOT_ID = lot.ENTRUST_LOT_ID
        where t.batch_no = #{batchNo} and t.del_flag = 0
        ORDER BY T.REG_DATE
    </select>

    <select id="selectPayDetailListByCarrierId" resultType="PayDetail">
        select
        t.pay_detail_id payDetailId,
        t.CARRIER_ID carrierId,
        t.vbillno,
        t.vbillstatus,
        t.FREE_TYPE freeType,
        t.COST_TYPE_ON_WAY costTypeOnWay,
        t.cost_type_freight costTypeFreight,
        t.oil_card_number oilCardNumber,
        t.memo,
        t.got_amount gotAmount,
        t.ungot_amount ungotAmount,
        t.trans_fee_count transFeeCount,
        t.is_close isClose,
        t.is_adjust isAdjust,
        t.reg_date regDate,
        t1.user_name regUserId
        from t_pay_detail t
        left join sys_user t1
        on t.reg_user_id = t1.user_id
        where t.CARRIER_ID = #{carrierId} and t.del_flag = 0
    </select>

    <!--根据运单id   查询应付单-->
    <select id="selectPayDetailListByCarrierIdAndFreeType" resultType="PayDetail">
        select
        t.pay_detail_id payDetailId,
        t.vbillno,
        t.vbillstatus,
        t.FREE_TYPE freeType,
        t.COST_TYPE_ON_WAY costTypeOnWay,
        t.cost_type_freight costTypeFreight,
        t.oil_card_number oilCardNumber,
        t.memo,
        t.got_amount gotAmount,
        t.ungot_amount ungotAmount,
        t.trans_fee_count transFeeCount,
        t.is_close isClose,
        t.is_adjust isAdjust,
        t.reg_date regDate,
        t.IS_OIL_DEPOSIT isOilDeposit,
        t1.user_name regUserId
        from t_pay_detail t
        left join sys_user t1
        on t.reg_user_id = t1.user_id
        where t.CARRIER_ID = #{carrierId} and t.del_flag = 0
        and t.FREE_TYPE = #{freeType}
    </select>
    <!--根据运单id   查询应付单-->
    <select id="selectPayDetailListByLotIdAndFreeType" resultType="PayDetail">
        select
        t.pay_detail_id payDetailId,
        t.vbillno,
        t.vbillstatus,
        t.FREE_TYPE freeType,
        t.COST_TYPE_ON_WAY costTypeOnWay,
        t.cost_type_freight costTypeFreight,
        t.oil_card_number oilCardNumber,
        t.memo,
        t.got_amount gotAmount,
        t.ungot_amount ungotAmount,
        t.trans_fee_count transFeeCount,
        t.is_close isClose,
        t.is_adjust isAdjust,
        t.reg_date regDate,
        t.IS_OIL_DEPOSIT isOilDeposit,
        t1.user_name regUserId
        from t_pay_detail t
        left join sys_user t1
        on t.reg_user_id = t1.user_id
        where t.LOT_ID = #{lotId} and t.del_flag = 0
        and t.FREE_TYPE = #{freeType}
    </select>



    <select id="getPayDetailList" parameterType="PayDetail" resultMap="PayDetailResult">
        select
            pay_detail_id,
            vbillno,
            vbillstatus,
            free_type,
            cost_type_on_way,
            cost_type_freight,
            lot_id,
            lotno,
            carrier_id,
            carr_code,
            carr_name,
            bala_corp,
            balatype,
            trans_fee_count,
            got_amount,
            ungot_amount,
            oil_card_number,
            check_no,
            check_head,
            unconfirm_type,
            unconfirm_memo,
            confirm_time,
            confirm_user,
            driver_mobile,
            driver_name,
            carno,
            req_deli_date,
            req_arri_date,
            is_close,
            is_adjust,
            adjust_memo,

            reg_date,
            del_user_id,
            rec_card_no,
            rec_account,
            rec_bank,
            is_ntocc,
            apply_user,
            apply_time,
            sysUser.user_name reg_user_id
        from t_pay_detail
        left join sys_user sysUser on t_pay_detail.reg_user_id = sysUser.USER_ID and sysUser.del_flag = 0
        <where>
            T_PAY_DETAIL.DEL_FLAG = 0
            <if test="payDetailId != null  and payDetailId != '' "> and pay_detail_id = #{payDetailId}</if>
            <if test="vbillno != null  and vbillno != '' "> and vbillno = #{vbillno}</if>
            <if test="vbillstatus != null "> and vbillstatus = #{vbillstatus}</if>
            <if test="freeType != null  and freeType != '' "> and free_type = #{freeType}</if>
            <if test="costTypeOnWay != null  and costTypeOnWay != '' "> and cost_type_on_way = #{costTypeOnWay}</if>
            <if test="costTypeFreight != null  and costTypeFreight != '' "> and cost_type_freight = #{costTypeFreight}</if>
            <if test="lotId != null  and lotId != '' "> and lot_id = #{lotId}</if>
            <if test="lotno != null  and lotno != '' "> and lotno = #{lotno}</if>
            <if test="carrierId != null  and carrierId != '' "> and carrier_id = #{carrierId}</if>
            <if test="carrCode != null  and carrCode != '' "> and carr_code = #{carrCode}</if>
            <if test="carrName != null  and carrName != '' "> and carr_name = #{carrName}</if>
            <if test="balaCorp != null  and balaCorp != '' "> and bala_corp = #{balaCorp}</if>
            <if test="balatype != null  and balatype != '' "> and balatype = #{balatype}</if>
            <if test="transFeeCount != null "> and trans_fee_count = #{transFeeCount}</if>
            <if test="gotAmount != null "> and got_amount = #{gotAmount}</if>
            <if test="ungotAmount != null "> and ungot_amount = #{ungotAmount}</if>
            <if test="oilCardNumber != null  and oilCardNumber != '' "> and oil_card_number = #{oilCardNumber}</if>
            <if test="checkNo != null  and checkNo != '' "> and check_no = #{checkNo}</if>
            <if test="checkHead != null  and checkHead != '' "> and check_head = #{checkHead}</if>
            <if test="unconfirmType != null "> and unconfirm_type = #{unconfirmType}</if>
            <if test="unconfirmMemo != null  and unconfirmMemo != '' "> and unconfirm_memo = #{unconfirmMemo}</if>
            <if test="confirmTime != null "> and confirm_time = #{confirmTime}</if>
            <if test="confirmUser != null  and confirmUser != '' "> and confirm_user = #{confirmUser}</if>
            <if test="driverMobile != null  and driverMobile != '' "> and driver_mobile = #{driverMobile}</if>
            <if test="driverName != null  and driverName != '' "> and driver_name = #{driverName}</if>
            <if test="carno != null  and carno != '' "> and carno = #{carno}</if>
            <if test="reqDeliDate != null "> and req_deli_date = #{reqDeliDate}</if>
            <if test="reqArriDate != null "> and req_arri_date = #{reqArriDate}</if>
            <if test="isClose != null "> and is_close = #{isClose}</if>
            <if test="isAdjust != null "> and is_adjust = #{isAdjust}</if>
            <if test="adjustMemo != null  and adjustMemo != '' "> and adjust_memo = #{adjustMemo}</if>
            <if test="memo != null  and memo != '' "> and memo = #{memo}</if>
            <if test="regUserName != null and regUserName != ''">
                <bind name="regUserName" value="regUserName + '%'"/>
                and sysUser.user_name like  #{regUserName}
            </if>







            <if test="recCardNo != null  and recCardNo != '' "> and rec_card_no = #{recCardNo}</if>
            <if test="recAccount != null  and recAccount != '' "> and rec_account = #{recAccount}</if>
            <if test="recBank != null  and recBank != '' "> and rec_bank = #{recBank}</if>
            <if test="isNtocc != null "> and is_ntocc = #{isNtocc}</if>
            <if test="carrBankId != null  and carrBankId != '' "> and carr_bank_id = #{carrBankId}</if>
            <if test="applyTime != null "> and apply_time = #{applyTime}</if>
            <if test="applyUser != null  and applyUser != '' "> and apply_user = #{applyUser}</if>
            <if test="bakcWriteType != null "> and bakc_write_type = #{bakcWriteType}</if>
            <if test="backWriteMemo != null  and backWriteMemo != '' "> and back_write_memo = #{backWriteMemo}</if>
            <if test="backWriteTime != null "> and back_write_time = #{backWriteTime}</if>
        </where>
    </select>

    <select id="selectPayDetailByPayDetailIds" resultType="PayDetail">
        select sum(trans_fee_count) transFeeCount,
                sum(got_amount) gotAmount,
                sum(ungot_amount) ungotAmount
         from t_pay_detail
        where  pay_detail_id in
        <foreach item="payDetailIds" collection="payDetailIds" open="(" separator="," close=")">
            #{payDetailIds}
        </foreach>
        and del_flag = 0
    </select>

    <select id="listAggDriverNameOfLot" resultType="Map">
        SELECT listagg(t.DRIVER_NAME,',') WITHIN GROUP(ORDER BY t.LOT_CAR_DRIVER_ID) driver_name,
        listagg(t2.phone,',') WITHIN GROUP(ORDER BY t.LOT_CAR_DRIVER_ID) driver_mobile,
        t.lot_id
        FROM T_ENTRUST_LOT_CAR_DRIVER t
        left join m_driver t2 on t2.driver_id = t.driver_id
        where t.lot_id in (<foreach collection="lotIds" item="lotId" separator=",">#{lotId}</foreach>) and t.del_flag=0
        group by t.lot_id
    </select>

    <select id="sumPayDetailAmountOfLot" resultType="Map">
        select
        pay.lot_id,
        sum(nvl(pay.Trans_Fee_Count, 0)) receiptAmount,
        nvl(sum(case when pay.free_type = 0 then pay.Trans_Fee_Count+nvl(pay.TAX_AMOUNT,0) end), 0) receiptAmountFreight,
        nvl(sum(case when pay.free_type = 1 then pay.Trans_Fee_Count+nvl(pay.TAX_AMOUNT,0) end), 0) receiptAmountOnWay,
        nvl(sum(case when pay.free_type = 0 and pay.cost_type_freight in (1,3,5) then pay.Trans_Fee_Count+nvl(pay.TAX_AMOUNT,0) end), 0) oilCardAmount,
        sum(nvl(pay.trans_fee_count,0)) sumTransFeeCount,
        sum(nvl(pay.tax_amount,0)) sumTaxAmount,
        sum(nvl(pay.got_amount,0)) sumGotAmount,
        sum(nvl(pay.trans_fee_count,0)) + sum(nvl(pay.tax_amount,0)) - sum(nvl(pay.got_amount,0)) sumUngotAmount
        from t_pay_detail pay
        where pay.lot_id in (<foreach collection="lotIds" item="lotId" separator=",">#{lotId}</foreach>) and pay.del_flag = 0
        group by pay.lot_id
    </select>

    <!--获取在途跟踪 应付对账-->
    <select id="selectPayReconciliationList" resultType="PayReconciliationVO">
        select t.* from (
    select <!--distinct-->
            lot.lot,
            lot.entrust_lot_id entrustLotId,
            lot.vbillstatus,
            lot.car_no carNo,
            lot.car_type_name carTypeName,
            lot.car_len_name carLenName,
            lot.carrier_name carrierName,
            lot.deli_province_name || lot.deli_city_name || lot.deli_area_name deliDetailAddress,
            lot.arri_province_name || lot.arri_city_name || lot.arri_area_name arriDetailAddress,
            CASE WHEN lot.NUM_COUNT_ADJUST IS NOT NULL AND lot.NUM_COUNT_ADJUST != 0
                THEN lot.NUM_COUNT_ADJUST ELSE lot.num_count END  AS numCount,
            CASE WHEN lot.WEIGHT_COUNT_ADJUST IS NOT NULL AND lot.WEIGHT_COUNT_ADJUST != 0
                THEN lot.WEIGHT_COUNT_ADJUST ELSE lot.weight_count END AS weightCount,
            CASE WHEN lot.VOLUME_COUNT_ADJUST IS NOT NULL AND lot.VOLUME_COUNT_ADJUST != 0
                THEN lot.VOLUME_COUNT_ADJUST ELSE lot.volume_count END AS volumeCount,
            to_date(to_char(sysdate,'yyyyMMdd'),'yyyyMMdd') - to_date(to_char(lot.REQ_DELI_DATE,'yyyyMMdd'),'yyyyMMdd') as paymentDays,
            lot.REQ_DELI_DATE reqDeliDate,
            lot.req_arri_date reqArriDate,
            lot.payable_write_off_status payableWriteOffStatus,
            lot.if_All_Confirm ifAllConfirm,
            lot.if_All_Receipt ifAllReceipt,
            lot.receipt_Time receiptTime,
            lot.bank_card bankCard,
            lot.bank_account bankAccount,
            lot.reg_date lotRegDate,
            lot.oil_ratio oilRatio,
            lot.is_agency_collect isAgencyCollect,
            lot.collect_Tid collectTid,
            lot.actualmileage,
        lot.UNIT_PRICE unitPrice,
        lot.GUIDING_PRICE guidingPrice,
        lot.freight_fee_rate freightFeeRate,
        nvl((select dict_label from sys_dict_data aa where aa.dict_type = 'billing_type' and aa.dict_value = lot.billing_type),'不开票') billingTypeLabel,
        lot.oil_Ratio oilCardRate,
        lot.pricing_Method pricingMethod,
        <!--nvl(t1.receiptamountfreight,0) receiptAmountFreight,-->  <!--运费-->
        <!--nvl(t1.receiptamountonway,0) receiptAmountOnWay,-->  <!-- 在途-->
        <!--nvl(t1.oilCardAmount,0) oilCardAmount, -->  <!-- 油卡总金额-->
        <!--t1.sumTransFeeCount,-->
        <!--t1.sumTaxAmount,-->
        <!--t1.sumGotAmount,-->
        <!--t1.sumTransFeeCount + t1.sumTaxAmount - t1.sumGotAmount  sumUngotAmount,-->

            t2.USER_NAME regUserName,
            lot.trans_line_name<!--t3.dept_name--> transDeptName,
            t4.CARR_TYPE carrType,
            t4.if_has_bill ifHasBill,
            t4.phone carrierPhone,
            lot.driver_name as driverName,
        (SELECT listagg(goods_name, ',') WITHIN GROUP (ORDER BY goods_name)
        FROM (select distinct packGoods.goods_name
        from t_entrust entrust
        left join t_ent_pack_goods packGoods on packGoods.entrust_id = entrust.entrust_id
        where entrust.lot_id = lot.entrust_lot_id
        and entrust.DEL_FLAG = 0)
        )  goodsName,
            to_char(lot.REQ_DELI_DATE,'yyyy-MM-dd') reqDeliDateString,
            t4.CARR_CODE carrCode,
        (SELECT listagg(cust_abbr, ',') WITHIN GROUP (ORDER BY cust_abbr)
        FROM (select distinct entrust.cust_abbr
        from t_entrust entrust
        where entrust.lot_id = lot.entrust_lot_id
        and entrust.DEL_FLAG = 0)
        )  custAbbr,
            CASE WHEN t4.BALA_TYPE = 1 THEN '单笔' ELSE '月结' END AS balaType,
        trans_Type transType
        from t_entrust_lot lot
            <!--left join t_entrust entrust on entrust.lot_id = lot.entrust_lot_id-->
            <!--left join (
                  select
                      pay.lotno,b.carr_name,
                      nvl(sum(pay.Trans_Fee_Count), 0) receiptAmount,
                      nvl(sum(case when pay.free_type = 0 then pay.Trans_Fee_Count+nvl(pay.TAX_AMOUNT,0) end), 0) receiptAmountFreight,
                      nvl(sum(case when pay.free_type = 1 then pay.Trans_Fee_Count+nvl(pay.TAX_AMOUNT,0) end), 0) receiptAmountOnWay,
                      nvl(sum(case when pay.free_type = 0 and pay.cost_type_freight in (1,3,5) then pay.Trans_Fee_Count+nvl(pay.TAX_AMOUNT,0) end), 0) oilCardAmount,
                      sum(pay.trans_fee_count) sumTransFeeCount,
                      nvl(sum(pay.tax_amount),0) sumTaxAmount,
                      sum(pay.got_amount) sumGotAmount
                      /*sum(pay.UNGOT_AMOUNT) sumUngotAmount*/
                  from t_entrust_lot lot
                  left join t_pay_detail pay on lot.lot = pay.lotno and pay.del_flag = 0
                  left join m_carrier b on pay.carrier_id = b.carrier_id
                  group by pay.lotno,b.carr_name) t1 on lot.lot = t1.lotno-->
            left join sys_user t2 on lot.reg_user_id = t2.user_id
            <!--left join sys_dept t3 on lot.trans_line_id = t3.DEPT_ID-->
            left join m_carrier t4 on lot.CARRIER_ID = t4.CARRIER_ID
        where
            lot.del_flag = 0 and lot.reg_scr_id != 'importAllData'
            <if test="isOversize != null">
                and lot.is_oversize =  #{isOversize,jdbcType=VARCHAR}
            </if>
            <if test="transType != null and transType != ''">
                and lot.trans_type =  #{transType,jdbcType=VARCHAR}
            </if>
            <if test="isFleetData != null and isFleetData != ''">
                and lot.is_Fleet_Data =  #{isFleetData,jdbcType=VARCHAR}
            </if>
            <if test="carLen != null and carLen != ''">
                and lot.car_len =  #{carLen}
            </if>
            <if test="isAgencyCollect != null">
                and lot.is_agency_collect =  #{isAgencyCollect}
            </if>
            <if test="params.payableWriteOffStatusList != null  and params.payableWriteOffStatusList != ''">
                and lot.payable_write_off_status in
                <foreach item="item" index="index" collection="params.payableWriteOffStatusList.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        <!--调度组-->
            <if test="transLineId != null and transLineId != ''">
                and lot.trans_line_id = #{transLineId}
            </if>
            <if test="carNo != null and carNo != ''">
                <bind name="carNo" value="carNo + '%'"/>
                and lot.car_no like  #{carNo}
            </if>
            <!--运单号-->
            <if test="lot != null and lot != ''">
                <choose>
                    <when test="lot.indexOf(',') lt 0">
                        <bind name="lot" value="lot + '%'"/>
                        and lot.lot like #{lot}
                    </when>
                    <when test="lot.split(',').length > 0">
                        and lot.lot in (<foreach collection="lot.split(',')" item="itm" separator=",">#{itm}</foreach>)
                    </when>
                </choose>

            </if>
            <!--运单状态-->
            <if test="lotVbillstatus != null  and lotVbillstatus != '' and lotVbillstatus.indexOf(',') != -1">
                and lot.vbillstatus in
                <foreach item="item" index="index" collection="lotVbillstatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="lotVbillstatus != null and lotVbillstatus.trim() != '' and lotVbillstatus.indexOf(',') == -1">
                and lot.vbillstatus = #{lotVbillstatus}
            </if>
            <!--承运商名称-->
            <if test="carrierName != null and carrierName != ''">
                <bind name="carrierName" value="carrierName + '%'"/>
                <!--and t1.carr_name like  #{carrierName}-->
                and lot.carrier_name like  #{carrierName}
            </if>
            <!--承运商类别-->
            <if test="carrType != null and carrType != ''">
                and t4.carr_type =  #{carrType}
            </if>
            <if test="ifAllConfirm != null ">
                and lot.if_All_Confirm =  #{ifAllConfirm}
            </if>
            <if test="ifAllReceipt != null ">
                and lot.if_All_Receipt =  #{ifAllReceipt}
            </if>
            <if test="ifAllReceiptUpload != null ">
                and lot.if_All_Receipt_Upload =  #{ifAllReceiptUpload}
            </if>
            <if test="carrCode != null and carrCode != ''">
                and t4.carr_code =  #{carrCode}
            </if>
            <if test="carrBalaType != null and carrBalaType != ''">
                and t4.bala_type =  #{carrBalaType}
            </if>

            <!--要求提货日期-->
            <if test="params.reqDeliDateStart != null  and params.reqDeliDateStart != ''">
                <!--and to_char(lot.req_deli_date,'yyyy-mm-dd') <![CDATA[ >= ]]> #{params.reqDeliDateStart}-->
                and lot.req_deli_date <![CDATA[ >= ]]> to_date(#{params.reqDeliDateStart},'YYYY-MM-DD')
            </if>
            <if test="params.reqDeliDateEnd != null  and params.reqDeliDateEnd != ''">
                <!--and to_char(lot.req_deli_date,'yyyy-mm-dd') <![CDATA[ <= ]]> #{params.reqDeliDateEnd}-->
                and lot.req_deli_date <![CDATA[ < ]]> to_date(#{params.reqDeliDateEnd},'YYYY-MM-DD') + 1
            </if>
            <!--提货省市区-->
            <if test="deliProvince != null  and deliProvince != '' "> and lot.deli_province = #{deliProvince}</if>
            <if test="deliCity != null  and deliCity != '' "> and lot.deli_city = #{deliCity}</if>
            <if test="deliArea != null  and deliArea != '' "> and lot.deli_area = #{deliArea}</if>
            <!--到货省市区-->
            <if test="arriProvince != null  and arriProvince != '' "> and lot.arri_province = #{arriProvince}</if>
            <if test="arriCity != null  and arriCity != '' "> and lot.arri_city = #{arriCity}</if>
            <if test="arriArea != null  and arriArea != '' "> and lot.arri_area = #{arriArea}</if>
             ${params.dataScope}
        order by lot.REQ_DELI_DATE desc
        )t
        <where>
            <!--客户简称-->
            <if test="custAbbr != null and custAbbr != ''">
                <bind name="custAbbr" value="custAbbr + '%'"/>
                t.custAbbr like  #{custAbbr}
            </if>
            <if test="goodsName != null and goodsName != ''">
                <bind name="goodsName" value="'%'+ goodsName + '%'"/>
                t.goodsName like  #{goodsName}
            </if>

        </where>
    </select>



    <!--获取在途跟踪 应付对账-->
    <select id="selectPayReconciliationAdjustList" resultType="PayReconciliationVO">
        <!--select
        lot.lot,
        lot.entrust_lot_id entrustLotId,
        lot.vbillstatus,
        lot.car_no carNo,
        lot.car_type_name carTypeName,
        lot.car_len_name carLenName,
        lot.carrier_name carrierName,
        lot.deli_province_name || lot.deli_city_name || lot.deli_area_name deliDetailAddress,
        lot.arri_province_name || lot.arri_city_name || lot.arri_area_name arriDetailAddress,
        lot.num_count numCount,
        lot.weight_count weightCount,
        lot.volume_count volumeCount,
        lot.REQ_DELI_DATE reqDeliDate,
        lot.WEIGHT_COUNT_ADJUST weightCountAdjust,
        lot.VOLUME_COUNT_ADJUST volumeCountAdjust,
        lot.NUM_COUNT_ADJUST numCountAdjust,
        nvl(t1.receiptamountfreight,0) receiptAmountFreight,
        nvl(t1.receiptamountonway,0) receiptAmountOnWay,
        nvl(t1.oilCardAmount,0) oilCardAmount,
        t1.sumTransFeeCount,
        t1.sumGotAmount,
        t1.sumUngotAmount,
        nvl(t2.receiptamountfreight,0) receiptAmountFreightAfter,
        nvl(t2.receiptamountonway,0) receiptAmountOnWayAfter,
        nvl(t2.oilCardAmount,0) oilCardAmountAfter,
        t2.sumTransFeeCount sumTransFeeCountAfter,
        t2.sumGotAmount sumGotAmountAfter,
        t2.sumUngotAmount sumUngotAmountAfter,
        (SELECT listagg(t.DRIVER_NAME,',') WITHIN GROUP(ORDER BY t.LOT_CAR_DRIVER_ID) FROM T_ENTRUST_LOT_CAR_DRIVER t where t.lot_id=lot.entrust_lot_id) as driverName
        from t_entrust_lot lot
        left join (
        select
        pay.lotno,
        nvl(sum(pay.Trans_Fee_Count), 0) receiptAmount,
        nvl(sum(case when pay.free_type = 0 then pay.Trans_Fee_Count end), 0) receiptAmountFreight,
        nvl(sum(case when pay.free_type = 1 then pay.Trans_Fee_Count end), 0) receiptAmountOnWay,
        nvl(sum(case when pay.free_type = 0 and pay.cost_type_freight in (1,3,5) then pay.Trans_Fee_Count end), 0) oilCardAmount,
        sum(pay.trans_fee_count) sumTransFeeCount,
        sum(pay.got_amount) sumGotAmount,
        sum(pay.UNGOT_AMOUNT) sumUngotAmount
        from t_entrust_lot lot
        left join t_pay_detail pay
        on lot.lot = pay.lotno
        and pay.del_flag = 0
        and pay.IS_ADJUST = 0
        group by pay.lotno) t1
        on lot.lot = t1.lotno
        left join (
        select
        pay.lotno,
        nvl(sum(pay.Trans_Fee_Count), 0) receiptAmount,
        nvl(sum(case when pay.free_type = 0 then pay.Trans_Fee_Count end), 0) receiptAmountFreight,
        nvl(sum(case when pay.free_type = 1 then pay.Trans_Fee_Count end), 0) receiptAmountOnWay,
        nvl(sum(case when pay.free_type = 0 and pay.cost_type_freight in (1,3,5) then pay.Trans_Fee_Count end), 0) oilCardAmount,
        sum(pay.trans_fee_count) sumTransFeeCount,
        sum(pay.got_amount) sumGotAmount,
        sum(pay.UNGOT_AMOUNT) sumUngotAmount
        from t_entrust_lot lot
        left join t_pay_detail pay
        on lot.lot = pay.lotno
        and pay.del_flag = 0
        group by pay.lotno) t2
        on lot.lot = t2.lotno-->
        select
        lot.lot,
        lot.entrust_lot_id entrustLotId,
        lot.vbillstatus,
        lot.car_no carNo,
        lot.car_type_name carTypeName,
        lot.car_len_name carLenName,
        lot.carrier_name carrierName,
        lot.deli_province_name || lot.deli_city_name || lot.deli_area_name deliDetailAddress,
        lot.arri_province_name || lot.arri_city_name || lot.arri_area_name arriDetailAddress,
        nvl(history.NUM_BEFORE,0) numCount,
        nvl(history.WEIGHT_BEFORE,0) weightCount,
        nvl(history.VOLUME_BEFORE,0) volumeCount,
        lot.REQ_DELI_DATE reqDeliDate,
        nvl(history.WEIGHT_AFETER,0) weightCountAdjust,
        nvl(history.VOLUME_AFTER,0) volumeCountAdjust,
        nvl(history.NUM_AFTER,0) numCountAdjust,
        nvl(CASH_BEFORE+OIL_BEFORE,0) receiptAmountFreight,
        nvl(ZXF_BEFORE+FK_BEFORE+KHPC_BEFORE+HS_BEFORE+QT_BEOFRE,0) receiptAmountOnWay,
        nvl(OIL_BEFORE,0) oilCardAmount,
        nvl(ZXF_BEFORE+FK_BEFORE+KHPC_BEFORE+HS_BEFORE+QT_BEOFRE+CASH_BEFORE+OIL_BEFORE,0) sumTransFeeCount,
        nvl(CASH_AFTER+OIL_AFTER,0) receiptAmountFreightAfter,
        nvl(ZXF_AFTER+FK_AFTER+KHPC_AFTER+HS_AFTER+QT_AFTER,0) receiptAmountOnWayAfter,
        nvl(OIL_AFTER,0) oilCardAmountAfter,
        nvl(ZXF_AFTER+FK_AFTER+KHPC_AFTER+HS_AFTER+QT_AFTER+CASH_AFTER+OIL_AFTER,0) sumTransFeeCountAfter,
        history.reg_date regDate,
        SYS_USER.USER_NAME regUserName,
        (SELECT listagg(t.DRIVER_NAME,',') WITHIN GROUP(ORDER BY t.LOT_CAR_DRIVER_ID) FROM T_ENTRUST_LOT_CAR_DRIVER t where t.lot_id=lot.entrust_lot_id) as driverName
        from T_PAY_DETAIL_ADJUST_HISTORY history
        left join T_ADJUST_RECORD adjustRecord on history.ADJUST_RECORD_ID = adjustRecord.ADJUST_RECORD_ID
        left join T_ENTRUST_lot lot on history.LOT_ID = lot.ENTRUST_LOT_ID
        left join SYS_USER on SYS_USER.USER_ID = history.REG_USER_ID
        <where>
            adjustRecord.ADJUST_CHECK_STATUS = 3
            and lot.del_flag = 0
            <!--运单号-->
            <if test="lot != null and lot != ''">
                <bind name="lot" value="lot + '%'"/>
                and lot.lot like  #{lot}
            </if>
            <!--运单状态-->
            <if test="lotVbillstatus != null  and lotVbillstatus != '' and lotVbillstatus.indexOf(',') != -1">
                and lot.vbillstatus in
                <foreach item="item" index="index" collection="lotVbillstatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="lotVbillstatus != null and lotVbillstatus.trim() != '' and lotVbillstatus.indexOf(',') == -1">
                and lot.vbillstatus = #{lotVbillstatus}
            </if>
            <!--承运商名称-->
            <if test="carrierName != null and carrierName != ''">
                <bind name="carrierName" value="carrierName + '%'"/>
                and lot.carrier_name like  #{carrierName}
            </if>
            <if test="pickStartDate != null and pickStartDate.trim() != ''">
                <![CDATA[ and to_char(history.reg_date,'yyyy-mm-dd') >= #{pickStartDate} ]]>
            </if>
            <if test="pickEndDate != null and pickEndDate.trim() != ''">
                <![CDATA[and to_char(history.reg_date,'yyyy-mm-dd') <= #{pickEndDate} ]]>
            </if>
            <if test="regUserName != null and regUserName != ''">
                <bind name="regUserName" value="regUserName + '%'"/>
                and SYS_USER.USER_NAME like  #{regUserName}
            </if>
        </where>
        and exists (select 1 from t_entrust entrust where entrust.lot_id = lot.entrust_lot_id ${params.dataScope})
        order by lot.REQ_DELI_DATE desc

    </select>


    <!--获取在途跟踪 应付对账-->
    <select id="selectPayReconciliationAdjustListExport" resultType="PayReconciliationExportVO">
        select
        lot.lot,
        lot.entrust_lot_id entrustLotId,
        lot.vbillstatus,
        lot.car_no carNo,
        lot.car_type_name carTypeName,
        lot.car_len_name carLenName,
        lot.carrier_name carrierName,
        lot.deli_province_name || lot.deli_city_name || lot.deli_area_name deliDetailAddress,
        lot.arri_province_name || lot.arri_city_name || lot.arri_area_name arriDetailAddress,
        history.NUM_BEFORE numCount,
        history.WEIGHT_BEFORE weightCount,
        history.VOLUME_BEFORE volumeCount,
        lot.REQ_DELI_DATE reqDeliDate,
        history.WEIGHT_AFETER weightCountAdjust,
        history.VOLUME_AFTER volumeCountAdjust,
        history.NUM_AFTER numCountAdjust,
        nvl(CASH_BEFORE+OIL_BEFORE,0) receiptAmountFreight,
        nvl(ZXF_BEFORE+FK_BEFORE+KHPC_BEFORE+HS_BEFORE+QT_BEOFRE,0) receiptAmountOnWay,
        nvl(OIL_BEFORE,0) oilCardAmount,
        nvl(ZXF_BEFORE+FK_BEFORE+KHPC_BEFORE+HS_BEFORE+QT_BEOFRE+CASH_BEFORE+OIL_BEFORE,0) sumTransFeeCount,
        nvl(CASH_AFTER+OIL_AFTER,0) receiptAmountFreightAfter,
        nvl(ZXF_AFTER+FK_AFTER+KHPC_AFTER+HS_AFTER+QT_AFTER,0) receiptAmountOnWayAfter,
        nvl(OIL_AFTER,0) oilCardAmountAfter,
        nvl(ZXF_AFTER+FK_AFTER+KHPC_AFTER+HS_AFTER+QT_AFTER+CASH_AFTER+OIL_AFTER,0) sumTransFeeCountAfter,
        history.reg_date regDate,
        SYS_USER.USER_NAME regUserName,
        (SELECT listagg(t.DRIVER_NAME,',') WITHIN GROUP(ORDER BY t.LOT_CAR_DRIVER_ID) FROM T_ENTRUST_LOT_CAR_DRIVER t where t.lot_id=lot.entrust_lot_id) as driverName
        from T_PAY_DETAIL_ADJUST_HISTORY history
        left join T_ADJUST_RECORD adjustRecord on history.ADJUST_RECORD_ID = adjustRecord.ADJUST_RECORD_ID
        left join T_ENTRUST_lot lot on history.LOT_ID = lot.ENTRUST_LOT_ID
        left join SYS_USER on SYS_USER.USER_ID = history.REG_USER_ID
        <where>
            adjustRecord.ADJUST_CHECK_STATUS = 3
            and lot.del_flag = 0
            <!--运单号-->
            <if test="lot != null and lot != ''">
                <bind name="lot" value="lot + '%'"/>
                and lot.lot like  #{lot}
            </if>
            <!--运单状态-->
            <if test="lotVbillstatus != null  and lotVbillstatus != '' and lotVbillstatus.indexOf(',') != -1">
                and lot.vbillstatus in
                <foreach item="item" index="index" collection="lotVbillstatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="lotVbillstatus != null and lotVbillstatus.trim() != '' and lotVbillstatus.indexOf(',') == -1">
                and lot.vbillstatus = #{lotVbillstatus}
            </if>
            <!--承运商名称-->
            <if test="carrierName != null and carrierName != ''">
                <bind name="carrierName" value="carrierName + '%'"/>
                and lot.carrier_name like  #{carrierName}
            </if>
            <if test="pickStartDate != null and pickStartDate.trim() != ''">
                <![CDATA[ and to_char(history.reg_date,'yyyy-mm-dd') >= #{pickStartDate} ]]>
            </if>
            <if test="pickEndDate != null and pickEndDate.trim() != ''">
                <![CDATA[and to_char(history.reg_date,'yyyy-mm-dd') <= #{pickEndDate} ]]>
            </if>
            <if test="regUserName != null and regUserName != ''">
                <bind name="regUserName" value="regUserName + '%'"/>
                and SYS_USER.USER_NAME like  #{regUserName}
            </if>
        </where>
        and exists (select 1 from t_entrust entrust where entrust.lot_id = lot.entrust_lot_id ${params.dataScope})
        order by lot.REQ_DELI_DATE desc

    </select>


    <!--获取在途跟踪 应付对账-->
    <select id="countPayReconciliationAdjustList" resultType="PayReconciliationVO">
        select sum(sumTransFeeCount) sumTransFeeCount , sum(sumTransFeeCountAfter) sumTransFeeCountAfter from (
        select
        lot.lot,
        lot.entrust_lot_id entrustLotId,
        lot.vbillstatus,
        lot.car_no carNo,
        lot.car_type_name carTypeName,
        lot.car_len_name carLenName,
        lot.carrier_name carrierName,
        lot.deli_province_name || lot.deli_city_name || lot.deli_area_name deliDetailAddress,
        lot.arri_province_name || lot.arri_city_name || lot.arri_area_name arriDetailAddress,
        history.NUM_BEFORE numCount,
        history.WEIGHT_BEFORE weightCount,
        history.VOLUME_BEFORE volumeCount,
        lot.REQ_DELI_DATE reqDeliDate,
        history.WEIGHT_AFETER weightCountAdjust,
        history.VOLUME_AFTER volumeCountAdjust,
        history.NUM_AFTER numCountAdjust,
        nvl(CASH_BEFORE+OIL_BEFORE,0) receiptAmountFreight,
        nvl(ZXF_BEFORE+FK_BEFORE+KHPC_BEFORE+HS_BEFORE+QT_BEOFRE,0) receiptAmountOnWay,
        nvl(OIL_BEFORE,0) oilCardAmount,
        nvl(ZXF_BEFORE+FK_BEFORE+KHPC_BEFORE+HS_BEFORE+QT_BEOFRE+CASH_BEFORE+OIL_BEFORE,0) sumTransFeeCount,
        nvl(CASH_AFTER+OIL_AFTER,0) receiptAmountFreightAfter,
        nvl(ZXF_AFTER+FK_AFTER+KHPC_AFTER+HS_AFTER+QT_AFTER,0) receiptAmountOnWayAfter,
        nvl(OIL_AFTER,0) oilCardAmountAfter,
        nvl(ZXF_AFTER+FK_AFTER+KHPC_AFTER+HS_AFTER+QT_AFTER+CASH_AFTER+OIL_AFTER,0) sumTransFeeCountAfter,
        (SELECT listagg(t.DRIVER_NAME,',') WITHIN GROUP(ORDER BY t.LOT_CAR_DRIVER_ID) FROM T_ENTRUST_LOT_CAR_DRIVER t where t.lot_id=lot.entrust_lot_id) as driverName
        from T_PAY_DETAIL_ADJUST_HISTORY history
        left join T_ADJUST_RECORD adjustRecord on history.ADJUST_RECORD_ID = adjustRecord.ADJUST_RECORD_ID
        left join T_ENTRUST_lot lot on history.LOT_ID = lot.ENTRUST_LOT_ID
        <where>
            lot.del_flag = 0
            and adjustRecord.ADJUST_CHECK_STATUS = 3
            <!--运单号-->
            <if test="lot != null and lot != ''">
                <bind name="lot" value="lot + '%'"/>
                and lot.lot like  #{lot}
            </if>
            <!--运单状态-->
            <if test="lotVbillstatus != null  and lotVbillstatus != '' and lotVbillstatus.indexOf(',') != -1">
                and lot.vbillstatus in
                <foreach item="item" index="index" collection="lotVbillstatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="lotVbillstatus != null and lotVbillstatus.trim() != '' and lotVbillstatus.indexOf(',') == -1">
                and lot.vbillstatus = #{lotVbillstatus}
            </if>
            <!--承运商名称-->
            <if test="carrierName != null and carrierName != ''">
                <bind name="carrierName" value="carrierName + '%'"/>
                and lot.carrier_name like  #{carrierName}
            </if>
            <if test="pickStartDate != null and pickStartDate.trim() != ''">
                <![CDATA[ and to_char(history.reg_date,'yyyy-mm-dd') >= #{pickStartDate} ]]>
            </if>
            <if test="pickEndDate != null and pickEndDate.trim() != ''">
                <![CDATA[and to_char(history.reg_date,'yyyy-mm-dd') <= #{pickEndDate} ]]>
            </if>
        </where>
        ${params.dataScope}
        order by lot.REQ_DELI_DATE desc
        )

    </select>


    <!--获取在途跟踪 应付对账-->
    <select id="selectPayReconciliationAdjustListForCheck" resultType="PayReconciliationVO">
        select
        lot.lot,
        lot.entrust_lot_id entrustLotId,
        lot.vbillstatus,
        lot.car_no carNo,
        lot.car_type_name carTypeName,
        lot.car_len_name carLenName,
        lot.carrier_name carrierName,
        lot.deli_province_name || lot.deli_city_name || lot.deli_area_name deliDetailAddress,
        lot.arri_province_name || lot.arri_city_name || lot.arri_area_name arriDetailAddress,
        lot.num_count numCount,
        lot.weight_count weightCount,
        lot.volume_count volumeCount,
        lot.REQ_DELI_DATE reqDeliDate,
        lot.WEIGHT_COUNT_ADJUST weightCountAdjust,
        lot.VOLUME_COUNT_ADJUST volumeCountAdjust,
        lot.NUM_COUNT_ADJUST numCountAdjust,
        nvl(t1.receiptamountfreight,0) receiptAmountFreight,
        nvl(t1.receiptamountonway,0) receiptAmountOnWay,
        nvl(t1.oilCardAmount,0) oilCardAmount,
        t1.sumTransFeeCount,
        t1.sumGotAmount,
        t1.sumUngotAmount,
        nvl(t2.receiptamountfreight,0)+nvl(t1.receiptamountfreight,0) receiptAmountFreightAfter,
        nvl(t2.receiptamountonway,0)+ nvl(t1.receiptamountonway,0) receiptAmountOnWayAfter,
        nvl(t2.oilCardAmount,0)+nvl(t1.oilCardAmount,0) oilCardAmountAfter,
        nvl(t2.sumTransFeeCount,0)+nvl(t1.sumTransFeeCount,0) sumTransFeeCountAfter,
        nvl(t2.sumGotAmount,0)+nvl(t1.sumGotAmount,0) sumGotAmountAfter,
        nvl(t2.sumUngotAmount,0)+nvl(t1.sumUngotAmount,0) sumUngotAmountAfter,
        t2.adjust_check_status adjustCheckStatus,
        (SELECT listagg(t.DRIVER_NAME,',') WITHIN GROUP(ORDER BY t.LOT_CAR_DRIVER_ID) FROM T_ENTRUST_LOT_CAR_DRIVER t where t.lot_id=lot.entrust_lot_id) as driverName
        from t_entrust_lot lot
        left join (
        select
        pay.lotno,
        nvl(sum(pay.Trans_Fee_Count), 0) receiptAmount,
        nvl(sum(case when pay.free_type = 0 then pay.Trans_Fee_Count end), 0) receiptAmountFreight,
        nvl(sum(case when pay.free_type = 1 then pay.Trans_Fee_Count end), 0) receiptAmountOnWay,
        nvl(sum(case when pay.free_type = 0 and pay.cost_type_freight in (1,3,5) then pay.Trans_Fee_Count end), 0) oilCardAmount,
        sum(pay.trans_fee_count) sumTransFeeCount,
        sum(pay.got_amount) sumGotAmount,
        sum(pay.UNGOT_AMOUNT) sumUngotAmount
        from t_entrust_lot lot
        left join t_pay_detail pay
        on lot.lot = pay.lotno
        and pay.del_flag = 0
        <!--and pay.IS_ADJUST = 0-->
        group by pay.lotno) t1
        on lot.lot = t1.lotno
        join (
        select
        pay.lotno,pay.adjust_check_status,
        nvl(sum(pay.Trans_Fee_Count), 0) receiptAmount,
        nvl(sum(case when pay.free_type = 0 then pay.Trans_Fee_Count end), 0) receiptAmountFreight,
        nvl(sum(case when pay.free_type = 1 then pay.Trans_Fee_Count end), 0) receiptAmountOnWay,
        nvl(sum(case when pay.free_type = 0 and pay.cost_type_freight in (1,3,5) then pay.Trans_Fee_Count end), 0) oilCardAmount,
        sum(pay.trans_fee_count) sumTransFeeCount,
        sum(pay.got_amount) sumGotAmount,
        sum(pay.UNGOT_AMOUNT) sumUngotAmount
        from t_entrust_lot lot
        left join T_PAY_DETAIL_CHECK pay
        on lot.lot = pay.lotno
        where pay.del_flag = 0 and pay.ADJUST_RECORD_ID = #{adjustRecordId}
        group by pay.lotno,pay.adjust_check_status) t2
        on lot.lot = t2.lotno
        <where>
            lot.del_flag = 0
            <!--运单号-->
            <if test="lot != null and lot != ''">
                <bind name="lot" value="lot + '%'"/>
                and lot.lot like  #{lot}
            </if>
            <!--运单状态-->
            <if test="lotVbillstatus != null  and lotVbillstatus != '' and lotVbillstatus.indexOf(',') != -1">
                and lot.vbillstatus in
                <foreach item="item" index="index" collection="lotVbillstatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="lotVbillstatus != null and lotVbillstatus.trim() != '' and lotVbillstatus.indexOf(',') == -1">
                and lot.vbillstatus = #{lotVbillstatus}
            </if>
            <!--承运商名称-->
            <if test="carrierName != null and carrierName != ''">
                <bind name="carrierName" value="carrierName + '%'"/>
                and lot.carrier_name like  #{carrierName}
            </if>
        </where>
        ${params.dataScope}
        order by lot.REQ_DELI_DATE desc

    </select>

    <select id="selectPayReconciliationAdjustListForCheckMultiple" resultType="PayReconciliationVO">
        select ADJUST_RECORD_ID adjustRecordId,
               adjust_check_status adjustCheckStatus,
               sum(numCount) numCount,
               sum(weightCount) weightCount,
               sum(volumeCount) volumeCount,
               sum(weightCountAdjust) weightCountAdjust,
               sum(volumeCountAdjust) volumeCountAdjust,
               sum(numCountAdjust) numCountAdjust,
               sum(receiptAmountFreight) receiptAmountFreight,
               sum(receiptAmountOnWay) receiptAmountOnWay,
               sum(oilCardAmount) oilCardAmount,
               sum(sumTransFeeCount) sumTransFeeCount,
               sum(sumGotAmount) sumGotAmount,
               sum(sumUngotAmount) sumUngotAmount,
               sum(receiptAmountFreightAfter) receiptAmountFreightAfter,
               sum(receiptAmountOnWayAfter) receiptAmountOnWayAfter,
               sum(oilCardAmountAfter) oilCardAmountAfter,
               sum(sumTransFeeCountAfter) sumTransFeeCountAfter,
               sum(sumGotAmountAfter) sumGotAmountAfter,
               sum(sumUngotAmountAfter) sumUngotAmountAfter

        from (
                 select adjustRecord.ADJUST_RECORD_ID,
                        adjustRecord.adjust_check_status,
                        lot.num_count numCount,
                        lot.weight_count weightCount,
                        lot.volume_count volumeCount,
                        lot.WEIGHT_COUNT_ADJUST weightCountAdjust,
                        lot.VOLUME_COUNT_ADJUST volumeCountAdjust,
                        lot.NUM_COUNT_ADJUST numCountAdjust,
                        nvl(t1.receiptamountfreight,0) receiptAmountFreight,
                        nvl(t1.receiptamountonway,0) receiptAmountOnWay,
                        nvl(t1.oilCardAmount,0) oilCardAmount,
                        t1.sumTransFeeCount,
                        t1.sumGotAmount,
                        t1.sumUngotAmount,
                        nvl(t2.receiptamountfreight,0)+nvl(t1.receiptamountfreight,0) receiptAmountFreightAfter,
                        nvl(t2.receiptamountonway,0)+ nvl(t1.receiptamountonway,0) receiptAmountOnWayAfter,
                        nvl(t2.oilCardAmount,0)+nvl(t1.oilCardAmount,0) oilCardAmountAfter,
                        nvl(t2.sumTransFeeCount,0)+nvl(t1.sumTransFeeCount,0) sumTransFeeCountAfter,
                        nvl(t2.sumGotAmount,0)+nvl(t1.sumGotAmount,0) sumGotAmountAfter,
                        nvl(t2.sumUngotAmount,0)+nvl(t1.sumUngotAmount,0) sumUngotAmountAfter
                 from T_ADJUST_RECORD adjustRecord
                          join (
                     select
                         pay.ADJUST_RECORD_ID,pay.lot_id,
                         nvl(sum(pay.Trans_Fee_Count), 0) receiptAmount,
                         nvl(sum(case when pay.free_type = 0 then pay.Trans_Fee_Count end), 0) receiptAmountFreight,
                         nvl(sum(case when pay.free_type = 1 then pay.Trans_Fee_Count end), 0) receiptAmountOnWay,
                         nvl(sum(case when pay.free_type = 0 and pay.cost_type_freight in (1,3,5) then pay.Trans_Fee_Count end), 0) oilCardAmount,
                         sum(pay.trans_fee_count) sumTransFeeCount,
                         sum(pay.got_amount) sumGotAmount,
                         sum(pay.UNGOT_AMOUNT) sumUngotAmount
                     from  T_PAY_DETAIL_CHECK pay
                     where pay.del_flag = 0 and pay.ADJUST_RECORD_ID in (<foreach collection="adjustRecordIds" separator="," item="adjustRecord">#{adjustRecord.adjustRecordId}</foreach>)
                     group by pay.ADJUST_RECORD_ID,pay.lot_id) t2 on t2.ADJUST_RECORD_ID = adjustRecord.ADJUST_RECORD_ID
                          left join (
                     select
                         pay.lot_id,
                         nvl(sum(pay.Trans_Fee_Count), 0) receiptAmount,
                         nvl(sum(case when pay.free_type = 0 then pay.Trans_Fee_Count end), 0) receiptAmountFreight,
                         nvl(sum(case when pay.free_type = 1 then pay.Trans_Fee_Count end), 0) receiptAmountOnWay,
                         nvl(sum(case when pay.free_type = 0 and pay.cost_type_freight in (1,3,5) then pay.Trans_Fee_Count end), 0) oilCardAmount,
                         sum(pay.trans_fee_count) sumTransFeeCount,
                         sum(pay.got_amount) sumGotAmount,
                         sum(pay.UNGOT_AMOUNT) sumUngotAmount
                     from t_entrust_lot lot
                              left join t_pay_detail pay
                                        on lot.lot = pay.lotno
                                            and pay.del_flag = 0
                     group by pay.lot_id) t1
                                    on t2.lot_id = t1.lot_id
                          left join t_entrust_lot lot on lot.ENTRUST_LOT_ID = t2.lot_id
                 where adjustRecord.ADJUST_RECORD_ID in (<foreach collection="adjustRecordIds" separator="," item="adjustRecord">#{adjustRecord.adjustRecordId}</foreach>)
             ) group by ADJUST_RECORD_ID,adjust_check_status
    </select>

    <!--获取在途跟踪 应付对账-->
    <select id="selectPayReconciliationListAdjust" resultType="PayReconciliationVO">
        select
        lot.lot,
        lot.entrust_lot_id entrustLotId,
        lot.vbillstatus,
        lot.car_no carNo,
        nvl(lot.car_type_name,'') carTypeName,
        nvl(lot.car_len_name,'') carLenName,
        lot.carrier_name carrierName,
        lot.deli_province_name || lot.deli_city_name || lot.deli_area_name deliDetailAddress,
        lot.arri_province_name || lot.arri_city_name || lot.arri_area_name arriDetailAddress,
        nvl(lot.num_count,0) numCount,
        nvl(lot.weight_count,0) weightCount,
        nvl(lot.volume_count,0) volumeCount,
        nvl(t1.receiptamountfreight,0) receiptAmountFreight,
        nvl(t1.receiptamountonway,0) receiptAmountOnWay,
        nvl(t1.oilCardAmount,0) oilCardAmount,
        nvl(t1.moneyFreight,0) moneyFreight,
        nvl(t1.oilFreight,0) oilFreight,
        nvl(t1.zxfOnWay,0) zxfOnWay,
        nvl(t1.fkfOnWay,0) fkfOnWay,
        nvl(t1.khpcOnWay,0) khpcOnWay,
        nvl(t1.hsOnWay,0) hsOnWay,
        nvl(t1.qtOnWay,0) qtOnWay,
        nvl(t1.sumTransFeeCount,0) sumTransFeeCount ,
        nvl(t1.sumGotAmount,0) sumGotAmount,
        nvl(t1.sumUngotAmount,0) sumUngotAmount,
        dict.dict_label as transName,
        lot.REQ_DELI_DATE reqDeliDate,
        (SELECT listagg(t.DRIVER_NAME,',') WITHIN GROUP(ORDER BY t.LOT_CAR_DRIVER_ID) FROM T_ENTRUST_LOT_CAR_DRIVER t where t.lot_id=lot.entrust_lot_id) as driverName
        from t_entrust_lot lot
        left join sys_dict_data dict on dict.dict_type = 'trans_code'
        and dict.dict_value = lot.trans_type
        left join (
        select
        pay.lotno,
        nvl(sum(pay.Trans_Fee_Count), 0) receiptAmount,
        nvl(sum(case when pay.free_type = 0 then pay.Trans_Fee_Count+nvl(pay.tax_amount,0) end), 0) receiptAmountFreight,
        nvl(sum(case when pay.free_type = 1 then pay.Trans_Fee_Count+nvl(pay.tax_amount,0) end), 0) receiptAmountOnWay,
        nvl(sum(case when pay.cost_type_freight in (1,3,5) then pay.Trans_Fee_Count end), 0) oilCardAmount,
        nvl(sum(case when pay.free_type = 0 and pay.cost_type_freight in (0,2,4) then pay.Trans_Fee_Count+nvl(pay.tax_amount,0) end), 0) moneyFreight,
        nvl(sum(case when pay.free_type = 0 and pay.cost_type_freight in (1,3,5) then pay.Trans_Fee_Count+nvl(pay.tax_amount,0) end), 0) oilFreight,
        nvl(sum(case when pay.free_type = 1 and pay.COST_TYPE_ON_WAY = 1  then pay.Trans_Fee_Count end+nvl(pay.tax_amount,0)), 0) zxfOnWay,
        nvl(sum(case when pay.free_type = 1 and pay.COST_TYPE_ON_WAY = 3  then pay.Trans_Fee_Count end+nvl(pay.tax_amount,0)), 0) fkfOnWay,
        nvl(sum(case when pay.free_type = 1 and pay.COST_TYPE_ON_WAY = 14  then pay.Trans_Fee_Count end+nvl(pay.tax_amount,0)), 0) khpcOnWay,
        nvl(sum(case when pay.free_type = 1 and pay.COST_TYPE_ON_WAY = 19  then pay.Trans_Fee_Count end+nvl(pay.tax_amount,0)), 0) hsOnWay,
        nvl(sum(case when pay.free_type = 1 and pay.COST_TYPE_ON_WAY not in (1,3,14,19)  then pay.Trans_Fee_Count end+nvl(pay.tax_amount,0)), 0) qtOnWay,
        sum(pay.trans_fee_count+nvl(pay.tax_amount,0)) sumTransFeeCount,
        sum(pay.got_amount) sumGotAmount,
        sum(pay.UNGOT_AMOUNT) sumUngotAmount
        from t_entrust_lot lot
        left join t_pay_detail pay
        on lot.lot = pay.lotno
        and pay.del_flag = 0
        group by pay.lotno) t1
        on lot.lot = t1.lotno
        left join m_carrier carrier
        on lot.carrier_id = carrier.carrier_id
        <where>
            lot.del_flag = 0
            <if test="entrustLotId != null and entrustLotId != ''">
                and lot.entrust_lot_id =  #{entrustLotId}
            </if>
            <!--运单号-->
           <if test="lot != null and lot != ''">
                <choose>
                    <when test="lot.indexOf(',') lt 0">
                        <bind name="lot" value="lot + '%'"/>
                        and lot.lot like  #{lot}
                    </when>
                    <when test="lot.split(',').length > 0">
                        and lot.lot in (<foreach collection="lot.split(',')" item="itm" separator=",">#{itm}</foreach>)
                    </when>
                </choose>
            </if>
            <if test="lotVbillstatus != null  and lotVbillstatus != '' and lotVbillstatus.indexOf(',') != -1">
                and lot.vbillstatus in
                <foreach item="item" index="index" collection="lotVbillstatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="lotVbillstatus != null and lotVbillstatus.trim() != '' and lotVbillstatus.indexOf(',') == -1">
                and lot.vbillstatus = #{lotVbillstatus}
            </if>
            <if test="carrierName != null and carrierName != ''">
                <bind name="carrierName" value="carrierName + '%'"/>
                and lot.carrier_name like  #{carrierName}
            </if>
            <if test="params.reqDeliDateStart != null  and params.reqDeliDateStart != ''">
                and to_char(lot.req_deli_date,'yyyy-mm-dd') <![CDATA[ >= ]]> #{params.reqDeliDateStart}
            </if>
            <if test="params.reqDeliDateEnd != null  and params.reqDeliDateEnd != ''">
                and to_char(lot.req_deli_date,'yyyy-mm-dd') <![CDATA[ <= ]]> #{params.reqDeliDateEnd}
            </if>
            <!--提货省市区-->
            <if test="deliProvince != null  and deliProvince != '' "> and lot.deli_province = #{deliProvince}</if>
            <if test="deliCity != null  and deliCity != '' "> and lot.deli_city = #{deliCity}</if>
            <if test="deliArea != null  and deliArea != '' "> and lot.deli_area = #{deliArea}</if>
            <!--到货省市区-->
            <if test="arriProvince != null  and arriProvince != '' "> and lot.arri_province = #{arriProvince}</if>
            <if test="arriCity != null  and arriCity != '' "> and lot.arri_city = #{arriCity}</if>
            <if test="arriArea != null  and arriArea != '' "> and lot.arri_area = #{arriArea}</if>
            <!--调度组-->
            <if test="transLineId != null and transLineId != ''">
                and lot.trans_line_id like  #{transLineId}
            </if>
            <!--承运商类别-->
            <if test="carrType != null and carrType != ''">
                and carrier.carr_type =  #{carrType}
            </if>
            <if test="isFleetData != null and isFleetData != ''">
                and lot.is_fleet_data = #{isFleetData,jdbcType=VARCHAR}
            </if>
        </where>
        ${params.dataScope}
    </select>

    <!--获取在途跟踪 应付对账-->
    <select id="selectPayReconciliationListForCheck" resultType="PayReconciliationVO">
        select a.*,
            a1.lot,
            a1.entrust_lot_id entrustLotId,
            a1.vbillstatus,
            a1.car_no carNo,
            a1.car_type_name carTypeName,
            a1.car_len_name carLenName,
            a1.carrier_name carrierName,
            a1.deli_province_name || a1.deli_city_name || a1.deli_area_name deliDetailAddress,
            a1.arri_province_name || a1.arri_city_name || a1.arri_area_name arriDetailAddress,
            a1.num_count numCount,
            a1.weight_count weightCount,
            a1.volume_count volumeCount,
            (SELECT listagg(t.DRIVER_NAME, ',') WITHIN GROUP(ORDER BY t.LOT_CAR_DRIVER_ID)
            FROM T_ENTRUST_LOT_CAR_DRIVER t
            where t.lot_id = a1.entrust_lot_id) as driverName
        from (select t1.LOT_ID,
                    nvl(sum(t1.Trans_Fee_Count), 0) receiptAmount,
                    nvl(sum(case
                    when t1.free_type = 0 then
                    t1.Trans_Fee_Count
                    end),
                    0) receiptAmountFreight,
                    nvl(sum(case
                    when t1.free_type = 1 then
                    t1.Trans_Fee_Count
                    end),
                    0) receiptAmountOnWay,
                    nvl(sum(case
                    when t1.cost_type_freight in (1, 3, 5) then
                    t1.Trans_Fee_Count
                    end),
                    0) oilCardAmount,
                    nvl(sum(t1.tax_amount),0) sumTaxAmount,
                    sum(t1.trans_fee_count) sumTransFeeCount,
                    sum(t1.got_amount) sumGotAmount,
                    sum(t1.UNGOT_AMOUNT) sumUngotAmount
              from t_pay_check_sheet_b t
            left join t_pay_detail t1
              on t.PAY_DETAIL_ID = t1.PAY_DETAIL_ID
        where t.pay_check_sheet_id = #{payCheckSheetId}
        and t1.del_flag = 0
        group by t1.LOT_ID) a
        left join t_entrust_lot a1
          on a.LOT_ID = a1.ENTRUST_LOT_ID
        <where>
            <!--运单号-->
            <if test="lot != null and lot != ''">
                <bind name="lot" value="lot + '%'"/>
                and a1.lot like  #{lot}
            </if>
            <!--运单状态-->
            <if test="lotVbillstatus != null  and lotVbillstatus != '' and lotVbillstatus.indexOf(',') != -1">
                and a1.vbillstatus in
                <foreach item="item" index="index" collection="lotVbillstatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="lotVbillstatus != null and lotVbillstatus.trim() != '' and lotVbillstatus.indexOf(',') == -1">
                and a1.vbillstatus = #{lotVbillstatus}
            </if>
            <!--承运商名称-->
            <if test="carrierName != null and carrierName != ''">
                <bind name="carrierName" value="carrierName + '%'"/>
                and a1.carrier_name like  #{carrierName}
            </if>
        </where>
    </select>

    <select id="selectPayDetailListDriverReceipt" parameterType="PayDetail" resultMap="PayDetailResult">
        select
        t_pay_detail.pay_detail_id,
        vbillno,
        vbillstatus,
        free_type,
        cost_type_on_way,
        cost_type_freight,
        lot_id,
        lotno,
        carrier_id,
        carr_code,
        carr_name,
        bala_corp,
        balatype,
        trans_fee_count,
        got_amount,
        ungot_amount,
        oil_card_number,
        check_no,
        check_head,
        unconfirm_type,
        unconfirm_memo,
        confirm_time,
        confirm_user,
        driver_mobile,
        driver_name,
        carno,
        req_deli_date,
        req_arri_date,
        is_close,
        is_adjust,
        adjust_memo,
        t_pay_detail.reg_date,
        t_pay_detail.del_user_id,
        rec_card_no,
        rec_account,
        rec_bank,
        is_ntocc,
        apply_user,
        apply_time,
        sysUser.user_name reg_user_id
        from t_pay_detail
        left join sys_user sysUser on t_pay_detail.reg_user_id = sysUser.USER_ID and sysUser.del_flag = 0
        <where>
            T_PAY_DETAIL.DEL_FLAG = 0 and T_PAY_DETAIL.trans_fee_count <![CDATA[   <  ]]> 0
            <if test="payDetailId != null  and payDetailId != '' "> and pay_detail_id = #{payDetailId}</if>
            <if test="vbillno != null  and vbillno != '' "> and vbillno = #{vbillno}</if>
            <if test="vbillstatus != null "> and vbillstatus = #{vbillstatus}</if>
            <if test="freeType != null  and freeType != '' "> and free_type = #{freeType}</if>
            <if test="costTypeOnWay != null  and costTypeOnWay != '' "> and cost_type_on_way = #{costTypeOnWay}</if>
            <if test="costTypeFreight != null  and costTypeFreight != '' "> and cost_type_freight = #{costTypeFreight}</if>
            <if test="lotId != null  and lotId != '' "> and lot_id = #{lotId}</if>
            <if test="lotno != null  and lotno != '' "> and lotno = #{lotno}</if>
            <if test="carrierId != null  and carrierId != '' "> and carrier_id = #{carrierId}</if>
            <if test="carrCode != null  and carrCode != '' "> and carr_code = #{carrCode}</if>
            <if test="carrName != null  and carrName != '' "> and carr_name = #{carrName}</if>
            <if test="balaCorp != null  and balaCorp != '' "> and bala_corp = #{balaCorp}</if>
            <if test="balatype != null  and balatype != '' "> and balatype = #{balatype}</if>
            <if test="transFeeCount != null "> and trans_fee_count = #{transFeeCount}</if>
            <if test="gotAmount != null "> and got_amount = #{gotAmount}</if>
            <if test="ungotAmount != null "> and ungot_amount = #{ungotAmount}</if>
            <if test="oilCardNumber != null  and oilCardNumber != '' "> and oil_card_number = #{oilCardNumber}</if>
            <if test="checkNo != null  and checkNo != '' "> and check_no = #{checkNo}</if>
            <if test="checkHead != null  and checkHead != '' "> and check_head = #{checkHead}</if>
            <if test="unconfirmType != null "> and unconfirm_type = #{unconfirmType}</if>
            <if test="unconfirmMemo != null  and unconfirmMemo != '' "> and unconfirm_memo = #{unconfirmMemo}</if>
            <if test="confirmTime != null "> and confirm_time = #{confirmTime}</if>
            <if test="confirmUser != null  and confirmUser != '' "> and confirm_user = #{confirmUser}</if>
            <if test="driverMobile != null  and driverMobile != '' "> and driver_mobile = #{driverMobile}</if>
            <if test="driverName != null  and driverName != '' "> and driver_name = #{driverName}</if>
            <if test="carno != null  and carno != '' "> and carno = #{carno}</if>
            <if test="reqDeliDate != null "> and req_deli_date = #{reqDeliDate}</if>
            <if test="reqArriDate != null "> and req_arri_date = #{reqArriDate}</if>
            <if test="isClose != null "> and is_close = #{isClose}</if>
            <if test="isAdjust != null "> and is_adjust = #{isAdjust}</if>
            <if test="adjustMemo != null  and adjustMemo != '' "> and adjust_memo = #{adjustMemo}</if>
            <if test="memo != null  and memo != '' "> and memo = #{memo}</if>
            <if test="regUserName != null and regUserName != ''">
                <bind name="regUserName" value="regUserName + '%'"/>
                and sysUser.user_name like  #{regUserName}
            </if>
            <if test="recCardNo != null  and recCardNo != '' "> and rec_card_no = #{recCardNo}</if>
            <if test="recAccount != null  and recAccount != '' "> and rec_account = #{recAccount}</if>
            <if test="recBank != null  and recBank != '' "> and rec_bank = #{recBank}</if>
            <if test="isNtocc != null "> and is_ntocc = #{isNtocc}</if>
            <if test="carrBankId != null  and carrBankId != '' "> and carr_bank_id = #{carrBankId}</if>
            <if test="applyTime != null "> and apply_time = #{applyTime}</if>
            <if test="applyUser != null  and applyUser != '' "> and apply_user = #{applyUser}</if>
            <if test="bakcWriteType != null "> and bakc_write_type = #{bakcWriteType}</if>
            <if test="backWriteMemo != null  and backWriteMemo != '' "> and back_write_memo = #{backWriteMemo}</if>
            <if test="backWriteTime != null "> and back_write_time = #{backWriteTime}</if>
        </where>
    </select>

    <select id="selectPayDetailAmountCount" resultType="java.util.Map">
        SELECT
        sum(nvl(t.trans_fee_count,0)) transFeeCount,
        sum(nvl(t.got_amount,0)) gotAmount,
        sum(nvl(t.ungot_amount,0)) ungotAmount
        FROM
        t_pay_detail t
        left join sys_user sysUser on t.reg_user_id = sysUser.USER_ID and sysUser.del_flag = 0
        left join T_PAY_CHECK_SHEET_B sheet on t.PAY_DETAIL_ID = sheet.PAY_DETAIL_ID
        left join T_ENTRUST_LOT lot on t.LOT_ID = lot.ENTRUST_LOT_ID
        <where>
            t.del_flag != 1
            and lot.DEL_FLAG = 0
            <if test="lotno != null and lotno.trim() != ''">
                <bind name="lotno" value="lotno + '%'"/>
                and t.lotno like #{lotno}
            </if>
            <if test="vbillno != null and vbillno.trim() != ''">
                <bind name="vbillno" value="vbillno + '%'"/>
                and t.vbillno like #{vbillno}
            </if>

            <if test="vbillstatus != null">
                and t.vbillstatus = #{vbillstatus}
            </if>
            <if test="status != null  and status != '' and status.indexOf(',') != -1">
                and t.vbillstatus in
                <foreach item="item" index="index" collection="status.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status.trim() != '' and status.indexOf(',') == -1">
                and t.vbillstatus = #{status}
            </if>
            <if test="carrName != null and carrName.trim() != ''">
                <bind name="carrName" value="carrName + '%'"/>
                and t.carr_name like #{carrName}
            </if>
            <if test="recAccount != null and recAccount.trim() != ''">
                <bind name="recAccount" value="recAccount + '%'"/>
                and t.rec_account like #{recAccount}
            </if>
            <if test="balatype != null and balatype.trim() != ''">
                and t.balatype = #{balatype}
            </if>
            <if test="driverName != null and driverName.trim() != ''">
                <bind name="driverName" value="driverName + '%'"/>
                and t.driver_name like #{driverName}
            </if>

            <if test="startDate != null ">
                and t.REG_DATE <![CDATA[   >=  ]]> #{startDate}
            </if>
            <if test="endtDate != null ">
                and t.REG_DATE <![CDATA[   <=  ]]> #{endtDate}
            </if>
            <if test="regUserName != null and regUserName != ''">
                <bind name="regUserName" value="regUserName + '%'"/>
                and sysUser.user_name like  #{regUserName}
            </if>
            <if test="applyUser != null and applyUser != ''">
                <bind name="applyUser" value="applyUser + '%'"/>
                and t.apply_user like  #{applyUser}
            </if>
            <if test="carrierId != null and carrierId.trim() != ''">
                and t.carrier_id = #{carrierId}
            </if>
            <if test="lotId != null and lotId != ''">
                and t.lot_id = #{lotId}
            </if>
            <if test="costTypeFreight != null  and costTypeFreight != ''">
                and t.cost_type_freight in
                <foreach item="item" index="index" collection="costTypeFreight.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and t.free_type = 0
            </if>
            <!--调度组-->
            <if test="params.transLineId != null  and params.transLineId != '' and params.transLineId.indexOf(',') != -1">
                and lot.trans_line_id in
                <foreach item="item" index="index" collection="params.transLineId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.transLineId != null and params.transLineId.trim() != '' and params.transLineId.indexOf(',') == -1">
                and lot.TRANS_LINE_ID = #{params.transLineId}
            </if>
            <!--申请时间-->
            <if test="params.applyDateStart != null and params.applyDateStart != ''">
                and t.apply_time <![CDATA[   >=  ]]> to_date(#{params.applyDateStart},'yyyy-MM-dd')
            </if>
            <if test="params.applyDateEnd != null and params.applyDateEnd != ''">
                and t.apply_time <![CDATA[   <=  ]]> to_date(#{params.applyDateEnd},'yyyy-MM-dd')
            </if>
        </where>
        order by t.req_pay_date ,t.vbillno desc
    </select>

    <select id="selectPackagePayDetailAmountCount" resultType="java.util.Map">
        SELECT
        sum(nvl(t.trans_fee_count,0)) transFeeCount,
        sum(nvl(t.got_amount,0)) gotAmount,
        sum(nvl(t.ungot_amount,0)) ungotAmount
        FROM
        t_pay_detail t
        left join sys_user sysUser on t.reg_user_id = sysUser.USER_ID and sysUser.del_flag = 0
        left join T_ENTRUST_LOT lot on t.LOT_ID = lot.ENTRUST_LOT_ID and lot.DEL_FLAG = 0
        left join T_ENTRUST entrust on entrust.LOT_ID = lot.ENTRUST_LOT_ID and entrust.DEL_FLAG = 0
        left join m_carrier carr on carr.carrier_id = t.carrier_id and carr.del_flag = 0
        left join (select LOT_ID,listagg(INVOICE_VBILLNO,',')  WITHIN GROUP(ORDER BY LOT_ID) INVOICE_VBILLNO from T_ENTRUST group by LOT_ID) t2
        on lot.ENTRUST_LOT_ID = t2.LOT_ID
        left join (select LOT_ID,listagg(cust_abbr,',')  WITHIN GROUP(ORDER BY LOT_ID) cust_abbr from T_ENTRUST group by LOT_ID) t3
        on lot.ENTRUST_LOT_ID = t3.LOT_ID
        <where>
            <!--排除对账单的应付明细-->
            t.del_flag != 1
            and lot.DEL_FLAG = 0
            and carr.BALA_TYPE = 2
            and  not exists(
            select ENTRUST_ID from T_ENTRUST entrust
            where  entrust.LOT_ID = lot.ENTRUST_LOT_ID
            and (RECEIPT_CONFIRM_FLAG = 0 or RECEIPT_CONFIRM_FLAG is null)
            and entrust.DEL_FLAG = 0
            )
            <!--回单日期-->
            <if test="receiptDateStart != null ">
                and entrust.receipt_date <![CDATA[   >=  ]]> #{receiptDateStart}
            </if>
            <if test="receiptDateEnd != null  ">
                and entrust.receipt_date <![CDATA[   <=  ]]> #{receiptDateEnd}
            </if>
            <!--要求提货日期-->
            <if test="reqDeliDateStart != null ">
                and to_date(to_char(entrust.req_deli_date,'yyyy-MM-dd'),'yyyy-MM-dd') <![CDATA[   >=  ]]> to_date(to_char(#{reqDeliDateStart},'yyyy-MM-dd'),'yyyy-MM-dd')
            </if>
            <if test="reqDeliDateEnd != null  ">
                and to_date(to_char(entrust.req_deli_date,'yyyy-MM-dd'),'yyyy-MM-dd') <![CDATA[   <=  ]]> to_date(to_char(#{reqDeliDateEnd},'yyyy-MM-dd'),'yyyy-MM-dd')
            </if>
            <if test="lotno != null and lotno.trim() != ''">
                <bind name="lotno" value="lotno + '%'"/>
                and t.lotno like #{lotno}
            </if>
            <if test="vbillno != null and vbillno.trim() != ''">
                <bind name="vbillno" value="vbillno + '%'"/>
                and t.vbillno like #{vbillno}
            </if>
            <if test="isNtocc != null and isNtocc != ''">
                and t.is_ntocc = #{isNtocc}
            </if>
            <if test="vbillstatus != null">
                and t.vbillstatus = #{vbillstatus}
            </if>
            <if test="status != null  and status != '' and status.indexOf(',') != -1">
                and t.vbillstatus in
                <foreach item="item" index="index" collection="status.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status.trim() != '' and status.indexOf(',') == -1">
                and t.vbillstatus = #{status}
            </if>
            <if test="carrName != null and carrName.trim() != ''">
                <bind name="carrName" value="carrName + '%'"/>
                and t.carr_name like #{carrName}
            </if>
            <if test="recAccount != null and recAccount.trim() != ''">
                <bind name="recAccount" value="recAccount + '%'"/>
                and t.rec_account like #{recAccount}
            </if>
            <if test="balatype != null and balatype.trim() != ''">
                and t.balatype = #{balatype}
            </if>
            <if test="driverName != null and driverName.trim() != ''">
                <bind name="driverName" value="driverName + '%'"/>
                and t.driver_name like #{driverName}
            </if>

            <if test="startDate != null ">
                and t.REG_DATE <![CDATA[   >=  ]]> #{startDate}
            </if>
            <if test="endtDate != null ">
                and t.REG_DATE <![CDATA[   <=  ]]> #{endtDate}
            </if>
            <if test="regUserName != null and regUserName != ''">
                <bind name="regUserName" value="regUserName + '%'"/>
                and sysUser.user_name like  #{regUserName}
            </if>
            <if test="applyUser != null and applyUser != ''">
                <bind name="applyUser" value="applyUser + '%'"/>
                and t.apply_user like  #{applyUser}
            </if>
            <if test="carrierId != null and carrierId.trim() != ''">
                and t.carrier_id = #{carrierId}
            </if>
            <if test="lotId != null and lotId != ''">
                and t.lot_id = #{lotId}
            </if>
            <if test="costTypeFreight != null  and costTypeFreight != ''">
                and t.cost_type_freight in
                <foreach item="item" index="index" collection="costTypeFreight.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and t.free_type = 0
            </if>
            <!--发货单号-->
            <if test="invoiceVbillno != null and invoiceVbillno != ''">
                <bind name="invoiceVbillno" value="invoiceVbillno + '%'"/>
                and t2.INVOICE_VBILLNO like  #{invoiceVbillno}
            </if>
            <!--客户简称-->
            <if test="custAbbr != null and custAbbr != ''">
                <bind name="custAbbr" value="custAbbr + '%'"/>
                and t3.cust_abbr like  #{custAbbr}
            </if>
        </where>
    </select>

    <!--获取所有数据的金额合计 运输中心应付对账-->
    <select id="selectPayReconciliationListAmountCount" resultType="java.util.Map">
        select
            sum(receiptAmountFreight) receiptAmountFreight,
            sum(receiptAmountOnWay) receiptAmountOnWay,
            sum(oilCardAmount) oilCardAmount,
            sum(sumTransFeeCount) sumTransFeeCount,
            sum(sumGotAmount) sumGotAmount,
            sum(sumTaxAmount) sumTaxAmount,
            sum(sumUngotAmount) sumUngotAmount
        from (
            select
            nvl(t1.receiptamountfreight,0) receiptAmountFreight,
            nvl(t1.receiptamountonway,0) receiptAmountOnWay,
            nvl(t1.oilCardAmount,0) oilCardAmount,
            nvl(t1.sumTransFeeCount,0) sumTransFeeCount,
            nvl(t1.sumGotAmount,0) sumGotAmount,
            nvl(t1.sumTaxAmount,0) sumTaxAmount,
            nvl(t1.sumTransFeeCount,0) + nvl(t1.sumTaxAmount,0) - nvl(t1.sumGotAmount,0) sumUngotAmount,
            (SELECT listagg(goods_name, ',') WITHIN GROUP (ORDER BY goods_name)
            FROM (select distinct packGoods.goods_name
            from t_entrust entrust
            left join t_ent_pack_goods packGoods on packGoods.entrust_id = entrust.entrust_id
            where entrust.lot_id = lot.entrust_lot_id
            and entrust.DEL_FLAG = 0)
            )  goodsName,
            (SELECT listagg(cust_abbr, ',') WITHIN GROUP (ORDER BY cust_abbr)
            FROM (select distinct entrust.cust_abbr
            from t_entrust entrust
            where entrust.lot_id = lot.entrust_lot_id
            and entrust.DEL_FLAG = 0)
            )  custAbbr
        from t_entrust_lot lot
        left join (
            select
            pay.lot_id,
            sum(nvl(pay.Trans_Fee_Count, 0)) receiptAmount,
            nvl(sum(case when pay.free_type = 0 then pay.Trans_Fee_Count+nvl(pay.TAX_AMOUNT,0) end), 0) receiptAmountFreight,
            nvl(sum(case when pay.free_type = 1 then pay.Trans_Fee_Count+nvl(pay.TAX_AMOUNT,0) end), 0) receiptAmountOnWay,
            nvl(sum(case when pay.free_type = 0 and pay.cost_type_freight in (1,3,5) then pay.Trans_Fee_Count+nvl(pay.TAX_AMOUNT,0) end), 0) oilCardAmount,
            sum(nvl(pay.trans_fee_count,0)) sumTransFeeCount,
            sum(nvl(pay.tax_amount,0)) sumTaxAmount,
            sum(nvl(pay.got_amount,0)) sumGotAmount,
            sum(nvl(pay.trans_fee_count,0)) + sum(nvl(pay.tax_amount,0)) - sum(nvl(pay.got_amount,0)) sumUngotAmount
            from t_entrust_lot lot
            left join t_pay_detail pay
              on lot.lot = pay.lotno
              and pay.del_flag = 0

                group by pay.lot_id) t1
          on lot.entrust_lot_id = t1.lot_id
        left join sys_user t2 on lot.reg_user_id = t2.user_id
        left join m_carrier t4 on lot.CARRIER_ID = t4.CARRIER_ID
        <where>
            lot.del_flag = 0 and lot.reg_scr_id != 'importAllData'
            <if test="isFleetData != null and isFleetData != ''">
                and lot.is_Fleet_Data =  #{isFleetData,jdbcType=VARCHAR}
            </if>
            <if test="carLen != null and carLen != ''">
                and lot.car_len =  #{carLen}
            </if>
            <if test="isAgencyCollect != null">
                and lot.is_agency_collect =  #{isAgencyCollect}
            </if>
            <if test="payableWriteOffStatusList != null  and payableWriteOffStatusList != ''">
                and lot.payable_write_off_status in
                <foreach item="item" index="index" collection="payableWriteOffStatusList.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <!--调度组-->
            <if test="transLineId != null and transLineId != ''">
                and lot.trans_line_id = #{transLineId}
            </if>
            <!--运单号-->
            <if test="lot != null and lot != ''">
                <choose>
                    <when test="lot.indexOf(',') lt 0">
                        <bind name="lot" value="lot + '%'"/>
                        and lot.lot like #{lot}
                    </when>
                    <when test="lot.split(',').length > 0">
                        and lot.lot in (<foreach collection="lot.split(',')" item="itm" separator=",">#{itm}</foreach>)
                    </when>
                </choose>

            </if>
            <!--运单状态-->
            <if test="lotVbillstatus != null  and lotVbillstatus != '' and lotVbillstatus.indexOf(',') != -1">
                and lot.vbillstatus in
                <foreach item="item" index="index" collection="lotVbillstatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="lotVbillstatus != null and lotVbillstatus.trim() != '' and lotVbillstatus.indexOf(',') == -1">
                and lot.vbillstatus = #{lotVbillstatus}
            </if>
            <!--承运商名称-->
            <if test="carrierName != null and carrierName != ''">
                <bind name="carrierName" value="carrierName + '%'"/>
                <!--and t1.carr_name like  #{carrierName}-->
                and lot.carrier_name like  #{carrierName}
            </if>
            <!--承运商类别-->
            <if test="carrType != null and carrType != ''">
                and t4.carr_type =  #{carrType}
            </if>
            <if test="ifAllConfirm != null ">
                and lot.if_All_Confirm =  #{ifAllConfirm}
            </if>
            <if test="ifAllReceipt != null ">
                and lot.if_All_Receipt =  #{ifAllReceipt}
            </if>
            <if test="ifAllReceiptUpload != null ">
                and lot.if_All_Receipt_Upload =  #{ifAllReceiptUpload}
            </if>
            <if test="carrCode != null and carrCode != ''">
                and t4.carr_code =  #{carrCode}
            </if>
            <if test="carrBalaType != null and carrBalaType != ''">
                and t4.bala_type =  #{carrBalaType}
            </if>

            <!--要求提货日期-->
            <if test="params.reqDeliDateStart != null  and params.reqDeliDateStart != ''">
                <!--and to_char(lot.req_deli_date,'yyyy-mm-dd') <![CDATA[ >= ]]> #{params.reqDeliDateStart}-->
                and lot.req_deli_date <![CDATA[ >= ]]> to_date(#{params.reqDeliDateStart},'YYYY-MM-DD')
            </if>
            <if test="params.reqDeliDateEnd != null  and params.reqDeliDateEnd != ''">
                <!--and to_char(lot.req_deli_date,'yyyy-mm-dd') <![CDATA[ <= ]]> #{params.reqDeliDateEnd}-->
                and lot.req_deli_date <![CDATA[ < ]]> to_date(#{params.reqDeliDateEnd},'YYYY-MM-DD') + 1
            </if>
            <!--提货省市区-->
            <if test="deliProvince != null  and deliProvince != '' "> and lot.deli_province = #{deliProvince}</if>
            <if test="deliCity != null  and deliCity != '' "> and lot.deli_city = #{deliCity}</if>
            <if test="deliArea != null  and deliArea != '' "> and lot.deli_area = #{deliArea}</if>
            <!--到货省市区-->
            <if test="arriProvince != null  and arriProvince != '' "> and lot.arri_province = #{arriProvince}</if>
            <if test="arriCity != null  and arriCity != '' "> and lot.arri_city = #{arriCity}</if>
            <if test="arriArea != null  and arriArea != '' "> and lot.arri_area = #{arriArea}</if>
            and exists (select 1 from t_entrust entrust
            left join m_customer cust on cust.customer_id = entrust.customer_id
            where entrust.lot_id = lot.entrust_lot_id ${params.dataScope})
        </where>
        ) t
        <where>
            <!--客户简称-->
            <if test="custAbbr != null and custAbbr != ''">
                <bind name="custAbbr" value="custAbbr + '%'"/>
                t.custAbbr like  #{custAbbr}
            </if>
            <if test="goodsName != null and goodsName != ''">
                <bind name="goodsName" value="'%'+ goodsName + '%'"/>
                t.goodsName like  #{goodsName}
            </if>

        </where>
    </select>

    <select id="selectPayReconciliationListAmountCountForCheck" resultType="java.util.Map">
        select
        sum(nvl(t1.receiptamountfreight,0)) receiptAmountFreight,  <!--运费-->
        sum(nvl(t1.receiptamountonway,0)) receiptAmountOnWay,   <!-- 在途-->
        sum(nvl(t1.oilCardAmount,0)) oilCardAmount,   <!-- 油卡总金额-->
        sum(t1.sumTransFeeCount) sumTransFeeCount,
        sum(t1.sumGotAmount) sumGotAmount,
        sum(t1.sumUngotAmount) sumUngotAmount
        from t_entrust_lot lot
        left join (
        select
        pay.lotno,
        nvl(sum(pay.Trans_Fee_Count), 0) receiptAmount,
        nvl(sum(case when pay.free_type = 0 then pay.Trans_Fee_Count end), 0) receiptAmountFreight,
        nvl(sum(case when pay.free_type = 1 then pay.Trans_Fee_Count end), 0) receiptAmountOnWay,
        nvl(sum(case when pay.cost_type_freight in (1,3,5) then pay.Trans_Fee_Count end), 0) oilCardAmount,
        sum(pay.trans_fee_count) sumTransFeeCount,
        sum(pay.got_amount) sumGotAmount,
        sum(pay.UNGOT_AMOUNT) sumUngotAmount
        from t_entrust_lot lot
        left join t_pay_detail pay
        on lot.lot = pay.lotno
        and pay.del_flag = 0
        group by pay.lotno) t1
        on lot.lot = t1.lotno
        <where>
            lot.ENTRUST_LOT_ID in (
            select lot_id from t_pay_detail
            where PAY_DETAIL_ID in (
            select  PAY_DETAIL_ID from t_pay_check_sheet_b
            where pay_check_sheet_id  = #{payCheckSheetId}
            )
            )
            and
            lot.del_flag = 0
            <!--运单号-->
            <if test="lot != null and lot != ''">
                <bind name="lot" value="lot + '%'"/>
                and lot.lot like  #{lot}
            </if>
            <!--运单状态-->
            <if test="lotVbillstatus != null  and lotVbillstatus != '' and lotVbillstatus.indexOf(',') != -1">
                and lot.vbillstatus in
                <foreach item="item" index="index" collection="lotVbillstatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="lotVbillstatus != null and lotVbillstatus.trim() != '' and lotVbillstatus.indexOf(',') == -1">
                and lot.vbillstatus = #{lotVbillstatus}
            </if>
            <!--承运商名称-->
            <if test="carrierName != null and carrierName != ''">
                <bind name="carrierName" value="carrierName + '%'"/>
                and lot.carrier_name like  #{carrierName}
            </if>
        </where>
    </select>

    <select id="selectPayCheckSheetBList" resultType="PayCheckSheetB">
        select
            pay_check_sheet_b_id payCheckSheetBId,
            pay_check_sheet_id payCheckSheetId,
            pay_detail_id payDetailId
        from t_pay_check_sheet_b
        <where>
            del_falg = 0
            <if test="payCheckSheetBId != null and payCheckSheetBId != '' "> and pay_check_sheet_b_id = #{payCheckSheetBId}</if>
            <if test="payCheckSheetId != null and payCheckSheetId != '' "> and pay_check_sheet_id = #{payCheckSheetId}</if>
            <if test="payDetailId != null and payDetailId != '' "> and pay_detail_id = #{payDetailId}</if>
        </where>
    </select>
    <select id="selectPayDetailListByOilCardNumber" resultMap="PayDetailResult">
        <include refid="selectTPayDetailVo"/>
        where oil_card_number = #{oilCardNumber}
        and pay_detail_id != #{payDetailId}
        <!--没有付款-->
        and vbillstatus in (0,1,2,5,6,8,9)
    </select>

    <update id="cancelReview">
        update t_pay_detail
          set
            check_status = 3,<!--3：反复核-->
            check_user_id = #{checkUserId,jdbcType=VARCHAR},
            check_user_name = #{checkUserName,jdbcType=VARCHAR},
            check_memo = #{checkMemo,jdbcType=VARCHAR},
            check_date = #{checkDate,jdbcType=VARCHAR},
            vbillstatus = #{vbillstatus},
            cor_scr_id = 'payDetail_cancelReview'
          where pay_detail_id = #{payDetailId}
            and vbillstatus = 9 <!--9：已复核-->
    </update>
    
    <select id="selectPayDetailNotSheet" resultType="java.lang.Integer">
        select count(1) from T_PAY_DETAIL
        where CARRIER_ID = #{carrierId}
        and DEL_FLAG = 0
        and PAY_DETAIL_ID not in (select T_PAY_CHECK_SHEET_B.PAY_DETAIL_ID from T_PAY_CHECK_SHEET_B)
        and VBILLSTATUS in(0,1)
        and TRANS_FEE_COUNT > 0
    </select>

    <update id="saveInvoiceCheck">
        update T_ADJUST_RECORD set
        <if test="checkStatus == 1">
           ADJUST_CHECK_STATUS = 1,
        </if>
        <if test="checkStatus == 2">
            ADJUST_CHECK_STATUS = 4,
        </if>
        FIRST_CHECK_MEMO = #{firstCheckMemo,jdbcType=VARCHAR},
        FIRST_CHECK_DATE = sysdate,
        FIRST_CHECK_USER_ID = #{firstCheckUserId,jdbcType=VARCHAR},
        FIRST_CHECK_USER_NAME = #{firstCheckUserName,jdbcType=VARCHAR}
        where ADJUST_RECORD_ID = #{adjustRecordId}
    </update>

    <update id="saveLeaderCheck">
        update T_ADJUST_RECORD set
        <if test="checkStatus == 1">
            ADJUST_CHECK_STATUS = 2,
        </if>
        <if test="checkStatus == 2">
            ADJUST_CHECK_STATUS = 4,
        </if>
        SECOND_CHECK_MEMO = #{secondCheckMemo,jdbcType=VARCHAR},
        SECOND_CHECK_DATE = sysdate,
        SECOND_CHECK_USER_ID = #{secondCheckUserId,jdbcType=VARCHAR},
        SECOND_CHECK_USER_NAME = #{secondCheckUserName,jdbcType=VARCHAR}
        where ADJUST_RECORD_ID = #{adjustRecordId}
    </update>

    <update id="saveFinanceCheck">
        update T_ADJUST_RECORD set
        <if test="checkStatus == 1">
            ADJUST_CHECK_STATUS = 3,
        </if>
        <if test="checkStatus == 2">
            ADJUST_CHECK_STATUS = 4,
        </if>
        Third_CHECK_MEMO = #{thirdCheckMemo,jdbcType=VARCHAR},
        Third_CHECK_DATE = sysdate,
        Third_CHECK_USER_ID = #{thirdCheckUserId,jdbcType=VARCHAR},
        Third_CHECK_USER_NAME = #{thirdCheckUserName,jdbcType=VARCHAR}
        where ADJUST_RECORD_ID = #{adjustRecordId}
    </update>

    <select id="selectPayDetailListByAdjustRecordId" resultType="PayDetail">
          select
               pay_detail_check_id payDetailId,
               vbillno,
               vbillstatus,
               free_type freeType,
               cost_type_on_way costTypeOnWay,
               cost_type_freight costTypeFreight,
               lot_id lotId,
               lotno,
               carrier_id carrierId,
               carr_code carrCode,
               carr_name carrName,
               bala_corp balaCorp,
               balatype,
               trans_fee_count transFeeCount,
               got_amount gotAmount,
               ungot_amount ungotAmount,
               oil_card_number oilCardNumber,
               check_no checkNo,
               check_head checkHead,
               unconfirm_type unconfirmType,
               unconfirm_memo unconfirmMemo,
               confirm_time confirmTime,
               confirm_user confirmUser,
               driver_mobile driverMobile,
               driver_name driverName,
               carno,
               req_deli_date reqDeliDate,
               req_arri_date reqArriDate,
               is_close isClose,
               is_adjust isAdjust,
               adjust_memo adjustMemo,
               memo memo,
               reg_user_id regUserId,
               reg_date regDate,
               reg_scr_id regScrId,
               cor_user_id corUserId,
               cor_date corDate,
               cor_scr_id corScrId,
               del_flag delFlag,
               del_date delDate,
               del_user_id delUserId,
               rec_card_no recCardNo,
               rec_account recAccount,
               rec_bank recBank,
               is_ntocc isNtocc,
               apply_user applyUser,
               apply_time applyTime,
                is_fleet_data isFleetData,
                is_fleet_assign isFleetAssign,
                fleet_receive_detail_id fleetReceiveDetailId,
                 billing_type billingType
        from T_PAY_DETAIL_CHECK
        where ADJUST_RECORD_ID = #{adjustRecordId}
    </select>

    <select id="selectPayDetailListSumByAdjustRecordId" resultType="PayDetail">
        select a.free_type freeType,
               a.cost_type_on_way costTypeOnWay,
               a.cost_type_freight costTypeFreight,
               sum(a.TRANS_FEE_COUNT) transFeeCount,
               nvl(nvl(a.billing_type,b.billing_type),'6') lotBillingType
        from T_PAY_DETAIL_CHECK a
            left join t_entrust_lot b on b.entrust_lot_id = a.lot_id
        where a.ADJUST_RECORD_ID = #{adjustRecordId}
        group by a.FREE_TYPE,
                 a.COST_TYPE_ON_WAY,
                 a.COST_TYPE_FREIGHT,
                 nvl(nvl(a.billing_type,b.billing_type),'6')
    </select>

    <select id="selectAdjustCheckRecord" resultType="payDetailCheck">
        select
        FIRST_CHECK_USER_NAME firstCheckUserName,
        FIRST_CHECK_DATE firstCheckDate,
        FIRST_CHECK_MEMO firstCheckMemo,
        SECOND_CHECK_USER_NAME secondCheckUserName,
        SECOND_CHECK_DATE secondCheckDate,
        SECOND_CHECK_MEMO secondCheckMemo,
        THIRD_CHECK_USER_NAME thirdCheckUserName,
        THIRD_CHECK_DATE thirdCheckDate,
        THIRD_CHECK_MEMO thirdCheckMemo
        from T_PAY_DETAIL_CHECK
        where LOTNO = #{lot}
    </select>
    <select id="selectPayDetailOverList" resultType="com.ruoyi.tms.vo.finance.PayDetailOverVO">
        select
            b.carr_code carrCode,
            b.carr_name carrName,
            a.total_amount,
            a.got_amount gotAmount,
            a.confirmed_amount+a.unconfirmed_amount ungotAmount,
            a.confirmed_amount confirmedAmount,
            a.unconfirmed_amount unconfirmedAmount
        from (select sum(p.trans_fee_count+nvl(p.TAX_AMOUNT,0)) total_amount,
                sum(p.got_amount) got_amount,
                sum(p.ungot_amount) ungot_amount,
                nvl(sum(case when p.vbillstatus in (1, 2, 3, 4, 6, 7, 8, 9) then p.ungot_amount end),0) confirmed_amount,
                nvl(sum(case when p.vbillstatus = 0 then p.trans_fee_count end), 0) unconfirmed_amount,
                p.carrier_id
            from t_pay_detail p
                left join t_entrust_lot l on p.lot_id = l.entrust_lot_id
            where p.del_flag = 0 and l.del_flag = 0  AND l.reg_scr_id != 'importAllData'
                <if test="params.reqDeliDateStart != null  and params.reqDeliDateStart != ''">
                    and to_char(l.req_deli_date,'yyyy-mm-dd') <![CDATA[ >= ]]> #{params.reqDeliDateStart}
                </if>
                <if test="params.reqDeliDateEnd != null  and params.reqDeliDateEnd != ''">
                    and to_char(l.req_deli_date,'yyyy-mm-dd') <![CDATA[ <= ]]> #{params.reqDeliDateEnd}
                </if>
            group by p.carrier_id) a
        left join m_carrier b on a.carrier_id = b.carrier_id
        where b.del_flag = 0
            <if test="params.showAll != null  and params.showAll == 0">
                and (a.confirmed_amount+a.unconfirmed_amount != 0)
            </if>
            <if test="carrName != null and carrName != '' ">
                <bind name="carrName" value="carrName + '%'"></bind>
                and b.carr_name like #{carrName}
            </if>
    </select>
    <select id="listPayDetailIdByVbillno" resultType="java.lang.String">
        select
          PAY_DETAIL_ID
          FROM t_pay_detail
        where vbillno = #{vbillno}
        and del_flag = 0
    </select>

    <select id="selectPayDetailListByPayCheckSheetId" resultMap="PayDetailResult">
        select
        t_pay_detail.pay_detail_id,
        t_pay_detail.vbillno,
        t_pay_detail.vbillstatus,
        t_pay_detail.free_type,
        t_pay_detail.cost_type_on_way,
        t_pay_detail.cost_type_freight,
        t_pay_detail.lot_id,
        t_pay_detail.lotno,
        t_pay_detail.carrier_id,
        t_pay_detail.carr_code,
        t_pay_detail.carr_name,
        t_pay_detail.bala_corp,
        t_pay_detail.balatype,
        t_pay_detail.trans_fee_count,
        nvl(t_pay_detail.got_amount,0) got_amount,
        nvl(t_pay_detail.ungot_amount,0) ungot_amount,
        t_pay_detail.oil_card_number,
        t_pay_detail.check_no,
        t_pay_detail.check_head,
        t_pay_detail.unconfirm_type,
        t_pay_detail.unconfirm_memo,
        t_pay_detail.confirm_time,
        t_pay_detail.confirm_user,
        t_pay_detail.driver_mobile,
        t_pay_detail.driver_name,
        t_pay_detail.carno,
        t_pay_detail.req_deli_date,
        t_pay_detail.req_arri_date,
        t_pay_detail.is_close,
        t_pay_detail.is_adjust,
        t_pay_detail.adjust_memo,
        t_pay_detail.memo,
        t_pay_detail.reg_user_id,
        t_pay_detail.reg_date,
        t_pay_detail.reg_scr_id,
        t_pay_detail.cor_user_id,
        t_pay_detail.cor_date,
        t_pay_detail.cor_scr_id,
        t_pay_detail.del_flag,
        t_pay_detail.del_date,
        t_pay_detail.del_user_id,
        t_pay_detail.rec_card_no,
        t_pay_detail.rec_account,
        t_pay_detail.rec_bank,
        t_pay_detail.is_ntocc,
        t_pay_detail.apply_user,
        t_pay_detail.apply_time,
        t_pay_detail.tax_amount
        from t_pay_detail
        join T_PAY_CHECK_SHEET_B on t_pay_detail.PAY_DETAIL_ID = T_PAY_CHECK_SHEET_B.PAY_DETAIL_ID
        where T_PAY_CHECK_SHEET_B.PAY_CHECK_SHEET_ID = #{payCheckSheetId}
        and t_pay_detail.DEL_FLAG = 0
    </select>

    <select id="selectPayDetailListByPayCheckSheetIdCash" resultMap="PayDetailResult">
        select
        t_pay_detail.pay_detail_id,
        t_pay_detail.vbillno,
        t_pay_detail.vbillstatus,
        t_pay_detail.free_type,
        t_pay_detail.cost_type_on_way,
        t_pay_detail.cost_type_freight,
        t_pay_detail.lot_id,
        t_pay_detail.lotno,
        t_pay_detail.carrier_id,
        t_pay_detail.carr_code,
        t_pay_detail.carr_name,
        t_pay_detail.bala_corp,
        t_pay_detail.balatype,
        t_pay_detail.trans_fee_count,
        nvl(t_pay_detail.got_amount,0) got_amount,
        nvl(t_pay_detail.ungot_amount,0) ungot_amount,
        t_pay_detail.oil_card_number,
        t_pay_detail.check_no,
        t_pay_detail.check_head,
        t_pay_detail.unconfirm_type,
        t_pay_detail.unconfirm_memo,
        t_pay_detail.confirm_time,
        t_pay_detail.confirm_user,
        t_pay_detail.driver_mobile,
        t_pay_detail.driver_name,
        t_pay_detail.carno,
        t_pay_detail.req_deli_date,
        t_pay_detail.req_arri_date,
        t_pay_detail.is_close,
        t_pay_detail.is_adjust,
        t_pay_detail.adjust_memo,
        t_pay_detail.memo,
        t_pay_detail.reg_user_id,
        t_pay_detail.reg_date,
        t_pay_detail.reg_scr_id,
        t_pay_detail.cor_user_id,
        t_pay_detail.cor_date,
        t_pay_detail.cor_scr_id,
        t_pay_detail.del_flag,
        t_pay_detail.del_date,
        t_pay_detail.del_user_id,
        t_pay_detail.rec_card_no,
        t_pay_detail.rec_account,
        t_pay_detail.rec_bank,
        t_pay_detail.is_ntocc,
        t_pay_detail.apply_user,
        t_pay_detail.apply_time,
        t_pay_detail.tax_amount
        from t_pay_detail
        join T_PAY_CHECK_SHEET_B on t_pay_detail.PAY_DETAIL_ID = T_PAY_CHECK_SHEET_B.PAY_DETAIL_ID
        where T_PAY_CHECK_SHEET_B.PAY_CHECK_SHEET_ID = #{payCheckSheetId}
        and t_pay_detail.DEL_FLAG = 0
        and t_pay_detail.COST_TYPE_FREIGHT in (0,2,4)
        <if test="ungotFlag !=null and ungotFlag !=''">
            and t_pay_detail.UNGOT_AMOUNT &gt; 0
        </if>
    </select>


    <select id="selectPayDetailListByPayCheckSheetIdOil" resultMap="PayDetailResult">
        select
        t_pay_detail.pay_detail_id,
        t_pay_detail.vbillno,
        t_pay_detail.vbillstatus,
        t_pay_detail.free_type,
        t_pay_detail.cost_type_on_way,
        t_pay_detail.cost_type_freight,
        t_pay_detail.lot_id,
        t_pay_detail.lotno,
        t_pay_detail.carrier_id,
        t_pay_detail.carr_code,
        t_pay_detail.carr_name,
        t_pay_detail.bala_corp,
        t_pay_detail.balatype,
        t_pay_detail.trans_fee_count,
        nvl(t_pay_detail.got_amount,0) got_amount,
        nvl(t_pay_detail.ungot_amount,0) ungot_amount,
        t_pay_detail.oil_card_number,
        t_pay_detail.check_no,
        t_pay_detail.check_head,
        t_pay_detail.unconfirm_type,
        t_pay_detail.unconfirm_memo,
        t_pay_detail.confirm_time,
        t_pay_detail.confirm_user,
        t_pay_detail.driver_mobile,
        t_pay_detail.driver_name,
        t_pay_detail.carno,
        t_pay_detail.req_deli_date,
        t_pay_detail.req_arri_date,
        t_pay_detail.is_close,
        t_pay_detail.is_adjust,
        t_pay_detail.adjust_memo,
        t_pay_detail.memo,
        t_pay_detail.reg_user_id,
        t_pay_detail.reg_date,
        t_pay_detail.reg_scr_id,
        t_pay_detail.cor_user_id,
        t_pay_detail.cor_date,
        t_pay_detail.cor_scr_id,
        t_pay_detail.del_flag,
        t_pay_detail.del_date,
        t_pay_detail.del_user_id,
        t_pay_detail.rec_card_no,
        t_pay_detail.rec_account,
        t_pay_detail.rec_bank,
        t_pay_detail.is_ntocc,
        t_pay_detail.apply_user,
        t_pay_detail.apply_time,
        t_pay_detail.tax_amount
        from t_pay_detail
        join T_PAY_CHECK_SHEET_B on t_pay_detail.PAY_DETAIL_ID = T_PAY_CHECK_SHEET_B.PAY_DETAIL_ID
        where T_PAY_CHECK_SHEET_B.PAY_CHECK_SHEET_ID = #{payCheckSheetId}
        and t_pay_detail.DEL_FLAG = 0
        and t_pay_detail.COST_TYPE_FREIGHT in (1,3,5)
        <if test="ungotFlag !=null and ungotFlag !=''">
            and t_pay_detail.UNGOT_AMOUNT &gt; 0
        </if>
    </select>

    <update id="updatePayDetailTaxAmount">
        update T_PAY_DETAIL
        set TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL},
        <if test="vbillstatus != null  ">vbillstatus = #{vbillstatus},</if>
        UNGOT_AMOUNT = #{ungotAmount,jdbcType=DECIMAL},
        COR_SCR_ID = #{corScrId,jdbcType=VARCHAR},
        COR_DATE = sysdate,
        COR_USER_ID = #{corUserId,jdbcType=VARCHAR}
        where PAY_DETAIL_ID = #{payDetailId,jdbcType=VARCHAR}
    </update>

    <update id="updatePayDetailTaxRevoke">
        update T_PAY_DETAIL set
        VBILLSTATUS = 2,
        UNGOT_AMOUNT = UNGOT_AMOUNT-nvl(TAX_AMOUNT,0),
        TAX_AMOUNT = null,
        COR_SCR_ID = #{corScrId,jdbcType=VARCHAR},
        COR_DATE = sysdate,
        COR_USER_ID = #{corUserId,jdbcType=VARCHAR}
        where PAY_DETAIL_ID = #{payDetailId}
    </update>

    <update id="updatePayDetailTaxRevokeAll">
        update T_PAY_DETAIL set
        UNGOT_AMOUNT = UNGOT_AMOUNT-nvl(TAX_AMOUNT,0),
        TAX_AMOUNT = null,
        COR_SCR_ID = #{corScrId,jdbcType=VARCHAR},
        COR_DATE = sysdate,
        COR_USER_ID = #{corUserId,jdbcType=VARCHAR}
        where PAY_DETAIL_ID in( select dtl.PAY_DETAIL_ID   from t_pay_detail dtl
        join T_PAY_CHECK_SHEET_B on dtl.PAY_DETAIL_ID = T_PAY_CHECK_SHEET_B.PAY_DETAIL_ID
        where T_PAY_CHECK_SHEET_B.PAY_CHECK_SHEET_ID = #{payCheckSheetId})
    </update>

    <select id="selectPayDetailUncheckCntByLot" resultType="java.lang.Integer">
        select count(1) from T_PAY_DETAIL_CHECK payCheck
        join T_ADJUST_RECORD record on payCheck.ADJUST_RECORD_ID = record.ADJUST_RECORD_ID
        where record.ADJUST_CHECK_STATUS in(0,1,2) and payCheck.LOTNO = #{lot}
    </select>

    <select id="selectPayDetailUncheckInfoByLot" resultType="com.ruoyi.tms.domain.finance.AdjustRecordVO">
        select * from (
select  record.adjust_Check_Status adjustCheckStatus,
                record.FIRST_CHECK_USER_NAME firstCheckUserName,
                to_char(record.FIRST_CHECK_DATE,'yyyy-mm-dd hh24:mi:ss') firstCheckDate,
                record.FIRST_CHECK_MEMO firstCheckMemo,
                record.SECOND_CHECK_USER_NAME secondCheckUserName,
                to_char(record.SECOND_CHECK_DATE,'yyyy-mm-dd hh24:mi:ss') secondCheckDate,
                record.SECOND_CHECK_MEMO secondCheckMemo,
                record.THIRD_CHECK_USER_NAME thirdCheckUserName,
                to_char(record.THIRD_CHECK_DATE,'yyyy-mm-dd hh24:mi:ss') thirdCheckDate,
                record.THIRD_CHECK_MEMO thirdCheckMemo,
                record.reg_user_name adjustUserName,
        to_char(record.reg_date,'yyyy-mm-dd hh24:mi:ss') adjustDate,
                payCheck.trans_fee_count adjustAmount,
        record.sp_no spNo
        from T_PAY_DETAIL_CHECK payCheck
        join T_ADJUST_RECORD record on payCheck.ADJUST_RECORD_ID = record.ADJUST_RECORD_ID
        where  payCheck.LOTNO = #{lot}  order by record.reg_date desc
                      ) where rownum = 1
    </select>

    <select id="selectOtherFeeUncheckCntByInvoiceNo" resultType="java.lang.Integer">
        select count(1) from T_OTHER_FEE_CHECK payCheck
        join T_ADJUST_RECORD record on payCheck.ADJUST_RECORD_ID = record.ADJUST_RECORD_ID
        where record.ADJUST_CHECK_STATUS in(0,1,2) and payCheck.LOTNO = #{lot}
    </select>

    <select id="selectOtherFeeUncheckInfoByInvoiceNo" resultType="com.ruoyi.tms.domain.finance.AdjustRecordVO">
        select * from (
select  record.adjust_Check_Status adjustCheckStatus,
                record.FIRST_CHECK_USER_NAME firstCheckUserName,
                to_char(record.FIRST_CHECK_DATE,'yyyy-mm-dd hh24:mi:ss') firstCheckDate,
                record.FIRST_CHECK_MEMO firstCheckMemo,
                record.SECOND_CHECK_USER_NAME secondCheckUserName,
                to_char(record.SECOND_CHECK_DATE,'yyyy-mm-dd hh24:mi:ss') secondCheckDate,
                record.SECOND_CHECK_MEMO secondCheckMemo,
                record.THIRD_CHECK_USER_NAME thirdCheckUserName,
                to_char(record.THIRD_CHECK_DATE,'yyyy-mm-dd hh24:mi:ss') thirdCheckDate,
                record.THIRD_CHECK_MEMO thirdCheckMemo,
                record.reg_user_name adjustUserName,
                to_char(record.reg_date,'yyyy-mm-dd hh24:mi:ss') adjustDate,
                payCheck.fee_amount adjustAmount,
            record.sp_no spNo
        from T_OTHER_FEE_CHECK payCheck
                                 join T_ADJUST_RECORD record on payCheck.ADJUST_RECORD_ID = record.ADJUST_RECORD_ID
        where  payCheck.LOTNO = #{lot}  order by record.reg_date desc
                      ) where rownum = 1
    </select>

    <select id="selectReceiveDetailUncheckCntByInvoiceNo" resultType="java.lang.Integer">
        select count(1) from T_RECEIVE_DETAIL_CHECK payCheck
        join T_ADJUST_RECORD record on payCheck.ADJUST_RECORD_ID = record.ADJUST_RECORD_ID
        where record.ADJUST_CHECK_STATUS in(0,1,2) and payCheck.INVOICE_VBILLNO = #{invoiceNo}
    </select>

    <select id="selectReceiveDetailUncheckInfoByInvoiceNo" resultType="com.ruoyi.tms.domain.finance.AdjustRecordVO">
        select * from (
       select record.adjust_Check_Status adjustCheckStatus,
               record.FIRST_CHECK_USER_NAME firstCheckUserName,
               to_char(record.FIRST_CHECK_DATE,'yyyy-mm-dd hh24:mi:ss') firstCheckDate,
               record.FIRST_CHECK_MEMO firstCheckMemo,
               record.SECOND_CHECK_USER_NAME secondCheckUserName,
               to_char(record.SECOND_CHECK_DATE,'yyyy-mm-dd hh24:mi:ss') secondCheckDate,
               record.SECOND_CHECK_MEMO secondCheckMemo,
               record.THIRD_CHECK_USER_NAME thirdCheckUserName,
               to_char(record.THIRD_CHECK_DATE,'yyyy-mm-dd hh24:mi:ss') thirdCheckDate,
               record.THIRD_CHECK_MEMO thirdCheckMemo,
               record.reg_user_name adjustUserName,
               to_char(record.reg_date,'yyyy-mm-dd hh24:mi:ss') adjustDate,
               payCheck.trans_fee_count adjustAmount,
               record.sp_no spNo
         from T_RECEIVE_DETAIL_CHECK payCheck
         join T_ADJUST_RECORD record on payCheck.ADJUST_RECORD_ID = record.ADJUST_RECORD_ID
        where payCheck.INVOICE_VBILLNO = #{invoiceNo}
        order by record.reg_date desc ) where rownum = 1
    </select>

    <insert id="insertPayBackRecord">
        insert into T_PAY_BACK_RECORD
        (id, pay_detail_id, pay_back_type, pay_back_memo, tid, reg_user_id, reg_date, reg_scr_id,pay_check_sheet_id,back_person,pay_Sheet_Record_Id
        <if test="dataType!=null">,data_type</if>)
        values
        (#{id},#{payDetailId,jdbcType=VARCHAR},#{payBackType},#{payBackMemo},#{tid},#{regUserId},#{regDate},#{regScrId},#{payCheckSheetId,jdbcType=VARCHAR},#{backPerson},#{paySheetRecordId,jdbcType=VARCHAR}
        <if test="dataType!=null">,#{dataType}</if>)
    </insert>

    <update id="updatePayDetailRegById">
        update t_pay_detail
        set reg_date = #{regDate,jdbcType=DATE},
            reg_user_id = #{regUserId,jdbcType=VARCHAR}
        where pay_detail_id = #{payDetailId}
    </update>

    <select id="queryG7LotInfoByPaydetailId" parameterType="String" resultType="Map">
        select c.driver_id,
               c.card_id,
               c.driver_name,
               b.g7_syn,
               e.billing_corp, <!-- G7结算公司 -->
               case e.billing_corp when 'MY' then c.g7_syn_my when 'JH' then c.g7_syn_jh when 'DH' then c.g7_syn_dh when 'DW' then c.g7_syn_dw end g7_driver_syn,
               case e.billing_corp when 'MY' then c.G7_CONTRACT_SIGNED_MY when 'JH' then c.G7_CONTRACT_SIGNED_JH when 'DH' then c.G7_CONTRACT_SIGNED_DH when 'DW' then c.G7_CONTRACT_SIGNED_DW end g7_driver_sign,
               b.lot
        from t_pay_detail a
                 LEFT JOIN t_entrust_lot b on a.lot_id = b.entrust_lot_id
                 LEFT JOIN m_driver c on c.driver_id = b.driver_id
                 LEFT JOIN M_CAR d ON d.CAR_ID = b.CARNO_ID
                 LEFT JOIN (SELECT e.*, ROW_NUMBER() OVER (PARTITION BY e.lot_id ORDER BY e.entrust_id) AS rn FROM t_entrust e where e.del_flag = 0) e on e.lot_id = a.lot_id and e.rn = 1
        where a.pay_detail_id = #{payDetailId}
            and b.TRANS_TYPE not in ('15', '16')
            and b.CARNO_ID is not null
            and b.ltl_type = 1
            and (b.pay_way is null or b.pay_way = 'G7')
            and (d.carrierid is null or (d.carrierid != '21228416176d4dbca8d3f1707a218e15' and d.carrierid != '0e3181c8acdc46c194b3308a79b1ca77'))
            <!--and not exists (
                select s.entrust_id from T_ENTRUST s where s.DEL_FLAG = 0 and s.LOT_ID = b.LOT_ID and s.entrust_id != b.entrust_id
            )-->
            and (select count(distinct billing_corp) from t_entrust x where x.del_flag = 0 and x.lot_id = a.lot_id) = 1
            and b.reg_date > to_date('2021-09-01','YYYY-MM-DD')
            and b.g7_ignore is null
    </select>

    <insert id="saveG7PayRecord">
        insert into t_pay_record_g7 (
            ID,STATUS,AMOUNT,REG_DATE,REG_SCR_ID,REG_USER_ID,PAYEE,OUT_ACCOUNT,billing_corp,lot_id,remark,pay_type,pay_sheet_record_id
        ) values (
            #{id},#{status},#{amount},#{regDate},#{regScrId},#{regUserId},#{payee},#{outAccount},#{billingCorp},#{lotId},#{remark,jdbcType=VARCHAR},#{payType},#{paySheetRecordId,jdbcType=VARCHAR}
        )
    </insert>
    <update id="updateG7PayRecordAmount">
        update t_pay_record_g7 set amount = #{amount} where id = #{id}
    </update>

    <insert id="saveG7PayRecordDtl">
        insert into t_pay_record_g7_dtl (
            PAY_RECORD_G7_ID,PAY_DETAIL_ID,PER_AMOUNT,VBILLSTATUS
        ) values (
            #{record_g7_id},#{pay_detail_id},#{perAmount},#{vbillstatus}
        )
    </insert>

    <!--<update id="setG7PayMan">
        update t_pay_record_g7 set pay_man = #{payMan},pay_time = #{payTime} where id = #{record_id}
    </update>-->

    <select id="listG7WaitingPay" resultType="Map">
        select a.id,
               a.status,
               a.amount,
               a.reg_date,
               a.payee,
               a.pay_man,
               a.out_account,
               a.billing_corp,
               a.lot_id,
               (
                   select listagg(t3.vbillno, ',') within
        group (order by t3.reg_date)
        from t_pay_record_g7_dtl t2
            left join t_pay_detail t3
        on t3.PAY_DETAIL_ID = t2.PAY_DETAIL_ID
        where t2.PAY_RECORD_G7_ID = a.id
            ) as vbillno,
            b.user_name reg_user_name
        from t_pay_record_g7 a
            left join sys_user b
        on b.user_id = a.reg_user_id
        where a.status = 1 and a.pay_type = 1
            <if test='corp != null and corp !=""'>
                and billing_corp = #{corp}
            </if>
            <if test='vbillno != null and vbillno != ""'>
                <bind name="vbillnoLike" value="'%' + vbillno + '%'"/>
                and a.id in (
                    select x.PAY_RECORD_G7_ID from t_pay_record_g7_dtl x
                    inner join t_pay_record_g7 z on x.PAY_RECORD_G7_ID = z.id and z.status = 1 and  z.pay_type = 1
                    left join t_pay_detail y on x.pay_detail_id = y.pay_detail_id
                    where y.vbillno like #{vbillnoLike}
                )
            </if>
        order by a.reg_date desc
    </select>

    <update id="setG7PayStatusByLotIds">
        update t_pay_record_g7 set pay_man = #{payMan},pay_time = #{payTime},status = #{status}
        where lot_id in
            <foreach collection="lotIds" item="lotId" separator=",">#{lotId}</foreach> and status = 1
    </update>

    <select id="listWaitingPayLotId" resultType="String">
        select distinct lot_id from t_pay_record_g7 where billing_corp = #{corp} and status = 1 and pay_type = 1
    </select>

    <select id="listG7StatusByPayDetailIds" resultMap="PayDetailResult">
        select t.pay_detail_id,
               t.vbillno,
               t.cost_type_freight,
               t.free_type,
               t.lot_id,
               t2.g7_syn lot_g7_syn,
               t2.g7_start lot_g7_start,
               t2.g7_end lot_g7_end,
               t.bala_corp,
               t.g7_pay,
               t.carr_bank_id,
               t.carrier_id,
               t.batch_no
        from t_pay_detail t
        left join t_entrust_lot t2 on t2.entrust_lot_id = t.lot_id
        where t.pay_detail_id in (<foreach collection="payDetailIdArr" separator="," item="id">#{id}</foreach>)
    </select>

    <select id="getVbillstatusForUpdate" resultMap="PayDetailResult">
        select pay_detail_id,vbillstatus from t_pay_detail where pay_detail_id = #{payDetailId} for update
    </select>

    <select id="getPayRecordG7Dtl" resultType="Map">
        select a.pay_record_g7_id, a.pay_detail_id, a.vbillstatus, b.lot_id, b.billing_corp
        from t_pay_record_g7_dtl a
        left join t_pay_record_g7 b on a.pay_record_g7_id = b.id
        where a.pay_record_g7_id in (
        <foreach collection="ids" separator="," item="id">#{id}</foreach>
        )
        and b.status = 1
    </select>

    <delete id="deletePayRecordG7">
        delete from t_pay_record_g7 where id in (
            <foreach collection="ids" separator="," item="id">#{id}</foreach>
        )
    </delete>

    <delete id="deletePayRecordG7Dtl">
        delete from t_pay_record_g7_dtl where pay_record_g7_id in (
        <foreach collection="ids" separator="," item="id">#{id}</foreach>
        )
    </delete>


    <update id="restoreVbillstatus">
        update t_pay_detail
        set
            <choose>
                <when test="vbillstatus == null">
                    vbillstatus = case when got_amount > 0 then 3 else 9 end
                </when>
                <otherwise>
                    vbillstatus = #{vbillstatus}
                </otherwise>
            </choose>
        where pay_detail_id = #{payDetailId}
    </update>

    <select id="tms_exist" resultType="Integer">
        <!--select (select count(1) from t_pay_detail where pay_detail_id = #{id}) +
               (select count(1) from t_pay_record_g7 where id = #{id}) n
        from dual-->
        select count(1) from t_pay_record_g7 where id = #{id}
    </select>

    <select id="selectPayDetailListByLotIds" resultMap="PayDetailResult">
        <include refid="selectTPayDetailVo"/>
        where
        del_flag = 0
        and lot_id in
        <foreach item="ids" collection="lotIds" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </select>

    <select id="selectPayDetailListByInvoiceId" resultMap="PayDetailResult">
        select
            payDetail.pay_detail_id,
            payDetail.vbillno,
            payDetail.vbillstatus,
            payDetail.free_type,
            payDetail.cost_type_on_way,
            payDetail.cost_type_freight,
            payDetail.lot_id,
            payDetail.lotno,
            payDetail.carrier_id,
            payDetail.carr_code,
            payDetail.carr_name,
            payDetail.bala_corp,
            payDetail.balatype,
            allo.COST_SHARE as trans_fee_count,
            payDetail.got_amount,
            payDetail.ungot_amount,
            payDetail.oil_card_number,
            payDetail.check_no,
            payDetail.check_head,
            payDetail.unconfirm_type,
            payDetail.unconfirm_memo,
            payDetail.confirm_time,
            payDetail.confirm_user,
            payDetail.driver_mobile,
            payDetail.driver_name,
            payDetail.carno,
            payDetail.req_deli_date,
            payDetail.req_arri_date,
            payDetail.is_close,
            payDetail.is_adjust,
            payDetail.adjust_memo,
            payDetail.memo,
            payDetail.reg_user_id,
            payDetail.reg_date,
            payDetail.reg_scr_id,
            payDetail.cor_user_id,
            payDetail.cor_date,
            payDetail.cor_scr_id,
            payDetail.del_flag,
            payDetail.del_date,
            payDetail.del_user_id,
            payDetail.rec_card_no,
            payDetail.rec_account,
            payDetail.rec_bank,
            payDetail.is_ntocc,
            payDetail.apply_user,
            payDetail.apply_time,
            payDetail.is_fleet_data,
            payDetail.is_fleet_assign,
            payDetail.fleet_receive_detail_id,
            payDetail.carr_bank_id,
            payDetail.INCOME_REMARK
        from t_pay_detail payDetail
                 left join t_cost_allocation allo on payDetail.pay_detail_id = allo.pay_detail_id
        where
            payDetail.del_flag = 0 and allo.DEL_FLAG = 0
        and allo.invoice_id = #{invoiceId}
    </select>

    <select id="selectPayDetailListByInvoiceIdAndEntrustId" resultMap="PayDetailResult">
        select
            payDetail.pay_detail_id,
            payDetail.vbillno,
            payDetail.vbillstatus,
            payDetail.free_type,
            payDetail.cost_type_on_way,
            payDetail.cost_type_freight,
            payDetail.lot_id,
            payDetail.lotno,
            payDetail.carrier_id,
            payDetail.carr_code,
            payDetail.carr_name,
            payDetail.bala_corp,
            payDetail.balatype,
            allo.COST_SHARE as trans_fee_count,
            payDetail.got_amount,
            payDetail.ungot_amount,
            payDetail.oil_card_number,
            payDetail.check_no,
            payDetail.check_head,
            payDetail.unconfirm_type,
            payDetail.unconfirm_memo,
            payDetail.confirm_time,
            payDetail.confirm_user,
            payDetail.driver_mobile,
            payDetail.driver_name,
            payDetail.carno,
            payDetail.req_deli_date,
            payDetail.req_arri_date,
            payDetail.is_close,
            payDetail.is_adjust,
            payDetail.adjust_memo,
            payDetail.memo,
            payDetail.reg_user_id,
            payDetail.reg_date,
            payDetail.reg_scr_id,
            payDetail.cor_user_id,
            payDetail.cor_date,
            payDetail.cor_scr_id,
            payDetail.del_flag,
            payDetail.del_date,
            payDetail.del_user_id,
            payDetail.rec_card_no,
            payDetail.rec_account,
            payDetail.rec_bank,
            payDetail.is_ntocc,
            payDetail.apply_user,
            payDetail.apply_time,
            payDetail.is_fleet_data,
            payDetail.is_fleet_assign,
            payDetail.fleet_receive_detail_id,
            payDetail.carr_bank_id
        from t_pay_detail payDetail
                 left join t_cost_allocation allo on payDetail.pay_detail_id = allo.pay_detail_id
        where
            payDetail.del_flag = 0 and allo.DEL_FLAG = 0
        and allo.invoice_id = #{invoiceId} and allo.entrust_id = #{entrustId,jdbcType=VARCHAR}
    </select>


    <select id="selectPayDetailListGroupByLotId" resultType="EntrustLotDTO">
        <!--select lot.LOT,
               lot.CARRIER_NAME carrierName,
               CAR_NO carNo,
               carr.PHONE phone,
               nvl(t_pay.yf,0) yf,
               nvl(t_pay.yfGot,0) yfGot,
               nvl(t_pay.zt,0) zt,
               nvl(t_pay.ztGot,0) ztGot,
               nvl(t_pay.transFeeCount,0) transFeeCount
        from T_ENTRUST_LOT lot
                 left join m_carrier carr on lot.CARRIER_ID = carr.CARRIER_ID
                 left join (
                select lot_id,sum(transFeeCount) transFeeCount,sum(yf) yf,sum(yfGot) yfGot,sum(zt) zt,sum(ztGot) ztGot from(
                     select
                LOT_ID,
                TRANS_FEE_COUNT+nvl(TAX_AMOUNT,0) transFeeCount,
                case when free_Type = 0 then TRANS_FEE_COUNT+nvl(TAX_AMOUNT,0) else 0 end as yf,
                case when free_Type = 0 then GOT_AMOUNT else 0 end as yfGot,
                case when free_Type = 1 then TRANS_FEE_COUNT+nvl(TAX_AMOUNT,0) else 0 end as zt,
                case when free_Type = 1 then GOT_AMOUNT else 0 end as ztGot
            from T_PAY_DETAIL where DEL_FLAG = 0) group by LOT_ID
        ) t_pay on t_pay.LOT_ID = lot.ENTRUST_LOT_ID
        where lot.DEL_FLAG = 0
          and lot.ENTRUST_LOT_ID in (
            select lot_id from T_ENTRUST where ORDERNO = #{invoiceId}
        )-->

        select lot.LOT, lot.CARRIER_NAME carrierName, lot.CAR_NO carNo, carr.PHONE phone,
            sum(case when p.income_remark not in (1,2) then p.trans_fee_count else 0 end) yf,
            sum(case when p.income_remark not in (1,2) then p.got_amount else 0 end) yfgot,
            sum(case when p.income_remark in (1,2) then p.trans_fee_count else 0 end) kk,
            sum(case when p.income_remark in (1,2) then p.got_amount else 0 end) kkgot,
            sum(p.trans_fee_count) transFeeCount,
            sum(case when p.income_remark not in (1,2) then ca.cost_share else 0 end) yfshare,
            sum(case when p.income_remark in (1,2) then ca.cost_share else 0 end) kkshare
            from t_cost_allocation ca
                 inner join t_pay_detail p on p.pay_detail_id = ca.pay_detail_id and p.del_flag = 0
                 left join t_entrust_lot lot on lot.entrust_lot_id = p.lot_id
                 left join m_carrier carr on lot.CARRIER_ID = carr.CARRIER_ID
        where ca.invoice_id = #{invoiceId} and ca.del_flag = 0
        group by lot.LOT, lot.CARRIER_NAME, lot.CAR_NO, carr.PHONE
        order by lot.lot
    </select>

    <select id="selectPayDetailCostAllocationListByLotIds" resultMap="PayDetailResult">
        select
        a.pay_detail_id,
        a.vbillno,
        a.vbillstatus,
        a.free_type,
        a.cost_type_on_way,
        a.cost_type_freight,
        a.lot_id,
        a.lotno,
        a.carrier_id,
        a.carr_code,
        a.carr_name,
        a.bala_corp,
        a.balatype,
        <!-- a.trans_fee_count, -->
        b.cost_share trans_fee_count,
        a.got_amount,
        a.ungot_amount,
        a.oil_card_number,
        a.check_no,
        a.check_head,
        a.unconfirm_type,
        a.unconfirm_memo,
        a.confirm_time,
        a.confirm_user,
        a.driver_mobile,
        a.driver_name,
        a.carno,
        a.req_deli_date,
        a.req_arri_date,
        a.is_close,
        a.is_adjust,
        a.adjust_memo,
        a.memo,
        a.reg_user_id,
        a.reg_date,
        a.reg_scr_id,
        a.cor_user_id,
        a.cor_date,
        a.cor_scr_id,
        a.del_flag,
        a.del_date,
        a.del_user_id,
        a.rec_card_no,
        a.rec_account,
        a.rec_bank,
        a.is_ntocc,
        a.apply_user,
        a.apply_time,
        a.is_fleet_data,
        a.is_fleet_assign,
        a.fleet_receive_detail_id,
        a.carr_bank_id
        from t_pay_detail a
        left join t_cost_allocation b
        on a.lot_id = b.lot_id

        where
        b.lot_id in
        <foreach item="ids" collection="lotIds" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </select>

    <select id="listOtherPayDetailIds" resultType="Map">
        select a.pay_detail_id,listagg(b.pay_detail_id,',') within GROUP(order by b.pay_detail_id) other_ids,a.vbillno,a.lotno from t_pay_detail a
            left join t_pay_detail b on a.lot_id = b.lot_id and b.pay_detail_id != a.pay_detail_id and b.del_flag = 0 and b.vbillstatus in (0,1)
            and b.pay_detail_id not in (<foreach collection="ids" item="id" separator=",">#{id}</foreach>)
        where a.pay_detail_id in (<foreach collection="ids" item="id" separator=",">#{id}</foreach>)
        group by a.pay_detail_id,a.vbillno,a.lotno
        order by a.vbillno desc
    </select>

    <select id="queryInvoicePayDetail" parameterType="PayDetail" resultMap="PayDetailResult">
        select a.pay_detail_id,a.vbillno,a.vbillstatus,a.free_type,a.cost_type_on_way,a.cost_type_freight,a.lot_id,a.lotno,a.bala_corp,
               a.trans_fee_count,a.got_amount,a.ungot_amount,a.reg_date,a.is_adjust,a.adjust_memo,a.apply_user,a.apply_time,a.memo,
               a.carno,nvl(a.tax_amount,0) tax_amount,a.req_deli_date,a.req_arri_date,a.CONSUMBLE_BACK,
               b.bala_type carr_bala_type,
               sysUser.user_name as reg_user_id,
               lot.DELI_PROVINCE_NAME || lot.DELI_CITY_NAME || lot.DELI_AREA_NAME as deli_addr,
               lot.ARRI_PROVINCE_NAME || lot.ARRI_CITY_NAME || lot.ARRI_AREA_NAME as arri_addr,
               lot.req_deli_date lot_req_deli_date,
               lot.g7_syn lot_g7_syn,
               lot.g7_start lot_g7_start,
               lot.g7_end lot_g7_end,
               a.g7_pay,
               lot.g7_msg lot_g7_msg
        from t_pay_detail a
        inner join t_entrust_lot lot on lot.entrust_lot_id = a.lot_id and lot.del_flag=0
        <if test="lotReqDeliDateStart != null and lotReqDeliDateStart !=''">and lot.req_deli_date >= to_date(#{lotReqDeliDateStart},'yyyy-mm-dd')</if>
        <if test="lotReqDeliDateEnd != null and lotReqDeliDateEnd !=''">and lot.req_deli_date &lt; to_date(#{lotReqDeliDateEnd},'yyyy-mm-dd') + 1</if>
        <if test='(custAbbr != null and custAbbr != "") or (invoiceVbillno != null and invoiceVbillno != "")'>
            and exists (select 1 from t_entrust entrust
            <if test="invoiceVbillno != null and invoiceVbillno != ''">
                <bind name="invoiceVbillno" value="invoiceVbillno + '%'"/>
                inner join t_invoice t2 on t2.invoice_id = entrust.orderno and t2.vbillno like #{invoiceVbillno}
            </if>
            where entrust.lot_id = lot.entrust_lot_id
            <if test='custAbbr != null and custAbbr != ""'>
                <bind name="custAbbr" value="custAbbr + '%'"/>
                and entrust.cust_abbr like #{custAbbr}
            </if>
            and entrust.del_flag=0)
        </if>
        left join m_carrier b on a.carrier_id = b.carrier_id
        left join sys_user sysUser on a.reg_user_id = sysUser.USER_ID
        WHERE a.del_flag = 0
            <if test="carno != null and carno !=''">
                <bind name="carnoLike" value="carno+'%'"/>
                and a.carno like #{carnoLike}
            </if>
            <if test="status != null and status != ''">
                and a.vbillstatus in
                <foreach item="item" index="index" collection="status.split(',')" open="(" separator="," close=")">
                    ${@Integer@parseInt(item)}
                </foreach>
            </if>
            <if test="carrierId != null and carrierId !=''">
                and a.carrier_id = #{carrierId}
            </if>
        order by a.reg_date desc
    </select>

    <select id="checkPayDetailLotLock" resultType="Integer">
        select count(1)
        from t_pay_detail a
                 left join t_entrust_lot b on a.lot_id = b.entrust_lot_id
        where a.pay_detail_id = #{payDetailId}
          and (b.lock_pay = '1' or b.single_lock = '1')
    </select>
    <select id="checkPayDetailDataByLotId" resultType="java.lang.Integer">
        select count(1)
        from t_pay_detail
        where lot_id = #{lotId}
        and del_flag = 0
        and vbillstatus &gt; 0
    </select>

    <update id="deletePayDetailByLotId">
        update t_pay_detail set del_flag = 1,del_user_id = #{userId},del_date = sysdate
        where lot_id = #{lotId}
    </update>

    <select id="selectPayDetailCountPermission" resultType="int">
        select count(*)
        FROM
        t_pay_detail t
        left join T_ENTRUST_LOT t1 on t.LOT_ID = t1.ENTRUST_LOT_ID and t1.DEL_FLAG = 0
        left join T_ENTRUST t2 on t1.ENTRUST_LOT_ID = t2.LOT_ID and t2.DEL_FLAG = 0
        where t.del_flag = 0
        <if test="vbillstatus != null">
            and t.vbillstatus = #{vbillstatus}
        </if>
        ${params.dataScope}
    </select>

    <select id="selectKpskchatsData" resultType="com.ruoyi.tms.vo.main.KpskChatsDataVO">
        select nvl(t.sales_dept_name, t1.dept_name) deptName, t.amount unbilledAmount, t1.overdue_amount overdueAmount
        from (SELECT A.sales_dept_name, A.dept_id, sum(A.amount) amount
              FROM (SELECT t.trans_fee_count - nvl(t.got_amount, 0) amount,
                           dept.dept_name sales_dept_name,
                           dept.dept_id
                    FROM t_receive_detail t
                                 LEFT JOIN t_rece_check_sheet_b t2
                            ON t.vbillstatus = 2
                                    and t2.receive_detail_id = t.receive_detail_id
                                    and t2.del_flag = 0
                                 LEFT JOIN t_rece_check_sheet t3
                            ON t3.rece_check_sheet_id = t2.rece_check_sheet_id
                                 LEFT JOIN t_invoice t4
                            ON t4.invoice_id = t.invoice_id
                                 LEFT JOIN m_customer cust
                            ON t.customer_id = cust.customer_id
                                 LEFT JOIN sys_dept dept
                            ON dept.dept_id = cust.sales_dept
                    WHERE t.is_ntocc = 0
                      and t.del_flag = 0
                      and t.reg_scr_id != 'importAllData' and t.reg_scr_id != 'partDataPayDetailApplication'
                      and (t.vbillstatus in (0, 1) or
                           (t.vbillstatus = 2 and
                            nvl(t3.appl_check_amount, 0) = 0))) A
              where a.dept_id is not null
              group by A.sales_dept_name, a.dept_id) t
                     full join (SELECT x.dept_id,
                                       x.dept_name,
                                       sum(case
                                               when x.overdue_days <![CDATA[ > ]]> 0 then
                                                       x.ungot_amount
                                               else
                                                       0
                                               end) overdue_amount
                                FROM (SELECT f.dept_id,
                                             f.dept_name,
                                             nvl(a.ungot_amount, a.billing_amount) ungot_amount,
                                             b.customer_id,
                                             to_date(sysdate) - to_date(a.billing_date) -
                                             nvl(c.collection_days, 0) overdue_days
                                      FROM t_rece_billing a
                                                   INNER JOIN t_rece_sheet_record b
                                              ON a.rece_sheet_record_id = b.rece_sheet_record_id
                                                      and b.del_flag = 0
                                                   INNER JOIN m_customer c
                                              ON c.customer_id = b.customer_id
                                                   LEFT JOIN sys_dept f
                                              ON f.dept_id = c.sales_dept
                                      WHERE a.del_flag = 0
                                        and a.reg_scr_id != 'importAllData' and a.reg_scr_id != 'partDataPayDetailApplication'
                                        and (a.ungot_amount is null or a.ungot_amount <![CDATA[ > ]]> 0)
                                        and a.billing_type != '6'
                        and a.billing_status = 1
                        and a.red = 0
                        and a.red_rece_billing_id is null) x
                                group by x.dept_id, x.dept_name) t1
                on t.dept_id = t1.dept_id
        order by t.amount desc nulls last
    </select>


    <select id="selectXslbchatsData" resultType="com.ruoyi.tms.vo.main.XslbChatsDataVO">
        select t.pay_method_name payMethodName, sum(t.pay_amount) payAmount
        from (select
                     t.pay_amount,
                     t11.dict_label pay_method_name
              from t_pay_record t
                           left join t_account t1
                      on t.out_account = t1.account_id
                              and t1.del_flag = 0
                           left join t_pay_detail t2
                      on t.pay_detail_id = t2.pay_detail_id
                              and t2.del_flag = 0
                           left join t_pay_sheet_record t3
                      on t.pay_sheet_record_id = t3.pay_sheet_record_id
                           left join m_carrier t4
                      on t3.CARRIER_ID = t4.carrier_id
                           left join t_pay_check_sheet t5
                      on t3.PAY_CHECK_SHEET_ID = t5.PAY_CHECK_SHEET_ID
                              and t5.del_flag = 0
                           left join sys_user t6
                      on t2.reg_user_id = t6.user_id
                           left join sys_user t7
                      on t3.reg_user_id = t7.user_id
                           left join m_carrier t8
                      on t2.CARRIER_ID = t8.CARRIER_ID
                           left join sys_user t9
                      on t4.reg_user_id = t9.user_id
                           left join sys_user t10
                      on t8.reg_user_id = t10.user_id
                           left join sys_dict_data t11
                      on t11.dict_type = 'pay_method'
                              and t11.dict_value = t.pay_method
        <where>
            t.del_falg = 0
            <!--查询付款时间-->
            <if test='param.payDateStart != null and param.payDateStart != ""'>
                and t.pay_date  <![CDATA[  >= ]]> to_date(#{param.payDateStart},'yyyy-mm-dd')
            </if>
            <if test='param.payDateEnd != null and param.payDateEnd != ""'>
                and t.pay_date <![CDATA[ <= ]]> to_date(#{param.payDateEnd},'yyyy-mm-dd')
            </if>

            <!--结算公司-->
            <if test="param.balaCorp != null  and param.balaCorp != '' and param.balaCorp.indexOf(',') != -1">
                and t1.bala_corp in
                <foreach item="item" index="index" collection="param.balaCorp.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.balaCorp != null  and param.balaCorp != '' and param.balaCorp.indexOf(',') == -1">
                and t1.bala_corp = #{param.balaCorp}
            </if>

            <if test='param.balaCorpType != null  and param.balaCorpType == "0"'>
                    and t1.bala_corp = 'MY'
            </if>
            <if test='param.balaCorpType != null  and param.balaCorpType == "1".toString()'>
                    and t1.bala_corp = 'JH'
            </if>
            <if test='param.balaCorpType != null  and param.balaCorpType == "2".toString()'>
                    and t1.bala_corp is null
            </if>
            <if test='param.balaCorpType != null  and param.balaCorpType == "3".toString()'>
                  and (t1.bala_corp is null or t1.bala_corp = 'MY' or t1.bala_corp = 'JH')
            </if>

        </where>
                     ) t
        group by t.pay_method_name

    </select>

    <update id="deletePayCheckSheetBByPayDetailId">
        update t_pay_check_sheet_b
        set DEL_FALG    = 1,
            DEL_USER_ID = #{delUserId},
            DEL_DATE    = #{delDate},
            COR_SCR_ID  = #{scrId}
        where pay_detail_id = #{payDetailId}
          and del_falg = 0
    </update>

    <select id="getLotBillingType" parameterType="String" resultType="String">
        select billing_type from t_entrust_lot where entrust_lot_id = #{lotId}
    </select>

    <insert id="saveLotReceipt">
        insert into t_entrust_lot_receipt (LOT_ID, TID, REG_SCR_ID, REG_USER_ID, REG_DATE)
        values (#{lotId}, #{tidReceipt}, #{scrId}, #{userId}, #{regDate})
    </insert>

    <select id="listLotReceipt" resultType="SysUploadFile">
        select b.FILE_ID fileid,b.TID,FILE_NAME filename,FILE_PATH filepath
        from t_entrust_lot_receipt a
                 left join sys_upload_file b on b.tid = a.tid
        where a.lot_id = #{lotId} and b.del_flag = 0
    </select>

    <select id="getPayDetailTaxTxt" resultType="PayDetail">
        select (
                select listagg(t.billing_type || ':' || sum(amount), ',') within GROUP(order by t.billing_type) from t_pay_detail_tax t
                where t.pay_detail_id = a.pay_detail_id group by t.billing_type having count(amount) = 1 or sum(amount) != 0
            ) taxtxt,
            b.is_close isclose, b.req_deli_date reqdelidate, b.entrust_lot_id lotId, b.billing_type lotBillingType,
            a.trans_fee_count transFeeCount, nvl(a.tax_amount, 0) taxAmount, a.g7_pay g7Pay
        from t_pay_detail a
            left join t_entrust_lot b on b.entrust_lot_id = a.lot_id
        where a.pay_detail_id = #{payDetailId}
    </select>

    <insert id="initPayDetailTax">
        insert into t_pay_detail_tax (ID, PAY_DETAIL_ID, BILLING_TYPE, AMOUNT, REG_DATE, REG_USER_ID, REG_SCR_ID,
                                      EFFECT_DATE, FLAG, IS_INIT, LOT_ID, seq, amountx, tax)
        select sys_guid(),
               t.pay_detail_id,
               nvl(t2.billing_type, '6'),
               t.trans_fee_count + nvl(tax_amount, 0),
               #{regDate},
               #{userId},
               #{scrId},
               <!-- 是调整单：单据的创建日期，不是调整单：要求提货日期 --><!-- 对账生成的调整单，被认为是调账记录生成哪个时间端的数据 -->
               case when t.is_adjust = 0 then t2.req_deli_date else t.reg_date end,
               0,
               1,
               t.lot_id,
               1,
               (t.trans_fee_count + nvl(tax_amount, 0)) / t3.num_val1,
               t3.num_val1
        from t_pay_detail t
                 left join t_entrust_lot t2 on t2.entrust_lot_id = t.lot_id
                 left join sys_dict_data t3 on t3.dict_type = 'billing_type' and t3.dict_value = nvl(t2.billing_type, '6')
        where t.pay_detail_id = #{payDetailId}
          and not exists (select 1 from t_pay_detail_tax x where x.pay_detail_id = #{payDetailId})
    </insert>

    <insert id="addDeductTax">
        insert into t_pay_detail_tax (ID, PAY_DETAIL_ID, BILLING_TYPE, AMOUNT, EFFECT_DATE, REG_DATE,
                                   REG_USER_ID, REG_SCR_ID, FLAG, LOT_ID, seq, amountx, tax<if test="flag == 3">,is_init</if>)
        select sys_guid(), t.pay_detail_id, #{billingType}, #{amount}, #{effectDate}, #{regDate},
               #{userId}, #{scrId}, #{flag}, t.lot_id, nvl((select max(seq) from t_pay_detail_tax tt where tt.pay_detail_id = t.pay_detail_id),0) + 1,
               #{amount} / t3.num_val1, t3.num_val1<if test="flag == 3">,1</if>
               from t_pay_detail t
                        left join sys_dict_data t3 on t3.dict_type = 'billing_type' and t3.dict_value = #{billingType}
        where t.pay_detail_id = #{payDetailId}
    </insert>

    <delete id="clearPayDetailTax">
        delete from t_pay_detail_tax t where t.pay_detail_id = #{payDetailId} <!--and t.is_init = 0-->
    </delete>

    <update id="deleteTidReceipt">
        begin

        update sys_upload_file
        set del_flag = 1
        where file_id = #{fileId}
          and tid = #{tid};

        update t_entrust_lot_receipt
        set del_flag = 1
        where tid = #{tid} and LOT_ID = #{lotId}
          and not exists(select 1 from sys_upload_file where tid = #{tid} and del_flag = 0);

        end;
    </update>

    <select id="selectInvoicePayDetailListByInvoiceId" resultType="com.ruoyi.tms.vo.finance.InvoicePayDetailVO">
        select
            t.invoice_id         invoiceId,
            sum(t.cost_share)         transFeeCount,
            t.free_type         freeType,
            t.cost_type_on_way  costTypeOnWay,
            t.cost_type_freight costTypeFreight
        from (
        select t.invoice_id,
               t.cost_share,
               t1.free_type,
               t1.cost_type_on_way,
               t1.cost_type_freight
        from t_cost_allocation t
        left join t_pay_detail t1 on t.pay_detail_id = t1.pay_detail_id
        where t.del_flag = 0 and t1.del_flag = 0 and t.invoice_id = #{invoiceId,jdbcType=VARCHAR}
        ) t group by t.invoice_id,t.free_type,t.cost_type_on_way,t.cost_type_freight
    </select>

    <select id="selectPayManagePayDetailVO" resultType="com.ruoyi.tms.vo.finance.PayManagePayDetailVO">
        select
            t.pay_detail_id       payDetailId,
            t.VBILLNO             VBILLNO,
            t.VBILLSTATUS         VBILLSTATUS,
            t.CARNO               CARNO,
            t.carr_name           carrName,
            t.driver_name         driverName,
            t.driver_mobile       driverMobile,
            t.trans_fee_count     transFeeCount,
            t.got_amount          gotAmount,
            t.ungot_amount        ungotAmount,
            t.free_type           freeType
        from t_pay_detail t
        left join t_entrust_lot t1 on t.lot_id = t1.entrust_lot_id
        where T.DEL_FLAG = 0
          and t1.entrust_lot_id = #{lotId,jdbcType=VARCHAR}
    </select>

    <update id="deletePayDetailBatchByLotId">
        update t_pay_detail t set  del_flag = #{payDetail.delFlag}, del_date = #{payDetail.delDate}
        where del_flag = 0 and INCOME_REMARK = 1
        and lot_id in
        <foreach collection="lotIds" item="lotId" index="index"
                 open="(" close=")" separator=",">
            #{lotId}
        </foreach>
    </update>

    <select id="selectUnPayCntAndAmountByCarrierId" resultType="java.util.Map">
        select nvl(sum(ungot_amount),0) amount,
               count(distinct lot_id) cnt
        from (
        select
               lot_id,
        case when vbillstatus = 0 then trans_Fee_Count
        else ungot_amount end as ungot_amount
        from t_pay_detail
        where del_flag = 0
        and carrier_id = #{carrierId}
        and vbillstatus not in (4,5))
    </select>

    <select id="selectUnPayCustAndAmountByCarrierId"  resultType="java.util.Map">
        select * from (select * from (
        select
            DEPT_NAME,
            cust_abbr,
            round(sum(ungot_amount)/10000,2) amount
        from (
                 select
                     dept.DEPT_NAME,
                     cust.cust_abbr,
                     case when payDetail.vbillstatus = 0 then payDetail.trans_Fee_Count
                          else payDetail.ungot_amount end as ungot_amount
                 from t_pay_detail payDetail
                          left join T_COST_ALLOCATION allo on payDetail.PAY_DETAIL_ID = allo.PAY_DETAIL_ID
                          left join t_invoice invoice on invoice.INVOICE_ID = allo.invoice_ID
                          left join m_customer cust on cust.customer_ID = invoice.customer_ID
                          left join sys_dept dept on dept.dept_ID = cust.SALES_DEPT
                 where payDetail.del_flag = 0
                   and payDetail.carrier_id = #{carrierId}
                   and payDetail.vbillstatus not in (4,5)
             ) group by DEPT_NAME,
                        cust_abbr) order by amount desc)where rownum &lt;=10
    </select>

    <select id="selectUnConfirmCntAndAmountByCarrier" resultType="java.util.Map">
        select nvl(sum(ungot_amount),0) amount,
               count(distinct lot_id) cnt
        from (
                 select
                        lot_id,
                     case when vbillstatus = 0 then trans_Fee_Count
                          else ungot_amount end as ungot_amount
                 from t_pay_detail
                 where del_flag = 0
                   and pay_detail_id in (
                    select pay_detail_id from t_pay_check_sheet_b
                    where pay_check_sheet_id in (
                        select pay_check_sheet_id from t_pay_check_sheet where carrier_id = #{carrierId}
                        and vbillstatus = 0 and del_flag = 0
                    )
                 )
            )
    </select>

    <select id="selectUnConfirmDetailAndAmountByCarrier" resultType="java.util.Map">
        select * from (select * from (
        select
            VBILLNO,
            yearMonth,
            round(sum(ungot_amount)/10000,2) amount
        from (
                 select
                     checkSheet.VBILLNO,
                     checkSheet.year||'-'||checkSheet.month yearMonth,
                     case when payDetail.vbillstatus = 0 then payDetail.trans_Fee_Count
                          else payDetail.ungot_amount end as ungot_amount
                 from t_pay_detail payDetail
                          left join t_pay_check_sheet_b checkSheetB on payDetail.PAY_DETAIL_ID = checkSheetB.PAY_DETAIL_ID
                          left join t_pay_check_sheet checkSheet on checkSheet.PAY_CHECK_SHEET_ID = checkSheetB.PAY_CHECK_SHEET_ID
                 where payDetail.del_flag = 0
                   and checkSheet. carrier_id = #{carrierId}
                   and checkSheet.vbillstatus =0  and checkSheet.del_flag = 0
             )
        group by VBILLNO,
                 yearMonth ) order by amount desc)where rownum &lt;=10
    </select>

    <select id="selectConfirmCntAndAmountByCarrier" resultType="java.util.Map">
        select nvl(sum(ungot_amount),0) amount,
               count(distinct lot_id) cnt
        from (
                 select
                     lot_id,
                     case when vbillstatus = 0 then trans_Fee_Count
                          else ungot_amount end as ungot_amount
                 from t_pay_detail
                 where del_flag = 0
                   and pay_detail_id in (
                     select pay_detail_id from t_pay_check_sheet_b
                     where pay_check_sheet_id in (
                         select pay_check_sheet_id from t_pay_check_sheet where carrier_id = #{carrierId}
                                                                            and vbillstatus in(1,2)  and del_flag = 0
                     )
                 )
             )
    </select>


    <select id="selectConfirmDetailAndAmountByCarrier" resultType="java.util.Map">
        select * from (select * from (
        select
            VBILLNO,
            yearMonth,
            round(sum(ungot_amount)/10000,2) amount
        from (
                 select
                     checkSheet.VBILLNO,
                     checkSheet.year||'-'||checkSheet.month yearMonth,
                     case when payDetail.vbillstatus = 0 then payDetail.trans_Fee_Count
                          else payDetail.ungot_amount end as ungot_amount
                 from t_pay_detail payDetail
                          left join t_pay_check_sheet_b checkSheetB on payDetail.PAY_DETAIL_ID = checkSheetB.PAY_DETAIL_ID
                          left join t_pay_check_sheet checkSheet on checkSheet.PAY_CHECK_SHEET_ID = checkSheetB.PAY_CHECK_SHEET_ID
                 where payDetail.del_flag = 0
                   and checkSheet. carrier_id = #{carrierId}
           and checkSheet.vbillstatus in(1,2)  and checkSheet.del_flag = 0
         )
      group by VBILLNO,
    yearMonth) order by amount desc)where rownum &lt;=10
    </select>


    <select id="selectUnPackageCntAndAmountByCarrierId" resultType="java.util.Map">
        select sum(ungot_amount) amount,
               count(distinct lot_id) cnt
        from (
                 select
                        lot_id,
                     case when vbillstatus = 0 then trans_Fee_Count
                          else ungot_amount end as ungot_amount
                 from t_pay_detail
                 where del_flag = 0
                   and carrier_id = #{carrierId}
                   and vbillstatus  in (0,1))
    </select>

    <select id="listInvoiceInfoForWecomSp" resultType="java.util.Map">
        select to_char(b.req_deli_date, 'YYYY-MM-DD')                                                               req_deli_date,
               b.vbillno,
               b.DELI_PRO_NAME,
               b.deli_city_name,
               b.deli_area_name,
               b.arri_pro_name,
               b.arri_city_name,
               b.arri_area_name,
               b.cust_abbr,
               b.CAR_LEN_NAME,
               b.CAR_TYPE_NAME,
               b.goods_name,
               b.weight_count,
               b.num_count,
               b.volume_count,
               b.customer_id,
               dept.dept_name                                                                                       sales_dept_name,
               dept.leader                                                                                          sales_dept_leader,
               sg.dept_name                                                                                         sales_name,
               sg.leader                                                                                            sales_team_leader,
               (select nvl(sum(TRANS_FEE_COUNT), 0)
                from t_receive_detail x
                where x.invoice_id = b.invoice_id
                  and x.del_flag = 0)                                                                               ys,
               (select nvl(sum(cost_share), 0)
                from T_COST_ALLOCATION x
                where x.invoice_id = b.invoice_id
                  and x.del_flag = 0)                                                                               yf,
               case b.billing_type when '6' then 0 else (select nvl(sum(case
                                   when t.cost_type_freight in ('1', '3', '5') then t.cost_share *
                                                                                    (select to_number(config_value) from sys_config c where c.config_key = 'oil_tax_rate')
                                   when p.g7_pay = 2 then t.cost_share * (select to_number(config_value) from sys_config c where c.config_key = 'g7_tax_rate')
                                   when nvl(nvl(b.billing_type, c.billing_type), '6') = '6' then
                                           nvl(b.amount, p.trans_fee_count) * t.cost_share / p.trans_fee_count *
                                           (select to_number(config_value) from sys_config c where c.config_key = 'cash_tax_rate')
                                   else 0 end), 0)
                from t_cost_allocation t
                         inner join t_pay_detail p on p.pay_detail_id = t.pay_detail_id
                         inner join t_entrust_lot c on c.entrust_lot_id = t.lot_id
                         left join t_pay_detail_tax b on p.pay_detail_id = b.pay_detail_id and
                                                         (p.cost_type_freight is null or p.cost_type_freight in ('0', '2', '4')) and
                                                         (p.g7_pay is null or p.g7_pay != 2)
                where t.del_flag = 0
                  and t.cost_share != 0
                  and t.invoice_id = a.orderno) end                                                                    yf_tax,
               (select nvl(sum(FEE_AMOUNT), 0) from t_other_fee x where x.lot_id = b.invoice_id and x.del_flag = 0) sf,
               case b.billing_type when '6' then 0 else (select nvl(sum(
                                   case
                                       when o.pay_type = 1 then o.fee_amount * (select to_number(config_value) from sys_config c where c.config_key = 'oil_tax_rate')
                                       when nvl(b.billing_type, '6') = '6' then nvl(b.amount, o.fee_amount) *
                                                                                (select to_number(config_value) from sys_config c where c.config_key = 'cash_tax_rate')
                                       else 0 end), 0)
                from t_other_fee o
                         left join t_other_fee_tax b
                                   on o.other_fee_id = b.other_fee_id and (o.pay_type is null or o.pay_type = 0)
                where o.lot_id = a.orderno
                  and o.del_flag = 0) end                                                                              sf_tax,
               cust.plat_rate,
               a.RECEIPT_UPLOAD_TID
        from t_entrust a
                 left join t_invoice b on a.orderno = b.invoice_id
                 left join t_entrust_lot d on d.entrust_lot_id = a.lot_id
                 left join m_customer cust on cust.customer_id = a.customer_id
                 left join sys_dept dept on dept.dept_id = to_number(cust.sales_dept)
                 left join sys_dept sg on sg.dept_id = dept.parent_id
        where a.lot_id = #{lotId}
          and a.del_flag = 0
        order by b.vbillno
    </select>

    <update id="writeSpNo">
        update t_pay_detail set sp_no = #{spNo} where pay_detail_id = #{payDetailId}
    </update>

    <update id="writeSpNoBatch">
        update t_pay_detail set sp_no = #{spNo} where batch_no = #{batchNo} and lot_id = #{lotId}
    </update>

    <select id="countCheckPass" resultType="java.lang.Integer">
        select count(1) from t_pay_detail
        where lot_id = #{lotId}
          and del_flag = 0
          and free_type = '0'
          and cost_type_freight in ('2','3','4','5')
          and check_status = 1
    </select>

    <select id="selectTaxTxtOfPayDetailArr" resultType="java.lang.String">
        select listagg(nvl(b.billing_type,nvl(c.billing_type,'6')) || ':' || sum(nvl(b.amount, a.trans_fee_count)), ',')
        within GROUP(order by nvl(b.billing_type,nvl(c.billing_type,'6')))
        from t_pay_detail a
        left join t_pay_detail_tax b on a.pay_detail_id = b.pay_detail_id
        left join t_entrust_lot c on c.entrust_lot_id = a.lot_id
        where a.pay_detail_id in (<foreach collection="payDetailIdArr" item="payDetailId" separator=",">#{payDetailId}</foreach>)
        group by nvl(b.billing_type,nvl(c.billing_type,'6'))
        having count(nvl(b.amount, a.trans_fee_count)) = 1 or sum(nvl(b.amount, a.trans_fee_count)) != 0
    </select>

    <select id="sumOnWay" resultType="java.math.BigDecimal">
        select nvl(sum(t.cost_share),0) from t_cost_allocation t
        where t.invoice_id = #{invoiceId} and t.lot_id = #{lotId} and t.del_flag = 0
        and t.free_type = '1' and t.cost_type_on_way = #{costType}
    </select>

    <update id="onWayFeeOilChange">
        update t_pay_detail set
        <if test="type == 0 ">
            cost_Type_Freight = null
        </if>
        <if test="type == 1 ">
            cost_Type_Freight = '5'
        </if>
        where pay_detail_id = #{payDetailId}
    </update>

</mapper>