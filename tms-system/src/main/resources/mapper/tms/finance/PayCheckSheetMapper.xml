<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tms.mapper.finance.PayCheckSheetMapper">

<resultMap type="PayCheckSheet" id="PayCheckSheetResult">
    <result property="payCheckSheetId"    column="pay_check_sheet_id"    />
    <result property="vbillno"    column="vbillno"    />
    <result property="vbillstatus"    column="vbillstatus"    />
    <result property="carrierId"    column="carrier_id"    />
    <result property="year"    column="year"    />
    <result property="month"    column="month"    />
    <result property="costAmount"    column="cost_amount"    />
    <result property="totalAmount"    column="total_amount"    />
    <result property="gotAmount"    column="got_amount"    />
    <result property="ungotAmount"    column="ungot_amount"    />
    <result property="ifCheck"    column="if_check"    />
    <result property="checkType"    column="check_type"    />
    <result property="checkNo"    column="check_no"    />
    <result property="checkHead"    column="check_head"    />
    <result property="checkDate"    column="check_date"    />
    <result property="checkAmount"    column="check_amount"    />
    <result property="checkTaxRate"    column="check_tax_rate"    />
    <result property="checkRemark"    column="check_remark"    />
    <result property="memo"    column="memo"    />
    <result property="unconfirmType"    column="unconfirm_type"    />
    <result property="unconfirmMemo"    column="unconfirm_memo"    />
    <result property="fuelCard"    column="fuel_card"    />
    <result property="recAccount"    column="rec_account"    />
    <result property="recBank"    column="rec_bank"    />
    <result property="recCardNo"    column="rec_card_no"    />
    <result property="applicationAmount"    column="application_amount"    />
    <result property="applicationStatus"    column="application_status"    />
    <result property="carrCode"    column="carr_code"    />
    <result property="carrName"    column="carr_name"    />
    <result property="regUserId"    column="reg_user_id"    />
    <result property="regDate"    column="reg_date"    />
    <result property="corDate"    column="cor_date"    />
    <result property="corUserId"    column="cor_user_id"    />
    <result property="delFlag"    column="del_flag"    />
    <result property="delUserId"    column="del_user_id"    />
    <result property="delDate"    column="del_date"    />
    <result property="handVerification"    column="hand_verification"    />
    <result property="corScrId"    column="cor_scr_id"    />
    <result property="regScrId"    column="reg_scr_id"    />
    <result property="isNtocc"    column="is_ntocc"    />
    <result property="payCheckSheetName"    column="pay_check_sheet_name"    />
    <result property="oilAmount"    column="oil_amount"    />
    <result property="applicationAmountOil"    column="application_amount_oil"    />
    <result property="balaType"    column="bala_type"    />
    <result property="isClose"    column="is_close"    />
    <result property="taxAmount"    column="tax_amount"    />
    <result property="taxAmountOil"    column="tax_amount_oil"    />
    <result property="invoiceNumber"    column="invoice_number"    />
    <result property="tid"    column="tid"    />
    <result property="taxMemo"    column="tax_memo"    />
    <result property="taxAmountOil"    column="tax_amount_oil"    />
    <result property="adjustType"    column="ADJUST_TYPE"    />
    <result property="isFleetData"    column="is_fleet_data"    />
    <result property="isExistFleetData"    column="is_exist_fleet_data"    />
    <result property="lotG7End"    column="lot_g7_end"    />
    <result property="balaCorp" column="bala_corp" />
    <result property="bzjAmount" column="bzj_amount" />

    <result property="carrierLockPay" column="carrier_lock_pay" />

    <result property="abnormalDeduction" column="abnormal_deduction" />
    <result property="hasExp" column="hasExp" />
</resultMap>
<resultMap type="PayDetail" id="PayDetailResult">
    <result property="payDetailId"    column="pay_detail_id"    />
    <result property="payType"    column="pay_type"    />
    <result property="freeType"    column="free_type"    />
    <result property="costTypeOnWay"    column="cost_type_on_way"    />
    <result property="costTypeFreight"    column="cost_type_freight"    />
    <result property="vbillno"    column="vbillno"    />
    <result property="vbillstatus"    column="vbillstatus"    />
    <result property="lotno"    column="lotno"    />
    <result property="carrierId"    column="carrier_id"    />
    <result property="balatype"    column="balatype"    />
    <result property="transFeeCount"    column="trans_fee_count"    />
    <result property="otherFeeCount"    column="other_fee_count"    />
    <result property="costAmount"    column="cost_amount"    />
    <result property="gotAmount"    column="got_amount"    />
    <result property="ungotAmount"    column="ungot_amount"    />
    <result property="memo"    column="memo"    />
    <result property="checkNo"    column="check_no"    />
    <result property="checkHead"    column="check_head"    />
    <result property="unconfirmType"    column="unconfirm_type"    />
    <result property="unconfirmMemo"    column="unconfirm_memo"    />
    <result property="confirmTime"    column="confirm_time"    />
    <result property="confirmUser"    column="confirm_user"    />
    <result property="custOrderno"    column="cust_orderno"    />
    <result property="orderno"    column="orderno"    />
    <result property="driverMobile"    column="driver_mobile"    />
    <result property="driverName"    column="driver_name"    />
    <result property="carno"    column="carno"    />
    <result property="driverId"    column="driver_id"    />
    <result property="invoiceVbillno"    column="invoice_vbillno"    />
    <result property="reqDeliDate"    column="req_deli_date"    />
    <result property="reqArriDate"    column="req_arri_date"    />
    <result property="recCardNo"    column="rec_card_no"    />
    <result property="recAccount"    column="rec_account"    />
    <result property="recBank"    column="rec_bank"    />
    <result property="carrCode"    column="carr_code"    />
    <result property="carrName"    column="carr_name"    />
    <result property="regUserId"    column="reg_user_id"    />
    <result property="regDate"    column="reg_date"    />
    <result property="corUserId"    column="cor_user_id"    />
    <result property="corDate"    column="cor_date"    />
    <result property="delFlag"    column="del_flag"    />
    <result property="delDate"    column="del_date"    />
    <result property="delUserId"    column="del_user_id"    />
    <result property="oilCardNumber"    column="oil_card_number"    />
    <result property="isNtocc"    column="is_ntocc"    />
    <result property="isClose"    column="is_close"    />
    <result property="numCount"    column="num_count"    />
    <result property="weightCount"    column="weight_count"    />
    <result property="volumeCount"    column="volume_count"    />
    <result property="deliAddr"    column="deli_addr"    />
    <result property="arriAddr"    column="arri_addr"    />
    <result property="costType"    column="cost_type"    />
    <result property="taxAmount"    column="tax_amount"    />
    <result property="lotId"    column="LOT_ID"    />
    <result property="salesDeptName"    column="dept_name"    />
    <result property="costTypeFreightName"    column="cost_type_freight_name"    />
    <result property="writeOffTime"    column="write_off_time"    />
    <result property="lotG7End"    column="lot_g7_end"    />
    <result property="lotG7Start"    column="lot_g7_start"    />
    <result property="lotG7Msg"    column="lot_g7_msg"    />
    <result property="lotG7Syn"    column="lot_g7_syn"    />
    <result property="isFleetData"    column="is_fleet_data"    />
    <result property="isFleetAssign"    column="is_fleet_assign"    />
    <result property="fleetReceiveDetailId"    column="fleet_receive_detail_id"    />
    <result property="payWay" column="pay_way" />
    <result property="lotLockPay" column="lot_lock_pay" />

</resultMap>

<sql id="selectPayCheckSheetVo">
    select
        pay_check_sheet_id, vbillno, vbillstatus, total_amount,carrier_id, year, month, nvl(got_amount,0) got_amount
        ,nvl(ungot_amount,0) ungot_amount,
        if_check, check_type, check_no, check_head, check_date, check_amount, check_tax_rate, check_remark, memo,
        unconfirm_type, unconfirm_memo,application_amount, carr_code, carr_name, reg_user_id, reg_date, cor_date,
        cor_user_id, del_flag, del_user_id, del_date, hand_verification, cor_scr_id, reg_scr_id,is_ntocc,pay_check_sheet_name,oil_amount,application_amount_oil
        ,tax_amount,invoice_number,tid,tax_memo,is_close,tax_amount_oil,is_fleet_data,is_exist_fleet_data,lot_g7_end,bala_corp,bzj_amount
    from t_pay_check_sheet
</sql>

<select id="selectPayCheckSheetList" parameterType="PayCheckSheet" resultMap="PayCheckSheetResult">
    SELECT
    t.pay_check_sheet_id,
    t.vbillno,
    t.vbillstatus,
    t.total_amount,
    t.carrier_id,
    t.YEAR,
    t.MONTH,
    t.got_amount,
    t.ungot_amount,
    t.if_check,
    t.check_type,
    t.check_no,
    t.check_head,
    t.check_date,
    t.check_amount,
    t.check_tax_rate,
    t.check_remark,
    t.memo,
    t.unconfirm_type,
    t.unconfirm_memo,
    t.application_amount,
    t.carr_code,
    t.carr_name,
    t.reg_date,
    t.cor_date,
    t.cor_user_id,
    t.del_flag,
    t.del_user_id,
    t.del_date,
    t.hand_verification,
    t.cor_scr_id,
    t.reg_scr_id,
    t.is_ntocc,
    t.pay_check_sheet_name,
    t.oil_amount,
    t.application_amount_oil,
    t.is_close,
    sysUser.user_name as reg_user_id,
    carrier.bala_type,
    t.tax_amount,
    t.tax_amount_oil,
    t.is_fleet_data,
    t.is_exist_fleet_data,
    t.lot_g7_end,
    t.bala_corp,
    carrier.LOCK_PAY carrier_lock_pay,
    CASE WHEN EXISTS (SELECT 1
                        FROM T_PAY_CHECK_SHEET_B a
                   left join T_PAY_DETAIL a1 on a.pay_detail_id = a1.pay_detail_id
                   left join T_EXP_ENTRUST_MORE a2 on a1.LOT_ID = a2.LOT_ID
                        WHERE a.del_falg = 0 and a2.id is not null and a.PAY_CHECK_SHEET_ID=t.PAY_CHECK_SHEET_ID)
        THEN 1 ELSE 0 END  hasExp
    FROM
    t_pay_check_sheet t
    left join sys_user sysUser on t.reg_user_id = sysUser.USER_ID and sysUser.del_flag = 0
    left join M_CARRIER carrier on carrier.carrier_id = t.carrier_id

    <where>
        <if test="balaType != null  and balaType != '' ">  AND carrier.BALA_TYPE = #{balaType}</if>
        <if test="payCheckSheetId != null  and payCheckSheetId != '' ">  t.pay_check_sheet_id = #{payCheckSheetId}</if>
<!--         <if test="vbillno != null  and vbillno != '' ">-->
<!--             <bind name="vbillno" value="vbillno + '%'"/>-->
<!--             and t.vbillno like #{vbillno}-->
<!--         </if>-->

        <if test="vbillno != null  and vbillno != '' and vbillno.indexOf(',') != -1">
            and
            <foreach item="item" index="index" collection="vbillno.split(',')" open="(" separator="or" close=")">
                t.vbillno like concat(trim(#{item}),'%')
            </foreach>
        </if>
        <if test="vbillno != null  and vbillno != '' and vbillno.indexOf(',') == -1">
            and t.vbillno like  concat(trim(#{vbillno}),'%')
        </if>

        <if test="vbillstatus != null "> and t.vbillstatus = #{vbillstatus}</if>
         <if test="carrierId != null  and carrierId != '' "> and t.carrier_id = #{carrierId}</if>
         <if test="year != null and year != ''">
             and t.year = #{year}
         </if>
         <if test="month != null and month != ''">
             and t.month = #{month}
         </if>
         <if test="costAmount != null "> and t.cost_amount = #{costAmount}</if>
         <if test="gotAmount != null "> and t.got_amount = #{gotAmount}</if>
         <if test="ungotAmount != null "> and t.ungot_amount = #{ungotAmount}</if>
         <if test="ifCheck != null  and ifCheck != '' "> and t.if_check = #{ifCheck}</if>
         <if test="checkType != null "> and t.check_type = #{checkType}</if>
         <if test="checkNo != null  and checkNo != '' "> and t.check_no = #{checkNo}</if>
         <if test="checkHead != null  and checkHead != '' "> and t.check_head = #{checkHead}</if>
         <if test="checkDate != null "> and t.check_date = #{checkDate}</if>
         <if test="checkAmount != null "> and t.check_amount = #{checkAmount}</if>
         <if test="checkTaxRate != null "> and t.check_tax_rate = #{checkTaxRate}</if>
         <if test="checkRemark != null  and checkRemark != '' "> and t.check_remark = #{checkRemark}</if>
         <if test="memo != null  and memo != '' "> and t.memo = #{memo}</if>
         <if test="unconfirmType != null "> and t.unconfirm_type = #{unconfirmType}</if>
         <if test="unconfirmMemo != null  and unconfirmMemo != '' "> and t.unconfirm_memo = #{unconfirmMemo}</if>
         <if test="fuelCard != null  and fuelCard != '' "> and t.fuel_card = #{fuelCard}</if>
         <if test="recAccount != null  and recAccount != '' "> and t.rec_account = #{recAccount}</if>
         <if test="recBank != null  and recBank != '' "> and t.rec_bank = #{recBank}</if>
         <if test="recCardNo != null  and recCardNo != '' "> and t.rec_card_no = #{recCardNo}</if>
         <if test="applicationAmount != null "> and t.application_amount = #{applicationAmount}</if>
         <if test="applicationStatus != null "> and t.application_status = #{applicationStatus}</if>
         <if test="payCheckSheetName != null "> and t.pay_check_sheet_name = #{payCheckSheetName}</if>
         <if test="carrCode != null  and carrCode != '' "> and t.carr_code = #{carrCode}</if>
         <if test="carrName != null  and carrName != '' ">
           <bind name="carrName" value="carrName + '%'"/>
             and t.carr_name like #{carrName}
          </if>
         <if test="regUserId != null  and regUserId != '' ">
              and t.reg_user_id = #{regUserId}
         </if>
        <if test="regUserName != null  and regUserName != '' ">
            <bind name="regUserName" value="regUserName + '%'"/>
            and sysUser.user_name like #{regUserName}
        </if>

         <if test="regDate != null "> and t.reg_date = #{regDate}</if>
         <if test="corDate != null "> and t.cor_date = #{corDate}</if>
         <if test="corUserId != null  and corUserId != '' "> and t.cor_user_id = #{corUserId}</if>
         <if test="delFlag != null "> and t.del_flag = #{delFlag}</if>
         <if test="delUserId != null  and delUserId != '' "> and t.del_user_id = #{delUserId}</if>
         <if test="delDate != null "> and t.del_date = #{delDate}</if>
         <if test="handVerification != null  and handVerification != '' "> and t.hand_verification = #{handVerification}</if>
         <if test="corScrId != null  and corScrId != '' "> and t.cor_scr_id = #{corScrId}</if>
         <if test="regScrId != null  and regScrId != '' "> and t.reg_scr_id = #{regScrId}</if>
     </where>
    order by t.REG_DATE  desc
</select>

<select id="selectPayCheckSheetListGroupByCarrier" parameterType="PayCheckSheet" resultMap="PayCheckSheetResult">
    SELECT
    sum(total_amount) as total_amount,
    sum(got_amount) as got_amount ,
    sum(ungot_amount) as ungot_amount ,
    sum(application_amount) as application_amount,
    carr_code,
    carr_name
    FROM
    t_pay_check_sheet
    where DEL_FLAG = 0
    <if test="carrName != null and carrName.trim() != ''">
        <bind name="carrName" value="carrName + '%'"/>
        and carr_name like #{carrName}
    </if>
    group by carr_code,carr_name
</select>

<select id="selectPayCheckSheetById" parameterType="String" resultMap="PayCheckSheetResult">
    <include refid="selectPayCheckSheetVo"/>
    where pay_check_sheet_id = #{payCheckSheetId}
</select>

<select id="selectPayCheckSheetId" parameterType="String" resultType="String">
    select PAY_CHECK_SHEET_ID
    from T_PAY_CHECK_SHEET_B
    where PAY_DETAIL_ID = #{payDetailId}
</select>

<delete id="deleteRelation" parameterType="String">
    delete from T_PAY_CHECK_SHEET_B where PAY_DETAIL_ID = #{payDetailId}
</delete>

<update id="updatePayCheck">
    update t_pay_check_sheet
    <trim prefix="SET" suffixOverrides=",">
        <if test="vbillno != null  and vbillno != ''  ">vbillno = #{vbillno},</if>
        <if test="vbillstatus != null  ">vbillstatus = #{vbillstatus},</if>
        <if test="carrierId != null  and carrierId != ''  ">carrier_id = #{carrierId},</if>
        <if test="year != null  ">year = #{year},</if>
        <if test="month != null  ">month = #{month},</if>
        <if test="totalAmount != null  ">total_amount = #{totalAmount},</if>
        <if test="costAmount != null  ">cost_amount = #{costAmount},</if>
        <if test="gotAmount != null  ">got_amount = #{gotAmount},</if>
        <if test="ungotAmount != null  ">ungot_amount = #{ungotAmount},</if>
        <if test="ifCheck != null  and ifCheck != ''  ">if_check = #{ifCheck},</if>
        <if test="checkType != null  ">check_type = #{checkType},</if>
        <if test="checkNo != null  and checkNo != ''  ">check_no = #{checkNo},</if>
        <if test="checkHead != null  and checkHead != ''  ">check_head = #{checkHead},</if>
        <if test="checkDate != null  ">check_date = #{checkDate},</if>
        <if test="checkAmount != null  ">check_amount = #{checkAmount},</if>
        <if test="checkTaxRate != null  ">check_tax_rate = #{checkTaxRate},</if>
        <if test="checkRemark != null  and checkRemark != ''  ">check_remark = #{checkRemark},</if>
        <if test="memo != null  and memo != ''  ">memo = #{memo},</if>
        <if test="unconfirmType != null  ">unconfirm_type = #{unconfirmType},</if>
        <if test="unconfirmMemo != null  and unconfirmMemo != ''  ">unconfirm_memo = #{unconfirmMemo},</if>
        <if test="fuelCard != null  and fuelCard != ''  ">fuel_card = #{fuelCard},</if>
        <if test="recAccount != null  and recAccount != ''  ">rec_account = #{recAccount},</if>
        <if test="recBank != null  and recBank != ''  ">rec_bank = #{recBank},</if>
        <if test="recCardNo != null  and recCardNo != ''  ">rec_card_no = #{recCardNo},</if>
        <if test="applicationAmount != null  ">application_amount = #{applicationAmount},</if>
        <if test="applicationStatus != null  ">application_status = #{applicationStatus},</if>
        <if test="carrCode != null  and carrCode != ''  ">carr_code = #{carrCode},</if>
        <if test="carrName != null  and carrName != ''  ">carr_name = #{carrName},</if>
        <if test="regUserId != null  and regUserId != ''  ">reg_user_id = #{regUserId},</if>
        <if test="regDate != null  ">reg_date = #{regDate},</if>
        <if test="corDate != null  ">cor_date = #{corDate},</if>
        <if test="corUserId != null  and corUserId != ''  ">cor_user_id = #{corUserId},</if>
        <if test="delFlag != null  ">del_flag = #{delFlag},</if>
        <if test="delUserId != null  and delUserId != ''  ">del_user_id = #{delUserId},</if>
        <if test="delDate != null  ">del_date = #{delDate},</if>
        <if test="handVerification != null  and handVerification != ''  ">hand_verification = #{handVerification},</if>
        <if test="corScrId != null  and corScrId != ''  ">cor_scr_id = #{corScrId},</if>
        <if test="regScrId != null  and regScrId != ''  ">reg_scr_id = #{regScrId},</if>
        <if test="payCheckSheetName != null  and payCheckSheetName != ''  ">pay_check_sheet_name = #{payCheckSheetName},</if>
        <if test="oilAmount != null">oil_amount = #{oilAmount},</if>
        <if test="applicationAmountOil != null">application_amount_oil = #{applicationAmountOil},</if>
        <if test="taxAmount != null  ">tax_amount = #{taxAmount},</if>
        <if test="invoiceNumber != null  and invoiceNumber != ''  ">INVOICE_NUMBER = #{invoiceNumber},</if>
        <if test="tid != null  and tid != ''  ">TID = #{tid},</if>
        <if test="taxMemo != null  and taxMemo != ''  ">TAX_MEMO = #{taxMemo},</if>
        <if test="taxAmountOil != null  ">tax_amount_oil = #{taxAmountOil},</if>
        <if test="invoiceNumberOil != null  and invoiceNumberOil != ''  ">INVOICE_NUMBER_OIL = #{invoiceNumberOil},</if>
        <if test="tidOil != null  and tidOil != ''  ">TID_OIL = #{tidOil},</if>
        <if test="bzjAmount != null  and bzjAmount != ''  ">bzj_amount = #{bzjAmount},</if>
        <if test="taxMemoOil != null  and taxMemoOil != ''  ">TAX_MEMO_OIL = #{taxMemoOil},</if>
    </trim>
    where pay_check_sheet_id = #{payCheckSheetId}
    <if test="params.checkVbillstatus!= null">
        and vbillstatus = #{params.checkVbillstatus}
    </if>
</update>

<update id="updatePayCheckSubtractAmount">
    update t_pay_check_sheet
    <trim prefix="SET" suffixOverrides=",">
        <if test="totalAmount != null  ">total_amount = total_amount - #{totalAmount},</if>
        <if test="ungotAmount != null  ">ungot_amount = ungot_amount - #{ungotAmount},</if>
        <if test="oilAmount != null">oil_amount = oil_amount - #{oilAmount},</if>
        <if test="corDate != null  ">cor_date = #{corDate},</if>
        <if test="corUserId != null  and corUserId != ''  ">cor_user_id = #{corUserId},</if>
        <if test="corScrId != null  and corScrId != ''  ">cor_scr_id = #{corScrId},</if>
    </trim>
    where pay_check_sheet_id = #{payCheckSheetId}
    <if test="params.checkVbillstatus!= null">
        and vbillstatus = #{params.checkVbillstatus}
    </if>
</update>


<!--核销-->
<update id="verification">
    update T_PAY_CHECK_SHEET set
    VBILLSTATUS = #{status}
    where
    HAND_VERIFICATION = 1 and
    PAY_CHECK_SHEET_ID in
    <foreach item="ids" collection="payCheckSheetIds" open="(" separator="," close=")">
        #{ids}
    </foreach>
</update>

<select id="selectPayDetailBySheetId" resultMap="PayDetailResult">
    select distinct  * from (
    SELECT
    t.pay_detail_id,
    t.free_type,
    t.vbillno,
    t.vbillstatus,
    t.LOT_ID,
    t.lotno,
    t.carrier_id,
    t.balatype,
    t.trans_fee_count,
    t.got_amount,
    t.ungot_amount,
    t.memo,
    t.check_no,
    t.check_head,
    t.unconfirm_type,
    t.unconfirm_memo,
    t.confirm_time,
    t.confirm_user,
    t.driver_mobile,
    t.driver_name,
    t.carno,
    t.req_deli_date,
    t.req_arri_date,
    t.carr_code,
    t.carr_name,
    t.oil_card_number,
    t.is_ntocc,
    t.cost_type_on_way,
    t.cost_type_freight,
    case t.cost_type_freight when '0' then '现金'
        when '1' then '油卡'
        when '2' then '现金'
        when '3' then '油卡'
        when '4' then '现金'
        when '5' then '油卡' end as cost_type_freight_name,
    t.tax_amount,
    t.is_close,
    <!--统计委托单所有总件数，总重量，总体积-->
    (select sum(num_count) from t_entrust where t.lot_id = lot_id and del_flag = 0) num_count,
    (select sum(weight_count) from t_entrust where t.lot_id = lot_id and del_flag = 0) weight_count,
    (select sum(volume_count) from t_entrust where t.lot_id = lot_id and del_flag = 0) volume_count,
    <!--运单下收|发货省市区-->
    lot.deli_province_name || lot.deli_city_name || lot.deli_area_name deli_addr,
    lot.arri_province_name || lot.arri_city_name || lot.arri_area_name arri_addr,
    <!--拼接发货单号，以逗号隔开-->
    (SELECT listagg(entrust.INVOICE_VBILLNO,',') WITHIN GROUP(ORDER BY entrust.entrust_id)
    FROM t_entrust entrust where entrust.lot_id = lot.entrust_lot_id
    and entrust.DEL_FLAG = 0) INVOICE_VBILLNO,
     dept.dept_name,
    t.write_off_time,
    t.is_fleet_data,
    t.is_fleet_assign,
    t.fleet_receive_detail_id,
    lot.g7_end  lot_g7_end,
    lot.g7_start lot_g7_start,
    lot.g7_msg lot_g7_msg,
    lot.g7_syn lot_g7_syn,
    lot.pay_way,
    lot.LOCK_PAY lot_lock_pay
    FROM
    t_pay_detail t
    left join T_PAY_CHECK_SHEET_B trcsb on t.PAY_DETAIL_ID = trcsb.PAY_DETAIL_ID and trcsb.DEL_FALG = 0
    left join t_entrust_lot lot on t.lot_id = lot.entrust_lot_id and lot.del_flag = 0
    left join T_COST_ALLOCATION allocation on allocation.PAY_DETAIL_ID = t.PAY_DETAIL_ID
    left join T_INVOICE invoice on invoice.INVOICE_ID = allocation.INVOICE_ID
    left join SYS_DEPT dept on invoice.SALES_DEPT = dept.DEPT_ID
    where
    trcsb.PAY_CHECK_SHEET_ID = #{payCheckSheetId}
    <if test="lotno != null and lotno.trim() != ''">
        <bind name="lotno" value="lotno + '%'"/>
        and t.lotno like #{lotno}
    </if>
    <!--<if test="lotno != null and lotno.trim() != ''">
        <bind name="lotno" value="lotno + '%'"/>
        and t.lotno like #{lotno}
    </if>-->
    <!--根据申请类型 判断应付明细 0：现金 1：油卡  + 在途-->
    <if test='type == "0"'>
        and ( (t.cost_type_freight in ('0','2','4') and t.free_type = '0') or (nvl(t.cost_type_freight,'0') in ('0','2','4') and t.free_type = '1'))
    </if>
    <if test='type == "1"'>
        and ( t.cost_type_freight in ('1','3','5'))
    </if>
    <if test="salesDept != null  and salesDept != '' and salesDept.indexOf(',') != -1">
        and invoice.sales_dept in
        <foreach item="item" index="index" collection="salesDept.split(',')" open="(" separator="," close=")">
            #{item}
        </foreach>
    </if>
    <if test="salesDept != null  and salesDept != '' and salesDept.indexOf(',') == -1">
        and invoice.sales_dept = #{salesDept}
    </if>
    and t.DEL_FLAG = 0
    <!--运费在前，创建时间在前-->
    order by t.free_type asc, t.REG_DATE asc
    ) order by lotno desc,INVOICE_VBILLNO desc,vbillno desc
</select>

    <resultMap id="rm507" type="java.util.Map">
        <result column="bala_corp" property="balaCorp" />
        <result column="sum_oil_fee" property="sumOilFee" javaType="BigDecimal" />
        <result column="sum_cash_fee" property="sumCashFee" javaType="BigDecimal" />
        <result column="sum_issue_fee" property="sumIssueFee" javaType="BigDecimal" />
        <result column="sum_g7_fee" property="sumG7Fee" javaType="BigDecimal" />
        <result column="sum_left_fee" property="sumLeftFee" javaType="BigDecimal" />
        <result column="ungot_oil_fee" property="ungotOilFee" javaType="BigDecimal" />
        <result column="ungot_cash_fee" property="ungotCashFee" javaType="BigDecimal" />
        <result column="ungot_issue_fee" property="ungotIssueFee" javaType="BigDecimal" />
        <result column="ungot_g7_fee" property="ungotG7Fee" javaType="BigDecimal" />
        <result column="ungot_left_fee" property="ungotLeftFee" javaType="BigDecimal" />
        <result column="applied_oil_fee" property="appliedOilFee" javaType="BigDecimal" />
        <result column="applied_cash_fee" property="appliedCashFee" javaType="BigDecimal" />
        <result column="applied_issue_fee" property="appliedIssueFee" javaType="BigDecimal" />
        <result column="applied_g7_fee" property="appliedG7Fee" javaType="BigDecimal" />
        <result column="applied_left_fee" property="appliedLeftFee" javaType="BigDecimal" />
    </resultMap>

    <select id="paySumByBalaCorp" resultMap="rm507">
        select b.bala_corp,
               sum(case when b.cost_type_freight in ('1','3','5') then b.trans_fee_count else 0 end) sum_oil_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') then b.trans_fee_count else 0 end) sum_cash_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') and d.billing_type != '6' then b.trans_fee_count else 0 end) sum_issue_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') and nvl(d.billing_type,'6') = '6' and d.g7_end = 2 and d.g7_qst is null then b.trans_fee_count else 0 end) sum_g7_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') and nvl(d.billing_type,'6') = '6' and (d.g7_end is null or d.g7_end != 2 or d.g7_qst is not null) then b.trans_fee_count else 0 end) sum_left_fee,
               sum(case when b.cost_type_freight in ('1','3','5') then b.ungot_amount else 0 end) ungot_oil_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') then b.ungot_amount else 0 end) ungot_cash_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') and d.billing_type != '6' then b.ungot_amount else 0 end) ungot_issue_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') and nvl(d.billing_type,'6') = '6' and d.g7_end = 2 and d.g7_qst is null then b.ungot_amount else 0 end) ungot_g7_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') and nvl(d.billing_type,'6') = '6' and (d.g7_end is null or d.g7_end != 2 or d.g7_qst is not null) then b.ungot_amount else 0 end) ungot_left_fee,
               sum(case when b.cost_type_freight in ('1','3','5') then nvl(a.applied,0) else 0 end) applied_oil_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') then nvl(a.applied,0) else 0 end) applied_cash_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') and d.billing_type != '6' then nvl(a.applied,0) else 0 end) applied_issue_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') and nvl(d.billing_type,'6') = '6' and d.g7_end = 2 and d.g7_qst is null then nvl(a.applied,0) else 0 end) applied_g7_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') and nvl(d.billing_type,'6') = '6' and (d.g7_end is null or d.g7_end != 2 or d.g7_qst is not null) then nvl(a.applied,0) else 0 end) applied_left_fee
        from T_PAY_CHECK_SHEET_B a
                 inner join t_pay_detail b on b.pay_detail_id = a.pay_detail_id and b.DEL_FLAG = 0
                 inner join t_entrust_lot d on d.entrust_lot_id = b.lot_id
        where a.PAY_CHECK_SHEET_ID = #{payCheckSheetId} and a.DEL_FALG = 0
        group by b.bala_corp
        order by b.bala_corp desc
    </select>

    <resultMap id="rm507plus" type="java.util.Map">
        <result column="bala_corp" property="balaCorp" />
        <result column="carr_bank_id" property="carrBankId" />
        <result column="bank_card" property="bankCard" />
        <result column="bank_account" property="bankAccount" />
        <result column="bank_name" property="bankName" />
        <result column="sum_oil_fee" property="sumOilFee" javaType="BigDecimal" />
        <result column="sum_cash_fee" property="sumCashFee" javaType="BigDecimal" />
        <result column="sum_issue_fee" property="sumIssueFee" javaType="BigDecimal" />
        <result column="sum_g7_fee" property="sumG7Fee" javaType="BigDecimal" />
        <result column="sum_left_fee" property="sumLeftFee" javaType="BigDecimal" />
        <result column="ungot_oil_fee" property="ungotOilFee" javaType="BigDecimal" />
        <result column="ungot_cash_fee" property="ungotCashFee" javaType="BigDecimal" />
        <result column="ungot_issue_fee" property="ungotIssueFee" javaType="BigDecimal" />
        <result column="ungot_g7_fee" property="ungotG7Fee" javaType="BigDecimal" />
        <result column="ungot_left_fee" property="ungotLeftFee" javaType="BigDecimal" />
    </resultMap>

    <select id="paySumByBalaCorpPlus" resultMap="rm507plus">
        select b.bala_corp,
               d.carr_bank_id,
               e.bank_card,
               e.BANK_ACCOUNT,
               e.bank_name,
               sum(case when b.cost_type_freight in ('1','3','5') then b.trans_fee_count else 0 end) sum_oil_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') then b.trans_fee_count else 0 end) sum_cash_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') and d.billing_type != '6' then b.trans_fee_count else 0 end) sum_issue_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') and nvl(d.billing_type,'6') = '6' and d.g7_end = 2 and d.g7_qst is null then b.trans_fee_count else 0 end) sum_g7_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') and nvl(d.billing_type,'6') = '6' and (d.g7_end is null or d.g7_end != 2 or d.g7_qst is not null) then b.trans_fee_count else 0 end) sum_left_fee,
               sum(case when b.cost_type_freight in ('1','3','5') then b.ungot_amount else 0 end) ungot_oil_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') then b.ungot_amount else 0 end) ungot_cash_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') and d.billing_type != '6' then b.ungot_amount else 0 end) ungot_issue_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') and nvl(d.billing_type,'6') = '6' and d.g7_end = 2 and d.g7_qst is null then b.ungot_amount else 0 end) ungot_g7_fee,
               sum(case when nvl(b.cost_type_freight,'0') in ('0','2','4') and nvl(d.billing_type,'6') = '6' and (d.g7_end is null or d.g7_end != 2 or d.g7_qst is not null) then b.ungot_amount else 0 end) ungot_left_fee
        from T_PAY_CHECK_SHEET_B a
                 inner join t_pay_detail b on b.pay_detail_id = a.pay_detail_id and b.DEL_FLAG = 0
                 inner join t_entrust_lot d on d.entrust_lot_id = b.lot_id
                 left join m_carr_bank e on e.CARR_BANK_ID = d.carr_bank_id and nvl(b.cost_type_freight,'0') in ('0','2','4')
        where a.PAY_CHECK_SHEET_ID = #{payCheckSheetId} and a.DEL_FALG = 0
        group by b.bala_corp,d.carr_bank_id, e.bank_card, e.BANK_ACCOUNT, e.bank_name
        order by b.bala_corp desc, e.BANK_ACCOUNT
    </select>


<select id="selectPaySumDetailBySheetId" resultType="java.math.BigDecimal">
    SELECT
        sum(cost_share)
    FROM
    t_pay_detail t
    left join T_PAY_CHECK_SHEET_B trcsb on t.PAY_DETAIL_ID = trcsb.PAY_DETAIL_ID and trcsb.DEL_FALG = 0
    left join t_entrust_lot lot on t.lot_id = lot.entrust_lot_id and lot.del_flag = 0
    left join T_COST_ALLOCATION costAllocation on t.PAY_DETAIL_ID = costAllocation.PAY_DETAIL_ID
    left join M_CUSTOMER cust on costAllocation.CUST_CODE = cust.CUST_CODE
    where
    trcsb.PAY_CHECK_SHEET_ID = #{payCheckSheetId}
    <if test="lotno != null and lotno.trim() != ''">
        <bind name="lotno" value="lotno + '%'"/>
        and t.lotno like #{lotno}
    </if>
    <!--<if test="lotno != null and lotno.trim() != ''">
        <bind name="lotno" value="lotno + '%'"/>
        and t.lotno like #{lotno}
    </if>-->
    <!--根据申请类型 判断应付明细 0：现金 1：油卡  + 在途-->
    <if test="type != null and type != '' and type == 0">
        and ( t.cost_type_freight not in ('1','3','5') or t.cost_type_freight is null /*or t.free_type = 1*/)
    </if>
    <if test="type != null and type != '' and type == 1">
        and ( t.cost_type_freight in ('1','3','5') /*or t.free_type = 1*/)
    </if>
    <if test="balaCorp != null and balaCorp != ''">
        and t.BALA_CORP = #{balaCorp}
    </if>
    and t.DEL_FLAG = 0
    and costAllocation.del_flag = 0
    <!--运费在前，创建时间在前-->
    order by t.free_type asc, t.REG_DATE asc
</select>

<select id="exportPayDetailBySheetId" resultType="PayDetailExportVO">
    select * from(
    SELECT distinct
    t.free_type freeType,
    t.vbillno vbillno,
    t.vbillstatus vbillstatus,
    t.lotno lotno,
    t.trans_fee_count transFeeCount,
    t.got_amount gotAmount,
    t.ungot_amount ungotAmount,
    t.memo memo,
    t.carno carno,
    t.driver_mobile driverMobile,
    t.driver_name driverName,
    t.req_deli_date reqDeliDate,
    t.req_arri_date reqArriDate,
    t.carr_name carrName,
    t.write_off_time writeOffTime,
    <!--统计委托单所有总件数，总重量，总体积-->
    (select sum(num_count) from t_entrust where t.lot_id = lot_id and del_flag = 0) numCount,
    (select sum(weight_count) from t_entrust where t.lot_id = lot_id and del_flag = 0) weightCount,
    (select sum(volume_count) from t_entrust where t.lot_id = lot_id and del_flag = 0) volumeCount,
    <!--运单下收|发货省市区-->
    lot.deli_province_name || lot.deli_city_name || lot.deli_area_name deliAddr,
    lot.arri_province_name || lot.arri_city_name || lot.arri_area_name arriAddr,
    <!--拼接发货单号，以逗号隔开-->
    (SELECT listagg(entrust.INVOICE_VBILLNO,',') WITHIN GROUP(ORDER BY entrust.entrust_id)
    FROM t_entrust entrust where entrust.lot_id = lot.entrust_lot_id
    and entrust.DEL_FLAG = 0) invoiceVbillno,
    dept.dept_name deptName,
    <!--收款类型 方便导出-->
    case
    when t.free_type = '0' and t.cost_type_freight = '0' then
    '预付现金'
    when t.free_type = '0' and t.cost_type_freight = '1' then
    '预付油卡'
    when t.free_type = '0' and t.cost_type_freight = '2' then
    '到付现金'
    when t.free_type = '0' and t.cost_type_freight = '3' then
    '到付油卡'
    when t.free_type = '0' and t.cost_type_freight = '4' then
    '回付现金'
    when t.free_type = '0' and t.cost_type_freight = '5' then
    '回付油卡'

    when t.free_type = '2' then
    '调整费'

    when t.free_type = '1' and t.cost_type_on_way = '1' then
    '装卸费'
    when t.free_type = '1' and t.cost_type_on_way = '2' then
    '提货费'
    when t.free_type = '1' and t.cost_type_on_way = '3' then
    '放空费'
    when t.free_type = '1' and t.cost_type_on_way = '4' then
    '停车费'
    when t.free_type = '1' and t.cost_type_on_way = '5' then
    '进门费'
    when t.free_type = '1' and t.cost_type_on_way = '6' then
    '信息费'
    when t.free_type = '1' and t.cost_type_on_way = '7' then
    '过磅费'
    when t.free_type = '1' and t.cost_type_on_way = '8' then
    '过路费'
    when t.free_type = '1' and t.cost_type_on_way = '9' then
    '仓储费'
    when t.free_type = '1' and t.cost_type_on_way = '10' then
    '其它费'
    when t.free_type = '1' and t.cost_type_on_way = '11' then
    '司机代收'
    when t.free_type = '1' and t.cost_type_on_way = '12' then
    '改送费'
    when t.free_type = '1' and t.cost_type_on_way = '13' then
    '误工费'
    when t.free_type = '1' and t.cost_type_on_way = '14' then
    '客户赔偿'
    when t.free_type = '1' and t.cost_type_on_way = '16' then
    '中转费'
    when t.free_type = '1' and t.cost_type_on_way = '17' then
    '回单扣款'
    when t.free_type = '1' and t.cost_type_on_way = '18' then
    '异常扣款'
    when t.free_type = '1' and t.cost_type_on_way = '19' then
    '货损扣款'
    else
    '其它'
    END costType,
    case t.cost_type_freight when '0' then '现金'
    when '1' then '油卡'
    when '2' then '现金'
    when '3' then '油卡'
    when '4' then '现金'
    when '5' then '油卡' end as costTypeFreightName
    FROM
    t_pay_detail t
    left join T_PAY_CHECK_SHEET_B trcsb on t.PAY_DETAIL_ID = trcsb.PAY_DETAIL_ID and trcsb.del_falg = 0
    left join t_entrust_lot lot on t.lot_id = lot.entrust_lot_id and lot.del_flag = 0
    left join T_COST_ALLOCATION allocation on allocation.PAY_DETAIL_ID = t.PAY_DETAIL_ID
    left join T_INVOICE invoice on invoice.INVOICE_ID = allocation.INVOICE_ID
    left join SYS_DEPT dept on invoice.SALES_DEPT = dept.DEPT_ID
    where
    trcsb.PAY_CHECK_SHEET_ID = #{payCheckSheetId}
    <if test="lotno != null and lotno.trim() != ''">
        <bind name="lotno" value="lotno + '%'"/>
        and t.lotno like #{lotno}
    </if>
    and t.DEL_FLAG = 0
    ) order by lotno desc,vbillno desc
</select>

<select id="selectPayDetailBySheetIdAmountCount" resultType="Map">
    SELECT
    sum( t.trans_fee_count) trans_fee_count,
    sum( t.got_amount) got_amount,
    sum( t.ungot_amount) ungot_amount
    FROM
    t_pay_detail t
    left join T_PAY_CHECK_SHEET_B trcsb on t.PAY_DETAIL_ID = trcsb.PAY_DETAIL_ID and trcsb.del_falg = 0
    left join t_entrust_lot lot on t.lot_id = lot.entrust_lot_id and lot.del_flag = 0
    where
    trcsb.PAY_CHECK_SHEET_ID = #{payCheckSheetId}
    <if test="lotno != null and lotno.trim() != ''">
        <bind name="lotno" value="lotno + '%'"/>
        and t.lotno like #{lotno}
    </if>
    <if test='type == "0"'>
        and ( (t.cost_type_freight in ('0','2','4') and t.free_type = '0') or (nvl(t.cost_type_freight,'0') in ('0','2','4') and t.free_type = '1'))
    </if>
    <if test='type == "1"'>
        and ( t.cost_type_freight in ('1','3','5'))
    </if>
    and t.DEL_FLAG = 0
    order by t.REG_DATE asc
</select>

<!-- 获取 Sequence  -->
<select id="getSeq" resultType="java.lang.String">
    select SEQ_PAY_SHEET.nextval from dual
</select>

<select id="selectPayCheckSheetByCarrierId" parameterType="String" resultMap="PayCheckSheetResult">
    <include refid="selectPayCheckSheetVo"/>
    where carrier_id = #{carrierId}
</select>

<!--确认对账-->
<update id="affirm">
    update t_pay_check_sheet
    <trim prefix="SET" suffixOverrides=",">
        <if test="vbillno != null  and vbillno != ''  ">vbillno = #{vbillno},</if>
        <if test="vbillstatus != null  ">vbillstatus = #{vbillstatus},</if>
        <if test="costAmount != null  ">cost_amount = #{costAmount},</if>
        <if test="gotAmount != null  ">got_amount = #{gotAmount},</if>
        <if test="ungotAmount != null  ">ungot_amount = #{ungotAmount},</if>
        <if test="ifCheck != null  and ifCheck != ''  ">if_check = #{ifCheck},</if>
        <if test="checkType != null  ">check_type = #{checkType},</if>
        <if test="checkNo != null  and checkNo != ''  ">check_no = #{checkNo},</if>
        <if test="checkHead != null  and checkHead != ''  ">check_head = #{checkHead},</if>
        <if test="checkDate != null  ">check_date = #{checkDate},</if>
        <if test="checkAmount != null  ">check_amount = #{checkAmount},</if>
        <if test="checkTaxRate != null  ">check_tax_rate = #{checkTaxRate},</if>
        <if test="checkRemark != null  and checkRemark != ''  ">check_remark = #{checkRemark},</if>
        <if test="memo != null  and memo != ''  ">memo = #{memo},</if>
        <if test="unconfirmType != null  ">unconfirm_type = #{unconfirmType},</if>
        <if test="unconfirmMemo != null  and unconfirmMemo != ''  ">unconfirm_memo = #{unconfirmMemo},</if>
        <if test="corDate != null  ">cor_date = #{corDate},</if>
        <if test="corUserId != null  and corUserId != ''  ">cor_user_id = #{corUserId},</if>
        <if test="handVerification != null  and handVerification != ''  ">hand_verification = #{handVerification},</if>
        <if test="corScrId != null  and corScrId != ''  ">cor_scr_id = #{corScrId},</if>
        <if test="regScrId != null  and regScrId != ''  ">reg_scr_id = #{regScrId},</if>
        <if test="payCheckSheetName != null  and payCheckSheetName != ''  ">pay_check_sheet_name = #{payCheckSheetName},</if>
    </trim>
    where  vbillstatus = 0 and pay_check_sheet_id = #{payCheckSheetId}
</update>

<!--反确认对账-->
<update id="reverse">
    update t_pay_check_sheet
    <trim prefix="SET" suffixOverrides=",">
        <if test="vbillno != null  and vbillno != ''  ">vbillno = #{vbillno},</if>
        <if test="vbillstatus != null  ">vbillstatus = #{vbillstatus},</if>
        <if test="costAmount != null  ">cost_amount = #{costAmount},</if>
        <if test="gotAmount != null  ">got_amount = #{gotAmount},</if>
        <if test="ungotAmount != null  ">ungot_amount = #{ungotAmount},</if>
        <if test="ifCheck != null  and ifCheck != ''  ">if_check = #{ifCheck},</if>
        <if test="checkType != null  ">check_type = #{checkType},</if>
        <if test="checkNo != null  and checkNo != ''  ">check_no = #{checkNo},</if>
        <if test="checkHead != null  and checkHead != ''  ">check_head = #{checkHead},</if>
        <if test="checkDate != null  ">check_date = #{checkDate},</if>
        <if test="checkAmount != null  ">check_amount = #{checkAmount},</if>
        <if test="checkTaxRate != null  ">check_tax_rate = #{checkTaxRate},</if>
        <if test="checkRemark != null  and checkRemark != ''  ">check_remark = #{checkRemark},</if>
        <if test="memo != null  and memo != ''  ">memo = #{memo},</if>
        <if test="unconfirmType != null  ">unconfirm_type = #{unconfirmType},</if>
        <if test="unconfirmMemo != null  and unconfirmMemo != ''  ">unconfirm_memo = #{unconfirmMemo},</if>
        <if test="corDate != null  ">cor_date = #{corDate},</if>
        <if test="corUserId != null  and corUserId != ''  ">cor_user_id = #{corUserId},</if>
        <if test="handVerification != null  and handVerification != ''  ">hand_verification = #{handVerification},</if>
        <if test="corScrId != null  and corScrId != ''  ">cor_scr_id = #{corScrId},</if>
        <if test="regScrId != null  and regScrId != ''  ">reg_scr_id = #{regScrId},</if>
        <if test="payCheckSheetName != null  and payCheckSheetName != ''  ">pay_check_sheet_name = #{payCheckSheetName},</if>
    </trim>
    where
    vbillstatus = 1 and pay_check_sheet_id = #{payCheckSheetId}
</update>

<!--承运端对账单-->
<select id="selectPayCheckSheetListByCarrierId" parameterType="PayCheckSheet" resultMap="PayCheckSheetResult">
    SELECT
    t.pay_check_sheet_id,
    t.vbillno,
    t.vbillstatus,
    t.total_amount,
    t.YEAR,
    t.MONTH,
    t.got_amount,
    t.ungot_amount,
    t.if_check,
    t.memo,
    t.carr_name,
    t.APPLICATION_AMOUNT,
    T.HAND_VERIFICATION
    FROM
    t_pay_check_sheet t
    <where>
        and t.del_flag=0
        <if test="carrierId != null  and carrierId != '' ">and t.carrier_id = #{carrierId}</if>
        <if test="year != null and year != ''">
            and t.year = #{year}
        </if>
        <if test="month != null and month != ''">
            and t.month = #{month}
        </if>
    </where>
    order by t.COR_DATE desc
</select>

<select id="selectPayCheckSheetByPayDetailId" resultMap="PayCheckSheetResult">
    SELECT
    t.pay_check_sheet_id,
    t.vbillno,
    t.vbillstatus,
    t.total_amount,
    t.YEAR,
    t.MONTH,
    t.got_amount,
    t.ungot_amount,
    t.if_check,
    t.memo,
    t.carr_name,
    t.APPLICATION_AMOUNT,
    T.HAND_VERIFICATION
    FROM
    t_pay_check_sheet t
    where pay_check_sheet_id =
    (
    select T_PAY_CHECK_SHEET_B.PAY_CHECK_SHEET_ID from T_PAY_CHECK_SHEET_B where PAY_DETAIL_ID = #{payDetailId} AND DEL_FALG = 0
    )
</select>

<!--查询月账单 应付明细-->
<select id="selectStatementDetailById" parameterType="String" resultType="PayDetail">
    select
    t_pay_detail.vbillstatus vbillstatus,
    t_pay_detail.vbillno,
    t_pay_detail.trans_fee_count transFeecount,
    t_pay_detail.got_amount gotAmount,
    t_pay_detail.ungot_amount ungotAmount,
    t_pay_detail.free_type freeType,
    t_pay_detail.cost_type_freight costTypeFreight,
    t_pay_detail.cost_type_on_way costTypeOnWay
    from t_pay_detail
    left join t_pay_check_sheet_b on t_pay_check_sheet_b.pay_detail_id = t_pay_detail.pay_detail_id
    and t_pay_check_sheet_b.DEL_FALG=0
    where t_pay_check_sheet_b.pay_check_sheet_id = #{payCheckSheetId} and t_pay_detail.DEL_FLAG=0
</select>

<select id="selectPayDetailBypayCheckSheetId" resultType="com.ruoyi.tms.domain.finance.PayDetail">
    SELECT T.PAY_DETAIL_ID payDetailId,
           T.VBILLNO vbillno,
           T.VBILLSTATUS vbillstatus,
           T.FREE_TYPE freeType,
           T.COST_TYPE_ON_WAY costTypeOnWay,
           T.COST_TYPE_FREIGHT costTypeFreight,
           T.LOT_ID lotId,
           T.LOTNO lotno,
           T.CARRIER_ID carrierId,
           T.CARR_CODE carrCode,
           T.CARR_NAME carrName,
           T.BALA_CORP balaCorp,
           T.BALATYPE balatype,
           T.TRANS_FEE_COUNT transFeeCount,
           T.GOT_AMOUNT gotAmount,
           T.UNGOT_AMOUNT ungotAmount,
           T.OIL_CARD_NUMBER oilCardNumber,
           T.CHECK_NO checkNo,
           T.CHECK_HEAD checkHead,
           T.UNCONFIRM_TYPE unconfirmType,
           T.UNCONFIRM_MEMO unconfirmMemo,
           T.CONFIRM_TIME confirmTime,
           T.CONFIRM_USER confirmUser,
           T.DRIVER_MOBILE driverMobile,
           T.DRIVER_NAME driverName,
           T.CARNO carno,
           T.REQ_DELI_DATE reqDeliDate,
           T.REQ_ARRI_DATE reqArriDate,
           T.IS_CLOSE isClose,
           T.IS_ADJUST isAdjust,
           T.ADJUST_MEMO adjustMemo,
           T.MEMO memo,
           T.REG_USER_ID regUserId,
           T.REG_DATE regDate,
           T.REG_SCR_ID regScrId,
           T.COR_USER_ID corUserId,
           T.COR_DATE corDate,
           T.COR_SCR_ID corScrId,
           T.DEL_FLAG delFlag,
           T.DEL_DATE delDate,
           T.DEL_USER_ID delUserId,
           T.REC_CARD_NO recCardNo,
           T.REC_ACCOUNT recAccount,
           T.REC_BANK recBank,
           T.IS_NTOCC isNtocc,
           T.CARR_BANK_ID carrBankId,
           T.APPLY_TIME applyTime,
           T.APPLY_USER applyUser,
           T.BAKC_WRITE_TYPE bakcWriteType,
           T.BACK_WRITE_MEMO backWriteMemo,
           T.BACK_WRITE_TIME backWriteTime,
           T.REQ_PAY_DATE reqPayDate,
           T.IS_FLEET_DATA isFleetData,
           T.IS_FLEET_ASSIGN isFleetAssign,
           T.FLEET_RECEIVE_DETAIL_ID fleetReceiveDetailId,
           t.tax_amount taxAmount
      FROM T_PAY_DETAIL T
      LEFT JOIN T_PAY_CHECK_SHEET_B T1
        ON T.PAY_DETAIL_ID = T1.PAY_DETAIL_ID
    WHERE T1.PAY_CHECK_SHEET_ID = #{payCheckSheetId}
      and t.DEL_FLAG = 0
</select>

<select id="selectLotBypayCheckSheetId" resultType="EntrustLot">
    select distinct
        lot.ENTRUST_LOT_ID entrustLotId,
        lot.LOT,
        lot.REQ_DELI_DATE reqDeliDate,
        lot.DELI_PROVINCE_NAME deliProvinceName,
        lot.DELI_CITY_NAME deliCityName,
        lot.DELI_AREA_NAME deliAreaName,
        lot.ARRI_PROVINCE_NAME arriProvinceName,
        lot.ARRI_CITY_NAME arriCityName,
        lot.ARRI_AREA_NAME arriAreaName,
        lot.NUM_COUNT numCount,
        lot.WEIGHT_COUNT weightCount,
        lot.VOLUME_COUNT volumeCount,
        lot.UNIT_PRICE unitPrice,
        costAllo.COST_SHARE costAmount,
        costAllo.oilAmount oilAmount,
        lot.CAR_LEN_NAME carLenName,
        lot.CAR_TYPE_NAME carTypeName,
        ( select listagg(GOODS_NAME)  within GROUP ( ORDER BY GOODS_NAME ) AS GOODS_NAME
            from (
            select distinct goods.GOODS_NAME
         from T_ENT_PACK_GOODS goods
                  left join T_ENTRUST entrust on goods.ENTRUST_ID = entrust.ENTRUST_ID
            where entrust.lot_id = lot.ENTRUST_LOT_ID)) goodsName
    from T_PAY_CHECK_SHEET payCheckSheet
             left join T_PAY_CHECK_SHEET_B checkSheetB
                       on payCheckSheet.PAY_CHECK_SHEET_ID = checkSheetB.PAY_CHECK_SHEET_ID
             left join t_pay_detail payDetail on checkSheetB.PAY_DETAIL_ID = payDetail.PAY_DETAIL_ID
             left join T_ENTRUST_LOT lot on lot.entrust_lot_id = payDetail.lot_id
left join (
    select lot_id,sum(COST_SHARE) COST_SHARE ,sum(oilAmount) oilAmount from (
    select LOT_ID, COST_SHARE, case when COST_TYPE_FREIGHT in (1, 3, 5) then COST_SHARE else 0 end as oilAmount
    from T_COST_ALLOCATION where del_flag = 0) group by lot_id) costAllo on costAllo.LOT_ID = lot.ENTRUST_LOT_ID
    where checkSheetB.DEL_FALG = 0 and payDetail.DEL_FLAG = 0
      and payCheckSheet.PAY_CHECK_SHEET_ID = #{payCheckSheetId}
</select>

<!--统计承运商对账 查询列表金额-->
<select id="selectPayCheckSheetAmountCount"  resultType="Map">
    SELECT
    sum(nvl(t.TOTAL_AMOUNT,0)) TOTAL_AMOUNT,
    sum(nvl(t.OIL_AMOUNT,0)) OIL_AMOUNT,
    sum(nvl(t.TOTAL_AMOUNT,0)-nvl(t.OIL_AMOUNT,0)) CASH_AMOUNT,
    sum(nvl(t.APPLICATION_AMOUNT_OIL,0)) APPLICATION_AMOUNT_OIL,
    sum(nvl(t.APPLICATION_AMOUNT,0)) APPLICATION_AMOUNT,
    sum(nvl(t.GOT_AMOUNT,0)) GOT_AMOUNT,
    sum(nvl(t.UNGOT_AMOUNT,0)) UNGOT_AMOUNT,
    sum(nvl(t.APPLICATION_AMOUNT_OIL,0) + nvl(t.APPLICATION_AMOUNT,0)) AMOUNT_APPLIED
    FROM
    t_pay_check_sheet t
    left join sys_user sysUser on t.reg_user_id = sysUser.USER_ID and sysUser.del_flag = 0
    <where>
        t.del_flag = 0
        <if test="payCheckSheetId != null  and payCheckSheetId != '' ">  t.pay_check_sheet_id = #{payCheckSheetId}</if>
        <if test="vbillno != null  and vbillno != '' ">
            <bind name="vbillno" value="vbillno + '%'"/>
            and t.vbillno like #{vbillno}
        </if>
        <if test="vbillstatus != null "> and t.vbillstatus = #{vbillstatus}</if>
        <if test="carrierId != null  and carrierId != '' "> and t.carrier_id = #{carrierId}</if>
        <if test="year != null and year != ''">
            and t.year = #{year}
        </if>
        <if test="month != null and month != ''">
            and t.month = #{month}
        </if>
        <if test="costAmount != null "> and t.cost_amount = #{costAmount}</if>
        <if test="gotAmount != null "> and t.got_amount = #{gotAmount}</if>
        <if test="ungotAmount != null "> and t.ungot_amount = #{ungotAmount}</if>
        <if test="ifCheck != null  and ifCheck != '' "> and t.if_check = #{ifCheck}</if>
        <if test="checkType != null "> and t.check_type = #{checkType}</if>
        <if test="checkNo != null  and checkNo != '' "> and t.check_no = #{checkNo}</if>
        <if test="checkHead != null  and checkHead != '' "> and t.check_head = #{checkHead}</if>
        <if test="checkDate != null "> and t.check_date = #{checkDate}</if>
        <if test="checkAmount != null "> and t.check_amount = #{checkAmount}</if>
        <if test="checkTaxRate != null "> and t.check_tax_rate = #{checkTaxRate}</if>
        <if test="checkRemark != null  and checkRemark != '' "> and t.check_remark = #{checkRemark}</if>
        <if test="memo != null  and memo != '' "> and t.memo = #{memo}</if>
        <if test="unconfirmType != null "> and t.unconfirm_type = #{unconfirmType}</if>
        <if test="unconfirmMemo != null  and unconfirmMemo != '' "> and t.unconfirm_memo = #{unconfirmMemo}</if>
        <if test="fuelCard != null  and fuelCard != '' "> and t.fuel_card = #{fuelCard}</if>
        <if test="recAccount != null  and recAccount != '' "> and t.rec_account = #{recAccount}</if>
        <if test="recBank != null  and recBank != '' "> and t.rec_bank = #{recBank}</if>
        <if test="recCardNo != null  and recCardNo != '' "> and t.rec_card_no = #{recCardNo}</if>
        <if test="applicationAmount != null "> and t.application_amount = #{applicationAmount}</if>
        <if test="applicationStatus != null "> and t.application_status = #{applicationStatus}</if>
        <if test="payCheckSheetName != null "> and t.pay_check_sheet_name = #{payCheckSheetName}</if>
        <if test="carrCode != null  and carrCode != '' "> and t.carr_code = #{carrCode}</if>
        <if test="carrName != null  and carrName != '' ">
            <bind name="carrName" value="carrName + '%'"/>
            and t.carr_name like #{carrName}

        </if>
        <if test="handVerification != null  and handVerification != '' "> and t.hand_verification = #{handVerification}</if>
    </where>
    order by t.REG_DATE  desc
</select>


    <select id="selectPayCheckSheetAmountSingleCount"  resultType="Map">
        SELECT
        sum(nvl(t.TOTAL_AMOUNT,0)) TOTAL_AMOUNT,
        sum(nvl(t.OIL_AMOUNT,0)) OIL_AMOUNT,
        sum(nvl(t.TOTAL_AMOUNT,0)-nvl(t.OIL_AMOUNT,0)) CASH_AMOUNT,
        sum(nvl(t.APPLICATION_AMOUNT_OIL,0)) APPLICATION_AMOUNT_OIL,
        sum(nvl(t.APPLICATION_AMOUNT,0)) APPLICATION_AMOUNT,
        sum(nvl(t.GOT_AMOUNT,0)) GOT_AMOUNT,
        sum(nvl(t.UNGOT_AMOUNT,0)) UNGOT_AMOUNT,
        sum(nvl(t.APPLICATION_AMOUNT_OIL,0) + nvl(t.APPLICATION_AMOUNT,0)) AMOUNT_APPLIED
        FROM
        t_pay_check_sheet t
        left join sys_user sysUser on t.reg_user_id = sysUser.USER_ID and sysUser.del_flag = 0
        left join M_CARRIER carrier on carrier.carrier_id = t.carrier_id
        <where>
            AND carrier.BALA_TYPE = 1
            <if test="payCheckSheetId != null  and payCheckSheetId != '' ">  t.pay_check_sheet_id = #{payCheckSheetId}</if>
            <if test="vbillno != null  and vbillno != '' ">
                <bind name="vbillno" value="vbillno + '%'"/>
                and t.vbillno like #{vbillno}
            </if>
            <if test="vbillstatus != null "> and t.vbillstatus = #{vbillstatus}</if>
            <if test="carrierId != null  and carrierId != '' "> and t.carrier_id = #{carrierId}</if>
            <if test="year != null and year != ''">
                and t.year = #{year}
            </if>
            <if test="month != null and month != ''">
                and t.month = #{month}
            </if>
            <if test="regUserId != null  and regUserId != '' ">
                and t.reg_user_id = #{regUserId}
            </if>
            <if test="regUserName != null  and regUserName != '' ">
                <bind name="regUserName" value="regUserName + '%'"/>
                and sysUser.user_name like #{regUserName}
            </if>
            <if test="costAmount != null "> and t.cost_amount = #{costAmount}</if>
            <if test="gotAmount != null "> and t.got_amount = #{gotAmount}</if>
            <if test="ungotAmount != null "> and t.ungot_amount = #{ungotAmount}</if>
            <if test="ifCheck != null  and ifCheck != '' "> and t.if_check = #{ifCheck}</if>
            <if test="checkType != null "> and t.check_type = #{checkType}</if>
            <if test="checkNo != null  and checkNo != '' "> and t.check_no = #{checkNo}</if>
            <if test="checkHead != null  and checkHead != '' "> and t.check_head = #{checkHead}</if>
            <if test="checkDate != null "> and t.check_date = #{checkDate}</if>
            <if test="checkAmount != null "> and t.check_amount = #{checkAmount}</if>
            <if test="checkTaxRate != null "> and t.check_tax_rate = #{checkTaxRate}</if>
            <if test="checkRemark != null  and checkRemark != '' "> and t.check_remark = #{checkRemark}</if>
            <if test="memo != null  and memo != '' "> and t.memo = #{memo}</if>
            <if test="unconfirmType != null "> and t.unconfirm_type = #{unconfirmType}</if>
            <if test="unconfirmMemo != null  and unconfirmMemo != '' "> and t.unconfirm_memo = #{unconfirmMemo}</if>
            <if test="fuelCard != null  and fuelCard != '' "> and t.fuel_card = #{fuelCard}</if>
            <if test="recAccount != null  and recAccount != '' "> and t.rec_account = #{recAccount}</if>
            <if test="recBank != null  and recBank != '' "> and t.rec_bank = #{recBank}</if>
            <if test="recCardNo != null  and recCardNo != '' "> and t.rec_card_no = #{recCardNo}</if>
            <if test="applicationAmount != null "> and t.application_amount = #{applicationAmount}</if>
            <if test="applicationStatus != null "> and t.application_status = #{applicationStatus}</if>
            <if test="payCheckSheetName != null "> and t.pay_check_sheet_name = #{payCheckSheetName}</if>
            <if test="carrCode != null  and carrCode != '' "> and t.carr_code = #{carrCode}</if>
            <if test="carrName != null  and carrName != '' ">
                <bind name="carrName" value="carrName + '%'"/>
                and t.carr_name like #{carrName}

            </if>
            <if test="handVerification != null  and handVerification != '' "> and t.hand_verification = #{handVerification}</if>
        </where>
        order by t.REG_DATE  desc
    </select>

<update id="updatePayCheckSheetAmount">
    update t_pay_check_sheet
    <trim prefix="SET" suffixOverrides=",">
        <if test="applicationAmount != null  ">
            application_amount = (#{applicationAmount} + nvl(application_amount,0)),
        </if>
        <if test="regUserId != null  and regUserId != ''  ">reg_user_id = #{regUserId},</if>
        <if test="regDate != null  ">reg_date = #{regDate},</if>
        <if test="corDate != null  ">cor_date = #{corDate},</if>
        <if test="corUserId != null  and corUserId != ''  ">cor_user_id = #{corUserId},</if>
        <if test="corScrId != null  and corScrId != ''  ">cor_scr_id = #{corScrId},</if>
        <if test="regScrId != null  and regScrId != ''  ">reg_scr_id = #{regScrId},</if>
    </trim>
    where pay_check_sheet_id = #{payCheckSheetId}
    and #{applicationAmount} <![CDATA[ <= ]]> (nvl(TOTAL_AMOUNT,0) - nvl(OIL_AMOUNT,0) - nvl(application_amount,0)+${taxAmountCnt})
</update>

<update id="updatePayCheckSheetAmountOil">
    update t_pay_check_sheet
    <trim prefix="SET" suffixOverrides=",">
        <if test="applicationAmountOil != null">
            application_amount_oil = (#{applicationAmountOil} + nvl(application_amount_oil,0)),
        </if>
        <if test="regUserId != null  and regUserId != ''  ">reg_user_id = #{regUserId},</if>
        <if test="regDate != null  ">reg_date = #{regDate},</if>
        <if test="corDate != null  ">cor_date = #{corDate},</if>
        <if test="corUserId != null  and corUserId != ''  ">cor_user_id = #{corUserId},</if>
        <if test="corScrId != null  and corScrId != ''  ">cor_scr_id = #{corScrId},</if>
        <if test="regScrId != null  and regScrId != ''  ">reg_scr_id = #{regScrId},</if>
    </trim>
    where pay_check_sheet_id = #{payCheckSheetId}
    and #{applicationAmountOil} <![CDATA[ <= ]]> (nvl(OIL_AMOUNT,0) - nvl(application_amount_oil,0)+${taxAmountCnt})
</update>

<insert id="insertTaxRecord">
    insert into T_TAX_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="taxRecordId != null  and taxRecordId != ''  ">id,</if>
        <if test="payCheckSheetId != null  and payCheckSheetId != ''  ">PAY_CHECK_SHEET_ID,</if>
        <if test="taxAmount != null">TAX_AMOUNT,</if>
        <if test="tid != null  and tid != ''  ">tid,</if>
        <if test="invoiceNumber != null  and invoiceNumber != ''  ">INVOICE_NUMBER,</if>
        <if test="taxMemo != null and taxMemo != '' ">TAX_MEMO,</if>
        <if test="regUserId != null  and regUserId != ''  ">reg_user_id,</if>
        <if test="regDate != null  ">reg_date,</if>
        <if test="regScrId != null  and regScrId != ''  ">reg_scr_id,</if>
        <if test="adjustType != null  and adjustType != ''  ">ADJUST_TYPE,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="taxRecordId != null  and taxRecordId != ''  ">#{taxRecordId},</if>
        <if test="payCheckSheetId != null  and payCheckSheetId != ''  ">#{payCheckSheetId},</if>
        <if test="taxAmount != null">#{taxAmount},</if>
        <if test="tid != null  and tid != ''  ">#{tid},</if>
        <if test="invoiceNumber != null  and invoiceNumber != ''  ">#{invoiceNumber},</if>
        <if test="taxMemo != null and taxMemo != '' ">#{taxMemo},</if>
        <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
        <if test="regDate != null  ">#{regDate},</if>
        <if test="regScrId != null  and regScrId != ''  ">#{regScrId},</if>
        <if test="adjustType != null  and adjustType != ''  ">#{adjustType},</if>
    </trim>
</insert>

<update id="updatePayCheckTaxRevoke">
    update t_pay_check_sheet
    set
    VBILLSTATUS = 1,
    UNGOT_AMOUNT = UNGOT_AMOUNT - nvl(TAX_AMOUNT,0),
    TAX_AMOUNT = null,
    INVOICE_NUMBER = null,
    TID = null,
    TAX_MEMO = null,
    reg_user_id = #{regUserId,jdbcType=VARCHAR},
    REG_DATE = sysdate,
    REG_SCR_ID = #{regScrId,jdbcType=VARCHAR}
    where PAY_CHECK_SHEET_ID = #{payCheckSheetId}
</update>

<update id="updatePayCheckTaxRevokeOil">
    update t_pay_check_sheet
    set
    VBILLSTATUS = 1,
    UNGOT_AMOUNT = UNGOT_AMOUNT - nvl(TAX_AMOUNT_OIL,0),
    TAX_AMOUNT_OIL = null,
    INVOICE_NUMBER_oil = null,
    TID_OIL = null,
    TAX_MEMO_OIL = null,
    reg_user_id = #{regUserId,jdbcType=VARCHAR},
    REG_DATE = sysdate,
    REG_SCR_ID = #{regScrId,jdbcType=VARCHAR}
    where PAY_CHECK_SHEET_ID = #{payCheckSheetId}
</update>

<update id="updatePayCheckTaxRevokeAll">
    update t_pay_check_sheet
    set
    UNGOT_AMOUNT = UNGOT_AMOUNT - nvl(TAX_AMOUNT,0) - nvl(TAX_AMOUNT_OIL,0),
    TAX_AMOUNT = null,
    INVOICE_NUMBER = null,
    TID = null,
    TAX_MEMO = null,
    TAX_AMOUNT_OIL = null,
    INVOICE_NUMBER_oil = null,
    TID_OIL = null,
    TAX_MEMO_OIL = null
    where PAY_CHECK_SHEET_ID = #{payCheckSheetId}
</update>

<insert id="insertPayCheckSheetCheck">
    insert into T_PAY_CHECK_SHEET_CHECK
    (PAY_CHECK_SHEET_ID, TAX_AMOUNT, TAX_MEMO, INVOICE_NUMBER, TID, ADJUST_RECORD_ID,ADJUST_TYPE)
    values
    (#{payCheckSheetId,jdbcType=VARCHAR},
    #{taxAmount,jdbcType=DECIMAL},
    #{taxMemo,jdbcType=VARCHAR},
    #{invoiceNumber,jdbcType=VARCHAR},
    #{tid,jdbcType=VARCHAR},
    #{adjustRecordId,jdbcType=VARCHAR},
    #{adjustType,jdbcType=INTEGER}
    )
</insert>

<select id="selectReceCheckSheetByAdjustRecordId" resultMap="PayCheckSheetResult">
      select
      PAY_CHECK_SHEET_ID,
      TAX_AMOUNT,
      TAX_MEMO,
      INVOICE_NUMBER,
      tid,
      ADJUST_RECORD_ID,
      ADJUST_TYPE
      from T_PAY_CHECK_SHEET_CHECK
      where ADJUST_RECORD_ID = #{adjustRecordId}
</select>

    <select id="selectReceCheckSheetCheckByCheckSheetId" resultMap="PayCheckSheetResult">
        select
            checkSheet.PAY_CHECK_SHEET_ID,
            checkSheet.TAX_AMOUNT,
            checkSheet.TAX_MEMO,
            checkSheet.INVOICE_NUMBER,
            checkSheet.tid,
            checkSheet.ADJUST_RECORD_ID,
            checkSheet.ADJUST_TYPE
        from T_PAY_CHECK_SHEET_CHECK checkSheet
        left join t_adjust_record record on checkSheet.adjust_record_id = record.adjust_record_id
        where checkSheet.PAY_CHECK_SHEET_ID = #{checkSheetId}
        and record.adjust_check_status  = 3
    </select>

<delete id="deletePayCheckSheetCheck">
    delete from t_pay_check_sheet_check where PAY_CHECK_SHEET_ID = #{payCheckSheetId} and ADJUST_TYPE = #{adjustType}
</delete>

<select id="checkDeletePayCheckSheetCheck" resultType="int">
    select count(1) from T_PAY_CHECK_SHEET_CHECK sheetCheck
  join T_ADJUST_RECORD adjustRecord
       on sheetCheck.ADJUST_RECORD_ID = adjustRecord.ADJUST_RECORD_ID
  join T_CLOSE_ACCOUNT closeAccount on to_char(adjustRecord.REG_DATE,'yyyy-MM')
                                           = to_char(closeAccount.YEAR_MONTH,'yyyy-MM')
  where sheetCheck.PAY_CHECK_SHEET_ID = #{payCheckSheetId} and sheetCheck.adjust_Type = #{adjustType}
</select>

<select id="sumG7PayAbleAmount" parameterType="String" resultType="java.math.BigDecimal">
    select sum(b.UNGOT_AMOUNT) amount
    from t_pay_check_sheet_b a
    left join t_pay_detail b on a.pay_detail_id = b.pay_detail_id
    where a.pay_check_sheet_id = #{pay_check_sheet_id} and b.del_flag = 0
    and (
        (b.free_type = '0' and b.COST_TYPE_FREIGHT in ('0','2','4'))
        or
        (b.free_type = '1' and nvl(b.COST_TYPE_FREIGHT,'0') in ('0','2','4'))
    )
</select>

<select id="listPayDetailPayInfo" parameterType="String" resultType="Map">
    select b.pay_detail_id,b.vbillno,c.driver_name,c.driver_mobile,d.card_id driver_idcard,b.g7_syn,b.g7_pay,
           d.g7_contract_signed_my,d.g7_contract_signed_jh,d.g7_contract_signed_dh,d.g7_contract_signed_dw,<!-- G7结算公司 -->
           b.lot_id,b.free_type,b.cost_type_freight,c.billing_corp,b.carr_bank_id,b.UNGOT_AMOUNT,c.lot,b.VBILLSTATUS
    from t_pay_check_sheet_b a
             left join t_pay_detail b on a.pay_detail_id = b.pay_detail_id
             left join t_entrust c on c.lot_id = b.lot_id
             left join m_driver d on d.driver_name = c.driver_name and d.phone = c.driver_mobile and d.del_flag = 0
    where a.pay_check_sheet_id = #{pay_check_sheet_id} and b.del_flag = 0
</select>

<select id="selectPayCheckPartDataByLotId" resultType="java.util.Map">
    select t.vbillno sheet_vbillno,
           t.total_amount,
           t1.pay_amount,
           t.reg_date,
           t.reg_user_id,
           t4.user_name user_name,
           t1.vbillno record_vbillno
    from t_pay_check_sheet t
                 left join t_pay_sheet_record t1
            on t.pay_check_sheet_id = t1.pay_check_sheet_id
                 left join sys_user t4 on t4.USER_ID = t.reg_user_id
    where exists
                  (select t2.pay_check_sheet_id
                   from t_pay_check_sheet_b t2
                                left join t_pay_detail t3
                           on t2.pay_detail_id = t3.pay_detail_id
                   where t3.lot_id = #{lotId,jdbcType=VARCHAR}
                     and t.pay_check_sheet_id = t2.pay_check_sheet_id)
        and t1.del_falg = 0
</select>

<select id="listLockedInfo" resultType="Map">
    select b.vbillno,b.lotno,c.lock_pay,c.single_lock,d.LOCK_PAY carrier_LOCK_PAY
    from t_pay_check_sheet_b a
             left join t_pay_detail b on a.pay_detail_id = b.pay_detail_id
             left join t_entrust_lot c on b.lot_id = c.entrust_lot_id
             left join m_carrier d on c.CARRIER_ID = d.CARRIER_ID
    where a.pay_check_sheet_id = #{payCheckSheetId} and a.del_falg=0
      and (c.lock_pay = '1' or c.single_lock = '1' or d.LOCK_PAY = 1)
</select>

    <insert id="saveLotReceipt">
        insert into t_pay_check_sheet_receipt (PAY_CHECK_SHEET_ID, TID, REG_SCR_ID, REG_USER_ID, REG_DATE, PAY_SHEET_RECORD_ID)
        values (#{payCheckSheetId}, #{tidReceipt}, #{scrId}, #{userId}, #{regDate}, #{paySheetRecordId,jdbcType=VARCHAR})
    </insert>

    <select id="listCheckSheetReceipt" resultType="com.ruoyi.system.domain.SysUploadFile">
        select b.FILE_ID fileid,b.TID,FILE_NAME filename,FILE_PATH filepath
        from t_pay_check_sheet_receipt a
                 left join sys_upload_file b on b.tid = a.tid
        where b.del_flag = 0
          <if test="payCheckSheetId != null and payCheckSheetId !=''">
              and a.pay_check_sheet_id = #{payCheckSheetId}
          </if>
          <if test="paySheetRecordId != null and paySheetRecordId !=''">
              and a.pay_sheet_record_id = #{paySheetRecordId}
          </if>
        order by a.reg_date, b.CREATE_TIME
    </select>

    <select id="listDetailTax" resultType="Map">
        select b.pay_detail_id,c.lot,b.lot_id,b.vbillno,b.trans_fee_count + nvl(tax_amount,0) amount,c.is_close,c.req_deli_date,
               (select listagg(t.billing_type || ':' || sum(amount), ',') within GROUP(order by t.billing_type) from t_pay_detail_tax t
                where t.pay_detail_id = a.pay_detail_id group by t.billing_type having count(amount) = 1 or sum(amount) != 0) tax_txt,
            c.DELI_PROVINCE_NAME, c.DELI_CITY_NAME, c.DELI_AREA_NAME, c.ARRI_PROVINCE_NAME, c.ARRI_CITY_NAME, c.ARRI_AREA_NAME, b.g7_pay
        from t_pay_check_sheet_b a
            left join t_pay_detail b on b.pay_detail_id = a.pay_detail_id
            left join t_entrust_lot c on c.entrust_lot_id = b.lot_id
        where a.pay_check_sheet_id = #{payCheckSheetId} and a.del_falg = 0 and b.del_flag = 0
          and ((b.free_type = '0' and b.cost_type_freight in ('0','2','4')) or (b.free_type = '1' and nvl(b.cost_type_freight,'0') in ('0','2','4')))
        order by c.lot,b.vbillno
    </select>

    <update id="bindPayCheckSheetAndTax">
        update t_pay_detail_tax t set t.PAY_CHECK_SHEET_ID = #{payCheckSheetId}
        where t.pay_detail_id in (select pay_detail_id from t_pay_check_sheet_b x where x.pay_check_sheet_id = #{payCheckSheetId} and x.del_falg = 0)
        and (t.PAY_CHECK_SHEET_ID is null or t.PAY_CHECK_SHEET_ID != #{payCheckSheetId})
    </update>

    <insert id="initPayDetailTax">
        insert into t_pay_detail_tax (ID, PAY_DETAIL_ID, BILLING_TYPE, AMOUNT, REG_DATE, REG_USER_ID, REG_SCR_ID,
                                      EFFECT_DATE, FLAG, IS_INIT, LOT_ID, PAY_CHECK_SHEET_ID, seq, amountx, tax)
        select sys_guid(),
               t.pay_detail_id,
               nvl(t2.billing_type, '6'),
               t.trans_fee_count + nvl(tax_amount, 0),
               #{regDate},
               #{userId},
               #{scrId},
               <!-- 是调整单：单据的创建日期，不是调整单：要求提货日期 --><!-- 对账生成的调整单，被认为是调账记录生成哪个时间端的数据 -->
               case when t.is_adjust = 0 then t2.req_deli_date else t.reg_date end,
               0,
               1,
               t.lot_id,
               #{payCheckSheetId},
               1,
               (t.trans_fee_count + nvl(tax_amount, 0)) / t3.num_val1,
               t3.num_val1
        from t_pay_detail t
                 left join t_entrust_lot t2 on t2.entrust_lot_id = t.lot_id
                 left join sys_dict_data t3 on t3.dict_type = 'billing_type' and t3.dict_value = nvl(t2.billing_type, '6')
        where t.pay_detail_id in (
            select a.pay_detail_id from t_pay_check_sheet_b a where a.pay_check_sheet_id = #{payCheckSheetId} and a.del_falg = 0
        ) and not exists (select 1 from t_pay_detail_tax x where x.pay_detail_id = t.pay_detail_id)
          and ((t.free_type = '0' and t.cost_type_freight in ('0','2','4')) or (t.free_type = '1' and nvl(t.cost_type_freight,'0') in ('0','2','4')))
    </insert>

    <delete id="clearPayDetailTax">
        delete from t_pay_detail_tax t where t.pay_detail_id in (
            select a.pay_detail_id from t_pay_check_sheet_b a where a.pay_check_sheet_id = #{payCheckSheetId} and a.del_falg = 0
        )<!-- and t.is_init = 0 -->
    </delete>

    <!--<select id="findNoneTaxTxtPayDetail" resultType="Map">
        select b.pay_detail_id
        from t_pay_check_sheet_b b
        left join t_pay_detail_tax y on y.pay_detail_id = b.pay_detail_id
        where b.pay_check_sheet_id = #{payCheckSheetId} and b.del_falg = 0 and y.pay_detail_id is null
    </select>-->

    <insert id="addDeductTax">
        insert into t_pay_detail_tax (ID, PAY_DETAIL_ID, BILLING_TYPE, AMOUNT, EFFECT_DATE, REG_DATE,
                                      REG_USER_ID, REG_SCR_ID, FLAG, LOT_ID, PAY_CHECK_SHEET_ID, seq, amountx, tax)
        select sys_guid(), t.pay_detail_id, #{billingType}, #{amount}, #{effectDate}, #{regDate},
               #{userId}, #{scrId}, #{flag}, t.lot_id, #{payCheckSheetId},(select max(seq) from t_pay_detail_tax tt where tt.pay_detail_id = t.pay_detail_id) + 1,
               <choose>
                   <when test="billingType == 'G7'">#{amount} / g7_tax.config_value, g7_tax.config_value</when>
                   <otherwise>#{amount} / t3.num_val1, t3.num_val1</otherwise>
               </choose>
        from t_pay_detail t
                left join sys_dict_data t3 on t3.dict_type = 'billing_type' and t3.dict_value = #{billingType}
                left join sys_config g7_tax on g7_tax.config_key = 'net_profits_g7_tax'
        where t.pay_detail_id = #{payDetailId}
    </insert>

    <select id="getCheckSheetSumTaxTxtByLot" resultType="String">
        select listagg(nvl(c.billing_type,'6') || ':' || sum(b.trans_fee_count), ',') within GROUP(order by nvl(c.billing_type,'6'))
        from t_pay_check_sheet_b a
                 inner join t_pay_detail b on b.pay_detail_id = a.pay_detail_id and b.del_flag = 0 and nvl(b.cost_type_freight,'0') in ('0','2','4')
                 inner join t_entrust_lot c on c.entrust_lot_id = b.lot_id and c.del_flag = 0
        where a.pay_check_sheet_id = #{payCheckSheetId} and a.del_falg = 0
        group by nvl(c.billing_type,'6')
        having count(b.trans_fee_count) = 1 or sum(b.trans_fee_count) != 0
    </select>
    
    <select id="getCheckSheetSumTaxTxt" resultType="java.lang.String">
        select listagg(t.billing_type || ':' || sum(amount), ',') within GROUP(order by t.billing_type)
        from t_pay_detail_tax t
        where t.pay_check_sheet_id = #{payCheckSheetId}
        group by t.billing_type
        having count(amount) = 1 or sum(amount) != 0
    </select>

    <select id="selectPayCheckSheetSureList" resultType="com.ruoyi.tms.domain.carrier.EntrustLot">
        select
            lot.ENTRUST_LOT_ID entrustLotId,
            lot.LOT lot,
            lot.VBILLSTATUS vbillstatus,
            lot.REQ_DELI_DATE reqDeliDate,
            lot.REQ_ARRI_DATE reqArriDate,
            lot.DELI_PROVINCE_NAME deliProvinceName,
            lot.DELI_CITY_NAME deliCityName,
            lot.DELI_AREA_NAME deliAreaName,
            lot.ARRI_PROVINCE_NAME arriProvinceName,
            lot.ARRI_CITY_NAME arriCityName,
            lot.ARRI_AREA_NAME arriAreaName,
            carrier.CARR_NAME carrierName,
            carrier.LEGAL_CARD legalCard,
            lot.NUM_COUNT numCount,
            lot.WEIGHT_COUNT weightCount,
            lot.VOLUME_COUNT volumeCount,
            goods.goods_name goodsName,
            lot.CAR_NO carNo,
            lot.CAR_LEN_NAME carLenName,
            lot.CAR_TYPE_NAME carTypeName,
            lot.carrier_num carrierNum,
            lot.carrier_weight carrierWeight,
            lot.carrier_volume carrierVolume,
            lot.CARRIER_CASH_FEE carrierCashFee,
            lot.CARRIER_OIL_FEE carrierOilFee,
            lot.CARRIER_ON_WAY_FEE carrierOnWayFee,
            LOT.CARRIER_CONFIRM_STATUS carrierConfirmStatus,
            LOT.CARRIER_CONFIRM_TIME carrierConfirmTime,
            LOT.CARRIER_SUBMIT_TIME carrierSubmitTime,
            T_FEE.FRIGHTFEE frightFee,
            T_FEE.OILFEE oilFee,
            T_FEE.ONWAYFEE onWayFee
        from
            T_ENTRUST_LOT lot
                LEFT JOIN M_CARRIER carrier on lot.CARRIER_ID = carrier.CARRIER_ID
                left join (   select listagg(GOODS_NAME, ',') within group(order by lot_id) goods_name,LOT_ID from (
                select distinct GOODS.GOODS_NAME,entrust.lot_id
                from T_ENT_PACK_GOODS GOODS LEFT JOIN T_ENTRUST ENTRUST ON
                entrust.ENTRUST_ID = goods.ENTRUST_ID
                where ENTRUST.DEL_FLAG = 0 and goods.DEL_FLAG = 0
                )group by lot_id) goods on goods.LOT_ID = lot.ENTRUST_LOT_ID
                LEFT JOIN (SELECT LOT_ID,SUM(FRIGHTFEE) FRIGHTFEE,SUM(OILFEE) OILFEE,SUM(ONWAYFEE) ONWAYFEE FROM (
         SELECT LOT_ID,
                CASE WHEN T_PAY_DETAIL.FREE_TYPE = 0 AND T_PAY_DETAIL.COST_TYPE_FREIGHT IN (0,2,4) THEN TRANS_FEE_COUNT+NVL(TAX_AMOUNT,0) ELSE 0 END AS FRIGHTFEE,
                CASE WHEN T_PAY_DETAIL.FREE_TYPE = 0 AND T_PAY_DETAIL.COST_TYPE_FREIGHT IN (1,3,5) THEN TRANS_FEE_COUNT+NVL(TAX_AMOUNT,0) ELSE 0 END AS OILFEE,
                CASE WHEN T_PAY_DETAIL.FREE_TYPE = 1 THEN TRANS_FEE_COUNT+NVL(TAX_AMOUNT,0) ELSE 0 END AS ONWAYFEE
         FROM T_PAY_DETAIL WHERE DEL_FLAG = 0 AND IS_NTOCC = 0
     ) GROUP BY LOT_ID) T_FEE ON T_FEE.LOT_ID = LOT.ENTRUST_LOT_ID
        where lot.DEL_FLAG = 0
          and CARRIER_CONFIRM_STATUS is NOT null
        <if test="carrierConfirmStatus != null">
         and  lot.carrier_Confirm_Status = #{carrierConfirmStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            <bind name="carrierName" value="carrierName+'%'"/>
            and carrier.CARR_NAME like trim(#{carrierName})
        </if>
        <if test="params.reqDeliDateStart != null  and params.reqDeliDateStart != ''">
            and lot.req_deli_date <![CDATA[ >= ]]> to_date(#{params.reqDeliDateStart},'yyyy-mm-dd')
        </if>
        <if test="params.reqDeliDateEnd != null  and params.reqDeliDateEnd != ''">
            and lot.req_deli_date <![CDATA[ < ]]> to_date(#{params.reqDeliDateEnd},'yyyy-mm-dd') + 1
        </if>
        <if test="params.carrierSubmitTimeStart != null  and params.carrierSubmitTimeStart != ''">
            and lot.carrier_Submit_Time <![CDATA[ >= ]]> to_date(#{carrierSubmitTimeStart},'yyyy-mm-dd')
        </if>
        <if test="params.carrierSubmitTimeEnd != null  and params.carrierSubmitTimeEnd != ''">
            and lot.carrier_Submit_Time <![CDATA[ < ]]> to_date(#{carrierSubmitTimeEnd},'yyyy-mm-dd') + 1
        </if>

    </select>

    <select id="exportPayCheckSheetSureList" resultType="com.ruoyi.tms.vo.carrier.EntrustLotExportVO">
        select
        lot.ENTRUST_LOT_ID entrustLotId,
        lot.LOT lot,
        lot.VBILLSTATUS vbillstatus,
        lot.REQ_DELI_DATE reqDeliDate,
        lot.REQ_ARRI_DATE reqArriDate,
        lot.DELI_PROVINCE_NAME deliProvinceName,
        lot.DELI_CITY_NAME deliCityName,
        lot.DELI_AREA_NAME deliAreaName,
        lot.ARRI_PROVINCE_NAME arriProvinceName,
        lot.ARRI_CITY_NAME arriCityName,
        lot.ARRI_AREA_NAME arriAreaName,
        carrier.CARR_NAME carrierName,
        carrier.LEGAL_CARD legalCard,
        lot.NUM_COUNT numCount,
        lot.WEIGHT_COUNT weightCount,
        lot.VOLUME_COUNT volumeCount,
        goods.goods_name goodsName,
        lot.CAR_NO carNo,
        lot.CAR_LEN_NAME carLenName,
        lot.CAR_TYPE_NAME carTypeName,
        lot.CARRIER_CASH_FEE carrierCashFee,
        lot.CARRIER_OIL_FEE carrierOilFee,
        lot.CARRIER_ON_WAY_FEE carrierOnWayFee,
        LOT.CARRIER_CONFIRM_STATUS carrierConfirmStatus,
        LOT.CARRIER_CONFIRM_TIME carrierConfirmTime,
        LOT.CARRIER_SUBMIT_TIME carrierSubmitTime,
        T_FEE.FRIGHTFEE frightFee,
        T_FEE.OILFEE oilFee,
        T_FEE.ONWAYFEE onWayFee
        from
        T_ENTRUST_LOT lot
        LEFT JOIN M_CARRIER carrier on lot.CARRIER_ID = carrier.CARRIER_ID
        left join (   select listagg(GOODS_NAME, ',') within group(order by lot_id) goods_name,LOT_ID from (
        select distinct GOODS.GOODS_NAME,entrust.lot_id
        from T_ENT_PACK_GOODS GOODS LEFT JOIN T_ENTRUST ENTRUST ON
        entrust.ENTRUST_ID = goods.ENTRUST_ID
        where ENTRUST.DEL_FLAG = 0 and goods.DEL_FLAG = 0
        )group by lot_id) goods on goods.LOT_ID = lot.ENTRUST_LOT_ID
        LEFT JOIN (SELECT LOT_ID,SUM(FRIGHTFEE) FRIGHTFEE,SUM(OILFEE) OILFEE,SUM(ONWAYFEE) ONWAYFEE FROM (
        SELECT LOT_ID,
        CASE WHEN T_PAY_DETAIL.FREE_TYPE = 0 AND T_PAY_DETAIL.COST_TYPE_FREIGHT IN (0,2,4) THEN TRANS_FEE_COUNT+NVL(TAX_AMOUNT,0) ELSE 0 END AS FRIGHTFEE,
        CASE WHEN T_PAY_DETAIL.FREE_TYPE = 0 AND T_PAY_DETAIL.COST_TYPE_FREIGHT IN (1,3,5) THEN TRANS_FEE_COUNT+NVL(TAX_AMOUNT,0) ELSE 0 END AS OILFEE,
        CASE WHEN T_PAY_DETAIL.FREE_TYPE = 1 THEN TRANS_FEE_COUNT+NVL(TAX_AMOUNT,0) ELSE 0 END AS ONWAYFEE
        FROM T_PAY_DETAIL WHERE DEL_FLAG = 0 AND IS_NTOCC = 0
        ) GROUP BY LOT_ID) T_FEE ON T_FEE.LOT_ID = LOT.ENTRUST_LOT_ID
        where lot.DEL_FLAG = 0
        and CARRIER_CONFIRM_STATUS is NOT null
        <if test="carrierConfirmStatus != null">
            and  lot.carrier_Confirm_Status = #{carrierConfirmStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            <bind name="carrierName" value="carrierName+'%'"/>
            and carrier.CARR_NAME like trim(#{carrierName})
        </if>
        <if test="params.reqDeliDateStart != null  and params.reqDeliDateStart != ''">
            and lot.req_deli_date <![CDATA[ >= ]]> to_date(#{params.reqDeliDateStart},'yyyy-mm-dd')
        </if>
        <if test="params.reqDeliDateEnd != null  and params.reqDeliDateEnd != ''">
            and lot.req_deli_date <![CDATA[ < ]]> to_date(#{params.reqDeliDateEnd},'yyyy-mm-dd') + 1
        </if>
        <if test="params.carrierSubmitTimeStart != null  and params.carrierSubmitTimeStart != ''">
            and lot.carrier_Submit_Time <![CDATA[ >= ]]> to_date(#{carrierSubmitTimeStart},'yyyy-mm-dd')
        </if>
        <if test="params.carrierSubmitTimeEnd != null  and params.carrierSubmitTimeEnd != ''">
            and lot.carrier_Submit_Time <![CDATA[ < ]]> to_date(#{carrierSubmitTimeEnd},'yyyy-mm-dd') + 1
        </if>

    </select>

    <update id="deleteTidReceipt">
        begin

        update sys_upload_file
        set del_flag = 1
        where file_id = #{fileId}
          and tid = #{tid};

        update t_pay_check_sheet_receipt
        set del_flag = 1
        where tid = #{tid} and pay_check_sheet_id = #{payCheckSheetId}
          and not exists(select 1 from sys_upload_file where tid = #{tid} and del_flag = 0);

        end;
    </update>

    <select id="listDriverIdcards" resultType="java.lang.String">
        select distinct c.card_id
        from t_pay_check_sheet_b t
                 left join t_pay_detail a on a.pay_detail_id = t.pay_detail_id
                 left join t_entrust b on a.lot_id = b.lot_id
                 left join m_driver c on c.driver_name = b.driver_name and c.phone = b.driver_mobile and c.del_flag = 0
        where t.pay_check_sheet_id = #{payCheckSheetId} and t.del_falg = 0 and a.del_flag = 0
    </select>

    <select id="getCarrBankIdcard" resultType="java.lang.String">
        select idcard from m_carr_bank where carr_bank_id = #{carrBankId}
    </select>

    <select id="selectPayDetailBySheetIdGroupByLot" resultType="com.ruoyi.tms.vo.finance.LotPayDetailVO">
        select
            t3.lot                    lotno,
            t3.entrust_lot_id         lotId,
            t3.deli_province          deliProvince,
            t3.deli_city              deliCity,
            t3.deli_area              deliArea,
            t3.deli_province_name     deliProvinceName,
            t3.deli_city_name         deliCityName,
            t3.deli_area_name         deliAreaName,
            t3.deli_province_name || t3.deli_city_name || t3.deli_area_name deliName,

            t3.arri_province          arriProvince,
            t3.arri_city              arriCity,
            t3.arri_area              arriArea,
            t3.arri_province_name     arriProvinceName,
            t3.arri_city_name         arriCityName,
            t3.arri_area_name         arriAreaName,
            t3.arri_province_name || t3.arri_city_name || t3.arri_area_name arriName,

            t3.carrier_id             carrierId,
            t3.carrier_name           carrierName,
            t3.driver_name            driverName,
            t3.carno_id               carnoId,
            t3.car_no                 carNo,
            t3.car_len_name           carLenName,
            t3.car_type_name          carTypeName,
            (select listagg(GOODS_NAME)  within GROUP ( ORDER BY GOODS_NAME ) AS GOODS_NAME
                from (
                        select distinct goods.GOODS_NAME
                          from T_ENT_PACK_GOODS goods
                          left join T_ENTRUST entrust on goods.ENTRUST_ID = entrust.ENTRUST_ID
                          where entrust.lot_id = t3.ENTRUST_LOT_ID)) goodsName,
            (select listagg(a.invoice_vbillno)  within GROUP ( ORDER BY GOODS_NAME ) AS invoiceNo
               from t_entrust a where a.del_flag = 0 and a.lot_id = t3.ENTRUST_LOT_ID) invoiceNo,
            t3.num_count             numCount,
            t3.weight_count          weightCount,
            t3.volume_count          volumeCount,
            t3.req_deli_date         reqDeliDate,
            t2.freightCash           freightCash,
            t2.freightOil            freightOil,
            t2.transFeeCountOnWay  transFeeCountOnWay,
            t2.transFeeCount         transFeeCount
        from (
                 select t.lot_id,
                        sum(case when t.free_type = 0 and t.cost_type_freight in ('1','3','5') then t.trans_fee_count else 0 end) freightCash,
                        sum(case when t.free_type = 0 and t.cost_type_freight in ('0','2','4') then t.trans_fee_count else 0 end) freightOil,
                        sum(case when t.free_type = 1 then t.trans_fee_count else 0 end) transFeeCountOnWay,
                        sum(t.trans_fee_count) transFeeCount
                 from t_pay_detail t
                          left join T_PAY_CHECK_SHEET_B t1 on t.pay_detail_id = t1.pay_detail_id
                 where t.del_flag = 0  and t1.pay_check_sheet_id = #{payCheckSheetId}
                 group by t.lot_id
             ) t2 left join t_entrust_lot t3 on t2.lot_id = t3.entrust_lot_id

    </select>

    <select id="getAbnormalDeductionBySheetId" resultType="com.ruoyi.tms.domain.finance.PayDetail">
        select  t1.PAY_DETAIL_ID    payDetailId,
                t1.VBILLNO          vbillno,
                t1.VBILLSTATUS      vbillstatus,
                t1.FREE_TYPE        freeType,
                t1.COST_TYPE_ON_WAY costTypeOnWay,
                t1.COST_TYPE_FREIGHT    costTypeFreight,
                t1.LOT_ID               lotId,
                t1.LOTNO                lotno,
                t1.CARRIER_ID           carrierId,
                t1.CARR_CODE            carrCode,
                t1.CARR_NAME            carrName,
                t1.BALA_CORP            balaCorp,
                t1.BALATYPE             balatype,
                t1.TRANS_FEE_COUNT      transFeeCount,
                t1.GOT_AMOUNT           gotAmount,
                t1.UNGOT_AMOUNT         ungotAmount,
                t1.OIL_CARD_NUMBER      oilCardNumber,
                t1.CHECK_NO             checkNo,
                t1.CHECK_HEAD           checkHead,
                t1.UNCONFIRM_TYPE       unconfirmType,
                t1.UNCONFIRM_MEMO       unconfirmMemo,
                t1.CONFIRM_TIME         confirmTime,
                t1.CONFIRM_USER         confirmUser,
                t1.DRIVER_MOBILE        driverMobile,
                t1.DRIVER_NAME          driverName,
                t1.CARNO                carno,
                t1.REQ_DELI_DATE    reqDeliDate,
                t1.REQ_ARRI_DATE    reqArriDate,
                t1.IS_CLOSE         isClose,
                t1.IS_ADJUST        isAdjust,
                t1.ADJUST_MEMO      adjustMemo,
                t1.MEMO             memo,
                t1.REG_USER_ID      regUserId,
                t1.REG_DATE         regDate,
                t1.REG_SCR_ID       regScrId,
                t1.COR_USER_ID      corUserId,
                t1.COR_DATE         corDate,
                t1.COR_SCR_ID       corScrId,
                t1.DEL_FLAG         delFlag,
                t1.DEL_DATE         delDate,
                t1.DEL_USER_ID      delUserId,
                t1.REC_CARD_NO      recCardNo,
                t1.REC_ACCOUNT      recAccount,
                t1.REC_BANK         recBank,
                t1.IS_NTOCC         isNtocc,
                t1.CARR_BANK_ID     carrBankId,
                t1.APPLY_TIME       applyTime,
                t1.APPLY_USER       applyUser,
                t1.BAKC_WRITE_TYPE  bakcWriteType,
                t1.BACK_WRITE_MEMO  backWriteMemo,
                t1.BACK_WRITE_TIME  backWriteTime,
                t1.REQ_PAY_DATE     reqpaydate,
                t1.TAX_AMOUNT       taxAmount,
                t1.CHECK_USER_ID    checkUserId,
                t1.CHECK_USER_NAME  checkUserName,
                t1.CHECK_DATE       checkDate,
                t1.CHECK_MEMO       checkMemo,
                t1.CHECK_STATUS     checkStatus,
                t1.APPLY_MEMO       applyMemo,
                t1.IS_OIL_DEPOSIT   isOilDeposit,
                t1.SPLIT_PAY_DETAIL_ID  splitPayDetailId,
                t1.BANK_BACK_FLAG       bankBackFlag,
                t1.IS_FLEET_DATA        isFleetData,
                t1.IS_FLEET_ASSIGN      isFleetAssign,
                t1.FLEET_RECEIVE_DETAIL_ID  fleetReceiveDetailId,
                t1.WRITE_OFF_TIME           writeOffTime,
                t1.ACCOUNT_TYPE             accountType,
                t1.WRITE_FUELCARD_ID        writeFuelcardId,
                t1.CONSUMBLE_BACK   consumbleBack,
                t1.INCOME_REMARK    incomeRemark,
                t1.CREATE_BY_ADJUST createByAdjust,
                t1.SP_NO            spNo,
                t1.LOT_SP_LOCK  lotSpLock,
                t1.WRITE_OFF_TO writeOffTo,
                t2.USER_NAME    regUserName
        from (select distinct b.lot_id, a.pay_check_sheet_id
              FROM T_PAY_CHECK_SHEET_B a
                       left join T_PAY_DETAIL b on a.pay_detail_id = b.pay_detail_id
              where a.del_falg = 0) t
        left join T_PAY_DETAIL t1 on t.lot_id = t1.lot_id
        left join SYS_USER t2 on t1.REG_USER_ID = t2.USER_ID
            where t1.del_flag = 0 and t1.INCOME_REMARK = 1
              and t.PAY_CHECK_SHEET_ID = #{payCheckSheetId,jdbcType=VARCHAR}
        order by t1.LOTNO desc

    </select>

    <select id="listG7Ext" resultType="java.util.Map">
        select driver.driver_id,
               driver.driver_name,
               driver.g7_ext g7_driver_ext,
               car.car_id,
               car.carno,
               car.g7_ext    g7_car_ext,
               e.billing_corp
        from t_entrust_lot_car_driver d
                 left join m_driver driver on driver.driver_id = d.driver_id
                 left join m_car car on car.car_id = d.car_id
                 left join t_entrust e on e.lot_id = d.lot_id and e.del_flag = 0
        where d.DEL_FLAG = 0
          and d.lot_id in (
            select b.lot_id
            from t_pay_check_sheet_b a
                     left join t_pay_detail b on b.pay_detail_id = a.pay_detail_id and b.del_flag = 0
            where a.del_falg = 0
              and a.pay_check_sheet_id = #{payCheckSheetId}
        )
    </select>

    <select id="countPayCheckSheet" resultType="java.lang.Integer">
        select count(1) from t_pay_check_sheet where carrier_id = #{carrierId} and year = #{year} and month = #{month} and del_flag = 0
    </select>

    <resultMap id="rm1813" type="java.util.Map">
        <result property="lotId" column="LOT_ID" />
        <result property="reqDeliDate" column="REQ_DELI_DATE" />
        <result property="lot" column="LOT" />
        <result property="lx" column="LX" />
        <result property="g7Syn" column="G7_SYN" javaType="Integer" />
        <result property="g7End" column="G7_END" javaType="Integer" />
        <result property="g7Qst" column="G7_QST" />
        <result property="deliProvinceName" column="DELI_PROVINCE_NAME" />
        <result property="deliCityName" column="DELI_CITY_NAME" />
        <result property="deliAreaName" column="DELI_AREA_NAME" />
        <result property="arriProvinceName" column="ARRI_PROVINCE_NAME" />
        <result property="arriCityName" column="ARRI_CITY_NAME" />
        <result property="arriAreaName" column="ARRI_AREA_NAME" />
        <result property="driverName" column="DRIVER_NAME" />
        <result property="driverId" column="DRIVER_ID" />
        <result property="phone" column="PHONE" />
        <result property="cardId" column="CARD_ID" />
        <result property="carno" column="CARNO" />
        <result property="carId" column="CAR_ID" />
        <result property="carrBankId" column="CARR_BANK_ID" />
        <result property="bankAccount" column="BANK_ACCOUNT" />
        <result property="bankCard" column="BANK_CARD" />
        <result property="bankName" column="BANK_NAME" />
        <result property="idcard" column="IDCARD" />
        <result property="balaCorp" column="BALA_CORP" />
        <result property="transFeeCount" column="trans_fee_count" javaType="BigDecimal" />
        <result property="applied" column="applied" javaType="BigDecimal" />
    </resultMap>

    <select id="listLotAndCarrBank" resultMap="rm1813">
        select c.ENTRUST_LOT_ID                       LOT_ID,
               to_char(c.REQ_DELI_DATE, 'YYYY-MM-DD') REQ_DELI_DATE,
               c.G7_SYN,
               c.G7_END,
               c.G7_QST,
               c.LOT,
               case when nvl(b.COST_TYPE_FREIGHT, '0') in ('0', '2', '4') then 'xj' else 'yk' end LX,
               c.REG_DATE,
               c.DELI_PROVINCE_NAME,
               c.DELI_CITY_NAME,
               c.DELI_AREA_NAME,
               c.ARRI_PROVINCE_NAME,
               c.ARRI_CITY_NAME,
               c.ARRI_AREA_NAME,
               d.DRIVER_NAME,
               d.DRIVER_ID,
               d.PHONE,
               d.CARD_ID,
               e.CARNO,
               e.CAR_ID,
               f.CARR_BANK_ID,
               f.BANK_ACCOUNT,
               f.BANK_CARD,
               f.BANK_NAME,
               f.IDCARD,
               b.BALA_CORP,
               sum(b.trans_fee_count)                 trans_fee_count,
               sum(nvl(a.applied,0))                  applied
        from T_PAY_CHECK_SHEET_B a
                 inner join t_pay_detail b on b.pay_detail_id = a.PAY_DETAIL_ID and b.DEL_FLAG = 0
                 inner join t_entrust_lot c on c.ENTRUST_LOT_ID = b.lot_id and c.del_flag = 0
                 left join m_driver d on d.driver_id = c.driver_id
                 left join m_car e on e.car_id = c.CARNO_ID
                 left join M_CARR_BANK f on nvl(b.COST_TYPE_FREIGHT, '0') in ('0', '2', '4') and f.CARR_BANK_ID = c.CARR_BANK_ID
        where a.PAY_CHECK_SHEET_ID = #{payCheckSheetId}
          <if test="driverId != null and driverId != ''">and c.driver_id = #{driverId}</if>
          <if test="carId != null and carId != ''">and c.CARNO_ID = #{carId}</if>
          <if test="reqDeliDate != null and reqDeliDate != ''">and c.req_deli_date >= to_date(#{reqDeliDate},'yyyy-mm-dd')
            and c.req_deli_date &lt; to_date(#{reqDeliDate},'yyyy-mm-dd') + 1</if>
          and a.DEL_FALG = 0
        group by c.ENTRUST_LOT_ID,
                 to_char(c.REQ_DELI_DATE, 'YYYY-MM-DD'),
                 c.G7_SYN,
                 c.G7_END,
                 c.G7_QST,
                 c.LOT,
                 case when nvl(b.COST_TYPE_FREIGHT, '0') in ('0', '2', '4') then 'xj' else 'yk' end,
                 c.REG_DATE,
                 c.DELI_PROVINCE_NAME,
                 c.DELI_CITY_NAME,
                 c.DELI_AREA_NAME,
                 c.ARRI_PROVINCE_NAME,
                 c.ARRI_CITY_NAME,
                 c.ARRI_AREA_NAME,
                 d.DRIVER_NAME,
                 d.DRIVER_ID,
                 d.PHONE,
                 d.CARD_ID,
                 e.CARNO,
                 e.CAR_ID,
                 f.CARR_BANK_ID,
                 f.BANK_ACCOUNT,
                 f.BANK_CARD,
                 f.BANK_NAME,
                 f.IDCARD,
                 b.BALA_CORP
        order by c.reg_date
    </select>

    <resultMap id="rm1913" type="java.util.Map">
        <result column="carrier_id" property="carrierId" />
        <result column="total_amount" property="totalAmount" javaType="BigDecimal"/>
        <result column="oil_amount" property="oilAmount" javaType="BigDecimal"/>
        <result column="need_pay" property="needPay" javaType="Integer" />
    </resultMap>

    <select id="sumTotalAmountByCarrier" resultMap="rm1913">
        select carrier_id, sum(nvl(TOTAL_AMOUNT,0)) total_amount, sum(nvl(OIL_AMOUNT,0)) oil_amount,
               sum(case when total_amount - nvl(oil_amount,0) > 0 and total_amount - nvl(oil_amount,0) > APPLICATION_AMOUNT then 1 else 0 end) need_pay
        from t_pay_check_sheet
        where DEL_FLAG = 0
          and CARRIER_ID = #{carrierId}
        group by carrier_id
    </select>

    <resultMap id="rm1929" type="java.util.Map">
        <result column="pay_check_sheet_id" property="payCheckSheetId" />
        <result column="fuel_card" property="fuelCard" />
        <result column="oil_account" property="oilAccount" />
        <result column="FUELCARD_NAME" property="fuelcardName" />
        <result column="fuelcard_type" property="fuelcardType" />
        <result column="bill_id" property="billId" />
        <result column="bill_carrier_id" property="billCarrierId" />
    </resultMap>

    <select id="checkPaySheetRecordFuelcard" resultMap="rm1929">
        select r.pay_check_sheet_id,
               r.fuel_card,
               r.oil_account,
               f.FUELCARD_NAME,
               f.fuelcard_type,
               f.bill_id,
               nvl(x.carrier_id, y.carrier_id) bill_carrier_id
        from t_pay_sheet_record r
                 inner join M_FUELCARD f on f.FUELCARD_ID = r.OIL_ACCOUNT and f.DEL_FLAG = 0
                 left join t_pay_detail x on x.pay_detail_id = f.bill_id
                 left join t_pay_check_sheet y on x.pay_detail_id is null and y.pay_check_sheet_id = f.bill_id
        where r.carrier_id = #{carrierId}
          and r.del_falg = 0
          and r.oil_account is not null
          and f.fuelcard_type not in ('0', '1')
          and f.Fuelcard_Kind = 0
          and f.is_pay != 1
    </select>

</mapper>
