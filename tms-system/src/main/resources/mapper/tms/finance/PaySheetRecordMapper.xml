<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tms.mapper.finance.PaySheetRecordMapper">
    <!--付款申请-->
    <resultMap type="PaySheetRecord" id="PaySheetRecordResult">
        <result property="paySheetRecordId"    column="pay_sheet_record_id"    />
        <result property="payCheckSheetId"    column="pay_check_sheet_id"    />
        <result property="vbillno"    column="vbillno"    />
        <result property="vbillnoStatus"    column="vbillno_status"    />
        <result property="payType"    column="pay_type"    />
        <result property="payAmount"    column="pay_amount"    />
        <result property="payDate"    column="pay_date"    />
        <result property="payMan"    column="pay_man"    />
        <result property="carrierId"    column="carrier_id"    />
        <result property="checkNo"    column="check_no"    />
        <result property="checkHead"    column="check_head"    />
        <result property="checkDate"    column="check_date"    />
        <result property="checkRemark"    column="check_remark"    />
        <result property="checkType"    column="check_type"    />
        <result property="memo"    column="memo"    />
        <result property="outAccount"    column="out_account"    />
        <result property="oilAccount"    column="oil_account"    />
        <result property="approveMan"    column="approve_man"    />
        <result property="approveMemo"    column="approve_memo"    />
        <result property="approveTime"    column="approve_time"    />
        <result property="gotAmount"    column="got_amount"    />
        <result property="ungotAmount"    column="ungot_amount"    />
        <result property="fuelCard"    column="fuel_card"    />
        <result property="recAccount"    column="rec_account"    />
        <result property="recBank"    column="rec_bank"    />
        <result property="recCardNo"    column="rec_card_no"    />
        <result property="regUserId"    column="reg_user_id"    />
        <result property="regDate"    column="reg_date"    />
        <result property="corUserId"    column="cor_user_id"    />
        <result property="corDate"    column="cor_date"    />
        <result property="delFalg"    column="del_falg"    />
        <result property="delDate"    column="del_date"    />
        <result property="delUserId"    column="del_user_id"    />
        <result property="reconNumber"    column="recon_number"    />
        <result property="params.carrName"    column="carr_name"    />
        <result property="balaType"    column="bala_type"    />
        <result property="receMan"    column="rece_man"    />
        <result property="regScrId"    column="reg_scr_id"    />
        <result property="corScrId"    column="cor_scr_id"    />
        <result property="bankNo"    column="bank_no"    />
        <result property="fuelcardName"    column="fuelcard_name"    />
        <result property="tid"    column="tid"    />
        <result property="balaCorp"    column="bala_corp"    />
        <result property="fuelcardType"    column="FUELCARD_TYPE"    />
        <result property="legalCard"    column="LEGAL_CARD"    />
        <result property="accountName"    column="account_name"    />
        <result property="lotG7End" column="lot_g7_end" />
        <result property="carrBankId" column="carr_bank_id" />
        <result property="accountType" column="account_type" />
        <result property="writeFuelcardId" column="write_fuelcard_id" />
        <result property="isOil" column="is_oil" />
        <result property="advancePayMoney" column="advance_Pay_Money" />

        <result property="carrLockPay" column="carrLockPay" />
        <!--账户管理-->
        <association property="account" column="account_id" javaType="Account" resultMap="AccountResult"/>

    </resultMap>
    <!--账户管理-->
    <resultMap type="Account" id="AccountResult">
        <result property="accountId"    column="account_id"    />
        <result property="accountCode"    column="account_code"    />
        <result property="accountName"    column="account_name"    />
        <result property="accountType"    column="account_type"    />
        <result property="account"    column="account"    />
        <result property="bank"    column="bank"    />
        <result property="accountBala"    column="account_bala"    />
        <result property="balaCorp"    column="bala_corp"    />
        <result property="regUserId"    column="reg_user_id"    />
        <result property="regDate"    column="reg_date"    />
        <result property="corUserId"    column="cor_user_id"    />
        <result property="corDate"    column="cor_date"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="delDate"    column="del_date"    />
        <result property="delUserId"    column="del_user_id"    />
        <result property="regScrId"    column="reg_scr_id"    />
        <result property="corScrId"    column="cor_scr_id"    />
        <result property="memo"    column="memo"    />
    </resultMap>
	<!--付款申请VO-->
	<sql id="selectPaySheetRecordVo">
        select pay_sheet_record_id,
            pay_check_sheet_id,
            vbillno,
            vbillno_status,
            pay_type,
            pay_amount,
            pay_date,
            pay_man,
            carrier_id,
            check_no,
            check_head,
            check_date,
            check_remark,
            check_type,
            memo,
            out_account,
            oil_account,
            approve_man,
            approve_memo,
            approve_time,
            got_amount,
            ungot_amount,
            fuel_card,
            rec_account,
            rec_bank,
            rec_card_no,
            reg_user_id,
            reg_date,
            cor_user_id,
            cor_date,
            del_falg,
            del_date,
            del_user_id,
            rece_man,
            bank_no,
            tid,
            account_type,
            write_fuelcard_id,
            carr_bank_id,
            is_oil
        from t_pay_sheet_record
    </sql>

    <resultMap id="OfflinePayMap" type="OfflinePay">
        <result property="vbillno" column="vbillno" />
        <result property="recCardNo" column="rec_card_no" />
        <result property="recAccount" column="rec_account" />
        <result property="ungotAmount" column="ungot_amount" />
    </resultMap>

    <select id="offlineList" resultMap="OfflinePayMap">
        select
            record.vbillno,
            record.REC_ACCOUNT,
            record.REC_CARD_NO,
            record.ungot_amount
        from t_pay_sheet_record record
        left join t_pay_check_sheet sheet on record.pay_check_sheet_id = sheet.pay_check_sheet_id
        left join M_CARRIER carr on record.CARRIER_ID = carr.CARRIER_ID
        left join t_account account on record.out_account = account.account_id
        left join M_FUELCARD fuelcard on fuelcard.FUELCARD_ID = record.OIL_ACCOUNT and fuelcard.DEL_FLAG = 0
        left join sys_user sysuser on sysuser.USER_ID = record.PAY_MAN
        <include refid="condition1"></include>
        order by record.cor_date desc
    </select>

    <sql id="condition1">
        <where>
            record.del_falg = 0
            <!--过滤园区数据-->
<!--            and record.reg_scr_id != 'partDataPayDetailApplication'-->
            <if test="payCheckSheetId != null  and payCheckSheetId != '' ">
                and record.pay_check_sheet_id = #{payCheckSheetId}
            </if>
            <if test="paySheetRecordId != null  and paySheetRecordId != '' ">
                and record.pay_sheet_record_id = #{paySheetRecordId}
            </if>
            <!--单据号-->
            <if test="vbillno != null  and vbillno != '' ">
                <bind name="vbillno" value="vbillno + '%'"/>
                and record.vbillno like #{vbillno}
            </if>
            <!--承运商名称-->
            <if test="params.carrName != null  and params.carrName != '' ">
                <bind name="params.carrName" value="params.carrName + '%'"/>
                and carr.carr_name like #{params.carrName}
            </if>
            <!--承运商id-->
            <if test="carrierId != null  and carrierId != '' ">
                and carr.CARRIER_ID = #{carrierId}
            </if>
            <!--申请人-->
            <if test="payMan != null  and payMan != '' ">
                <bind name="payMan" value="payMan+'%'"/>
                and sysuser.USER_NAME like #{payMan}
            </if>
            <!--对账单号-->
            <if test="reconNumber != null  and reconNumber != '' ">
                <bind name="reconNumber" value="reconNumber + '%'"/>
                and sheet.vbillno like #{reconNumber}
            </if>
            <!--单据状态-->
            <if test="status != null and status.indexOf(',') != -1 ">
                and record.vbillno_status in
                <foreach item="item" index="index" collection="status.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status != '' and status.indexOf(',') == -1 ">
                and record.vbillno_status = #{status}
            </if>
            <if test="payType != null ">
                and record.pay_type = #{payType}
            </if>
            <!--判断申请类型（油卡0，现金1）-->
            <if test="params.type != null and params.type != '' and params.type == 0">
                and (record.oil_account is not null or record.is_oil = 1 )
            </if>
            <if test="params.type != null and params.type != '' and params.type == 1">
                and record.oil_account is null and  (record.is_oil is null or record.is_oil != 1)
            </if>
            <!--收款账户-->
            <if test="recAccount != null  and recAccount != '' ">
                <bind name="recAccount" value="recAccount + '%'"/>
                and record.REC_ACCOUNT like #{recAccount}
            </if>
            <choose>
                <when test="params.payWay == 'g7'">
                    and sheet.lot_g7_end = 2
                </when>
                <when test="params.payWay == 'yl'">
                    and (sheet.lot_g7_end is null or sheet.lot_g7_end != 2)
                </when>
            </choose>
        </where>
    </sql>

	<!--付款申请列表-->
    <select id="selectPaySheetRecordList" parameterType="PaySheetRecord" resultMap="PaySheetRecordResult">
        select
            record.pay_check_sheet_id,
            record.pay_sheet_record_id,
            record.vbillno,
            record.vbillno_status,
            record.pay_type,
            record.pay_amount,
            record.pay_date,

            record.carrier_id,
            record.check_no,
            record.check_head,
            record.check_date,
            record.check_remark,
            record.check_type,
            record.memo,
            record.got_amount,
            record.ungot_amount,
            record.oil_account,
            record.fuel_card,
            record.REC_ACCOUNT,
            record.REC_CARD_NO,
            record.REC_BANK,
            record.OUT_ACCOUNT,
            carr.CARR_NAME as carr_name,
            sheet.vbillno as recon_number,
            sheet.bala_corp,
            account.account_name,
            sysuser.USER_NAME pay_man,
            fuelcard.FUELCARD_NAME fuelcard_name,
            fuelcard.FUELCARD_TYPE,
            carr.LEGAL_CARD,
            carr.bala_type,
            sheet.lot_g7_end,
            record.account_type,
        record.tid,
               record.is_oil,
               nvl(carr.advance_Pay_Money,0)+nvl(carr.advance_Pay_Money_XJ,0) advance_Pay_Money,
        carr.LOCK_PAY carrLockPay
        from t_pay_sheet_record record
        left join t_pay_check_sheet sheet  <!--承运商对账-->
        on record.pay_check_sheet_id = sheet.pay_check_sheet_id
        left join M_CARRIER carr <!--承运商-->
        on record.CARRIER_ID = carr.CARRIER_ID
        left join t_account account <!--账户管理-->
        on record.out_account = account.account_id
        left join M_FUELCARD fuelcard
        on fuelcard.FUELCARD_ID = record.OIL_ACCOUNT and fuelcard.DEL_FLAG = 0
        left join sys_user sysuser on sysuser.USER_ID = record.PAY_MAN
        <include refid="condition1"></include>
        order by record.cor_date desc
    </select>
    <!--付款申请信息-->
    <select id="selectPaySheetRecordById" parameterType="String" resultMap="PaySheetRecordResult">
        <include refid="selectPaySheetRecordVo"/>
        where pay_sheet_record_id = #{paySheetRecordId}
    </select>
    <select id="selectPayDetailIdsByPaySheetRecordIds" resultType="java.lang.String">
        select
            detail.PAY_DETAIL_ID
        from T_PAY_CHECK_SHEET_B b
            left join T_PAY_DETAIL detail on detail.PAY_DETAIL_ID = b.PAY_DETAIL_ID
            left join T_PAY_SHEET_RECORD sheetrecord on sheetrecord.PAY_CHECK_SHEET_ID = b.PAY_CHECK_SHEET_ID
        where
            detail.DEL_FLAG = 0 and b.DEL_FALG = 0
            and sheetrecord.PAY_SHEET_RECORD_ID in
            <foreach item="ids" collection="list" open="(" separator="," close=")">
                #{ids}
            </foreach>
    </select>
    <!--新增付款申请-->
    <insert id="insertPaySheetRecord" parameterType="PaySheetRecord">
        <selectKey keyProperty="vbillno" order="BEFORE" resultType="String">
            select 'YFSQ' || substr(to_char(sysdate, 'yyyymmdd'), 3, 7) ||
            lpad(SEQ_T_PAY_SHEET_RECORD.nextval, 4, '0') from dual
        </selectKey>
        insert into t_pay_sheet_record
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="paySheetRecordId != null  and paySheetRecordId != ''  ">pay_sheet_record_id,</if>
			<if test="payCheckSheetId != null  and payCheckSheetId != ''  ">pay_check_sheet_id,</if>
			vbillno,
			<if test="vbillnoStatus != null  ">vbillno_status,</if>
			<if test="payType != null  ">pay_type,</if>
			<if test="payAmount != null  ">pay_amount,</if>
			<if test="payDate != null  ">pay_date,</if>
			<if test="payMan != null  and payMan != ''  ">pay_man,</if>
			<if test="carrierId != null  and carrierId != ''  ">carrier_id,</if>
			<if test="checkNo != null  and checkNo != ''  ">check_no,</if>
			<if test="checkHead != null  and checkHead != ''  ">check_head,</if>
			<if test="checkDate != null  ">check_date,</if>
			<if test="checkRemark != null  and checkRemark != ''  ">check_remark,</if>
			<if test="checkType != null  and checkType != ''  ">check_type,</if>
			<if test="memo != null  and memo != ''  ">memo,</if>
			<if test="outAccount != null  and outAccount != ''  ">out_account,</if>
			<if test="oilAccount != null  and oilAccount != ''  ">oil_account,</if>
			<if test="approveMan != null  and approveMan != ''  ">approve_man,</if>
			<if test="approveMemo != null  and approveMemo != ''  ">approve_memo,</if>
			<if test="approveTime != null  ">approve_time,</if>
			<if test="gotAmount != null  ">got_amount,</if>
			<if test="ungotAmount != null  ">ungot_amount,</if>
			<if test="fuelCard != null  and fuelCard != ''  ">fuel_card,</if>
			<if test="recAccount != null  and recAccount != ''  ">rec_account,</if>
			<if test="recBank != null  and recBank != ''  ">rec_bank,</if>
			<if test="recCardNo != null  and recCardNo != ''  ">rec_card_no,</if>
			<if test="regUserId != null  and regUserId != ''  ">reg_user_id,</if>
			<if test="regDate != null  ">reg_date,</if>
			<if test="corUserId != null  and corUserId != ''  ">cor_user_id,</if>
			<if test="corDate != null  ">cor_date,</if>
			<if test="delFalg != null  ">del_falg,</if>
			<if test="delDate != null  ">del_date,</if>
			<if test="delUserId != null  and delUserId != ''  ">del_user_id,</if>
            <if test="receMan != null  and receMan != ''  ">rece_man,</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id,</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id,</if>
            <if test="bankNo != null  and bankNo != '' ">bank_no,</if>
            <if test="tid != null  and tid != '' ">TID,</if>
            <if test="carrBankId != null  and carrBankId != '' ">CARR_BANK_ID,</if>
            <if test="accountType != null  and accountType != '' ">ACCOUNT_TYPE,</if>
            <if test="writeFuelcardId != null  and writeFuelcardId != '' ">WRITE_FUELCARD_ID,</if>
            <if test="isOil != null  ">is_oil,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="paySheetRecordId != null  and paySheetRecordId != ''  ">#{paySheetRecordId},</if>
			<if test="payCheckSheetId != null  and payCheckSheetId != ''  ">#{payCheckSheetId},</if>
			#{vbillno},
			<if test="vbillnoStatus != null  ">#{vbillnoStatus},</if>
			<if test="payType != null  ">#{payType},</if>
			<if test="payAmount != null  ">#{payAmount},</if>
			<if test="payDate != null  ">#{payDate},</if>
			<if test="payMan != null  and payMan != ''  ">#{payMan},</if>
			<if test="carrierId != null  and carrierId != ''  ">#{carrierId},</if>
			<if test="checkNo != null  and checkNo != ''  ">#{checkNo},</if>
			<if test="checkHead != null  and checkHead != ''  ">#{checkHead},</if>
			<if test="checkDate != null  ">#{checkDate},</if>
			<if test="checkRemark != null  and checkRemark != ''  ">#{checkRemark},</if>
			<if test="checkType != null  and checkType != ''  ">#{checkType},</if>
			<if test="memo != null  and memo != ''  ">#{memo},</if>
			<if test="outAccount != null  and outAccount != ''  ">#{outAccount},</if>
			<if test="oilAccount != null  and oilAccount != ''  ">#{oilAccount},</if>
			<if test="approveMan != null  and approveMan != ''  ">#{approveMan},</if>
			<if test="approveMemo != null  and approveMemo != ''  ">#{approveMemo},</if>
			<if test="approveTime != null  ">#{approveTime},</if>
			<if test="gotAmount != null  ">#{gotAmount},</if>
			<if test="ungotAmount != null  ">#{ungotAmount},</if>
			<if test="fuelCard != null  and fuelCard != ''  ">#{fuelCard},</if>
			<if test="recAccount != null  and recAccount != ''  ">#{recAccount},</if>
			<if test="recBank != null  and recBank != ''  ">#{recBank},</if>
			<if test="recCardNo != null  and recCardNo != ''  ">#{recCardNo},</if>
			<if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
			<if test="regDate != null  ">#{regDate},</if>
			<if test="corUserId != null  and corUserId != ''  ">#{corUserId},</if>
			<if test="corDate != null  ">#{corDate},</if>
			<if test="delFalg != null  ">#{delFalg},</if>
			<if test="delDate != null  ">#{delDate},</if>
			<if test="delUserId != null  and delUserId != ''  ">#{delUserId},</if>
            <if test="receMan != null  and receMan != ''  ">#{receMan},</if>
            <if test="regScrId != null  and regScrId != ''  ">#{regScrId},</if>
            <if test="corScrId != null  and corScrId != ''  ">#{corScrId},</if>
            <if test="bankNo != null  and bankNo != '' ">#{bankNo},</if>
            <if test="tid != null  and tid != '' ">#{tid},</if>
            <if test="carrBankId != null and carrBankId != '' ">#{carrBankId},</if>
            <if test="accountType != null  and accountType != '' ">#{accountType},</if>
            <if test="writeFuelcardId != null  and writeFuelcardId != '' ">#{writeFuelcardId},</if>
            <if test="isOil != null  ">#{isOil},</if>
         </trim>
    </insert>
	 <!--修改付款申请-->
    <update id="updatePaySheetRecord" parameterType="PaySheetRecord">
        update t_pay_sheet_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="payCheckSheetId != null  and payCheckSheetId != ''  ">pay_check_sheet_id = #{payCheckSheetId},</if>
            <if test="vbillno != null  and vbillno != ''  ">vbillno = #{vbillno},</if>
            <if test="vbillnoStatus != null  ">vbillno_status = #{vbillnoStatus},</if>
            <if test="payType != null  ">pay_type = #{payType},</if>
            <if test="payAmount != null  ">pay_amount = #{payAmount},</if>
            <if test="payDate != null  ">pay_date = #{payDate},</if>
            <if test="payMan != null  and payMan != ''  ">pay_man = #{payMan},</if>
            <if test="carrierId != null  and carrierId != ''  ">carrier_id = #{carrierId},</if>
            <if test="checkNo != null  and checkNo != ''  ">check_no = #{checkNo},</if>
            <if test="checkHead != null  and checkHead != ''  ">check_head = #{checkHead},</if>
            <if test="checkDate != null  ">check_date = #{checkDate},</if>
            <if test="checkRemark != null  and checkRemark != ''  ">check_remark = #{checkRemark},</if>
            <if test="checkType != null  and checkType != ''  ">check_type = #{checkType},</if>
            <if test="memo != null  and memo != ''  ">memo = #{memo},</if>
            <if test="outAccount != null  and outAccount != ''  ">out_account = #{outAccount},</if>
            <if test="oilAccount != null  and oilAccount != ''  ">oil_account = #{oilAccount},</if>
            <if test="approveMan != null  and approveMan != ''  ">approve_man = #{approveMan},</if>
            <if test="approveMemo != null  and approveMemo != ''  ">approve_memo = #{approveMemo},</if>
            <if test="approveTime != null  ">approve_time = #{approveTime},</if>
            <if test="gotAmount != null  ">got_amount = #{gotAmount},</if>
            <if test="ungotAmount != null  ">ungot_amount = #{ungotAmount},</if>
            <if test="fuelCard != null  and fuelCard != ''  ">fuel_card = #{fuelCard},</if>
            <if test="recAccount != null  and recAccount != ''  ">rec_account = #{recAccount},</if>
            <if test="recBank != null  and recBank != ''  ">rec_bank = #{recBank},</if>
            <if test="recCardNo != null  and recCardNo != ''  ">rec_card_no = #{recCardNo},</if>
            <if test="regUserId != null  and regUserId != ''  ">reg_user_id = #{regUserId},</if>
            <if test="regDate != null  ">reg_date = #{regDate},</if>
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id = #{corUserId},</if>
            <if test="corDate != null  ">cor_date = #{corDate},</if>
            <if test="delFalg != null  ">del_falg = #{delFalg},</if>
            <if test="delDate != null  ">del_date = #{delDate},</if>
            <if test="delUserId != null  and delUserId != ''  ">del_user_id = #{delUserId},</if>
            <if test="receMan != null  and receMan != ''  ">rece_man = #{receMan},</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id = #{regScrId},</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id = #{corScrId},</if>
            <if test="bankNo != null  and bankNo != '' ">bank_no = #{bankNo},</if>
        </trim>
        where pay_sheet_record_id = #{paySheetRecordId}
    </update>

    <update id="updatePaySheetRecordStatus">
         update t_pay_sheet_record
         set vbillno_status = #{vbillnoStatus}
        where pay_sheet_record_id = #{paySheetRecordId}
        and vbillno_status != #{vbillnoStatus}
    </update>
    <!--删除付款申请信息-->
	<delete id="deletePaySheetRecordById" parameterType="String">
        delete from t_pay_sheet_record where pay_sheet_record_id = #{paySheetRecordId}
    </delete>
	<!--批量删除付款申请信息-->
    <delete id="deletePaySheetRecordByIds" parameterType="String">
        delete from t_pay_sheet_record where pay_sheet_record_id in 
        <foreach item="paySheetRecordId" collection="array" open="(" separator="," close=")">
            #{paySheetRecordId}
        </foreach>
    </delete>

    <!--审核-->
    <update id="checkPaySheetRecord" parameterType="PaySheetRecord">
        update t_pay_sheet_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="vbillnoStatus != null  and vbillnoStatus != ''  ">vbillno_status = #{vbillnoStatus},</if>
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id = #{corUserId},</if>
            <if test="corDate != null  ">cor_date = #{corDate},</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id = #{corScrId},</if>
        </trim>
        where pay_sheet_record_id = #{paySheetRecordId} and del_falg = 0 and vbillno_status in(0,6,7)
    </update>

    <!--查询月度付款状态-->
    <select id="selectStatusByPaySheetRecordIds" resultMap="PaySheetRecordResult">
        select
        VBILLNO_STATUS
        from t_pay_sheet_record
        where del_falg = 0
        and PAY_SHEET_RECORD_ID in
        <foreach item="ids" collection="list" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </select>


    <!--修改付款申请 判断状态-->
    <update id="updatePaySheetRecordJudgmentStatus" parameterType="PaySheetRecord">
        update t_pay_sheet_record
        <trim prefix="SET" suffixOverrides=",">
            <!--已付款金额 = 已付款金额+本次支付金额 -->
            got_amount = got_amount + #{payAmount},
            <!--未付款金额 = 未付款金额-本次支付金额 -->
            ungot_amount = ungot_amount - #{payAmount},
            <!--状态判定：3为未付款 4为已付款-->
            vbillno_status =
              (case when
                ungot_amount > #{payAmount}
              then 3
              else 4 end),
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id = #{corUserId},</if>
            <if test="corDate != null  ">cor_date = #{corDate},</if>
            cor_scr_id = 'payRecord-checkBatchPay'
        </trim>
        where pay_sheet_record_id = #{paySheetRecordId} and vbillno_status != 4 and ungot_amount >= #{payAmount}

    </update>

    <select id="getPaySheetRecordList" parameterType="PaySheetRecord" resultMap="PaySheetRecordResult">
        <include refid="selectPaySheetRecordVo"/>
        <where>
            <if test="paySheetRecordId != null  and paySheetRecordId != '' "> and pay_sheet_record_id = #{paySheetRecordId}</if>
            <if test="payCheckSheetId != null  and payCheckSheetId != '' "> and pay_check_sheet_id = #{payCheckSheetId}</if>
            <if test="vbillno != null  and vbillno != '' "> and vbillno = #{vbillno}</if>
            <if test="vbillnoStatus != null "> and vbillno_status = #{vbillnoStatus}</if>
            <if test="payType != null "> and pay_type = #{payType}</if>
            <if test="payAmount != null "> and pay_amount = #{payAmount}</if>
            <if test="payDate != null "> and pay_date = #{payDate}</if>
            <if test="payMan != null  and payMan != '' "> and pay_man = #{payMan}</if>
            <if test="carrierId != null  and carrierId != '' "> and carrier_id = #{carrierId}</if>
            <if test="checkNo != null  and checkNo != '' "> and check_no = #{checkNo}</if>
            <if test="checkHead != null  and checkHead != '' "> and check_head = #{checkHead}</if>
            <if test="checkDate != null "> and check_date = #{checkDate}</if>
            <if test="checkRemark != null  and checkRemark != '' "> and check_remark = #{checkRemark}</if>
            <if test="checkType != null  and checkType != '' "> and check_type = #{checkType}</if>
            <if test="memo != null  and memo != '' "> and memo = #{memo}</if>
            <if test="outAccount != null  and outAccount != '' "> and out_account = #{outAccount}</if>
            <if test="oilAccount != null  and oilAccount != '' "> and oil_account = #{oilAccount}</if>
            <if test="approveMan != null  and approveMan != '' "> and approve_man = #{approveMan}</if>
            <if test="approveMemo != null  and approveMemo != '' "> and approve_memo = #{approveMemo}</if>
            <if test="approveTime != null "> and approve_time = #{approveTime}</if>
            <if test="gotAmount != null "> and got_amount = #{gotAmount}</if>
            <if test="ungotAmount != null "> and ungot_amount = #{ungotAmount}</if>
            <if test="fuelCard != null  and fuelCard != '' "> and fuel_card = #{fuelCard}</if>
            <if test="recAccount != null  and recAccount != '' "> and rec_account = #{recAccount}</if>
            <if test="recBank != null  and recBank != '' "> and rec_bank = #{recBank}</if>
            <if test="recCardNo != null  and recCardNo != '' "> and rec_card_no = #{recCardNo}</if>
            <if test="regUserId != null  and regUserId != '' "> and reg_user_id = #{regUserId}</if>
            <if test="regDate != null "> and reg_date = #{regDate}</if>
            <if test="corUserId != null  and corUserId != '' "> and cor_user_id = #{corUserId}</if>
            <if test="corDate != null "> and cor_date = #{corDate}</if>
            <if test="delFalg != null "> and del_falg = #{delFalg}</if>
            <if test="delDate != null "> and del_date = #{delDate}</if>
            <if test="delUserId != null  and delUserId != '' "> and del_user_id = #{delUserId}</if>
            <if test="receMan != null  and receMan != '' "> and rece_man = #{receMan}</if>
            <if test="regScrId != null  and regScrId != '' "> and reg_scr_id = #{regScrId}</if>
            <if test="corScrId != null  and corScrId != '' "> and cor_scr_id = #{corScrId}</if>
            <if test="bankNo != null  and bankNo != '' "> and bank_no = #{bankNo}</if>
        </where>
    </select>

    <select id="selectPaySheetRecordAmountCount"  resultType="java.util.Map">
        select
        sum(nvl(record.pay_amount,0)) payAmount,
        sum(nvl(record.GOT_AMOUNT,0)) gotAmount,
        sum(nvl(record.UNGOT_AMOUNT,0)) ungotAmount
        from t_pay_sheet_record record
        left join t_pay_check_sheet sheet  <!--承运商对账-->
        on record.pay_check_sheet_id = sheet.pay_check_sheet_id
        left join M_CARRIER carr <!--承运商-->
        on record.CARRIER_ID = carr.CARRIER_ID
        left join t_account account <!--账户管理-->
        on record.out_account = account.account_id
        <where>
            record.del_falg = 0
            <if test="payCheckSheetId != null  and payCheckSheetId != '' ">
                and record.pay_check_sheet_id = #{payCheckSheetId}
            </if>
            <if test="paySheetRecordId != null  and paySheetRecordId != '' ">
                and record.pay_sheet_record_id = #{paySheetRecordId}
            </if>
            <!--单据号-->
            <if test="vbillno != null  and vbillno != '' ">
                <bind name="vbillno" value="vbillno + '%'"/>
                and record.vbillno like #{vbillno}
            </if>
            <!--承运商名称-->
            <if test="carrierId != null  and carrierId != '' ">
                <bind name="carrierId" value="carrierId+'%'"/>
                and carr.carr_name like #{carrierId}
            </if>
            <!--对账单号-->
            <if test="reconNumber != null  and reconNumber != '' ">
                <bind name="reconNumber" value="reconNumber + '%'"/>
                and sheet.vbillno like #{reconNumber}
            </if>
            <!--单据状态-->
            <if test="status != null and status.indexOf(',') != -1 ">
                and record.vbillno_status in
                <foreach item="item" index="index" collection="status.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status != '' and status.indexOf(',') == -1 ">
                and record.vbillno_status = #{status}
            </if>
            <if test="payType != null ">
                and record.pay_type = #{payType}
            </if>
            <if test="params.type != null and params.type != '' and params.type == 0">
                and record.oil_account is not null
            </if>
            <if test="params.type != null and params.type != '' and params.type == 1">
                and record.oil_account is null
            </if>
            <choose>
                <when test="params.payWay == 'g7'">
                    and sheet.lot_g7_end = 2
                </when>
                <when test="params.payWay == 'yl'">
                    and (sheet.lot_g7_end is null or sheet.lot_g7_end != 2)
                </when>
            </choose>
        </where>
        order by record.cor_date desc
    </select>
    <select id="selectPayDetailIdsByVbillno" resultType="java.lang.String">
        select
          detail.PAY_DETAIL_ID
        from T_PAY_CHECK_SHEET_B b
        left join T_PAY_DETAIL detail
          on detail.PAY_DETAIL_ID = b.PAY_DETAIL_ID
        left join T_PAY_SHEET_RECORD sheetrecord
          on sheetrecord.PAY_CHECK_SHEET_ID = b.PAY_CHECK_SHEET_ID
        where sheetrecord.vbillno = #{vbillno}
          and detail.DEL_FLAG = 0
          and b.DEL_FALG = 0
    </select>

    <select id="listG7WaitingPay" resultType="Map">
        select a.pay_sheet_record_id,
               a.status,
               sum(a.amount) amount,
               a.reg_date,
               a.payee,
               a.pay_man,
               a.out_account,
               a.billing_corp,
               b.user_name   reg_user_name,
               c.vbillno,
               a.PAY_SHEET_RECORD_STATUS
        from t_pay_record_g7 a
                 left join sys_user b on b.user_id = a.reg_user_id
                 left join t_pay_sheet_record c on c.pay_sheet_record_id = a.pay_sheet_record_id
        where a.status = 1
          and a.pay_type = 0
            <if test='corp != null and corp !=""'>
                and a.billing_corp = #{corp}
            </if>
            <if test='vbillno != null and vbillno !=""'>
                <bind name="vbillnoLike" value="'%'+vbillno+'%'"/>
                and c.vbillno like #{vbillnoLike}
            </if>
        group by a.pay_sheet_record_id,
                 a.status,
                 a.reg_date,
                 a.payee,
                 a.pay_man,
                 a.out_account,
                 a.billing_corp,
                 b.user_name,
                 c.vbillno,
                 a.PAY_SHEET_RECORD_STATUS
        order by a.reg_date desc
    </select>

    <select id="listWaitingPayLotId" resultType="String">
        select lot_id from t_pay_record_g7 where billing_corp = #{corp} and status = 1 and pay_type = 0
        <if test="paySheetRecordIds != null">
            and pay_sheet_record_id in (
            <foreach collection="paySheetRecordIds" separator="," item="paySheetRecordId">#{paySheetRecordId}</foreach>
            )
        </if>
        order by pay_sheet_record_id, lot_id
    </select>

    <update id="setPaySheetRecordStatusByLotIds">
        update t_pay_record_g7 set pay_sheet_record_status = 3 where pay_sheet_record_id in (
            select pay_sheet_record_id from t_pay_record_g7 where lot_id in (
                <foreach collection="lotIds" item="lotId" separator=",">#{lotId}</foreach>
            )
        )
    </update>

    <select id="selectPaySheetRecordCountPermission" resultType="int">
        select count(*) from t_pay_sheet_record t
        where t.del_falg = 0
        <!--单据状态-->
        <if test="status != null and status.indexOf(',') != -1 ">
            and t.vbillno_status in
            <foreach item="item" index="index" collection="status.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="status != null and status != '' and status.indexOf(',') == -1 ">
            and t.vbillno_status = #{status}
        </if>

    </select>

    <resultMap id="rm695" type="java.util.Map">
        <result column="carr_bank_id" property="carrBankId" />
        <result column="bank_account" property="bankAccount" />
        <result column="bank_card" property="bankCard" />
        <result column="bank_name" property="bankName" />
        <result column="phone" property="phone" />
        <result column="idcard" property="idcard" />
        <result column="payee_card" property="payeeCard" />
        <result column="payee_name" property="payeeName" />
        <result column="g7_bank_name" property="g7BankName" />
        <result column="payee_mobile" property="payeeMobile" />
        <result column="g7_syn_jh" property="g7SynJH" javaType="Integer"/>
        <result column="g7_syn_my" property="g7SynMY" javaType="Integer"/>
        <result column="g7_syn_dh" property="g7SynDH" javaType="Integer"/>
        <result column="g7_syn_dw" property="g7SynDW" javaType="Integer"/>
    </resultMap>

    <select id="listCarrBankAndG7PayeeSyn" resultMap="rm695">
        select a.carr_bank_id,a.bank_account,a.bank_card,a.bank_name,a.phone,a.idcard,
        b.payee_card,b.payee_name,b.bank_name g7_bank_name,b.payee_mobile,g7_syn_jh,g7_syn_my,g7_syn_dh,g7_syn_dw
        from m_carr_bank a
        left join t_g7_payee b on b.bank_card_number = a.bank_card
        where a.CARR_BANK_ID in (<foreach collection="carrBankIdList" item="carrBankId" separator=",">#{carrBankId}</foreach>)
    </select>

</mapper>